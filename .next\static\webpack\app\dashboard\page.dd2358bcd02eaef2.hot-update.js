"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DashboardPage() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalScans: 0,\n        vulnerabilitiesFound: 0,\n        filesAnalyzed: 0,\n        osintQueries: 0,\n        apiCalls: 0,\n        score: 0,\n        level: 0,\n        rank: 0,\n        streak: 0,\n        pointsToday: 0,\n        pointsThisWeek: 0,\n        pointsThisMonth: 0\n    });\n    const [recentActivity, setRecentActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [achievements, setAchievements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [planUsage, setPlanUsage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [trendingVulns, setTrendingVulns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update time every minute\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch real dashboard data from API\n            const response = await fetch(\"/api/dashboard/stats\");\n            const data = await response.json();\n            if (data.success) {\n                setStats({\n                    totalScans: data.data.stats.totalScans,\n                    vulnerabilitiesFound: data.data.stats.vulnerabilitiesFound,\n                    filesAnalyzed: data.data.stats.fileAnalyses,\n                    osintQueries: data.data.stats.osintQueries,\n                    apiCalls: data.data.stats.cveSearches + data.data.stats.dorkingQueries,\n                    score: data.data.user.score,\n                    level: data.data.user.level,\n                    rank: data.data.user.rank,\n                    streak: data.data.user.streak,\n                    pointsToday: Math.floor(data.data.user.score * 0.1),\n                    pointsThisWeek: Math.floor(data.data.user.score * 0.3),\n                    pointsThisMonth: Math.floor(data.data.user.score * 0.6)\n                });\n                // Transform recent activities to match interface\n                const transformedActivities = data.data.recentActivities.map((activity)=>({\n                        id: activity.id.toString(),\n                        type: activity.type === \"vulnerability_scan\" ? \"scan\" : activity.type === \"osint_query\" ? \"osint\" : activity.type === \"file_analysis\" ? \"file\" : activity.type === \"cve_search\" ? \"cve\" : \"dorking\",\n                        target: activity.description.includes(\"target.\") ? \"target.example.com\" : activity.description.includes(\"domain\") ? \"example.com\" : activity.description.includes(\"file:\") ? \"malware.exe\" : activity.description.includes(\"Apache\") ? \"Apache HTTP Server\" : activity.description.includes(\"databases\") ? \"Google Search\" : \"Unknown\",\n                        time: new Date(activity.timestamp).toLocaleTimeString() + \" ago\",\n                        status: \"completed\",\n                        severity: activity.severity,\n                        result: activity.result\n                    }));\n                setRecentActivity(transformedActivities);\n                setUser(data.data.user);\n                setAchievements(data.data.achievements);\n                setPlanUsage(data.data.usage);\n                setTrendingVulns(data.data.trendingVulnerabilities);\n            } else {\n                // Fallback to mock data if API fails\n                setStats({\n                    totalScans: 142,\n                    vulnerabilitiesFound: 23,\n                    filesAnalyzed: 89,\n                    osintQueries: 456,\n                    apiCalls: 2847,\n                    score: 8950,\n                    level: 28,\n                    rank: 156,\n                    streak: 12,\n                    pointsToday: 250,\n                    pointsThisWeek: 1450,\n                    pointsThisMonth: 5890\n                });\n                setRecentActivity([\n                    {\n                        id: \"1\",\n                        type: \"scan\",\n                        target: \"example.com\",\n                        time: \"2 minutes ago\",\n                        status: \"completed\",\n                        severity: \"high\",\n                        result: \"3 vulnerabilities found\"\n                    }\n                ]);\n            }\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            setLoading(false);\n        }\n    };\n    const quickActions = [\n        {\n            title: \"OSINT Lookup\",\n            description: \"Investigate emails, phone numbers, and personal information\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"/osint\",\n            badge: \"Popular\",\n            color: \"cyber-primary\",\n            stats: \"456 queries\"\n        },\n        {\n            title: \"Vulnerability Scan\",\n            description: \"Scan websites and applications for security vulnerabilities\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"/scanner\",\n            premium: true,\n            color: \"cyber-secondary\",\n            stats: \"142 scans\"\n        },\n        {\n            title: \"File Analysis\",\n            description: \"Analyze files for malware, webshells, and suspicious content\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"/file-analyzer\",\n            premium: true,\n            color: \"cyber-accent\",\n            stats: \"89 files\"\n        },\n        {\n            title: \"CVE Database\",\n            description: \"Search and explore the latest CVE vulnerabilities\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: \"/cve\",\n            color: \"green-400\",\n            stats: \"50K+ CVEs\"\n        },\n        {\n            title: \"Google Dorking\",\n            description: \"Use advanced search queries to find sensitive information\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: \"/dorking\",\n            color: \"blue-400\",\n            stats: \"Advanced\"\n        },\n        {\n            title: \"API Playground\",\n            description: \"Test and explore our API endpoints\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            href: \"/playground\",\n            color: \"purple-400\",\n            stats: \"2.8K calls\"\n        }\n    ];\n    const getActivityIcon = (type)=>{\n        switch(type){\n            case \"scan\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"osint\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"file\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"cve\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"dorking\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            default:\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"text-green-400\";\n            case \"failed\":\n                return \"text-red-400\";\n            case \"malicious\":\n                return \"text-red-400\";\n            case \"clean\":\n                return \"text-green-400\";\n            case \"pending\":\n                return \"text-yellow-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"critical\":\n                return \"text-red-400 bg-red-400/20\";\n            case \"high\":\n                return \"text-orange-400 bg-orange-400/20\";\n            case \"medium\":\n                return \"text-yellow-400 bg-yellow-400/20\";\n            case \"low\":\n                return \"text-green-400 bg-green-400/20\";\n            default:\n                return \"text-gray-400 bg-gray-400/20\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-cyber-primary font-medium\",\n                            children: \"Loading cyber dashboard...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 289,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 288,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-glow\",\n                                            children: \"Cyber\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-pink\",\n                                            children: \"Command\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg\",\n                                    children: [\n                                        \"Welcome back, \",\n                                        (user === null || user === void 0 ? void 0 : user.username) || \"Cyber Warrior\",\n                                        \"! Your digital arsenal awaits.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 lg:mt-0 flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: loadDashboardData,\n                                    disabled: loading,\n                                    className: \"btn-cyber-secondary text-sm\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-cyber-primary mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Refreshing...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Refresh\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Current Time\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-mono text-cyber-primary\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-cyber-primary\",\n                                    onClick: ()=>window.location.href = \"/vulnerability-scanner\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Quick Scan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-2xl font-bold text-black\",\n                                        children: (user === null || user === void 0 ? void 0 : user.username) ? user.username.substring(0, 2).toUpperCase() : \"CW\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: (user === null || user === void 0 ? void 0 : user.fullName) || (user === null || user === void 0 ? void 0 : user.username) || \"Cyber Warrior\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Level \",\n                                                            stats.level\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded text-xs font-medium \".concat((user === null || user === void 0 ? void 0 : user.plan) === \"Elite\" ? \"bg-purple-500/20 text-purple-400\" : (user === null || user === void 0 ? void 0 : user.plan) === \"Pro\" ? \"bg-blue-500/20 text-blue-400\" : (user === null || user === void 0 ? void 0 : user.plan) === \"Cybersecurity\" ? \"bg-red-500/20 text-red-400\" : (user === null || user === void 0 ? void 0 : user.plan) === \"Bughunter\" ? \"bg-orange-500/20 text-orange-400\" : \"bg-gray-500/20 text-gray-400\"),\n                                                        children: (user === null || user === void 0 ? void 0 : user.plan) || \"Free\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Rank #\",\n                                                            stats.rank\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 text-cyber-secondary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    stats.streak,\n                                                                    \" day streak\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-cyber-primary\",\n                                        children: stats.score.toLocaleString()\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"Total Score\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-green-400\",\n                                                children: [\n                                                    \"+\",\n                                                    stats.pointsToday,\n                                                    \" today\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-3 w-3 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-8 w-8 text-cyber-primary mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.totalScans\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Total Scans\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-green-400 mt-1\",\n                                    children: \"+12 this week\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-8 w-8 text-red-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.vulnerabilitiesFound\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Vulnerabilities\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-red-400 mt-1\",\n                                    children: \"+3 critical\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-8 w-8 text-cyber-accent mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.filesAnalyzed\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Files Analyzed\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-yellow-400 mt-1\",\n                                    children: \"5 malicious\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-8 w-8 text-blue-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.osintQueries\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"OSINT Queries\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-blue-400 mt-1\",\n                                    children: \"+45 today\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.apiCalls.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"API Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-purple-400 mt-1\",\n                                    children: \"98% uptime\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.pointsThisWeek.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Weekly Points\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-green-400 mt-1\",\n                                    children: \"+15% vs last week\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber border-l-4 border-l-cyber-secondary\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-6 w-6 text-cyber-secondary mt-1\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold text-white mb-1\",\n                                        children: \"System Status Update\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-3\",\n                                        children: \"New CVE database update available. 15 critical vulnerabilities added to our intelligence feed.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-cyber-secondary text-sm\",\n                                                children: \"View Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"2 minutes ago\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 451,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Quick\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: quickActions.map((action, index)=>{\n                                const Icon = action.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber hover:scale-105 transition-all duration-300 cursor-pointer group\",\n                                    onClick: ()=>window.location.href = action.href,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg bg-\".concat(action.color, \"/20\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-6 w-6 text-\".concat(action.color, \" group-hover:animate-cyber-pulse\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-end space-y-1\",\n                                                    children: [\n                                                        action.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-cyber-secondary/20 text-cyber-secondary px-2 py-1 rounded-full text-xs font-bold\",\n                                                            children: action.badge\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        action.premium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-cyber-accent/20 text-cyber-accent px-2 py-1 rounded-full text-xs font-bold\",\n                                                            children: \"PRO\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-2 group-hover:text-cyber-primary transition-colors\",\n                                            children: action.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm mb-4\",\n                                            children: action.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium text-\".concat(action.color),\n                                                    children: action.stats\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400 group-hover:text-cyber-primary transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 470,\n                    columnNumber: 9\n                }, this),\n                planUsage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Plan\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Usage\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: \"Vulnerability Scans\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5 text-cyber-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Used\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: [\n                                                                planUsage.usage.vulnerabilityScans,\n                                                                \" / \",\n                                                                planUsage.limits.scans === -1 ? \"∞\" : planUsage.limits.scans\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 19\n                                                }, this),\n                                                planUsage.limits.scans !== -1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-cyber-primary h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(Math.min(planUsage.percentage.vulnerabilityScans, 100), \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: \"OSINT Queries\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-cyber-secondary\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Used\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: [\n                                                                planUsage.usage.osintQueries,\n                                                                \" / \",\n                                                                planUsage.limits.osint === -1 ? \"∞\" : planUsage.limits.osint\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 19\n                                                }, this),\n                                                planUsage.limits.osint !== -1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-cyber-secondary h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(Math.min(planUsage.percentage.osintQueries, 100), \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: \"File Analyses\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5 text-cyber-accent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Used\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: [\n                                                                planUsage.usage.fileAnalyses,\n                                                                \" / \",\n                                                                planUsage.limits.files === -1 ? \"∞\" : planUsage.limits.files\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 19\n                                                }, this),\n                                                planUsage.limits.files !== -1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-cyber-accent h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(Math.min(planUsage.percentage.fileAnalyses, 100), \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 524,\n                    columnNumber: 11\n                }, this),\n                achievements && achievements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Recent\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Achievements\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 600,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: achievements.slice(0, 4).map((achievement)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center transition-all duration-300 \".concat(achievement.unlocked ? \"border-cyber-primary/50 bg-cyber-primary/5\" : \"border-gray-600/50 bg-gray-800/50 opacity-60\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center \".concat(achievement.unlocked ? \"bg-cyber-primary/20 text-cyber-primary\" : \"bg-gray-700 text-gray-500\"),\n                                            children: [\n                                                achievement.icon === \"shield\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 55\n                                                }, this),\n                                                achievement.icon === \"eye\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 52\n                                                }, this),\n                                                achievement.icon === \"bug\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 52\n                                                }, this),\n                                                achievement.icon === \"flame\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 54\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2 \".concat(achievement.unlocked ? \"text-white\" : \"text-gray-400\"),\n                                            children: achievement.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mb-3\",\n                                            children: achievement.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(achievement.rarity === \"legendary\" ? \"bg-purple-500/20 text-purple-400\" : achievement.rarity === \"epic\" ? \"bg-orange-500/20 text-orange-400\" : achievement.rarity === \"rare\" ? \"bg-blue-500/20 text-blue-400\" : \"bg-gray-500/20 text-gray-400\"),\n                                            children: achievement.rarity\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 19\n                                        }, this),\n                                        achievement.unlocked && achievement.unlockedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-2\",\n                                            children: [\n                                                \"Unlocked \",\n                                                new Date(achievement.unlockedAt).toLocaleDateString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, achievement.id, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 605,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 599,\n                    columnNumber: 11\n                }, this),\n                trendingVulns && trendingVulns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Trending\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Vulnerabilities\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: trendingVulns.map((vuln)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-white mb-1\",\n                                                            children: vuln.id\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: vuln.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-2 py-1 rounded-full text-xs font-bold uppercase \".concat(vuln.severity === \"critical\" ? \"bg-red-500/20 text-red-400\" : vuln.severity === \"high\" ? \"bg-orange-500/20 text-orange-400\" : vuln.severity === \"medium\" ? \"bg-yellow-500/20 text-yellow-400\" : \"bg-green-500/20 text-green-400\"),\n                                                    children: vuln.severity\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-300 mb-4\",\n                                            children: vuln.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400\",\n                                                    children: [\n                                                        \"CVSS: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: vuln.score\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400\",\n                                                    children: [\n                                                        \"Affected: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-cyber-primary font-medium\",\n                                                            children: vuln.affectedSystems.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, vuln.id, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 664,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 662,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 656,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Recent\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Activity\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: recentActivity.map((activity)=>{\n                                        const Icon = getActivityIcon(activity.type);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5 hover:bg-cyber-primary/10 transition-colors duration-200 cursor-pointer\",\n                                            onClick: ()=>{\n                                                // Navigate to appropriate tool based on activity type\n                                                const routes = {\n                                                    \"scan\": \"/vulnerability-scanner\",\n                                                    \"osint\": \"/osint\",\n                                                    \"file\": \"/file-analyzer\",\n                                                    \"cve\": \"/cve-intelligence\",\n                                                    \"dorking\": \"/dorking\"\n                                                };\n                                                const route = routes[activity.type];\n                                                if (route) {\n                                                    window.location.href = route;\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-cyber-primary/20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"h-5 w-5 text-cyber-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 728,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium text-white\",\n                                                                            children: activity.target\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 733,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-bold uppercase \".concat(getStatusColor(activity.status)),\n                                                                            children: activity.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 734,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        activity.severity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-bold uppercase \".concat(getSeverityColor(activity.severity)),\n                                                                            children: activity.severity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 738,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-sm\",\n                                                                    children: activity.result\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 743,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 731,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: activity.time\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 capitalize\",\n                                                            children: activity.type\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 749,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, activity.id, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-cyber-primary hover:text-cyber-secondary transition-colors\",\n                                        children: \"View All Activity →\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 756,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 703,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 697,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 301,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"BFRTwvn+5IkN9CfaeTLp5dlKHsI=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});