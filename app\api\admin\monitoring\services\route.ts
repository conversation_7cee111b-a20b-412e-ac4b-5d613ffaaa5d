'use server'

import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    // Authenticate request - only admins can access
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      )
    }

    // Mock service status data
    // In real implementation, this would check actual service health
    const services = [
      {
        name: 'Web Server',
        status: 'running',
        uptime: 86400 * 7, // 7 days
        lastCheck: new Date().toISOString(),
        responseTime: 45,
        errorCount: 0
      },
      {
        name: 'Database',
        status: 'running',
        uptime: 86400 * 14, // 14 days
        lastCheck: new Date().toISOString(),
        responseTime: 12,
        errorCount: 2
      },
      {
        name: 'Redis Cache',
        status: 'running',
        uptime: 86400 * 5, // 5 days
        lastCheck: new Date().toISOString(),
        responseTime: 3,
        errorCount: 0
      },
      {
        name: 'Elasticsearch',
        status: 'running',
        uptime: 86400 * 10, // 10 days
        lastCheck: new Date().toISOString(),
        responseTime: 89,
        errorCount: 1
      },
      {
        name: 'WhatsApp Bot',
        status: Math.random() > 0.8 ? 'error' : 'running',
        uptime: 86400 * 2, // 2 days
        lastCheck: new Date().toISOString(),
        responseTime: 156,
        errorCount: 5
      },
      {
        name: 'Telegram Bot',
        status: Math.random() > 0.9 ? 'stopped' : 'running',
        uptime: 86400 * 1, // 1 day
        lastCheck: new Date().toISOString(),
        responseTime: 234,
        errorCount: 3
      },
      {
        name: 'OSINT Engine',
        status: 'running',
        uptime: 86400 * 6, // 6 days
        lastCheck: new Date().toISOString(),
        responseTime: 567,
        errorCount: 8
      },
      {
        name: 'Scanner Engine',
        status: 'running',
        uptime: 86400 * 4, // 4 days
        lastCheck: new Date().toISOString(),
        responseTime: 1234,
        errorCount: 12
      },
      {
        name: 'File Analyzer',
        status: 'running',
        uptime: 86400 * 3, // 3 days
        lastCheck: new Date().toISOString(),
        responseTime: 789,
        errorCount: 4
      }
    ]

    // Check database connectivity
    try {
      await db.query('SELECT 1')
      const dbService = services.find(s => s.name === 'Database')
      if (dbService) {
        dbService.status = 'running'
        dbService.lastCheck = new Date().toISOString()
      }
    } catch (error) {
      const dbService = services.find(s => s.name === 'Database')
      if (dbService) {
        dbService.status = 'error'
        dbService.errorCount += 1
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        services: services,
        lastUpdate: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Error getting service status:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get service status'
      },
      { status: 500 }
    )
  }
}
