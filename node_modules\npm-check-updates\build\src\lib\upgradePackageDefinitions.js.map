{"version": 3, "file": "upgradePackageDefinitions.js", "sourceRoot": "", "sources": ["../../../src/lib/upgradePackageDefinitions.ts"], "names": [], "mappings": ";;;;;;AAAA,6DAAoC;AACpC,6DAAoC;AACpC,2DAAkC;AAClC,mCAAkC;AAClC,+CAAgD;AAKhD,wGAA+E;AAC/E,8DAAqC;AACrC,oEAA2C;AAC3C,gFAAuD;AAEvD;;;;;;GAMG;AACI,KAAK,UAAU,yBAAyB,CAC7C,mBAAuC,EACvC,OAAgB;IAEhB,MAAM,oBAAoB,GAAG,MAAM,IAAA,uBAAa,EAAC,mBAAmB,EAAE,OAAO,CAAC,CAAA;IAE9E,MAAM,cAAc,GAAG,IAAA,oBAAU,EAAC,oBAAoB,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CACtE,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO;QACf,CAAC,CAAC,OAAO,CAAC,aAAa;YACrB,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE;gBACzB,cAAc,EAAE,mBAAmB,CAAC,GAAG,CAAC;gBACxC,oBAAoB,EAAE,IAAA,yBAAU,EAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;gBAC1D,eAAe,EAAE,MAAM,CAAC,OAAO;gBAC/B,qBAAqB,EAAE,IAAA,oBAAK,EAAC,MAAM,CAAC,OAAO,CAAC;aAC7C,CAAC,CAAC;QACH,CAAC,CAAC;YACE,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,OAAO;SACtB;QACH,CAAC,CAAC,IAAI,CACT,CAAA;IAED,MAAM,oBAAoB,GAAG,IAAA,6BAAmB,EAAC,mBAAmB,EAAE,cAAc,EAAE,OAAO,CAAC,CAAA;IAE9F,MAAM,4BAA4B,GAAG,IAAA,gBAAM,EAAC,oBAAoB,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;QAC3E,OAAO,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,IAAA,kBAAS,EAAC,cAAc,CAAC,GAAG,CAAC,EAAE,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAA;IAC/G,CAAC,CAAC,CAAA;IAEF,MAAM,0BAA0B,GAAG,IAAA,gBAAM,EAAC,cAAc,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC,CAAA;IAE3G,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,IAAA,iBAAO,EAAC,4BAA4B,CAAC,EAAE;QAC1D,MAAM,wBAAwB,GAAG,MAAM,IAAA,yCAA+B,EAAC,0BAA0B,EAAE,OAAO,CAAC,CAAA;QAC3G,MAAM,gBAAgB,GAAG,EAAE,GAAG,OAAO,CAAC,gBAAgB,EAAE,GAAG,wBAAwB,EAAE,CAAA;QACrF,IAAI,CAAC,IAAA,iBAAO,EAAC,OAAO,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,EAAE;YACxD,MAAM,CAAC,uBAAuB,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,GAAG,MAAM,yBAAyB,CACvG,EAAE,GAAG,mBAAmB,EAAE,GAAG,4BAA4B,EAAE,EAC3D,EAAE,GAAG,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,QAAQ,EAAE,CACrD,CAAA;YACD,OAAO;gBACL,EAAE,GAAG,4BAA4B,EAAE,GAAG,uBAAuB,EAAE;gBAC/D,EAAE,GAAG,oBAAoB,EAAE,GAAG,iBAAiB,EAAE;gBACjD,mBAAmB;aACpB,CAAA;SACF;KACF;IACD,OAAO,CAAC,4BAA4B,EAAE,oBAAoB,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAA;AACvF,CAAC;AA7CD,8DA6CC;AAED,kBAAe,yBAAyB,CAAA"}