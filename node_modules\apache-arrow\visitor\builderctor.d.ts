import { Data } from '../data.js';
import { Type } from '../enum.js';
import { Vector } from '../vector.js';
import { DataType } from '../type.js';
import { Visitor } from '../visitor.js';
import { BuilderCtor } from '../interfaces.js';
import { BinaryBuilder } from '../builder/binary.js';
import { LargeBinaryBuilder } from '../builder/largebinary.js';
import { BoolBuilder } from '../builder/bool.js';
import { DateBuilder, DateDayBuilder, DateMillisecondBuilder } from '../builder/date.js';
import { DecimalBuilder } from '../builder/decimal.js';
import { DictionaryBuilder } from '../builder/dictionary.js';
import { FixedSizeBinaryBuilder } from '../builder/fixedsizebinary.js';
import { FixedSizeListBuilder } from '../builder/fixedsizelist.js';
import { FloatBuilder, Float16Builder, Float32Builder, Float64Builder } from '../builder/float.js';
import { IntervalBuilder, IntervalDayTimeBuilder, IntervalYearMonthBuilder } from '../builder/interval.js';
import { DurationBuilder, DurationSecondBuilder, DurationMillisecondBuilder, DurationMicrosecondBuilder, DurationNanosecondBuilder } from '../builder/duration.js';
import { IntBuilder, Int8Builder, Int16Builder, Int32Builder, Int64Builder, Uint8Builder, Uint16Builder, Uint32Builder, Uint64Builder } from '../builder/int.js';
import { ListBuilder } from '../builder/list.js';
import { MapBuilder } from '../builder/map.js';
import { NullBuilder } from '../builder/null.js';
import { StructBuilder } from '../builder/struct.js';
import { TimestampBuilder, TimestampSecondBuilder, TimestampMillisecondBuilder, TimestampMicrosecondBuilder, TimestampNanosecondBuilder } from '../builder/timestamp.js';
import { TimeBuilder, TimeSecondBuilder, TimeMillisecondBuilder, TimeMicrosecondBuilder, TimeNanosecondBuilder } from '../builder/time.js';
import { UnionBuilder, DenseUnionBuilder, SparseUnionBuilder } from '../builder/union.js';
import { Utf8Builder } from '../builder/utf8.js';
import { LargeUtf8Builder } from '../builder/largeutf8.js';
/** @ignore */
export interface GetBuilderCtor extends Visitor {
    visit<T extends Type>(type: T): BuilderCtor<T>;
    visitMany<T extends Type>(types: T[]): BuilderCtor<T>[];
    getVisitFn<T extends Type>(type: T): () => BuilderCtor<T>;
    getVisitFn<T extends DataType>(node: Vector<T> | Data<T> | T): () => BuilderCtor<T>;
}
/** @ignore */
export declare class GetBuilderCtor extends Visitor {
    visitNull(): typeof NullBuilder;
    visitBool(): typeof BoolBuilder;
    visitInt(): typeof IntBuilder;
    visitInt8(): typeof Int8Builder;
    visitInt16(): typeof Int16Builder;
    visitInt32(): typeof Int32Builder;
    visitInt64(): typeof Int64Builder;
    visitUint8(): typeof Uint8Builder;
    visitUint16(): typeof Uint16Builder;
    visitUint32(): typeof Uint32Builder;
    visitUint64(): typeof Uint64Builder;
    visitFloat(): typeof FloatBuilder;
    visitFloat16(): typeof Float16Builder;
    visitFloat32(): typeof Float32Builder;
    visitFloat64(): typeof Float64Builder;
    visitUtf8(): typeof Utf8Builder;
    visitLargeUtf8(): typeof LargeUtf8Builder;
    visitBinary(): typeof BinaryBuilder;
    visitLargeBinary(): typeof LargeBinaryBuilder;
    visitFixedSizeBinary(): typeof FixedSizeBinaryBuilder;
    visitDate(): typeof DateBuilder;
    visitDateDay(): typeof DateDayBuilder;
    visitDateMillisecond(): typeof DateMillisecondBuilder;
    visitTimestamp(): typeof TimestampBuilder;
    visitTimestampSecond(): typeof TimestampSecondBuilder;
    visitTimestampMillisecond(): typeof TimestampMillisecondBuilder;
    visitTimestampMicrosecond(): typeof TimestampMicrosecondBuilder;
    visitTimestampNanosecond(): typeof TimestampNanosecondBuilder;
    visitTime(): typeof TimeBuilder;
    visitTimeSecond(): typeof TimeSecondBuilder;
    visitTimeMillisecond(): typeof TimeMillisecondBuilder;
    visitTimeMicrosecond(): typeof TimeMicrosecondBuilder;
    visitTimeNanosecond(): typeof TimeNanosecondBuilder;
    visitDecimal(): typeof DecimalBuilder;
    visitList(): typeof ListBuilder;
    visitStruct(): typeof StructBuilder;
    visitUnion(): typeof UnionBuilder;
    visitDenseUnion(): typeof DenseUnionBuilder;
    visitSparseUnion(): typeof SparseUnionBuilder;
    visitDictionary(): typeof DictionaryBuilder;
    visitInterval(): typeof IntervalBuilder;
    visitIntervalDayTime(): typeof IntervalDayTimeBuilder;
    visitIntervalYearMonth(): typeof IntervalYearMonthBuilder;
    visitDuration(): typeof DurationBuilder;
    visitDurationSecond(): typeof DurationSecondBuilder;
    visitDurationMillisecond(): typeof DurationMillisecondBuilder;
    visitDurationMicrosecond(): typeof DurationMicrosecondBuilder;
    visitDurationNanosecond(): typeof DurationNanosecondBuilder;
    visitFixedSizeList(): typeof FixedSizeListBuilder;
    visitMap(): typeof MapBuilder;
}
/** @ignore */
export declare const instance: GetBuilderCtor;
