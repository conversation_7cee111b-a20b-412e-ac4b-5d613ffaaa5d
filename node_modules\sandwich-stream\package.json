{"name": "sandwich-stream", "version": "2.0.2", "description": "A readable stream that concatenates multiple streams with optional head, tail & join buffers", "license": "Apache-2.0", "engines": {"node": ">= 0.10"}, "repository": {"type": "git", "url": "https://github.com/connrs/node-sandwich-stream.git"}, "main": "dist/sandwich-stream", "module": "dist/sandwich-stream.mjs", "types": "dist/sandwich-stream.d.ts", "keywords": ["stream", "sandwich", "readable", "typescript", "concatenation"], "scripts": {"rollup:build": "rollup -c", "rollup:watch": "npm run rollup:build -- --watch", "build": "npm run rollup:build", "test": "snyk test && npm run jest", "docs": "typedoc --out ./docs/ ./src/", "lint": "tslint --config tslint.json --project .", "jest": "jest --config jest.config.json --ci --runInBand --detectOpenHandles --forceExit --no-cache"}, "licenses": [{"type": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}], "author": {"name": "connrs", "url": "https://connrs.uk/"}, "contributors": [{"name": "Fazendaaa", "email": "<EMAIL>", "url": "http://fazendaaa.me/"}], "dependencies": {}, "devDependencies": {"@types/jest": "^23.3.5", "@types/node": "^10.12.0", "codecov": "^3.1.0", "husky": "^1.1.2", "jest": "^23.6.0", "rollup": "^0.66.6", "rollup-plugin-typescript2": "^0.17.2", "snyk": "^1.104.1", "ts-jest": "^23.10.4", "ts-node": "^7.0.1", "tslint": "^5.11.0", "tslint-microsoft-contrib": "^5.2.1", "typedoc": "^0.13.0", "typescript": "^3.1.4"}, "husky": {"hooks": {"pre-push": "npm run build && npm test", "pre-commit": "npm run lint && npm run docs"}}}