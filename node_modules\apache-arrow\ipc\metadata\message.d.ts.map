{"version": 3, "sources": ["ipc/metadata/message.ts"], "names": [], "mappings": "AAmBA,OAAO,KAAK,WAAW,MAAM,aAAa,CAAC;AAE3C,OAAO,EAAE,MAAM,IAAI,OAAO,EAAE,MAAM,oBAAoB,CAAC;AAEvD,OAAO,EAAE,WAAW,IAAI,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACvE,OAAO,EAAE,eAAe,IAAI,gBAAgB,EAAE,MAAM,8BAA8B,CAAC;AACnF,OAAO,EAAE,MAAM,IAAI,OAAO,EAAE,MAAM,oBAAoB,CAAC;AACvD,OAAO,EAAE,KAAK,IAAI,MAAM,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,SAAS,IAAI,UAAU,EAAE,MAAM,wBAAwB,CAAC;AAkBjE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAE5D,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,MAAM,eAAe,CAAC;AAE/D,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,MAAM,WAAW,CAAC;AAExG,OAAO,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;AAGrC,OAAO,EACH,QAAQ,EAIX,MAAM,eAAe,CAAC;AAEvB;;;IAGI;AACJ,qBAAa,OAAO,CAAC,CAAC,SAAS,aAAa,GAAG,GAAG;IAE9C,kBAAkB;WACJ,QAAQ,CAAC,CAAC,SAAS,aAAa,EAAE,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAMpF,kBAAkB;WACJ,MAAM,CAAC,GAAG,EAAE,oBAAoB;IAW9C,kBAAkB;WACJ,MAAM,CAAC,CAAC,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;IAmBjE,kBAAkB;WACJ,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,WAAW,GAAG,eAAe,EAAE,UAAU,SAAI;IAa1E,IAAI,EAAE,UAAU,CAAC;IACxB,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;IACzB,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC;IAC9B,SAAS,CAAC,QAAQ,EAAE,eAAe,CAAC;IACpC,IAAW,IAAI,MAA8B;IAC7C,IAAW,OAAO,oBAA4B;IAC9C,IAAW,UAAU,MAA+B;IACpD,IAAW,UAAU,WAA+B;IACpD,UAAkB,aAAa,EAAE,oBAAoB,CAAC;IAC/C,MAAM;IACN,QAAQ,IAAI,IAAI,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC;IACjD,aAAa,IAAI,IAAI,IAAI,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC;IAC3D,iBAAiB,IAAI,IAAI,IAAI,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC;gBAE9D,UAAU,EAAE,MAAM,GAAG,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG;CAOjG;AAED;;;IAGI;AACJ,qBAAa,WAAW;IACpB,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC;IAC1B,SAAS,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;IAC9B,SAAS,CAAC,QAAQ,EAAE,YAAY,EAAE,CAAC;IACnC,IAAW,KAAK,gBAA0B;IAC1C,IAAW,MAAM,WAA2B;IAC5C,IAAW,OAAO,mBAA4B;gBAClC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE;CAKnF;AAED;;;IAGI;AACJ,qBAAa,eAAe;IAExB,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC;IACtB,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC;IAC5B,SAAS,CAAC,KAAK,EAAE,WAAW,CAAC;IAC7B,IAAW,EAAE,WAAuB;IACpC,IAAW,IAAI,gBAAyB;IACxC,IAAW,OAAO,YAA4B;IAC9C,IAAW,MAAM,IAAI,MAAM,CAA6B;IACxD,IAAW,KAAK,IAAI,SAAS,EAAE,CAA4B;IAC3D,IAAW,OAAO,IAAI,YAAY,EAAE,CAA8B;gBAEtD,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,MAAM,GAAG,MAAM,EAAE,OAAO,UAAQ;CAKtE;AAED;;;IAGI;AACJ,qBAAa,YAAY;IACd,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;gBACV,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM;CAI/D;AAED;;;IAGI;AACJ,qBAAa,SAAS;IACX,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,MAAM,CAAC;gBACb,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,MAAM;CAIlE;AAgDD,OAAO,QAAQ,cAAc,CAAC;IAC1B,UAAU,KAAK,CAAC;QACZ,OAAO,EAAE,WAAW,IAAI,MAAM,EAAE,CAAC;QACjC,OAAO,EAAE,WAAW,IAAI,MAAM,EAAE,CAAC;QACjC,OAAO,EAAE,aAAa,IAAI,QAAQ,EAAE,CAAC;KACxC;IACD,UAAU,MAAM,CAAC;QACb,OAAO,EAAE,YAAY,IAAI,MAAM,EAAE,CAAC;QAClC,OAAO,EAAE,YAAY,IAAI,MAAM,EAAE,CAAC;QAClC,OAAO,EAAE,cAAc,IAAI,QAAQ,EAAE,CAAC;KACzC;CACJ;AAED,OAAO,QAAQ,WAAW,CAAC;IACvB,UAAU,WAAW,CAAC;QAClB,OAAO,EAAE,iBAAiB,IAAI,MAAM,EAAE,CAAC;QACvC,OAAO,EAAE,iBAAiB,IAAI,MAAM,EAAE,CAAC;QACvC,OAAO,EAAE,mBAAmB,IAAI,QAAQ,EAAE,CAAC;KAC9C;IACD,UAAU,eAAe,CAAC;QACtB,OAAO,EAAE,qBAAqB,IAAI,MAAM,EAAE,CAAC;QAC3C,OAAO,EAAE,qBAAqB,IAAI,MAAM,EAAE,CAAC;QAC3C,OAAO,EAAE,uBAAuB,IAAI,QAAQ,EAAE,CAAC;KAClD;IACD,UAAU,SAAS,CAAC;QAChB,OAAO,EAAE,eAAe,IAAI,MAAM,EAAE,CAAC;QACrC,OAAO,EAAE,eAAe,IAAI,MAAM,EAAE,CAAC;KACxC;IACD,UAAU,YAAY,CAAC;QACnB,OAAO,EAAE,kBAAkB,IAAI,MAAM,EAAE,CAAC;QACxC,OAAO,EAAE,kBAAkB,IAAI,MAAM,EAAE,CAAC;KAC3C;CACJ;AAED,cAAc;AACd,iBAAS,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,GAAE,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAa,EAAE,OAAO,kBAAqB,eAGpH;AAED,cAAc;AACd,iBAAS,iBAAiB,CAAC,KAAK,EAAE,YAAY,EAAE,OAAO,kBAAqB,eAK3E;AAED,cAAc;AACd,iBAAS,qBAAqB,CAAC,KAAK,EAAE,gBAAgB,EAAE,OAAO,kBAAqB,mBAEnF;AAED,cAAc;AACd,iBAAS,kBAAkB,CAAC,CAAC,EAAE,OAAO,gBAErC;AAED,cAAc;AACd,iBAAS,eAAe,CAAC,CAAC,EAAE,UAAU,aAErC;AAoDD,cAAc;AACd,iBAAS,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,cAiCnE;AA0FD,cAAc;AACd,iBAAS,YAAY,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,UAyB/C;AAED,cAAc;AACd,iBAAS,WAAW,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,UA6C5C;AAED,cAAc;AACd,iBAAS,iBAAiB,CAAC,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,UAoB9D;AAED,cAAc;AACd,iBAAS,qBAAqB,CAAC,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,eAAe,UAO1E;AAED,cAAc;AACd,iBAAS,eAAe,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,UAEnD;AAED,cAAc;AACd,iBAAS,kBAAkB,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,UAEzD;AAUD,cAAc;AACd,KAAK,oBAAoB,GAAG,CAAC,CAAC,SAAS,aAAa,OAAO,CAAC,SAAS,aAAa,CAAC,MAAM,GAAG,MAAM,GAC5F,CAAC,SAAS,aAAa,CAAC,WAAW,GAAG,WAAW,GACjD,CAAC,SAAS,aAAa,CAAC,eAAe,GAAG,eAAe,GAAG,KAAK,CAAC", "file": "message.d.ts", "sourceRoot": "../../src"}