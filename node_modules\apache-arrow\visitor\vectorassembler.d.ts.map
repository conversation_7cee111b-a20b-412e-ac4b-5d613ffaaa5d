{"version": 3, "sources": ["visitor/vectorassembler.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EAAE,IAAI,EAAa,MAAM,YAAY,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAGlD,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AACrE,OAAO,EACH,QAAQ,EAAE,UAAU,EACpB,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAC7D,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAChH,MAAM,YAAY,CAAC;AAGpB,cAAc;AACd,MAAM,WAAW,eAAgB,SAAQ,OAAO;IAC5C,KAAK,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAC3D,SAAS,CAAC,CAAC,SAAS,QAAQ,EAAE,KAAK,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC;IACjE,UAAU,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;IAC7E,UAAU,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;IAEvF,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAC/C,QAAQ,CAAC,CAAC,SAAS,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAC7C,UAAU,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACjD,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAC/C,cAAc,CAAC,CAAC,SAAS,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACzD,WAAW,CAAC,CAAC,SAAS,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACnD,gBAAgB,CAAC,CAAC,SAAS,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAC7D,oBAAoB,CAAC,CAAC,SAAS,eAAe,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACrE,SAAS,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAChD,cAAc,CAAC,CAAC,SAAS,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACzD,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAC/C,YAAY,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACrD,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAC/C,WAAW,CAAC,CAAC,SAAS,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACnD,UAAU,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACjD,aAAa,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACvD,aAAa,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACvD,kBAAkB,CAAC,CAAC,SAAS,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACjE,QAAQ,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;CACjD;AAED,cAAc;AACd,qBAAa,eAAgB,SAAQ,OAAO;IAExC,kBAAkB;WACJ,QAAQ,CAAC,CAAC,SAAS,MAAM,GAAG,WAAW,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;IAS3E,OAAO;IA8BA,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;IAIxC,eAAe,CAAC,CAAC,SAAS,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAK1D,IAAW,KAAK,gBAA0B;IAC1C,IAAW,OAAO,sBAA4B;IAC9C,IAAW,UAAU,WAA+B;IACpD,IAAW,aAAa,mBAAkC;IAE1D,SAAS,CAAC,WAAW,SAAK;IAC1B,SAAS,CAAC,MAAM,EAAE,SAAS,EAAE,CAAM;IACnC,SAAS,CAAC,QAAQ,EAAE,eAAe,EAAE,CAAM;IAC3C,SAAS,CAAC,cAAc,EAAE,YAAY,EAAE,CAAM;CACjD", "file": "vectorassembler.d.ts", "sourceRoot": "../src"}