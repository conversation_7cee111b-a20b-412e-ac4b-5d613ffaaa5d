{"version": 3, "file": "socket.d.ts", "sourceRoot": "", "sources": ["../../../lib/client/socket.ts"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,YAAY,EAAQ,MAAM,aAAa,CAAC;AACjD,OAAO,GAAG,MAAM,UAAU,CAAC;AAC3B,OAAO,GAAG,MAAM,UAAU,CAAC;AAG3B,OAAO,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAE9C,KAAK,UAAU,GAAG;IAChB,GAAG,CAAC,EAAE,KAAK,CAAC;CACb,CAAC;AAEF,KAAK,yBAAyB,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,MAAM,CAAC;AAE3F,KAAK,wBAAwB,GAAG;IAC9B;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB;;;;;OAKG;IACH,iBAAiB,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,yBAAyB,CAAC;IAC/D;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB,CAAA;AAED,KAAK,eAAe,GAAG,wBAAwB,GAAG,UAAU,GAAG,IAAI,CACjE,GAAG,CAAC,iBAAiB,EACrB,SAAS,GAAG,QAAQ,GAAG,UAAU,GAAG,UAAU,GAAG,MAAM,CACxD,GAAG;IACF,IAAI,CAAC,EAAE,MAAM,CAAC;CACf,CAAC;AAEF,KAAK,eAAe,GAAG,wBAAwB,GAAG,GAAG,CAAC,iBAAiB,GAAG;IACxE,GAAG,EAAE,IAAI,CAAC;IACV,IAAI,EAAE,MAAM,CAAC;CACd,CAAA;AAED,KAAK,eAAe,GAAG,wBAAwB,GAAG,IAAI,CACpD,GAAG,CAAC,iBAAiB,EACrB,SAAS,GAAG,QAAQ,GAAG,UAAU,GAAG,UAAU,CAC/C,GAAG;IACF,GAAG,EAAE,KAAK,CAAC;CACZ,CAAA;AAED,MAAM,MAAM,qBAAqB,GAAG,eAAe,GAAG,eAAe,CAAC;AAEtE,MAAM,MAAM,kBAAkB,GAAG,qBAAqB,GAAG,eAAe,CAAC;AAEzE,MAAM,MAAM,oBAAoB,GAAG,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;AAEjE,MAAM,CAAC,OAAO,OAAO,WAAY,SAAQ,YAAY;;IAWnD,IAAI,MAAM,YAET;IAID,IAAI,OAAO,YAEV;IAMD,IAAI,WAAW,WAEd;gBAEW,SAAS,EAAE,oBAAoB,EAAE,OAAO,CAAC,EAAE,kBAAkB;IAqHnE,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IA2F9B,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;IAchD,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAW/C,KAAK;IAQL,OAAO;IASP,aAAa;IAWb,GAAG;IAKH,KAAK;IAKL,wBAAwB,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO;CAazD"}