{"version": 3, "sources": ["ipc/writer.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;;AAGrB,0CAAoC;AACpC,6CAAqC;AACrC,4CAAsC;AACtC,wCAA+C;AAE/C,sDAAgD;AAChD,kDAAkD;AAClD,gDAAuD;AACvD,wCAA4D;AAC5D,oEAA8D;AAC9D,+CAA+D;AAC/D,sEAAgE;AAChE,0EAAoE;AACpE,8EAAwE;AACxE,iDAAuE;AACvE,sDAAsF;AACtF,uDAA0F;AAC1F,iDAAgI;AAkBhI,MAAa,iBAA2C,SAAQ,+BAA2B;IAEvF,kBAAkB;IAClB,aAAa;IACN,MAAM,CAAC,WAAW,CAAC,OAAkD;QACxE,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;IACvE,CAAC;IACD,kBAAkB;IACX,MAAM,CAAC,UAAU;IACpB,aAAa;IACb,gBAA6E;IAC7E,aAAa;IACb,gBAAyD;QAEzD,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;IACtE,CAAC;IAED,YAAY,OAAwC;QAChD,KAAK,EAAE,CAAC;QAMF,cAAS,GAAG,CAAC,CAAC;QACd,aAAQ,GAAG,KAAK,CAAC;QAG3B,aAAa;QACH,UAAK,GAAG,IAAI,0BAAc,EAAE,CAAC;QAC7B,YAAO,GAAkB,IAAI,CAAC;QAC9B,sBAAiB,GAAgB,EAAE,CAAC;QACpC,uBAAkB,GAAgB,EAAE,CAAC;QACrC,sBAAiB,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC9C,4BAAuB,GAAG,IAAI,GAAG,EAAkB,CAAC;QAf1D,IAAA,oBAAQ,EAAC,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,WAAW,EAAE,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC,CAAC;QACpF,IAAI,CAAC,YAAY,GAAG,CAAC,OAAO,OAAO,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5F,IAAI,CAAC,qBAAqB,GAAG,CAAC,OAAO,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,KAAK,CAAC;IAC5H,CAAC;IAgBM,QAAQ,CAAC,OAAY,KAAK;QAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAA6B,CAAC;IACjE,CAAC;IAGM,YAAY,CAAC,OAAY,KAAK;QACjC,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAqC,CAAC;IAC7E,CAAC;IAMM,QAAQ,CAAC,KAA6F;QACzG,IAAI,IAAA,qBAAS,EAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC;aAAM,IAAI,IAAA,2BAAe,EAAiB,KAAK,CAAC,EAAE,CAAC;YAChD,OAAO,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACtC,CAAC;QACD,OAAO,QAAQ,CAAC,IAAI,EAAO,KAAK,CAAC,CAAC;IACtC,CAAC;IAED,IAAW,MAAM,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAC1C,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;IACvE,WAAW,CAAC,OAAkC,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3F,YAAY,CAAC,OAAyB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAEpF,KAAK;QACR,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACtC,CAAC;IACM,KAAK,CAAC,MAAY;QACrB,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;IACM,MAAM;QACT,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACxE,OAAO,IAAI,CAAC;IAChB,CAAC;IACM,KAAK,CAAC,OAA2C,IAAI,CAAC,KAAK,EAAE,SAA2B,IAAI;QAC/F,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,YAAY,0BAAc,CAAC,EAAE,CAAC;YAC5D,IAAI,CAAC,KAAK,GAAG,IAAsB,CAAC;QACxC,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,KAAK,GAAG,IAAI,0BAAc,EAAE,CAAC;YAClC,IAAI,IAAI,IAAI,IAAA,+BAAmB,EAAC,IAAI,CAAC,EAAE,CAAC;gBACpC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACrD,CAAC;iBAAM,IAAI,IAAI,IAAI,IAAA,gCAAoB,EAAC,IAAI,CAAC,EAAE,CAAC;gBAC5C,IAAI,CAAC,YAAY,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;QACnC,IAAI,CAAC,uBAAuB,GAAG,IAAI,GAAG,EAAE,CAAC;QAEzC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,IAAA,kCAAc,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;YACrD,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;gBACjB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;gBACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;gBACtB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,OAAqE;QAC9E,IAAI,MAAM,GAAqB,IAAI,CAAC;QAEpC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACnD,CAAC;aAAM,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,SAAS,CAAC;QACtC,CAAC;aAAM,IAAI,OAAO,YAAY,gBAAK,IAAI,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAChE,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,SAAS,CAAC;QACtC,CAAC;aAAM,IAAI,OAAO,YAAY,4BAAW,IAAI,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACtE,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,SAAS,CAAC;QACtC,CAAC;QAED,IAAI,MAAM,IAAI,CAAC,IAAA,kCAAc,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YAClD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;YACxB,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QACnC,CAAC;QAED,IAAI,OAAO,YAAY,4BAAW,EAAE,CAAC;YACjC,IAAI,CAAC,CAAC,OAAO,YAAY,qDAAoC,CAAC,EAAE,CAAC;gBAC7D,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;aAAM,IAAI,OAAO,YAAY,gBAAK,EAAE,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC;aAAM,IAAI,IAAA,sBAAU,EAAC,OAAO,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;IACL,CAAC;IAES,aAAa,CAA0B,OAAmB,EAAE,SAAS,GAAG,CAAC;QAC/E,MAAM,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC;QACxB,MAAM,MAAM,GAAG,oBAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC;QACzC,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,MAAM,WAAW,GAAG,CAAC,cAAc,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3D,MAAM,aAAa,GAAG,WAAW,GAAG,cAAc,GAAG,UAAU,CAAC;QAEhE,IAAI,OAAO,CAAC,UAAU,KAAK,uBAAa,CAAC,WAAW,EAAE,CAAC;YACnD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,mBAAS,CAAC,WAAW,EAAE,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QACjG,CAAC;aAAM,IAAI,OAAO,CAAC,UAAU,KAAK,uBAAa,CAAC,eAAe,EAAE,CAAC;YAC9D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,mBAAS,CAAC,WAAW,EAAE,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAChG,CAAC;QAED,4EAA4E;QAC5E,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QACD,qDAAqD;QACrD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC;QACrD,uBAAuB;QACvB,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;YAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAAC,CAAC;QAChD,oBAAoB;QACpB,OAAO,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;IAC7C,CAAC;IAES,MAAM,CAAC,KAA2B;QACxC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,MAAM,MAAM,GAAG,IAAA,wBAAY,EAAC,KAAK,CAAC,CAAC;YACnC,IAAI,MAAM,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;gBAClC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACzB,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,UAAU,CAAC;YACxC,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,YAAY,CAAC,MAAiB;QACpC,OAAO,IAAI,CAAC,aAAa,CAAC,oBAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,aAAa;IACH,YAAY,CAAC,MAAiB;QACpC,YAAY;QACZ,OAAO,IAAI,CAAC,qBAAqB;YAC7B,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IAES,WAAW;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAK,CAAC,CAAC;IAC9B,CAAC;IAES,aAAa,CAAC,MAAc;QAClC,OAAO,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACnE,CAAC;IAES,iBAAiB,CAAC,KAAqB;QAC7C,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,oCAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACtF,MAAM,WAAW,GAAG,IAAI,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;QAClF,MAAM,OAAO,GAAG,oBAAO,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QACtD,OAAO,IAAI;aACN,kBAAkB,CAAC,KAAK,CAAC;aACzB,aAAa,CAAC,OAAO,CAAC;aACtB,iBAAiB,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAES,qBAAqB,CAAC,UAAgB,EAAE,EAAU,EAAE,OAAO,GAAG,KAAK;QACzE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,oCAAe,CAAC,QAAQ,CAAC,IAAI,kBAAM,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACzG,MAAM,WAAW,GAAG,IAAI,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;QACtF,MAAM,eAAe,GAAG,IAAI,QAAQ,CAAC,eAAe,CAAC,WAAW,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;QAC/E,MAAM,OAAO,GAAG,oBAAO,CAAC,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;QAC1D,OAAO,IAAI;aACN,aAAa,CAAC,OAAO,CAAC;aACtB,iBAAiB,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAES,iBAAiB,CAAC,OAA0B;QAClD,IAAI,MAAuB,CAAC;QAC5B,IAAI,IAAY,EAAE,OAAe,CAAC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;YAC5C,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1D,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACpB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC3C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBAChC,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,kBAAkB,CAAC,KAAqB;;QAC9C,KAAK,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YAChD,MAAM,MAAM,GAAG,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI,mCAAI,EAAE,CAAC;YACtC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACtD,MAAM,MAAM,GAAG,MAAA,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,EAAE,CAAC,mCAAI,CAAC,CAAC;YACzD,+EAA+E;YAC/E,yHAAyH;YACzH,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1D,4CAA4C;gBAC5C,oHAAoH;gBACpH,KAAK,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE;oBAAE,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,EAAE,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YACpG,CAAC;iBAAM,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;gBAChC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;oBAAE,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;YAC1F,CAAC;YACD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;YAC3C,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACxD,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AA5PD,8CA4PC;AAED,cAAc;AACd,MAAa,uBAAiD,SAAQ,iBAAoB;IAKtF,kBAAkB;IACX,MAAM,CAAC,QAAQ,CAA0B,KAAU,EAAE,OAAwC;QAChG,MAAM,MAAM,GAAG,IAAI,uBAAuB,CAAI,OAAO,CAAC,CAAC;QACvD,IAAI,IAAA,qBAAS,EAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC;aAAM,IAAI,IAAA,2BAAe,EAAiB,KAAK,CAAC,EAAE,CAAC;YAChD,OAAO,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACnC,CAAC;CACJ;AAfD,0DAeC;AAED,cAAc;AACd,MAAa,qBAA+C,SAAQ,iBAAoB;IAKpF,kBAAkB;IACX,MAAM,CAAC,QAAQ,CAA0B,KAAU;QACtD,MAAM,MAAM,GAAG,IAAI,qBAAqB,EAAK,CAAC;QAC9C,IAAI,IAAA,qBAAS,EAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC;aAAM,IAAI,IAAA,2BAAe,EAAiB,KAAK,CAAC,EAAE,CAAC;YAChD,OAAO,aAAa,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACnC,CAAC;IAED;QACI,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC7B,CAAC;IAED,aAAa;IACH,YAAY,CAAC,MAAiB;QACpC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;IAES,qBAAqB,CAAC,UAAgB,EAAE,EAAU,EAAE,OAAO,GAAG,KAAK;QACzE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;QACzF,CAAC;QACD,OAAO,KAAK,CAAC,qBAAqB,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;IAChE,CAAC;IAES,YAAY,CAAC,MAAiB;QACpC,MAAM,MAAM,GAAG,gBAAM,CAAC,MAAM,CAAC,IAAI,gBAAM,CACnC,MAAM,EAAE,yBAAe,CAAC,EAAE,EAC1B,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,iBAAiB,CAClD,CAAC,CAAC;QACH,OAAO,KAAK;aACP,YAAY,CAAC,MAAM,CAAC,CAAC,mCAAmC;aACxD,MAAM,CAAC,MAAM,CAAC,CAAC,uBAAuB;aACtC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,8BAA8B;aACvE,WAAW,EAAE,CAAC,CAAC,wBAAwB;IAChD,CAAC;CACJ;AA5CD,sDA4CC;AAED,cAAc;AACd,MAAa,qBAA+C,SAAQ,iBAAoB;IAOpF,kBAAkB;IACX,MAAM,CAAC,QAAQ,CAA0D,KAAU;QACtF,OAAO,IAAI,qBAAqB,EAAK,CAAC,QAAQ,CAAC,KAAY,CAAC,CAAC;IACjE,CAAC;IAKD;QACI,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,8BAA8B,GAAG,EAAE,CAAC;IAC7C,CAAC;IAES,aAAa,KAAK,OAAO,IAAI,CAAC,CAAC,CAAC;IAC1C,aAAa;IACH,YAAY,CAAC,MAAiB,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC;IAChD,YAAY,CAAC,MAAiB;QACpC,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAChI,CAAC;IACS,kBAAkB,CAAC,KAAqB;QAC9C,IAAI,KAAK,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IACS,qBAAqB,CAAC,UAAgB,EAAE,EAAU,EAAE,OAAO,GAAG,KAAK;QACzE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QACtE,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,mBAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC;IAChB,CAAC;IACS,iBAAiB,CAAC,KAAqB;QAC7C,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC;IAChB,CAAC;IACM,KAAK;QACR,IAAI,IAAI,CAAC,8BAA8B,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjD,IAAI,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;YACxC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,8BAA8B,EAAE,CAAC;gBACtD,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACpC,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACzB,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;gBACxD,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;gBAC7D,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,mBAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACzB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,8BAA8B,GAAG,EAAE,CAAC;QACzC,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAEzB,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;CACJ;AAxED,sDAwEC;AAED,cAAc;AACd,SAAS,QAAQ,CAA0B,MAA4B,EAAE,KAA0C;IAC/G,IAAI,MAAM,GAAG,KAAiC,CAAC;IAC/C,IAAI,KAAK,YAAY,gBAAK,EAAE,CAAC;QACzB,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC;QACvB,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IACD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QACzB,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC;IACD,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;AAC3B,CAAC;AAED,cAAc;AACd,SAAe,aAAa,CAA0B,MAA4B,EAAE,OAAsC;;;;;YACtH,gBAA0B,YAAA,sBAAA,OAAO,CAAA,+EAAE,CAAC;gBAAV,uBAAO;gBAAP,WAAO;gBAAtB,MAAM,KAAK,KAAA,CAAA;gBAClB,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;;;;;;;;;QACD,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;IAC3B,CAAC;CAAA;AAED,cAAc;AACd,SAAS,WAAW,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAS;IAChD,MAAM,SAAS,GAAG,IAAI,wCAAiB,EAAE,CAAC;IAC1C,OAAO;QACH,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ;QAClC,MAAM,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;QAC7B,UAAU,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACzE,YAAY,EAAE,CAAC,kBAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;YACrD,IAAI,EAAE,IAAI,CAAC,EAAE;YACb,WAAW,EAAE,IAAI,CAAC,SAAS;YAC3B,WAAW,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;SAC7C;KACJ,CAAC;AACN,CAAC;AAED,cAAc;AACd,SAAS,qBAAqB,CAAC,UAAgB,EAAE,EAAU,EAAE,OAAO,GAAG,KAAK;IACxE,MAAM,CAAC,OAAO,CAAC,GAAG,4CAAmB,CAAC,QAAQ,CAAC,IAAI,4BAAW,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;IACtF,OAAO,IAAI,CAAC,SAAS,CAAC;QAClB,IAAI,EAAE,EAAE;QACR,SAAS,EAAE,OAAO;QAClB,MAAM,EAAE;YACJ,OAAO,EAAE,UAAU,CAAC,MAAM;YAC1B,SAAS,EAAE,OAAO;SACrB;KACJ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAChB,CAAC;AAED,cAAc;AACd,SAAS,iBAAiB,CAAC,OAAoB;IAC3C,MAAM,CAAC,OAAO,CAAC,GAAG,4CAAmB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACxD,OAAO,IAAI,CAAC,SAAS,CAAC;QAClB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,SAAS,EAAE,OAAO;KACrB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAChB,CAAC", "file": "writer.js", "sourceRoot": "../src"}