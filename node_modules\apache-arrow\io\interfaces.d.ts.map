{"version": 3, "sources": ["io/interfaces.ts"], "names": [], "mappings": ";;;AAmBA,YAAY,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AACnD,OAAO,KAAK,EAAE,eAAe,EAAE,QAAQ,IAAI,cAAc,EAAE,MAAM,aAAa,CAAC;AAE/E,cAAc;AACd,eAAO,MAAM,aAAa,EAAE,GAAoD,CAAC;AAEjF,cAAc;AACd,MAAM,MAAM,aAAa,GAAG;IAAE,MAAM,EAAE,GAAG,CAAC;IAAC,OAAO,CAAC,EAAE,GAAG,EAAE,CAAC;IAAC,YAAY,CAAC,EAAE,GAAG,EAAE,CAAA;CAAE,CAAC;AACnF,cAAc;AACd,MAAM,MAAM,wBAAwB,GAAG;IAAE,IAAI,EAAE,OAAO,GAAG,SAAS,CAAC;IAAC,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAAC,aAAa,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC;AAE7H,cAAc;AACd,qBAAa,SAAS;IACN,OAAO,CAAC,KAAK;gBAAL,KAAK,EAAE,aAAa;IACxC,IAAW,MAAM,IAAI,GAAG,CAAiC;IACzD,IAAW,OAAO,IAAI,GAAG,EAAE,CAAmD;IAC9E,IAAW,YAAY,IAAI,GAAG,EAAE,CAAwD;CAC3F;AAED,cAAc;AACd,MAAM,WAAW,QAAQ,CAAC,CAAC;IAEvB,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEpC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAC9C,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAC9C,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;IACjD,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;IAClD,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;CAC1D;AAED,cAAc;AACd,MAAM,WAAW,QAAQ,CAAC,CAAC;IACvB,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/B,KAAK,IAAI,IAAI,CAAC;IACd,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;IACtB,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC;CAC7B;AAED,cAAc;AACd,MAAM,WAAW,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAE,SAAQ,QAAQ,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC;IACpG,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,qBAAqB,CAAC,SAAS,CAAC,CAAC;IAC3D,WAAW,CAAC,OAAO,CAAC,EAAE,wBAAwB,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;IAC3E,YAAY,CAAC,OAAO,CAAC,EAAE,eAAe,GAAG,cAAc,CAAC;CAC3D;AAED,cAAc;AACd,8BAAsB,eAAe,CAAC,CAAC;aAEnB,WAAW,CAAC,OAAO,CAAC,EAAE,wBAAwB,GAAG,cAAc,CAAC,CAAC,CAAC;aAClE,YAAY,CAAC,OAAO,CAAC,EAAE,eAAe,GAAG,cAAc;IAEhE,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;IAG7C,IAAI,CAAC,CAAC,SAAS,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE;QAAE,GAAG,CAAC,EAAE,OAAO,CAAA;KAAE;IAG9E,MAAM,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,iBAAiB;IAC/D,WAAW,CAAC,CAAC,SAAS,cAAc,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE;QAAE,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;QAAC,QAAQ,EAAE,CAAC,CAAA;KAAE,EAAE,OAAO,CAAC,EAAE,iBAAiB;IAInI,SAAS,CAAC,UAAU,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;IACzC,OAAO,CAAC,aAAa;IAIrB,SAAS,CAAC,WAAW,CAAC,EAAE,cAAc,CAAC;IACvC,OAAO,CAAC,cAAc;CAGzB;AAED,cAAc;AACd,KAAK,UAAU,CAAC,CAAC,IAAI;IAAE,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;IAAC,MAAM,EAAE,CAAC,MAAM,CAAC,EAAE,GAAG,KAAK,IAAI,CAAA;CAAE,CAAC;AAEtG,cAAc;AACd,qBAAa,UAAU,CAAC,SAAS,GAAG,UAAU,EAAE,SAAS,GAAG,SAAS,CAAE,SAAQ,eAAe,CAAC,SAAS,CACpG,YAAW,qBAAqB,CAAC,SAAS,CAAC,EAAE,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC;IAEnF,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE,CAAM;IACpC,SAAS,CAAC,MAAM,CAAC,EAAE;QAAE,KAAK,EAAE,GAAG,CAAA;KAAE,CAAC;IAClC,SAAS,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;IACxC,SAAS,CAAC,qBAAqB,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC;IACxD,SAAS,CAAC,SAAS,EAAE,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,CAAM;;IAOlE,IAAW,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,CAAgC;IACrD,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG;IACzB,KAAK,CAAC,KAAK,EAAE,SAAS;IAOtB,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG;IAOjB,KAAK;IAWL,CAAC,MAAM,CAAC,aAAa,CAAC;IACtB,WAAW,CAAC,OAAO,CAAC,EAAE,wBAAwB;IAO9C,YAAY,CAAC,OAAO,CAAC,EAAE,eAAe;IAOhC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG;IACb,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG;IAEd,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;IACrD,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;IAC3D,IAAI,CAAC,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IAchE,SAAS,CAAC,WAAW;CAMxB", "file": "interfaces.d.ts", "sourceRoot": "../src"}