{"version": 3, "sources": ["io/whatwg/builder.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;;AAKrB,qDAAiD;AAWjD,cAAc;AACd,SAAgB,uBAAuB,CAAwC,OAA0C;IACrH,OAAO,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACzC,CAAC;AAFD,0DAEC;AAED,cAAc;AACd,MAAa,gBAAgB;IAYzB,YAAY,OAA0C;QAElD,kEAAkE;QAR9D,eAAU,GAAG,CAAC,CAAC;QACf,cAAS,GAAG,KAAK,CAAC;QAClB,kBAAa,GAAG,CAAC,CAAC;QAQtB,MAAM,EACF,CAAC,kBAAkB,CAAC,EAAE,gBAAgB,EACtC,CAAC,kBAAkB,CAAC,EAAE,gBAAgB,EACtC,CAAC,kBAAkB,CAAC,EAAE,gBAAgB,GAAG,OAAO,KAEhD,OAAO,EADJ,cAAc,kBACjB,OAAO,EALL,4DAKL,CAAU,CAAC;QAEZ,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,IAAA,0BAAW,EAAW,cAAc,CAAC,CAAC;QACtD,IAAI,CAAC,QAAQ,GAAG,gBAAgB,KAAK,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe,CAAC;QAE7E,MAAM,EAAE,CAAC,eAAe,CAAC,EAAE,qBAAqB,GAAG,gBAAgB,KAAK,OAAO,CAAC,CAAC,CAAC,SAAA,CAAC,EAAI,EAAE,CAAA,CAAC,CAAC,CAAC,IAAI,EAAE,qBAAQ,gBAAgB,CAAE,CAAC;QAC7H,MAAM,EAAE,CAAC,eAAe,CAAC,EAAE,qBAAqB,GAAG,gBAAgB,KAAK,OAAO,CAAC,CAAC,CAAC,SAAA,CAAC,EAAI,EAAE,CAAA,CAAC,CAAC,CAAC,IAAI,EAAE,qBAAQ,gBAAgB,CAAE,CAAC;QAE7H,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,cAAc,CAAY;YAC7C,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC5C,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3E,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/E,EAAE;YACC,eAAe,EAAE,qBAAqB;YACtC,MAAM,EAAE,gBAAgB,KAAK,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,eAAe;SACvE,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,cAAc,CAAC;YAClC,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC3C,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACvE,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SACnF,EAAE;YACC,eAAe,EAAE,qBAAqB;YACtC,MAAM,EAAE,CAAC,KAA0B,EAAE,EAAE,CAAC,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC;SACpF,CAAC,CAAC;IACP,CAAC;IAEO,6BAA6B,CAAC,KAA0B;QAC5D,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;QACxC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;IAC7C,CAAC;IAEO,WAAW,CAAC,OAA0B,EAAE,UAA6D;QACzG,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YAAC,OAAO;QAAC,CAAC;QACnC,IAAI,IAAI,CAAC,aAAa,IAAI,UAAU,CAAC,WAAY,EAAE,CAAC;YAChD,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvE,CAAC;QACD,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;gBAC9C,EAAE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YACvE,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC;gBAC7C,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;IACL,CAAC;IAEO,QAAQ,CAAC,UAAsD,EAAE,KAAuB;QAC5F,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACnE,CAAC;CACJ;AA3ED,4CA2EC;AAED,cAAc,CAAC,MAAM,WAAW,GAAG,CAA2B,KAA8B,EAAE,EAAE,WAAC,OAAA,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,mCAAI,CAAC,CAAA,EAAA,CAAC;AACpH,cAAc,CAAC,MAAM,eAAe,GAAG,CAA2B,KAA8B,EAAE,EAAE,WAAC,OAAA,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,UAAU,mCAAI,CAAC,CAAA,EAAA,CAAC", "file": "builder.js", "sourceRoot": "../../src"}