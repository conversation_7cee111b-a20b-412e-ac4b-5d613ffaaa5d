{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/parse-url.ts"], "names": ["searchParamsToUrlQuery", "parseRelativeUrl", "parseUrl", "url", "startsWith", "parsedURL", "URL", "hash", "hostname", "href", "pathname", "port", "protocol", "query", "searchParams", "search"], "mappings": "AAEA,SAASA,sBAAsB,QAAQ,gBAAe;AACtD,SAASC,gBAAgB,QAAQ,uBAAsB;AAavD,OAAO,SAASC,SAASC,GAAW;IAClC,IAAIA,IAAIC,UAAU,CAAC,MAAM;QACvB,OAAOH,iBAAiBE;IAC1B;IAEA,MAAME,YAAY,IAAIC,IAAIH;IAC1B,OAAO;QACLI,MAAMF,UAAUE,IAAI;QACpBC,UAAUH,UAAUG,QAAQ;QAC5BC,MAAMJ,UAAUI,IAAI;QACpBC,UAAUL,UAAUK,QAAQ;QAC5BC,MAAMN,UAAUM,IAAI;QACpBC,UAAUP,UAAUO,QAAQ;QAC5BC,OAAOb,uBAAuBK,UAAUS,YAAY;QACpDC,QAAQV,UAAUU,MAAM;IAC1B;AACF"}