// automatically generated by the FlatBuffers compiler, do not modify
export { <PERSON><PERSON><PERSON> } from './buffer.mjs';
export { Int } from './int.mjs';
export { SparseMatrixCompressedAxis } from './sparse-matrix-compressed-axis.mjs';
export { SparseMatrixIndexCSX } from './sparse-matrix-index-csx.mjs';
export { SparseTensor } from './sparse-tensor.mjs';
export { SparseTensorIndex, unionToSparseTensorIndex, unionListToSparseTensorIndex } from './sparse-tensor-index.mjs';
export { SparseTensorIndexCOO } from './sparse-tensor-index-coo.mjs';
export { SparseTensorIndexCSF } from './sparse-tensor-index-csf.mjs';
export { TensorDim } from './tensor-dim.mjs';
export { Type, unionToType, unionListToType } from './type.mjs';

//# sourceMappingURL=SparseTensor_generated.mjs.map
