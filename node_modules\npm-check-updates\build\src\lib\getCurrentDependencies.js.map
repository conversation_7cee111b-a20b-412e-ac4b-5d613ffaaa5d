{"version": 3, "file": "getCurrentDependencies.js", "sourceRoot": "", "sources": ["../../../src/lib/getCurrentDependencies.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAgC;AAKhC,wEAA+C;AAC/C,kEAAyC;AACzC,6CAAyC;AACzC,8EAAqD;AAErD,oFAAoF;AACpF,MAAM,iBAAiB,GAAG,CAAC,KAAkB,EAAE,KAAkB,EAAE,EAAE;AACnE,iDAAiD;AACjD,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;IACxB,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;IACxB,uDAAuD;IACvD,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAE,EAAE,MAAM,CAAC,UAAU,CAAC,KAAK,CAAE,CAAC,CAAA;AAEjE,uEAAuE;AACvE,MAAM,mBAAmB,GAAG,CAAC,OAAoB,EAAE,EAAE;IACnD,IAAI,CAAC,OAAO,CAAC,cAAc;QAAE,OAAO,EAAE,CAAA;IACtC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IACzD,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAA;AAC5B,CAAC,CAAA;AACD;;;;;;;;;GASG;AACH,SAAS,sBAAsB,CAAC,UAAuB,EAAE,EAAE,UAAmB,EAAE;IAC9E,MAAM,WAAW,GAAG,IAAA,4BAAkB,EAAC,OAAO,CAAC,GAAG,CAAC,CAAA;IAEnD,kDAAkD;IAClD,mFAAmF;IACnF,MAAM,eAAe,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;QAC/D,OAAO;YACL,GAAG,KAAK;YACR,GAAG,CAAC,UAAU,KAAK,gBAAgB;gBACjC,CAAC,CAAC,mBAAmB,CAAC,OAAO,CAAC;gBAC9B,CAAC,CAAC,IAAA,sBAAY,EACT,OAAO,CAAC,UAAU,CAAmB,IAAI,EAAE,EAC5C,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CACpD,CAAC;SACP,CAAA;IACH,CAAC,EAAE,EAAwB,CAAC,CAAA;IAE5B,4CAA4C;IAC5C,MAAM,mBAAmB,GAAG,IAAA,uBAAU,EAAC,OAAO,CAAC,iBAAiB,IAAI,EAAE,CAAC,CAAA;IACvE,MAAM,oBAAoB,GAAG,IAAA,sBAAY,EACvC,IAAA,sBAAY,EAAC,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,EACjE,IAAA,yBAAe,EACb,OAAO,CAAC,MAAM,IAAI,IAAI,EACtB,OAAO,CAAC,MAAM,IAAI,IAAI,EACtB,OAAO,CAAC,aAAa,IAAI,IAAI,EAC7B,OAAO,CAAC,aAAa,IAAI,IAAI,CAC9B,CACF,CAAA;IAED,OAAO,oBAAoB,CAAA;AAC7B,CAAC;AAED,kBAAe,sBAAsB,CAAA"}