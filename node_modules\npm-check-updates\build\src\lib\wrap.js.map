{"version": 3, "file": "wrap.js", "sourceRoot": "", "sources": ["../../../src/lib/wrap.ts"], "names": [], "mappings": ";;AAAA,uGAAuG;AACvG,MAAM,IAAI,GAAG,CAAC,CAAS,EAAE,aAAa,GAAG,EAAE,EAAE,EAAE;IAC7C,2CAA2C;IAC3C,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IAC7B,MAAM,QAAQ,GAAa,EAAE,CAAA;IAC7B,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;;QACvB,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACjB,OAAM;SACP;QAED,uCAAuC;QACvC,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;YACxB,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,aAAa,GAAG,CAAC,CAAC,CAAA;YAEvD,4EAA4E;YAC5E,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAA;YACtC,IAAI,WAAW,CAAC,MAAM,IAAI,aAAa,EAAE;gBACvC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBAC1B,MAAK;aACN;YAED,oEAAoE;YACpE,sCAAsC;YACtC,mFAAmF;YACnF,MAAM,UAAU,GACd,CAAA,MAAA,QAAQ;iBACL,KAAK,CAAC,EAAE,CAAC;iBACT,OAAO,EAAE;iBACT,IAAI,CAAC,EAAE,CAAC;gBACT,qDAAqD;iBACpD,KAAK,CAAC,WAAW,CAAC,0CAAE,KAAK,KAAI,CAAC,CAAA;YACnC,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC,CAAA;YAE5D,iDAAiD;YACjD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;gBAAE,MAAK;YAE5B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;YAC7B,CAAC,IAAI,IAAI,CAAC,MAAM,CAAA;SACjB;QACD,CAAC,GAAG,CAAC,CAAA;IACP,CAAC,CAAC,CAAA;IACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAA;AACnC,CAAC,CAAA;AAED,kBAAe,IAAI,CAAA"}