import { Version } from '../types/Version';
import { VersionSpec } from '../types/VersionSpec';
/**
 * Check if a version satisfies the latest, and is not beyond the latest). Ignores `v` prefix.
 *
 * @param current
 * @param latest
 * @param downgrade  Allow downgrading
 * @returns
 */
declare function isUpgradeable(current: VersionSpec, latest: Version, { downgrade }?: {
    downgrade?: boolean;
}): boolean;
export default isUpgradeable;
