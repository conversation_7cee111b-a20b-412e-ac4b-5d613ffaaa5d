{"version": 3, "file": "upgradeDependencies.js", "sourceRoot": "", "sources": ["../../../src/lib/upgradeDependencies.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uDAA8B;AAC9B,iEAAwC;AACxC,2DAAkC;AAClC,+CAAyC;AAKzC,kEAAyC;AACzC,kFAAyD;AACzD,oEAA2C;AAC3C,4DAA6C;AAS7C;;;;;;;GAOG;AACH,SAAS,mBAAmB,CAC1B,mBAA8C,EAC9C,cAA8B,EAC9B,UAAmB,EAAE;IAErB,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,IAAI,QAAQ,CAAA;IAE/C,4CAA4C;IAC5C,mBAAmB,GAAG,IAAA,sBAAY,EAAC,mBAAmB,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;IAEhF,yEAAyE;IACzE,MAAM,QAAQ,GAAG,IAAA,8BAAoB,EAAC,mBAAmB,CAAC,IAAI,WAAW,CAAC,gBAAgB,CAAA;IAE1F,oCAAoC;IACpC,MAAM,UAAU,GAAG,CAAC,OAAoB,EAAE,MAAe,EAAE,EAAE,CAC3D,WAAW,CAAC,4BAA4B,CAAC,OAAO,EAAE,MAAM,EAAE;QACxD,QAAQ;QACR,WAAW,EAAE,OAAO,CAAC,WAAW;KACjC,CAAC,CAAA;IAEJ,OAAO,IAAA,cAAI,EAAC;QACV,+DAA+D;QAC/D,CAAC,IAAwB,EAAsB,EAAE,CAC/C,IAAA,gBAAM,EAAC,IAAI,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,EAAE,CAAC,WAAW,IAAI,cAAc,CAAC;QACvE,gCAAgC;QAChC,CAAC,IAAwB,EAAsB,EAAE,CAC/C,IAAA,mBAAS,EAAC,IAAI,EAAE,CAAC,OAAe,EAAE,WAAmB,EAAE,EAAE;YACvD,MAAM,MAAM,GAAG,cAAc,CAAC,WAAW,CAAC,CAAA;YAC1C,IAAI,aAAa,GAAG,IAAI,CAAA;YACxB,IAAI,YAAY,GAAG,IAAI,CAAA;YAEvB,kBAAkB;YAClB,IAAI,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;gBACnC,aAAa,GAAG,WAAW,CAAC,aAAa,CAAC,OAAO,CAAE,CAAC,CAAC,CAAC,CAAA;aACvD;YACD,IAAI,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;gBAClC,YAAY,GAAG,WAAW,CAAC,aAAa,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC,CAAA;aACrD;YAED,iFAAiF;YACjF,IAAI,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;gBACpC,MAAM,UAAU,GAAG,WAAW,CAAC,eAAe,CAAC,OAAO,CAAE,CAAA;gBACxD,MAAM,CAAC,aAAa,CAAC,GAAG,IAAA,yBAAU,EAAC,UAAU,CAAC,CAAA;gBAC9C,aAAa,GAAG,WAAW,CAAC,SAAS,CAAC,aAAa,CAAC,CAAA;aACrD;YAED,IAAI,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;gBACnC,MAAM,SAAS,GAAG,WAAW,CAAC,eAAe,CAAC,MAAM,CAAE,CAAA;gBACtD,MAAM,CAAC,YAAY,CAAC,GAAG,IAAA,yBAAU,EAAC,SAAS,CAAC,CAAA;gBAC5C,YAAY,GAAG,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA;aACnD;YAED,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,CAAA;QACzD,CAAC,CAAC;QACJ,yCAAyC;QACzC,CAAC,IAAwB,EAAsB,EAAE,CAC/C,IAAA,gBAAM,EAAC,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAe,EAAE,IAAY,EAAE,EAAE;YAC3F,+DAA+D;YAC/D,MAAM,SAAS,GACb,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC;gBAC1B,CAAC,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,IAAA,yBAAU,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;YAC7G,OAAO,IAAA,uBAAa,EAAC,aAAa,IAAI,OAAO,EAAE,YAAY,IAAI,MAAM,EAAE,EAAE,SAAS,EAAE,CAAC,CAAA;QACvF,CAAC,CAAC;QACJ,mDAAmD;QACnD,CAAC,IAAwB,EAAyB,EAAE,CAClD,IAAA,mBAAS,EAAC,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAe,EAAE,EAAE;YAChF,MAAM,QAAQ,GAAG,UAAU,CAAC,aAAa,IAAI,OAAO,EAAE,YAAY,IAAI,MAAM,CAAC,CAAA;YAC7E,OAAO,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC;gBACpC,CAAC,CAAC,WAAW,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC;gBAChD,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC;oBAClC,CAAC,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC;oBACjD,CAAC,CAAC,QAAQ,CAAA;QACd,CAAC,CAAC;KACL,CAAC,CAAC,mBAAmB,CAAC,CAAA;AACzB,CAAC;AAED,kBAAe,mBAAmB,CAAA"}