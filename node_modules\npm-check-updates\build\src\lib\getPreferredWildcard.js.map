{"version": 3, "file": "getPreferredWildcard.js", "sourceRoot": "", "sources": ["../../../src/lib/getPreferredWildcard.ts"], "names": [], "mappings": ";;;;;AAAA,6DAAoC;AACpC,2DAAkC;AAElC,iDAA0C;AAE1C;;;;;;GAMG;AACH,SAAS,oBAAoB,CAAC,YAAkC;IAC9D,6CAA6C;IAC7C,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;QAC1C,OAAO,IAAI,CAAA;KACZ;IAED,qCAAqC;IACrC,MAAM,MAAM,GAAG,IAAA,iBAAO,EAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC,EAAE,CACxD,wBAAS,CAAC,IAAI,CAAC,CAAC,QAAgB,EAAE,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CACpE,CAAA;IAED,OAAO,MAAM,CAAC,SAAS,CAAA,CAAC,mCAAmC;IAE3D,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,CAAC,CAAA;IAEpG,yGAAyG;IACzG,MAAM,MAAM,GAAG,IAAA,gBAAM,EAAC,WAAW,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;IAEtF,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAA;AACtD,CAAC;AAED,kBAAe,oBAAoB,CAAA"}