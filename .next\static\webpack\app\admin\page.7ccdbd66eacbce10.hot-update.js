"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AdminPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [overview, setOverview] = useState({\n        systemHealth: {\n            status: \"healthy\",\n            uptime: 99.8,\n            lastCheck: new Date().toISOString()\n        },\n        quickStats: {\n            totalUsers: 1247,\n            activeToday: 156,\n            totalScans: 15634,\n            alertsCount: 3\n        },\n        recentAlerts: [\n            {\n                id: \"1\",\n                type: \"security\",\n                message: \"Multiple failed login attempts detected\",\n                severity: \"medium\",\n                timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString()\n            },\n            {\n                id: \"2\",\n                type: \"system\",\n                message: \"High API usage detected\",\n                severity: \"low\",\n                timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString()\n            },\n            {\n                id: \"3\",\n                type: \"user\",\n                message: \"New premium subscription activated\",\n                severity: \"low\",\n                timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString()\n            }\n        ],\n        systemServices: [\n            {\n                name: \"Database\",\n                status: \"online\",\n                uptime: 99.9,\n                lastCheck: new Date().toISOString()\n            },\n            {\n                name: \"API Gateway\",\n                status: \"online\",\n                uptime: 99.8,\n                lastCheck: new Date().toISOString()\n            },\n            {\n                name: \"Scanner Engine\",\n                status: \"online\",\n                uptime: 98.5,\n                lastCheck: new Date().toISOString()\n            },\n            {\n                name: \"OSINT Service\",\n                status: \"online\",\n                uptime: 99.2,\n                lastCheck: new Date().toISOString()\n            },\n            {\n                name: \"File Analyzer\",\n                status: \"maintenance\",\n                uptime: 95.1,\n                lastCheck: new Date().toISOString()\n            },\n            {\n                name: \"Bot Services\",\n                status: \"online\",\n                uptime: 97.8,\n                lastCheck: new Date().toISOString()\n            }\n        ]\n    });\n    const [loading, setLoading] = useState(false);\n    const adminModules = [\n        {\n            title: \"Dashboard\",\n            description: \"System monitoring and analytics\",\n            icon: BarChart3,\n            href: \"/admin/dashboard\",\n            color: \"blue\",\n            stats: \"\".concat(overview.quickStats.totalUsers, \" users\")\n        },\n        {\n            title: \"User Management\",\n            description: \"Manage users, roles, and permissions\",\n            icon: Users,\n            href: \"/admin/users\",\n            color: \"green\",\n            stats: \"\".concat(overview.quickStats.activeToday, \" active today\")\n        },\n        {\n            title: \"System Settings\",\n            description: \"Configure system parameters\",\n            icon: Settings,\n            href: \"/admin/settings\",\n            color: \"purple\",\n            stats: \"Global config\"\n        },\n        {\n            title: \"Bot Management\",\n            description: \"WhatsApp & Telegram bot controls\",\n            icon: Bot,\n            href: \"/admin/bots\",\n            color: \"cyan\",\n            stats: \"2 bots active\"\n        },\n        {\n            title: \"Payment System\",\n            description: \"Subscription and billing management\",\n            icon: CreditCard,\n            href: \"/admin/payments\",\n            color: \"yellow\",\n            stats: \"Revenue tracking\"\n        },\n        {\n            title: \"System Monitor\",\n            description: \"Real-time system monitoring\",\n            icon: Monitor,\n            href: \"/admin/monitoring\",\n            color: \"red\",\n            stats: \"\".concat(overview.systemHealth.uptime, \"% uptime\")\n        }\n    ];\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"online\":\n                return \"text-green-400\";\n            case \"offline\":\n                return \"text-red-400\";\n            case \"maintenance\":\n                return \"text-yellow-400\";\n            case \"healthy\":\n                return \"text-green-400\";\n            case \"warning\":\n                return \"text-yellow-400\";\n            case \"critical\":\n                return \"text-red-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"low\":\n                return \"text-blue-400\";\n            case \"medium\":\n                return \"text-yellow-400\";\n            case \"high\":\n                return \"text-orange-400\";\n            case \"critical\":\n                return \"text-red-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdminLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-400\",\n                                            children: \"\\uD83D\\uDD27 Admin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-pink\",\n                                            children: \"Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg\",\n                                    children: \"System overview and administrative controls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 mt-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full \".concat(overview.systemHealth.status === \"healthy\" ? \"bg-green-400\" : overview.systemHealth.status === \"warning\" ? \"bg-yellow-400\" : \"bg-red-400\", \" animate-pulse\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium \".concat(getStatusColor(overview.systemHealth.status)),\n                                                    children: [\n                                                        \"System \",\n                                                        overview.systemHealth.status.charAt(0).toUpperCase() + overview.systemHealth.status.slice(1)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Users, {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-blue-400 font-medium\",\n                                                    children: [\n                                                        overview.quickStats.totalUsers,\n                                                        \" Total Users\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Activity, {\n                                                    className: \"w-5 h-5 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-green-400 font-medium\",\n                                                    children: [\n                                                        overview.quickStats.activeToday,\n                                                        \" Active Today\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertTriangle, {\n                                                    className: \"w-5 h-5 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-yellow-400 font-medium\",\n                                                    children: [\n                                                        overview.quickStats.alertsCount,\n                                                        \" Alerts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 lg:mt-0 flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"System Uptime\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-mono text-green-400\",\n                                            children: [\n                                                overview.systemHealth.uptime,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setLoading(!loading),\n                                    disabled: loading,\n                                    className: \"btn-cyber-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RefreshCw, {\n                                            className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        loading ? \"Refreshing...\" : \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Total Users\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: overview.quickStats.totalUsers.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Users, {\n                                        className: \"h-8 w-8 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Active Today\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: overview.quickStats.activeToday\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Activity, {\n                                        className: \"h-8 w-8 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Total Scans\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: overview.quickStats.totalScans.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"h-8 w-8 text-cyber-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Active Alerts\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: overview.quickStats.alertsCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertTriangle, {\n                                        className: \"h-8 w-8 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Administrative Modules\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: adminModules.map((module, index)=>{\n                                const Icon = module.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    onClick: ()=>router.push(module.href),\n                                    className: \"card-cyber hover:border-cyber-primary transition-all duration-300 cursor-pointer group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg bg-\".concat(module.color, \"-500/20\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-6 w-6 text-\".concat(module.color, \"-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowUp, {\n                                                    className: \"h-5 w-5 text-gray-400 group-hover:text-cyber-primary transition-colors transform group-hover:-translate-y-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-2\",\n                                            children: module.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm mb-3\",\n                                            children: module.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: module.stats\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white\",\n                                            children: \"System Services\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Server, {\n                                            className: \"h-6 w-6 text-cyber-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: overview.systemServices.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-cyber-dark/50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 rounded-full \".concat(service.status === \"online\" ? \"bg-green-400\" : service.status === \"maintenance\" ? \"bg-yellow-400\" : \"bg-red-400\", \" animate-pulse\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: service.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: [\n                                                                        \"Uptime: \",\n                                                                        service.uptime,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium \".concat(getStatusColor(service.status)),\n                                                            children: service.status.charAt(0).toUpperCase() + service.status.slice(1)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-xs\",\n                                                            children: new Date(service.lastCheck).toLocaleTimeString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white\",\n                                            children: \"Recent Alerts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertTriangle, {\n                                            className: \"h-6 w-6 text-yellow-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        overview.recentAlerts.map((alert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-cyber-dark/50 rounded-lg border-l-4 border-l-yellow-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded-full bg-\".concat(alert.type === \"security\" ? \"red\" : alert.type === \"system\" ? \"yellow\" : \"blue\", \"-500/20 text-\").concat(alert.type === \"security\" ? \"red\" : alert.type === \"system\" ? \"yellow\" : \"blue\", \"-400\"),\n                                                                            children: alert.type.toUpperCase()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs font-medium \".concat(getSeverityColor(alert.severity)),\n                                                                            children: alert.severity.toUpperCase()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white text-sm\",\n                                                                    children: alert.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-xs mt-1\",\n                                                                    children: new Date(alert.timestamp).toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Eye, {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push(\"/admin/monitoring\"),\n                                                className: \"text-cyber-primary hover:text-cyber-secondary transition-colors text-sm\",\n                                                children: \"View All Alerts →\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-white mb-6\",\n                            children: \"Quick Actions\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/admin/users\"),\n                                    className: \"btn-cyber-secondary text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Users, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Manage Users\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/admin/settings\"),\n                                    className: \"btn-cyber-secondary text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Settings, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"System Config\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/admin/monitoring\"),\n                                    className: \"btn-cyber-secondary text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Monitor, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Monitor System\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/admin/dashboard\"),\n                                    className: \"btn-cyber-primary text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BarChart3, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Full Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"3SOnn0SKfTmCfzRyJviwa4+DecM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/page.tsx\n"));

/***/ })

});