# Test Login API
Write-Host "🧪 Testing Login API..." -ForegroundColor Cyan
Write-Host ""

$loginData = @{
    email = "<EMAIL>"
    password = "admin123"
} | ConvertTo-Json

Write-Host "📤 Sending login request..." -ForegroundColor Yellow
Write-Host "Email: <EMAIL>"
Write-Host "Password: [HIDDEN]"

try {
    $response = Invoke-RestMethod -Uri "http://localhost:3000/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
    
    Write-Host ""
    Write-Host "✅ LOGIN TEST PASSED!" -ForegroundColor Green
    Write-Host "🎉 User successfully authenticated" -ForegroundColor Green
    Write-Host "👤 User ID: $($response.user.id)"
    Write-Host "📧 Email: $($response.user.email)"
    Write-Host "🔑 Role: $($response.user.role)"
    Write-Host "📦 Plan: $($response.user.plan)"
    if ($response.tokens.accessToken) {
        Write-Host "🎫 Access Token: Generated"
    } else {
        Write-Host "🎫 Access Token: Missing"
    }

    if ($response.tokens.refreshToken) {
        Write-Host "🔄 Refresh Token: Generated"
    } else {
        Write-Host "🔄 Refresh Token: Missing"
    }
    
    Write-Host ""
    Write-Host "📋 Full Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3
    
} catch {
    Write-Host ""
    Write-Host "❌ LOGIN TEST FAILED!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "Status Code: $statusCode" -ForegroundColor Red
    }
}
