/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Determines whether a given node is suitable for text matching.
 *
 * @internal
 */
export declare const isSuitableNodeForTextMatching: (node: Node) => boolean;
/**
 * @internal
 */
export interface TextContent {
    full: string;
    immediate: string[];
}
/**
 * Builds the text content of a node using some custom logic.
 *
 * @remarks
 * The primary reason this function exists is due to {@link ShadowRoot}s not having
 * text content.
 *
 * @internal
 */
export declare const createTextContent: (root: Node) => TextContent;
//# sourceMappingURL=TextContent.d.ts.map