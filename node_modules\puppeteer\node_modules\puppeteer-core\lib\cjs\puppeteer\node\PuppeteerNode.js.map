{"version": 3, "file": "PuppeteerNode.js", "sourceRoot": "", "sources": ["../../../../src/node/PuppeteerNode.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,kDAM6B;AAS7B,yDAA+E;AAC/E,kDAAoD;AAEpD,2DAAmD;AACnD,6DAAqD;AAmBrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,MAAa,aAAc,SAAQ,wBAAS;IAC1C,UAAU,CAAmB;IAC7B,oBAAoB,CAAW;IAE/B;;OAEG;IACH,sBAAsB,CAAS;IAE/B;;OAEG;IACH,aAAa,GAAkB,EAAE,CAAC;IAElC;;OAEG;IACH,YACE,QAE2B;QAE3B,MAAM,EAAC,aAAa,EAAE,GAAG,cAAc,EAAC,GAAG,QAAQ,CAAC;QACpD,KAAK,CAAC,cAAc,CAAC,CAAC;QACtB,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACrC,CAAC;QACD,QAAQ,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YAC1C,KAAK,SAAS;gBACZ,IAAI,CAAC,sBAAsB,GAAG,kCAAmB,CAAC,OAAO,CAAC;gBAC1D,MAAM;YACR;gBACE,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,QAAQ,CAAC;gBAC7C,IAAI,CAAC,sBAAsB,GAAG,kCAAmB,CAAC,MAAM,CAAC;gBACzD,MAAM;QACV,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;OAKG;IACM,OAAO,CAAC,OAAuB;QACtC,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAmCG;IACH,MAAM,CAAC,UAAkC,EAAE;QACzC,MAAM,EAAC,OAAO,GAAG,IAAI,CAAC,cAAc,EAAC,GAAG,OAAO,CAAC;QAChD,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QACpC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,IACE,IAAI,CAAC,UAAU;YACf,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,IAAI,CAAC,mBAAmB,EACpD,CAAC;YACD,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;QACD,QAAQ,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACjC,KAAK,QAAQ;gBACX,IAAI,CAAC,sBAAsB,GAAG,kCAAmB,CAAC,MAAM,CAAC;gBACzD,IAAI,CAAC,UAAU,GAAG,IAAI,kCAAc,CAAC,IAAI,CAAC,CAAC;gBAC3C,MAAM;YACR,KAAK,SAAS;gBACZ,IAAI,CAAC,sBAAsB,GAAG,kCAAmB,CAAC,OAAO,CAAC;gBAC1D,IAAI,CAAC,UAAU,GAAG,IAAI,oCAAe,CAAC,IAAI,CAAC,CAAC;gBAC5C,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,oBAAoB,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;QACrE,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,OAA8B;QAC3C,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,CACL,IAAI,CAAC,UAAU,EAAE,wBAAwB,EAAE;YAC3C,IAAI,CAAC,aAAa,CAAC,eAAe;YAClC,IAAI,CAAC,sBAAuB,CAC7B,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;IAC9E,CAAC;IAED;;OAEG;IACH,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,CAAC;IAC1D,CAAC;IAED;;;;OAIG;IACH,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,IAAI,QAAQ,CAAC;IACvD,CAAC;IAED;;;;;;;OAOG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACH,WAAW,CAAC,UAAwC,EAAE;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,KAAK,CAAC,SAAS;QACb,MAAM,QAAQ,GAAG,IAAA,gCAAqB,GAAE,CAAC;QACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,QAAQ,GACZ,IAAI,CAAC,aAAa,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC,cAAe,CAAC;QACxE,MAAM,iBAAiB,GAAG,MAAM,IAAA,+BAAoB,EAAC;YACnD,QAAQ;SACT,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,cAAe,CAAC;QAEnD,MAAM,iBAAiB,GAIlB;YACH;gBACE,OAAO,EAAE,QAAQ;gBACjB,OAAO,EAAE,kBAAgB,CAAC,MAAM;gBAChC,cAAc,EAAE,EAAE;aACnB;YACD;gBACE,OAAO,EAAE,SAAS;gBAClB,OAAO,EAAE,kBAAgB,CAAC,OAAO;gBACjC,cAAc,EAAE,EAAE;aACnB;SACF,CAAC;QAEF,4BAA4B;QAC5B,KAAK,MAAM,IAAI,IAAI,iBAAiB,EAAE,CAAC;YACrC,IAAI,CAAC,cAAc,GAAG,MAAM,IAAA,yBAAc,EACxC,IAAI,CAAC,OAAO,EACZ,QAAQ,EACR,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO;gBACvB,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,eAAe;gBACpC,CAAC,CAAC,IAAI,CAAC,IAAI,kCAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAC/C,CAAC;QACJ,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAClC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YAC9B,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;QACxD,CAAC,CAAC,CACH,CAAC;QAEF,MAAM,eAAe,GAAG,IAAI,GAAG,CAC7B,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YAC9B,OAAO,OAAO,CAAC,OAAO,CAAC;QACzB,CAAC,CAAC,CACH,CAAC;QAEF,KAAK,MAAM,gBAAgB,IAAI,iBAAiB,EAAE,CAAC;YACjD,kEAAkE;YAClE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC;gBACnD,SAAS;YACX,CAAC;YACD,qEAAqE;YACrE,IACE,oBAAoB,CAAC,GAAG,CACtB,GAAG,gBAAgB,CAAC,OAAO,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAC1D,EACD,CAAC;gBACD,SAAS;YACX,CAAC;YAED,MAAM,IAAA,oBAAS,EAAC;gBACd,OAAO,EAAE,gBAAgB,CAAC,OAAO;gBACjC,QAAQ;gBACR,QAAQ;gBACR,OAAO,EAAE,gBAAgB,CAAC,OAAO;aAClC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AApRD,sCAoRC"}