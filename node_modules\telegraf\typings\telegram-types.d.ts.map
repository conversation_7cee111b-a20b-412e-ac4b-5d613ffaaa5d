{"version": 3, "file": "telegram-types.d.ts", "sourceRoot": "", "sources": ["../src/telegram-types.ts"], "names": [], "mappings": "AAAA,cAAc;AAEd,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAA;AAC5C,OAAO,EACL,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,MAAM,EACN,eAAe,EACf,kBAAkB,EAClB,eAAe,EACf,eAAe,EAChB,MAAM,uBAAuB,CAAA;AAE9B,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAA;AACrD,OAAO,EAAE,SAAS,EAAE,MAAM,UAAU,CAAA;AAEpC,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAA;AAGjC,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAA;AAGzD,MAAM,MAAM,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS;IAAE,OAAO,CAAC,EAAE,MAAM,CAAA;CAAE,GACvD,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG;IAAE,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAAE,CAAC,GAC7D,CAAC,CAAA;AAGL;;;;GAIG;AACH,KAAK,SAAS,CACZ,CAAC,SAAS,MAAM,QAAQ,EACxB,CAAC,SAAS,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,KAAK,IAC9C,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,CAAA;AAE7C,MAAM,MAAM,oBAAoB,GAAG,SAAS,CAC1C,iBAAiB,EACjB,MAAM,GAAG,SAAS,CACnB,CAAA;AACD,MAAM,MAAM,cAAc,GAAG,SAAS,CAAC,eAAe,EAAE,WAAW,CAAC,CAAA;AACpE,MAAM,MAAM,kBAAkB,GAAG,SAAS,CACxC,qBAAqB,EACrB,MAAM,GAAG,mBAAmB,CAC7B,CAAA;AACD,MAAM,MAAM,sBAAsB,GAAG,SAAS,CAC5C,mBAAmB,EACnB,iBAAiB,GAAG,SAAS,CAC9B,CAAA;AACD,MAAM,MAAM,uBAAuB,GAAG,SAAS,CAC7C,oBAAoB,EACpB,aAAa,CACd,CAAA;AACD,MAAM,MAAM,UAAU,GAAG,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;AACxD,MAAM,MAAM,YAAY,GAAG,SAAS,CAClC,aAAa,EACb,cAAc,GAAG,YAAY,CAC9B,CAAA;AACD,MAAM,MAAM,gBAAgB,GAAG,SAAS,CACtC,aAAa,EACb,cAAc,GAAG,YAAY,CAC9B,CAAA;AACD,MAAM,MAAM,iBAAiB,GAAG,SAAS,CACvC,cAAc,EACd,cAAc,GAAG,aAAa,CAC/B,CAAA;AACD,MAAM,MAAM,yBAAyB,GAAG,SAAS,CAAC,sBAAsB,CAAC,CAAA;AACzE,MAAM,MAAM,wBAAwB,GAAG,SAAS,CAAC,mBAAmB,CAAC,CAAA;AACrE,MAAM,MAAM,wBAAwB,GAAG,SAAS,CAC9C,qBAAqB,EACrB,MAAM,GAAG,OAAO,GAAG,SAAS,CAC7B,CAAA;AACD,MAAM,MAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC,CAAA;AAC7C,MAAM,MAAM,aAAa,GAAG,SAAS,CAAC,cAAc,EAAE,UAAU,CAAC,CAAA;AACjE,MAAM,MAAM,uBAAuB,GAAG,SAAS,CAC7C,oBAAoB,EACpB,aAAa,CACd,CAAA;AACD,MAAM,MAAM,uBAAuB,GAAG,SAAS,CAC7C,oBAAoB,EACpB,YAAY,GAAG,mBAAmB,GAAG,SAAS,CAC/C,CAAA;AACD,MAAM,MAAM,4BAA4B,GAAG,SAAS,CAClD,yBAAyB,EACzB,YAAY,GAAG,mBAAmB,GAAG,UAAU,GAAG,WAAW,CAC9D,CAAA;AACD,MAAM,MAAM,qBAAqB,GAAG,SAAS,CAC3C,kBAAkB,EAClB,YAAY,GAAG,mBAAmB,GAAG,OAAO,CAC7C,CAAA;AACD,MAAM,MAAM,oBAAoB,GAAG,SAAS,CAC1C,iBAAiB,EACjB,YAAY,GAAG,mBAAmB,GAAG,MAAM,CAC5C,CAAA;AACD,MAAM,MAAM,SAAS,GAAG,SAAS,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAA;AAChE,MAAM,MAAM,oBAAoB,GAAG,SAAS,CAC1C,aAAa,EACX,sBAAsB,GACtB,kBAAkB,GAClB,cAAc,GACd,mBAAmB,CACtB,CAAA;AACD,MAAM,MAAM,YAAY,GAAG,SAAS,CAAC,aAAa,EAAE,MAAM,oBAAoB,CAAC,CAAA;AAC/E,MAAM,MAAM,kBAAkB,GAAG,SAAS,CACxC,eAAe,EACf,SAAS,GAAG,YAAY,CACzB,CAAA;AACD,MAAM,MAAM,mBAAmB,GAAG,kBAAkB,CAAA;AACpD,MAAM,MAAM,aAAa,GAAG,SAAS,CAAC,cAAc,EAAE,UAAU,GAAG,WAAW,CAAC,CAAA;AAC/E,MAAM,MAAM,eAAe,GAAG,SAAS,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAA;AAClE,MAAM,MAAM,UAAU,GAAG,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;AACxD,MAAM,MAAM,SAAS,GAAG,SAAS,CAAC,UAAU,EAAE,UAAU,GAAG,SAAS,GAAG,MAAM,CAAC,CAAA;AAC9E,MAAM,MAAM,sBAAsB,GAAG,SAAS,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAA;AAC9E,MAAM,MAAM,iBAAiB,GAAG,SAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAA;AAChE,MAAM,MAAM,mBAAmB,GAAG,SAAS,CACzC,gBAAgB,EAChB,cAAc,GAAG,YAAY,CAC9B,CAAA;AACD,MAAM,MAAM,oBAAoB,GAAG,SAAS,CAC1C,iBAAiB,EACjB,cAAc,GAAG,aAAa,CAC/B,CAAA;AACD,MAAM,MAAM,mBAAmB,GAAG,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAA;AACvE,MAAM,MAAM,uBAAuB,GAAG,SAAS,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAA;AAChF,MAAM,MAAM,kBAAkB,GAAG,SAAS,CAAC,eAAe,EAAE,UAAU,CAAC,CAAA;AACvE,MAAM,MAAM,eAAe,GAAG,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;AAC5D,MAAM,MAAM,YAAY,GAAG,SAAS,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;AAC9D,MAAM,MAAM,aAAa,GAAG,SAAS,CAAC,UAAU,EAAE,YAAY,CAAC,CAAA;AAC/D,MAAM,MAAM,UAAU,GAAG,SAAS,CAChC,WAAW,EACX,UAAU,GAAG,WAAW,GAAG,OAAO,GAAG,SAAS,CAC/C,CAAA;AACD,MAAM,MAAM,UAAU,GAAG,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;AACxD,MAAM,MAAM,cAAc,GAAG,SAAS,CAAC,eAAe,EAAE,YAAY,CAAC,CAAA;AACrE,MAAM,MAAM,UAAU,GAAG,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;AACxD,MAAM,MAAM,sBAAsB,GAAG,SAAS,CAC5C,mBAAmB,EACnB,gBAAgB,CACjB,CAAA;AACD,MAAM,MAAM,qBAAqB,GAAG,SAAS,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAA;AACzE,MAAM,MAAM,mBAAmB,GAAG,SAAS,CACzC,gBAAgB,EAChB,mBAAmB,CACpB,CAAA;AAED,MAAM,MAAM,UAAU,GAClB,SAAS,CAAC,eAAe,GAAG,eAAe,CAAC,EAAE,GAC9C,SAAS,eAAe,EAAE,GAC1B,SAAS,kBAAkB,EAAE,CAAA;AAIjC,4BAA4B;AAC5B,MAAM,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,MAAM,CAAC,CAAA;AAEjE,4EAA4E;AAC5E,MAAM,MAAM,cAAc,GACtB,cAAc,GACd,OAAO,CACL,SAAS,CAAC,OAAO,CAAC,EAClB,MAAM,OAAO,CAAC,kBAAkB,GAAG,UAAU,GAAG,gBAAgB,CACjE,CAAA;AAEL,KAAK,cAAc,CAAC,CAAC,SAAS,MAAM,EAAE,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,OAAO,GACvE,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,GACnB,CAAC,GACD,KAAK,GACP,KAAK,CAAA;AAET;;;GAGG;AACH,MAAM,MAAM,QAAQ,GAAG;KACpB,CAAC,IAAI,UAAU,GAAG,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;CACtD,GAAG;KACD,CAAC,IAAI,cAAc,GAAG;QACrB,OAAO,EAAE,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAA;QAC5E,SAAS,EAAE,MAAM,CAAA;KAClB;CACF,CAAA;AAED,MAAM,WAAW,kBAAkB;IACjC,KAAK,EAAE,eAAe,CAAA;IACtB;;;;;;;;OAQG;IACH,OAAO,EAAE,MAAM,CAAA;IACf;;;;;;;;OAQG;IACH,OAAO,EAAE,MAAM,CAAA;IACf;;;;;;;;;;SAUK;IACL,IAAI,EAAE,MAAM,EAAE,CAAA;CACf"}