'use server'

import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { TelegramBotService } from '@/lib/services/telegram-bot'
import { z } from 'zod'

const connectSchema = z.object({
  token: z.string().min(1),
  webhookUrl: z.string().url().optional(),
  webhookSecret: z.string().optional()
})

// Global bot service instance
let botService: TelegramBotService | null = null

function getBotService(): TelegramBotService {
  if (!botService) {
    botService = new TelegramBotService()
  }
  return botService
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate request - only admins can connect bots
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const validatedData = connectSchema.parse(body)

    // Get bot service
    const service = getBotService()

    // Connect Telegram bot
    const session = await service.connect({
      token: validatedData.token,
      webhookUrl: validatedData.webhookUrl,
      webhookSecret: validatedData.webhookSecret
    })

    return NextResponse.json({
      success: true,
      message: 'Telegram bot connected successfully',
      data: {
        sessionId: session.id,
        botUsername: session.botUsername,
        status: session.status
      }
    })

  } catch (error) {
    console.error('Telegram bot connection error:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to connect Telegram bot',
        error: error instanceof z.ZodError ? error.errors : undefined
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Authenticate request
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      )
    }

    // Get bot service
    const service = getBotService()

    // Get session
    const session = service.getSession()

    return NextResponse.json({
      success: true,
      data: {
        session: session ? {
          id: session.id,
          botId: session.botId,
          botUsername: session.botUsername,
          status: session.status,
          lastActivity: session.lastActivity,
          connectedAt: session.connectedAt,
          error: session.error
        } : null
      }
    })

  } catch (error) {
    console.error('Error getting Telegram session:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get Telegram session'
      },
      { status: 500 }
    )
  }
}
