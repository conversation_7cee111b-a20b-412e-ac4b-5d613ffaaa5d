{"version": 3, "sources": ["../../../src/server/app-render/get-asset-query-string.ts"], "names": ["getAssetQueryString", "isDev", "process", "env", "NODE_ENV", "isTurbopack", "TURBOPACK", "ctx", "addTimestamp", "qs", "requestTimestamp", "renderOpts", "deploymentId"], "mappings": ";;;;+BAIg<PERSON>;;;eAAAA;;;AAFhB,MAAMC,QAAQC,QAAQC,GAAG,CAACC,QAAQ,KAAK;AACvC,MAAMC,cAAc,CAAC,CAACH,QAAQC,GAAG,CAACG,SAAS;AACpC,SAASN,oBACdO,GAAqB,EACrBC,YAAqB;IAErB,IAAIC,KAAK;IAET,gEAAgE;IAChE,qDAAqD;IACrD,+EAA+E;IAC/E,gDAAgD;IAChD,IAAIR,SAAS,CAACI,eAAeG,cAAc;QACzCC,MAAM,CAAC,GAAG,EAAEF,IAAIG,gBAAgB,CAAC,CAAC;IACpC;IAEA,IAAIH,IAAII,UAAU,CAACC,YAAY,EAAE;QAC/BH,MAAM,CAAC,EAAER,QAAQ,MAAM,IAAI,IAAI,EAAEM,IAAII,UAAU,CAACC,YAAY,CAAC,CAAC;IAChE;IACA,OAAOH;AACT"}