{"version": 3, "file": "ScriptProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/domains/script/ScriptProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,yDAKoC;AAMpC,mDAA8C;AAK9C,MAAa,eAAe;IACjB,uBAAuB,CAAyB;IAChD,aAAa,CAAe;IAC5B,qBAAqB,CAAC;IACtB,OAAO,CAAY;IAE5B,YACE,sBAA8C,EAC9C,YAA0B,EAC1B,oBAA0C,EAC1C,MAAiB;QAEjB,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAClD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,MAAyC;QAEzC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAuB,CAAC;QAChD,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACpB,+EAA+E;YAC/E,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAI,mCAAwB,CAAC,yBAAyB,CAAC,CAAC;YAChE,CAAC;YAED,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACxC,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;gBACnE,IAAI,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;oBAChC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBACxB,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,mCAAwB,CAChC,0BAA0B,SAAS,UAAU,CAC9C,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,6BAAa,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9D,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAE9C,MAAM,UAAU,GACd,QAAQ,CAAC,IAAI,KAAK,CAAC;YACjB,CAAC,CAAC,IAAI,GAAG,CACL,IAAI,CAAC,uBAAuB;iBACzB,mBAAmB,EAAE;iBACrB,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CACvC;YACH,CAAC,CAAC,IAAI,GAAG,CACL,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAC3D,CAAC;QAER,MAAM,aAAa,CAAC,aAAa,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAErD,OAAO;YACL,MAAM,EAAE,aAAa,CAAC,EAAE;SACzB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,MAA4C;QAE5C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAE7B,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YAC9C,EAAE,EAAE,MAAM;SACX,CAAC,CAAC;QAEH,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,MAAM,IAAI,gCAAqB,CAC7B,mCAAmC,MAAM,GAAG,CAC7C,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAE5D,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;YAChC,EAAE,EAAE,MAAM;SACX,CAAC,CAAC;QAEH,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,MAAqC;QAErC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAClD,OAAO,MAAM,KAAK,CAAC,YAAY,CAC7B,MAAM,CAAC,mBAAmB,EAC1B,MAAM,CAAC,IAAI,IAAI;YACb,IAAI,EAAE,WAAW;SAClB,EAAE,oCAAoC;QACvC,MAAM,CAAC,SAAS,IAAI,EAAE,EAAE,kCAAkC;QAC1D,MAAM,CAAC,YAAY,EACnB,MAAM,CAAC,eAAe,4CAA+B,EACrD,MAAM,CAAC,oBAAoB,IAAI,EAAE,EACjC,MAAM,CAAC,cAAc,IAAI,KAAK,CAC/B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,MAAiC;QAEjC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAClD,OAAO,MAAM,KAAK,CAAC,QAAQ,CACzB,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,YAAY,EACnB,MAAM,CAAC,eAAe,4CAA+B,EACrD,MAAM,CAAC,oBAAoB,IAAI,EAAE,EACjC,MAAM,CAAC,cAAc,IAAI,KAAK,CAC/B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,MAA+B;QAC1C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAClD,MAAM,OAAO,CAAC,GAAG,CACf,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CACjE,CAAC;QACF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,SAAS,CAAC,MAAkC;QAC1C,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,kCAAkC;YAClC,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC1D,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa;aAC9B,UAAU,CAAC;YACV,iBAAiB,EAAE,MAAM,CAAC,OAAO;YACjC,IAAI,EAAE,MAAM,CAAC,IAAI;SAClB,CAAC;aACD,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACnC,OAAO,EAAC,MAAM,EAAC,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,MAAqB;QACnC,IAAI,OAAO,IAAI,MAAM,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;gBACjC,OAAO,EAAE,MAAM,CAAC,KAAK;aACtB,CAAC,CAAC;QACL,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,OAAO,MAAM,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC1D,CAAC;CACF;AAlJD,0CAkJC"}