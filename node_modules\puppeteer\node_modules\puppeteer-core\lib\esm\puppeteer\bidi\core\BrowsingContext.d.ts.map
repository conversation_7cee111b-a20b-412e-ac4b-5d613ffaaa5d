{"version": 3, "file": "BrowsingContext.d.ts", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/BrowsingContext.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,KAAK,IAAI,MAAM,4CAA4C,CAAC;AAExE,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAE1D,OAAO,EAAkB,aAAa,EAAC,MAAM,0BAA0B,CAAC;AAExE,OAAO,KAAK,EAAC,uBAAuB,EAAC,MAAM,cAAc,CAAC;AAC1D,OAAO,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAC3C,OAAO,EAAC,WAAW,EAAC,MAAM,YAAY,CAAC;AACvC,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AACrC,OAAO,KAAK,EAAC,WAAW,EAAC,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAE3C;;GAEG;AACH,MAAM,MAAM,wBAAwB,GAAG,IAAI,CACzC,IAAI,CAAC,eAAe,CAAC,2BAA2B,EAChD,SAAS,CACV,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,aAAa,GAAG,IAAI,CAC9B,IAAI,CAAC,eAAe,CAAC,gBAAgB,EACrC,SAAS,CACV,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,YAAY,GAAG,IAAI,CAC7B,IAAI,CAAC,eAAe,CAAC,eAAe,EACpC,SAAS,CACV,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,uBAAuB,GAAG,IAAI,CACxC,IAAI,CAAC,eAAe,CAAC,0BAA0B,EAC/C,SAAS,CACV,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAAG,IAAI,CACnC,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAC1C,SAAS,CACV,CAAC;AAEF;;GAEG;AACH,qBAAa,eAAgB,SAAQ,YAAY,CAAC;IAChD,2CAA2C;IAC3C,MAAM,EAAE;QACN,iDAAiD;QACjD,MAAM,EAAE,MAAM,CAAC;KAChB,CAAC;IACF,wDAAwD;IACxD,eAAe,EAAE;QACf,gDAAgD;QAChD,eAAe,EAAE,eAAe,CAAC;KAClC,CAAC;IACF,4CAA4C;IAC5C,UAAU,EAAE;QACV,oCAAoC;QACpC,UAAU,EAAE,UAAU,CAAC;KACxB,CAAC;IACF,0CAA0C;IAC1C,OAAO,EAAE;QACP,iCAAiC;QACjC,OAAO,EAAE,OAAO,CAAC;KAClB,CAAC;IACF,6CAA6C;IAC7C,GAAG,EAAE;QACH,8BAA8B;QAC9B,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;KACvB,CAAC;IACF,2CAA2C;IAC3C,UAAU,EAAE;QACV,kCAAkC;QAClC,UAAU,EAAE,UAAU,CAAC;KACxB,CAAC;IACF,0DAA0D;IAC1D,gBAAgB,EAAE,IAAI,CAAC;IACvB,8CAA8C;IAC9C,IAAI,EAAE,IAAI,CAAC;CACZ,CAAC;;IACA,MAAM,CAAC,IAAI,CACT,WAAW,EAAE,WAAW,EACxB,MAAM,EAAE,eAAe,GAAG,SAAS,EACnC,EAAE,EAAE,MAAM,EACV,GAAG,EAAE,MAAM,GACV,eAAe;IAclB,QAAQ,CAAC,YAAY,EAAE,WAAW,CAAC;IACnC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,MAAM,EAAE,eAAe,GAAG,SAAS,CAAC;IAC7C,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC;IAGlC,OAAO;IAsIP,IAAI,QAAQ,IAAI,QAAQ,CAAC,eAAe,CAAC,CAExC;IACD,IAAI,MAAM,IAAI,OAAO,CAEpB;IACD,IAAI,QAAQ,IAAI,OAAO,CAEtB;IACD,IAAI,MAAM,IAAI,QAAQ,CAAC,WAAW,CAAC,CAElC;IACD,IAAI,GAAG,IAAI,eAAe,CAMzB;IACD,IAAI,GAAG,IAAI,MAAM,CAEhB;IAID,OAAO,CAAC,OAAO;IAST,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;IAUzB,iBAAiB,CACrB,OAAO,GAAE,wBAA6B,GACrC,OAAO,CAAC,MAAM,CAAC;IAcZ,KAAK,CAAC,YAAY,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAgB5C,eAAe,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAW7C,QAAQ,CACZ,GAAG,EAAE,MAAM,EACX,IAAI,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc,GACzC,OAAO,CAAC,UAAU,CAAC;IAiBhB,MAAM,CAAC,OAAO,GAAE,aAAkB,GAAG,OAAO,CAAC,UAAU,CAAC;IAgBxD,KAAK,CAAC,OAAO,GAAE,YAAiB,GAAG,OAAO,CAAC,MAAM,CAAC;IAclD,gBAAgB,CAAC,OAAO,GAAE,uBAA4B,GAAG,OAAO,CAAC,IAAI,CAAC;IAWtE,WAAW,CAAC,OAAO,GAAE,kBAAuB,GAAG,OAAO,CAAC,IAAI,CAAC;IAW5D,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IAWlE,cAAc,IAAI,OAAO,CAAC,IAAI,CAAC;IAUrC,iBAAiB,CAAC,OAAO,EAAE,MAAM,GAAG,WAAW;IAQzC,gBAAgB,CACpB,mBAAmB,EAAE,MAAM,EAC3B,OAAO,GAAE,uBAA4B,GACpC,OAAO,CAAC,MAAM,CAAC;IAcZ,mBAAmB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAIxD,CAAC,aAAa,CAAC,IAAI,IAAI;CAQxB"}