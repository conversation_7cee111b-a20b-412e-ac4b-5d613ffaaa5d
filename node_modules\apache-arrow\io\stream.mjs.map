{"version": 3, "sources": ["io/stream.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;AAErB,OAAO,cAAc,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAC7C,OAAO,EAAE,aAAa,EAAsB,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAChF,OAAO,EAAE,YAAY,EAAE,eAAe,EAAwB,MAAM,mBAAmB,CAAC;AAExF,OAAO,EACH,SAAS,EAAE,eAAe,EAC1B,UAAU,EAAE,eAAe,EAC3B,mBAAmB,EAAE,oBAAoB,EAC5C,MAAM,mBAAmB,CAAC;AAO3B,cAAc;AACd,MAAM,OAAO,cAA4D,SAAQ,UAAyB;IAC/F,KAAK,CAAC,KAAwC;QACjD,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC,KAAK,CAAC,KAAU,CAAC,CAAC;QACnC,CAAC;IACL,CAAC;IAGM,QAAQ,CAAC,IAAI,GAAG,KAAK;QACxB,OAAO,IAAI;YACP,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YACrC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACpD,CAAC;IAGM,YAAY,CAAC,IAAI,GAAG,KAAK;QAC5B,OAAO,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,OAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAS,EAAE;;YAClE,MAAM,OAAO,GAAG,EAAE,CAAC;YACnB,IAAI,UAAU,GAAG,CAAC,CAAC;;gBACnB,KAA0B,eAAA,KAAA,cAAA,IAAI,CAAA,IAAA,sDAAE,CAAC;oBAAP,cAAI;oBAAJ,WAAI;oBAAnB,MAAM,KAAK,KAAA,CAAA;oBAClB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACpB,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC;gBACnC,CAAC;;;;;;;;;YACD,OAAO,eAAe,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAA,CAAC,EAAE,CAAC;IACT,CAAC;CACJ;AAED,cAAc;AACd,MAAM,OAAO,UAAU;IAEnB,YAAY,MAA8D;QACtE,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAgB,CAAC,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;QAC5E,CAAC;IACL,CAAC;IACD,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,OAAO,IAAI,CAAC,CAAC,CAAC;IAC7B,IAAI,CAAC,KAAW,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACrD,KAAK,CAAC,KAAW,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACvD,MAAM,CAAC,KAAW,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACzD,IAAI,CAAC,IAAoB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAI,CAAC,IAAoB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACvE;AAED,cAAc;AACd,MAAM,OAAO,eAAe;IAExB,YAAY,MAA2L;QACnM,IAAI,MAAM,YAAY,eAAe,EAAE,CAAC;YACpC,IAAI,CAAC,MAAM,GAAI,MAA0B,CAAC,MAAM,CAAC;QACrD,CAAC;aAAM,IAAI,MAAM,YAAY,cAAc,EAAE,CAAC;YAC1C,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAqB,CAAC,cAAc,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QACtF,CAAC;aAAM,IAAI,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAqB,CAAC,cAAc,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;QACnF,CAAC;aAAM,IAAI,mBAAmB,CAAuB,MAAM,CAAC,EAAE,CAAC;YAC3D,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAqB,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;QAClF,CAAC;aAAM,IAAI,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAqB,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,IAAK,CAAC,CAAC,CAAC;QACxF,CAAC;aAAM,IAAI,UAAU,CAAuB,MAAM,CAAC,EAAE,CAAC;YAClD,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAqB,CAAC,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;QACjF,CAAC;aAAM,IAAI,SAAS,CAAuB,MAAM,CAAC,EAAE,CAAC;YACjD,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAqB,CAAC,cAAc,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QACtF,CAAC;aAAM,IAAI,eAAe,CAAuB,MAAM,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAqB,CAAC,cAAc,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;QACtF,CAAC;IACL,CAAC;IACD,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,OAAO,IAAI,CAAC,CAAC,CAAC;IAClC,IAAI,CAAC,KAAW,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACrD,KAAK,CAAC,KAAW,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACvD,MAAM,CAAC,KAAW,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAChE,IAAW,MAAM,KAAoB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IAC1D,MAAM,CAAC,MAAY,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC3D,IAAI,CAAC,IAAoB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAI,CAAC,IAAoB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACvE;AAOD,cAAc;AACd,MAAM,gBAAgB;IAClB,YAAsB,MAAmC;QAAnC,WAAM,GAAN,MAAM,CAA6B;IAAI,CAAC;IACvD,MAAM,CAAC,MAAY,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7C,IAAI,CAAC,IAAoB,IAAc,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9E,IAAI,CAAC,IAAoB,IAAc,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9E,IAAI,CAAC,IAAoB,EAAE,MAAuB,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACrG,KAAK,CAAC,KAAW,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC;IAC9G,MAAM,CAAC,KAAW,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC;CAC3H;AAED,cAAc;AACd,MAAM,qBAAqB;IAIvB,YAAsB,MAAsE;QAAtE,WAAM,GAAN,MAAM,CAAgE;QACxF,IAAI,CAAC,cAAc,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC;IAC7E,CAAC;IACY,MAAM,CAAC,MAAY;8DAAI,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;KAAA;IAChE,IAAW,MAAM,KAAoB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;IACrD,IAAI,CAAC,IAAoB;8DAAuB,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;KAAA;IAC/F,IAAI,CAAC,IAAoB;8DAAuB,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;KAAA;IAC/F,IAAI;6DAAC,IAAoB,EAAE,MAAuB,MAAM,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KAAA;IAC7G,KAAK,CAAC,KAAW;;YAC1B,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,KAAI,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA,CAAC,IAAI,aAAa,CAAC;YACtF,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC3D,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;YACvC,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC;KAAA;IACY,MAAM,CAAC,KAAW;;YAC3B,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,KAAI,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA,CAAC,IAAI,aAAa,CAAC;YACxF,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC3D,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;YACvC,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC;KAAA;CACJ", "file": "stream.mjs", "sourceRoot": "../src"}