{"version": 3, "sources": ["../../src/lib/get-project-dir.ts"], "names": ["getProjectDir", "dir", "resolvedDir", "path", "resolve", "realDir", "realpathSync", "toLowerCase", "warn", "err", "code", "detectedTypo", "detectTypo", "error", "process", "exit"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;6DALC;qBACW;4BACD;0BACE;;;;;;AAEtB,SAASA,cAAcC,GAAY;IACxC,IAAI;QACF,MAAMC,cAAcC,aAAI,CAACC,OAAO,CAACH,OAAO;QACxC,MAAMI,UAAUC,IAAAA,sBAAY,EAACJ;QAE7B,IACEA,gBAAgBG,WAChBH,YAAYK,WAAW,OAAOF,QAAQE,WAAW,IACjD;YACAC,IAAAA,SAAI,EACF,CAAC,kDAAkD,EAAEN,YAAY,aAAa,EAAEG,QAAQ,gFAAgF,CAAC;QAE7K;QAEA,OAAOA;IACT,EAAE,OAAOI,KAAU;QACjB,IAAIA,IAAIC,IAAI,KAAK,UAAU;YACzB,IAAI,OAAOT,QAAQ,UAAU;gBAC3B,MAAMU,eAAeC,IAAAA,sBAAU,EAACX,KAAK;oBACnC;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,IAAIU,cAAc;oBAChBE,IAAAA,UAAK,EACH,CAAC,MAAM,EAAEZ,IAAI,qCAAqC,EAAEU,aAAa,EAAE,CAAC;oBAEtEG,QAAQC,IAAI,CAAC;gBACf;YACF;YAEAF,IAAAA,UAAK,EACH,CAAC,uDAAuD,EAAEV,aAAI,CAACC,OAAO,CACpEH,OAAO,KACP,CAAC;YAELa,QAAQC,IAAI,CAAC;QACf;QACA,MAAMN;IACR;AACF"}