// automatically generated by the FlatBuffers compiler, do not modify

import * as flatbuffers from 'flatbuffers';

import { BodyCompressionMethod } from './body-compression-method.js';
import { CompressionType } from './compression-type.js';


/**
 * Optional compression for the memory buffers constituting IPC message
 * bodies. Intended for use with RecordBatch but could be used for other
 * message types
 */
export class BodyCompression {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):BodyCompression {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsBodyCompression(bb:flatbuffers.ByteBuffer, obj?:BodyCompression):BodyCompression {
  return (obj || new BodyCompression()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsBodyCompression(bb:flatbuffers.ByteBuffer, obj?:BodyCompression):BodyCompression {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new BodyCompression()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

/**
 * Compressor library.
 * For LZ4_FRAME, each compressed buffer must consist of a single frame.
 */
codec():CompressionType {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.readInt8(this.bb_pos + offset) : CompressionType.LZ4_FRAME;
}

/**
 * Indicates the way the record batch body was compressed
 */
method():BodyCompressionMethod {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? this.bb!.readInt8(this.bb_pos + offset) : BodyCompressionMethod.BUFFER;
}

static startBodyCompression(builder:flatbuffers.Builder) {
  builder.startObject(2);
}

static addCodec(builder:flatbuffers.Builder, codec:CompressionType) {
  builder.addFieldInt8(0, codec, CompressionType.LZ4_FRAME);
}

static addMethod(builder:flatbuffers.Builder, method:BodyCompressionMethod) {
  builder.addFieldInt8(1, method, BodyCompressionMethod.BUFFER);
}

static endBodyCompression(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

static createBodyCompression(builder:flatbuffers.Builder, codec:CompressionType, method:BodyCompressionMethod):flatbuffers.Offset {
  BodyCompression.startBodyCompression(builder);
  BodyCompression.addCodec(builder, codec);
  BodyCompression.addMethod(builder, method);
  return BodyCompression.endBodyCompression(builder);
}
}
