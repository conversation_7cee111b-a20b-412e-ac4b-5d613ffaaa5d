"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    NOT_KEYED_COMMAND: true,
    IS_READ_ONLY: true,
    /**
     * Returns a list of all configured ACL usernames
     * @param parser - The Redis command parser
     */
    parseCommand(parser) {
        parser.push('ACL', 'USERS');
    },
    transformReply: undefined
};
//# sourceMappingURL=ACL_USERS.js.map