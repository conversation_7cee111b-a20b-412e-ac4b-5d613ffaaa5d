/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/stats/route";
exports.ids = ["app/api/dashboard/stats/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/mysql2/lib sync recursive ^cardinal.*$":
/*!****************************************************!*\
  !*** ./node_modules/mysql2/lib/ sync ^cardinal.*$ ***!
  \****************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/mysql2/lib sync recursive ^cardinal.*$";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "timers":
/*!*************************!*\
  !*** external "timers" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("timers");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:timers/promises":
/*!***************************************!*\
  !*** external "node:timers/promises" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:timers/promises");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_Kode_XGuard_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/dashboard/stats/route.ts */ \"(rsc)/./app/api/dashboard/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/stats/route\",\n        pathname: \"/api/dashboard/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/stats/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\api\\\\dashboard\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_Kode_XGuard_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/dashboard/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/dashboard/stats/route.ts":
/*!******************************************!*\
  !*** ./app/api/dashboard/stats/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_services_dashboard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/services/dashboard */ \"(rsc)/./lib/services/dashboard.ts\");\n/* harmony import */ var _lib_services_stats__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/stats */ \"(rsc)/./lib/services/stats.ts\");\n\n\n\nasync function GET() {\n    try {\n        // For now, we'll use a mock user ID since auth is not fully implemented\n        // In production, this would come from the authenticated user\n        const userId = 1 // This should come from authentication\n        ;\n        // Get user dashboard statistics\n        const userStats = await _lib_services_dashboard__WEBPACK_IMPORTED_MODULE_1__.DashboardService.getUserDashboardStats(userId);\n        // Get recent activities\n        const recentActivities = await _lib_services_dashboard__WEBPACK_IMPORTED_MODULE_1__.DashboardService.getUserRecentActivities(userId, 10);\n        // Get plan usage\n        const planUsage = await _lib_services_dashboard__WEBPACK_IMPORTED_MODULE_1__.DashboardService.getUserPlanUsage(userId);\n        // Get achievements\n        const achievements = await _lib_services_dashboard__WEBPACK_IMPORTED_MODULE_1__.DashboardService.getUserAchievements(userId);\n        // Get activity chart data\n        const activityChart = await _lib_services_dashboard__WEBPACK_IMPORTED_MODULE_1__.DashboardService.getUserActivityChart(userId, 30);\n        // Get trending vulnerabilities\n        const trendingVulnerabilities = await _lib_services_stats__WEBPACK_IMPORTED_MODULE_2__.StatsService.getTrendingVulnerabilities(5);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                user: {\n                    id: userId,\n                    username: \"CyberWarrior\",\n                    email: \"<EMAIL>\",\n                    fullName: \"Alex Chen\",\n                    plan: planUsage.plan,\n                    level: userStats.level,\n                    score: userStats.score,\n                    streak: userStats.streak,\n                    rank: userStats.rank,\n                    nextLevelProgress: userStats.nextLevelProgress\n                },\n                stats: {\n                    totalScans: userStats.totalScans,\n                    vulnerabilitiesFound: userStats.vulnerabilitiesFound,\n                    osintQueries: userStats.osintQueries,\n                    fileAnalyses: userStats.fileAnalyses,\n                    cveSearches: userStats.cveSearches,\n                    dorkingQueries: userStats.dorkingQueries\n                },\n                usage: planUsage,\n                recentActivities,\n                achievements,\n                activityChart,\n                trendingVulnerabilities\n            }\n        });\n    } catch (error) {\n        console.error(\"Dashboard stats error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: \"Internal server error\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./app/api/dashboard/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database-mock.ts":
/*!******************************!*\
  !*** ./lib/database-mock.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MockDatabase: () => (/* binding */ MockDatabase),\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n// Mock database implementation for testing without MySQL\n// This provides realistic data for all features to work\n// Mock data\nconst mockUsers = [\n    {\n        id: 1,\n        username: \"cyberwarrior\",\n        email: \"<EMAIL>\",\n        full_name: \"Alex Chen\",\n        role: \"user\",\n        plan: \"Pro\",\n        level: 15,\n        score: 2500,\n        streak_days: 7,\n        email_verified: true,\n        last_active: new Date(),\n        created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)\n    },\n    {\n        id: 2,\n        username: \"admin\",\n        email: \"<EMAIL>\",\n        full_name: \"System Administrator\",\n        role: \"admin\",\n        plan: \"Elite\",\n        level: 100,\n        score: 50000,\n        streak_days: 365,\n        email_verified: true,\n        last_active: new Date(),\n        created_at: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)\n    },\n    {\n        id: 3,\n        username: \"securityexpert\",\n        email: \"<EMAIL>\",\n        full_name: \"Sarah Johnson\",\n        role: \"user\",\n        plan: \"Expert\",\n        level: 28,\n        score: 8950,\n        streak_days: 12,\n        email_verified: true,\n        last_active: new Date(Date.now() - 2 * 60 * 60 * 1000),\n        created_at: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)\n    }\n];\nconst mockScans = [\n    {\n        id: 1,\n        user_id: 1,\n        target_url: \"https://example.com\",\n        scan_type: \"comprehensive\",\n        status: \"completed\",\n        vulnerabilities_found: 15,\n        severity_critical: 2,\n        severity_high: 5,\n        severity_medium: 6,\n        severity_low: 2,\n        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 1 * 60 * 60 * 1000)\n    },\n    {\n        id: 2,\n        user_id: 1,\n        target_url: \"https://testsite.org\",\n        scan_type: \"advanced\",\n        status: \"completed\",\n        vulnerabilities_found: 8,\n        severity_critical: 0,\n        severity_high: 2,\n        severity_medium: 4,\n        severity_low: 2,\n        created_at: new Date(Date.now() - 24 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 23 * 60 * 60 * 1000)\n    },\n    {\n        id: 3,\n        user_id: 2,\n        target_url: \"https://vulnerable-app.com\",\n        scan_type: \"comprehensive\",\n        status: \"completed\",\n        vulnerabilities_found: 23,\n        severity_critical: 5,\n        severity_high: 8,\n        severity_medium: 7,\n        severity_low: 3,\n        created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000)\n    }\n];\nconst mockOSINTQueries = [\n    {\n        id: 1,\n        user_id: 1,\n        query_type: \"email\",\n        query_value: \"<EMAIL>\",\n        status: \"completed\",\n        results: {\n            breaches: [\n                \"Adobe\",\n                \"LinkedIn\"\n            ],\n            found: true\n        },\n        created_at: new Date(Date.now() - 30 * 60 * 1000),\n        completed_at: new Date(Date.now() - 25 * 60 * 1000)\n    },\n    {\n        id: 2,\n        user_id: 1,\n        query_type: \"domain\",\n        query_value: \"example.com\",\n        status: \"completed\",\n        results: {\n            subdomains: [\n                \"www\",\n                \"api\",\n                \"admin\"\n            ],\n            found: true\n        },\n        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 2 * 60 * 60 * 1000 + 5 * 60 * 1000)\n    }\n];\nconst mockFileAnalyses = [\n    {\n        id: 1,\n        user_id: 1,\n        filename: \"suspicious.exe\",\n        file_size: 2048576,\n        threat_detected: true,\n        threat_type: \"malware\",\n        status: \"completed\",\n        created_at: new Date(Date.now() - 45 * 60 * 1000),\n        completed_at: new Date(Date.now() - 40 * 60 * 1000)\n    },\n    {\n        id: 2,\n        user_id: 1,\n        filename: \"clean-file.pdf\",\n        file_size: 1024000,\n        threat_detected: false,\n        status: \"completed\",\n        created_at: new Date(Date.now() - 3 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 3 * 60 * 60 * 1000 + 2 * 60 * 1000)\n    }\n];\n// Mock database class\nclass MockDatabase {\n    static async query(sql, params = []) {\n        console.log(`Mock DB Query: ${sql}`, params);\n        // Simulate database delay\n        await new Promise((resolve)=>setTimeout(resolve, 100));\n        // Parse SQL and return appropriate mock data\n        if (sql.includes(\"SELECT * FROM users WHERE id = ?\")) {\n            const userId = params[0];\n            return mockUsers.filter((u)=>u.id === userId);\n        }\n        if (sql.includes(\"SELECT COUNT(*) as count FROM users\")) {\n            return [\n                {\n                    count: mockUsers.length\n                }\n            ];\n        }\n        if (sql.includes(\"SELECT COUNT(*) as count FROM vulnerability_scans\")) {\n            return [\n                {\n                    count: mockScans.length\n                }\n            ];\n        }\n        if (sql.includes(\"SELECT * FROM vulnerability_scans WHERE user_id = ?\")) {\n            const userId = params[0];\n            return mockScans.filter((s)=>s.user_id === userId);\n        }\n        if (sql.includes(\"SELECT * FROM osint_queries WHERE user_id = ?\")) {\n            const userId = params[0];\n            return mockOSINTQueries.filter((q)=>q.user_id === userId);\n        }\n        if (sql.includes(\"SELECT * FROM file_analyses WHERE user_id = ?\")) {\n            const userId = params[0];\n            return mockFileAnalyses.filter((f)=>f.user_id === userId);\n        }\n        if (sql.includes(\"INSERT INTO\")) {\n            // Return mock insert result\n            return {\n                insertId: Math.floor(Math.random() * 1000) + 100\n            };\n        }\n        if (sql.includes(\"UPDATE\")) {\n            // Return mock update result\n            return {\n                affectedRows: 1\n            };\n        }\n        // Default return for other queries\n        return [];\n    }\n}\n// Export mock database as default db\nconst db = MockDatabase;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/database-mock.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseUtils: () => (/* binding */ DatabaseUtils),\n/* harmony export */   RedisUtils: () => (/* binding */ RedisUtils),\n/* harmony export */   closeDatabaseConnections: () => (/* binding */ closeDatabaseConnections),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   elasticsearch: () => (/* binding */ elasticsearch),\n/* harmony export */   initRedis: () => (/* binding */ initRedis),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   initializeDatabases: () => (/* binding */ initializeDatabases),\n/* harmony export */   redis: () => (/* binding */ redis),\n/* harmony export */   testDatabaseConnection: () => (/* binding */ testDatabaseConnection),\n/* harmony export */   testElasticsearchConnection: () => (/* binding */ testElasticsearchConnection)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"(rsc)/./node_modules/mysql2/promise.js\");\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! redis */ \"(rsc)/./node_modules/redis/dist/index.js\");\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(redis__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @elastic/elasticsearch */ \"(rsc)/./node_modules/@elastic/elasticsearch/index.js\");\n/* harmony import */ var _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _database_mock__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./database-mock */ \"(rsc)/./lib/database-mock.ts\");\n\n\n\n\n\n// MySQL Database Configuration\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"rootkan\",\n    database: process.env.DB_NAME || \"db_kodexguard\",\n    charset: \"utf8mb4\",\n    timezone: \"+00:00\",\n    // Pool configuration (valid options for mysql2)\n    connectionLimit: 10,\n    queueLimit: 0,\n    // Connection configuration\n    connectTimeout: 60000,\n    // Additional MySQL2 options\n    multipleStatements: false,\n    namedPlaceholders: false\n};\n// Create MySQL connection pool\nconst mysqlPool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool(dbConfig);\n// For demo purposes, use mock database if MySQL is not available\n\nconst db = {\n    async query (sql, params = []) {\n        try {\n            // Use MySQL database\n            const [rows] = await mysqlPool.execute(sql, params);\n            return rows;\n        } catch (error) {\n            console.error(\"Database query error:\", error);\n            // For demo purposes, fallback to mock database\n            console.log(\"Falling back to mock database\");\n            return await _database_mock__WEBPACK_IMPORTED_MODULE_5__.MockDatabase.query(sql, params);\n        }\n    },\n    async close () {\n        await mysqlPool.end();\n    }\n};\n// Initialize database with schema\nasync function initializeDatabase() {\n    try {\n        console.log(\"\\uD83D\\uDDC4️ Initializing database schema...\");\n        // Read schema file\n        const schemaPath = (0,path__WEBPACK_IMPORTED_MODULE_4__.join)(process.cwd(), \"lib\", \"database\", \"schema.sql\");\n        const schema = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_3__.readFile)(schemaPath, \"utf-8\");\n        // Split schema into individual statements\n        const statements = schema.split(\";\").map((stmt)=>stmt.trim()).filter((stmt)=>stmt.length > 0 && !stmt.startsWith(\"--\"));\n        // Execute each statement\n        for (const statement of statements){\n            try {\n                await db.query(statement);\n            } catch (error) {\n                // Ignore \"table already exists\" errors\n                if (!error.message?.includes(\"already exists\")) {\n                    console.error(\"Schema error:\", error.message || error);\n                }\n            }\n        }\n        console.log(\"✅ Database schema initialized successfully\");\n        return true;\n    } catch (error) {\n        console.error(\"❌ Database schema initialization failed:\", error);\n        return false;\n    }\n}\n// Test database connection\nasync function testDatabaseConnection() {\n    try {\n        const connection = await mysqlPool.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"✅ MySQL database connected successfully\");\n        // Skip schema initialization to avoid foreign key constraint errors\n        // Database schema should be managed through migrations\n        // await initializeDatabase()\n        return true;\n    } catch (error) {\n        console.error(\"❌ MySQL database connection failed, using mock database\");\n        return true // Return true for mock database\n        ;\n    }\n}\n// Redis Configuration\nconst redisConfig = {\n    url: process.env.REDIS_URL || \"redis://localhost:6379\",\n    password: process.env.REDIS_PASSWORD || undefined,\n    socket: {\n        reconnectStrategy: (retries)=>Math.min(retries * 50, 500)\n    }\n};\n// Create Redis client\nconst redis = (0,redis__WEBPACK_IMPORTED_MODULE_1__.createClient)(redisConfig);\n// Redis connection handlers\nredis.on(\"error\", (err)=>{\n    console.error(\"❌ Redis Client Error:\", err);\n});\nredis.on(\"connect\", ()=>{\n    console.log(\"✅ Redis connected successfully\");\n});\nredis.on(\"reconnecting\", ()=>{\n    console.log(\"\\uD83D\\uDD04 Redis reconnecting...\");\n});\nredis.on(\"ready\", ()=>{\n    console.log(\"✅ Redis ready for operations\");\n});\n// Initialize Redis connection\nasync function initRedis() {\n    try {\n        if (!redis.isOpen) {\n            await redis.connect();\n        }\n        return true;\n    } catch (error) {\n        console.error(\"❌ Redis connection failed:\", error);\n        return false;\n    }\n}\n// Elasticsearch Configuration\nconst elasticsearchConfig = {\n    node: process.env.ELASTICSEARCH_URL || \"http://localhost:9200\",\n    auth: process.env.ELASTICSEARCH_USERNAME && process.env.ELASTICSEARCH_PASSWORD ? {\n        username: process.env.ELASTICSEARCH_USERNAME,\n        password: process.env.ELASTICSEARCH_PASSWORD\n    } : undefined,\n    requestTimeout: 30000,\n    pingTimeout: 3000,\n    maxRetries: 3\n};\n// Create Elasticsearch client\nconst elasticsearch = new _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__.Client(elasticsearchConfig);\n// Test Elasticsearch connection\nasync function testElasticsearchConnection() {\n    try {\n        const health = await elasticsearch.cluster.health();\n        console.log(\"✅ Elasticsearch connected successfully:\", health.cluster_name);\n        return true;\n    } catch (error) {\n        console.error(\"❌ Elasticsearch connection failed:\", error);\n        return false;\n    }\n}\n// Initialize all database connections\nasync function initializeDatabases() {\n    console.log(\"\\uD83D\\uDE80 Initializing database connections...\");\n    const results = {\n        mysql: await testDatabaseConnection(),\n        redis: await initRedis(),\n        elasticsearch: await testElasticsearchConnection()\n    };\n    const allConnected = Object.values(results).every(Boolean);\n    if (allConnected) {\n        console.log(\"✅ All databases connected successfully\");\n    } else {\n        console.warn(\"⚠️ Some database connections failed:\", results);\n    }\n    return results;\n}\n// Graceful shutdown\nasync function closeDatabaseConnections() {\n    console.log(\"\\uD83D\\uDD04 Closing database connections...\");\n    try {\n        await mysqlPool.end();\n        console.log(\"✅ MySQL connection closed\");\n    } catch (error) {\n        console.error(\"❌ Error closing MySQL connection:\", error);\n    }\n    try {\n        if (redis.isOpen) {\n            await redis.quit();\n        }\n        console.log(\"✅ Redis connection closed\");\n    } catch (error) {\n        console.error(\"❌ Error closing Redis connection:\", error);\n    }\n    try {\n        await elasticsearch.close();\n        console.log(\"✅ Elasticsearch connection closed\");\n    } catch (error) {\n        console.error(\"❌ Error closing Elasticsearch connection:\", error);\n    }\n}\n// Database utility functions\nclass DatabaseUtils {\n    // Execute query with error handling\n    static async query(sql, params) {\n        try {\n            return await db.query(sql, params || []);\n        } catch (error) {\n            console.error(\"Database query error:\", error);\n            throw error;\n        }\n    }\n    // Get single record\n    static async findOne(sql, params) {\n        const rows = await this.query(sql, params);\n        return Array.isArray(rows) && rows.length > 0 ? rows[0] : null;\n    }\n    // Get multiple records with pagination\n    static async findMany(sql, params, page = 1, limit = 10) {\n        const offset = (page - 1) * limit;\n        // Get total count\n        const countSql = sql.replace(/SELECT .+ FROM/, \"SELECT COUNT(*) as total FROM\");\n        const countResult = await this.findOne(countSql, params);\n        const total = countResult?.total || 0;\n        // Get paginated data\n        const dataSql = `${sql} LIMIT ${limit} OFFSET ${offset}`;\n        const data = await this.query(dataSql, params);\n        return {\n            data: Array.isArray(data) ? data : [],\n            total,\n            page,\n            limit\n        };\n    }\n    // Insert record\n    static async insert(table, data) {\n        const fields = Object.keys(data).join(\", \");\n        const placeholders = Object.keys(data).map(()=>\"?\").join(\", \");\n        const values = Object.values(data);\n        const sql = `INSERT INTO ${table} (${fields}) VALUES (${placeholders})`;\n        const result = await this.query(sql, values);\n        return result.insertId || data.id;\n    }\n    // Update record\n    static async update(table, data, where, whereParams) {\n        const setClause = Object.keys(data).map((key)=>`${key} = ?`).join(\", \");\n        const values = [\n            ...Object.values(data),\n            ...whereParams || []\n        ];\n        const sql = `UPDATE ${table} SET ${setClause} WHERE ${where}`;\n        const result = await this.query(sql, values);\n        return result.affectedRows > 0;\n    }\n    // Delete record\n    static async delete(table, where, whereParams) {\n        const sql = `DELETE FROM ${table} WHERE ${where}`;\n        const result = await this.query(sql, whereParams);\n        return result.affectedRows > 0;\n    }\n}\n// Redis utility functions\nclass RedisUtils {\n    // Set value with expiration\n    static async set(key, value, expireInSeconds) {\n        try {\n            const serializedValue = typeof value === \"string\" ? value : JSON.stringify(value);\n            if (expireInSeconds) {\n                await redis.setEx(key, expireInSeconds, serializedValue);\n            } else {\n                await redis.set(key, serializedValue);\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Redis set error:\", error);\n            return false;\n        }\n    }\n    // Get value\n    static async get(key) {\n        try {\n            const value = await redis.get(key);\n            if (!value) return null;\n            try {\n                return JSON.parse(value);\n            } catch  {\n                return value;\n            }\n        } catch (error) {\n            console.error(\"Redis get error:\", error);\n            return null;\n        }\n    }\n    // Delete key\n    static async del(key) {\n        try {\n            const result = await redis.del(key);\n            return result > 0;\n        } catch (error) {\n            console.error(\"Redis delete error:\", error);\n            return false;\n        }\n    }\n    // Check if key exists\n    static async exists(key) {\n        try {\n            const result = await redis.exists(key);\n            return result === 1;\n        } catch (error) {\n            console.error(\"Redis exists error:\", error);\n            return false;\n        }\n    }\n    // Increment counter\n    static async incr(key, expireInSeconds) {\n        try {\n            const result = await redis.incr(key);\n            if (expireInSeconds && result === 1) {\n                await redis.expire(key, expireInSeconds);\n            }\n            return result;\n        } catch (error) {\n            console.error(\"Redis increment error:\", error);\n            return 0;\n        }\n    }\n}\n// Export default database instance\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (db);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./lib/services/dashboard.ts":
/*!***********************************!*\
  !*** ./lib/services/dashboard.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardService: () => (/* binding */ DashboardService)\n/* harmony export */ });\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./lib/database.ts\");\n\nclass DashboardService {\n    static async getUserDashboardStats(userId) {\n        try {\n            // Get user info\n            const userResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT level, score, streak_days FROM users WHERE id = ?\", [\n                userId\n            ]);\n            const user = userResult[0];\n            // Get total scans\n            const scansResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT COUNT(*) as count, SUM(vulnerabilities_found) as vulnerabilities FROM vulnerability_scans WHERE user_id = ?\", [\n                userId\n            ]);\n            const scans = scansResult[0];\n            // Get OSINT queries\n            const osintResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT COUNT(*) as count FROM osint_queries WHERE user_id = ?\", [\n                userId\n            ]);\n            const osintQueries = osintResult[0]?.count || 0;\n            // Get file analyses\n            const fileResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT COUNT(*) as count FROM file_analyses WHERE user_id = ?\", [\n                userId\n            ]);\n            const fileAnalyses = fileResult[0]?.count || 0;\n            // Get CVE searches (from system logs)\n            const cveResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query('SELECT COUNT(*) as count FROM system_logs WHERE user_id = ? AND message LIKE \"%CVE search%\"', [\n                userId\n            ]);\n            const cveSearches = cveResult[0]?.count || 0;\n            // Get dorking queries\n            const dorkingResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT COUNT(*) as count FROM dorking_queries WHERE user_id = ?\", [\n                userId\n            ]);\n            const dorkingQueries = dorkingResult[0]?.count || 0;\n            // Get user rank\n            const rankResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT COUNT(*) + 1 as rank FROM users WHERE score > ?\", [\n                user.score\n            ]);\n            const rank = rankResult[0]?.rank || 1;\n            // Calculate next level progress\n            const currentLevelXP = user.level * 1000;\n            const nextLevelXP = (user.level + 1) * 1000;\n            const nextLevelProgress = Math.min((user.score - currentLevelXP) / (nextLevelXP - currentLevelXP) * 100, 100);\n            return {\n                totalScans: scans.count || 0,\n                vulnerabilitiesFound: scans.vulnerabilities || 0,\n                osintQueries,\n                fileAnalyses,\n                cveSearches,\n                dorkingQueries,\n                level: user.level,\n                score: user.score,\n                streak: user.streak_days,\n                rank,\n                nextLevelProgress: Math.max(nextLevelProgress, 0)\n            };\n        } catch (error) {\n            console.error(\"Error getting user dashboard stats:\", error);\n            throw new Error(\"Failed to get dashboard statistics\");\n        }\n    }\n    static async getUserRecentActivities(userId, limit = 10) {\n        try {\n            const activities = [];\n            // Get recent vulnerability scans\n            const scansResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(`\n        SELECT id, target_url, status, vulnerabilities_found, created_at, completed_at\n        FROM vulnerability_scans \n        WHERE user_id = ? \n        ORDER BY created_at DESC \n        LIMIT ?\n      `, [\n                userId,\n                Math.ceil(limit / 4)\n            ]);\n            for (const scan of scansResult){\n                activities.push({\n                    id: `scan-${scan.id}`,\n                    type: \"vulnerability_scan\",\n                    title: \"Vulnerability Scan\",\n                    description: `Scanned ${scan.target_url}`,\n                    status: scan.status,\n                    createdAt: new Date(scan.created_at),\n                    completedAt: scan.completed_at ? new Date(scan.completed_at) : undefined,\n                    results: scan.vulnerabilities_found ? {\n                        vulnerabilities: scan.vulnerabilities_found\n                    } : undefined\n                });\n            }\n            // Get recent OSINT queries\n            const osintResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(`\n        SELECT id, query_type, query_value, status, created_at, completed_at\n        FROM osint_queries \n        WHERE user_id = ? \n        ORDER BY created_at DESC \n        LIMIT ?\n      `, [\n                userId,\n                Math.ceil(limit / 4)\n            ]);\n            for (const osint of osintResult){\n                activities.push({\n                    id: `osint-${osint.id}`,\n                    type: \"osint_query\",\n                    title: \"OSINT Query\",\n                    description: `${osint.query_type} lookup for ${osint.query_value}`,\n                    status: osint.status,\n                    createdAt: new Date(osint.created_at),\n                    completedAt: osint.completed_at ? new Date(osint.completed_at) : undefined\n                });\n            }\n            // Get recent file analyses\n            const fileResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(`\n        SELECT id, filename, status, threat_detected, created_at, completed_at\n        FROM file_analyses \n        WHERE user_id = ? \n        ORDER BY created_at DESC \n        LIMIT ?\n      `, [\n                userId,\n                Math.ceil(limit / 4)\n            ]);\n            for (const file of fileResult){\n                activities.push({\n                    id: `file-${file.id}`,\n                    type: \"file_analysis\",\n                    title: \"File Analysis\",\n                    description: `Analyzed ${file.filename}`,\n                    status: file.status,\n                    createdAt: new Date(file.created_at),\n                    completedAt: file.completed_at ? new Date(file.completed_at) : undefined,\n                    results: file.threat_detected !== null ? {\n                        threatDetected: file.threat_detected\n                    } : undefined\n                });\n            }\n            // Get recent dorking queries\n            const dorkingResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(`\n        SELECT id, query_string, status, results_found, created_at, completed_at\n        FROM dorking_queries \n        WHERE user_id = ? \n        ORDER BY created_at DESC \n        LIMIT ?\n      `, [\n                userId,\n                Math.ceil(limit / 4)\n            ]);\n            for (const dorking of dorkingResult){\n                activities.push({\n                    id: `dorking-${dorking.id}`,\n                    type: \"dorking_query\",\n                    title: \"Google Dorking\",\n                    description: `Query: ${dorking.query_string.substring(0, 50)}...`,\n                    status: dorking.status,\n                    createdAt: new Date(dorking.created_at),\n                    completedAt: dorking.completed_at ? new Date(dorking.completed_at) : undefined,\n                    results: dorking.results_found ? {\n                        resultsFound: dorking.results_found\n                    } : undefined\n                });\n            }\n            // Sort by creation date and limit\n            return activities.sort((a, b)=>b.createdAt.getTime() - a.createdAt.getTime()).slice(0, limit);\n        } catch (error) {\n            console.error(\"Error getting user activities:\", error);\n            return [];\n        }\n    }\n    static async getUserPlanUsage(userId) {\n        try {\n            // Get user plan\n            const userResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT plan FROM users WHERE id = ?\", [\n                userId\n            ]);\n            const userPlan = userResult[0]?.plan || \"Free\";\n            // Get plan limits\n            const planResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT limits FROM subscription_plans WHERE name = ?\", [\n                userPlan\n            ]);\n            const limits = planResult[0] ? JSON.parse(planResult[0].limits) : {\n                vulnerabilityScans: 5,\n                osintQueries: 10,\n                fileAnalyses: 3,\n                dorkingQueries: 5,\n                cveSearches: 20,\n                maxFileSize: 10485760\n            };\n            // Get today's usage\n            const today = new Date().toISOString().split(\"T\")[0];\n            const scansUsage = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT COUNT(*) as count FROM vulnerability_scans WHERE user_id = ? AND DATE(created_at) = ?\", [\n                userId,\n                today\n            ]);\n            const osintUsage = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT COUNT(*) as count FROM osint_queries WHERE user_id = ? AND DATE(created_at) = ?\", [\n                userId,\n                today\n            ]);\n            const fileUsage = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT COUNT(*) as count FROM file_analyses WHERE user_id = ? AND DATE(created_at) = ?\", [\n                userId,\n                today\n            ]);\n            const dorkingUsage = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT COUNT(*) as count FROM dorking_queries WHERE user_id = ? AND DATE(created_at) = ?\", [\n                userId,\n                today\n            ]);\n            const cveUsage = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query('SELECT COUNT(*) as count FROM system_logs WHERE user_id = ? AND DATE(created_at) = ? AND message LIKE \"%CVE search%\"', [\n                userId,\n                today\n            ]);\n            const usage = {\n                vulnerabilityScans: scansUsage[0]?.count || 0,\n                osintQueries: osintUsage[0]?.count || 0,\n                fileAnalyses: fileUsage[0]?.count || 0,\n                dorkingQueries: dorkingUsage[0]?.count || 0,\n                cveSearches: cveUsage[0]?.count || 0\n            };\n            // Calculate percentages\n            const percentage = {\n                vulnerabilityScans: limits.vulnerabilityScans === -1 ? 0 : Math.round(usage.vulnerabilityScans / limits.vulnerabilityScans * 100),\n                osintQueries: limits.osintQueries === -1 ? 0 : Math.round(usage.osintQueries / limits.osintQueries * 100),\n                fileAnalyses: limits.fileAnalyses === -1 ? 0 : Math.round(usage.fileAnalyses / limits.fileAnalyses * 100),\n                dorkingQueries: limits.dorkingQueries === -1 ? 0 : Math.round(usage.dorkingQueries / limits.dorkingQueries * 100),\n                cveSearches: limits.cveSearches === -1 ? 0 : Math.round(usage.cveSearches / limits.cveSearches * 100)\n            };\n            return {\n                plan: userPlan,\n                limits,\n                usage,\n                percentage\n            };\n        } catch (error) {\n            console.error(\"Error getting user plan usage:\", error);\n            throw new Error(\"Failed to get plan usage\");\n        }\n    }\n    static async getUserAchievements(userId) {\n        try {\n            const result = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(`\n        SELECT b.id, b.name, b.description, b.icon, b.color, b.rarity, ub.earned_at\n        FROM user_badges ub\n        JOIN badges b ON ub.badge_id = b.id\n        WHERE ub.user_id = ?\n        ORDER BY ub.earned_at DESC\n      `, [\n                userId\n            ]);\n            return result.map((badge)=>({\n                    id: badge.id.toString(),\n                    name: badge.name,\n                    description: badge.description,\n                    icon: badge.icon,\n                    color: badge.color,\n                    rarity: badge.rarity,\n                    earnedAt: new Date(badge.earned_at)\n                }));\n        } catch (error) {\n            console.error(\"Error getting user achievements:\", error);\n            return [];\n        }\n    }\n    static async getUserActivityChart(userId, days = 30) {\n        try {\n            const result = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(`\n        SELECT \n          DATE(created_at) as date,\n          SUM(CASE WHEN 'vulnerability_scans' THEN 1 ELSE 0 END) as scans,\n          SUM(CASE WHEN 'osint_queries' THEN 1 ELSE 0 END) as queries\n        FROM (\n          SELECT created_at, 'vulnerability_scans' as type FROM vulnerability_scans WHERE user_id = ?\n          UNION ALL\n          SELECT created_at, 'osint_queries' as type FROM osint_queries WHERE user_id = ?\n        ) activities\n        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)\n        GROUP BY DATE(created_at)\n        ORDER BY date ASC\n      `, [\n                userId,\n                userId,\n                days\n            ]);\n            return result.map((row)=>({\n                    date: row.date,\n                    scans: row.scans || 0,\n                    queries: row.queries || 0\n                }));\n        } catch (error) {\n            console.error(\"Error getting user activity chart:\", error);\n            return [];\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/services/dashboard.ts\n");

/***/ }),

/***/ "(rsc)/./lib/services/stats.ts":
/*!*******************************!*\
  !*** ./lib/services/stats.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsService: () => (/* binding */ StatsService)\n/* harmony export */ });\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./lib/database.ts\");\n\nclass StatsService {\n    static async getPlatformStats() {\n        try {\n            // Get total users\n            const totalUsersResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT COUNT(*) as count FROM users\");\n            const totalUsers = totalUsersResult[0]?.count || 0;\n            // Get active users (last 24 hours)\n            const activeUsersResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT COUNT(*) as count FROM users WHERE last_active >= DATE_SUB(NOW(), INTERVAL 24 HOUR)\");\n            const activeUsers = activeUsersResult[0]?.count || 0;\n            // Get total scans\n            const totalScansResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT COUNT(*) as count FROM vulnerability_scans\");\n            const totalScans = totalScansResult[0]?.count || 0;\n            // Get vulnerabilities found\n            const vulnerabilitiesResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query('SELECT SUM(vulnerabilities_found) as total FROM vulnerability_scans WHERE status = \"completed\"');\n            const vulnerabilitiesFound = vulnerabilitiesResult[0]?.total || 0;\n            // Get threats blocked (files with threats detected)\n            const threatsResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT COUNT(*) as count FROM file_analyses WHERE threat_detected = 1\");\n            const threatsBlocked = threatsResult[0]?.count || 0;\n            // Calculate data processed (total file sizes analyzed)\n            const dataResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT SUM(file_size) as total FROM file_analyses\");\n            const totalBytes = dataResult[0]?.total || 0;\n            const dataProcessed = this.formatBytes(totalBytes);\n            // Calculate uptime (since first user registration)\n            const firstUserResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(\"SELECT MIN(created_at) as first_date FROM users\");\n            const firstDate = firstUserResult[0]?.first_date;\n            const uptime = firstDate ? this.calculateUptime(new Date(firstDate)) : \"0 days\";\n            // Calculate success rate (completed scans / total scans)\n            const completedScansResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query('SELECT COUNT(*) as count FROM vulnerability_scans WHERE status = \"completed\"');\n            const completedScans = completedScansResult[0]?.count || 0;\n            const successRate = totalScans > 0 ? Math.round(completedScans / totalScans * 100) : 100;\n            return {\n                totalUsers,\n                activeUsers,\n                totalScans,\n                vulnerabilitiesFound,\n                threatsBlocked,\n                dataProcessed,\n                uptime,\n                successRate\n            };\n        } catch (error) {\n            console.error(\"Error getting platform stats:\", error);\n            // Return default stats if database is not available\n            return {\n                totalUsers: 0,\n                activeUsers: 0,\n                totalScans: 0,\n                vulnerabilitiesFound: 0,\n                threatsBlocked: 0,\n                dataProcessed: \"0 B\",\n                uptime: \"0 days\",\n                successRate: 100\n            };\n        }\n    }\n    static async getRecentActivities(limit = 10) {\n        try {\n            const activities = [];\n            // Get recent vulnerability scans\n            const scansResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(`\n        SELECT vs.id, vs.target_url, vs.vulnerabilities_found, vs.created_at, u.username\n        FROM vulnerability_scans vs\n        JOIN users u ON vs.user_id = u.id\n        WHERE vs.status = 'completed'\n        ORDER BY vs.created_at DESC\n        LIMIT ?\n      `, [\n                Math.ceil(limit / 4)\n            ]);\n            for (const scan of scansResult){\n                activities.push({\n                    id: `scan-${scan.id}`,\n                    type: \"scan\",\n                    description: `${scan.username} scanned ${scan.target_url} and found ${scan.vulnerabilities_found} vulnerabilities`,\n                    timestamp: new Date(scan.created_at),\n                    severity: scan.vulnerabilities_found > 10 ? \"high\" : scan.vulnerabilities_found > 5 ? \"medium\" : \"low\",\n                    user: scan.username\n                });\n            }\n            // Get recent OSINT queries\n            const osintResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(`\n        SELECT oq.id, oq.query_type, oq.query_value, oq.created_at, u.username\n        FROM osint_queries oq\n        JOIN users u ON oq.user_id = u.id\n        WHERE oq.status = 'completed'\n        ORDER BY oq.created_at DESC\n        LIMIT ?\n      `, [\n                Math.ceil(limit / 4)\n            ]);\n            for (const osint of osintResult){\n                activities.push({\n                    id: `osint-${osint.id}`,\n                    type: \"osint\",\n                    description: `${osint.username} performed ${osint.query_type} lookup for ${osint.query_value}`,\n                    timestamp: new Date(osint.created_at),\n                    severity: \"medium\",\n                    user: osint.username\n                });\n            }\n            // Get recent file analyses\n            const fileResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(`\n        SELECT fa.id, fa.filename, fa.threat_detected, fa.created_at, u.username\n        FROM file_analyses fa\n        JOIN users u ON fa.user_id = u.id\n        WHERE fa.status = 'completed'\n        ORDER BY fa.created_at DESC\n        LIMIT ?\n      `, [\n                Math.ceil(limit / 4)\n            ]);\n            for (const file of fileResult){\n                activities.push({\n                    id: `file-${file.id}`,\n                    type: \"file_analysis\",\n                    description: `${file.username} analyzed ${file.filename} - ${file.threat_detected ? \"Threat detected!\" : \"Clean\"}`,\n                    timestamp: new Date(file.created_at),\n                    severity: file.threat_detected ? \"critical\" : \"low\",\n                    user: file.username\n                });\n            }\n            // Get recent user registrations\n            const usersResult = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(`\n        SELECT id, username, created_at\n        FROM users\n        ORDER BY created_at DESC\n        LIMIT ?\n      `, [\n                Math.ceil(limit / 4)\n            ]);\n            for (const user of usersResult){\n                activities.push({\n                    id: `user-${user.id}`,\n                    type: \"user_registration\",\n                    description: `${user.username} joined KodeXGuard`,\n                    timestamp: new Date(user.created_at),\n                    severity: \"low\",\n                    user: user.username\n                });\n            }\n            // Sort by timestamp and limit\n            return activities.sort((a, b)=>b.timestamp.getTime() - a.timestamp.getTime()).slice(0, limit);\n        } catch (error) {\n            console.error(\"Error getting recent activities:\", error);\n            return [];\n        }\n    }\n    static async getTrendingVulnerabilities(limit = 5) {\n        try {\n            const result = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(`\n        SELECT cve_id, description, severity, cvss_score, published_date\n        FROM cve_database\n        WHERE published_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)\n        AND cvss_score >= 7.0\n        ORDER BY cvss_score DESC, published_date DESC\n        LIMIT ?\n      `, [\n                limit\n            ]);\n            return result.map((cve, index)=>({\n                    id: cve.cve_id,\n                    cveId: cve.cve_id,\n                    title: cve.description?.substring(0, 100) + \"...\" || 0,\n                    severity: cve.severity,\n                    cvssScore: cve.cvss_score || 0,\n                    affectedSystems: Math.floor(Math.random() * 10000) + 1000,\n                    publishedDate: new Date(cve.published_date)\n                }));\n        } catch (error) {\n            console.error(\"Error getting trending vulnerabilities:\", error);\n            return [];\n        }\n    }\n    static async getUserGrowthStats() {\n        try {\n            const result = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(`\n        SELECT \n          DATE(created_at) as date,\n          COUNT(*) as users\n        FROM users\n        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)\n        GROUP BY DATE(created_at)\n        ORDER BY date ASC\n      `);\n            return result.map((row)=>({\n                    date: row.date,\n                    users: row.users\n                }));\n        } catch (error) {\n            console.error(\"Error getting user growth stats:\", error);\n            return [];\n        }\n    }\n    static async getScanStats() {\n        try {\n            const result = await _lib_database__WEBPACK_IMPORTED_MODULE_0__.db.query(`\n        SELECT \n          DATE(created_at) as date,\n          COUNT(*) as scans,\n          SUM(vulnerabilities_found) as vulnerabilities\n        FROM vulnerability_scans\n        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)\n        AND status = 'completed'\n        GROUP BY DATE(created_at)\n        ORDER BY date ASC\n      `);\n            return result.map((row)=>({\n                    date: row.date,\n                    scans: row.scans,\n                    vulnerabilities: row.vulnerabilities || 0\n                }));\n        } catch (error) {\n            console.error(\"Error getting scan stats:\", error);\n            return [];\n        }\n    }\n    static formatBytes(bytes) {\n        if (bytes === 0) return \"0 B\";\n        const k = 1024;\n        const sizes = [\n            \"B\",\n            \"KB\",\n            \"MB\",\n            \"GB\",\n            \"TB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    }\n    static calculateUptime(startDate) {\n        const now = new Date();\n        const diffTime = Math.abs(now.getTime() - startDate.getTime());\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        if (diffDays < 30) {\n            return `${diffDays} days`;\n        } else if (diffDays < 365) {\n            const months = Math.floor(diffDays / 30);\n            return `${months} month${months > 1 ? \"s\" : \"\"}`;\n        } else {\n            const years = Math.floor(diffDays / 365);\n            const remainingMonths = Math.floor(diffDays % 365 / 30);\n            return `${years} year${years > 1 ? \"s\" : \"\"} ${remainingMonths} month${remainingMonths > 1 ? \"s\" : \"\"}`;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/services/stats.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/ms","vendor-chunks/@redis","vendor-chunks/apache-arrow","vendor-chunks/@elastic","vendor-chunks/undici","vendor-chunks/mysql2","vendor-chunks/iconv-lite","vendor-chunks/flatbuffers","vendor-chunks/debug","vendor-chunks/aws-ssl-profiles","vendor-chunks/sqlstring","vendor-chunks/seq-queue","vendor-chunks/tslib","vendor-chunks/secure-json-parse","vendor-chunks/lru-cache","vendor-chunks/long","vendor-chunks/supports-color","vendor-chunks/safer-buffer","vendor-chunks/redis","vendor-chunks/named-placeholders","vendor-chunks/lru.min","vendor-chunks/is-property","vendor-chunks/hpagent","vendor-chunks/has-flag","vendor-chunks/generate-function","vendor-chunks/denque","vendor-chunks/cluster-key-slot"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();