"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/stats/route";
exports.ids = ["app/api/dashboard/stats/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_Kode_XGuard_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/dashboard/stats/route.ts */ \"(rsc)/./app/api/dashboard/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/stats/route\",\n        pathname: \"/api/dashboard/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/stats/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\api\\\\dashboard\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_Kode_XGuard_app_api_dashboard_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/dashboard/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/dashboard/stats/route.ts":
/*!******************************************!*\
  !*** ./app/api/dashboard/stats/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth_simple__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth-simple */ \"(rsc)/./lib/auth-simple.ts\");\n\n\nasync function GET(request) {\n    try {\n        // Get authenticated user\n        const authResult = await _lib_auth_simple__WEBPACK_IMPORTED_MODULE_1__.SimpleAuthService.authenticateRequest(request);\n        if (!authResult.success || !authResult.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const user = authResult.user;\n        // Generate mock dashboard statistics based on user data\n        const userStats = {\n            level: user.level,\n            score: user.score,\n            streak: user.streak_days,\n            rank: user.level > 50 ? Math.floor(Math.random() * 100) + 1 : Math.floor(Math.random() * 1000) + 100,\n            nextLevelProgress: Math.floor(user.score % 1000 / 10),\n            totalScans: Math.floor(user.score / 10) + Math.floor(Math.random() * 50),\n            vulnerabilitiesFound: Math.floor(user.score / 20) + Math.floor(Math.random() * 25),\n            osintQueries: Math.floor(user.score / 15) + Math.floor(Math.random() * 30),\n            fileAnalyses: Math.floor(user.score / 25) + Math.floor(Math.random() * 20),\n            cveSearches: Math.floor(user.score / 30) + Math.floor(Math.random() * 15),\n            dorkingQueries: Math.floor(user.score / 35) + Math.floor(Math.random() * 10)\n        };\n        // Generate mock recent activities\n        const recentActivities = [\n            {\n                id: 1,\n                type: \"vulnerability_scan\",\n                description: \"Completed vulnerability scan on target.example.com\",\n                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                severity: \"high\",\n                result: \"Found 3 critical vulnerabilities\"\n            },\n            {\n                id: 2,\n                type: \"osint_query\",\n                description: \"OSINT investigation on domain example.com\",\n                timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n                severity: \"medium\",\n                result: \"Collected 15 data points\"\n            },\n            {\n                id: 3,\n                type: \"file_analysis\",\n                description: \"Analyzed suspicious file: malware.exe\",\n                timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n                severity: \"critical\",\n                result: \"Detected Trojan.Win32.Agent\"\n            },\n            {\n                id: 4,\n                type: \"cve_search\",\n                description: \"CVE research for Apache HTTP Server\",\n                timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n                severity: \"low\",\n                result: \"Found 2 relevant CVEs\"\n            },\n            {\n                id: 5,\n                type: \"dorking\",\n                description: \"Google dorking for exposed databases\",\n                timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),\n                severity: \"medium\",\n                result: \"Discovered 5 potential targets\"\n            }\n        ];\n        // Generate mock plan usage based on user plan\n        const planLimits = {\n            \"Free\": {\n                scans: 10,\n                osint: 20,\n                files: 5,\n                cve: 50,\n                dorking: 10\n            },\n            \"Student\": {\n                scans: 50,\n                osint: 100,\n                files: 25,\n                cve: 200,\n                dorking: 50\n            },\n            \"Hobby\": {\n                scans: 100,\n                osint: 200,\n                files: 50,\n                cve: 500,\n                dorking: 100\n            },\n            \"Bughunter\": {\n                scans: 500,\n                osint: 1000,\n                files: 200,\n                cve: 2000,\n                dorking: 500\n            },\n            \"Cybersecurity\": {\n                scans: 1000,\n                osint: 2000,\n                files: 500,\n                cve: 5000,\n                dorking: 1000\n            },\n            \"Pro\": {\n                scans: 2000,\n                osint: 5000,\n                files: 1000,\n                cve: 10000,\n                dorking: 2000\n            },\n            \"Expert\": {\n                scans: 5000,\n                osint: 10000,\n                files: 2000,\n                cve: 20000,\n                dorking: 5000\n            },\n            \"Elite\": {\n                scans: -1,\n                osint: -1,\n                files: -1,\n                cve: -1,\n                dorking: -1\n            }\n        };\n        const limits = planLimits[user.plan] || planLimits.Free;\n        const planUsage = {\n            plan: user.plan,\n            limits,\n            usage: {\n                vulnerabilityScans: userStats.totalScans,\n                osintQueries: userStats.osintQueries,\n                fileAnalyses: userStats.fileAnalyses,\n                dorkingQueries: userStats.dorkingQueries,\n                cveSearches: userStats.cveSearches\n            },\n            percentage: {\n                vulnerabilityScans: limits.scans === -1 ? 0 : Math.min(userStats.totalScans / limits.scans * 100, 100),\n                osintQueries: limits.osint === -1 ? 0 : Math.min(userStats.osintQueries / limits.osint * 100, 100),\n                fileAnalyses: limits.files === -1 ? 0 : Math.min(userStats.fileAnalyses / limits.files * 100, 100),\n                dorkingQueries: limits.dorking === -1 ? 0 : Math.min(userStats.dorkingQueries / limits.dorking * 100, 100),\n                cveSearches: limits.cve === -1 ? 0 : Math.min(userStats.cveSearches / limits.cve * 100, 100)\n            }\n        };\n        // Generate mock achievements\n        const achievements = [\n            {\n                id: 1,\n                title: \"First Scan\",\n                description: \"Complete your first vulnerability scan\",\n                icon: \"shield\",\n                unlocked: userStats.totalScans > 0,\n                unlockedAt: userStats.totalScans > 0 ? new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString() : null,\n                rarity: \"common\"\n            },\n            {\n                id: 2,\n                title: \"OSINT Expert\",\n                description: \"Perform 100 OSINT queries\",\n                icon: \"eye\",\n                unlocked: userStats.osintQueries >= 100,\n                unlockedAt: userStats.osintQueries >= 100 ? new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString() : null,\n                rarity: \"rare\"\n            },\n            {\n                id: 3,\n                title: \"Vulnerability Hunter\",\n                description: \"Find 50 vulnerabilities\",\n                icon: \"bug\",\n                unlocked: userStats.vulnerabilitiesFound >= 50,\n                unlockedAt: userStats.vulnerabilitiesFound >= 50 ? new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString() : null,\n                rarity: \"epic\"\n            },\n            {\n                id: 4,\n                title: \"Streak Master\",\n                description: \"Maintain a 30-day streak\",\n                icon: \"flame\",\n                unlocked: user.streak_days >= 30,\n                unlockedAt: user.streak_days >= 30 ? new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString() : null,\n                rarity: \"legendary\"\n            }\n        ];\n        // Generate mock activity chart data (last 30 days)\n        const activityChart = Array.from({\n            length: 30\n        }, (_, i)=>{\n            const date = new Date();\n            date.setDate(date.getDate() - (29 - i));\n            return {\n                date: date.toISOString().split(\"T\")[0],\n                scans: Math.floor(Math.random() * 10),\n                osint: Math.floor(Math.random() * 15),\n                files: Math.floor(Math.random() * 5),\n                cve: Math.floor(Math.random() * 20),\n                dorking: Math.floor(Math.random() * 8)\n            };\n        });\n        // Generate mock trending vulnerabilities\n        const trendingVulnerabilities = [\n            {\n                id: \"CVE-2024-0001\",\n                title: \"Critical RCE in Apache HTTP Server\",\n                severity: \"critical\",\n                score: 9.8,\n                description: \"Remote code execution vulnerability in Apache HTTP Server 2.4.x\",\n                affectedSystems: 15420,\n                discoveredDate: \"2024-01-15\",\n                trending: true\n            },\n            {\n                id: \"CVE-2024-0002\",\n                title: \"SQL Injection in WordPress Plugin\",\n                severity: \"high\",\n                score: 8.5,\n                description: \"SQL injection vulnerability in popular WordPress plugin\",\n                affectedSystems: 8930,\n                discoveredDate: \"2024-01-12\",\n                trending: true\n            },\n            {\n                id: \"CVE-2024-0003\",\n                title: \"XSS in React Component Library\",\n                severity: \"medium\",\n                score: 6.2,\n                description: \"Cross-site scripting vulnerability in React component library\",\n                affectedSystems: 5670,\n                discoveredDate: \"2024-01-10\",\n                trending: false\n            }\n        ];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                user: {\n                    id: user.id,\n                    username: user.username,\n                    email: user.email,\n                    fullName: user.full_name,\n                    role: user.role,\n                    plan: user.plan,\n                    level: user.level,\n                    score: user.score,\n                    streak: user.streak_days,\n                    rank: userStats.rank,\n                    nextLevelProgress: userStats.nextLevelProgress,\n                    emailVerified: user.email_verified,\n                    lastActive: user.last_active,\n                    createdAt: user.created_at\n                },\n                stats: {\n                    totalScans: userStats.totalScans,\n                    vulnerabilitiesFound: userStats.vulnerabilitiesFound,\n                    osintQueries: userStats.osintQueries,\n                    fileAnalyses: userStats.fileAnalyses,\n                    cveSearches: userStats.cveSearches,\n                    dorkingQueries: userStats.dorkingQueries\n                },\n                usage: planUsage,\n                recentActivities,\n                achievements,\n                activityChart,\n                trendingVulnerabilities\n            }\n        });\n    } catch (error) {\n        console.error(\"Dashboard stats error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: \"Internal server error\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/dashboard/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth-simple.ts":
/*!****************************!*\
  !*** ./lib/auth-simple.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleAuthService: () => (/* binding */ SimpleAuthService)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key\";\nconst JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || \"your-super-secret-refresh-key\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"1h\";\nconst JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || \"7d\";\nconst BCRYPT_ROUNDS = 12;\n// Mock users for testing\nconst mockUsers = [\n    {\n        id: 1,\n        username: \"admin\",\n        email: \"<EMAIL>\",\n        full_name: \"Administrator\",\n        role: \"admin\",\n        plan: \"Elite\",\n        level: 100,\n        score: 10000,\n        streak_days: 365,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"admin123\"\n    },\n    {\n        id: 2,\n        username: \"testuser\",\n        email: \"<EMAIL>\",\n        full_name: \"Test User\",\n        role: \"user\",\n        plan: \"Free\",\n        level: 1,\n        score: 0,\n        streak_days: 0,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"test123\"\n    }\n];\nclass SimpleAuthService {\n    // Hash password\n    static async hashPassword(password) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, BCRYPT_ROUNDS);\n    }\n    // Verify password\n    static async verifyPassword(password, hash) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hash);\n    }\n    // Generate JWT tokens\n    static generateTokens(user) {\n        const payload = {\n            id: user.id,\n            username: user.username,\n            email: user.email,\n            role: user.role,\n            plan: user.plan\n        };\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n        const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_REFRESH_SECRET, {\n            expiresIn: JWT_REFRESH_EXPIRES_IN\n        });\n        return {\n            accessToken,\n            refreshToken,\n            expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds\n        };\n    }\n    // Verify JWT token\n    static verifyToken(token, isRefreshToken = false) {\n        try {\n            const secret = isRefreshToken ? JWT_REFRESH_SECRET : JWT_SECRET;\n            console.log(\"\\uD83D\\uDD0D Verifying token with secret length:\", secret.length);\n            console.log(\"\\uD83D\\uDD0D Token length:\", token.length);\n            const result = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n            console.log(\"✅ Token verification successful:\", result);\n            return result;\n        } catch (error) {\n            console.log(\"❌ Token verification error:\", error instanceof Error ? error.message : String(error));\n            return null;\n        }\n    }\n    // Register new user\n    static async register(data) {\n        try {\n            // Check if user already exists\n            const existingUser = mockUsers.find((u)=>u.email === data.email || u.username === data.username);\n            if (existingUser) {\n                return {\n                    success: false,\n                    message: \"User already exists with this email or username\"\n                };\n            }\n            // Create new user\n            const newUser = {\n                id: mockUsers.length + 1,\n                username: data.username,\n                email: data.email,\n                full_name: data.fullName || \"\",\n                role: \"user\",\n                plan: \"Free\",\n                level: 1,\n                score: 0,\n                streak_days: 0,\n                email_verified: false,\n                status: \"active\",\n                last_active: new Date(),\n                created_at: new Date(),\n                password: data.password\n            };\n            // Add to mock users\n            mockUsers.push(newUser);\n            // Remove password from user object\n            const { password, ...userWithoutPassword } = newUser;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                message: \"Registration failed\"\n            };\n        }\n    }\n    // Login user\n    static async login(email, password) {\n        try {\n            // Find user by email\n            const user = mockUsers.find((u)=>u.email === email && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Verify password (simple comparison for testing)\n            if (password !== user.password) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Remove password from user object\n            const { password: userPassword, ...userWithoutPassword } = user;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"Login failed\"\n            };\n        }\n    }\n    // Authenticate request\n    static async authenticateRequest(request) {\n        try {\n            // Get token from Authorization header or cookies\n            let token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n            if (!token) {\n                // Try to get from cookies\n                const cookies = request.headers.get(\"cookie\");\n                console.log(\"\\uD83C\\uDF6A Cookies in middleware:\", cookies);\n                if (cookies) {\n                    const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n                    token = tokenMatch ? tokenMatch[1] : undefined;\n                    console.log(\"\\uD83D\\uDD11 Token from cookies:\", token ? \"Found\" : \"Not found\");\n                }\n            }\n            if (!token) {\n                console.log(\"❌ No token found in headers or cookies\");\n                return {\n                    success: false,\n                    message: \"No token provided\"\n                };\n            }\n            // Verify token\n            console.log(\"\\uD83D\\uDD0D Verifying token in middleware...\");\n            const decoded = this.verifyToken(token);\n            if (!decoded) {\n                console.log(\"❌ Token verification failed in middleware\");\n                return {\n                    success: false,\n                    message: \"Invalid token\"\n                };\n            }\n            console.log(\"✅ Token decoded successfully:\", decoded.id, decoded.username);\n            // Get user from mock data\n            const user = mockUsers.find((u)=>u.id === decoded.id);\n            if (!user) {\n                console.log(\"❌ User not found in mock data:\", decoded.id);\n                return {\n                    success: false,\n                    message: \"User not found\"\n                };\n            }\n            console.log(\"✅ User found in middleware:\", user.username);\n            const { password, ...userWithoutPassword } = user;\n            return {\n                success: true,\n                user: userWithoutPassword\n            };\n        } catch (error) {\n            console.error(\"Authentication error:\", error);\n            return {\n                success: false,\n                message: \"Authentication failed\"\n            };\n        }\n    }\n    // Logout user (for mock, just return success)\n    static async logout(userId, _sessionToken) {\n        // In a real implementation, this would remove the session from database\n        console.log(`User ${userId} logged out`);\n    }\n    // Refresh token\n    static async refreshToken(refreshToken) {\n        try {\n            // Verify refresh token\n            const decoded = this.verifyToken(refreshToken, true);\n            if (!decoded) {\n                return {\n                    success: false,\n                    message: \"Invalid refresh token\"\n                };\n            }\n            // Get user data\n            const user = mockUsers.find((u)=>u.id === decoded.id && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"User not found or inactive\"\n                };\n            }\n            const { password, ...userWithoutPassword } = user;\n            // Generate new tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update user last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            return {\n                success: false,\n                message: \"Token refresh failed\"\n            };\n        }\n    }\n    // Get user by ID\n    static async getUserById(userId) {\n        try {\n            const user = mockUsers.find((u)=>u.id === userId);\n            if (!user) return null;\n            const { password, ...userWithoutPassword } = user;\n            return userWithoutPassword;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            return null;\n        }\n    }\n    // Get all users (admin only)\n    static getAllUsers() {\n        return mockUsers.map((user)=>{\n            const { password, ...userWithoutPassword } = user;\n            return userWithoutPassword;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth-simple.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fstats%2Froute&page=%2Fapi%2Fdashboard%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();