{"name": "parse-github-url", "description": "Parse a github URL into an object.", "version": "1.0.3", "homepage": "https://github.com/jonschlinkert/parse-github-url", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> (https://github.com/b<PERSON>ri)", "<PERSON> (https://twitter.com/doowb)", "<PERSON> (http://twitter.com/jmendiara)", "<PERSON> (jeremyruppel.github.io)", "<PERSON> (http://twitter.com/jonschlink<PERSON>)", "<PERSON> (cookpete.com)", "<PERSON> (https://github.com/alferov)", "<PERSON> (https://willbar.com)"], "repository": "jonschlinkert/parse-github-url", "bugs": {"url": "https://github.com/jonschlinkert/parse-github-url/issues"}, "license": "MIT", "files": ["cli.js", "index.js"], "main": "index.js", "bin": {"parse-github-url": "./cli.js"}, "engines": {"node": ">= 0.10"}, "scripts": {"lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "tape": "^5.8.1"}, "keywords": ["branch", "git", "github", "match", "parse", "regex", "repo", "test", "url", "user", "username"], "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}}