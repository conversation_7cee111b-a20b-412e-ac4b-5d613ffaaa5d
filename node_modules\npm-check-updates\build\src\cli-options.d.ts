import CLIOption from './types/CLIOption';
import { Index } from './types/IndexType';
/** Renders the extended help for an option with usage information. */
export declare const renderExtendedHelp: (option: CLIOption, { markdown }?: {
    markdown?: boolean | undefined;
}) => string;
export declare const cliOptionsMap: Index<CLIOption<any>>;
declare const cliOptionsSorted: CLIOption<any>[];
export default cliOptionsSorted;
