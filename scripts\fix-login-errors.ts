#!/usr/bin/env ts-node

import { testDatabaseConnection, closeDatabaseConnections, db } from '../lib/database'

async function fixLoginErrors() {
  console.log('🔧 Fixing Login Errors...\n')

  try {
    // Test database connection
    const connected = await testDatabaseConnection()
    if (!connected) {
      console.error('❌ Database connection failed')
      process.exit(1)
    }

    // 1. Add missing last_active column to users table
    console.log('🔄 Adding missing last_active column to users table...')
    await addColumnSafely('users', 'last_active', 'TIMESTAMP NULL')

    // 2. Fix user_sessions table to use VARCHAR for user_id
    console.log('🔄 Fixing user_sessions table structure...')
    await fixUserSessionsTable()

    // 3. Create missing cve_database table
    console.log('🔄 Creating missing cve_database table...')
    await createCveDatabase()

    // 4. Update StatsService to handle missing columns gracefully
    console.log('🔄 Updating existing users with default values...')
    await updateUsersDefaults()

    console.log('\n✅ Login errors fixed successfully!')
    
  } catch (error) {
    console.error('❌ Fix login errors failed:', error)
    process.exit(1)
  } finally {
    await closeDatabaseConnections()
  }
}

async function addColumnSafely(tableName: string, columnName: string, columnDef: string) {
  try {
    await db.query(`ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnDef}`)
    console.log(`✅ Added column: ${tableName}.${columnName}`)
  } catch (error: any) {
    if (error.code === 'ER_DUP_FIELDNAME') {
      console.log(`ℹ️ Column ${tableName}.${columnName} already exists`)
    } else {
      console.log(`⚠️ Error adding column ${tableName}.${columnName}: ${error.message}`)
    }
  }
}

async function fixUserSessionsTable() {
  try {
    // Check if user_sessions table exists
    const [tables] = await db.query('SHOW TABLES LIKE ?', ['user_sessions']) as any[]

    if (tables.length > 0) {
      console.log('🔄 Dropping and recreating user_sessions table with correct structure...')

      // Drop existing table
      await db.query('DROP TABLE IF EXISTS user_sessions')
    }

    // Create new table with correct structure
    await db.query(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        session_token TEXT NOT NULL,
        refresh_token TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_session_token (session_token(255)),
        INDEX idx_expires_at (expires_at)
      )
    `)

    console.log('✅ user_sessions table recreated with correct structure')
  } catch (error: any) {
    console.log(`⚠️ Error fixing user_sessions table: ${error.message}`)
  }
}

async function createCveDatabase() {
  try {
    await db.query(`
      CREATE TABLE IF NOT EXISTS cve_database (
        id VARCHAR(36) PRIMARY KEY,
        cve_id VARCHAR(20) NOT NULL UNIQUE,
        description TEXT NOT NULL,
        severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') NOT NULL,
        cvss_score DECIMAL(3,1) NOT NULL,
        cvss_vector VARCHAR(255),
        published_date DATE NOT NULL,
        modified_date DATE,
        affected_products JSON,
        cve_references JSON,
        cwe_id VARCHAR(20),
        exploit_available BOOLEAN DEFAULT FALSE,
        patch_available BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_cve_id (cve_id),
        INDEX idx_severity (severity),
        INDEX idx_cvss_score (cvss_score),
        INDEX idx_published_date (published_date),
        INDEX idx_exploit_available (exploit_available)
      )
    `)
    
    console.log('✅ cve_database table created')
    
    // Insert some sample CVE data
    await insertSampleCveData()
    
  } catch (error: any) {
    if (error.code === 'ER_TABLE_EXISTS_ERROR') {
      console.log('ℹ️ cve_database table already exists')
    } else {
      console.log(`⚠️ Error creating cve_database table: ${error.message}`)
    }
  }
}

async function insertSampleCveData() {
  try {
    const sampleCves = [
      {
        id: 'cve_001',
        cve_id: 'CVE-2024-0001',
        description: 'Critical remote code execution vulnerability in web application framework',
        severity: 'CRITICAL',
        cvss_score: 9.8,
        cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H',
        published_date: '2024-01-15',
        affected_products: JSON.stringify(['Web Framework v1.0-2.5']),
        cve_references: JSON.stringify(['https://nvd.nist.gov/vuln/detail/CVE-2024-0001']),
        cwe_id: 'CWE-78',
        exploit_available: true,
        patch_available: true
      },
      {
        id: 'cve_002',
        cve_id: 'CVE-2024-0002',
        description: 'SQL injection vulnerability in database management system',
        severity: 'HIGH',
        cvss_score: 8.1,
        cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N',
        published_date: '2024-01-10',
        affected_products: JSON.stringify(['Database System v3.0-3.2']),
        cve_references: JSON.stringify(['https://nvd.nist.gov/vuln/detail/CVE-2024-0002']),
        cwe_id: 'CWE-89',
        exploit_available: false,
        patch_available: true
      },
      {
        id: 'cve_003',
        cve_id: 'CVE-2024-0003',
        description: 'Cross-site scripting (XSS) vulnerability in web application',
        severity: 'MEDIUM',
        cvss_score: 6.1,
        cvss_vector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N',
        published_date: '2024-01-05',
        affected_products: JSON.stringify(['Web App v2.0-2.3']),
        cve_references: JSON.stringify(['https://nvd.nist.gov/vuln/detail/CVE-2024-0003']),
        cwe_id: 'CWE-79',
        exploit_available: true,
        patch_available: false
      }
    ]

    for (const cve of sampleCves) {
      await db.query(`
        INSERT IGNORE INTO cve_database (
          id, cve_id, description, severity, cvss_score, cvss_vector,
          published_date, affected_products, cve_references, cwe_id,
          exploit_available, patch_available
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        cve.id, cve.cve_id, cve.description, cve.severity, cve.cvss_score,
        cve.cvss_vector, cve.published_date, cve.affected_products,
        cve.cve_references, cve.cwe_id, cve.exploit_available, cve.patch_available
      ])
    }
    
    console.log('✅ Sample CVE data inserted')
  } catch (error: any) {
    console.log(`⚠️ Error inserting sample CVE data: ${error.message}`)
  }
}

async function updateUsersDefaults() {
  try {
    // Update users with default values for new columns
    await db.query(`
      UPDATE users 
      SET 
        last_active = COALESCE(last_active, NOW()),
        score = COALESCE(score, 0),
        level = COALESCE(level, 1),
        streak_days = COALESCE(streak_days, 0),
        status = COALESCE(status, 'active'),
        timezone = COALESCE(timezone, 'Asia/Jakarta'),
        language = COALESCE(language, 'id')
      WHERE last_active IS NULL OR score IS NULL OR level IS NULL
    `)
    
    console.log('✅ Updated users with default values')
  } catch (error: any) {
    console.log(`⚠️ Error updating users defaults: ${error.message}`)
  }
}

// Run fix if this file is executed directly
if (require.main === module) {
  fixLoginErrors()
}
