{"version": 3, "sources": ["util/vector.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;AAErB,4CAAsC;AACtC,0CAAuC;AACvC,gDAA6C;AAC7C,iDAAqD;AAOrD,cAAc;AACd,IAAI,GAAW,CAAC;AAGhB,cAAc;AACd,SAAgB,UAAU,CAAuE,MAAS,EAAE,KAAyB,EAAE,GAAuB,EAAE,IAAQ;IAEpK,uEAAuE;IACvE,wEAAwE;IACxE,+BAA+B;IAC/B,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC;IACnC,IAAI,GAAG,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAChD,IAAI,GAAG,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IAC9C,8CAA8C;IAC9C,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;IAC/C,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;IAC/C,oBAAoB;IACpB,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;IACjD,uBAAuB;IACvB,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;IAE3B,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACtD,CAAC;AAjBD,gCAiBC;AAED,cAAc;AACP,MAAM,SAAS,GAAG,CAAC,KAAa,EAAE,GAAW,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAA9E,QAAA,SAAS,aAAqE;AAE3F,MAAM,SAAS,GAAG,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,KAAK,KAAK,CAAC;AAElD,cAAc;AACd,SAAgB,uBAAuB,CAAC,MAAW;IAC/C,MAAM,YAAY,GAAG,OAAO,MAAM,CAAC;IACnC,qBAAqB;IACrB,IAAI,YAAY,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;QAC/C,cAAc;QACd,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YACpB,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,OAAO,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,KAAK,MAAM,CAAC;IAC5C,CAAC;IACD,gBAAgB;IAChB,IAAI,MAAM,YAAY,IAAI,EAAE,CAAC;QACzB,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QACvC,OAAO,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAC/F,CAAC;IACD,sBAAsB;IACtB,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC7B,OAAO,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAA,4BAAgB,EAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAC3E,CAAC;IACD,wBAAwB;IACxB,IAAI,MAAM,YAAY,GAAG,EAAE,CAAC;QAAC,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAAC,CAAC;IAClE,sBAAsB;IACtB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QAAC,OAAO,yBAAyB,CAAC,MAAM,CAAC,CAAC;IAAC,CAAC;IACxE,kBAAkB;IAClB,IAAI,MAAM,YAAY,kBAAM,EAAE,CAAC;QAAC,OAAO,sBAAsB,CAAC,MAAM,CAAC,CAAC;IAAC,CAAC;IACxE,OAAO,sBAAsB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC5C,4BAA4B;IAC5B,kEAAkE;AACtE,CAAC;AA5BD,0DA4BC;AAED,cAAc;AACd,SAAS,yBAAyB,CAAC,GAAmB;IAClD,MAAM,WAAW,GAAG,EAA6B,CAAC;IAClD,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;QACxC,WAAW,CAAC,CAAC,CAAC,GAAG,uBAAuB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;IACD,OAAO,2BAA2B,CAAC,WAAW,CAAC,CAAC;AACpD,CAAC;AAED,cAAc;AACd,SAAS,mBAAmB,CAAC,GAAkB;IAC3C,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACX,MAAM,WAAW,GAAG,EAA6B,CAAC;IAClD,KAAK,MAAM,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE;QAAE,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;IAC5E,OAAO,2BAA2B,CAAC,WAAW,CAAC,CAAC;AACpD,CAAC;AAED,cAAc;AACd,SAAS,sBAAsB,CAAC,GAAgB;IAC5C,MAAM,WAAW,GAAG,EAA6B,CAAC;IAClD,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;QACxC,WAAW,CAAC,CAAC,CAAC,GAAG,uBAAuB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC;IACD,OAAO,2BAA2B,CAAC,WAAW,CAAC,CAAC;AACpD,CAAC;AAED,cAAc;AACd,SAAS,sBAAsB,CAAC,GAAQ,EAAE,UAAU,GAAG,KAAK;IACxD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9B,iCAAiC;IACjC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC;IAAC,CAAC;IAC7D,MAAM,WAAW,GAAG,EAA6B,CAAC;IAClD,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;QACzC,WAAW,CAAC,CAAC,CAAC,GAAG,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,CAAC;IACD,OAAO,2BAA2B,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AAC1D,CAAC;AAED,SAAS,2BAA2B,CAAC,WAAoC,EAAE,IAAuB;IAC9F,OAAO,CAAC,GAAQ,EAAE,EAAE;QAChB,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,QAAQ,GAAG,CAAC,WAAW,EAAE,CAAC;YACtB,KAAK,KAAK,CAAC,CAAC,OAAO,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;YAClD,KAAK,GAAG;gBACJ,OAAO,aAAa,CAAC,WAAW,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YACvD,KAAK,eAAM,CAAC;YACZ,KAAK,qBAAS,CAAC;YACf,KAAK,MAAM,CAAC;YACZ,KAAK,SAAS,EAAE,wCAAwC;gBACpD,OAAO,aAAa,CAAC,WAAW,EAAE,GAAG,EAAE,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACzE,CAAC;QACD,OAAO,GAAG,YAAY,kBAAM,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAC3E,CAAC,CAAC;AACN,CAAC;AAED,SAAS,YAAY,CAAC,WAAoC,EAAE,GAAU;IAClE,MAAM,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;IAC7B,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAAC,OAAO,KAAK,CAAC;IAAC,CAAC;IACvC,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;QACxB,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAAC,OAAO,KAAK,CAAC;QAAC,CAAC;IACpD,CAAC;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,SAAS,aAAa,CAAC,WAAoC,EAAE,GAAW;IACpE,MAAM,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;IAC7B,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAAC,OAAO,KAAK,CAAC;IAAC,CAAC;IACvC,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;QACxB,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAAC,OAAO,KAAK,CAAC;QAAC,CAAC;IACxD,CAAC;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,SAAS,aAAa,CAAC,WAAoC,EAAE,GAAkB,EAAE,IAAsB;IAEnG,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;IACxC,MAAM,OAAO,GAAG,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;IACtF,MAAM,OAAO,GAAG,GAAG,YAAY,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;IAE1F,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,MAAM,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;IAC7B,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAC1B,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAC1B,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAE1B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAClD,EAAE,CAAC,EAAE,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;QAC3E,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3D,MAAM;QACV,CAAC;IACL,CAAC;IACD,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACjD,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;IACnC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;IACnC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;IACnC,OAAO,KAAK,CAAC;AACjB,CAAC", "file": "vector.js", "sourceRoot": "../src"}