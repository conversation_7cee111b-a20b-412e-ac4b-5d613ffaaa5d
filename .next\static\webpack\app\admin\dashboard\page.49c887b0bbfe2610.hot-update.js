"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/dashboard/page",{

/***/ "(app-pages-browser)/./app/admin/dashboard/page.tsx":
/*!**************************************!*\
  !*** ./app/admin/dashboard/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AdminLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AdminLayout */ \"(app-pages-browser)/./components/AdminLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bug,CheckCircle,Clock,Crown,Download,Edit,Pause,Play,Plus,RefreshCw,Server,Settings,Shield,Square,Trash2,TrendingUp,Upload,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AdminPage() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalUsers: 0,\n        activeUsers: 0,\n        totalScans: 0,\n        totalVulnerabilities: 0,\n        systemUptime: 0,\n        apiCalls: 0,\n        storageUsed: 0,\n        bandwidthUsed: 0\n    });\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        database: \"online\",\n        api: \"online\",\n        scanner: \"online\",\n        osint: \"online\",\n        fileAnalyzer: \"online\"\n    });\n    const [recentUsers, setRecentUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentActivity, setRecentActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [alerts, setAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAdminData();\n    }, []);\n    const loadAdminData = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD27 Loading admin dashboard data...\");\n            setLoading(true);\n            // Fetch real admin data from API\n            const response = await fetch(\"/api/admin/dashboard\");\n            const data = await response.json();\n            if (data.success) {\n                console.log(\"✅ Admin dashboard data loaded:\", data.data);\n                setStats(data.data.stats);\n                setSystemStatus(data.data.systemStatus);\n                setRecentUsers(data.data.recentUsers);\n                setRecentActivity(data.data.recentActivity);\n                setAlerts(data.data.alerts);\n            } else {\n                console.error(\"❌ Failed to load admin data:\", data.error);\n                // Fallback to default data\n                setStats({\n                    totalUsers: 1247,\n                    activeUsers: 892,\n                    totalScans: 15634,\n                    totalVulnerabilities: 4521,\n                    systemUptime: 99.8,\n                    apiCalls: 234567,\n                    storageUsed: 78.5,\n                    bandwidthUsed: 45.2\n                });\n                setRecentUsers([\n                    {\n                        id: \"1\",\n                        username: \"CyberNinja\",\n                        email: \"<EMAIL>\",\n                        plan: \"Elite\",\n                        joinedAt: \"2024-01-15\",\n                        lastActive: \"2 minutes ago\",\n                        status: \"active\"\n                    }\n                ]);\n            }\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error loading admin data:\", error);\n            setLoading(false);\n        }\n    };\n    const handleResolveAlert = async (alertId)=>{\n        try {\n            const response = await fetch(\"/api/admin/dashboard\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"resolve_alert\",\n                    data: {\n                        alertId\n                    }\n                })\n            });\n            if (response.ok) {\n                loadAdminData() // Refresh data\n                ;\n            }\n        } catch (error) {\n            console.error(\"Error resolving alert:\", error);\n        }\n    };\n    const handleBanUser = async (userId)=>{\n        try {\n            const response = await fetch(\"/api/admin/dashboard\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    action: \"ban_user\",\n                    data: {\n                        userId\n                    }\n                })\n            });\n            if (response.ok) {\n                loadAdminData() // Refresh data\n                ;\n            }\n        } catch (error) {\n            console.error(\"Error banning user:\", error);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"online\":\n                return \"text-green-400 bg-green-400/20\";\n            case \"offline\":\n                return \"text-red-400 bg-red-400/20\";\n            case \"maintenance\":\n                return \"text-yellow-400 bg-yellow-400/20\";\n            default:\n                return \"text-gray-400 bg-gray-400/20\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"online\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 29\n                }, this);\n            case \"offline\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 30\n                }, this);\n            case \"maintenance\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 34\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getUserStatusColor = (status)=>{\n        switch(status){\n            case \"active\":\n                return \"text-green-400\";\n            case \"inactive\":\n                return \"text-yellow-400\";\n            case \"banned\":\n                return \"text-red-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-cyber-primary font-medium\",\n                            children: \"Loading admin console...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                lineNumber: 232,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n            lineNumber: 231,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-400\",\n                                            children: \"⚡ Admin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-pink\",\n                                            children: \"Control Center\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg\",\n                                    children: \"System administration, monitoring, and user management\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 mt-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-blue-400 font-medium\",\n                                                    children: [\n                                                        stats.totalUsers,\n                                                        \" Total Users\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-green-400 font-medium\",\n                                                    children: [\n                                                        stats.activeUsers,\n                                                        \" Active\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-yellow-400 font-medium\",\n                                                    children: [\n                                                        stats.totalScans,\n                                                        \" Scans\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5 text-cyber-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-cyber-primary font-medium\",\n                                                    children: [\n                                                        stats.systemUptime,\n                                                        \"% Uptime\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 lg:mt-0 flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 bg-green-400 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"System Online\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: loadAdminData,\n                                    disabled: loading,\n                                    className: \"btn-cyber-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        loading ? \"Refreshing...\" : \"Refresh Data\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1 bg-cyber-dark/50 p-1 rounded-lg\",\n                    children: [\n                        {\n                            id: \"overview\",\n                            label: \"Overview\",\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                        },\n                        {\n                            id: \"users\",\n                            label: \"Users\",\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                        },\n                        {\n                            id: \"system\",\n                            label: \"System\",\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                        },\n                        {\n                            id: \"security\",\n                            label: \"Security\",\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                        },\n                        {\n                            id: \"settings\",\n                            label: \"Settings\",\n                            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                        }\n                    ].map((tab)=>{\n                        const Icon = tab.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: \"flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 \".concat(activeTab === tab.id ? \"bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30\" : \"text-gray-400 hover:text-white hover:bg-cyber-primary/10\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 9\n                }, this),\n                activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-8 w-8 text-cyber-primary mx-auto mb-3 animate-cyber-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-white mb-1\",\n                                            children: stats.totalUsers.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-green-400 mt-1\",\n                                            children: [\n                                                \"+\",\n                                                stats.activeUsers,\n                                                \" active\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-8 w-8 text-cyber-secondary mx-auto mb-3 animate-cyber-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-white mb-1\",\n                                            children: stats.totalScans.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Total Scans\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-blue-400 mt-1\",\n                                            children: \"+1.2K today\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-8 w-8 text-red-400 mx-auto mb-3 animate-cyber-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-white mb-1\",\n                                            children: stats.totalVulnerabilities.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Vulnerabilities\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-red-400 mt-1\",\n                                            children: \"45 critical\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-400 mx-auto mb-3 animate-cyber-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-white mb-1\",\n                                            children: [\n                                                stats.systemUptime,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"System Uptime\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-green-400 mt-1\",\n                                            children: \"99.8% this month\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-glow\",\n                                            children: \"System\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-pink\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                                    children: Object.entries(systemStatus).map((param)=>{\n                                        let [service, status] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 rounded-lg bg-cyber-secondary/5 border border-cyber-border\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-white capitalize\",\n                                                            children: service.replace(/([A-Z])/g, \" $1\").trim()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-bold \".concat(getStatusColor(status)),\n                                                            children: [\n                                                                getStatusIcon(status),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"capitalize\",\n                                                                    children: status\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: status === \"online\" ? \"Running normally\" : status === \"offline\" ? \"Service unavailable\" : \"Under maintenance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, service, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-cyber-glow\",\n                                                    children: \"API\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-cyber-pink\",\n                                                    children: \"Performance\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Total API Calls\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-bold\",\n                                                            children: stats.apiCalls.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Avg Response Time\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-bold\",\n                                                            children: \"145ms\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Error Rate\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-400 font-bold\",\n                                                            children: \"0.02%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Rate Limit Hits\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-yellow-400 font-bold\",\n                                                            children: \"12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-cyber-glow\",\n                                                    children: \"Resource\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-cyber-pink\",\n                                                    children: \"Usage\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Storage Used\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold\",\n                                                                    children: [\n                                                                        stats.storageUsed,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-cyber-dark rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-cyber-primary h-2 rounded-full transition-all duration-300\",\n                                                                style: {\n                                                                    width: \"\".concat(stats.storageUsed, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Bandwidth Used\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold\",\n                                                                    children: [\n                                                                        stats.bandwidthUsed,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-cyber-dark rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-cyber-secondary h-2 rounded-full transition-all duration-300\",\n                                                                style: {\n                                                                    width: \"\".concat(stats.bandwidthUsed, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"CPU Usage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400 font-bold\",\n                                                            children: \"23%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Memory Usage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-400 font-bold\",\n                                                            children: \"67%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"users\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-glow\",\n                                            children: \"User\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-pink\",\n                                            children: \"Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-cyber-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Add User\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-6 w-6 text-cyber-primary mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: stats.totalUsers.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6 text-green-400 mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: stats.activeUsers.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Active Users\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-cyber-accent mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: \"1,247\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Premium Users\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-6 w-6 text-cyber-secondary mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: \"+12%\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Growth Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 482,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-white mb-4\",\n                                    children: \"Recent Users\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: recentUsers.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5 hover:bg-cyber-primary/10 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 rounded-full bg-cyber-primary/20 flex items-center justify-center text-sm font-bold text-cyber-primary\",\n                                                            children: user.username.charAt(0)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-white\",\n                                                                    children: user.username\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 519,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-sm\",\n                                                                    children: user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-cyber-accent\",\n                                                                    children: user.plan\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: \"Plan\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium \".concat(getUserStatusColor(user.status)),\n                                                                    children: user.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: user.lastActive\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 533,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"p-2 rounded-lg bg-cyber-primary/20 text-cyber-primary hover:bg-cyber-primary/30 transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 537,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"p-2 rounded-lg bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, user.id, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"system\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"System\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Management\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: \"Service Control\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: Object.entries(systemStatus).map((param)=>{\n                                                let [service, status] = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 rounded-full \".concat(status === \"online\" ? \"bg-green-400\" : status === \"offline\" ? \"bg-red-400\" : \"bg-yellow-400\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-medium capitalize\",\n                                                                    children: service.replace(/([A-Z])/g, \" $1\").trim()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"p-1 rounded bg-green-500/20 text-green-400 hover:bg-green-500/30\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 576,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 575,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"p-1 rounded bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 579,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"p-1 rounded bg-red-500/20 text-red-400 hover:bg-red-500/30\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 582,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, service, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: \"Database Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full flex items-center justify-center space-x-2 p-3 rounded-lg bg-cyber-primary/20 text-cyber-primary hover:bg-cyber-primary/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Backup Database\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full flex items-center justify-center space-x-2 p-3 rounded-lg bg-cyber-secondary/20 text-cyber-secondary hover:bg-cyber-secondary/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 598,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Restore Database\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"w-full flex items-center justify-center space-x-2 p-3 rounded-lg bg-cyber-accent/20 text-cyber-accent hover:bg-cyber-accent/30 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Optimize Database\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 559,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 553,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"security\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Security\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Center\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: \"Security Alerts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg bg-red-500/10 border border-red-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-400 font-medium\",\n                                                                    children: \"Critical Alert\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 text-sm\",\n                                                            children: \"Multiple failed login attempts detected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg bg-yellow-500/10 border border-yellow-500/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-yellow-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 632,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-yellow-400 font-medium\",\n                                                                    children: \"Warning\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 633,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 text-sm\",\n                                                            children: \"High API usage detected from IP *************\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 635,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: \"Access Control\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: \"Two-Factor Authentication\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400 text-sm\",\n                                                                    children: \"Enabled\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: \"IP Whitelist\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400 text-sm\",\n                                                                    children: \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: \"Rate Limiting\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 658,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bug_CheckCircle_Clock_Crown_Download_Edit_Pause_Play_Plus_RefreshCw_Server_Settings_Shield_Square_Trash2_TrendingUp_Upload_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 660,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400 text-sm\",\n                                                                    children: \"Configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 661,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 613,\n                    columnNumber: 11\n                }, this),\n                activeTab === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"System\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 674,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Settings\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 675,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 673,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-white mb-4\",\n                                    children: \"Configuration\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: \"Maintenance Mode\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"Enable system maintenance mode\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"relative inline-flex h-6 w-11 items-center rounded-full bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-cyber-primary focus:ring-offset-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 687,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: \"Debug Mode\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"Enable detailed logging\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 694,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"relative inline-flex h-6 w-11 items-center rounded-full bg-cyber-primary transition-colors focus:outline-none focus:ring-2 focus:ring-cyber-primary focus:ring-offset-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 678,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 672,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n            lineNumber: 244,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\dashboard\\\\page.tsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"p9GD+xtPCBWhySJPoDrdz/5JHhY=\");\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/dashboard/page.tsx\n"));

/***/ })

});