{"c": ["app/layout", "app/settings/page", "webpack"], "r": ["app/_not-found/page"], "m": ["(app-pages-browser)/./app/settings/page.tsx", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage!", "(app-pages-browser)/./node_modules/next/dist/client/components/not-found-error.js"]}