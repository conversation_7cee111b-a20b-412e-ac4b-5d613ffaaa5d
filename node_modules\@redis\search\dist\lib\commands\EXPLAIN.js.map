{"version": 3, "file": "EXPLAIN.js", "sourceRoot": "", "sources": ["../../../lib/commands/EXPLAIN.ts"], "names": [], "mappings": ";;AAEA,qCAA+D;AAC/D,gDAAqD;AAOrD,kBAAe;IACb,iBAAiB,EAAE,IAAI;IACvB,YAAY,EAAE,IAAI;IAClB;;;;;;;;OAQG;IACH,YAAY,CACV,MAAqB,EACrB,KAAoB,EACpB,KAAoB,EACpB,OAA0B;QAE1B,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAExC,IAAA,4BAAmB,EAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAE7C,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,yBAAe,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IACD,cAAc,EAAE,SAA+C;CACrC,CAAC"}