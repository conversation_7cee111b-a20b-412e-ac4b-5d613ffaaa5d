"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/eye.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Eye; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst Eye = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Eye\", [\n    [\n        \"path\",\n        {\n            d: \"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z\",\n            key: \"rwhkz3\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"3\",\n            key: \"1v7zrd\"\n        }\n    ]\n]);\n //# sourceMappingURL=eye.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DashboardPage() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalScans: 0,\n        vulnerabilitiesFound: 0,\n        filesAnalyzed: 0,\n        osintQueries: 0,\n        apiCalls: 0,\n        score: 0,\n        level: 0,\n        rank: 0,\n        streak: 0,\n        pointsToday: 0,\n        pointsThisWeek: 0,\n        pointsThisMonth: 0\n    });\n    const [recentActivity, setRecentActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [achievements, setAchievements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [planUsage, setPlanUsage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [trendingVulns, setTrendingVulns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update time every minute\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch real dashboard data from API\n            const response = await fetch(\"/api/dashboard/stats\");\n            const data = await response.json();\n            if (data.success) {\n                setStats({\n                    totalScans: data.data.stats.totalScans,\n                    vulnerabilitiesFound: data.data.stats.vulnerabilitiesFound,\n                    filesAnalyzed: data.data.stats.fileAnalyses,\n                    osintQueries: data.data.stats.osintQueries,\n                    apiCalls: data.data.stats.cveSearches + data.data.stats.dorkingQueries,\n                    score: data.data.user.score,\n                    level: data.data.user.level,\n                    rank: data.data.user.rank,\n                    streak: data.data.user.streak,\n                    pointsToday: Math.floor(data.data.user.score * 0.1),\n                    pointsThisWeek: Math.floor(data.data.user.score * 0.3),\n                    pointsThisMonth: Math.floor(data.data.user.score * 0.6)\n                });\n                // Transform recent activities to match interface\n                const transformedActivities = data.data.recentActivities.map((activity)=>({\n                        id: activity.id.toString(),\n                        type: activity.type === \"vulnerability_scan\" ? \"scan\" : activity.type === \"osint_query\" ? \"osint\" : activity.type === \"file_analysis\" ? \"file\" : activity.type === \"cve_search\" ? \"cve\" : \"dorking\",\n                        target: activity.description.includes(\"target.\") ? \"target.example.com\" : activity.description.includes(\"domain\") ? \"example.com\" : activity.description.includes(\"file:\") ? \"malware.exe\" : activity.description.includes(\"Apache\") ? \"Apache HTTP Server\" : activity.description.includes(\"databases\") ? \"Google Search\" : \"Unknown\",\n                        time: new Date(activity.timestamp).toLocaleTimeString() + \" ago\",\n                        status: \"completed\",\n                        severity: activity.severity,\n                        result: activity.result\n                    }));\n                setRecentActivity(transformedActivities);\n                setUser(data.data.user);\n                setAchievements(data.data.achievements);\n                setPlanUsage(data.data.usage);\n                setTrendingVulns(data.data.trendingVulnerabilities);\n            } else {\n                // Fallback to mock data if API fails\n                setStats({\n                    totalScans: 142,\n                    vulnerabilitiesFound: 23,\n                    filesAnalyzed: 89,\n                    osintQueries: 456,\n                    apiCalls: 2847,\n                    score: 8950,\n                    level: 28,\n                    rank: 156,\n                    streak: 12,\n                    pointsToday: 250,\n                    pointsThisWeek: 1450,\n                    pointsThisMonth: 5890\n                });\n                setRecentActivity([\n                    {\n                        id: \"1\",\n                        type: \"scan\",\n                        target: \"example.com\",\n                        time: \"2 minutes ago\",\n                        status: \"completed\",\n                        severity: \"high\",\n                        result: \"3 vulnerabilities found\"\n                    }\n                ]);\n            }\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            setLoading(false);\n        }\n    };\n    const quickActions = [\n        {\n            title: \"OSINT Lookup\",\n            description: \"Investigate emails, phone numbers, and personal information\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"/osint\",\n            badge: \"Popular\",\n            color: \"cyber-primary\",\n            stats: \"456 queries\"\n        },\n        {\n            title: \"Vulnerability Scan\",\n            description: \"Scan websites and applications for security vulnerabilities\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"/scanner\",\n            premium: true,\n            color: \"cyber-secondary\",\n            stats: \"142 scans\"\n        },\n        {\n            title: \"File Analysis\",\n            description: \"Analyze files for malware, webshells, and suspicious content\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"/file-analyzer\",\n            premium: true,\n            color: \"cyber-accent\",\n            stats: \"89 files\"\n        },\n        {\n            title: \"CVE Database\",\n            description: \"Search and explore the latest CVE vulnerabilities\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: \"/cve\",\n            color: \"green-400\",\n            stats: \"50K+ CVEs\"\n        },\n        {\n            title: \"Google Dorking\",\n            description: \"Use advanced search queries to find sensitive information\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: \"/dorking\",\n            color: \"blue-400\",\n            stats: \"Advanced\"\n        },\n        {\n            title: \"API Playground\",\n            description: \"Test and explore our API endpoints\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            href: \"/playground\",\n            color: \"purple-400\",\n            stats: \"2.8K calls\"\n        }\n    ];\n    const getActivityIcon = (type)=>{\n        switch(type){\n            case \"scan\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"osint\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"file\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"cve\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"dorking\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            default:\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"text-green-400\";\n            case \"failed\":\n                return \"text-red-400\";\n            case \"malicious\":\n                return \"text-red-400\";\n            case \"clean\":\n                return \"text-green-400\";\n            case \"pending\":\n                return \"text-yellow-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"critical\":\n                return \"text-red-400 bg-red-400/20\";\n            case \"high\":\n                return \"text-orange-400 bg-orange-400/20\";\n            case \"medium\":\n                return \"text-yellow-400 bg-yellow-400/20\";\n            case \"low\":\n                return \"text-green-400 bg-green-400/20\";\n            default:\n                return \"text-gray-400 bg-gray-400/20\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-cyber-primary font-medium\",\n                            children: \"Loading cyber dashboard...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 287,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-glow\",\n                                            children: \"Cyber\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-pink\",\n                                            children: \"Command\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg\",\n                                    children: [\n                                        \"Welcome back, \",\n                                        (user === null || user === void 0 ? void 0 : user.username) || \"Cyber Warrior\",\n                                        \"! Your digital arsenal awaits.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 lg:mt-0 flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Current Time\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-mono text-cyber-primary\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-cyber-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Quick Scan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-2xl font-bold text-black\",\n                                        children: (user === null || user === void 0 ? void 0 : user.username) ? user.username.substring(0, 2).toUpperCase() : \"CW\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: (user === null || user === void 0 ? void 0 : user.fullName) || (user === null || user === void 0 ? void 0 : user.username) || \"Cyber Warrior\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Level \",\n                                                            stats.level\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded text-xs font-medium \".concat((user === null || user === void 0 ? void 0 : user.plan) === \"Elite\" ? \"bg-purple-500/20 text-purple-400\" : (user === null || user === void 0 ? void 0 : user.plan) === \"Pro\" ? \"bg-blue-500/20 text-blue-400\" : (user === null || user === void 0 ? void 0 : user.plan) === \"Cybersecurity\" ? \"bg-red-500/20 text-red-400\" : (user === null || user === void 0 ? void 0 : user.plan) === \"Bughunter\" ? \"bg-orange-500/20 text-orange-400\" : \"bg-gray-500/20 text-gray-400\"),\n                                                        children: (user === null || user === void 0 ? void 0 : user.plan) || \"Free\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Rank #\",\n                                                            stats.rank\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4 text-cyber-secondary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    stats.streak,\n                                                                    \" day streak\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-cyber-primary\",\n                                        children: stats.score.toLocaleString()\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"Total Score\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-green-400\",\n                                                children: [\n                                                    \"+\",\n                                                    stats.pointsToday,\n                                                    \" today\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-3 w-3 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-8 w-8 text-cyber-primary mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.totalScans\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Total Scans\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-green-400 mt-1\",\n                                    children: \"+12 this week\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-8 w-8 text-red-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.vulnerabilitiesFound\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Vulnerabilities\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-red-400 mt-1\",\n                                    children: \"+3 critical\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-8 w-8 text-cyber-accent mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.filesAnalyzed\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Files Analyzed\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-yellow-400 mt-1\",\n                                    children: \"5 malicious\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-8 w-8 text-blue-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.osintQueries\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"OSINT Queries\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-blue-400 mt-1\",\n                                    children: \"+45 today\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.apiCalls.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"API Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-purple-400 mt-1\",\n                                    children: \"98% uptime\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.pointsThisWeek.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Weekly Points\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-green-400 mt-1\",\n                                    children: \"+15% vs last week\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber border-l-4 border-l-cyber-secondary\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-6 w-6 text-cyber-secondary mt-1\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold text-white mb-1\",\n                                        children: \"System Status Update\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-3\",\n                                        children: \"New CVE database update available. 15 critical vulnerabilities added to our intelligence feed.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-cyber-secondary text-sm\",\n                                                children: \"View Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"2 minutes ago\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Quick\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: quickActions.map((action, index)=>{\n                                const Icon = action.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber hover:scale-105 transition-all duration-300 cursor-pointer group\",\n                                    onClick: ()=>window.location.href = action.href,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg bg-\".concat(action.color, \"/20\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-6 w-6 text-\".concat(action.color, \" group-hover:animate-cyber-pulse\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-end space-y-1\",\n                                                    children: [\n                                                        action.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-cyber-secondary/20 text-cyber-secondary px-2 py-1 rounded-full text-xs font-bold\",\n                                                            children: action.badge\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        action.premium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-cyber-accent/20 text-cyber-accent px-2 py-1 rounded-full text-xs font-bold\",\n                                                            children: \"PRO\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-2 group-hover:text-cyber-primary transition-colors\",\n                                            children: action.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm mb-4\",\n                                            children: action.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium text-\".concat(action.color),\n                                                    children: action.stats\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400 group-hover:text-cyber-primary transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 455,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 449,\n                    columnNumber: 9\n                }, this),\n                planUsage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Plan\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Usage\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: \"Vulnerability Scans\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5 text-cyber-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Used\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: [\n                                                                planUsage.usage.vulnerabilityScans,\n                                                                \" / \",\n                                                                planUsage.limits.scans === -1 ? \"∞\" : planUsage.limits.scans\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 19\n                                                }, this),\n                                                planUsage.limits.scans !== -1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-cyber-primary h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(Math.min(planUsage.percentage.vulnerabilityScans, 100), \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: \"OSINT Queries\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-cyber-secondary\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Used\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: [\n                                                                planUsage.usage.osintQueries,\n                                                                \" / \",\n                                                                planUsage.limits.osint === -1 ? \"∞\" : planUsage.limits.osint\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 19\n                                                }, this),\n                                                planUsage.limits.osint !== -1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-cyber-secondary h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(Math.min(planUsage.percentage.osintQueries, 100), \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: \"File Analyses\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5 text-cyber-accent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Used\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: [\n                                                                planUsage.usage.fileAnalyses,\n                                                                \" / \",\n                                                                planUsage.limits.files === -1 ? \"∞\" : planUsage.limits.files\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 19\n                                                }, this),\n                                                planUsage.limits.files !== -1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-cyber-accent h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(Math.min(planUsage.percentage.fileAnalyses, 100), \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 503,\n                    columnNumber: 11\n                }, this),\n                achievements && achievements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Recent\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Achievements\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: achievements.slice(0, 4).map((achievement)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center transition-all duration-300 \".concat(achievement.unlocked ? \"border-cyber-primary/50 bg-cyber-primary/5\" : \"border-gray-600/50 bg-gray-800/50 opacity-60\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center \".concat(achievement.unlocked ? \"bg-cyber-primary/20 text-cyber-primary\" : \"bg-gray-700 text-gray-500\"),\n                                            children: [\n                                                achievement.icon === \"shield\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 55\n                                                }, this),\n                                                achievement.icon === \"eye\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 52\n                                                }, this),\n                                                achievement.icon === \"bug\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 52\n                                                }, this),\n                                                achievement.icon === \"flame\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 54\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2 \".concat(achievement.unlocked ? \"text-white\" : \"text-gray-400\"),\n                                            children: achievement.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mb-3\",\n                                            children: achievement.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(achievement.rarity === \"legendary\" ? \"bg-purple-500/20 text-purple-400\" : achievement.rarity === \"epic\" ? \"bg-orange-500/20 text-orange-400\" : achievement.rarity === \"rare\" ? \"bg-blue-500/20 text-blue-400\" : \"bg-gray-500/20 text-gray-400\"),\n                                            children: achievement.rarity\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 19\n                                        }, this),\n                                        achievement.unlocked && achievement.unlockedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-2\",\n                                            children: [\n                                                \"Unlocked \",\n                                                new Date(achievement.unlockedAt).toLocaleDateString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, achievement.id, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 578,\n                    columnNumber: 11\n                }, this),\n                trendingVulns && trendingVulns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Trending\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Vulnerabilities\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: trendingVulns.map((vuln)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-white mb-1\",\n                                                            children: vuln.id\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: vuln.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 647,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-2 py-1 rounded-full text-xs font-bold uppercase \".concat(vuln.severity === \"critical\" ? \"bg-red-500/20 text-red-400\" : vuln.severity === \"high\" ? \"bg-orange-500/20 text-orange-400\" : vuln.severity === \"medium\" ? \"bg-yellow-500/20 text-yellow-400\" : \"bg-green-500/20 text-green-400\"),\n                                                    children: vuln.severity\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-300 mb-4\",\n                                            children: vuln.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400\",\n                                                    children: [\n                                                        \"CVSS: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: vuln.score\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400\",\n                                                    children: [\n                                                        \"Affected: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-cyber-primary font-medium\",\n                                                            children: vuln.affectedSystems.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, vuln.id, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 635,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Recent\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Activity\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 677,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: recentActivity.map((activity)=>{\n                                        const Icon = getActivityIcon(activity.type);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5 hover:bg-cyber-primary/10 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-cyber-primary/20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"h-5 w-5 text-cyber-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium text-white\",\n                                                                            children: activity.target\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-bold uppercase \".concat(getStatusColor(activity.status)),\n                                                                            children: activity.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 699,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        activity.severity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-bold uppercase \".concat(getSeverityColor(activity.severity)),\n                                                                            children: activity.severity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 703,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-sm\",\n                                                                    children: activity.result\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: activity.time\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 capitalize\",\n                                                            children: activity.type\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, activity.id, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 683,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-cyber-primary hover:text-cyber-secondary transition-colors\",\n                                        children: \"View All Activity →\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 682,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 676,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 300,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 299,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"BFRTwvn+5IkN9CfaeTLp5dlKHsI=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9kYXNoYm9hcmQvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ2U7QUFvQ3JDO0FBc0NOLFNBQVNpQjs7SUFDdEIsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUduQiwrQ0FBUUEsQ0FBaUI7UUFDakRvQixZQUFZO1FBQ1pDLHNCQUFzQjtRQUN0QkMsZUFBZTtRQUNmQyxjQUFjO1FBQ2RDLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxhQUFhO1FBQ2JDLGdCQUFnQjtRQUNoQkMsaUJBQWlCO0lBQ25CO0lBRUEsTUFBTSxDQUFDQyxnQkFBZ0JDLGtCQUFrQixHQUFHakMsK0NBQVFBLENBQW1CLEVBQUU7SUFDekUsTUFBTSxDQUFDa0MsU0FBU0MsV0FBVyxHQUFHbkMsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDb0MsYUFBYUMsZUFBZSxHQUFHckMsK0NBQVFBLENBQUMsSUFBSXNDO0lBQ25ELE1BQU0sQ0FBQ0MsTUFBTUMsUUFBUSxHQUFHeEMsK0NBQVFBLENBQU07SUFDdEMsTUFBTSxDQUFDeUMsY0FBY0MsZ0JBQWdCLEdBQUcxQywrQ0FBUUEsQ0FBUSxFQUFFO0lBQzFELE1BQU0sQ0FBQzJDLFdBQVdDLGFBQWEsR0FBRzVDLCtDQUFRQSxDQUFNO0lBQ2hELE1BQU0sQ0FBQzZDLGVBQWVDLGlCQUFpQixHQUFHOUMsK0NBQVFBLENBQVEsRUFBRTtJQUU1REMsZ0RBQVNBLENBQUM7UUFDUiwyQkFBMkI7UUFDM0IsTUFBTThDLFFBQVFDLFlBQVk7WUFDeEJYLGVBQWUsSUFBSUM7UUFDckIsR0FBRztRQUVILE9BQU8sSUFBTVcsY0FBY0Y7SUFDN0IsR0FBRyxFQUFFO0lBRUw5QyxnREFBU0EsQ0FBQztRQUNSaUQ7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNQSxvQkFBb0I7UUFDeEIsSUFBSTtZQUNGZixXQUFXO1lBRVgscUNBQXFDO1lBQ3JDLE1BQU1nQixXQUFXLE1BQU1DLE1BQU07WUFDN0IsTUFBTUMsT0FBTyxNQUFNRixTQUFTRyxJQUFJO1lBRWhDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtnQkFDaEJwQyxTQUFTO29CQUNQQyxZQUFZaUMsS0FBS0EsSUFBSSxDQUFDbkMsS0FBSyxDQUFDRSxVQUFVO29CQUN0Q0Msc0JBQXNCZ0MsS0FBS0EsSUFBSSxDQUFDbkMsS0FBSyxDQUFDRyxvQkFBb0I7b0JBQzFEQyxlQUFlK0IsS0FBS0EsSUFBSSxDQUFDbkMsS0FBSyxDQUFDc0MsWUFBWTtvQkFDM0NqQyxjQUFjOEIsS0FBS0EsSUFBSSxDQUFDbkMsS0FBSyxDQUFDSyxZQUFZO29CQUMxQ0MsVUFBVTZCLEtBQUtBLElBQUksQ0FBQ25DLEtBQUssQ0FBQ3VDLFdBQVcsR0FBR0osS0FBS0EsSUFBSSxDQUFDbkMsS0FBSyxDQUFDd0MsY0FBYztvQkFDdEVqQyxPQUFPNEIsS0FBS0EsSUFBSSxDQUFDZCxJQUFJLENBQUNkLEtBQUs7b0JBQzNCQyxPQUFPMkIsS0FBS0EsSUFBSSxDQUFDZCxJQUFJLENBQUNiLEtBQUs7b0JBQzNCQyxNQUFNMEIsS0FBS0EsSUFBSSxDQUFDZCxJQUFJLENBQUNaLElBQUk7b0JBQ3pCQyxRQUFReUIsS0FBS0EsSUFBSSxDQUFDZCxJQUFJLENBQUNYLE1BQU07b0JBQzdCQyxhQUFhOEIsS0FBS0MsS0FBSyxDQUFDUCxLQUFLQSxJQUFJLENBQUNkLElBQUksQ0FBQ2QsS0FBSyxHQUFHO29CQUMvQ0ssZ0JBQWdCNkIsS0FBS0MsS0FBSyxDQUFDUCxLQUFLQSxJQUFJLENBQUNkLElBQUksQ0FBQ2QsS0FBSyxHQUFHO29CQUNsRE0saUJBQWlCNEIsS0FBS0MsS0FBSyxDQUFDUCxLQUFLQSxJQUFJLENBQUNkLElBQUksQ0FBQ2QsS0FBSyxHQUFHO2dCQUNyRDtnQkFFQSxpREFBaUQ7Z0JBQ2pELE1BQU1vQyx3QkFBd0JSLEtBQUtBLElBQUksQ0FBQ1MsZ0JBQWdCLENBQUNDLEdBQUcsQ0FBQyxDQUFDQyxXQUFtQjt3QkFDL0VDLElBQUlELFNBQVNDLEVBQUUsQ0FBQ0MsUUFBUTt3QkFDeEJDLE1BQU1ILFNBQVNHLElBQUksS0FBSyx1QkFBdUIsU0FDekNILFNBQVNHLElBQUksS0FBSyxnQkFBZ0IsVUFDbENILFNBQVNHLElBQUksS0FBSyxrQkFBa0IsU0FDcENILFNBQVNHLElBQUksS0FBSyxlQUFlLFFBQVE7d0JBQy9DQyxRQUFRSixTQUFTSyxXQUFXLENBQUNDLFFBQVEsQ0FBQyxhQUFhLHVCQUMzQ04sU0FBU0ssV0FBVyxDQUFDQyxRQUFRLENBQUMsWUFBWSxnQkFDMUNOLFNBQVNLLFdBQVcsQ0FBQ0MsUUFBUSxDQUFDLFdBQVcsZ0JBQ3pDTixTQUFTSyxXQUFXLENBQUNDLFFBQVEsQ0FBQyxZQUFZLHVCQUMxQ04sU0FBU0ssV0FBVyxDQUFDQyxRQUFRLENBQUMsZUFBZSxrQkFBa0I7d0JBQ3ZFQyxNQUFNLElBQUlqQyxLQUFLMEIsU0FBU1EsU0FBUyxFQUFFQyxrQkFBa0IsS0FBSzt3QkFDMURDLFFBQVE7d0JBQ1JDLFVBQVVYLFNBQVNXLFFBQVE7d0JBQzNCQyxRQUFRWixTQUFTWSxNQUFNO29CQUN6QjtnQkFFQTNDLGtCQUFrQjRCO2dCQUNsQnJCLFFBQVFhLEtBQUtBLElBQUksQ0FBQ2QsSUFBSTtnQkFDdEJHLGdCQUFnQlcsS0FBS0EsSUFBSSxDQUFDWixZQUFZO2dCQUN0Q0csYUFBYVMsS0FBS0EsSUFBSSxDQUFDd0IsS0FBSztnQkFDNUIvQixpQkFBaUJPLEtBQUtBLElBQUksQ0FBQ3lCLHVCQUF1QjtZQUNwRCxPQUFPO2dCQUNMLHFDQUFxQztnQkFDckMzRCxTQUFTO29CQUNQQyxZQUFZO29CQUNaQyxzQkFBc0I7b0JBQ3RCQyxlQUFlO29CQUNmQyxjQUFjO29CQUNkQyxVQUFVO29CQUNWQyxPQUFPO29CQUNQQyxPQUFPO29CQUNQQyxNQUFNO29CQUNOQyxRQUFRO29CQUNSQyxhQUFhO29CQUNiQyxnQkFBZ0I7b0JBQ2hCQyxpQkFBaUI7Z0JBQ25CO2dCQUVBRSxrQkFBa0I7b0JBQ2hCO3dCQUNFZ0MsSUFBSTt3QkFDSkUsTUFBTTt3QkFDTkMsUUFBUTt3QkFDUkcsTUFBTTt3QkFDTkcsUUFBUTt3QkFDUkMsVUFBVTt3QkFDVkMsUUFBUTtvQkFDVjtpQkFDRDtZQUNIO1lBRUF6QyxXQUFXO1FBQ2IsRUFBRSxPQUFPNEMsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtZQUMvQzVDLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTThDLGVBQThCO1FBQ2xDO1lBQ0VDLE9BQU87WUFDUGIsYUFBYTtZQUNiYyxNQUFNL0UsZ0xBQU1BO1lBQ1pnRixNQUFNO1lBQ05DLE9BQU87WUFDUEMsT0FBTztZQUNQcEUsT0FBTztRQUNUO1FBQ0E7WUFDRWdFLE9BQU87WUFDUGIsYUFBYTtZQUNiYyxNQUFNaEYsZ0xBQU1BO1lBQ1ppRixNQUFNO1lBQ05HLFNBQVM7WUFDVEQsT0FBTztZQUNQcEUsT0FBTztRQUNUO1FBQ0E7WUFDRWdFLE9BQU87WUFDUGIsYUFBYTtZQUNiYyxNQUFNOUUsZ0xBQVFBO1lBQ2QrRSxNQUFNO1lBQ05HLFNBQVM7WUFDVEQsT0FBTztZQUNQcEUsT0FBTztRQUNUO1FBQ0E7WUFDRWdFLE9BQU87WUFDUGIsYUFBYTtZQUNiYyxNQUFNekUsZ0xBQVFBO1lBQ2QwRSxNQUFNO1lBQ05FLE9BQU87WUFDUHBFLE9BQU87UUFDVDtRQUNBO1lBQ0VnRSxPQUFPO1lBQ1BiLGFBQWE7WUFDYmMsTUFBTTFFLGdMQUFLQTtZQUNYMkUsTUFBTTtZQUNORSxPQUFPO1lBQ1BwRSxPQUFPO1FBQ1Q7UUFDQTtZQUNFZ0UsT0FBTztZQUNQYixhQUFhO1lBQ2JjLE1BQU01RSxnTEFBR0E7WUFDVDZFLE1BQU07WUFDTkUsT0FBTztZQUNQcEUsT0FBTztRQUNUO0tBQ0Q7SUFFRCxNQUFNc0Usa0JBQWtCLENBQUNyQjtRQUN2QixPQUFRQTtZQUNOLEtBQUs7Z0JBQVEsT0FBT2hFLGdMQUFNQTtZQUMxQixLQUFLO2dCQUFTLE9BQU9DLGdMQUFNQTtZQUMzQixLQUFLO2dCQUFRLE9BQU9DLGdMQUFRQTtZQUM1QixLQUFLO2dCQUFPLE9BQU9LLGdMQUFRQTtZQUMzQixLQUFLO2dCQUFXLE9BQU9ELGdMQUFLQTtZQUM1QjtnQkFBUyxPQUFPRCxnTEFBUUE7UUFDMUI7SUFDRjtJQUVBLE1BQU1pRixpQkFBaUIsQ0FBQ2Y7UUFDdEIsT0FBUUE7WUFDTixLQUFLO2dCQUFhLE9BQU87WUFDekIsS0FBSztnQkFBVSxPQUFPO1lBQ3RCLEtBQUs7Z0JBQWEsT0FBTztZQUN6QixLQUFLO2dCQUFTLE9BQU87WUFDckIsS0FBSztnQkFBVyxPQUFPO1lBQ3ZCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLE1BQU1nQixtQkFBbUIsQ0FBQ2Y7UUFDeEIsT0FBUUE7WUFDTixLQUFLO2dCQUFZLE9BQU87WUFDeEIsS0FBSztnQkFBUSxPQUFPO1lBQ3BCLEtBQUs7Z0JBQVUsT0FBTztZQUN0QixLQUFLO2dCQUFPLE9BQU87WUFDbkI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsSUFBSXpDLFNBQVM7UUFDWCxxQkFDRSw4REFBQ2hDLG1FQUFlQTtzQkFDZCw0RUFBQ3lGO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs7Ozs7c0NBQ2YsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUFpQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUsxRDtJQUVBLHFCQUNFLDhEQUFDMUYsbUVBQWVBO2tCQUNkLDRFQUFDeUY7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7OzhDQUNDLDhEQUFDRTtvQ0FBR0QsV0FBVTs7c0RBQ1osOERBQUNFOzRDQUFLRixXQUFVO3NEQUFrQjs7Ozs7O3dDQUFhO3NEQUMvQyw4REFBQ0U7NENBQUtGLFdBQVU7c0RBQWtCOzs7Ozs7Ozs7Ozs7OENBRXBDLDhEQUFDRztvQ0FBRUgsV0FBVTs7d0NBQXdCO3dDQUNwQnJELENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTXlELFFBQVEsS0FBSTt3Q0FBZ0I7Ozs7Ozs7Ozs7Ozs7c0NBSXJELDhEQUFDTDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQXdCOzs7Ozs7c0RBQ3ZDLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDWnhELFlBQVlxQyxrQkFBa0I7Ozs7Ozs7Ozs7Ozs4Q0FHbkMsOERBQUN3QjtvQ0FBT0wsV0FBVTs7c0RBQ2hCLDhEQUFDckYsZ0xBQUdBOzRDQUFDcUYsV0FBVTs7Ozs7O3dDQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFPdEMsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1pyRCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU15RCxRQUFRLElBQUd6RCxLQUFLeUQsUUFBUSxDQUFDRSxTQUFTLENBQUMsR0FBRyxHQUFHQyxXQUFXLEtBQUs7Ozs7OztrREFFbEUsOERBQUNSOzswREFDQyw4REFBQ1M7Z0RBQUdSLFdBQVU7MERBQWlDckQsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNOEQsUUFBUSxNQUFJOUQsaUJBQUFBLDJCQUFBQSxLQUFNeUQsUUFBUSxLQUFJOzs7Ozs7MERBQ25GLDhEQUFDTDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNFOzs0REFBSzs0REFBTzVFLE1BQU1RLEtBQUs7Ozs7Ozs7a0VBQ3hCLDhEQUFDb0U7a0VBQUs7Ozs7OztrRUFDTiw4REFBQ0E7d0RBQUtGLFdBQVcseUNBTWhCLE9BTENyRCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU0rRCxJQUFJLE1BQUssVUFBVSxxQ0FDekIvRCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU0rRCxJQUFJLE1BQUssUUFBUSxpQ0FDdkIvRCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU0rRCxJQUFJLE1BQUssa0JBQWtCLCtCQUNqQy9ELENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTStELElBQUksTUFBSyxjQUFjLHFDQUM3QjtrRUFFQy9ELENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTStELElBQUksS0FBSTs7Ozs7O2tFQUVqQiw4REFBQ1I7a0VBQUs7Ozs7OztrRUFDTiw4REFBQ0E7OzREQUFLOzREQUFPNUUsTUFBTVMsSUFBSTs7Ozs7OztrRUFDdkIsOERBQUNtRTtrRUFBSzs7Ozs7O2tFQUNOLDhEQUFDSDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUMvRSxpTEFBS0E7Z0VBQUMrRSxXQUFVOzs7Ozs7MEVBQ2pCLDhEQUFDRTs7b0VBQU01RSxNQUFNVSxNQUFNO29FQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU01Qiw4REFBQytEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1oxRSxNQUFNTyxLQUFLLENBQUM4RSxjQUFjOzs7Ozs7a0RBRTdCLDhEQUFDWjt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7OztrREFDdkMsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O29EQUF5QjtvREFBRTFFLE1BQU1XLFdBQVc7b0RBQUM7Ozs7Ozs7MERBQzVELDhEQUFDZCxpTEFBT0E7Z0RBQUM2RSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFPM0IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDekYsZ0xBQU1BO29DQUFDeUYsV0FBVTs7Ozs7OzhDQUNsQiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1oxRSxNQUFNRSxVQUFVOzs7Ozs7OENBRW5CLDhEQUFDdUU7b0NBQUlDLFdBQVU7OENBQXdCOzs7Ozs7OENBQ3ZDLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FBOEI7Ozs7Ozs7Ozs7OztzQ0FHL0MsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3RGLGlMQUFHQTtvQ0FBQ3NGLFdBQVU7Ozs7Ozs4Q0FDZiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1oxRSxNQUFNRyxvQkFBb0I7Ozs7Ozs4Q0FFN0IsOERBQUNzRTtvQ0FBSUMsV0FBVTs4Q0FBd0I7Ozs7Ozs4Q0FDdkMsOERBQUNEO29DQUFJQyxXQUFVOzhDQUE0Qjs7Ozs7Ozs7Ozs7O3NDQUc3Qyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDdkYsZ0xBQVFBO29DQUFDdUYsV0FBVTs7Ozs7OzhDQUNwQiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1oxRSxNQUFNSSxhQUFhOzs7Ozs7OENBRXRCLDhEQUFDcUU7b0NBQUlDLFdBQVU7OENBQXdCOzs7Ozs7OENBQ3ZDLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FBK0I7Ozs7Ozs7Ozs7OztzQ0FHaEQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3hGLGdMQUFNQTtvQ0FBQ3dGLFdBQVU7Ozs7Ozs4Q0FDbEIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaMUUsTUFBTUssWUFBWTs7Ozs7OzhDQUVyQiw4REFBQ29FO29DQUFJQyxXQUFVOzhDQUF3Qjs7Ozs7OzhDQUN2Qyw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQTZCOzs7Ozs7Ozs7Ozs7c0NBRzlDLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNyRixnTEFBR0E7b0NBQUNxRixXQUFVOzs7Ozs7OENBQ2YsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaMUUsTUFBTU0sUUFBUSxDQUFDK0UsY0FBYzs7Ozs7OzhDQUVoQyw4REFBQ1o7b0NBQUlDLFdBQVU7OENBQXdCOzs7Ozs7OENBQ3ZDLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FBK0I7Ozs7Ozs7Ozs7OztzQ0FHaEQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2pGLGlMQUFVQTtvQ0FBQ2lGLFdBQVU7Ozs7Ozs4Q0FDdEIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaMUUsTUFBTVksY0FBYyxDQUFDeUUsY0FBYzs7Ozs7OzhDQUV0Qyw4REFBQ1o7b0NBQUlDLFdBQVU7OENBQXdCOzs7Ozs7OENBQ3ZDLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FBOEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFLakQsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUM5RSxpTEFBV0E7Z0NBQUM4RSxXQUFVOzs7Ozs7MENBQ3ZCLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNZO3dDQUFHWixXQUFVO2tEQUFvQzs7Ozs7O2tEQUNsRCw4REFBQ0c7d0NBQUVILFdBQVU7a0RBQXFCOzs7Ozs7a0RBR2xDLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNLO2dEQUFPTCxXQUFVOzBEQUE4Qjs7Ozs7OzBEQUdoRCw4REFBQ0U7Z0RBQUtGLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFPaEQsOERBQUNEOztzQ0FDQyw4REFBQ1M7NEJBQUdSLFdBQVU7OzhDQUNaLDhEQUFDRTtvQ0FBS0YsV0FBVTs4Q0FBa0I7Ozs7OztnQ0FBYTs4Q0FDL0MsOERBQUNFO29DQUFLRixXQUFVOzhDQUFrQjs7Ozs7Ozs7Ozs7O3NDQUdwQyw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ1pYLGFBQWFsQixHQUFHLENBQUMsQ0FBQzBDLFFBQVFDO2dDQUN6QixNQUFNQyxPQUFPRixPQUFPdEIsSUFBSTtnQ0FDeEIscUJBQ0UsOERBQUNRO29DQUVDQyxXQUFVO29DQUNWZ0IsU0FBUyxJQUFNQyxPQUFPQyxRQUFRLENBQUMxQixJQUFJLEdBQUdxQixPQUFPckIsSUFBSTs7c0RBRWpELDhEQUFDTzs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFXLHFCQUFrQyxPQUFiYSxPQUFPbkIsS0FBSyxFQUFDOzhEQUNoRCw0RUFBQ3FCO3dEQUFLZixXQUFXLGdCQUE2QixPQUFiYSxPQUFPbkIsS0FBSyxFQUFDOzs7Ozs7Ozs7Ozs4REFFaEQsOERBQUNLO29EQUFJQyxXQUFVOzt3REFDWmEsT0FBT3BCLEtBQUssa0JBQ1gsOERBQUNTOzREQUFLRixXQUFVO3NFQUNiYSxPQUFPcEIsS0FBSzs7Ozs7O3dEQUdoQm9CLE9BQU9sQixPQUFPLGtCQUNiLDhEQUFDTzs0REFBS0YsV0FBVTtzRUFBZ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFPdEcsOERBQUNZOzRDQUFHWixXQUFVO3NEQUNYYSxPQUFPdkIsS0FBSzs7Ozs7O3NEQUVmLDhEQUFDYTs0Q0FBRUgsV0FBVTtzREFDVmEsT0FBT3BDLFdBQVc7Ozs7OztzREFHckIsOERBQUNzQjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNFO29EQUFLRixXQUFXLDRCQUF5QyxPQUFiYSxPQUFPbkIsS0FBSzs4REFDdERtQixPQUFPdkYsS0FBSzs7Ozs7OzhEQUVmLDhEQUFDRixpTEFBSUE7b0RBQUM0RSxXQUFVOzs7Ozs7Ozs7Ozs7O21DQWpDYmM7Ozs7OzRCQXFDWDs7Ozs7Ozs7Ozs7O2dCQUtIL0QsMkJBQ0MsOERBQUNnRDs7c0NBQ0MsOERBQUNTOzRCQUFHUixXQUFVOzs4Q0FDWiw4REFBQ0U7b0NBQUtGLFdBQVU7OENBQWtCOzs7Ozs7Z0NBQVk7OENBQzlDLDhEQUFDRTtvQ0FBS0YsV0FBVTs4Q0FBa0I7Ozs7Ozs7Ozs7OztzQ0FHcEMsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNZO29EQUFHWixXQUFVOzhEQUEyQjs7Ozs7OzhEQUN6Qyw4REFBQ3pGLGdMQUFNQTtvREFBQ3lGLFdBQVU7Ozs7Ozs7Ozs7OztzREFFcEIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRTs0REFBS0YsV0FBVTtzRUFBZ0I7Ozs7OztzRUFDaEMsOERBQUNFOzREQUFLRixXQUFVOztnRUFBY2pELFVBQVVrQyxLQUFLLENBQUNrQyxrQkFBa0I7Z0VBQUM7Z0VBQUlwRSxVQUFVcUUsTUFBTSxDQUFDQyxLQUFLLEtBQUssQ0FBQyxJQUFJLE1BQU10RSxVQUFVcUUsTUFBTSxDQUFDQyxLQUFLOzs7Ozs7Ozs7Ozs7O2dEQUVsSXRFLFVBQVVxRSxNQUFNLENBQUNDLEtBQUssS0FBSyxDQUFDLG1CQUMzQiw4REFBQ3RCO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDRDt3REFDQ0MsV0FBVTt3REFDVnNCLE9BQU87NERBQUVDLE9BQU8sR0FBMEQsT0FBdkR4RCxLQUFLeUQsR0FBRyxDQUFDekUsVUFBVTBFLFVBQVUsQ0FBQ04sa0JBQWtCLEVBQUUsTUFBSzt3REFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBT3ZGLDhEQUFDcEI7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNZO29EQUFHWixXQUFVOzhEQUEyQjs7Ozs7OzhEQUN6Qyw4REFBQ2hGLGlMQUFHQTtvREFBQ2dGLFdBQVU7Ozs7Ozs7Ozs7OztzREFFakIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRTs0REFBS0YsV0FBVTtzRUFBZ0I7Ozs7OztzRUFDaEMsOERBQUNFOzREQUFLRixXQUFVOztnRUFBY2pELFVBQVVrQyxLQUFLLENBQUN0RCxZQUFZO2dFQUFDO2dFQUFJb0IsVUFBVXFFLE1BQU0sQ0FBQ00sS0FBSyxLQUFLLENBQUMsSUFBSSxNQUFNM0UsVUFBVXFFLE1BQU0sQ0FBQ00sS0FBSzs7Ozs7Ozs7Ozs7OztnREFFNUgzRSxVQUFVcUUsTUFBTSxDQUFDTSxLQUFLLEtBQUssQ0FBQyxtQkFDM0IsOERBQUMzQjtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0Q7d0RBQ0NDLFdBQVU7d0RBQ1ZzQixPQUFPOzREQUFFQyxPQUFPLEdBQW9ELE9BQWpEeEQsS0FBS3lELEdBQUcsQ0FBQ3pFLFVBQVUwRSxVQUFVLENBQUM5RixZQUFZLEVBQUUsTUFBSzt3REFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBT2pGLDhEQUFDb0U7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNZO29EQUFHWixXQUFVOzhEQUEyQjs7Ozs7OzhEQUN6Qyw4REFBQ3ZGLGdMQUFRQTtvREFBQ3VGLFdBQVU7Ozs7Ozs7Ozs7OztzREFFdEIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRTs0REFBS0YsV0FBVTtzRUFBZ0I7Ozs7OztzRUFDaEMsOERBQUNFOzREQUFLRixXQUFVOztnRUFBY2pELFVBQVVrQyxLQUFLLENBQUNyQixZQUFZO2dFQUFDO2dFQUFJYixVQUFVcUUsTUFBTSxDQUFDTyxLQUFLLEtBQUssQ0FBQyxJQUFJLE1BQU01RSxVQUFVcUUsTUFBTSxDQUFDTyxLQUFLOzs7Ozs7Ozs7Ozs7O2dEQUU1SDVFLFVBQVVxRSxNQUFNLENBQUNPLEtBQUssS0FBSyxDQUFDLG1CQUMzQiw4REFBQzVCO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDRDt3REFDQ0MsV0FBVTt3REFDVnNCLE9BQU87NERBQUVDLE9BQU8sR0FBb0QsT0FBakR4RCxLQUFLeUQsR0FBRyxDQUFDekUsVUFBVTBFLFVBQVUsQ0FBQzdELFlBQVksRUFBRSxNQUFLO3dEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFXdEZmLGdCQUFnQkEsYUFBYStFLE1BQU0sR0FBRyxtQkFDckMsOERBQUM3Qjs7c0NBQ0MsOERBQUNTOzRCQUFHUixXQUFVOzs4Q0FDWiw4REFBQ0U7b0NBQUtGLFdBQVU7OENBQWtCOzs7Ozs7Z0NBQWM7OENBQ2hELDhEQUFDRTtvQ0FBS0YsV0FBVTs4Q0FBa0I7Ozs7Ozs7Ozs7OztzQ0FHcEMsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNabkQsYUFBYWdGLEtBQUssQ0FBQyxHQUFHLEdBQUcxRCxHQUFHLENBQUMsQ0FBQzJELDRCQUM3Qiw4REFBQy9CO29DQUVDQyxXQUFXLHNEQUlWLE9BSEM4QixZQUFZQyxRQUFRLEdBQ2hCLCtDQUNBOztzREFHTiw4REFBQ2hDOzRDQUFJQyxXQUFXLHdFQUlmLE9BSEM4QixZQUFZQyxRQUFRLEdBQ2hCLDJDQUNBOztnREFFSEQsWUFBWXZDLElBQUksS0FBSywwQkFBWSw4REFBQ2hGLGdMQUFNQTtvREFBQ3lGLFdBQVU7Ozs7OztnREFDbkQ4QixZQUFZdkMsSUFBSSxLQUFLLHVCQUFTLDhEQUFDdkUsaUxBQUdBO29EQUFDZ0YsV0FBVTs7Ozs7O2dEQUM3QzhCLFlBQVl2QyxJQUFJLEtBQUssdUJBQVMsOERBQUM3RSxpTEFBR0E7b0RBQUNzRixXQUFVOzs7Ozs7Z0RBQzdDOEIsWUFBWXZDLElBQUksS0FBSyx5QkFBVyw4REFBQ3RFLGlMQUFLQTtvREFBQytFLFdBQVU7Ozs7Ozs7Ozs7OztzREFHcEQsOERBQUNZOzRDQUFHWixXQUFXLHNCQUE0RSxPQUF0RDhCLFlBQVlDLFFBQVEsR0FBRyxlQUFlO3NEQUN4RUQsWUFBWXhDLEtBQUs7Ozs7OztzREFHcEIsOERBQUNhOzRDQUFFSCxXQUFVO3NEQUNWOEIsWUFBWXJELFdBQVc7Ozs7OztzREFHMUIsOERBQUNzQjs0Q0FBSUMsV0FBVyx1RUFLZixPQUpDOEIsWUFBWUUsTUFBTSxLQUFLLGNBQWMscUNBQ3JDRixZQUFZRSxNQUFNLEtBQUssU0FBUyxxQ0FDaENGLFlBQVlFLE1BQU0sS0FBSyxTQUFTLGlDQUNoQztzREFFQ0YsWUFBWUUsTUFBTTs7Ozs7O3dDQUdwQkYsWUFBWUMsUUFBUSxJQUFJRCxZQUFZRyxVQUFVLGtCQUM3Qyw4REFBQ2xDOzRDQUFJQyxXQUFVOztnREFBNkI7Z0RBQ2hDLElBQUl0RCxLQUFLb0YsWUFBWUcsVUFBVSxFQUFFQyxrQkFBa0I7Ozs7Ozs7O21DQXJDNURKLFlBQVl6RCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7O2dCQStDNUJwQixpQkFBaUJBLGNBQWMyRSxNQUFNLEdBQUcsbUJBQ3ZDLDhEQUFDN0I7O3NDQUNDLDhEQUFDUzs0QkFBR1IsV0FBVTs7OENBQ1osOERBQUNFO29DQUFLRixXQUFVOzhDQUFrQjs7Ozs7O2dDQUFnQjs4Q0FDbEQsOERBQUNFO29DQUFLRixXQUFVOzhDQUFrQjs7Ozs7Ozs7Ozs7O3NDQUdwQyw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ1ovQyxjQUFja0IsR0FBRyxDQUFDLENBQUNnRSxxQkFDbEIsOERBQUNwQztvQ0FBa0JDLFdBQVU7O3NEQUMzQiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUNhOzREQUFHWixXQUFVO3NFQUFpQ21DLEtBQUs5RCxFQUFFOzs7Ozs7c0VBQ3RELDhEQUFDOEI7NERBQUVILFdBQVU7c0VBQXlCbUMsS0FBSzdDLEtBQUs7Ozs7Ozs7Ozs7Ozs4REFFbEQsOERBQUNTO29EQUFJQyxXQUFXLHNEQUtmLE9BSkNtQyxLQUFLcEQsUUFBUSxLQUFLLGFBQWEsK0JBQy9Cb0QsS0FBS3BELFFBQVEsS0FBSyxTQUFTLHFDQUMzQm9ELEtBQUtwRCxRQUFRLEtBQUssV0FBVyxxQ0FDN0I7OERBRUNvRCxLQUFLcEQsUUFBUTs7Ozs7Ozs7Ozs7O3NEQUlsQiw4REFBQ29COzRDQUFFSCxXQUFVO3NEQUE4Qm1DLEtBQUsxRCxXQUFXOzs7Ozs7c0RBRTNELDhEQUFDc0I7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7d0RBQWdCO3NFQUN2Qiw4REFBQ0U7NERBQUtGLFdBQVU7c0VBQTBCbUMsS0FBS3RHLEtBQUs7Ozs7Ozs7Ozs7Ozs4REFFNUQsOERBQUNrRTtvREFBSUMsV0FBVTs7d0RBQWdCO3NFQUNuQiw4REFBQ0U7NERBQUtGLFdBQVU7c0VBQWtDbUMsS0FBS0MsZUFBZSxDQUFDekIsY0FBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzttQ0F2QjNGd0IsS0FBSzlELEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBaUN6Qiw4REFBQzBCOztzQ0FDQyw4REFBQ1M7NEJBQUdSLFdBQVU7OzhDQUNaLDhEQUFDRTtvQ0FBS0YsV0FBVTs4Q0FBa0I7Ozs7OztnQ0FBYzs4Q0FDaEQsOERBQUNFO29DQUFLRixXQUFVOzhDQUFrQjs7Ozs7Ozs7Ozs7O3NDQUdwQyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWjVELGVBQWUrQixHQUFHLENBQUMsQ0FBQ0M7d0NBQ25CLE1BQU0yQyxPQUFPbkIsZ0JBQWdCeEIsU0FBU0csSUFBSTt3Q0FDMUMscUJBQ0UsOERBQUN3Qjs0Q0FFQ0MsV0FBVTs7OERBRVYsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQ2IsNEVBQUNlO2dFQUFLZixXQUFVOzs7Ozs7Ozs7OztzRUFHbEIsOERBQUNEOzs4RUFDQyw4REFBQ0E7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDcUM7NEVBQUdyQyxXQUFVO3NGQUEwQjVCLFNBQVNJLE1BQU07Ozs7OztzRkFDdkQsOERBQUMwQjs0RUFBS0YsV0FBVyxzREFBc0YsT0FBaENILGVBQWV6QixTQUFTVSxNQUFNO3NGQUNsR1YsU0FBU1UsTUFBTTs7Ozs7O3dFQUVqQlYsU0FBU1csUUFBUSxrQkFDaEIsOERBQUNtQjs0RUFBS0YsV0FBVyxzREFBMEYsT0FBcENGLGlCQUFpQjFCLFNBQVNXLFFBQVE7c0ZBQ3RHWCxTQUFTVyxRQUFROzs7Ozs7Ozs7Ozs7OEVBSXhCLDhEQUFDb0I7b0VBQUVILFdBQVU7OEVBQXlCNUIsU0FBU1ksTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUl6RCw4REFBQ2U7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTtzRUFBeUI1QixTQUFTTyxJQUFJOzs7Ozs7c0VBQ3JELDhEQUFDb0I7NERBQUlDLFdBQVU7c0VBQW9DNUIsU0FBU0csSUFBSTs7Ozs7Ozs7Ozs7OzsyQ0ExQjdESCxTQUFTQyxFQUFFOzs7OztvQ0E4QnRCOzs7Ozs7OENBR0YsOERBQUMwQjtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0s7d0NBQU9MLFdBQVU7a0RBQWtFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU2xHO0dBN29Cd0IzRTtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4P2QxMjUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCBEYXNoYm9hcmRMYXlvdXQgZnJvbSAnQC9jb21wb25lbnRzL0Rhc2hib2FyZExheW91dCdcbmltcG9ydCB7IFxuICBTaGllbGQsIFxuICBTZWFyY2gsIFxuICBGaWxlVGV4dCwgXG4gIEJ1ZywgXG4gIFphcCwgXG4gIFVzZXJzLCBcbiAgQmFyQ2hhcnQzLCBcbiAgQWN0aXZpdHksXG4gIEdsb2JlLFxuICBEYXRhYmFzZSxcbiAgQm90LFxuICBDcm93bixcbiAgVHJlbmRpbmdVcCxcbiAgQ2xvY2ssXG4gIEFsZXJ0VHJpYW5nbGUsXG4gIFRhcmdldCxcbiAgRXllLFxuICBGbGFtZSxcbiAgU3RhcixcbiAgQXdhcmQsXG4gIENhbGVuZGFyLFxuICBNYXBQaW4sXG4gIFdpZmksXG4gIFNlcnZlcixcbiAgTG9jayxcbiAgVW5sb2NrLFxuICBDaGVja0NpcmNsZSxcbiAgWENpcmNsZSxcbiAgQWxlcnRDaXJjbGUsXG4gIEFycm93VXAsXG4gIEFycm93RG93bixcbiAgUGxheSxcbiAgUGF1c2UsXG4gIFNldHRpbmdzXG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuaW50ZXJmYWNlIERhc2hib2FyZFN0YXRzIHtcbiAgdG90YWxTY2FuczogbnVtYmVyXG4gIHZ1bG5lcmFiaWxpdGllc0ZvdW5kOiBudW1iZXJcbiAgZmlsZXNBbmFseXplZDogbnVtYmVyXG4gIG9zaW50UXVlcmllczogbnVtYmVyXG4gIGFwaUNhbGxzOiBudW1iZXJcbiAgc2NvcmU6IG51bWJlclxuICBsZXZlbDogbnVtYmVyXG4gIHJhbms6IG51bWJlclxuICBzdHJlYWs6IG51bWJlclxuICBwb2ludHNUb2RheTogbnVtYmVyXG4gIHBvaW50c1RoaXNXZWVrOiBudW1iZXJcbiAgcG9pbnRzVGhpc01vbnRoOiBudW1iZXJcbn1cblxuaW50ZXJmYWNlIFJlY2VudEFjdGl2aXR5IHtcbiAgaWQ6IHN0cmluZ1xuICB0eXBlOiAnc2NhbicgfCAnb3NpbnQnIHwgJ2ZpbGUnIHwgJ2N2ZScgfCAnZG9ya2luZydcbiAgdGFyZ2V0OiBzdHJpbmdcbiAgdGltZTogc3RyaW5nXG4gIHN0YXR1czogJ2NvbXBsZXRlZCcgfCAnZmFpbGVkJyB8ICdtYWxpY2lvdXMnIHwgJ2NsZWFuJyB8ICdwZW5kaW5nJ1xuICBzZXZlcml0eT86ICdsb3cnIHwgJ21lZGl1bScgfCAnaGlnaCcgfCAnY3JpdGljYWwnXG4gIHJlc3VsdD86IHN0cmluZ1xufVxuXG5pbnRlcmZhY2UgUXVpY2tBY3Rpb24ge1xuICB0aXRsZTogc3RyaW5nXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgaWNvbjogYW55XG4gIGhyZWY6IHN0cmluZ1xuICBiYWRnZT86IHN0cmluZ1xuICBwcmVtaXVtPzogYm9vbGVhblxuICBjb2xvcjogc3RyaW5nXG4gIHN0YXRzPzogc3RyaW5nXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZFBhZ2UoKSB7XG4gIGNvbnN0IFtzdGF0cywgc2V0U3RhdHNdID0gdXNlU3RhdGU8RGFzaGJvYXJkU3RhdHM+KHtcbiAgICB0b3RhbFNjYW5zOiAwLFxuICAgIHZ1bG5lcmFiaWxpdGllc0ZvdW5kOiAwLFxuICAgIGZpbGVzQW5hbHl6ZWQ6IDAsXG4gICAgb3NpbnRRdWVyaWVzOiAwLFxuICAgIGFwaUNhbGxzOiAwLFxuICAgIHNjb3JlOiAwLFxuICAgIGxldmVsOiAwLFxuICAgIHJhbms6IDAsXG4gICAgc3RyZWFrOiAwLFxuICAgIHBvaW50c1RvZGF5OiAwLFxuICAgIHBvaW50c1RoaXNXZWVrOiAwLFxuICAgIHBvaW50c1RoaXNNb250aDogMFxuICB9KVxuICBcbiAgY29uc3QgW3JlY2VudEFjdGl2aXR5LCBzZXRSZWNlbnRBY3Rpdml0eV0gPSB1c2VTdGF0ZTxSZWNlbnRBY3Rpdml0eVtdPihbXSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2N1cnJlbnRUaW1lLCBzZXRDdXJyZW50VGltZV0gPSB1c2VTdGF0ZShuZXcgRGF0ZSgpKVxuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpXG4gIGNvbnN0IFthY2hpZXZlbWVudHMsIHNldEFjaGlldmVtZW50c10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pXG4gIGNvbnN0IFtwbGFuVXNhZ2UsIHNldFBsYW5Vc2FnZV0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpXG4gIGNvbnN0IFt0cmVuZGluZ1Z1bG5zLCBzZXRUcmVuZGluZ1Z1bG5zXSA9IHVzZVN0YXRlPGFueVtdPihbXSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFVwZGF0ZSB0aW1lIGV2ZXJ5IG1pbnV0ZVxuICAgIGNvbnN0IHRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgc2V0Q3VycmVudFRpbWUobmV3IERhdGUoKSlcbiAgICB9LCA2MDAwMClcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKHRpbWVyKVxuICB9LCBbXSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxvYWREYXNoYm9hcmREYXRhKClcbiAgfSwgW10pXG5cbiAgY29uc3QgbG9hZERhc2hib2FyZERhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSlcblxuICAgICAgLy8gRmV0Y2ggcmVhbCBkYXNoYm9hcmQgZGF0YSBmcm9tIEFQSVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9kYXNoYm9hcmQvc3RhdHMnKVxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIHNldFN0YXRzKHtcbiAgICAgICAgICB0b3RhbFNjYW5zOiBkYXRhLmRhdGEuc3RhdHMudG90YWxTY2FucyxcbiAgICAgICAgICB2dWxuZXJhYmlsaXRpZXNGb3VuZDogZGF0YS5kYXRhLnN0YXRzLnZ1bG5lcmFiaWxpdGllc0ZvdW5kLFxuICAgICAgICAgIGZpbGVzQW5hbHl6ZWQ6IGRhdGEuZGF0YS5zdGF0cy5maWxlQW5hbHlzZXMsXG4gICAgICAgICAgb3NpbnRRdWVyaWVzOiBkYXRhLmRhdGEuc3RhdHMub3NpbnRRdWVyaWVzLFxuICAgICAgICAgIGFwaUNhbGxzOiBkYXRhLmRhdGEuc3RhdHMuY3ZlU2VhcmNoZXMgKyBkYXRhLmRhdGEuc3RhdHMuZG9ya2luZ1F1ZXJpZXMsXG4gICAgICAgICAgc2NvcmU6IGRhdGEuZGF0YS51c2VyLnNjb3JlLFxuICAgICAgICAgIGxldmVsOiBkYXRhLmRhdGEudXNlci5sZXZlbCxcbiAgICAgICAgICByYW5rOiBkYXRhLmRhdGEudXNlci5yYW5rLFxuICAgICAgICAgIHN0cmVhazogZGF0YS5kYXRhLnVzZXIuc3RyZWFrLFxuICAgICAgICAgIHBvaW50c1RvZGF5OiBNYXRoLmZsb29yKGRhdGEuZGF0YS51c2VyLnNjb3JlICogMC4xKSxcbiAgICAgICAgICBwb2ludHNUaGlzV2VlazogTWF0aC5mbG9vcihkYXRhLmRhdGEudXNlci5zY29yZSAqIDAuMyksXG4gICAgICAgICAgcG9pbnRzVGhpc01vbnRoOiBNYXRoLmZsb29yKGRhdGEuZGF0YS51c2VyLnNjb3JlICogMC42KVxuICAgICAgICB9KVxuXG4gICAgICAgIC8vIFRyYW5zZm9ybSByZWNlbnQgYWN0aXZpdGllcyB0byBtYXRjaCBpbnRlcmZhY2VcbiAgICAgICAgY29uc3QgdHJhbnNmb3JtZWRBY3Rpdml0aWVzID0gZGF0YS5kYXRhLnJlY2VudEFjdGl2aXRpZXMubWFwKChhY3Rpdml0eTogYW55KSA9PiAoe1xuICAgICAgICAgIGlkOiBhY3Rpdml0eS5pZC50b1N0cmluZygpLFxuICAgICAgICAgIHR5cGU6IGFjdGl2aXR5LnR5cGUgPT09ICd2dWxuZXJhYmlsaXR5X3NjYW4nID8gJ3NjYW4nIDpcbiAgICAgICAgICAgICAgICBhY3Rpdml0eS50eXBlID09PSAnb3NpbnRfcXVlcnknID8gJ29zaW50JyA6XG4gICAgICAgICAgICAgICAgYWN0aXZpdHkudHlwZSA9PT0gJ2ZpbGVfYW5hbHlzaXMnID8gJ2ZpbGUnIDpcbiAgICAgICAgICAgICAgICBhY3Rpdml0eS50eXBlID09PSAnY3ZlX3NlYXJjaCcgPyAnY3ZlJyA6ICdkb3JraW5nJyxcbiAgICAgICAgICB0YXJnZXQ6IGFjdGl2aXR5LmRlc2NyaXB0aW9uLmluY2x1ZGVzKCd0YXJnZXQuJykgPyAndGFyZ2V0LmV4YW1wbGUuY29tJyA6XG4gICAgICAgICAgICAgICAgICBhY3Rpdml0eS5kZXNjcmlwdGlvbi5pbmNsdWRlcygnZG9tYWluJykgPyAnZXhhbXBsZS5jb20nIDpcbiAgICAgICAgICAgICAgICAgIGFjdGl2aXR5LmRlc2NyaXB0aW9uLmluY2x1ZGVzKCdmaWxlOicpID8gJ21hbHdhcmUuZXhlJyA6XG4gICAgICAgICAgICAgICAgICBhY3Rpdml0eS5kZXNjcmlwdGlvbi5pbmNsdWRlcygnQXBhY2hlJykgPyAnQXBhY2hlIEhUVFAgU2VydmVyJyA6XG4gICAgICAgICAgICAgICAgICBhY3Rpdml0eS5kZXNjcmlwdGlvbi5pbmNsdWRlcygnZGF0YWJhc2VzJykgPyAnR29vZ2xlIFNlYXJjaCcgOiAnVW5rbm93bicsXG4gICAgICAgICAgdGltZTogbmV3IERhdGUoYWN0aXZpdHkudGltZXN0YW1wKS50b0xvY2FsZVRpbWVTdHJpbmcoKSArICcgYWdvJyxcbiAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICAgIHNldmVyaXR5OiBhY3Rpdml0eS5zZXZlcml0eSxcbiAgICAgICAgICByZXN1bHQ6IGFjdGl2aXR5LnJlc3VsdFxuICAgICAgICB9KSlcblxuICAgICAgICBzZXRSZWNlbnRBY3Rpdml0eSh0cmFuc2Zvcm1lZEFjdGl2aXRpZXMpXG4gICAgICAgIHNldFVzZXIoZGF0YS5kYXRhLnVzZXIpXG4gICAgICAgIHNldEFjaGlldmVtZW50cyhkYXRhLmRhdGEuYWNoaWV2ZW1lbnRzKVxuICAgICAgICBzZXRQbGFuVXNhZ2UoZGF0YS5kYXRhLnVzYWdlKVxuICAgICAgICBzZXRUcmVuZGluZ1Z1bG5zKGRhdGEuZGF0YS50cmVuZGluZ1Z1bG5lcmFiaWxpdGllcylcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIEZhbGxiYWNrIHRvIG1vY2sgZGF0YSBpZiBBUEkgZmFpbHNcbiAgICAgICAgc2V0U3RhdHMoe1xuICAgICAgICAgIHRvdGFsU2NhbnM6IDE0MixcbiAgICAgICAgICB2dWxuZXJhYmlsaXRpZXNGb3VuZDogMjMsXG4gICAgICAgICAgZmlsZXNBbmFseXplZDogODksXG4gICAgICAgICAgb3NpbnRRdWVyaWVzOiA0NTYsXG4gICAgICAgICAgYXBpQ2FsbHM6IDI4NDcsXG4gICAgICAgICAgc2NvcmU6IDg5NTAsXG4gICAgICAgICAgbGV2ZWw6IDI4LFxuICAgICAgICAgIHJhbms6IDE1NixcbiAgICAgICAgICBzdHJlYWs6IDEyLFxuICAgICAgICAgIHBvaW50c1RvZGF5OiAyNTAsXG4gICAgICAgICAgcG9pbnRzVGhpc1dlZWs6IDE0NTAsXG4gICAgICAgICAgcG9pbnRzVGhpc01vbnRoOiA1ODkwXG4gICAgICAgIH0pXG5cbiAgICAgICAgc2V0UmVjZW50QWN0aXZpdHkoW1xuICAgICAgICAgIHtcbiAgICAgICAgICAgIGlkOiAnMScsXG4gICAgICAgICAgICB0eXBlOiAnc2NhbicsXG4gICAgICAgICAgICB0YXJnZXQ6ICdleGFtcGxlLmNvbScsXG4gICAgICAgICAgICB0aW1lOiAnMiBtaW51dGVzIGFnbycsXG4gICAgICAgICAgICBzdGF0dXM6ICdjb21wbGV0ZWQnLFxuICAgICAgICAgICAgc2V2ZXJpdHk6ICdoaWdoJyxcbiAgICAgICAgICAgIHJlc3VsdDogJzMgdnVsbmVyYWJpbGl0aWVzIGZvdW5kJ1xuICAgICAgICAgIH1cbiAgICAgICAgXSlcbiAgICAgIH1cbiAgICAgIFxuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBkYXNoYm9hcmQgZGF0YTonLCBlcnJvcilcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgcXVpY2tBY3Rpb25zOiBRdWlja0FjdGlvbltdID0gW1xuICAgIHtcbiAgICAgIHRpdGxlOiAnT1NJTlQgTG9va3VwJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAnSW52ZXN0aWdhdGUgZW1haWxzLCBwaG9uZSBudW1iZXJzLCBhbmQgcGVyc29uYWwgaW5mb3JtYXRpb24nLFxuICAgICAgaWNvbjogU2VhcmNoLFxuICAgICAgaHJlZjogJy9vc2ludCcsXG4gICAgICBiYWRnZTogJ1BvcHVsYXInLFxuICAgICAgY29sb3I6ICdjeWJlci1wcmltYXJ5JyxcbiAgICAgIHN0YXRzOiAnNDU2IHF1ZXJpZXMnXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ1Z1bG5lcmFiaWxpdHkgU2NhbicsXG4gICAgICBkZXNjcmlwdGlvbjogJ1NjYW4gd2Vic2l0ZXMgYW5kIGFwcGxpY2F0aW9ucyBmb3Igc2VjdXJpdHkgdnVsbmVyYWJpbGl0aWVzJyxcbiAgICAgIGljb246IFNoaWVsZCxcbiAgICAgIGhyZWY6ICcvc2Nhbm5lcicsXG4gICAgICBwcmVtaXVtOiB0cnVlLFxuICAgICAgY29sb3I6ICdjeWJlci1zZWNvbmRhcnknLFxuICAgICAgc3RhdHM6ICcxNDIgc2NhbnMnXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ0ZpbGUgQW5hbHlzaXMnLFxuICAgICAgZGVzY3JpcHRpb246ICdBbmFseXplIGZpbGVzIGZvciBtYWx3YXJlLCB3ZWJzaGVsbHMsIGFuZCBzdXNwaWNpb3VzIGNvbnRlbnQnLFxuICAgICAgaWNvbjogRmlsZVRleHQsXG4gICAgICBocmVmOiAnL2ZpbGUtYW5hbHl6ZXInLFxuICAgICAgcHJlbWl1bTogdHJ1ZSxcbiAgICAgIGNvbG9yOiAnY3liZXItYWNjZW50JyxcbiAgICAgIHN0YXRzOiAnODkgZmlsZXMnXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ0NWRSBEYXRhYmFzZScsXG4gICAgICBkZXNjcmlwdGlvbjogJ1NlYXJjaCBhbmQgZXhwbG9yZSB0aGUgbGF0ZXN0IENWRSB2dWxuZXJhYmlsaXRpZXMnLFxuICAgICAgaWNvbjogRGF0YWJhc2UsXG4gICAgICBocmVmOiAnL2N2ZScsXG4gICAgICBjb2xvcjogJ2dyZWVuLTQwMCcsXG4gICAgICBzdGF0czogJzUwSysgQ1ZFcydcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAnR29vZ2xlIERvcmtpbmcnLFxuICAgICAgZGVzY3JpcHRpb246ICdVc2UgYWR2YW5jZWQgc2VhcmNoIHF1ZXJpZXMgdG8gZmluZCBzZW5zaXRpdmUgaW5mb3JtYXRpb24nLFxuICAgICAgaWNvbjogR2xvYmUsXG4gICAgICBocmVmOiAnL2RvcmtpbmcnLFxuICAgICAgY29sb3I6ICdibHVlLTQwMCcsXG4gICAgICBzdGF0czogJ0FkdmFuY2VkJ1xuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6ICdBUEkgUGxheWdyb3VuZCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ1Rlc3QgYW5kIGV4cGxvcmUgb3VyIEFQSSBlbmRwb2ludHMnLFxuICAgICAgaWNvbjogWmFwLFxuICAgICAgaHJlZjogJy9wbGF5Z3JvdW5kJyxcbiAgICAgIGNvbG9yOiAncHVycGxlLTQwMCcsXG4gICAgICBzdGF0czogJzIuOEsgY2FsbHMnXG4gICAgfVxuICBdXG5cbiAgY29uc3QgZ2V0QWN0aXZpdHlJY29uID0gKHR5cGU6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgY2FzZSAnc2Nhbic6IHJldHVybiBTaGllbGRcbiAgICAgIGNhc2UgJ29zaW50JzogcmV0dXJuIFNlYXJjaFxuICAgICAgY2FzZSAnZmlsZSc6IHJldHVybiBGaWxlVGV4dFxuICAgICAgY2FzZSAnY3ZlJzogcmV0dXJuIERhdGFiYXNlXG4gICAgICBjYXNlICdkb3JraW5nJzogcmV0dXJuIEdsb2JlXG4gICAgICBkZWZhdWx0OiByZXR1cm4gQWN0aXZpdHlcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRTdGF0dXNDb2xvciA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdjb21wbGV0ZWQnOiByZXR1cm4gJ3RleHQtZ3JlZW4tNDAwJ1xuICAgICAgY2FzZSAnZmFpbGVkJzogcmV0dXJuICd0ZXh0LXJlZC00MDAnXG4gICAgICBjYXNlICdtYWxpY2lvdXMnOiByZXR1cm4gJ3RleHQtcmVkLTQwMCdcbiAgICAgIGNhc2UgJ2NsZWFuJzogcmV0dXJuICd0ZXh0LWdyZWVuLTQwMCdcbiAgICAgIGNhc2UgJ3BlbmRpbmcnOiByZXR1cm4gJ3RleHQteWVsbG93LTQwMCdcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAndGV4dC1ncmF5LTQwMCdcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRTZXZlcml0eUNvbG9yID0gKHNldmVyaXR5Pzogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChzZXZlcml0eSkge1xuICAgICAgY2FzZSAnY3JpdGljYWwnOiByZXR1cm4gJ3RleHQtcmVkLTQwMCBiZy1yZWQtNDAwLzIwJ1xuICAgICAgY2FzZSAnaGlnaCc6IHJldHVybiAndGV4dC1vcmFuZ2UtNDAwIGJnLW9yYW5nZS00MDAvMjAnXG4gICAgICBjYXNlICdtZWRpdW0nOiByZXR1cm4gJ3RleHQteWVsbG93LTQwMCBiZy15ZWxsb3ctNDAwLzIwJ1xuICAgICAgY2FzZSAnbG93JzogcmV0dXJuICd0ZXh0LWdyZWVuLTQwMCBiZy1ncmVlbi00MDAvMjAnXG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ3RleHQtZ3JheS00MDAgYmctZ3JheS00MDAvMjAnXG4gICAgfVxuICB9XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPERhc2hib2FyZExheW91dD5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci0yIGJvcmRlci1jeWJlci1wcmltYXJ5IGJvcmRlci10LXRyYW5zcGFyZW50IG14LWF1dG8gbWItNFwiPjwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWN5YmVyLXByaW1hcnkgZm9udC1tZWRpdW1cIj5Mb2FkaW5nIGN5YmVyIGRhc2hib2FyZC4uLjwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvRGFzaGJvYXJkTGF5b3V0PlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPERhc2hib2FyZExheW91dD5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XG4gICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBsZzpmbGV4LXJvdyBsZzppdGVtcy1jZW50ZXIgbGc6anVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgbWItMlwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWN5YmVyLWdsb3dcIj5DeWJlcjwvc3Bhbj57JyAnfVxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWN5YmVyLXBpbmtcIj5Db21tYW5kPC9zcGFuPlxuICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgdGV4dC1sZ1wiPlxuICAgICAgICAgICAgICBXZWxjb21lIGJhY2ssIHt1c2VyPy51c2VybmFtZSB8fCAnQ3liZXIgV2Fycmlvcid9ISBZb3VyIGRpZ2l0YWwgYXJzZW5hbCBhd2FpdHMuXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgbGc6bXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPkN1cnJlbnQgVGltZTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tb25vIHRleHQtY3liZXItcHJpbWFyeVwiPlxuICAgICAgICAgICAgICAgIHtjdXJyZW50VGltZS50b0xvY2FsZVRpbWVTdHJpbmcoKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwiYnRuLWN5YmVyLXByaW1hcnlcIj5cbiAgICAgICAgICAgICAgPFphcCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICBRdWljayBTY2FuXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFVzZXIgUHJvZmlsZSBDYXJkICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtY3liZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgcm91bmRlZC1mdWxsIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1jeWJlci1wcmltYXJ5IHRvLWN5YmVyLXNlY29uZGFyeSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibGFja1wiPlxuICAgICAgICAgICAgICAgIHt1c2VyPy51c2VybmFtZSA/IHVzZXIudXNlcm5hbWUuc3Vic3RyaW5nKDAsIDIpLnRvVXBwZXJDYXNlKCkgOiAnQ1cnfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj57dXNlcj8uZnVsbE5hbWUgfHwgdXNlcj8udXNlcm5hbWUgfHwgJ0N5YmVyIFdhcnJpb3InfTwvaDI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgdGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj5MZXZlbCB7c3RhdHMubGV2ZWx9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+4oCiPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC14cyBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICB1c2VyPy5wbGFuID09PSAnRWxpdGUnID8gJ2JnLXB1cnBsZS01MDAvMjAgdGV4dC1wdXJwbGUtNDAwJyA6XG4gICAgICAgICAgICAgICAgICAgIHVzZXI/LnBsYW4gPT09ICdQcm8nID8gJ2JnLWJsdWUtNTAwLzIwIHRleHQtYmx1ZS00MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgdXNlcj8ucGxhbiA9PT0gJ0N5YmVyc2VjdXJpdHknID8gJ2JnLXJlZC01MDAvMjAgdGV4dC1yZWQtNDAwJyA6XG4gICAgICAgICAgICAgICAgICAgIHVzZXI/LnBsYW4gPT09ICdCdWdodW50ZXInID8gJ2JnLW9yYW5nZS01MDAvMjAgdGV4dC1vcmFuZ2UtNDAwJyA6XG4gICAgICAgICAgICAgICAgICAgICdiZy1ncmF5LTUwMC8yMCB0ZXh0LWdyYXktNDAwJ1xuICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICB7dXNlcj8ucGxhbiB8fCAnRnJlZSd9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj7igKI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj5SYW5rICN7c3RhdHMucmFua308L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj7igKI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8RmxhbWUgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWN5YmVyLXNlY29uZGFyeVwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPntzdGF0cy5zdHJlYWt9IGRheSBzdHJlYWs8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtY3liZXItcHJpbWFyeVwiPlxuICAgICAgICAgICAgICAgIHtzdGF0cy5zY29yZS50b0xvY2FsZVN0cmluZygpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5Ub3RhbCBTY29yZTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtdC0yXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JlZW4tNDAwXCI+K3tzdGF0cy5wb2ludHNUb2RheX0gdG9kYXk8L2Rpdj5cbiAgICAgICAgICAgICAgICA8QXJyb3dVcCBjbGFzc05hbWU9XCJoLTMgdy0zIHRleHQtZ3JlZW4tNDAwXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFN0YXRzIEdyaWQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtMyBsZzpncmlkLWNvbHMtNiBnYXAtNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1jeWJlciB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPFNoaWVsZCBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtY3liZXItcHJpbWFyeSBteC1hdXRvIG1iLTMgYW5pbWF0ZS1jeWJlci1wdWxzZVwiIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTFcIj5cbiAgICAgICAgICAgICAge3N0YXRzLnRvdGFsU2NhbnN9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+VG90YWwgU2NhbnM8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyZWVuLTQwMCBtdC0xXCI+KzEyIHRoaXMgd2VlazwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWN5YmVyIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8QnVnIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1yZWQtNDAwIG14LWF1dG8gbWItMyBhbmltYXRlLWN5YmVyLXB1bHNlXCIgLz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItMVwiPlxuICAgICAgICAgICAgICB7c3RhdHMudnVsbmVyYWJpbGl0aWVzRm91bmR9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+VnVsbmVyYWJpbGl0aWVzPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1yZWQtNDAwIG10LTFcIj4rMyBjcml0aWNhbDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWN5YmVyIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LWN5YmVyLWFjY2VudCBteC1hdXRvIG1iLTMgYW5pbWF0ZS1jeWJlci1wdWxzZVwiIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTFcIj5cbiAgICAgICAgICAgICAge3N0YXRzLmZpbGVzQW5hbHl6ZWR9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+RmlsZXMgQW5hbHl6ZWQ8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXllbGxvdy00MDAgbXQtMVwiPjUgbWFsaWNpb3VzPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtY3liZXIgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LWJsdWUtNDAwIG14LWF1dG8gbWItMyBhbmltYXRlLWN5YmVyLXB1bHNlXCIgLz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItMVwiPlxuICAgICAgICAgICAgICB7c3RhdHMub3NpbnRRdWVyaWVzfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPk9TSU5UIFF1ZXJpZXM8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtNDAwIG10LTFcIj4rNDUgdG9kYXk8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1jeWJlciB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPFphcCBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtcHVycGxlLTQwMCBteC1hdXRvIG1iLTMgYW5pbWF0ZS1jeWJlci1wdWxzZVwiIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTFcIj5cbiAgICAgICAgICAgICAge3N0YXRzLmFwaUNhbGxzLnRvTG9jYWxlU3RyaW5nKCl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+QVBJIENhbGxzPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1wdXJwbGUtNDAwIG10LTFcIj45OCUgdXB0aW1lPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtY3liZXIgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1ncmVlbi00MDAgbXgtYXV0byBtYi0zIGFuaW1hdGUtY3liZXItcHVsc2VcIiAvPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi0xXCI+XG4gICAgICAgICAgICAgIHtzdGF0cy5wb2ludHNUaGlzV2Vlay50b0xvY2FsZVN0cmluZygpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPldlZWtseSBQb2ludHM8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyZWVuLTQwMCBtdC0xXCI+KzE1JSB2cyBsYXN0IHdlZWs8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFN5c3RlbSBTdGF0dXMgQWxlcnQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1jeWJlciBib3JkZXItbC00IGJvcmRlci1sLWN5YmVyLXNlY29uZGFyeVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTYgdy02IHRleHQtY3liZXItc2Vjb25kYXJ5IG10LTFcIiAvPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItMVwiPlN5c3RlbSBTdGF0dXMgVXBkYXRlPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBtYi0zXCI+XG4gICAgICAgICAgICAgICAgTmV3IENWRSBkYXRhYmFzZSB1cGRhdGUgYXZhaWxhYmxlLiAxNSBjcml0aWNhbCB2dWxuZXJhYmlsaXRpZXMgYWRkZWQgdG8gb3VyIGludGVsbGlnZW5jZSBmZWVkLlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJidG4tY3liZXItc2Vjb25kYXJ5IHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIFZpZXcgVXBkYXRlc1xuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPjIgbWludXRlcyBhZ288L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBRdWljayBBY3Rpb25zIEdyaWQgKi99XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTZcIj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtY3liZXItZ2xvd1wiPlF1aWNrPC9zcGFuPnsnICd9XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWN5YmVyLXBpbmtcIj5BY3Rpb25zPC9zcGFuPlxuICAgICAgICAgIDwvaDI+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cbiAgICAgICAgICAgIHtxdWlja0FjdGlvbnMubWFwKChhY3Rpb24sIGluZGV4KSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IEljb24gPSBhY3Rpb24uaWNvblxuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjYXJkLWN5YmVyIGhvdmVyOnNjYWxlLTEwNSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgY3Vyc29yLXBvaW50ZXIgZ3JvdXBcIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gd2luZG93LmxvY2F0aW9uLmhyZWYgPSBhY3Rpb24uaHJlZn1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BwLTMgcm91bmRlZC1sZyBiZy0ke2FjdGlvbi5jb2xvcn0vMjBgfT5cbiAgICAgICAgICAgICAgICAgICAgICA8SWNvbiBjbGFzc05hbWU9e2BoLTYgdy02IHRleHQtJHthY3Rpb24uY29sb3J9IGdyb3VwLWhvdmVyOmFuaW1hdGUtY3liZXItcHVsc2VgfSAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWVuZCBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7YWN0aW9uLmJhZGdlICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLWN5YmVyLXNlY29uZGFyeS8yMCB0ZXh0LWN5YmVyLXNlY29uZGFyeSBweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1ib2xkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHthY3Rpb24uYmFkZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICB7YWN0aW9uLnByZW1pdW0gJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmctY3liZXItYWNjZW50LzIwIHRleHQtY3liZXItYWNjZW50IHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LWJvbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgUFJPXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItMiBncm91cC1ob3Zlcjp0ZXh0LWN5YmVyLXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAge2FjdGlvbi50aXRsZX1cbiAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc20gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICB7YWN0aW9uLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtJHthY3Rpb24uY29sb3J9YH0+XG4gICAgICAgICAgICAgICAgICAgICAge2FjdGlvbi5zdGF0c31cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8UGxheSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JheS00MDAgZ3JvdXAtaG92ZXI6dGV4dC1jeWJlci1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApXG4gICAgICAgICAgICB9KX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFBsYW4gVXNhZ2UgKi99XG4gICAgICAgIHtwbGFuVXNhZ2UgJiYgKFxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNlwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWN5YmVyLWdsb3dcIj5QbGFuPC9zcGFuPnsnICd9XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtY3liZXItcGlua1wiPlVzYWdlPC9zcGFuPlxuICAgICAgICAgICAgPC9oMj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1jeWJlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5WdWxuZXJhYmlsaXR5IFNjYW5zPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxTaGllbGQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWN5YmVyLXByaW1hcnlcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPlVzZWQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIj57cGxhblVzYWdlLnVzYWdlLnZ1bG5lcmFiaWxpdHlTY2Fuc30gLyB7cGxhblVzYWdlLmxpbWl0cy5zY2FucyA9PT0gLTEgPyAn4oieJyA6IHBsYW5Vc2FnZS5saW1pdHMuc2NhbnN9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICB7cGxhblVzYWdlLmxpbWl0cy5zY2FucyAhPT0gLTEgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTcwMCByb3VuZGVkLWZ1bGwgaC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctY3liZXItcHJpbWFyeSBoLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7TWF0aC5taW4ocGxhblVzYWdlLnBlcmNlbnRhZ2UudnVsbmVyYWJpbGl0eVNjYW5zLCAxMDApfSVgIH19XG4gICAgICAgICAgICAgICAgICAgICAgPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1jeWJlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5PU0lOVCBRdWVyaWVzPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxFeWUgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWN5YmVyLXNlY29uZGFyeVwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+VXNlZDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZVwiPntwbGFuVXNhZ2UudXNhZ2Uub3NpbnRRdWVyaWVzfSAvIHtwbGFuVXNhZ2UubGltaXRzLm9zaW50ID09PSAtMSA/ICfiiJ4nIDogcGxhblVzYWdlLmxpbWl0cy5vc2ludH08L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIHtwbGFuVXNhZ2UubGltaXRzLm9zaW50ICE9PSAtMSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktNzAwIHJvdW5kZWQtZnVsbCBoLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1jeWJlci1zZWNvbmRhcnkgaC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke01hdGgubWluKHBsYW5Vc2FnZS5wZXJjZW50YWdlLm9zaW50UXVlcmllcywgMTAwKX0lYCB9fVxuICAgICAgICAgICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtY3liZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+RmlsZSBBbmFseXNlczwvaDM+XG4gICAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWN5YmVyLWFjY2VudFwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+VXNlZDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZVwiPntwbGFuVXNhZ2UudXNhZ2UuZmlsZUFuYWx5c2VzfSAvIHtwbGFuVXNhZ2UubGltaXRzLmZpbGVzID09PSAtMSA/ICfiiJ4nIDogcGxhblVzYWdlLmxpbWl0cy5maWxlc308L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIHtwbGFuVXNhZ2UubGltaXRzLmZpbGVzICE9PSAtMSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktNzAwIHJvdW5kZWQtZnVsbCBoLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1jeWJlci1hY2NlbnQgaC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke01hdGgubWluKHBsYW5Vc2FnZS5wZXJjZW50YWdlLmZpbGVBbmFseXNlcywgMTAwKX0lYCB9fVxuICAgICAgICAgICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBBY2hpZXZlbWVudHMgKi99XG4gICAgICAgIHthY2hpZXZlbWVudHMgJiYgYWNoaWV2ZW1lbnRzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNlwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWN5YmVyLWdsb3dcIj5SZWNlbnQ8L3NwYW4+eycgJ31cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1jeWJlci1waW5rXCI+QWNoaWV2ZW1lbnRzPC9zcGFuPlxuICAgICAgICAgICAgPC9oMj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC02XCI+XG4gICAgICAgICAgICAgIHthY2hpZXZlbWVudHMuc2xpY2UoMCwgNCkubWFwKChhY2hpZXZlbWVudCkgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGtleT17YWNoaWV2ZW1lbnQuaWR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BjYXJkLWN5YmVyIHRleHQtY2VudGVyIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgICAgICAgICAgICBhY2hpZXZlbWVudC51bmxvY2tlZFxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1jeWJlci1wcmltYXJ5LzUwIGJnLWN5YmVyLXByaW1hcnkvNSdcbiAgICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS02MDAvNTAgYmctZ3JheS04MDAvNTAgb3BhY2l0eS02MCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy0xNiBoLTE2IG14LWF1dG8gbWItNCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgJHtcbiAgICAgICAgICAgICAgICAgICAgYWNoaWV2ZW1lbnQudW5sb2NrZWRcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1jeWJlci1wcmltYXJ5LzIwIHRleHQtY3liZXItcHJpbWFyeSdcbiAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTcwMCB0ZXh0LWdyYXktNTAwJ1xuICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICB7YWNoaWV2ZW1lbnQuaWNvbiA9PT0gJ3NoaWVsZCcgJiYgPFNoaWVsZCBjbGFzc05hbWU9XCJoLTggdy04XCIgLz59XG4gICAgICAgICAgICAgICAgICAgIHthY2hpZXZlbWVudC5pY29uID09PSAnZXllJyAmJiA8RXllIGNsYXNzTmFtZT1cImgtOCB3LThcIiAvPn1cbiAgICAgICAgICAgICAgICAgICAge2FjaGlldmVtZW50Lmljb24gPT09ICdidWcnICYmIDxCdWcgY2xhc3NOYW1lPVwiaC04IHctOFwiIC8+fVxuICAgICAgICAgICAgICAgICAgICB7YWNoaWV2ZW1lbnQuaWNvbiA9PT0gJ2ZsYW1lJyAmJiA8RmxhbWUgY2xhc3NOYW1lPVwiaC04IHctOFwiIC8+fVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9e2Bmb250LXNlbWlib2xkIG1iLTIgJHthY2hpZXZlbWVudC51bmxvY2tlZCA/ICd0ZXh0LXdoaXRlJyA6ICd0ZXh0LWdyYXktNDAwJ31gfT5cbiAgICAgICAgICAgICAgICAgICAge2FjaGlldmVtZW50LnRpdGxlfVxuICAgICAgICAgICAgICAgICAgPC9oMz5cblxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAge2FjaGlldmVtZW50LmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgYWNoaWV2ZW1lbnQucmFyaXR5ID09PSAnbGVnZW5kYXJ5JyA/ICdiZy1wdXJwbGUtNTAwLzIwIHRleHQtcHVycGxlLTQwMCcgOlxuICAgICAgICAgICAgICAgICAgICBhY2hpZXZlbWVudC5yYXJpdHkgPT09ICdlcGljJyA/ICdiZy1vcmFuZ2UtNTAwLzIwIHRleHQtb3JhbmdlLTQwMCcgOlxuICAgICAgICAgICAgICAgICAgICBhY2hpZXZlbWVudC5yYXJpdHkgPT09ICdyYXJlJyA/ICdiZy1ibHVlLTUwMC8yMCB0ZXh0LWJsdWUtNDAwJyA6XG4gICAgICAgICAgICAgICAgICAgICdiZy1ncmF5LTUwMC8yMCB0ZXh0LWdyYXktNDAwJ1xuICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICB7YWNoaWV2ZW1lbnQucmFyaXR5fVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHthY2hpZXZlbWVudC51bmxvY2tlZCAmJiBhY2hpZXZlbWVudC51bmxvY2tlZEF0ICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIFVubG9ja2VkIHtuZXcgRGF0ZShhY2hpZXZlbWVudC51bmxvY2tlZEF0KS50b0xvY2FsZURhdGVTdHJpbmcoKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBUcmVuZGluZyBWdWxuZXJhYmlsaXRpZXMgKi99XG4gICAgICAgIHt0cmVuZGluZ1Z1bG5zICYmIHRyZW5kaW5nVnVsbnMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi02XCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtY3liZXItZ2xvd1wiPlRyZW5kaW5nPC9zcGFuPnsnICd9XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtY3liZXItcGlua1wiPlZ1bG5lcmFiaWxpdGllczwvc3Bhbj5cbiAgICAgICAgICAgIDwvaDI+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMyBnYXAtNlwiPlxuICAgICAgICAgICAgICB7dHJlbmRpbmdWdWxucy5tYXAoKHZ1bG4pID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17dnVsbi5pZH0gY2xhc3NOYW1lPVwiY2FyZC1jeWJlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi0xXCI+e3Z1bG4uaWR9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj57dnVsbi50aXRsZX08L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LWJvbGQgdXBwZXJjYXNlICR7XG4gICAgICAgICAgICAgICAgICAgICAgdnVsbi5zZXZlcml0eSA9PT0gJ2NyaXRpY2FsJyA/ICdiZy1yZWQtNTAwLzIwIHRleHQtcmVkLTQwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgIHZ1bG4uc2V2ZXJpdHkgPT09ICdoaWdoJyA/ICdiZy1vcmFuZ2UtNTAwLzIwIHRleHQtb3JhbmdlLTQwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgIHZ1bG4uc2V2ZXJpdHkgPT09ICdtZWRpdW0nID8gJ2JnLXllbGxvdy01MDAvMjAgdGV4dC15ZWxsb3ctNDAwJyA6XG4gICAgICAgICAgICAgICAgICAgICAgJ2JnLWdyZWVuLTUwMC8yMCB0ZXh0LWdyZWVuLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgIHt2dWxuLnNldmVyaXR5fVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS0zMDAgbWItNFwiPnt2dWxuLmRlc2NyaXB0aW9ufTwvcD5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICBDVlNTOiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtbWVkaXVtXCI+e3Z1bG4uc2NvcmV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgQWZmZWN0ZWQ6IDxzcGFuIGNsYXNzTmFtZT1cInRleHQtY3liZXItcHJpbWFyeSBmb250LW1lZGl1bVwiPnt2dWxuLmFmZmVjdGVkU3lzdGVtcy50b0xvY2FsZVN0cmluZygpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogUmVjZW50IEFjdGl2aXR5ICovfVxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi02XCI+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWN5YmVyLWdsb3dcIj5SZWNlbnQ8L3NwYW4+eycgJ31cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtY3liZXItcGlua1wiPkFjdGl2aXR5PC9zcGFuPlxuICAgICAgICAgIDwvaDI+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtY3liZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIHtyZWNlbnRBY3Rpdml0eS5tYXAoKGFjdGl2aXR5KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgSWNvbiA9IGdldEFjdGl2aXR5SWNvbihhY3Rpdml0eS50eXBlKVxuICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT17YWN0aXZpdHkuaWR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTQgcm91bmRlZC1sZyBiZy1jeWJlci1zZWNvbmRhcnkvNSBob3ZlcjpiZy1jeWJlci1wcmltYXJ5LzEwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgcm91bmRlZC1sZyBiZy1jeWJlci1wcmltYXJ5LzIwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8SWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtY3liZXItcHJpbWFyeVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtd2hpdGVcIj57YWN0aXZpdHkudGFyZ2V0fTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14cyBmb250LWJvbGQgdXBwZXJjYXNlICR7Z2V0U3RhdHVzQ29sb3IoYWN0aXZpdHkuc3RhdHVzKX1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YWN0aXZpdHkuc3RhdHVzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHthY3Rpdml0eS5zZXZlcml0eSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgcHgtMiBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtYm9sZCB1cHBlcmNhc2UgJHtnZXRTZXZlcml0eUNvbG9yKGFjdGl2aXR5LnNldmVyaXR5KX1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHthY3Rpdml0eS5zZXZlcml0eX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbVwiPnthY3Rpdml0eS5yZXN1bHR9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPnthY3Rpdml0eS50aW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIGNhcGl0YWxpemVcIj57YWN0aXZpdHkudHlwZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInRleHQtY3liZXItcHJpbWFyeSBob3Zlcjp0ZXh0LWN5YmVyLXNlY29uZGFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgIFZpZXcgQWxsIEFjdGl2aXR5IOKGklxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvRGFzaGJvYXJkTGF5b3V0PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJEYXNoYm9hcmRMYXlvdXQiLCJTaGllbGQiLCJTZWFyY2giLCJGaWxlVGV4dCIsIkJ1ZyIsIlphcCIsIkFjdGl2aXR5IiwiR2xvYmUiLCJEYXRhYmFzZSIsIlRyZW5kaW5nVXAiLCJFeWUiLCJGbGFtZSIsIkFsZXJ0Q2lyY2xlIiwiQXJyb3dVcCIsIlBsYXkiLCJEYXNoYm9hcmRQYWdlIiwic3RhdHMiLCJzZXRTdGF0cyIsInRvdGFsU2NhbnMiLCJ2dWxuZXJhYmlsaXRpZXNGb3VuZCIsImZpbGVzQW5hbHl6ZWQiLCJvc2ludFF1ZXJpZXMiLCJhcGlDYWxscyIsInNjb3JlIiwibGV2ZWwiLCJyYW5rIiwic3RyZWFrIiwicG9pbnRzVG9kYXkiLCJwb2ludHNUaGlzV2VlayIsInBvaW50c1RoaXNNb250aCIsInJlY2VudEFjdGl2aXR5Iiwic2V0UmVjZW50QWN0aXZpdHkiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImN1cnJlbnRUaW1lIiwic2V0Q3VycmVudFRpbWUiLCJEYXRlIiwidXNlciIsInNldFVzZXIiLCJhY2hpZXZlbWVudHMiLCJzZXRBY2hpZXZlbWVudHMiLCJwbGFuVXNhZ2UiLCJzZXRQbGFuVXNhZ2UiLCJ0cmVuZGluZ1Z1bG5zIiwic2V0VHJlbmRpbmdWdWxucyIsInRpbWVyIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwibG9hZERhc2hib2FyZERhdGEiLCJyZXNwb25zZSIsImZldGNoIiwiZGF0YSIsImpzb24iLCJzdWNjZXNzIiwiZmlsZUFuYWx5c2VzIiwiY3ZlU2VhcmNoZXMiLCJkb3JraW5nUXVlcmllcyIsIk1hdGgiLCJmbG9vciIsInRyYW5zZm9ybWVkQWN0aXZpdGllcyIsInJlY2VudEFjdGl2aXRpZXMiLCJtYXAiLCJhY3Rpdml0eSIsImlkIiwidG9TdHJpbmciLCJ0eXBlIiwidGFyZ2V0IiwiZGVzY3JpcHRpb24iLCJpbmNsdWRlcyIsInRpbWUiLCJ0aW1lc3RhbXAiLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJzdGF0dXMiLCJzZXZlcml0eSIsInJlc3VsdCIsInVzYWdlIiwidHJlbmRpbmdWdWxuZXJhYmlsaXRpZXMiLCJlcnJvciIsImNvbnNvbGUiLCJxdWlja0FjdGlvbnMiLCJ0aXRsZSIsImljb24iLCJocmVmIiwiYmFkZ2UiLCJjb2xvciIsInByZW1pdW0iLCJnZXRBY3Rpdml0eUljb24iLCJnZXRTdGF0dXNDb2xvciIsImdldFNldmVyaXR5Q29sb3IiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInNwYW4iLCJwIiwidXNlcm5hbWUiLCJidXR0b24iLCJzdWJzdHJpbmciLCJ0b1VwcGVyQ2FzZSIsImgyIiwiZnVsbE5hbWUiLCJwbGFuIiwidG9Mb2NhbGVTdHJpbmciLCJoMyIsImFjdGlvbiIsImluZGV4IiwiSWNvbiIsIm9uQ2xpY2siLCJ3aW5kb3ciLCJsb2NhdGlvbiIsInZ1bG5lcmFiaWxpdHlTY2FucyIsImxpbWl0cyIsInNjYW5zIiwic3R5bGUiLCJ3aWR0aCIsIm1pbiIsInBlcmNlbnRhZ2UiLCJvc2ludCIsImZpbGVzIiwibGVuZ3RoIiwic2xpY2UiLCJhY2hpZXZlbWVudCIsInVubG9ja2VkIiwicmFyaXR5IiwidW5sb2NrZWRBdCIsInRvTG9jYWxlRGF0ZVN0cmluZyIsInZ1bG4iLCJhZmZlY3RlZFN5c3RlbXMiLCJoNCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});