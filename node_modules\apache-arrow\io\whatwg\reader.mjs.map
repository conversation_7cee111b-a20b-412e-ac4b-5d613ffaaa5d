{"version": 3, "sources": ["io/whatwg/reader.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;AAIrB,OAAO,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAC;AACpD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAExD,cAAc;AACd,MAAM,UAAU,iCAAiC,CAA0B,gBAA4C,EAAE,gBAA2C;IAEhK,MAAM,KAAK,GAAG,IAAI,cAAc,EAAE,CAAC;IACnC,IAAI,MAAM,GAAgC,IAAI,CAAC;IAE/C,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAiB;QAC1C,MAAM;kEAAK,MAAM,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAAA;QACjC,KAAK,CAAC,UAAU;kEAAI,MAAM,IAAI,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;SAAA;QAChF,IAAI,CAAC,UAAU;kEAAI,MAAM,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAAA;KAC3F,CAAC,CAAC;IAEH,OAAO,EAAE,QAAQ,EAAE,IAAI,cAAc,CAAC,KAAK,kBAAI,eAAe,EAAE,SAAA,CAAC,EAAI,EAAE,CAAA,IAAK,gBAAgB,EAAG,EAAE,QAAQ,EAAE,CAAC;IAE5G,SAAe,IAAI;;YACf,OAAO,MAAM,CAAC,MAAM,iBAAiB,CAAC,IAAI,CAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACjF,CAAC;KAAA;IAED,SAAe,IAAI,CAAC,UAA2D,EAAE,MAA4B;;YACzG,IAAI,IAAI,GAAG,UAAU,CAAC,WAAW,CAAC;YAClC,IAAI,CAAC,GAA0C,IAAI,CAAC;YACpD,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;gBACrC,UAAU,CAAC,OAAO,CAAC,CAAE,CAAC,KAAK,CAAC,CAAC;gBAC7B,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC;oBAC9B,OAAO;gBACX,CAAC;YACL,CAAC;YACD,UAAU,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;KAAA;AACL,CAAC", "file": "reader.mjs", "sourceRoot": "../../src"}