#!/usr/bin/env ts-node

import { runMigrations, runMigrationFile } from '../lib/database/run-migrations'
import { testDatabaseConnection, closeDatabaseConnections } from '../lib/database'
import { join } from 'path'

async function main() {
  try {
    // Test database connection
    const connected = await testDatabaseConnection()
    if (!connected) {
      console.error('❌ Database connection failed')
      process.exit(1)
    }

    // Get command line arguments
    const args = process.argv.slice(2)
    const command = args[0]
    
    if (command === 'run') {
      // Run all migrations
      const success = await runMigrations()
      if (!success) {
        console.error('❌ Migrations failed')
        process.exit(1)
      }
    } else if (command === 'file' && args[1]) {
      // Run a specific migration file
      const filePath = args[1]
      const fullPath = filePath.startsWith('/') 
        ? filePath 
        : join(process.cwd(), 'lib', 'database', 'migrations', filePath)
      
      const success = await runMigrationFile(fullPath)
      if (!success) {
        console.error(`❌ Migration file failed: ${filePath}`)
        process.exit(1)
      }
    } else {
      // Show usage
      console.log(`
Usage:
  npm run migrate run                 - Run all pending migrations
  npm run migrate file <filename>     - Run a specific migration file
      `)
      process.exit(1)
    }

    console.log('✅ Migration process completed successfully')
  } catch (error) {
    console.error('❌ Migration process failed:', error)
    process.exit(1)
  } finally {
    // Close database connections
    await closeDatabaseConnections()
  }
}

main()
