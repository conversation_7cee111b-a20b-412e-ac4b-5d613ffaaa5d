'use client'

import { useState, useEffect } from 'react'
import AdminLayout from '@/components/AdminLayout'
import { Card, StatsCard } from '@/components/Card'
import { Loading } from '@/components/Loading'
import { useToast } from '@/components/Toast'
import { 
  Activity,
  Server,
  Database,
  Cpu,
  HardDrive,
  Wifi,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Clock,
  Users,
  Zap,
  Globe,
  Shield,
  Eye,
  Settings
} from 'lucide-react'

interface SystemStats {
  cpu: {
    usage: number
    cores: number
    temperature?: number
  }
  memory: {
    used: number
    total: number
    percentage: number
  }
  disk: {
    used: number
    total: number
    percentage: number
  }
  network: {
    inbound: number
    outbound: number
  }
  uptime: number
  loadAverage: number[]
}

interface ServiceStatus {
  name: string
  status: 'running' | 'stopped' | 'error'
  uptime: number
  lastCheck: string
  responseTime?: number
  errorCount: number
}

interface DatabaseStats {
  connections: {
    active: number
    max: number
  }
  queries: {
    total: number
    slow: number
    failed: number
  }
  size: number
  tables: number
}

export default function AdminMonitoringPage() {
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null)
  const [services, setServices] = useState<ServiceStatus[]>([])
  const [databaseStats, setDatabaseStats] = useState<DatabaseStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [autoRefresh, setAutoRefresh] = useState(true)
  const { showToast } = useToast()

  useEffect(() => {
    loadMonitoringData()
    
    let interval: NodeJS.Timeout
    if (autoRefresh) {
      interval = setInterval(loadMonitoringData, 30000) // Refresh every 30 seconds
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [autoRefresh])

  const loadMonitoringData = async () => {
    try {
      // Load system stats
      const systemResponse = await fetch('/api/admin/monitoring/system')
      if (systemResponse.ok) {
        const systemData = await systemResponse.json()
        if (systemData.success) {
          setSystemStats(systemData.data)
        }
      }

      // Load service status
      const servicesResponse = await fetch('/api/admin/monitoring/services')
      if (servicesResponse.ok) {
        const servicesData = await servicesResponse.json()
        if (servicesData.success) {
          setServices(servicesData.data.services)
        }
      }

      // Load database stats
      const dbResponse = await fetch('/api/admin/monitoring/database')
      if (dbResponse.ok) {
        const dbData = await dbResponse.json()
        if (dbData.success) {
          setDatabaseStats(dbData.data)
        }
      }
    } catch (error) {
      console.error('Failed to load monitoring data:', error)
      showToast('Failed to load monitoring data', 'error')
    } finally {
      setLoading(false)
    }
  }

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else {
      return `${minutes}m`
    }
  }

  const formatBytes = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'stopped':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default:
        return <XCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'text-green-500'
      case 'stopped':
        return 'text-red-500'
      case 'error':
        return 'text-yellow-500'
      default:
        return 'text-gray-500'
    }
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500'
    if (percentage >= 75) return 'bg-yellow-500'
    if (percentage >= 50) return 'bg-blue-500'
    return 'bg-green-500'
  }

  if (loading) {
    return (
      <AdminLayout>
        <Loading />
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">System Monitoring</h1>
            <p className="text-gray-600">Monitor system performance and service health</p>
          </div>
          <div className="flex items-center space-x-3">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-gray-700">Auto Refresh</span>
            </label>
            <button
              onClick={loadMonitoringData}
              className="btn-primary flex items-center space-x-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {/* System Overview */}
        {systemStats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatsCard
              title="CPU Usage"
              value={`${systemStats.cpu.usage.toFixed(1)}%`}
              icon={Cpu}
              color={systemStats.cpu.usage > 80 ? 'red' : systemStats.cpu.usage > 60 ? 'yellow' : 'green'}
            />
            <StatsCard
              title="Memory Usage"
              value={`${systemStats.memory.percentage.toFixed(1)}%`}
              icon={HardDrive}
              color={systemStats.memory.percentage > 80 ? 'red' : systemStats.memory.percentage > 60 ? 'yellow' : 'green'}
            />
            <StatsCard
              title="Disk Usage"
              value={`${systemStats.disk.percentage.toFixed(1)}%`}
              icon={Database}
              color={systemStats.disk.percentage > 80 ? 'red' : systemStats.disk.percentage > 60 ? 'yellow' : 'green'}
            />
            <StatsCard
              title="Uptime"
              value={formatUptime(systemStats.uptime)}
              icon={Clock}
              color="blue"
            />
          </div>
        )}

        {/* Detailed System Stats */}
        {systemStats && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">System Resources</h3>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">CPU Usage</span>
                    <span className="text-sm text-gray-500">{systemStats.cpu.usage.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${getUsageColor(systemStats.cpu.usage)}`}
                      style={{ width: `${systemStats.cpu.usage}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {systemStats.cpu.cores} cores
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">Memory</span>
                    <span className="text-sm text-gray-500">
                      {formatBytes(systemStats.memory.used)} / {formatBytes(systemStats.memory.total)}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${getUsageColor(systemStats.memory.percentage)}`}
                      style={{ width: `${systemStats.memory.percentage}%` }}
                    ></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">Disk Space</span>
                    <span className="text-sm text-gray-500">
                      {formatBytes(systemStats.disk.used)} / {formatBytes(systemStats.disk.total)}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${getUsageColor(systemStats.disk.percentage)}`}
                      style={{ width: `${systemStats.disk.percentage}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Network Activity</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <TrendingDown className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium text-gray-700">Inbound</span>
                  </div>
                  <span className="text-sm text-gray-500">{formatBytes(systemStats.network.inbound)}/s</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium text-gray-700">Outbound</span>
                  </div>
                  <span className="text-sm text-gray-500">{formatBytes(systemStats.network.outbound)}/s</span>
                </div>
                
                <div className="pt-4 border-t">
                  <div className="text-sm text-gray-700 mb-2">Load Average</div>
                  <div className="flex space-x-4">
                    <div className="text-center">
                      <div className="text-lg font-semibold">{systemStats.loadAverage[0]?.toFixed(2) || '0.00'}</div>
                      <div className="text-xs text-gray-500">1m</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold">{systemStats.loadAverage[1]?.toFixed(2) || '0.00'}</div>
                      <div className="text-xs text-gray-500">5m</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold">{systemStats.loadAverage[2]?.toFixed(2) || '0.00'}</div>
                      <div className="text-xs text-gray-500">15m</div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Services Status */}
        <Card>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {services.map((service, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{service.name}</h4>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(service.status)}
                    <span className={`text-sm capitalize ${getStatusColor(service.status)}`}>
                      {service.status}
                    </span>
                  </div>
                </div>
                
                <div className="space-y-1 text-sm text-gray-600">
                  <div>Uptime: {formatUptime(service.uptime)}</div>
                  {service.responseTime && (
                    <div>Response: {service.responseTime}ms</div>
                  )}
                  <div>Errors: {service.errorCount}</div>
                  <div>Last Check: {new Date(service.lastCheck).toLocaleTimeString()}</div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Database Stats */}
        {databaseStats && (
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Database Statistics</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {databaseStats.connections.active}/{databaseStats.connections.max}
                </div>
                <div className="text-sm text-gray-600">Active Connections</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {databaseStats.queries.total.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600">Total Queries</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {databaseStats.queries.slow}
                </div>
                <div className="text-sm text-gray-600">Slow Queries</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {formatBytes(databaseStats.size)}
                </div>
                <div className="text-sm text-gray-600">Database Size</div>
              </div>
            </div>
          </Card>
        )}
      </div>
    </AdminLayout>
  )
}
