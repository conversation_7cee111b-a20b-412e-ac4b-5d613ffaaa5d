{"version": 3, "sources": ["visitor/typector.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,KAAK,IAAI,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAEhD,cAAc;AACd,MAAM,WAAW,sBAAuB,SAAQ,OAAO;IACnD,KAAK,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IAChD,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;IACzD,UAAU,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,GAAG,MAAM,YAAY,CAAC,CAAC,CAAC,CAAC;IAC3D,UAAU,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,YAAY,CAAC,CAAC,CAAC,CAAC;CACxF;AAED,cAAc;AACd,qBAAa,sBAAuB,SAAQ,OAAO;IACxC,SAAS;IACT,SAAS;IACT,QAAQ;IACR,SAAS;IACT,UAAU;IACV,UAAU;IACV,UAAU;IACV,UAAU;IACV,WAAW;IACX,WAAW;IACX,WAAW;IACX,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,SAAS;IACT,cAAc;IACd,WAAW;IACX,gBAAgB;IAChB,oBAAoB;IACpB,SAAS;IACT,YAAY;IACZ,oBAAoB;IACpB,cAAc;IACd,oBAAoB;IACpB,yBAAyB;IACzB,yBAAyB;IACzB,wBAAwB;IACxB,SAAS;IACT,eAAe;IACf,oBAAoB;IACpB,oBAAoB;IACpB,mBAAmB;IACnB,YAAY;IACZ,SAAS;IACT,WAAW;IACX,UAAU;IACV,eAAe;IACf,gBAAgB;IAChB,eAAe;IACf,aAAa;IACb,oBAAoB;IACpB,sBAAsB;IACtB,aAAa;IACb,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,uBAAuB;IACvB,kBAAkB;IAClB,QAAQ;CAClB;AAED,cAAc;AACd,eAAO,MAAM,QAAQ,wBAA+B,CAAC", "file": "typector.d.ts", "sourceRoot": "../src"}