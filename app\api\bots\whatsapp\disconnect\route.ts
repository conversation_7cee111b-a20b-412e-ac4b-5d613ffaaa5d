'use server'

import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { WhatsAppBotService } from '@/lib/services/whatsapp-bot'
import { z } from 'zod'

const disconnectSchema = z.object({
  sessionId: z.string().min(1)
})

// Global bot service instance
let botService: WhatsAppBotService | null = null

function getBotService(): WhatsAppBotService {
  if (!botService) {
    botService = new WhatsAppBotService()
  }
  return botService
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate request - only admins can disconnect bots
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const validatedData = disconnectSchema.parse(body)

    // Get bot service
    const service = getBotService()

    // Disconnect WhatsApp session
    const success = await service.disconnectSession(validatedData.sessionId)

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'WhatsApp bot disconnected successfully',
        data: {
          sessionId: validatedData.sessionId
        }
      })
    } else {
      return NextResponse.json(
        { success: false, message: 'Failed to disconnect WhatsApp bot' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('WhatsApp bot disconnection error:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to disconnect WhatsApp bot',
        error: error instanceof z.ZodError ? error.errors : undefined
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    endpoint: '/api/bots/whatsapp/disconnect',
    method: 'POST',
    description: 'Disconnect a WhatsApp bot session',
    parameters: {
      sessionId: 'string (required) - ID of the session to disconnect'
    },
    example: {
      sessionId: 'wa_kodexguard-wa_1234567890'
    }
  })
}
