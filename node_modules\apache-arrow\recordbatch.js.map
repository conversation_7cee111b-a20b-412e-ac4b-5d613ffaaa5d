{"version": 3, "sources": ["recordbatch.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;;AAErB,uCAA2C;AAC3C,yCAAmC;AACnC,2CAAqC;AACrC,2CAA4C;AAC5C,uCAA4D;AAC5D,gDAA6C;AAE7C,6CAA0D;AAC1D,6CAA0D;AAC1D,qDAAkE;AAClE,uDAAoE;AAkBpE,cAAc;AACd,MAAa,WAAW;IAIpB,YAAY,GAAG,IAAW;QACtB,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YAClB,KAAK,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,YAAY,kBAAM,CAAC,EAAE,CAAC;oBACnC,MAAM,IAAI,SAAS,CAAC,wDAAwD,CAAC,CAAC;gBAClF,CAAC;gBACD,CAAC;oBACG,IAAI,CAAC,IAAI,GAAG,IAAA,kBAAQ,EAAC;wBACjB,SAAS,EAAE,CAAC;wBACZ,IAAI,EAAE,IAAI,gBAAM,CAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;wBACvC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;qBACpF,CAAC;iBACL,GAAG,IAAI,CAAC;gBACT,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,YAAY,cAAI,CAAC,EAAE,CAAC;oBAC/B,MAAM,IAAI,SAAS,CAAC,wDAAwD,CAAC,CAAC;gBAClF,CAAC;gBACD,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAI,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,QAA8B,CAAC,CAAC;gBAC1G,MAAM;YACV,CAAC;YACD,KAAK,CAAC,CAAC,CAAC,CAAC;gBACL,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;gBACnB,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE;oBAC3E,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;oBAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC;oBACtD,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,iBAAK,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;oBAC3E,OAAO,IAAI,CAAC;gBAChB,CAAC,EAAE;oBACC,MAAM,EAAE,CAAC;oBACT,MAAM,EAAE,IAAI,KAAK,EAAqB;oBACtC,QAAQ,EAAE,IAAI,KAAK,EAAoB;iBAC1C,CAAC,CAAC;gBAEH,MAAM,MAAM,GAAG,IAAI,kBAAM,CAAI,MAAM,CAAC,CAAC;gBACrC,MAAM,IAAI,GAAG,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,gBAAM,CAAI,MAAM,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;gBACvF,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAI,MAAM,EAAE,IAAI,CAAC,QAA8B,EAAE,MAAM,CAAC,CAAC;gBACxG,MAAM;YACV,CAAC;YACD,OAAO,CAAC,CAAC,MAAM,IAAI,SAAS,CAAC,kGAAkG,CAAC,CAAC;QACrI,CAAC;IACL,CAAC;IAOD,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACpH,CAAC;IAED;;OAEG;IACH,IAAW,OAAO,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IAE1D;;OAEG;IACH,IAAW,OAAO,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAEjD;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,KAAa;QACxB,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,GAAG,CAAC,KAAa;QACpB,OAAO,iBAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED;;;QAGI;IACG,EAAE,CAAC,KAAa;QACnB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAA,qBAAS,EAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;;;OAIG;IACI,GAAG,CAAC,KAAa,EAAE,KAA0B;QAChD,OAAO,iBAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;IAED;;;;OAIG;IACI,OAAO,CAAC,OAA4B,EAAE,MAAe;QACxD,OAAO,qBAAc,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpB,OAAO,sBAAe,CAAC,KAAK,CAAC,IAAI,kBAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAA0C,CAAC;IACnG,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;IACrB,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,GAAG,MAAwB;QACrC,OAAO,IAAI,gBAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,KAAc,EAAE,GAAY;QACrC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,kBAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC;QAC/D,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACI,QAAQ,CAAoB,IAAO;;QACtC,OAAO,IAAI,CAAC,UAAU,CAAO,MAAA,IAAI,CAAC,MAAM,CAAC,MAAM,0CAAE,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC;IACxF,CAAC;IAED;;;OAGG;IACI,UAAU,CAA6B,KAAa;QACvD,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YAClD,OAAO,IAAI,kBAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAc,CAAC;QAChE,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAwC,IAAO,EAAE,KAAgB;;QAC5E,OAAO,IAAI,CAAC,UAAU,CAAC,MAAA,IAAI,CAAC,MAAM,CAAC,MAAM,0CAAE,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,KAAK,CAAqC,CAAC;IAC7H,CAAC;IASM,UAAU,CAAC,KAAa,EAAE,KAAU;QACvC,IAAI,MAAM,GAAW,IAAI,CAAC,MAAM,CAAC;QACjC,IAAI,IAAI,GAAiB,IAAI,CAAC,IAAI,CAAC;QACnC,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YACrC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,KAAK,GAAG,IAAI,kBAAM,CAAC,CAAC,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,cAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;YAC7E,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,EAAkB,CAAC;YACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAiB,CAAC;YACtD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YACxD,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,MAAM,GAAG,IAAI,kBAAM,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC3D,IAAI,GAAG,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,gBAAM,CAAI,MAAM,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC/D,CAAC;QACD,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACzC,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAA0B,WAAgB;QACnD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC/C,MAAM,IAAI,GAAG,IAAI,gBAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACvC,MAAM,QAAQ,GAAG,EAAkB,CAAC;QACpC,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;YACnE,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAe,CAAC;YAC9D,CAAC;QACL,CAAC;QACD,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IACvF,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAoB,aAAuB;QACtD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAI,aAAa,CAAC,CAAC;QACtD,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACjF,MAAM,MAAM,GAAG,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,gBAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC7F,OAAO,IAAI,WAAW,CAA2B,MAAM,EAAE,MAAM,CAAC,CAAC;IACrE,CAAC;;AArOL,kCA8OC;KALqB,MAAM,CAAC,WAAW;AAFpC,2EAA2E;AAC3E,gFAAgF;AAC/D,eAAoB,GAAG,CAAC,CAAC,KAAkB,EAAE,EAAE;IAC3D,KAAa,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IAC9B,KAAa,CAAC,MAAM,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC;IACjD,OAAO,aAAa,CAAC;AACzB,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAI9B,cAAc;AACd,SAAS,oBAAoB,CACzB,MAAiB,EACjB,MAA0B,EAC1B,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;;IAErE,MAAM,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;IAClC,MAAM,QAAQ,GAAG,CAAC,GAAG,MAAM,CAAuB,CAAC;IACnD,MAAM,cAAc,GAAG,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAErD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;QACjD,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACvC,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9C,QAAQ,CAAC,GAAG,CAAC,GAAG,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,kCAAkC,CAAC,SAAS,CAAC,mCAAI,IAAA,kBAAQ,EAAC;gBAC7E,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,IAAI,UAAU,CAAC,cAAc,CAAC;aAC7C,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED,OAAO;QACH,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;QACrB,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,gBAAM,CAAI,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC;KACzC,CAAC;AACtC,CAAC;AAED,cAAc;AACd,SAAS,mBAAmB,CAAC,MAAe,EAAE,QAAyB,EAAE,eAAe,IAAI,GAAG,EAAkB;;IAC7G,IAAI,CAAC,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,mCAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,OAAK,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,MAAM,CAAA,CAAC,EAAE,CAAC;QACrE,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;YAC3C,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzB,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,0CAAE,IAAI,KAAI,EAAE,CAAC,CAAC,EAAE,CAAC;gBAC3D,mBAAmB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;YACrE,CAAC;YACD,IAAI,kBAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;gBACpB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;oBACxB,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,EAAE,CAAC;wBACnB,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC1C,CAAC;gBACL,CAAC;qBAAM,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC;oBAClD,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;gBACnG,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IACD,OAAO,YAAY,CAAC;AACxB,CAAC;AAED;;;;;;;GAOG;AACH,MAAa,oCAA8D,SAAQ,WAAc;IAC7F,YAAY,MAAiB;QACzB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACtE,MAAM,IAAI,GAAG,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,gBAAM,CAAI,MAAM,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;QACtF,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACxB,CAAC;CACJ;AAND,oFAMC", "file": "recordbatch.js", "sourceRoot": "src"}