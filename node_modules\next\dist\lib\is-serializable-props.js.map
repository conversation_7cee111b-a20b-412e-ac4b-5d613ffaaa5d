{"version": 3, "sources": ["../../src/lib/is-serializable-props.ts"], "names": ["SerializableError", "isSerializableProps", "regexpPlainIdentifier", "Error", "constructor", "page", "method", "path", "message", "input", "isPlainObject", "getObjectClassLabel", "visit", "visited", "value", "has", "get", "set", "isSerializable", "refs", "type", "Object", "entries", "every", "key", "nestedV<PERSON>ue", "nextPath", "test", "JSON", "stringify", "newRefs", "Map", "Array", "isArray", "index", "prototype", "toString", "call"], "mappings": ";;;;;;;;;;;;;;;IAOaA,iBAAiB;eAAjBA;;IAUGC,mBAAmB;eAAnBA;;;+BAdT;AAEP,MAAMC,wBAAwB;AAEvB,MAAMF,0BAA0BG;IACrCC,YAAYC,IAAY,EAAEC,MAAc,EAAEC,IAAY,EAAEC,OAAe,CAAE;QACvE,KAAK,CACHD,OACI,CAAC,oBAAoB,EAAEA,KAAK,mBAAmB,EAAED,OAAO,OAAO,EAAED,KAAK,YAAY,EAAEG,QAAQ,CAAC,GAC7F,CAAC,wCAAwC,EAAEF,OAAO,OAAO,EAAED,KAAK,YAAY,EAAEG,QAAQ,CAAC;IAE/F;AACF;AAEO,SAASP,oBACdI,IAAY,EACZC,MAAc,EACdG,KAAU;IAEV,IAAI,CAACC,IAAAA,4BAAa,EAACD,QAAQ;QACzB,MAAM,IAAIT,kBACRK,MACAC,QACA,IACA,CAAC,8CAA8C,EAAEA,OAAO,sCAAsC,EAAEK,IAAAA,kCAAmB,EACjHF,OACA,IAAI,CAAC;IAEX;IAEA,SAASG,MAAMC,OAAyB,EAAEC,KAAU,EAAEP,IAAY;QAChE,IAAIM,QAAQE,GAAG,CAACD,QAAQ;YACtB,MAAM,IAAId,kBACRK,MACAC,QACAC,MACA,CAAC,+DAA+D,EAC9DM,QAAQG,GAAG,CAACF,UAAU,SACvB,IAAI,CAAC;QAEV;QAEAD,QAAQI,GAAG,CAACH,OAAOP;IACrB;IAEA,SAASW,eACPC,IAAsB,EACtBL,KAAU,EACVP,IAAY;QAEZ,MAAMa,OAAO,OAAON;QACpB,IACE,iDAAiD;QACjDA,UAAU,QACV,iEAAiE;QACjE,cAAc;QACd,EAAE;QACF,yEAAyE;QACzE,kCAAkC;QAClCM,SAAS,aACTA,SAAS,YACTA,SAAS,UACT;YACA,OAAO;QACT;QAEA,IAAIA,SAAS,aAAa;YACxB,MAAM,IAAIpB,kBACRK,MACAC,QACAC,MACA;QAEJ;QAEA,IAAIG,IAAAA,4BAAa,EAACI,QAAQ;YACxBF,MAAMO,MAAML,OAAOP;YAEnB,IACEc,OAAOC,OAAO,CAACR,OAAOS,KAAK,CAAC,CAAC,CAACC,KAAKC,YAAY;gBAC7C,MAAMC,WAAWxB,sBAAsByB,IAAI,CAACH,OACxC,CAAC,EAAEjB,KAAK,CAAC,EAAEiB,IAAI,CAAC,GAChB,CAAC,EAAEjB,KAAK,CAAC,EAAEqB,KAAKC,SAAS,CAACL,KAAK,CAAC,CAAC;gBAErC,MAAMM,UAAU,IAAIC,IAAIZ;gBACxB,OACED,eAAeY,SAASN,KAAKE,aAC7BR,eAAeY,SAASL,aAAaC;YAEzC,IACA;gBACA,OAAO;YACT;YAEA,MAAM,IAAI1B,kBACRK,MACAC,QACAC,MACA,CAAC,+CAA+C,CAAC;QAErD;QAEA,IAAIyB,MAAMC,OAAO,CAACnB,QAAQ;YACxBF,MAAMO,MAAML,OAAOP;YAEnB,IACEO,MAAMS,KAAK,CAAC,CAACE,aAAaS;gBACxB,MAAMJ,UAAU,IAAIC,IAAIZ;gBACxB,OAAOD,eAAeY,SAASL,aAAa,CAAC,EAAElB,KAAK,CAAC,EAAE2B,MAAM,CAAC,CAAC;YACjE,IACA;gBACA,OAAO;YACT;YAEA,MAAM,IAAIlC,kBACRK,MACAC,QACAC,MACA,CAAC,8CAA8C,CAAC;QAEpD;QAEA,0CAA0C;QAC1C,0DAA0D;QAC1D,MAAM,IAAIP,kBACRK,MACAC,QACAC,MACA,MACEa,OACA,MACCA,CAAAA,SAAS,WACN,CAAC,GAAG,EAAEC,OAAOc,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACvB,OAAO,EAAE,CAAC,GAC/C,EAAC,IACL;IAEN;IAEA,OAAOI,eAAe,IAAIa,OAAOtB,OAAO;AAC1C"}