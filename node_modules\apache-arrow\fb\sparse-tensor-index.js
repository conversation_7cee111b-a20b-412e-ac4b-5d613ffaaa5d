"use strict";
// automatically generated by the FlatBuffers compiler, do not modify
Object.defineProperty(exports, "__esModule", { value: true });
exports.unionListToSparseTensorIndex = exports.unionToSparseTensorIndex = exports.SparseTensorIndex = void 0;
const sparse_matrix_index_csx_js_1 = require("./sparse-matrix-index-csx.js");
const sparse_tensor_index_coo_js_1 = require("./sparse-tensor-index-coo.js");
const sparse_tensor_index_csf_js_1 = require("./sparse-tensor-index-csf.js");
var SparseTensorIndex;
(function (SparseTensorIndex) {
    SparseTensorIndex[SparseTensorIndex["NONE"] = 0] = "NONE";
    SparseTensorIndex[SparseTensorIndex["SparseTensorIndexCOO"] = 1] = "SparseTensorIndexCOO";
    SparseTensorIndex[SparseTensorIndex["SparseMatrixIndexCSX"] = 2] = "SparseMatrixIndexCSX";
    SparseTensorIndex[SparseTensorIndex["SparseTensorIndexCSF"] = 3] = "SparseTensorIndexCSF";
})(SparseTensorIndex || (exports.SparseTensorIndex = SparseTensorIndex = {}));
function unionToSparseTensorIndex(type, accessor) {
    switch (SparseTensorIndex[type]) {
        case 'NONE': return null;
        case 'SparseTensorIndexCOO': return accessor(new sparse_tensor_index_coo_js_1.SparseTensorIndexCOO());
        case 'SparseMatrixIndexCSX': return accessor(new sparse_matrix_index_csx_js_1.SparseMatrixIndexCSX());
        case 'SparseTensorIndexCSF': return accessor(new sparse_tensor_index_csf_js_1.SparseTensorIndexCSF());
        default: return null;
    }
}
exports.unionToSparseTensorIndex = unionToSparseTensorIndex;
function unionListToSparseTensorIndex(type, accessor, index) {
    switch (SparseTensorIndex[type]) {
        case 'NONE': return null;
        case 'SparseTensorIndexCOO': return accessor(index, new sparse_tensor_index_coo_js_1.SparseTensorIndexCOO());
        case 'SparseMatrixIndexCSX': return accessor(index, new sparse_matrix_index_csx_js_1.SparseMatrixIndexCSX());
        case 'SparseTensorIndexCSF': return accessor(index, new sparse_tensor_index_csf_js_1.SparseTensorIndexCSF());
        default: return null;
    }
}
exports.unionListToSparseTensorIndex = unionListToSparseTensorIndex;

//# sourceMappingURL=sparse-tensor-index.js.map
