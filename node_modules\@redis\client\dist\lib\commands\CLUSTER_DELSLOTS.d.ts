import { CommandParser } from '../client/parser';
import { SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Removes hash slots from the current node in a Redis Cluster
     * @param parser - The Redis command parser
     * @param slots - One or more hash slots to be removed
     */
    readonly parseCommand: (this: void, parser: CommandParser, slots: number | Array<number>) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=CLUSTER_DELSLOTS.d.ts.map