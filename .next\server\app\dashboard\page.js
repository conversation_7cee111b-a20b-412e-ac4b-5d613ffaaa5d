/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\")), \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(ssr)/./app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q0tvZGUtWEd1YXJkJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUFnRyIsInNvdXJjZXMiOlsid2VicGFjazovL2RiX2tvZGV4Z3VhcmQvP2U3YjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxVc2Vyc1xcXFxEb3dubG9hZHNcXFxcS29kZS1YR3VhcmRcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccomponents%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Chooks%5C%5Cauth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccomponents%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Chooks%5C%5Cauth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Toast.tsx */ \"(ssr)/./components/Toast.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/ThemeContext.tsx */ \"(ssr)/./contexts/ThemeContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./hooks/auth.tsx */ \"(ssr)/./hooks/auth.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccomponents%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Chooks%5C%5Cauth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(ssr)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DashboardPage() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalScans: 0,\n        vulnerabilitiesFound: 0,\n        filesAnalyzed: 0,\n        osintQueries: 0,\n        apiCalls: 0,\n        score: 0,\n        level: 0,\n        rank: 0,\n        streak: 0,\n        pointsToday: 0,\n        pointsThisWeek: 0,\n        pointsThisMonth: 0\n    });\n    const [recentActivity, setRecentActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update time every minute\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch real dashboard data from API\n            const response = await fetch(\"/api/dashboard/stats\");\n            const data = await response.json();\n            if (data.success) {\n                setStats({\n                    totalScans: data.data.stats.totalScans,\n                    vulnerabilitiesFound: data.data.stats.vulnerabilitiesFound,\n                    filesAnalyzed: data.data.stats.fileAnalyses,\n                    osintQueries: data.data.stats.osintQueries,\n                    apiCalls: data.data.stats.cveSearches + data.data.stats.dorkingQueries,\n                    score: data.data.user.score,\n                    level: data.data.user.level,\n                    rank: data.data.user.rank,\n                    streak: data.data.user.streak,\n                    pointsToday: Math.floor(data.data.user.score * 0.1),\n                    pointsThisWeek: Math.floor(data.data.user.score * 0.3),\n                    pointsThisMonth: Math.floor(data.data.user.score * 0.6)\n                });\n                // Transform recent activities to match interface\n                const transformedActivities = data.data.recentActivities.map((activity)=>({\n                        id: activity.id,\n                        type: activity.type === \"vulnerability_scan\" ? \"scan\" : activity.type === \"osint_query\" ? \"osint\" : activity.type === \"file_analysis\" ? \"file\" : activity.type === \"cve_search\" ? \"cve\" : \"dorking\",\n                        target: activity.description.split(\" \")[activity.description.split(\" \").length - 1] || \"Unknown\",\n                        time: new Date(activity.createdAt).toLocaleString(),\n                        status: activity.status === \"completed\" ? \"completed\" : activity.status === \"failed\" ? \"failed\" : \"pending\",\n                        severity: activity.results?.threatDetected ? \"critical\" : activity.results?.vulnerabilities > 5 ? \"high\" : activity.results?.vulnerabilities > 0 ? \"medium\" : \"low\",\n                        result: activity.results?.vulnerabilities ? `${activity.results.vulnerabilities} vulnerabilities found` : activity.results?.threatDetected ? \"Threat detected\" : activity.results?.resultsFound ? `${activity.results.resultsFound} results found` : \"Completed successfully\"\n                    }));\n                setRecentActivity(transformedActivities);\n            } else {\n                // Fallback to mock data if API fails\n                setStats({\n                    totalScans: 142,\n                    vulnerabilitiesFound: 23,\n                    filesAnalyzed: 89,\n                    osintQueries: 456,\n                    apiCalls: 2847,\n                    score: 8950,\n                    level: 28,\n                    rank: 156,\n                    streak: 12,\n                    pointsToday: 250,\n                    pointsThisWeek: 1450,\n                    pointsThisMonth: 5890\n                });\n                setRecentActivity([\n                    {\n                        id: \"1\",\n                        type: \"scan\",\n                        target: \"example.com\",\n                        time: \"2 minutes ago\",\n                        status: \"completed\",\n                        severity: \"high\",\n                        result: \"3 vulnerabilities found\"\n                    }\n                ]);\n            }\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            setLoading(false);\n        }\n    };\n    const quickActions = [\n        {\n            title: \"OSINT Lookup\",\n            description: \"Investigate emails, phone numbers, and personal information\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"/osint\",\n            badge: \"Popular\",\n            color: \"cyber-primary\",\n            stats: \"456 queries\"\n        },\n        {\n            title: \"Vulnerability Scan\",\n            description: \"Scan websites and applications for security vulnerabilities\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"/scanner\",\n            premium: true,\n            color: \"cyber-secondary\",\n            stats: \"142 scans\"\n        },\n        {\n            title: \"File Analysis\",\n            description: \"Analyze files for malware, webshells, and suspicious content\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"/file-analyzer\",\n            premium: true,\n            color: \"cyber-accent\",\n            stats: \"89 files\"\n        },\n        {\n            title: \"CVE Database\",\n            description: \"Search and explore the latest CVE vulnerabilities\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: \"/cve\",\n            color: \"green-400\",\n            stats: \"50K+ CVEs\"\n        },\n        {\n            title: \"Google Dorking\",\n            description: \"Use advanced search queries to find sensitive information\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: \"/dorking\",\n            color: \"blue-400\",\n            stats: \"Advanced\"\n        },\n        {\n            title: \"API Playground\",\n            description: \"Test and explore our API endpoints\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            href: \"/playground\",\n            color: \"purple-400\",\n            stats: \"2.8K calls\"\n        }\n    ];\n    const getActivityIcon = (type)=>{\n        switch(type){\n            case \"scan\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"osint\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"file\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"cve\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"dorking\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            default:\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"text-green-400\";\n            case \"failed\":\n                return \"text-red-400\";\n            case \"malicious\":\n                return \"text-red-400\";\n            case \"clean\":\n                return \"text-green-400\";\n            case \"pending\":\n                return \"text-yellow-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"critical\":\n                return \"text-red-400 bg-red-400/20\";\n            case \"high\":\n                return \"text-orange-400 bg-orange-400/20\";\n            case \"medium\":\n                return \"text-yellow-400 bg-yellow-400/20\";\n            case \"low\":\n                return \"text-green-400 bg-green-400/20\";\n            default:\n                return \"text-gray-400 bg-gray-400/20\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-cyber-primary font-medium\",\n                            children: \"Loading cyber dashboard...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 282,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 281,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-glow\",\n                                            children: \"Cyber\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-pink\",\n                                            children: \"Command\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg\",\n                                    children: \"Welcome back, Cyber Warrior! Your digital arsenal awaits.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 lg:mt-0 flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Current Time\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-mono text-cyber-primary\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-cyber-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Quick Scan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-2xl font-bold text-black\",\n                                        children: \"CW\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: \"Cyber Warrior\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Level \",\n                                                            stats.level\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Rank #\",\n                                                            stats.rank\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4 text-cyber-secondary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    stats.streak,\n                                                                    \" day streak\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-cyber-primary\",\n                                        children: stats.score.toLocaleString()\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"Total Score\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-green-400\",\n                                                children: [\n                                                    \"+\",\n                                                    stats.pointsToday,\n                                                    \" today\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-3 w-3 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-8 w-8 text-cyber-primary mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.totalScans\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Total Scans\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-green-400 mt-1\",\n                                    children: \"+12 this week\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-8 w-8 text-red-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.vulnerabilitiesFound\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Vulnerabilities\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-red-400 mt-1\",\n                                    children: \"+3 critical\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-8 w-8 text-cyber-accent mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.filesAnalyzed\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Files Analyzed\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-yellow-400 mt-1\",\n                                    children: \"5 malicious\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-8 w-8 text-blue-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.osintQueries\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"OSINT Queries\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-blue-400 mt-1\",\n                                    children: \"+45 today\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.apiCalls.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"API Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-purple-400 mt-1\",\n                                    children: \"98% uptime\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.pointsThisWeek.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Weekly Points\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-green-400 mt-1\",\n                                    children: \"+15% vs last week\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber border-l-4 border-l-cyber-secondary\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-6 w-6 text-cyber-secondary mt-1\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold text-white mb-1\",\n                                        children: \"System Status Update\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-3\",\n                                        children: \"New CVE database update available. 15 critical vulnerabilities added to our intelligence feed.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-cyber-secondary text-sm\",\n                                                children: \"View Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"2 minutes ago\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 414,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Quick\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: quickActions.map((action, index)=>{\n                                const Icon = action.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber hover:scale-105 transition-all duration-300 cursor-pointer group\",\n                                    onClick: ()=>window.location.href = action.href,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `p-3 rounded-lg bg-${action.color}/20`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: `h-6 w-6 text-${action.color} group-hover:animate-cyber-pulse`\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-end space-y-1\",\n                                                    children: [\n                                                        action.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-cyber-secondary/20 text-cyber-secondary px-2 py-1 rounded-full text-xs font-bold\",\n                                                            children: action.badge\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        action.premium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-cyber-accent/20 text-cyber-accent px-2 py-1 rounded-full text-xs font-bold\",\n                                                            children: \"PRO\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-2 group-hover:text-cyber-primary transition-colors\",\n                                            children: action.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm mb-4\",\n                                            children: action.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-xs font-medium text-${action.color}`,\n                                                    children: action.stats\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400 group-hover:text-cyber-primary transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Recent\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Activity\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: recentActivity.map((activity)=>{\n                                        const Icon = getActivityIcon(activity.type);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5 hover:bg-cyber-primary/10 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-cyber-primary/20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"h-5 w-5 text-cyber-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium text-white\",\n                                                                            children: activity.target\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: `px-2 py-1 rounded-full text-xs font-bold uppercase ${getStatusColor(activity.status)}`,\n                                                                            children: activity.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 509,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        activity.severity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: `px-2 py-1 rounded-full text-xs font-bold uppercase ${getSeverityColor(activity.severity)}`,\n                                                                            children: activity.severity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 513,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-sm\",\n                                                                    children: activity.result\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: activity.time\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 capitalize\",\n                                                            children: activity.type\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, activity.id, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-cyber-primary hover:text-cyber-secondary transition-colors\",\n                                        children: \"View All Activity →\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 486,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 294,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 293,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/DashboardLayout.tsx":
/*!****************************************!*\
  !*** ./components/DashboardLayout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DashboardLayout({ children }) {\n    const [isSidebarOpen, setIsSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"online\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Update time every minute\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    // Load user and check auth\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            try {\n                const token = localStorage.getItem(\"token\");\n                const storedUser = localStorage.getItem(\"user\");\n                if (!token || !storedUser) {\n                    router.push(\"/login\");\n                    return;\n                }\n                // Mock user data for now\n                const userData = {\n                    id: \"1\",\n                    username: \"CyberWarrior\",\n                    email: \"<EMAIL>\",\n                    fullName: \"Alex Chen\",\n                    role: \"admin\",\n                    plan: \"Expert\",\n                    level: 28,\n                    score: 8950,\n                    streak: 12\n                };\n                setUser(userData);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Auth check error:\", error);\n                router.push(\"/login\");\n            }\n        };\n        checkAuth();\n    }, [\n        router\n    ]);\n    const handleLogout = async ()=>{\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"refreshToken\");\n        localStorage.removeItem(\"user\");\n        router.push(\"/login\");\n    };\n    const toggleSidebar = ()=>{\n        setIsSidebarOpen(!isSidebarOpen);\n    };\n    const toggleExpanded = (itemName)=>{\n        setExpandedItems((prev)=>prev.includes(itemName) ? prev.filter((name)=>name !== itemName) : [\n                ...prev,\n                itemName\n            ]);\n    };\n    const isActive = (href)=>{\n        if (href === \"/dashboard\") {\n            return pathname === \"/dashboard\";\n        }\n        return pathname.startsWith(href);\n    };\n    const isAdmin = pathname.startsWith(\"/admin\");\n    const dashboardNavItems = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            name: \"Profile\",\n            href: \"/profile\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"Settings\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"Tools\",\n            href: \"#\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            children: [\n                {\n                    name: \"OSINT Lookup\",\n                    href: \"/osint\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    badge: \"Popular\"\n                },\n                {\n                    name: \"Vulnerability Scanner\",\n                    href: \"/scanner\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    badge: \"Pro\"\n                },\n                {\n                    name: \"File Analyzer\",\n                    href: \"/file-analyzer\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    badge: \"Pro\"\n                },\n                {\n                    name: \"CVE Database\",\n                    href: \"/cve\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                },\n                {\n                    name: \"Google Dorking\",\n                    href: \"/dorking\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                }\n            ]\n        },\n        {\n            name: \"Resources\",\n            href: \"#\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            children: [\n                {\n                    name: \"Documentation\",\n                    href: \"/docs\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                },\n                {\n                    name: \"Community\",\n                    href: \"/community\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                },\n                {\n                    name: \"Leaderboard\",\n                    href: \"/leaderboard\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                }\n            ]\n        }\n    ];\n    const adminNavItems = [\n        {\n            name: \"Dashboard\",\n            href: \"/admin/dashboard\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        },\n        {\n            name: \"Users\",\n            href: \"/admin/users\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            name: \"Bots\",\n            href: \"/admin/bots\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        },\n        {\n            name: \"Plans\",\n            href: \"/admin/plans\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n        },\n        {\n            name: \"Settings\",\n            href: \"/admin/settings\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"System\",\n            href: \"#\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            children: [\n                {\n                    name: \"Monitoring\",\n                    href: \"/admin/monitoring\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n                },\n                {\n                    name: \"Logs\",\n                    href: \"/admin/logs\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                },\n                {\n                    name: \"Security\",\n                    href: \"/admin/security\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n                }\n            ]\n        }\n    ];\n    const navItems = isAdmin ? adminNavItems : dashboardNavItems;\n    const getPlanColor = (plan)=>{\n        switch(plan){\n            case \"Elite\":\n                return \"text-yellow-400 bg-yellow-400/20\";\n            case \"Expert\":\n                return \"text-purple-400 bg-purple-400/20\";\n            case \"Pro\":\n                return \"text-blue-400 bg-blue-400/20\";\n            case \"Free\":\n                return \"text-gray-400 bg-gray-400/20\";\n            default:\n                return \"text-gray-400 bg-gray-400/20\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-cyber-dark flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-cyber-primary font-medium\",\n                        children: \"Initializing cyber interface...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-cyber-dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-cyber-dark/95 backdrop-blur-md border-b border-cyber-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleSidebar,\n                                    className: \"p-2 rounded-lg text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-colors lg:hidden\",\n                                    children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 32\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 60\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-8 w-8 text-cyber-primary animate-cyber-glow\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden sm:block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl font-bold text-cyber-glow\",\n                                                    children: \"KodeXGuard\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-cyber-secondary uppercase tracking-wider\",\n                                                    children: isAdmin ? \"Admin Console\" : \"Cyber Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex flex-1 max-w-md mx-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search...\",\n                                        className: \"w-full pl-10 pr-4 py-2 rounded-lg input-cyber text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `w-2 h-2 rounded-full ${systemStatus === \"online\" ? \"bg-green-400\" : \"bg-red-400\"} animate-pulse`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: [\n                                                \"System \",\n                                                systemStatus\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:block text-xs text-gray-400 font-mono\",\n                                    children: currentTime.toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 rounded-lg text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-colors relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-1 right-1 w-2 h-2 bg-cyber-secondary rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsProfileOpen(!isProfileOpen),\n                                            className: \"flex items-center space-x-3 p-2 rounded-lg hover:bg-cyber-primary/10 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-sm font-bold text-black\",\n                                                    children: user.username.charAt(0)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"hidden sm:block text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-white\",\n                                                            children: user.username\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `text-xs px-2 py-0.5 rounded-full ${getPlanColor(user.plan)}`,\n                                                            children: user.plan\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        isProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 top-full mt-2 w-64 bg-cyber-card border border-cyber-border rounded-lg shadow-xl z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 border-b border-cyber-border\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-lg font-bold text-black\",\n                                                                    children: user.username.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-white\",\n                                                                            children: user.fullName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-400\",\n                                                                            children: user.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        user.streak && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-1 text-xs text-cyber-secondary\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                                    lineNumber: 314,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        user.streak,\n                                                                                        \" day streak\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                                    lineNumber: 315,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 313,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        user.level && user.score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-3 grid grid-cols-2 gap-3 text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-bold text-cyber-primary\",\n                                                                            children: user.level\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 324,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"Level\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 325,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-bold text-cyber-secondary\",\n                                                                            children: user.score.toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"Score\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>router.push(\"/profile\"),\n                                                            className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-white hover:bg-cyber-primary/10 rounded-lg transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-cyber-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Profile\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>router.push(\"/settings\"),\n                                                            className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-white hover:bg-cyber-primary/10 rounded-lg transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-cyber-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 347,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Settings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleLogout,\n                                                            className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-red-400 hover:bg-red-500/10 rounded-lg transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Logout\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: `fixed top-16 left-0 z-40 w-64 h-[calc(100vh-4rem)] bg-cyber-card border-r border-cyber-border transform transition-transform duration-300 ease-in-out ${isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"} lg:translate-x-0`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 p-4 space-y-2 overflow-y-auto\",\n                            children: navItems.map((item)=>{\n                                const Icon = item.icon;\n                                const hasChildren = item.children && item.children.length > 0;\n                                const isExpanded = expandedItems.includes(item.name);\n                                const itemIsActive = hasChildren ? item.children?.some((child)=>isActive(child.href)) : isActive(item.href);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleExpanded(item.name),\n                                            className: `w-full flex items-center justify-between px-3 py-2 rounded-lg font-medium transition-colors duration-200 ${itemIsActive ? \"bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30\" : \"text-gray-300 hover:text-white hover:bg-cyber-primary/10\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: `h-4 w-4 transition-transform duration-200 ${isExpanded ? \"rotate-90\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>router.push(item.href),\n                                            className: `w-full flex items-center space-x-3 px-3 py-2 rounded-lg font-medium transition-colors duration-200 ${itemIsActive ? \"bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30\" : \"text-gray-300 hover:text-white hover:bg-cyber-primary/10\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 23\n                                                }, this),\n                                                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-auto bg-cyber-secondary/20 text-cyber-secondary px-2 py-0.5 rounded-full text-xs font-bold\",\n                                                    children: item.badge\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 21\n                                        }, this),\n                                        hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 mt-2 space-y-1\",\n                                            children: item.children?.map((child)=>{\n                                                const ChildIcon = child.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(child.href),\n                                                    className: `w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors duration-200 ${isActive(child.href) ? \"bg-cyber-primary/20 text-cyber-primary\" : \"text-gray-400 hover:text-white hover:bg-cyber-primary/10\"}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChildIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: child.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        child.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-auto bg-cyber-secondary/20 text-cyber-secondary px-1.5 py-0.5 rounded-full text-xs font-bold\",\n                                                            children: child.badge\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    ]\n                                                }, child.name, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 27\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t border-cyber-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mb-2\",\n                                        children: \"System Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-2 h-2 rounded-full ${systemStatus === \"online\" ? \"bg-green-400\" : \"bg-red-400\"} animate-pulse`\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-300 capitalize\",\n                                                children: systemStatus\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 369,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this),\n            isSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-30 bg-black/50 lg:hidden\",\n                onClick: ()=>setIsSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 461,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: `transition-all duration-300 pt-16 ${isSidebarOpen ? \"lg:ml-64\" : \"lg:ml-64\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-[calc(100vh-4rem)]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-cyber-card border-t border-cyber-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-6 py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-1 md:col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-8 w-8 text-cyber-primary animate-cyber-glow\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-cyber-glow\",\n                                                                    children: \"KodeXGuard\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-cyber-secondary uppercase tracking-wider\",\n                                                                    children: \"Cyber Security Platform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm mb-4\",\n                                                    children: \"Advanced cybersecurity platform for vulnerability scanning, OSINT intelligence, and comprehensive security analysis.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"All systems operational\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-bold text-white mb-4 uppercase tracking-wider\",\n                                                    children: \"Quick Links\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/dashboard\"),\n                                                                className: \"text-gray-400 hover:text-cyber-primary transition-colors text-sm\",\n                                                                children: \"Dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/profile\"),\n                                                                className: \"text-gray-400 hover:text-cyber-primary transition-colors text-sm\",\n                                                                children: \"Profile\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/settings\"),\n                                                                className: \"text-gray-400 hover:text-cyber-primary transition-colors text-sm\",\n                                                                children: \"Settings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/docs\"),\n                                                                className: \"text-gray-400 hover:text-cyber-primary transition-colors text-sm\",\n                                                                children: \"Documentation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-bold text-white mb-4 uppercase tracking-wider\",\n                                                    children: \"System\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Version:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 549,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-cyber-primary\",\n                                                                    children: \"v2.1.0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Uptime:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400\",\n                                                                    children: \"99.8%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Users:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-cyber-secondary\",\n                                                                    children: \"15.4K\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Scans:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-cyber-accent\",\n                                                                    children: \"89.4K\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 pt-8 border-t border-cyber-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"\\xa9 2024 KodeXGuard. All rights reserved.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 mt-4 md:mt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Last updated: \",\n                                                            currentTime.toLocaleDateString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-400\",\n                                                                children: \"Connected\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 477,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 468,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Toast.tsx":
/*!******************************!*\
  !*** ./components/Toast.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,useToast,toast,default auto */ \n\n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ToastProvider({ children }) {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((toast)=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        const newToast = {\n            ...toast,\n            id\n        };\n        setToasts((prev)=>[\n                ...prev,\n                newToast\n            ]);\n        // Auto remove after duration\n        const duration = toast.duration || 5000;\n        setTimeout(()=>{\n            removeToast(id);\n        }, duration);\n    }, []);\n    const removeToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    }, []);\n    const success = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"success\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    const error = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"error\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    const warning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"warning\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    const info = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"info\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toasts,\n            addToast,\n            removeToast,\n            success,\n            error,\n            warning,\n            info\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\nfunction useToast() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (context === undefined) {\n        throw new Error(\"useToast must be used within a ToastProvider\");\n    }\n    return context;\n}\nfunction ToastContainer() {\n    const { toasts, removeToast } = useToast();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastItem, {\n                toast: toast,\n                onRemove: ()=>removeToast(toast.id)\n            }, toast.id, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\nfunction ToastItem({ toast, onRemove }) {\n    const getIcon = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"h-5 w-5 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-5 w-5 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 16\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 16\n                }, this);\n            case \"info\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getBackgroundColor = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return \"bg-green-50 border-green-200\";\n            case \"error\":\n                return \"bg-red-50 border-red-200\";\n            case \"warning\":\n                return \"bg-yellow-50 border-yellow-200\";\n            case \"info\":\n                return \"bg-blue-50 border-blue-200\";\n        }\n    };\n    const getTextColor = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return \"text-green-800\";\n            case \"error\":\n                return \"text-red-800\";\n            case \"warning\":\n                return \"text-yellow-800\";\n            case \"info\":\n                return \"text-blue-800\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n      max-w-sm w-full shadow-lg rounded-lg border pointer-events-auto\n      ${getBackgroundColor()}\n      animate-slide-in-right\n    `,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: getIcon()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3 w-0 flex-1\",\n                        children: [\n                            toast.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-sm font-medium ${getTextColor()}`,\n                                children: toast.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-sm ${toast.title ? \"mt-1\" : \"\"} ${getTextColor()}`,\n                                children: toast.message\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            toast.action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toast.action.onClick,\n                                    className: `text-sm font-medium underline hover:no-underline ${getTextColor()}`,\n                                    children: toast.action.label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4 flex-shrink-0 flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onRemove,\n                            className: `rounded-md inline-flex ${getTextColor()} hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n// Utility function for standalone usage\nconst toast = {\n    success: (message, title)=>{\n        // This will only work if ToastProvider is available\n        console.log(\"Success:\", title || \"\", message);\n    },\n    error: (message, title)=>{\n        console.log(\"Error:\", title || \"\", message);\n    },\n    warning: (message, title)=>{\n        console.log(\"Warning:\", title || \"\", message);\n    },\n    info: (message, title)=>{\n        console.log(\"Info:\", title || \"\", message);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ToastProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1RvYXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRjtBQUNKO0FBd0IvRSxNQUFNUyw2QkFBZVQsb0RBQWFBLENBQStCVTtBQUUxRCxTQUFTQyxjQUFjLEVBQUVDLFFBQVEsRUFBMkI7SUFDakUsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdaLCtDQUFRQSxDQUFVLEVBQUU7SUFFaEQsTUFBTWEsV0FBV1osa0RBQVdBLENBQUMsQ0FBQ2E7UUFDNUIsTUFBTUMsS0FBS0MsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUc7UUFDaEQsTUFBTUMsV0FBVztZQUFFLEdBQUdOLEtBQUs7WUFBRUM7UUFBRztRQUVoQ0gsVUFBVVMsQ0FBQUEsT0FBUTttQkFBSUE7Z0JBQU1EO2FBQVM7UUFFckMsNkJBQTZCO1FBQzdCLE1BQU1FLFdBQVdSLE1BQU1RLFFBQVEsSUFBSTtRQUNuQ0MsV0FBVztZQUNUQyxZQUFZVDtRQUNkLEdBQUdPO0lBQ0wsR0FBRyxFQUFFO0lBRUwsTUFBTUUsY0FBY3ZCLGtEQUFXQSxDQUFDLENBQUNjO1FBQy9CSCxVQUFVUyxDQUFBQSxPQUFRQSxLQUFLSSxNQUFNLENBQUNYLENBQUFBLFFBQVNBLE1BQU1DLEVBQUUsS0FBS0E7SUFDdEQsR0FBRyxFQUFFO0lBRUwsTUFBTVcsVUFBVXpCLGtEQUFXQSxDQUFDLENBQUMwQixTQUFpQkM7UUFDNUNmLFNBQVM7WUFBRWdCLE1BQU07WUFBV0Y7WUFBU0M7UUFBTTtJQUM3QyxHQUFHO1FBQUNmO0tBQVM7SUFFYixNQUFNaUIsUUFBUTdCLGtEQUFXQSxDQUFDLENBQUMwQixTQUFpQkM7UUFDMUNmLFNBQVM7WUFBRWdCLE1BQU07WUFBU0Y7WUFBU0M7UUFBTTtJQUMzQyxHQUFHO1FBQUNmO0tBQVM7SUFFYixNQUFNa0IsVUFBVTlCLGtEQUFXQSxDQUFDLENBQUMwQixTQUFpQkM7UUFDNUNmLFNBQVM7WUFBRWdCLE1BQU07WUFBV0Y7WUFBU0M7UUFBTTtJQUM3QyxHQUFHO1FBQUNmO0tBQVM7SUFFYixNQUFNbUIsT0FBTy9CLGtEQUFXQSxDQUFDLENBQUMwQixTQUFpQkM7UUFDekNmLFNBQVM7WUFBRWdCLE1BQU07WUFBUUY7WUFBU0M7UUFBTTtJQUMxQyxHQUFHO1FBQUNmO0tBQVM7SUFFYixxQkFDRSw4REFBQ04sYUFBYTBCLFFBQVE7UUFBQ0MsT0FBTztZQUM1QnZCO1lBQ0FFO1lBQ0FXO1lBQ0FFO1lBQ0FJO1lBQ0FDO1lBQ0FDO1FBQ0Y7O1lBQ0d0QjswQkFDRCw4REFBQ3lCOzs7Ozs7Ozs7OztBQUdQO0FBRU8sU0FBU0M7SUFDZCxNQUFNQyxVQUFVdEMsaURBQVVBLENBQUNRO0lBQzNCLElBQUk4QixZQUFZN0IsV0FBVztRQUN6QixNQUFNLElBQUk4QixNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVDtBQUVBLFNBQVNGO0lBQ1AsTUFBTSxFQUFFeEIsTUFBTSxFQUFFYSxXQUFXLEVBQUUsR0FBR1k7SUFFaEMscUJBQ0UsOERBQUNHO1FBQUlDLFdBQVU7a0JBQ1o3QixPQUFPOEIsR0FBRyxDQUFDM0IsQ0FBQUEsc0JBQ1YsOERBQUM0QjtnQkFFQzVCLE9BQU9BO2dCQUNQNkIsVUFBVSxJQUFNbkIsWUFBWVYsTUFBTUMsRUFBRTtlQUYvQkQsTUFBTUMsRUFBRTs7Ozs7Ozs7OztBQU92QjtBQUVBLFNBQVMyQixVQUFVLEVBQ2pCNUIsS0FBSyxFQUNMNkIsUUFBUSxFQUlUO0lBQ0MsTUFBTUMsVUFBVTtRQUNkLE9BQVE5QixNQUFNZSxJQUFJO1lBQ2hCLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUMxQix3SEFBV0E7b0JBQUNxQyxXQUFVOzs7Ozs7WUFDaEMsS0FBSztnQkFDSCxxQkFBTyw4REFBQ3BDLHdIQUFXQTtvQkFBQ29DLFdBQVU7Ozs7OztZQUNoQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDbEMsd0hBQWFBO29CQUFDa0MsV0FBVTs7Ozs7O1lBQ2xDLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUNuQyx3SEFBSUE7b0JBQUNtQyxXQUFVOzs7Ozs7UUFDM0I7SUFDRjtJQUVBLE1BQU1LLHFCQUFxQjtRQUN6QixPQUFRL0IsTUFBTWUsSUFBSTtZQUNoQixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87UUFDWDtJQUNGO0lBRUEsTUFBTWlCLGVBQWU7UUFDbkIsT0FBUWhDLE1BQU1lLElBQUk7WUFDaEIsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1FBQ1g7SUFDRjtJQUVBLHFCQUNFLDhEQUFDVTtRQUFJQyxXQUFXLENBQUM7O01BRWYsRUFBRUsscUJBQXFCOztJQUV6QixDQUFDO2tCQUNDLDRFQUFDTjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNaSTs7Ozs7O2tDQUdILDhEQUFDTDt3QkFBSUMsV0FBVTs7NEJBQ1oxQixNQUFNYyxLQUFLLGtCQUNWLDhEQUFDbUI7Z0NBQUVQLFdBQVcsQ0FBQyxvQkFBb0IsRUFBRU0sZUFBZSxDQUFDOzBDQUNsRGhDLE1BQU1jLEtBQUs7Ozs7OzswQ0FHaEIsOERBQUNtQjtnQ0FBRVAsV0FBVyxDQUFDLFFBQVEsRUFBRTFCLE1BQU1jLEtBQUssR0FBRyxTQUFTLEdBQUcsQ0FBQyxFQUFFa0IsZUFBZSxDQUFDOzBDQUNuRWhDLE1BQU1hLE9BQU87Ozs7Ozs0QkFHZmIsTUFBTWtDLE1BQU0sa0JBQ1gsOERBQUNUO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDUztvQ0FDQ0MsU0FBU3BDLE1BQU1rQyxNQUFNLENBQUNFLE9BQU87b0NBQzdCVixXQUFXLENBQUMsaURBQWlELEVBQUVNLGVBQWUsQ0FBQzs4Q0FFOUVoQyxNQUFNa0MsTUFBTSxDQUFDRyxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNM0IsOERBQUNaO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDUzs0QkFDQ0MsU0FBU1A7NEJBQ1RILFdBQVcsQ0FBQyx1QkFBdUIsRUFBRU0sZUFBZSx3RkFBd0YsQ0FBQztzQ0FFN0ksNEVBQUM1Qyx3SEFBQ0E7Z0NBQUNzQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU8zQjtBQUVBLHdDQUF3QztBQUNqQyxNQUFNMUIsUUFBUTtJQUNuQlksU0FBUyxDQUFDQyxTQUFpQkM7UUFDekIsb0RBQW9EO1FBQ3BEd0IsUUFBUUMsR0FBRyxDQUFDLFlBQVl6QixTQUFTLElBQUlEO0lBQ3ZDO0lBQ0FHLE9BQU8sQ0FBQ0gsU0FBaUJDO1FBQ3ZCd0IsUUFBUUMsR0FBRyxDQUFDLFVBQVV6QixTQUFTLElBQUlEO0lBQ3JDO0lBQ0FJLFNBQVMsQ0FBQ0osU0FBaUJDO1FBQ3pCd0IsUUFBUUMsR0FBRyxDQUFDLFlBQVl6QixTQUFTLElBQUlEO0lBQ3ZDO0lBQ0FLLE1BQU0sQ0FBQ0wsU0FBaUJDO1FBQ3RCd0IsUUFBUUMsR0FBRyxDQUFDLFNBQVN6QixTQUFTLElBQUlEO0lBQ3BDO0FBQ0YsRUFBQztBQUVELGlFQUFlbEIsYUFBYUEsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2RiX2tvZGV4Z3VhcmQvLi9jb21wb25lbnRzL1RvYXN0LnRzeD85MzlhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlQ2FsbGJhY2ssIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgWCwgQ2hlY2tDaXJjbGUsIEFsZXJ0Q2lyY2xlLCBJbmZvLCBBbGVydFRyaWFuZ2xlIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgVG9hc3Qge1xuICBpZDogc3RyaW5nXG4gIHR5cGU6ICdzdWNjZXNzJyB8ICdlcnJvcicgfCAnd2FybmluZycgfCAnaW5mbydcbiAgdGl0bGU/OiBzdHJpbmdcbiAgbWVzc2FnZTogc3RyaW5nXG4gIGR1cmF0aW9uPzogbnVtYmVyXG4gIGFjdGlvbj86IHtcbiAgICBsYWJlbDogc3RyaW5nXG4gICAgb25DbGljazogKCkgPT4gdm9pZFxuICB9XG59XG5cbmludGVyZmFjZSBUb2FzdENvbnRleHRUeXBlIHtcbiAgdG9hc3RzOiBUb2FzdFtdXG4gIGFkZFRvYXN0OiAodG9hc3Q6IE9taXQ8VG9hc3QsICdpZCc+KSA9PiB2b2lkXG4gIHJlbW92ZVRvYXN0OiAoaWQ6IHN0cmluZykgPT4gdm9pZFxuICBzdWNjZXNzOiAobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4gdm9pZFxuICBlcnJvcjogKG1lc3NhZ2U6IHN0cmluZywgdGl0bGU/OiBzdHJpbmcpID0+IHZvaWRcbiAgd2FybmluZzogKG1lc3NhZ2U6IHN0cmluZywgdGl0bGU/OiBzdHJpbmcpID0+IHZvaWRcbiAgaW5mbzogKG1lc3NhZ2U6IHN0cmluZywgdGl0bGU/OiBzdHJpbmcpID0+IHZvaWRcbn1cblxuY29uc3QgVG9hc3RDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxUb2FzdENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpXG5cbmV4cG9ydCBmdW5jdGlvbiBUb2FzdFByb3ZpZGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3ROb2RlIH0pIHtcbiAgY29uc3QgW3RvYXN0cywgc2V0VG9hc3RzXSA9IHVzZVN0YXRlPFRvYXN0W10+KFtdKVxuXG4gIGNvbnN0IGFkZFRvYXN0ID0gdXNlQ2FsbGJhY2soKHRvYXN0OiBPbWl0PFRvYXN0LCAnaWQnPikgPT4ge1xuICAgIGNvbnN0IGlkID0gTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpXG4gICAgY29uc3QgbmV3VG9hc3QgPSB7IC4uLnRvYXN0LCBpZCB9XG4gICAgXG4gICAgc2V0VG9hc3RzKHByZXYgPT4gWy4uLnByZXYsIG5ld1RvYXN0XSlcblxuICAgIC8vIEF1dG8gcmVtb3ZlIGFmdGVyIGR1cmF0aW9uXG4gICAgY29uc3QgZHVyYXRpb24gPSB0b2FzdC5kdXJhdGlvbiB8fCA1MDAwXG4gICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICByZW1vdmVUb2FzdChpZClcbiAgICB9LCBkdXJhdGlvbilcbiAgfSwgW10pXG5cbiAgY29uc3QgcmVtb3ZlVG9hc3QgPSB1c2VDYWxsYmFjaygoaWQ6IHN0cmluZykgPT4ge1xuICAgIHNldFRvYXN0cyhwcmV2ID0+IHByZXYuZmlsdGVyKHRvYXN0ID0+IHRvYXN0LmlkICE9PSBpZCkpXG4gIH0sIFtdKVxuXG4gIGNvbnN0IHN1Y2Nlc3MgPSB1c2VDYWxsYmFjaygobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4ge1xuICAgIGFkZFRvYXN0KHsgdHlwZTogJ3N1Y2Nlc3MnLCBtZXNzYWdlLCB0aXRsZSB9KVxuICB9LCBbYWRkVG9hc3RdKVxuXG4gIGNvbnN0IGVycm9yID0gdXNlQ2FsbGJhY2soKG1lc3NhZ2U6IHN0cmluZywgdGl0bGU/OiBzdHJpbmcpID0+IHtcbiAgICBhZGRUb2FzdCh7IHR5cGU6ICdlcnJvcicsIG1lc3NhZ2UsIHRpdGxlIH0pXG4gIH0sIFthZGRUb2FzdF0pXG5cbiAgY29uc3Qgd2FybmluZyA9IHVzZUNhbGxiYWNrKChtZXNzYWdlOiBzdHJpbmcsIHRpdGxlPzogc3RyaW5nKSA9PiB7XG4gICAgYWRkVG9hc3QoeyB0eXBlOiAnd2FybmluZycsIG1lc3NhZ2UsIHRpdGxlIH0pXG4gIH0sIFthZGRUb2FzdF0pXG5cbiAgY29uc3QgaW5mbyA9IHVzZUNhbGxiYWNrKChtZXNzYWdlOiBzdHJpbmcsIHRpdGxlPzogc3RyaW5nKSA9PiB7XG4gICAgYWRkVG9hc3QoeyB0eXBlOiAnaW5mbycsIG1lc3NhZ2UsIHRpdGxlIH0pXG4gIH0sIFthZGRUb2FzdF0pXG5cbiAgcmV0dXJuIChcbiAgICA8VG9hc3RDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt7XG4gICAgICB0b2FzdHMsXG4gICAgICBhZGRUb2FzdCxcbiAgICAgIHJlbW92ZVRvYXN0LFxuICAgICAgc3VjY2VzcyxcbiAgICAgIGVycm9yLFxuICAgICAgd2FybmluZyxcbiAgICAgIGluZm9cbiAgICB9fT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDxUb2FzdENvbnRhaW5lciAvPlxuICAgIDwvVG9hc3RDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VUb2FzdCgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoVG9hc3RDb250ZXh0KVxuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VUb2FzdCBtdXN0IGJlIHVzZWQgd2l0aGluIGEgVG9hc3RQcm92aWRlcicpXG4gIH1cbiAgcmV0dXJuIGNvbnRleHRcbn1cblxuZnVuY3Rpb24gVG9hc3RDb250YWluZXIoKSB7XG4gIGNvbnN0IHsgdG9hc3RzLCByZW1vdmVUb2FzdCB9ID0gdXNlVG9hc3QoKVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCB0b3AtNCByaWdodC00IHotNTAgc3BhY2UteS0yXCI+XG4gICAgICB7dG9hc3RzLm1hcCh0b2FzdCA9PiAoXG4gICAgICAgIDxUb2FzdEl0ZW1cbiAgICAgICAgICBrZXk9e3RvYXN0LmlkfVxuICAgICAgICAgIHRvYXN0PXt0b2FzdH1cbiAgICAgICAgICBvblJlbW92ZT17KCkgPT4gcmVtb3ZlVG9hc3QodG9hc3QuaWQpfVxuICAgICAgICAvPlxuICAgICAgKSl9XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZnVuY3Rpb24gVG9hc3RJdGVtKHsgXG4gIHRvYXN0LCBcbiAgb25SZW1vdmUgXG59OiB7IFxuICB0b2FzdDogVG9hc3RcbiAgb25SZW1vdmU6ICgpID0+IHZvaWQgXG59KSB7XG4gIGNvbnN0IGdldEljb24gPSAoKSA9PiB7XG4gICAgc3dpdGNoICh0b2FzdC50eXBlKSB7XG4gICAgICBjYXNlICdzdWNjZXNzJzpcbiAgICAgICAgcmV0dXJuIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgIGNhc2UgJ2Vycm9yJzpcbiAgICAgICAgcmV0dXJuIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtcmVkLTYwMFwiIC8+XG4gICAgICBjYXNlICd3YXJuaW5nJzpcbiAgICAgICAgcmV0dXJuIDxBbGVydFRyaWFuZ2xlIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC15ZWxsb3ctNjAwXCIgLz5cbiAgICAgIGNhc2UgJ2luZm8nOlxuICAgICAgICByZXR1cm4gPEluZm8gY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWJsdWUtNjAwXCIgLz5cbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRCYWNrZ3JvdW5kQ29sb3IgPSAoKSA9PiB7XG4gICAgc3dpdGNoICh0b2FzdC50eXBlKSB7XG4gICAgICBjYXNlICdzdWNjZXNzJzpcbiAgICAgICAgcmV0dXJuICdiZy1ncmVlbi01MCBib3JkZXItZ3JlZW4tMjAwJ1xuICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICByZXR1cm4gJ2JnLXJlZC01MCBib3JkZXItcmVkLTIwMCdcbiAgICAgIGNhc2UgJ3dhcm5pbmcnOlxuICAgICAgICByZXR1cm4gJ2JnLXllbGxvdy01MCBib3JkZXIteWVsbG93LTIwMCdcbiAgICAgIGNhc2UgJ2luZm8nOlxuICAgICAgICByZXR1cm4gJ2JnLWJsdWUtNTAgYm9yZGVyLWJsdWUtMjAwJ1xuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdldFRleHRDb2xvciA9ICgpID0+IHtcbiAgICBzd2l0Y2ggKHRvYXN0LnR5cGUpIHtcbiAgICAgIGNhc2UgJ3N1Y2Nlc3MnOlxuICAgICAgICByZXR1cm4gJ3RleHQtZ3JlZW4tODAwJ1xuICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICByZXR1cm4gJ3RleHQtcmVkLTgwMCdcbiAgICAgIGNhc2UgJ3dhcm5pbmcnOlxuICAgICAgICByZXR1cm4gJ3RleHQteWVsbG93LTgwMCdcbiAgICAgIGNhc2UgJ2luZm8nOlxuICAgICAgICByZXR1cm4gJ3RleHQtYmx1ZS04MDAnXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YFxuICAgICAgbWF4LXctc20gdy1mdWxsIHNoYWRvdy1sZyByb3VuZGVkLWxnIGJvcmRlciBwb2ludGVyLWV2ZW50cy1hdXRvXG4gICAgICAke2dldEJhY2tncm91bmRDb2xvcigpfVxuICAgICAgYW5pbWF0ZS1zbGlkZS1pbi1yaWdodFxuICAgIGB9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICB7Z2V0SWNvbigpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtMyB3LTAgZmxleC0xXCI+XG4gICAgICAgICAgICB7dG9hc3QudGl0bGUgJiYgKFxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWVkaXVtICR7Z2V0VGV4dENvbG9yKCl9YH0+XG4gICAgICAgICAgICAgICAge3RvYXN0LnRpdGxlfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtgdGV4dC1zbSAke3RvYXN0LnRpdGxlID8gJ210LTEnIDogJyd9ICR7Z2V0VGV4dENvbG9yKCl9YH0+XG4gICAgICAgICAgICAgIHt0b2FzdC5tZXNzYWdlfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7dG9hc3QuYWN0aW9uICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17dG9hc3QuYWN0aW9uLm9uQ2xpY2t9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWVkaXVtIHVuZGVybGluZSBob3Zlcjpuby11bmRlcmxpbmUgJHtnZXRUZXh0Q29sb3IoKX1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHt0b2FzdC5hY3Rpb24ubGFiZWx9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTQgZmxleC1zaHJpbmstMCBmbGV4XCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29uUmVtb3ZlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Byb3VuZGVkLW1kIGlubGluZS1mbGV4ICR7Z2V0VGV4dENvbG9yKCl9IGhvdmVyOm9wYWNpdHktNzUgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctY3VycmVudGB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cblxuLy8gVXRpbGl0eSBmdW5jdGlvbiBmb3Igc3RhbmRhbG9uZSB1c2FnZVxuZXhwb3J0IGNvbnN0IHRvYXN0ID0ge1xuICBzdWNjZXNzOiAobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4ge1xuICAgIC8vIFRoaXMgd2lsbCBvbmx5IHdvcmsgaWYgVG9hc3RQcm92aWRlciBpcyBhdmFpbGFibGVcbiAgICBjb25zb2xlLmxvZygnU3VjY2VzczonLCB0aXRsZSB8fCAnJywgbWVzc2FnZSlcbiAgfSxcbiAgZXJyb3I6IChtZXNzYWdlOiBzdHJpbmcsIHRpdGxlPzogc3RyaW5nKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ0Vycm9yOicsIHRpdGxlIHx8ICcnLCBtZXNzYWdlKVxuICB9LFxuICB3YXJuaW5nOiAobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdXYXJuaW5nOicsIHRpdGxlIHx8ICcnLCBtZXNzYWdlKVxuICB9LFxuICBpbmZvOiAobWVzc2FnZTogc3RyaW5nLCB0aXRsZT86IHN0cmluZykgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdJbmZvOicsIHRpdGxlIHx8ICcnLCBtZXNzYWdlKVxuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IFRvYXN0UHJvdmlkZXJcbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJYIiwiQ2hlY2tDaXJjbGUiLCJBbGVydENpcmNsZSIsIkluZm8iLCJBbGVydFRyaWFuZ2xlIiwiVG9hc3RDb250ZXh0IiwidW5kZWZpbmVkIiwiVG9hc3RQcm92aWRlciIsImNoaWxkcmVuIiwidG9hc3RzIiwic2V0VG9hc3RzIiwiYWRkVG9hc3QiLCJ0b2FzdCIsImlkIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyIiwibmV3VG9hc3QiLCJwcmV2IiwiZHVyYXRpb24iLCJzZXRUaW1lb3V0IiwicmVtb3ZlVG9hc3QiLCJmaWx0ZXIiLCJzdWNjZXNzIiwibWVzc2FnZSIsInRpdGxlIiwidHlwZSIsImVycm9yIiwid2FybmluZyIsImluZm8iLCJQcm92aWRlciIsInZhbHVlIiwiVG9hc3RDb250YWluZXIiLCJ1c2VUb2FzdCIsImNvbnRleHQiLCJFcnJvciIsImRpdiIsImNsYXNzTmFtZSIsIm1hcCIsIlRvYXN0SXRlbSIsIm9uUmVtb3ZlIiwiZ2V0SWNvbiIsImdldEJhY2tncm91bmRDb2xvciIsImdldFRleHRDb2xvciIsInAiLCJhY3Rpb24iLCJidXR0b24iLCJvbkNsaWNrIiwibGFiZWwiLCJjb25zb2xlIiwibG9nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/Toast.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useThemeClasses: () => (/* binding */ useThemeClasses)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme,useThemeClasses auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dark\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load theme from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"theme\");\n        const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n        const initialTheme = savedTheme || systemTheme;\n        setThemeState(initialTheme);\n        setMounted(true);\n    }, []);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        const root = document.documentElement;\n        // Remove existing theme classes\n        root.classList.remove(\"dark\", \"light\");\n        // Add current theme class\n        root.classList.add(theme);\n        // Update CSS variables based on theme\n        if (theme === \"light\") {\n            // Light theme variables\n            root.style.setProperty(\"--cyber-bg-primary\", \"#ffffff\");\n            root.style.setProperty(\"--cyber-bg-secondary\", \"#f8fafc\");\n            root.style.setProperty(\"--cyber-bg-tertiary\", \"#f1f5f9\");\n            root.style.setProperty(\"--cyber-bg-card\", \"#ffffff\");\n            root.style.setProperty(\"--cyber-bg-hover\", \"#f1f5f9\");\n            root.style.setProperty(\"--cyber-text-primary\", \"#1e293b\");\n            root.style.setProperty(\"--cyber-text-secondary\", \"#475569\");\n            root.style.setProperty(\"--cyber-text-muted\", \"#64748b\");\n            root.style.setProperty(\"--cyber-border\", \"#e2e8f0\");\n            root.style.setProperty(\"--cyber-border-bright\", \"#0ea5e9\");\n            // Keep cyber colors but adjust opacity for light mode\n            root.style.setProperty(\"--cyber-primary\", \"#0ea5e9\");\n            root.style.setProperty(\"--cyber-secondary\", \"#ec4899\");\n            root.style.setProperty(\"--cyber-accent\", \"#f59e0b\");\n        } else {\n            // Dark theme variables (cyberpunk)\n            root.style.setProperty(\"--cyber-bg-primary\", \"#0a0a0f\");\n            root.style.setProperty(\"--cyber-bg-secondary\", \"#1a1a2e\");\n            root.style.setProperty(\"--cyber-bg-tertiary\", \"#16213e\");\n            root.style.setProperty(\"--cyber-bg-card\", \"#0f0f23\");\n            root.style.setProperty(\"--cyber-bg-hover\", \"#1e1e3f\");\n            root.style.setProperty(\"--cyber-text-primary\", \"#ffffff\");\n            root.style.setProperty(\"--cyber-text-secondary\", \"#b0b0b0\");\n            root.style.setProperty(\"--cyber-text-muted\", \"#808080\");\n            root.style.setProperty(\"--cyber-border\", \"#333366\");\n            root.style.setProperty(\"--cyber-border-bright\", \"#00ffff\");\n            root.style.setProperty(\"--cyber-primary\", \"#00ffff\");\n            root.style.setProperty(\"--cyber-secondary\", \"#ff0080\");\n            root.style.setProperty(\"--cyber-accent\", \"#ffff00\");\n        }\n        // Save to localStorage\n        localStorage.setItem(\"theme\", theme);\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setThemeState((prev)=>prev === \"dark\" ? \"light\" : \"dark\");\n    };\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-cyber-dark\",\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\contexts\\\\ThemeContext.tsx\",\n            lineNumber: 95,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme,\n            setTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\n// Hook for theme-aware styling\nfunction useThemeClasses() {\n    const { theme } = useTheme();\n    return {\n        // Background classes\n        bgPrimary: theme === \"dark\" ? \"bg-cyber-dark\" : \"bg-white\",\n        bgSecondary: theme === \"dark\" ? \"bg-cyber-secondary\" : \"bg-gray-50\",\n        bgCard: theme === \"dark\" ? \"bg-cyber-card\" : \"bg-white\",\n        // Text classes\n        textPrimary: theme === \"dark\" ? \"text-white\" : \"text-gray-900\",\n        textSecondary: theme === \"dark\" ? \"text-gray-300\" : \"text-gray-600\",\n        textMuted: theme === \"dark\" ? \"text-gray-400\" : \"text-gray-500\",\n        // Border classes\n        border: theme === \"dark\" ? \"border-gray-700\" : \"border-gray-200\",\n        borderBright: theme === \"dark\" ? \"border-cyber-primary\" : \"border-blue-500\",\n        // Button classes\n        btnPrimary: theme === \"dark\" ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white\",\n        btnSecondary: theme === \"dark\" ? \"btn-cyber-secondary\" : \"bg-gray-200 hover:bg-gray-300 text-gray-900\",\n        // Input classes\n        input: theme === \"dark\" ? \"input-cyber\" : \"bg-white border-gray-300 text-gray-900 focus:border-blue-500\",\n        // Card classes\n        card: theme === \"dark\" ? \"card-cyber\" : \"bg-white border border-gray-200 rounded-lg p-6 shadow-sm\",\n        // Effects\n        glow: theme === \"dark\" ? \"animate-cyber-glow\" : \"\",\n        pulse: theme === \"dark\" ? \"animate-cyber-pulse\" : \"\",\n        // Theme identifier\n        isDark: theme === \"dark\",\n        isLight: theme === \"light\"\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/auth.tsx":
/*!************************!*\
  !*** ./hooks/auth.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tokens, setTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAuthenticated = !!user && !!tokens;\n    // Verify token validity\n    const verifyToken = async (token)=>{\n        try {\n            const response = await fetch(\"/api/auth/verify\", {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            return response.ok;\n        } catch  {\n            return false;\n        }\n    };\n    // Initialize auth state from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                if (false) {}\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n                await logout();\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const login = async (email, password, rememberMe = false)=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password,\n                    rememberMe\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                const { user: userData, tokens: tokenData } = data.data;\n                setUser(userData);\n                setTokens(tokenData);\n                // Store in localStorage\n                if (false) {}\n                console.log(\"✅ User logged in:\", userData.username);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: data.error || \"Login failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: \"Network error. Please try again.\"\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch(\"/api/auth/register\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(userData)\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                const { user: userInfo, tokens: tokenData } = data.data;\n                setUser(userInfo);\n                setTokens(tokenData);\n                // Store in localStorage\n                if (false) {}\n                console.log(\"✅ User registered:\", userInfo.username);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: data.error || \"Registration failed\",\n                    details: data.details\n                };\n            }\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                error: \"Network error. Please try again.\"\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            // Call logout API if we have a token\n            if (tokens?.accessToken) {\n                await fetch(\"/api/auth/logout\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Authorization\": `Bearer ${tokens.accessToken}`,\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Logout API error:\", error);\n        } finally{\n            // Clear state\n            setUser(null);\n            setTokens(null);\n            // Clear localStorage\n            if (false) {}\n            console.log(\"✅ User logged out\");\n            router.push(\"/login\");\n        }\n    };\n    const refreshTokenFunc = async ()=>{\n        try {\n            if (!tokens?.refreshToken) {\n                return false;\n            }\n            const response = await fetch(\"/api/auth/refresh\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refreshToken: tokens.refreshToken\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                const { tokens: newTokens } = data.data;\n                setTokens(newTokens);\n                if (false) {}\n                return true;\n            } else {\n                await logout();\n                return false;\n            }\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            await logout();\n            return false;\n        }\n    };\n    const updateUser = (userData)=>{\n        if (user) {\n            const updatedUser = {\n                ...user,\n                ...userData\n            };\n            setUser(updatedUser);\n            if (false) {}\n        }\n    };\n    const value = {\n        user,\n        tokens,\n        isLoading,\n        isAuthenticated,\n        login,\n        register,\n        logout,\n        refreshToken: refreshTokenFunc,\n        updateUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\hooks\\\\auth.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/auth.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6662484993ed\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYl9rb2RleGd1YXJkLy4vYXBwL2dsb2JhbHMuY3NzPzY0YTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2NjYyNDg0OTkzZWRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\app\dashboard\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Toast */ \"(rsc)/./components/Toast.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(rsc)/./contexts/ThemeContext.tsx\");\n/* harmony import */ var _hooks_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/auth */ \"(rsc)/./hooks/auth.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"KodeXGuard - Cybersecurity & Bug Hunting Platform\",\n    description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan komunitas Bug Hunter\",\n    keywords: \"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing\",\n    authors: [\n        {\n            name: \"KodeXGuard Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} scrollbar-cyber`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_auth__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/Toast.tsx":
/*!******************************!*\
  !*** ./components/Toast.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   toast: () => (/* binding */ e2),
/* harmony export */   useToast: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#ToastProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#useToast`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#toast`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#default`));


/***/ }),

/***/ "(rsc)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1),
/* harmony export */   useThemeClasses: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\contexts\ThemeContext.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\contexts\ThemeContext.tsx#useTheme`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\contexts\ThemeContext.tsx#useThemeClasses`);


/***/ }),

/***/ "(rsc)/./hooks/auth.tsx":
/*!************************!*\
  !*** ./hooks/auth.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\hooks\auth.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\hooks\auth.tsx#useAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@opentelemetry","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();