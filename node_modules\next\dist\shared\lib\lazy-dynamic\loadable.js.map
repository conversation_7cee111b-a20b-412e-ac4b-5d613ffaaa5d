{"version": 3, "sources": ["../../../../src/shared/lib/lazy-dynamic/loadable.tsx"], "names": ["convertModule", "mod", "<PERSON><PERSON><PERSON><PERSON>", "default", "defaultOptions", "loader", "Promise", "resolve", "loading", "ssr", "Loadable", "options", "opts", "Lazy", "lazy", "then", "Loading", "LoadableComponent", "props", "fallbackElement", "isLoading", "past<PERSON>elay", "error", "children", "window", "PreloadCss", "moduleIds", "modules", "BailoutToCSR", "reason", "Suspense", "fallback", "displayName"], "mappings": ";;;;+BAwEA;;;eAAA;;;;uBAxE+B;qCACF;4BAEF;AAE3B,yFAAyF;AACzF,qGAAqG;AACrG,qEAAqE;AACrE,SAASA,cACPC,GAA4D;IAI5D,iHAAiH;IACjH,SAAS;IACT,8BAA8B;IAC9B,iBAAiB;IACjB,+CAA+C;IAC/C,wBAAwB;IACxB,MAAMC,aAAaD,OAAO,aAAaA;IACvC,OAAO;QACLE,SAASD,aACL,AAACD,IAA2BE,OAAO,GAClCF;IACP;AACF;AAEA,MAAMG,iBAAiB;IACrBC,QAAQ,IAAMC,QAAQC,OAAO,CAACP,cAAc,IAAM;IAClDQ,SAAS;IACTC,KAAK;AACP;AASA,SAASC,SAASC,OAAwB;IACxC,MAAMC,OAAO;QAAE,GAAGR,cAAc;QAAE,GAAGO,OAAO;IAAC;IAC7C,MAAME,qBAAOC,IAAAA,WAAI,EAAC,IAAMF,KAAKP,MAAM,GAAGU,IAAI,CAACf;IAC3C,MAAMgB,UAAUJ,KAAKJ,OAAO;IAE5B,SAASS,kBAAkBC,KAAU;QACnC,MAAMC,kBAAkBH,wBACtB,qBAACA;YAAQI,WAAW;YAAMC,WAAW;YAAMC,OAAO;aAChD;QAEJ,MAAMC,WAAWX,KAAKH,GAAG,iBACvB;;gBAEG,OAAOe,WAAW,4BACjB,qBAACC,sBAAU;oBAACC,WAAWd,KAAKe,OAAO;qBACjC;8BACJ,qBAACd;oBAAM,GAAGK,KAAK;;;2BAGjB,qBAACU,iCAAY;YAACC,QAAO;sBACnB,cAAA,qBAAChB;gBAAM,GAAGK,KAAK;;;QAInB,qBAAO,qBAACY,eAAQ;YAACC,UAAUZ;sBAAkBI;;IAC/C;IAEAN,kBAAkBe,WAAW,GAAG;IAEhC,OAAOf;AACT;MAEA,WAAeP"}