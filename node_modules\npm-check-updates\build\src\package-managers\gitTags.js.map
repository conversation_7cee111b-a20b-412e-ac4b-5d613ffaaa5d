{"version": 3, "file": "gitTags.js", "sourceRoot": "", "sources": ["../../../src/package-managers/gitTags.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iDAAiD;AACjD,wEAA6C;AAC7C,sEAA2C;AAC3C,mCAA8B;AAC9B,4CAAsC;AACtC,iEAAkD;AAOlD,mCAAmC;AACnC,MAAM,iBAAiB,GAAG,KAAK,EAAE,IAAY,EAAE,WAAwB,EAAE,OAAiB,EAAE,EAAE;IAC5F,qGAAqG;IACrG,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;IACjD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAA,0BAAc,EAAC,WAAW,CAAE,CAAA;IACnE,IAAI,MAAM,GAAG,IAAI,GAAG,EAAE,CAAA;IACtB,IAAI,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IACzC,MAAM,aAAa,GAAG,QAAQ,IAAI,IAAI,CAAA;IACtC,IAAI,aAAa,EAAE;QACjB,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,CAClC,IAAA,yBAAa,EAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC,CACjH,CAAA;KACF;SAAM;QACL,uCAAuC;QACvC,WAAW,GAAG,WAAW;aACtB,IAAI,CAAC,GAAG,EAAE,CAAC,IAAA,yBAAa,EAAC,aAAa,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC;aACtD,KAAK,CAAC,GAAG,EAAE,CAAC,IAAA,yBAAa,EAAC,WAAW,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC,CAAA;KAClF;IAED,oBAAoB;IACpB,IAAI;QACF,MAAM,GAAG,MAAM,WAAW,CAAA;KAC3B;IAAC,OAAO,CAAC,EAAE;QACV,mEAAmE;QACnE,IAAA,eAAK,EAAC,OAAO,IAAI,EAAE,EAAE,yCAAyC,IAAI,KAAK,WAAW,EAAE,EAAE,SAAS,CAAC,CAAA;QAChG,OAAO,IAAI,CAAA;KACZ;IAED,kDAAkD;IAClD,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;SACnC,GAAG,CAAC,WAAW,CAAC,gBAAgB,CAAC;QAClC,8GAA8G;QAC9G,+CAA+C;SAC9C,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,IAAA,cAAK,EAAC,GAAG,CAAC,CAAC;SACzB,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAA;IAEpC,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAED,0EAA0E;AACnE,MAAM,MAAM,GAAe,KAAK,EAAE,IAAY,EAAE,WAAwB,EAAE,OAAiB,EAAE,EAAE;IACpG,MAAM,QAAQ,GAAG,MAAM,iBAAiB,CAAC,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,CAAA;IACpE,IAAI,CAAC,QAAQ;QAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;IACvC,MAAM,gBAAgB,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,EAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;IAC9F,MAAM,aAAa,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IACnE,OAAO,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;AACrG,CAAC,CAAA;AANY,QAAA,MAAM,UAMlB;AAED,2DAA2D;AACpD,MAAM,QAAQ,GAAe,KAAK,EAAE,IAAY,EAAE,WAAwB,EAAE,OAAiB,EAAE,EAAE;IACtG,MAAM,QAAQ,GAAG,MAAM,iBAAiB,CAAC,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,CAAA;IACpE,IAAI,CAAC,QAAQ;QAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;IACvC,MAAM,eAAe,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IACrD,OAAO,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;AACzG,CAAC,CAAA;AALY,QAAA,QAAQ,YAKpB;AAED,8EAA8E;AACvE,MAAM,aAAa,GACxB,CAAC,KAAmB,EAAE,EAAE,CACxB,KAAK,EAAE,IAAY,EAAE,WAAwB,EAAE,UAAmB,EAAE,EAA0B,EAAE;IAC9F,MAAM,OAAO,GAAG,kBAAkB,CAAC,IAAA,0BAAc,EAAC,WAAW,CAAE,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;IAC/F,MAAM,QAAQ,GAAG,MAAM,iBAAiB,CAAC,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,CAAA;IACpE,IAAI,CAAC,QAAQ;QAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;IAEvC,MAAM,aAAa,GAAG,WAAW,CAAC,mBAAmB,CACnD,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,EACtC,OAAO,EACP,KAAK,CACN,CAAA;IAED,OAAO,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,gBAAgB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;AACrG,CAAC,CAAA;AAdU,QAAA,aAAa,iBAcvB;AAEU,QAAA,KAAK,GAAG,IAAA,qBAAa,EAAC,OAAO,CAAC,CAAA;AAC9B,QAAA,KAAK,GAAG,IAAA,qBAAa,EAAC,OAAO,CAAC,CAAA;AAE3C,yFAAyF;AACzF,8DAA8D;AAC9D,6DAA6D;AACtD,MAAM,MAAM,GAAe,KAAK,EAAE,KAAa,EAAE,YAAyB,EAAE,QAAkB,EAAE,EAAE;IACvG,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;AAC1B,CAAC,CAAA;AAFY,QAAA,MAAM,UAElB;AAED,+DAA+D;AAC/D,sHAAsH;AACzG,QAAA,MAAM,GAAG,gBAAQ,CAAA"}