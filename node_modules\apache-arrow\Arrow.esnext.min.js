// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.
(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
    typeof define === 'function' && define.amd ? define(['exports'], factory) :
    (factory(global.Arrow = global.Arrow || {}));
}(this, (function (exports) {var g;const aa=new TextDecoder("utf-8"),ba=a=>aa.decode(a),ca=new TextEncoder;const l=a=>"function"===typeof a,m=a=>null!=a&&Object(a)===a,da=a=>"_getDOMStream"in a&&"_getNodeStream"in a,ea=a=>m(a)&&l(a.cancel)&&l(a.getReader)&&!da(a),fa=a=>m(a)&&l(a.read)&&l(a.pipe)&&"boolean"===typeof a.readable&&!da(a);const ha="undefined"!==typeof SharedArrayBuffer?SharedArrayBuffer:ArrayBuffer;function ia(a){const b=a[0]?[a[0]]:[];let c,d,e,f;for(let h,k,n=0,q=0,B=a.length;++n<B;)h=b[q],k=a[n],!h||!k||h.buffer!==k.buffer||k.byteOffset<h.byteOffset?k&&(b[++q]=k):({byteOffset:c,byteLength:e}=h,{byteOffset:d,byteLength:f}=k,c+e<d||d+f<c?k&&(b[++q]=k):b[q]=new Uint8Array(h.buffer,c,d-c+f));return b}
function ja(a,b,c=0,d=b.byteLength){const e=a.byteLength,f=new Uint8Array(a.buffer,a.byteOffset,e);b=new Uint8Array(b.buffer,b.byteOffset,Math.min(d,e));f.set(b,c);return a}
function ka(a,b){a=ia(a);const c=a.reduce((n,q)=>n+q.byteLength,0);let d,e,f=0,h=-1;const k=Math.min(b||Number.POSITIVE_INFINITY,c);for(const n=a.length;++h<n;){b=a[h];d=b.subarray(0,Math.min(b.length,k-f));if(k<=f+d.length){d.length<b.length?a[h]=b.subarray(d.length):d.length===b.length&&h++;e?ja(e,d,f):e=d;break}ja(e||=new Uint8Array(k),d,f);f+=d.length}return[e||new Uint8Array(0),a.slice(h),c-(e?e.byteLength:0)]}
function p(a,b){b=m(b)&&"done"in b&&"value"in b?b.value:b;if(b instanceof a)return a===Uint8Array?new a(b.buffer,b.byteOffset,b.byteLength):b;if(!b)return new a(0);"string"===typeof b&&(b=ca.encode(b));return b instanceof ArrayBuffer||b instanceof ha?new a(b):m(b)&&l(b.clear)&&l(b.bytes)&&l(b.position)&&l(b.setPosition)&&l(b.capacity)&&l(b.getBufferIdentifier)&&l(b.createLong)?p(a,b.R()):ArrayBuffer.isView(b)?0>=b.byteLength?new a(0):new a(b.buffer,b.byteOffset,b.byteLength/a.BYTES_PER_ELEMENT):a.from(b)}
const la=a=>{a.next();return a};function*ma(a,b){function*c(d){yield d}b="string"===typeof b?c(b):ArrayBuffer.isView(b)?c(b):b instanceof ArrayBuffer?c(b):b instanceof ha?c(b):m(b)&&l(b[Symbol.iterator])?b:c(b);yield*la(function*(d){let e=null;do e=d.next(yield p(a,e));while(!e.done)}(b[Symbol.iterator]()));return new a}
async function*na(a,b){async function*c(e){yield*la(function*(f){let h=null;do h=f.next(yield h?.value);while(!h.done)}(e[Symbol.iterator]()))}async function*d(e){yield await e}if(m(b)&&l(b.then))return yield*na(a,await b);b="string"===typeof b?d(b):ArrayBuffer.isView(b)?d(b):b instanceof ArrayBuffer?d(b):b instanceof ha?d(b):m(b)&&l(b[Symbol.iterator])?c(b):m(b)&&l(b[Symbol.asyncIterator])?b:d(b);yield*la(async function*(e){let f=null;do f=await e.next(yield p(a,f));while(!f.done)}(b[Symbol.asyncIterator]()));
return new a}function oa(a,b,c){if(0!==a){c=c.slice(0,b);for(let d=-1,e=c.length;++d<e;)c[d]+=a}return c.subarray(0,b)}function pa(a,b){let c=0;const d=a.length;if(d!==b.length)return!1;if(0<d){do if(a[c]!==b[c])return!1;while(++c<d)}return!0}var r={};r.compareArrayLike=pa;r.joinUint8Arrays=ka;r.memcpy=ja;r.rebaseValueOffsets=oa;r.toArrayBufferView=p;r.toArrayBufferViewAsyncIterator=na;r.toArrayBufferViewIterator=ma;r.toBigInt64Array=a=>p(BigInt64Array,a);r.toBigUint64Array=a=>p(BigUint64Array,a);
r.toFloat32Array=a=>p(Float32Array,a);r.toFloat32ArrayAsyncIterator=a=>na(Float32Array,a);r.toFloat32ArrayIterator=a=>ma(Float32Array,a);r.toFloat64Array=a=>p(Float64Array,a);r.toFloat64ArrayAsyncIterator=a=>na(Float64Array,a);r.toFloat64ArrayIterator=a=>ma(Float64Array,a);r.toInt16Array=a=>p(Int16Array,a);r.toInt16ArrayAsyncIterator=a=>na(Int16Array,a);r.toInt16ArrayIterator=a=>ma(Int16Array,a);r.toInt32Array=a=>p(Int32Array,a);r.toInt32ArrayAsyncIterator=a=>na(Int32Array,a);
r.toInt32ArrayIterator=a=>ma(Int32Array,a);r.toInt8Array=a=>p(Int8Array,a);r.toInt8ArrayAsyncIterator=a=>na(Int8Array,a);r.toInt8ArrayIterator=a=>ma(Int8Array,a);r.toUint16Array=a=>p(Uint16Array,a);r.toUint16ArrayAsyncIterator=a=>na(Uint16Array,a);r.toUint16ArrayIterator=a=>ma(Uint16Array,a);r.toUint32Array=a=>p(Uint32Array,a);r.toUint32ArrayAsyncIterator=a=>na(Uint32Array,a);r.toUint32ArrayIterator=a=>ma(Uint32Array,a);r.toUint8Array=a=>p(Uint8Array,a);
r.toUint8ArrayAsyncIterator=a=>na(Uint8Array,a);r.toUint8ArrayIterator=a=>ma(Uint8Array,a);r.toUint8ClampedArray=a=>p(Uint8ClampedArray,a);r.toUint8ClampedArrayAsyncIterator=a=>na(Uint8ClampedArray,a);r.toUint8ClampedArrayIterator=a=>ma(Uint8ClampedArray,a);function qa(){throw Error('"toDOMStream" not available in this environment');}function ra(){throw Error('"toNodeStream" not available in this environment');}const sa=a=>{a.next();return a};
function*ta(a){let b,c=!1,d=[],e,f,h,k=0;({M:f,size:h}=(yield null)||{M:"read",size:0});a=ma(Uint8Array,a)[Symbol.iterator]();try{do if({done:b,value:e}=Number.isNaN(h-k)?a.next():a.next(h-k),!b&&0<e.byteLength&&(d.push(e),k+=e.byteLength),b||h<=k){do{if("peek"===f)var n=ka(d,h)[0];else[e,d,k]=ka(d,h),n=e;({M:f,size:h}=yield n)}while(h<k)}while(!b)}catch(q){(c=!0,"function"===typeof a.throw)&&a.throw(q)}finally{!1===c&&"function"===typeof a.return&&a.return(null)}return null}
async function*ua(a){let b,c=!1,d=[],e,f,h,k=0;({M:f,size:h}=(yield null)||{M:"read",size:0});a=na(Uint8Array,a)[Symbol.asyncIterator]();try{do if({done:b,value:e}=Number.isNaN(h-k)?await a.next():await a.next(h-k),!b&&0<e.byteLength&&(d.push(e),k+=e.byteLength),b||h<=k){do{if("peek"===f)var n=ka(d,h)[0];else[e,d,k]=ka(d,h),n=e;({M:f,size:h}=yield n)}while(h<k)}while(!b)}catch(q){(c=!0,"function"===typeof a.throw)&&await a.throw(q)}finally{!1===c&&"function"===typeof a.return&&await a.return(new Uint8Array(0))}return null}
async function*va(a){let b=!1,c=!1,d=[],e,f,h,k=0;({M:f,size:h}=(yield null)||{M:"read",size:0});const n=new wa(a);try{do if({done:b,value:e}=Number.isNaN(h-k)?await n.read():await n.read(h-k),!b&&0<e.byteLength&&(d.push(p(Uint8Array,e)),k+=e.byteLength),b||h<=k){do{if("peek"===f)var q=ka(d,h)[0];else[e,d,k]=ka(d,h),q=e;({M:f,size:h}=yield q)}while(h<k)}while(!b)}catch(B){c=!0,await n.cancel(B)}finally{!1===c?await n.cancel():a.locked&&n.releaseLock()}return null}
class wa{constructor(a){this.source=a;this.T=null;this.T=this.source.getReader();this.T.closed.catch(()=>{})}get closed(){return this.T?this.T.closed.catch(()=>{}):Promise.resolve()}releaseLock(){this.T&&this.T.releaseLock();this.T=null}async cancel(a){const b=this.T,c=this.source;b&&await b.cancel(a).catch(()=>{});c&&c.locked&&this.releaseLock()}async read(a){if(0===a)return{done:null==this.T,value:new Uint8Array(0)};a=await this.T.read();!a.done&&(a.value=p(Uint8Array,a));return a}}
const xa=(a,b)=>{const c=e=>d([b,e]);let d;return[b,c,new Promise(e=>(d=e)&&a.once(b,c))]};
async function*ya(a){function b(ib,xb){B=q=null;return new Promise((Ji,Ki)=>{for(const [yb,Li]of ib)a.off(yb,Li);try{const yb=a.destroy;yb&&yb.call(a,xb);xb=void 0}catch(yb){xb=yb||xb}finally{null!=xb?Ki(xb):Ji()}})}const c=[];let d="error",e=!1,f=null,h,k,n=0,q=[],B;({M:h,size:k}=(yield null)||{M:"read",size:0});if(a.isTTY)return yield new Uint8Array(0),null;try{c[0]=xa(a,"end");c[1]=xa(a,"error");do{c[2]=xa(a,"readable");[d,f]=await Promise.race(c.map(ib=>ib[2]));if("error"===d)break;(e="end"===
d)||(Number.isFinite(k-n)?(B=p(Uint8Array,a.read(k-n)),B.byteLength<k-n&&(B=p(Uint8Array,a.read()))):B=p(Uint8Array,a.read()),0<B.byteLength&&(q.push(B),n+=B.byteLength));if(e||k<=n){do{if("peek"===h)var Na=ka(q,k)[0];else[B,q,n]=ka(q,k),Na=B;({M:h,size:k}=yield Na)}while(k<n)}}while(!e)}finally{await b(c,"error"===d?f:null)}return null};var t,za=t||={};za[za.V1=0]="V1";za[za.V2=1]="V2";za[za.V3=2]="V3";za[za.V4=3]="V4";za[za.V5=4]="V5";var u,Aa=u||={};Aa[Aa.Sparse=0]="Sparse";Aa[Aa.Dense=1]="Dense";var v,Ba=v||={};Ba[Ba.HALF=0]="HALF";Ba[Ba.SINGLE=1]="SINGLE";Ba[Ba.DOUBLE=2]="DOUBLE";var Ca,Da=Ca||={};Da[Da.DAY=0]="DAY";Da[Da.MILLISECOND=1]="MILLISECOND";var w,Ea=w||={};Ea[Ea.SECOND=0]="SECOND";Ea[Ea.MILLISECOND=1]="MILLISECOND";Ea[Ea.MICROSECOND=2]="MICROSECOND";Ea[Ea.NANOSECOND=3]="NANOSECOND";var Fa,Ga=Fa||={};Ga[Ga.YEAR_MONTH=0]="YEAR_MONTH";Ga[Ga.DAY_TIME=1]="DAY_TIME";Ga[Ga.MONTH_DAY_NANO=2]="MONTH_DAY_NANO";const Ha=new Int32Array(2);new Float32Array(Ha.buffer);new Float64Array(Ha.buffer);new Uint16Array((new Uint8Array([1,0])).buffer);var Ia,Ja=Ia||={};Ja[Ja.UTF8_BYTES=1]="UTF8_BYTES";Ja[Ja.UTF16_STRING=2]="UTF16_STRING";function Ka(a,b){return(a.l[b]|a.l[b+1]<<8)<<16>>16}function La(a,b){return BigInt.asIntN(64,BigInt(a.u(b)>>>0)+(BigInt(a.u(b+4)>>>0)<<BigInt(32)))}function x(a,b,c){b-=a.u(b);return c<Ka(a,b)?Ka(a,b+c):0}function Ma(a,b,c){b.h=c+a.u(c);b.g=a;return b}function Oa(a,b,c){b+=a.u(b);const d=a.u(b);b+=4;b=a.l.subarray(b,b+d);return c===Ia.UTF8_BYTES?b:a.Rb.decode(b)}function Pa(a,b){return b+a.u(b)}function Qa(a,b){return b+a.u(b)+4}function Ra(a,b){return a.u(b+a.u(b))}
class Sa{constructor(a){this.l=a;this.ab=0;this.Rb=new TextDecoder}clear(){this.ab=0}R(){return this.l}position(){return this.ab}setPosition(a){this.ab=a}u(a){return this.l[a]|this.l[a+1]<<8|this.l[a+2]<<16|this.l[a+3]<<24}fa(a,b){this.l[a]=b}gb(a,b){this.l[a]=b;this.l[a+1]=b>>8}V(a,b){this.l[a]=b;this.l[a+1]=b>>8;this.l[a+2]=b>>16;this.l[a+3]=b>>24}Y(a,b){this.V(a,Number(BigInt.asIntN(32,b)));this.V(a+4,Number(BigInt.asIntN(32,b>>BigInt(32))))}};function Ta(a){return a.g.R().subarray(a.g.position(),a.g.position()+a.offset())}function Ua(a,b){for(let c=0;c<b;c++)a.g.fa(--a.B,0)}function Va(a,b,c){b>a.qa&&(a.qa=b);const d=~(a.g.l.length-a.B+c)+1&b-1;for(;a.B<d+b+c;){const h=a.g.l.length;var e=a,f=a.g;const k=f.l.length;if(k&3221225472)throw Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");const n=k<<1,q=new Sa(new Uint8Array(n));q.setPosition(n-k);q.R().set(f.R(),n-k);e.g=q;a.B+=a.g.l.length-h}Ua(a,d)}
function Wa(a,b){Va(a,2,0);a.gb(b)}function Xa(a,b){Va(a,4,0);a.V(b)}function Ya(a,b,c,d){if(a.ba||c!=d)Va(a,1,0),a.fa(c),a.slot(b)}function Za(a,b,c){if(a.ba||b!=c)Wa(a,b),a.slot(0)}function $a(a,b,c,d){if(a.ba||c!=d)Xa(a,c),a.slot(b)}function ab(a,b,c){var d=BigInt("0");if(a.ba||c!==d)Va(a,8,0),a.Y(c),a.slot(b)}function bb(a,b){Va(a,4,0);a.V(a.offset()-b+4)}function y(a,b,c){if(a.ba||0!=c)bb(a,c),a.slot(b)}
function cb(a){if(a.pa)throw new TypeError("FlatBuffers: object serialization must not be nested.");}function z(a,b){cb(a);null==a.U&&(a.U=[]);a.fb=b;for(let c=0;c<b;c++)a.U[c]=0;a.pa=!0;a.$a=a.offset()}
function A(a){if(null==a.U||!a.pa)throw Error("FlatBuffers: endObject called without startObject");Xa(a,0);const b=a.offset();let c=a.fb-1;for(;0<=c&&0==a.U[c];c--);for(var d=c+1;0<=c;c--)Wa(a,0!=a.U[c]?b-a.U[c]:0);Wa(a,b-a.$a);d=2*(d+2);Wa(a,d);let e=0;const f=a.B;c=0;a:for(;c<a.ra.length;c++){const h=a.g.l.length-a.ra[c];if(d==Ka(a.g,h)){for(let k=2;k<d;k+=2)if(Ka(a.g,f+k)!=Ka(a.g,h+k))continue a;e=a.ra[c];break}}e?(a.B=a.g.l.length-b,a.g.V(a.B,e-b)):(a.ra.push(a.offset()),a.g.V(a.g.l.length-b,
a.offset()-b));a.pa=!1;return b}function db(a,b,c,d){cb(a);a.eb=c;Va(a,4,b*c);Va(a,d,b*c)}function eb(a){a.V(a.eb);return a.offset()}function fb(a,b){if(null===b||void 0===b)return 0;b=b instanceof Uint8Array?b:a.Sb.encode(b);Va(a,1,0);a.fa(0);db(a,1,b.length,1);a.g.setPosition(a.B-=b.length);a.g.R().set(b,a.B);return eb(a)}
class gb{constructor(){this.qa=1;this.U=null;this.fb=0;this.pa=!1;this.$a=0;this.ra=[];this.eb=0;this.ba=!1;this.Sb=new TextEncoder;this.g=new Sa(new Uint8Array(1024));this.B=1024}clear(){this.g.clear();this.B=this.g.l.length;this.qa=1;this.U=null;this.fb=0;this.pa=!1;this.$a=0;this.ra=[];this.eb=0;this.ba=!1}fa(a){this.g.fa(--this.B,a)}gb(a){this.g.gb(this.B-=2,a)}V(a){this.g.V(this.B-=4,a)}Y(a){this.g.Y(this.B-=8,a)}slot(a){null!==this.U&&(this.U[a]=this.offset())}offset(){return this.g.l.length-
this.B}finish(a,b,c){c=c?4:0;if(b){Va(this,this.qa,8+c);if(4!=b.length)throw new TypeError("FlatBuffers: file identifier must be length 4");for(let d=3;0<=d;d--)this.fa(b.charCodeAt(d))}Va(this,this.qa,4+c);bb(this,a);c&&Xa(this,this.g.l.length-this.B);this.g.setPosition(this.B)}};var hb,jb=hb||={};jb[jb.BUFFER=0]="BUFFER";var kb,lb=kb||={};lb[lb.LZ4_FRAME=0]="LZ4_FRAME";lb[lb.ZSTD=1]="ZSTD";class mb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}codec(){const a=x(this.g,this.h,4);return a?this.g.l[this.h+a]<<24>>24:kb.Wb}method(){const a=x(this.g,this.h,6);return a?this.g.l[this.h+a]<<24>>24:hb.Ub}};class nb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}offset(){return La(this.g,this.h)}length(){return La(this.g,this.h+8)}};class ob{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}length(){return La(this.g,this.h)}nullCount(){return La(this.g,this.h+8)}};function pb(a){const b=x(a.g,a.h,6);return b?Ra(a.g,a.h+b):0}function qb(a){const b=x(a.g,a.h,8);return b?Ra(a.g,a.h+b):0}class rb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}length(){const a=x(this.g,this.h,4);return a?La(this.g,this.h+a):BigInt("0")}L(a,b){const c=x(this.g,this.h,6);return c?(b||new ob).i(Qa(this.g,this.h+c)+16*a,this.g):null}buffers(a,b){const c=x(this.g,this.h,8);return c?(b||new nb).i(Qa(this.g,this.h+c)+16*a,this.g):null}};class sb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}id(){const a=x(this.g,this.h,4);return a?La(this.g,this.h+a):BigInt("0")}data(a){const b=x(this.g,this.h,6);return b?(a||new rb).i(Pa(this.g,this.h+b),this.g):null}Fa(){const a=x(this.g,this.h,8);return a?!!(this.g.l[this.h+a]<<24>>24):!1}};var tb,ub=tb||={};ub[ub.Little=0]="Little";ub[ub.Big=1]="Big";var vb,wb=vb||={};wb[wb.DenseArray=0]="DenseArray";class zb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}bitWidth(){const a=x(this.g,this.h,4);return a?this.g.u(this.h+a):0}isSigned(){const a=x(this.g,this.h,6);return a?!!(this.g.l[this.h+a]<<24>>24):!1}};function Ab(a){const b=x(a.g,a.h,6);return b?(new zb).i(Pa(a.g,a.h+b),a.g):null}class Bb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}id(){const a=x(this.g,this.h,4);return a?La(this.g,this.h+a):BigInt("0")}isOrdered(){const a=x(this.g,this.h,8);return a?!!(this.g.l[this.h+a]<<24>>24):!1}};class Cb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}key(a){const b=x(this.g,this.h,4);return b?Oa(this.g,this.h+b,a):null}value(a){const b=x(this.g,this.h,6);return b?Oa(this.g,this.h+b,a):null}};class Db{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}unit(){const a=x(this.g,this.h,4);return a?Ka(this.g,this.h+a):Ca.MILLISECOND}};class Eb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}precision(){const a=x(this.g,this.h,4);return a?this.g.u(this.h+a):0}scale(){const a=x(this.g,this.h,6);return a?this.g.u(this.h+a):0}bitWidth(){const a=x(this.g,this.h,8);return a?this.g.u(this.h+a):128}};class Fb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}unit(){const a=x(this.g,this.h,4);return a?Ka(this.g,this.h+a):w.MILLISECOND}};class Gb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}byteWidth(){const a=x(this.g,this.h,4);return a?this.g.u(this.h+a):0}};class Hb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}listSize(){const a=x(this.g,this.h,4);return a?this.g.u(this.h+a):0}};class Ib{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}precision(){const a=x(this.g,this.h,4);return a?Ka(this.g,this.h+a):v.HALF}};class Jb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}unit(){const a=x(this.g,this.h,4);return a?Ka(this.g,this.h+a):Fa.YEAR_MONTH}};class Kb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}keysSorted(){const a=x(this.g,this.h,4);return a?!!(this.g.l[this.h+a]<<24>>24):!1}};class Lb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}unit(){const a=x(this.g,this.h,4);return a?Ka(this.g,this.h+a):w.MILLISECOND}bitWidth(){const a=x(this.g,this.h,6);return a?this.g.u(this.h+a):32}};class Mb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}unit(){const a=x(this.g,this.h,4);return a?Ka(this.g,this.h+a):w.SECOND}timezone(a){const b=x(this.g,this.h,6);return b?Oa(this.g,this.h+b,a):null}};class Nb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}mode(){const a=x(this.g,this.h,4);return a?Ka(this.g,this.h+a):u.Sparse}typeIds(a){const b=x(this.g,this.h,6);return b?this.g.u(Qa(this.g,this.h+b)+4*a):0}};var C,D=C||={};D[D.NONE=0]="NONE";D[D.Null=1]="Null";D[D.Int=2]="Int";D[D.FloatingPoint=3]="FloatingPoint";D[D.Binary=4]="Binary";D[D.Utf8=5]="Utf8";D[D.Bool=6]="Bool";D[D.Decimal=7]="Decimal";D[D.Date=8]="Date";D[D.Time=9]="Time";D[D.Timestamp=10]="Timestamp";D[D.Interval=11]="Interval";D[D.List=12]="List";D[D.Struct_=13]="Struct_";D[D.Union=14]="Union";D[D.FixedSizeBinary=15]="FixedSizeBinary";D[D.FixedSizeList=16]="FixedSizeList";D[D.Map=17]="Map";D[D.Duration=18]="Duration";
D[D.LargeBinary=19]="LargeBinary";D[D.LargeUtf8=20]="LargeUtf8";D[D.LargeList=21]="LargeList";D[D.RunEndEncoded=22]="RunEndEncoded";function Ob(a,b){db(a,4,b.length,4);for(let c=b.length-1;0<=c;c--)bb(a,b[c]);return eb(a)}function Pb(a,b){db(a,4,b.length,4);for(let c=b.length-1;0<=c;c--)bb(a,b[c]);return eb(a)}function Qb(a){const b=x(a.g,a.h,14);return b?Ra(a.g,a.h+b):0}
class Rb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}name(a){const b=x(this.g,this.h,4);return b?Oa(this.g,this.h+b,a):null}nullable(){const a=x(this.g,this.h,6);return a?!!(this.g.l[this.h+a]<<24>>24):!1}type(a){const b=x(this.g,this.h,10);return b?Ma(this.g,a,this.h+b):null}dictionary(a){const b=x(this.g,this.h,12);return b?(a||new Bb).i(Pa(this.g,this.h+b),this.g):null}children(a,b){const c=x(this.g,this.h,14);return c?(b||new Rb).i(Pa(this.g,Qa(this.g,this.h+c)+4*a),
this.g):null}Ca(a){const b=x(this.g,this.h,16);return b?(new Cb).i(Pa(this.g,Qa(this.g,this.h+b)+4*a),this.g):null}Da(){const a=x(this.g,this.h,16);return a?Ra(this.g,this.h+a):0}};function Sb(a,b){db(a,4,b.length,4);for(let c=b.length-1;0<=c;c--)bb(a,b[c]);return eb(a)}function Tb(a,b){db(a,4,b.length,4);for(let c=b.length-1;0<=c;c--)bb(a,b[c]);return eb(a)}function Ub(a){const b=x(a.g,a.h,6);return b?Ra(a.g,a.h+b):0}
class Vb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}fields(a,b){const c=x(this.g,this.h,6);return c?(b||new Rb).i(Pa(this.g,Qa(this.g,this.h+c)+4*a),this.g):null}Ca(a){const b=x(this.g,this.h,8);return b?(new Cb).i(Pa(this.g,Qa(this.g,this.h+b)+4*a),this.g):null}Da(){const a=x(this.g,this.h,8);return a?Ra(this.g,this.h+a):0}features(a){const b=x(this.g,this.h,10);return b?La(this.g,Qa(this.g,this.h+b)+8*a):BigInt(0)}};var Wb,Xb=Wb||={};Xb[Xb.Row=0]="Row";Xb[Xb.Column=1]="Column";var Yb,Zb=Yb||={};Zb[Zb.NONE=0]="NONE";Zb[Zb.SparseTensorIndexCOO=1]="SparseTensorIndexCOO";Zb[Zb.SparseMatrixIndexCSX=2]="SparseMatrixIndexCSX";Zb[Zb.SparseTensorIndexCSF=3]="SparseTensorIndexCSF";var E,$b=E||={};$b[$b.NONE=0]="NONE";$b[$b.Schema=1]="Schema";$b[$b.DictionaryBatch=2]="DictionaryBatch";$b[$b.RecordBatch=3]="RecordBatch";$b[$b.Tensor=4]="Tensor";$b[$b.SparseTensor=5]="SparseTensor";var F,G=F||={};G[G.NONE=0]="NONE";G[G.Null=1]="Null";G[G.Int=2]="Int";G[G.Float=3]="Float";G[G.Binary=4]="Binary";G[G.Utf8=5]="Utf8";G[G.Bool=6]="Bool";G[G.Decimal=7]="Decimal";G[G.Date=8]="Date";G[G.Time=9]="Time";G[G.Timestamp=10]="Timestamp";G[G.Interval=11]="Interval";G[G.List=12]="List";G[G.Struct=13]="Struct";G[G.Union=14]="Union";G[G.FixedSizeBinary=15]="FixedSizeBinary";G[G.FixedSizeList=16]="FixedSizeList";G[G.Map=17]="Map";G[G.Duration=18]="Duration";G[G.LargeBinary=19]="LargeBinary";
G[G.LargeUtf8=20]="LargeUtf8";G[G.Dictionary=-1]="Dictionary";G[G.Int8=-2]="Int8";G[G.Int16=-3]="Int16";G[G.Int32=-4]="Int32";G[G.Int64=-5]="Int64";G[G.Uint8=-6]="Uint8";G[G.Uint16=-7]="Uint16";G[G.Uint32=-8]="Uint32";G[G.Uint64=-9]="Uint64";G[G.Float16=-10]="Float16";G[G.Float32=-11]="Float32";G[G.Float64=-12]="Float64";G[G.DateDay=-13]="DateDay";G[G.DateMillisecond=-14]="DateMillisecond";G[G.TimestampSecond=-15]="TimestampSecond";G[G.TimestampMillisecond=-16]="TimestampMillisecond";
G[G.TimestampMicrosecond=-17]="TimestampMicrosecond";G[G.TimestampNanosecond=-18]="TimestampNanosecond";G[G.TimeSecond=-19]="TimeSecond";G[G.TimeMillisecond=-20]="TimeMillisecond";G[G.TimeMicrosecond=-21]="TimeMicrosecond";G[G.TimeNanosecond=-22]="TimeNanosecond";G[G.DenseUnion=-23]="DenseUnion";G[G.SparseUnion=-24]="SparseUnion";G[G.IntervalDayTime=-25]="IntervalDayTime";G[G.IntervalYearMonth=-26]="IntervalYearMonth";G[G.DurationSecond=-27]="DurationSecond";G[G.DurationMillisecond=-28]="DurationMillisecond";
G[G.DurationMicrosecond=-29]="DurationMicrosecond";G[G.DurationNanosecond=-30]="DurationNanosecond";var ac,bc=ac||={};bc[bc.OFFSET=0]="OFFSET";bc[bc.DATA=1]="DATA";bc[bc.VALIDITY=2]="VALIDITY";bc[bc.TYPE=3]="TYPE";function cc(a){if(null===a)return"null";if(void 0===a)return"undefined";switch(typeof a){case "number":return`${a}`;case "bigint":return`${a}`;case "string":return`"${a}"`}return"function"===typeof a[Symbol.toPrimitive]?a[Symbol.toPrimitive]("string"):ArrayBuffer.isView(a)?a instanceof BigInt64Array||a instanceof BigUint64Array?`[${[...a].map(b=>cc(b))}]`:`[${a}]`:ArrayBuffer.isView(a)?`[${a}]`:JSON.stringify(a,(b,c)=>"bigint"===typeof c?`${c}`:c)}var dc={};dc.valueToString=cc;function H(a){if("bigint"===typeof a&&(a<Number.MIN_SAFE_INTEGER||a>Number.MAX_SAFE_INTEGER))throw new TypeError(`${a} is not safe to convert to a number.`);return Number(a)}function ec(a,b){return H(a/b)+H(a%b)/H(b)};const fc=Symbol.for("isArrowBigNum");function gc(a,...b){return 0===b.length?Object.setPrototypeOf(p(this.TypedArray,a),this.constructor.prototype):Object.setPrototypeOf(new this.TypedArray(a,...b),this.constructor.prototype)}gc.prototype[fc]=!0;gc.prototype.toJSON=function(){return`"${hc(this)}"`};gc.prototype.valueOf=function(a){return ic(this,a)};gc.prototype.toString=function(){return hc(this)};gc.prototype[Symbol.toPrimitive]=function(a="default"){switch(a){case "number":return ic(this);case "default":return jc(this)}return hc(this)};
function kc(...a){return gc.apply(this,a)}function lc(...a){return gc.apply(this,a)}function mc(...a){return gc.apply(this,a)}Object.setPrototypeOf(kc.prototype,Object.create(Int32Array.prototype));Object.setPrototypeOf(lc.prototype,Object.create(Uint32Array.prototype));Object.setPrototypeOf(mc.prototype,Object.create(Uint32Array.prototype));Object.assign(kc.prototype,gc.prototype,{constructor:kc,signed:!0,TypedArray:Int32Array,BigIntArray:BigInt64Array});
Object.assign(lc.prototype,gc.prototype,{constructor:lc,signed:!1,TypedArray:Uint32Array,BigIntArray:BigUint64Array});Object.assign(mc.prototype,gc.prototype,{constructor:mc,signed:!0,TypedArray:Uint32Array,BigIntArray:BigUint64Array});const nc=BigInt(4294967296)*BigInt(4294967296)-BigInt(1);
function ic(a,b){const {buffer:c,byteOffset:d,byteLength:e,signed:f}=a,h=new BigUint64Array(c,d,e/8),k=f&&h.at(-1)&BigInt(1)<<BigInt(63);a=BigInt(0);let n=0;if(k){for(var q of h)a|=(q^nc)*(BigInt(1)<<BigInt(64*n++));a*=BigInt(-1);a-=BigInt(1)}else for(const B of h)a|=B*(BigInt(1)<<BigInt(64*n++));return"number"===typeof b?(b=BigInt(Math.pow(10,b)),q=a%b,H(a/b)+H(q)/H(b)):H(a)}
function hc(a){if(8===a.byteLength)return`${(new a.BigIntArray(a.buffer,a.byteOffset,1))[0]}`;if(!a.signed)return oc(a);let b=new Uint16Array(a.buffer,a.byteOffset,a.byteLength/2);if(0<=(new Int16Array([b.at(-1)]))[0])return oc(a);b=b.slice();a=1;for(let c=0;c<b.length;c++){const d=b[c];b[c]=~d+a;a&=0===d?1:0}return`-${oc(b)}`}function jc(a){return 8===a.byteLength?(new a.BigIntArray(a.buffer,a.byteOffset,1))[0]:hc(a)}
function oc(a){let b="";const c=new Uint32Array(2);a=new Uint16Array(a.buffer,a.byteOffset,a.byteLength/2);const d=new Uint32Array((a=(new Uint16Array(a)).reverse()).buffer);let e;const f=a.length-1;do{for(c[0]=a[e=0];e<f;)a[e++]=c[1]=c[0]/10,c[0]=(c[0]-10*c[1]<<16)+a[e];a[e]=c[1]=c[0]/10;c[0]-=10*c[1];b=`${c[0]}${b}`}while(d[0]||d[1]||d[2]||d[3]);return b??"0"}
class pc{static new(a,b){switch(b){case !0:return new kc(a);case !1:return new lc(a)}switch(a.constructor){case Int8Array:case Int16Array:case Int32Array:case BigInt64Array:return new kc(a)}return 16===a.byteLength?new mc(a):new lc(a)}static Zb(a){return new kc(a)}constructor(a,b){return pc.new(a,b)}}var qc={};qc.BN=pc;qc.bigNumToBigInt=jc;qc.bigNumToNumber=ic;qc.bigNumToString=hc;qc.isArrowBigNumSymbol=fc;class I{static isNull(a){return a?.typeId===F.Null}static isInt(a){return a?.typeId===F.Int}static isFloat(a){return a?.typeId===F.Float}static isBinary(a){return a?.typeId===F.Binary}static isLargeBinary(a){return a?.typeId===F.LargeBinary}static isUtf8(a){return a?.typeId===F.Utf8}static isLargeUtf8(a){return a?.typeId===F.LargeUtf8}static isBool(a){return a?.typeId===F.Bool}static isDecimal(a){return a?.typeId===F.Decimal}static isDate(a){return a?.typeId===F.Date}static isTime(a){return a?.typeId===
F.Time}static isTimestamp(a){return a?.typeId===F.Timestamp}static isInterval(a){return a?.typeId===F.Interval}static isDuration(a){return a?.typeId===F.Duration}static isList(a){return a?.typeId===F.List}static isStruct(a){return a?.typeId===F.Struct}static isUnion(a){return a?.typeId===F.Union}static isFixedSizeBinary(a){return a?.typeId===F.FixedSizeBinary}static isFixedSizeList(a){return a?.typeId===F.FixedSizeList}static isMap(a){return a?.typeId===F.Map}static isDictionary(a){return a?.typeId===
F.Dictionary}static isDenseUnion(a){return I.isUnion(a)&&a.mode===u.Dense}static isSparseUnion(a){return I.isUnion(a)&&a.mode===u.Sparse}constructor(a){this.typeId=a}}var rc=Symbol.toStringTag,sc,tc=I.prototype;tc.children=null;tc.ArrayType=Array;tc.OffsetArrayType=Int32Array;sc=tc[Symbol.toStringTag]="DataType";I[rc]=sc;class uc extends I{constructor(){super(F.Null)}toString(){return"Null"}}uc[Symbol.toStringTag]=uc.prototype[Symbol.toStringTag]="Null";
class vc extends I{constructor(a,b){super(F.Int);this.isSigned=a;this.bitWidth=b}get ArrayType(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:return this.isSigned?Int32Array:Uint32Array;case 64:return this.isSigned?BigInt64Array:BigUint64Array}throw Error(`Unrecognized ${this[Symbol.toStringTag]} type`);}toString(){return`${this.isSigned?"I":"Ui"}nt${this.bitWidth}`}}var wc=Symbol.toStringTag,xc,yc=vc.prototype;
yc.isSigned=null;yc.bitWidth=null;xc=yc[Symbol.toStringTag]="Int";vc[wc]=xc;class zc extends vc{constructor(){super(!0,8)}get ArrayType(){return Int8Array}}class Ac extends vc{constructor(){super(!0,16)}get ArrayType(){return Int16Array}}class Bc extends vc{constructor(){super(!0,32)}get ArrayType(){return Int32Array}}class Cc extends vc{constructor(){super(!0,64)}get ArrayType(){return BigInt64Array}}class Dc extends vc{constructor(){super(!1,8)}get ArrayType(){return Uint8Array}}
class Ec extends vc{constructor(){super(!1,16)}get ArrayType(){return Uint16Array}}class Fc extends vc{constructor(){super(!1,32)}get ArrayType(){return Uint32Array}}class Gc extends vc{constructor(){super(!1,64)}get ArrayType(){return BigUint64Array}}Object.defineProperty(zc.prototype,"ArrayType",{value:Int8Array});Object.defineProperty(Ac.prototype,"ArrayType",{value:Int16Array});Object.defineProperty(Bc.prototype,"ArrayType",{value:Int32Array});Object.defineProperty(Cc.prototype,"ArrayType",{value:BigInt64Array});
Object.defineProperty(Dc.prototype,"ArrayType",{value:Uint8Array});Object.defineProperty(Ec.prototype,"ArrayType",{value:Uint16Array});Object.defineProperty(Fc.prototype,"ArrayType",{value:Uint32Array});Object.defineProperty(Gc.prototype,"ArrayType",{value:BigUint64Array});
class Hc extends I{constructor(a){super(F.Float);this.precision=a}get ArrayType(){switch(this.precision){case v.HALF:return Uint16Array;case v.SINGLE:return Float32Array;case v.DOUBLE:return Float64Array}throw Error(`Unrecognized ${this[Symbol.toStringTag]} type`);}toString(){return`Float${this.precision<<5||16}`}}var Ic=Symbol.toStringTag,Jc,Kc=Hc.prototype;Kc.precision=null;Jc=Kc[Symbol.toStringTag]="Float";Hc[Ic]=Jc;class Lc extends Hc{constructor(){super(v.HALF)}}
class Mc extends Hc{constructor(){super(v.SINGLE)}}class Nc extends Hc{constructor(){super(v.DOUBLE)}}Object.defineProperty(Lc.prototype,"ArrayType",{value:Uint16Array});Object.defineProperty(Mc.prototype,"ArrayType",{value:Float32Array});Object.defineProperty(Nc.prototype,"ArrayType",{value:Float64Array});class Oc extends I{constructor(){super(F.Binary)}toString(){return"Binary"}}var Pc=Symbol.toStringTag,Qc,Rc=Oc.prototype;Rc.ArrayType=Uint8Array;Qc=Rc[Symbol.toStringTag]="Binary";Oc[Pc]=Qc;
class Sc extends I{constructor(){super(F.LargeBinary)}toString(){return"LargeBinary"}}var Tc=Symbol.toStringTag,Uc,Vc=Sc.prototype;Vc.ArrayType=Uint8Array;Vc.OffsetArrayType=BigInt64Array;Uc=Vc[Symbol.toStringTag]="LargeBinary";Sc[Tc]=Uc;class Wc extends I{constructor(){super(F.Utf8)}toString(){return"Utf8"}}var Xc=Symbol.toStringTag,Yc,Zc=Wc.prototype;Zc.ArrayType=Uint8Array;Yc=Zc[Symbol.toStringTag]="Utf8";Wc[Xc]=Yc;
class $c extends I{constructor(){super(F.LargeUtf8)}toString(){return"LargeUtf8"}}var ad=Symbol.toStringTag,bd,cd=$c.prototype;cd.ArrayType=Uint8Array;cd.OffsetArrayType=BigInt64Array;bd=cd[Symbol.toStringTag]="LargeUtf8";$c[ad]=bd;class dd extends I{constructor(){super(F.Bool)}toString(){return"Bool"}}var ed=Symbol.toStringTag,fd,gd=dd.prototype;gd.ArrayType=Uint8Array;fd=gd[Symbol.toStringTag]="Bool";dd[ed]=fd;
class hd extends I{constructor(a,b,c=128){super(F.Decimal);this.scale=a;this.precision=b;this.bitWidth=c}toString(){return`Decimal[${this.precision}e${0<this.scale?"+":""}${this.scale}]`}}var id=Symbol.toStringTag,jd,kd=hd.prototype;kd.scale=null;kd.precision=null;kd.ArrayType=Uint32Array;jd=kd[Symbol.toStringTag]="Decimal";hd[id]=jd;
class ld extends I{constructor(a){super(F.Date);this.unit=a}toString(){return`Date${32*(this.unit+1)}<${Ca[this.unit]}>`}get ArrayType(){return this.unit===Ca.DAY?Int32Array:BigInt64Array}}var md=Symbol.toStringTag,nd,od=ld.prototype;od.unit=null;nd=od[Symbol.toStringTag]="Date";ld[md]=nd;class pd extends ld{constructor(){super(Ca.DAY)}}class qd extends ld{constructor(){super(Ca.MILLISECOND)}}
class rd extends I{constructor(a,b){super(F.Time);this.unit=a;this.bitWidth=b}toString(){return`Time${this.bitWidth}<${w[this.unit]}>`}get ArrayType(){switch(this.bitWidth){case 32:return Int32Array;case 64:return BigInt64Array}throw Error(`Unrecognized ${this[Symbol.toStringTag]} type`);}}var sd=Symbol.toStringTag,td,ud=rd.prototype;ud.unit=null;ud.bitWidth=null;td=ud[Symbol.toStringTag]="Time";rd[sd]=td;class vd extends rd{constructor(){super(w.SECOND,32)}}
class wd extends rd{constructor(){super(w.MILLISECOND,32)}}class xd extends rd{constructor(){super(w.MICROSECOND,64)}}class yd extends rd{constructor(){super(w.NANOSECOND,64)}}class zd extends I{constructor(a,b){super(F.Timestamp);this.unit=a;this.timezone=b}toString(){return`Timestamp<${w[this.unit]}${this.timezone?`, ${this.timezone}`:""}>`}}var Ad=Symbol.toStringTag,Bd,Cd=zd.prototype;Cd.unit=null;Cd.timezone=null;Cd.ArrayType=BigInt64Array;Bd=Cd[Symbol.toStringTag]="Timestamp";zd[Ad]=Bd;
class Dd extends zd{constructor(a){super(w.SECOND,a)}}class Ed extends zd{constructor(a){super(w.MILLISECOND,a)}}class Fd extends zd{constructor(a){super(w.MICROSECOND,a)}}class Gd extends zd{constructor(a){super(w.NANOSECOND,a)}}class Hd extends I{constructor(a){super(F.Interval);this.unit=a}toString(){return`Interval<${Fa[this.unit]}>`}}var Id=Symbol.toStringTag,Jd,Kd=Hd.prototype;Kd.unit=null;Kd.ArrayType=Int32Array;Jd=Kd[Symbol.toStringTag]="Interval";Hd[Id]=Jd;
class Ld extends Hd{constructor(){super(Fa.DAY_TIME)}}class Md extends Hd{constructor(){super(Fa.YEAR_MONTH)}}class Nd extends I{constructor(a){super(F.Duration);this.unit=a}toString(){return`Duration<${w[this.unit]}>`}}var Od=Symbol.toStringTag,Pd,Qd=Nd.prototype;Qd.unit=null;Qd.ArrayType=BigInt64Array;Pd=Qd[Symbol.toStringTag]="Duration";Nd[Od]=Pd;class Rd extends Nd{constructor(){super(w.SECOND)}}class Sd extends Nd{constructor(){super(w.MILLISECOND)}}
class Td extends Nd{constructor(){super(w.MICROSECOND)}}class Ud extends Nd{constructor(){super(w.NANOSECOND)}}class Vd extends I{constructor(a){super(F.List);this.children=[a]}toString(){return`List<${this.valueType}>`}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}}var Wd=Symbol.toStringTag,Xd,Yd=Vd.prototype;Yd.children=null;Xd=Yd[Symbol.toStringTag]="List";Vd[Wd]=Xd;
class J extends I{constructor(a){super(F.Struct);this.children=a}toString(){return`Struct<{${this.children.map(a=>`${a.name}:${a.type}`).join(", ")}}>`}}var Zd=Symbol.toStringTag,$d,ae=J.prototype;ae.children=null;$d=ae[Symbol.toStringTag]="Struct";J[Zd]=$d;
class be extends I{constructor(a,b,c){super(F.Union);this.mode=a;this.children=c;this.typeIds=b=Int32Array.from(b);this.typeIdToChildIndex=b.reduce((d,e,f)=>(d[e]=f)&&d||d,Object.create(null))}toString(){return`${this[Symbol.toStringTag]}<${this.children.map(a=>`${a.type}`).join(" | ")}>`}}var ce=Symbol.toStringTag,de,ee=be.prototype;ee.mode=null;ee.typeIds=null;ee.children=null;ee.typeIdToChildIndex=null;ee.ArrayType=Int8Array;de=ee[Symbol.toStringTag]="Union";be[ce]=de;
class fe extends be{constructor(a,b){super(u.Dense,a,b)}}class ge extends be{constructor(a,b){super(u.Sparse,a,b)}}class he extends I{constructor(a){super(F.FixedSizeBinary);this.byteWidth=a}toString(){return`FixedSizeBinary[${this.byteWidth}]`}}var ie=Symbol.toStringTag,je,ke=he.prototype;ke.byteWidth=null;ke.ArrayType=Uint8Array;je=ke[Symbol.toStringTag]="FixedSizeBinary";he[ie]=je;
class le extends I{constructor(a,b){super(F.FixedSizeList);this.listSize=a;this.children=[b]}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}toString(){return`FixedSizeList[${this.listSize}]<${this.valueType}>`}}var me=Symbol.toStringTag,ne,oe=le.prototype;oe.children=null;oe.listSize=null;ne=oe[Symbol.toStringTag]="FixedSizeList";le[me]=ne;
class pe extends I{constructor(a,b=!1){super(F.Map);this.children=[a];this.keysSorted=b;a&&(a.name="entries",a?.type?.children&&((b=a?.type?.children[0])&&(b.name="key"),(a=a?.type?.children[1])&&(a.name="value")))}get keyType(){return this.children[0].type.children[0].type}get valueType(){return this.children[0].type.children[1].type}get childType(){return this.children[0].type}toString(){return`Map<{${this.children[0].type.children.map(a=>`${a.name}:${a.type}`).join(", ")}}>`}}
var qe=Symbol.toStringTag,re,se=pe.prototype;se.children=null;se.keysSorted=null;re=se[Symbol.toStringTag]="Map_";pe[qe]=re;const te=(a=>()=>++a)(-1);class ue extends I{constructor(a,b,c,d){super(F.Dictionary);this.indices=b;this.dictionary=a;this.isOrdered=d||!1;this.id=null==c?te():H(c)}get children(){return this.dictionary.children}get valueType(){return this.dictionary}get ArrayType(){return this.dictionary.ArrayType}toString(){return`Dictionary<${this.indices}, ${this.dictionary}>`}}
var ve=Symbol.toStringTag,we,xe=ue.prototype;xe.id=null;xe.indices=null;xe.isOrdered=null;xe.dictionary=null;we=xe[Symbol.toStringTag]="Dictionary";ue[ve]=we;function ye(a){switch(a.typeId){case F.Decimal:return a.bitWidth/32;case F.Interval:return 1+a.unit;case F.FixedSizeList:return a.listSize;case F.FixedSizeBinary:return a.byteWidth;default:return 1}};class ze{visitMany(a,...b){return a.map((c,d)=>this.visit(c,...b.map(e=>e[d])))}visit(...a){return this.getVisitFn(a[0],!1).apply(this,a)}getVisitFn(a,b=!0){return Ae(this,a,b)}getVisitFnByTypeId(a,b=!0){return Be(this,a,b)}visitNull(){return null}visitBool(){return null}visitInt(){return null}visitFloat(){return null}visitUtf8(){return null}visitLargeUtf8(){return null}visitBinary(){return null}visitLargeBinary(){return null}visitFixedSizeBinary(){return null}visitDate(){return null}visitTimestamp(){return null}visitTime(){return null}visitDecimal(){return null}visitList(){return null}visitStruct(){return null}visitUnion(){return null}visitDictionary(){return null}visitInterval(){return null}visitDuration(){return null}visitFixedSizeList(){return null}visitMap(){return null}}
function Ae(a,b,c=!0){return"number"===typeof b?Be(a,b,c):"string"===typeof b&&b in F?Be(a,F[b],c):b&&b instanceof I?Be(a,Ce(b),c):b?.type&&b.type instanceof I?Be(a,Ce(b.type),c):Be(a,F.NONE,c)}
function Be(a,b,c=!0){let d=null;switch(b){case F.Null:d=a.visitNull;break;case F.Bool:d=a.visitBool;break;case F.Int:d=a.visitInt;break;case F.Int8:d=a.visitInt8||a.visitInt;break;case F.Int16:d=a.visitInt16||a.visitInt;break;case F.Int32:d=a.visitInt32||a.visitInt;break;case F.Int64:d=a.visitInt64||a.visitInt;break;case F.Uint8:d=a.visitUint8||a.visitInt;break;case F.Uint16:d=a.visitUint16||a.visitInt;break;case F.Uint32:d=a.visitUint32||a.visitInt;break;case F.Uint64:d=a.visitUint64||a.visitInt;
break;case F.Float:d=a.visitFloat;break;case F.Float16:d=a.visitFloat16||a.visitFloat;break;case F.Float32:d=a.visitFloat32||a.visitFloat;break;case F.Float64:d=a.visitFloat64||a.visitFloat;break;case F.Utf8:d=a.visitUtf8;break;case F.LargeUtf8:d=a.visitLargeUtf8;break;case F.Binary:d=a.visitBinary;break;case F.LargeBinary:d=a.visitLargeBinary;break;case F.FixedSizeBinary:d=a.visitFixedSizeBinary;break;case F.Date:d=a.visitDate;break;case F.DateDay:d=a.visitDateDay||a.visitDate;break;case F.DateMillisecond:d=
a.visitDateMillisecond||a.visitDate;break;case F.Timestamp:d=a.visitTimestamp;break;case F.TimestampSecond:d=a.visitTimestampSecond||a.visitTimestamp;break;case F.TimestampMillisecond:d=a.visitTimestampMillisecond||a.visitTimestamp;break;case F.TimestampMicrosecond:d=a.visitTimestampMicrosecond||a.visitTimestamp;break;case F.TimestampNanosecond:d=a.visitTimestampNanosecond||a.visitTimestamp;break;case F.Time:d=a.visitTime;break;case F.TimeSecond:d=a.visitTimeSecond||a.visitTime;break;case F.TimeMillisecond:d=
a.visitTimeMillisecond||a.visitTime;break;case F.TimeMicrosecond:d=a.visitTimeMicrosecond||a.visitTime;break;case F.TimeNanosecond:d=a.visitTimeNanosecond||a.visitTime;break;case F.Decimal:d=a.visitDecimal;break;case F.List:d=a.visitList;break;case F.Struct:d=a.visitStruct;break;case F.Union:d=a.visitUnion;break;case F.DenseUnion:d=a.visitDenseUnion||a.visitUnion;break;case F.SparseUnion:d=a.visitSparseUnion||a.visitUnion;break;case F.Dictionary:d=a.visitDictionary;break;case F.Interval:d=a.visitInterval;
break;case F.IntervalDayTime:d=a.visitIntervalDayTime||a.visitInterval;break;case F.IntervalYearMonth:d=a.visitIntervalYearMonth||a.visitInterval;break;case F.Duration:d=a.visitDuration;break;case F.DurationSecond:d=a.visitDurationSecond||a.visitDuration;break;case F.DurationMillisecond:d=a.visitDurationMillisecond||a.visitDuration;break;case F.DurationMicrosecond:d=a.visitDurationMicrosecond||a.visitDuration;break;case F.DurationNanosecond:d=a.visitDurationNanosecond||a.visitDuration;break;case F.FixedSizeList:d=
a.visitFixedSizeList;break;case F.Map:d=a.visitMap}if("function"===typeof d)return d;if(!c)return()=>null;throw Error(`Unrecognized type '${F[b]}'`);}
function Ce(a){switch(a.typeId){case F.Null:return F.Null;case F.Int:const b=a.isSigned;switch(a.bitWidth){case 8:return b?F.Int8:F.Uint8;case 16:return b?F.Int16:F.Uint16;case 32:return b?F.Int32:F.Uint32;case 64:return b?F.Int64:F.Uint64}return F.Int;case F.Float:switch(a.precision){case v.HALF:return F.Float16;case v.SINGLE:return F.Float32;case v.DOUBLE:return F.Float64}return F.Float;case F.Binary:return F.Binary;case F.LargeBinary:return F.LargeBinary;case F.Utf8:return F.Utf8;case F.LargeUtf8:return F.LargeUtf8;
case F.Bool:return F.Bool;case F.Decimal:return F.Decimal;case F.Time:switch(a.unit){case w.SECOND:return F.TimeSecond;case w.MILLISECOND:return F.TimeMillisecond;case w.MICROSECOND:return F.TimeMicrosecond;case w.NANOSECOND:return F.TimeNanosecond}return F.Time;case F.Timestamp:switch(a.unit){case w.SECOND:return F.TimestampSecond;case w.MILLISECOND:return F.TimestampMillisecond;case w.MICROSECOND:return F.TimestampMicrosecond;case w.NANOSECOND:return F.TimestampNanosecond}return F.Timestamp;case F.Date:switch(a.unit){case Ca.DAY:return F.DateDay;
case Ca.MILLISECOND:return F.DateMillisecond}return F.Date;case F.Interval:switch(a.unit){case Fa.DAY_TIME:return F.IntervalDayTime;case Fa.YEAR_MONTH:return F.IntervalYearMonth}return F.Interval;case F.Duration:switch(a.unit){case w.SECOND:return F.DurationSecond;case w.MILLISECOND:return F.DurationMillisecond;case w.MICROSECOND:return F.DurationMicrosecond;case w.NANOSECOND:return F.DurationNanosecond}return F.Duration;case F.Map:return F.Map;case F.List:return F.List;case F.Struct:return F.Struct;
case F.Union:switch(a.mode){case u.Dense:return F.DenseUnion;case u.Sparse:return F.SparseUnion}return F.Union;case F.FixedSizeBinary:return F.FixedSizeBinary;case F.FixedSizeList:return F.FixedSizeList;case F.Dictionary:return F.Dictionary}throw Error(`Unrecognized type '${F[a.typeId]}'`);}g=ze.prototype;g.visitInt8=null;g.visitInt16=null;g.visitInt32=null;g.visitInt64=null;g.visitUint8=null;g.visitUint16=null;g.visitUint32=null;g.visitUint64=null;g.visitFloat16=null;g.visitFloat32=null;
g.visitFloat64=null;g.visitDateDay=null;g.visitDateMillisecond=null;g.visitTimestampSecond=null;g.visitTimestampMillisecond=null;g.visitTimestampMicrosecond=null;g.visitTimestampNanosecond=null;g.visitTimeSecond=null;g.visitTimeMillisecond=null;g.visitTimeMicrosecond=null;g.visitTimeNanosecond=null;g.visitDenseUnion=null;g.visitSparseUnion=null;g.visitIntervalDayTime=null;g.visitIntervalYearMonth=null;g.visitDuration=null;g.visitDurationSecond=null;g.visitDurationMillisecond=null;
g.visitDurationMicrosecond=null;g.visitDurationNanosecond=null;const De=new Float64Array(1),Ee=new Uint32Array(De.buffer);function Fe(a){const b=(a&31744)>>10,c=(a&1023)/1024;a=(-1)**((a&32768)>>15);switch(b){case 31:return a*(c?Number.NaN:1/0);case 0:return a*(c?6.103515625E-5*c:0)}return a*2**(b-15)*(1+c)}
function Ge(a){if(a!==a)return 32256;De[0]=a;a=(Ee[1]&2147483648)>>16&65535;let b=Ee[1]&2146435072,c=0;1089470464<=b?0<Ee[0]?b=31744:(b=(b&2080374784)>>16,c=(Ee[1]&1048575)>>10):1056964608>=b?(c=1048576+(Ee[1]&1048575),c=1048576+(c<<(b>>20)-998)>>21,b=0):(b=b-1056964608>>10,c=(Ee[1]&1048575)+512>>10);return a|b|c&65535}var He={};He.float64ToUint16=Ge;He.uint16ToFloat64=Fe;class Ie extends ze{}function K(a){return(b,c,d)=>{if(b.setValid(c,null!=d))return a(b,c,d)}}
const Je=(a,b,c,d)=>{if(c+1<b.length){const e=H(b[c]);b=H(b[c+1]);a.set(d.subarray(0,b-e),e)}},Ke=({values:a},b,c)=>{a[b]=c},Le=({values:a},b,c)=>{a[b]=c},Me=({values:a},b,c)=>{a[b]=Ge(c)},Ne=({values:a},b,c)=>{a[b]=Math.floor(c.valueOf()/864E5)},Oe=({values:a},b,c)=>{a[b]=BigInt(c)},Pe=({stride:a,values:b},c,d)=>{b.set(d.subarray(0,a),a*c)},Qe=({values:a,valueOffsets:b},c,d)=>Je(a,b,c,d),Re=({values:a,valueOffsets:b},c,d)=>Je(a,b,c,ca.encode(d)),Se=(a,b,c)=>{a.type.unit===Ca.DAY?Ne(a,b,c):Oe(a,b,
c)},Te=({values:a},b,c)=>{a[b]=BigInt(c/1E3)},Ue=({values:a},b,c)=>{a[b]=BigInt(c)},Ve=({values:a},b,c)=>{a[b]=BigInt(1E3*c)},We=({values:a},b,c)=>{a[b]=BigInt(1E6*c)},Xe=(a,b,c)=>{switch(a.type.unit){case w.SECOND:return Te(a,b,c);case w.MILLISECOND:return Ue(a,b,c);case w.MICROSECOND:return Ve(a,b,c);case w.NANOSECOND:return We(a,b,c)}},Ye=({values:a},b,c)=>{a[b]=c},Ze=({values:a},b,c)=>{a[b]=c},$e=({values:a},b,c)=>{a[b]=c},af=({values:a},b,c)=>{a[b]=c},bf=(a,b,c)=>{switch(a.type.unit){case w.SECOND:return Ye(a,
b,c);case w.MILLISECOND:return Ze(a,b,c);case w.MICROSECOND:return $e(a,b,c);case w.NANOSECOND:return af(a,b,c)}},cf=({values:a,stride:b},c,d)=>{a.set(d.subarray(0,b),b*c)},df=(a,b)=>(c,d,e,f)=>d&&c(d,a,b[f]),ef=(a,b)=>(c,d,e,f)=>d&&c(d,a,b.get(f)),ff=(a,b)=>(c,d,e)=>d&&c(d,a,b.get(e.name)),gf=(a,b)=>(c,d,e)=>d&&c(d,a,b[e.name]),jf=(a,b,c)=>{hf.visit(a.children[a.type.typeIdToChildIndex[a.typeIds[b]]],a.valueOffsets[b],c)},kf=(a,b,c)=>{hf.visit(a.children[a.type.typeIdToChildIndex[a.typeIds[b]]],
b,c)},nf=(a,b,c)=>{a.type.unit===Fa.DAY_TIME?lf(a,b,c):mf(a,b,c)},lf=({values:a},b,c)=>{a.set(c.subarray(0,2),2*b)},mf=({values:a},b,c)=>{a[b]=12*c[0]+c[1]%12},of=({values:a},b,c)=>{a[b]=c},pf=({values:a},b,c)=>{a[b]=c},qf=({values:a},b,c)=>{a[b]=c},rf=({values:a},b,c)=>{a[b]=c},sf=(a,b,c)=>{switch(a.type.unit){case w.SECOND:return of(a,b,c);case w.MILLISECOND:return pf(a,b,c);case w.MICROSECOND:return qf(a,b,c);case w.NANOSECOND:return rf(a,b,c)}};g=Ie.prototype;
g.visitBool=K(({offset:a,values:b},c,d)=>{a+=c;d?b[a>>3]|=1<<a%8:b[a>>3]&=~(1<<a%8)});g.visitInt=K(Ke);g.visitInt8=K(Ke);g.visitInt16=K(Ke);g.visitInt32=K(Ke);g.visitInt64=K(Ke);g.visitUint8=K(Ke);g.visitUint16=K(Ke);g.visitUint32=K(Ke);g.visitUint64=K(Ke);g.visitFloat=K((a,b,c)=>{switch(a.type.precision){case v.HALF:return Me(a,b,c);case v.SINGLE:case v.DOUBLE:return Le(a,b,c)}});g.visitFloat16=K(Me);g.visitFloat32=K(Le);g.visitFloat64=K(Le);g.visitUtf8=K(Re);g.visitLargeUtf8=K(Re);
g.visitBinary=K(Qe);g.visitLargeBinary=K(Qe);g.visitFixedSizeBinary=K(Pe);g.visitDate=K(Se);g.visitDateDay=K(Ne);g.visitDateMillisecond=K(Oe);g.visitTimestamp=K(Xe);g.visitTimestampSecond=K(Te);g.visitTimestampMillisecond=K(Ue);g.visitTimestampMicrosecond=K(Ve);g.visitTimestampNanosecond=K(We);g.visitTime=K(bf);g.visitTimeSecond=K(Ye);g.visitTimeMillisecond=K(Ze);g.visitTimeMicrosecond=K($e);g.visitTimeNanosecond=K(af);g.visitDecimal=K(cf);
g.visitList=K((a,b,c)=>{const d=a.children[0];a=a.valueOffsets;const e=hf.getVisitFn(d);if(Array.isArray(c))for(let f=-1,h=a[b],k=a[b+1];h<k;)e(d,h++,c[++f]);else for(let f=-1,h=a[b],k=a[b+1];h<k;)e(d,h++,c.get(++f))});g.visitStruct=K((a,b,c)=>{const d=a.type.children.map(f=>hf.getVisitFn(f.type)),e=c instanceof Map?ff(b,c):c instanceof L?ef(b,c):Array.isArray(c)?df(b,c):gf(b,c);a.type.children.forEach((f,h)=>e(d[h],a.children[h],f,h))});
g.visitUnion=K((a,b,c)=>{a.type.mode===u.Dense?jf(a,b,c):kf(a,b,c)});g.visitDenseUnion=K(jf);g.visitSparseUnion=K(kf);g.visitDictionary=K((a,b,c)=>{a.dictionary?.set(a.values[b],c)});g.visitInterval=K(nf);g.visitIntervalDayTime=K(lf);g.visitIntervalYearMonth=K(mf);g.visitDuration=K(sf);g.visitDurationSecond=K(of);g.visitDurationMillisecond=K(pf);g.visitDurationMicrosecond=K(qf);g.visitDurationNanosecond=K(rf);
g.visitFixedSizeList=K((a,b,c)=>{const d=a.stride;a=a.children[0];const e=hf.getVisitFn(a);if(Array.isArray(c))for(let f=-1,h=b*d;++f<d;)e(a,h+f,c[f]);else for(let f=-1,h=b*d;++f<d;)e(a,h+f,c.get(f))});g.visitMap=K((a,b,c)=>{const d=a.children[0],e=a.valueOffsets;a=hf.getVisitFn(d);let {[b]:f,[b+1]:h}=e;b=c instanceof Map?c.entries():Object.entries(c);for(const k of b)if(a(d,f,k),++f>=h)break});const hf=new Ie;const tf=Symbol.for("parent"),uf=Symbol.for("rowIndex");
class vf{constructor(a,b){this[tf]=a;this[uf]=b;return new Proxy(this,wf)}toArray(){return Object.values(this.toJSON())}toJSON(){const a=this[uf],b=this[tf],c=b.type.children,d={};for(let e=-1,f=c.length;++e<f;)d[c[e].name]=xf.visit(b.children[e],a);return d}toString(){return`{${[...this].map(([a,b])=>`${cc(a)}: ${cc(b)}`).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}[Symbol.iterator](){return new yf(this[tf],this[uf])}}
class yf{constructor(a,b){this.ub=0;this.children=a.children;this.rowIndex=b;this.tb=a.type.children;this.numChildren=this.tb.length}[Symbol.iterator](){return this}next(){const a=this.ub;return a<this.numChildren?(this.ub=a+1,{done:!1,value:[this.tb[a].name,xf.visit(this.children[a],this.rowIndex)]}):{done:!0,value:null}}}
Object.defineProperties(vf.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[tf]:{writable:!0,enumerable:!1,configurable:!1,value:null},[uf]:{writable:!0,enumerable:!1,configurable:!1,value:-1}});
class zf{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(a){return a[tf].type.children.map(b=>b.name)}has(a,b){return a[tf].type.children.some(c=>c.name===b)}getOwnPropertyDescriptor(a,b){if(a[tf].type.children.some(c=>c.name===b))return{writable:!0,enumerable:!0,configurable:!0}}get(a,b){if(Reflect.has(a,b))return a[b];var c=a[tf].type.children.findIndex(d=>d.name===b);if(-1!==c)return c=xf.visit(a[tf].children[c],a[uf]),Reflect.set(a,b,c),c}set(a,b,c){const d=
a[tf].type.children.findIndex(e=>e.name===b);return-1!==d?(hf.visit(a[tf].children[d],a[uf],c),Reflect.set(a,b,c)):Reflect.has(a,b)||"symbol"===typeof b?Reflect.set(a,b,c):!1}}const wf=new zf;class Af extends ze{}function M(a){return(b,c)=>b.getValid(c)?a(b,c):null}
const Bf=(a,b,c)=>{if(c+1>=b.length)return null;const d=H(b[c]);b=H(b[c+1]);return a.subarray(d,b)},Cf=({values:a},b)=>864E5*a[b],Df=({values:a},b)=>H(a[b]),Ef=({stride:a,values:b},c)=>b[a*c],Ff=({values:a},b)=>a[b],Gf=({values:a,valueOffsets:b},c)=>Bf(a,b,c),Hf=({values:a,valueOffsets:b},c)=>{a=Bf(a,b,c);return null!==a?aa.decode(a):null},If=({values:a},b)=>1E3*H(a[b]),Jf=({values:a},b)=>H(a[b]),Kf=({values:a},b)=>ec(a[b],BigInt(1E3)),Lf=({values:a},b)=>ec(a[b],BigInt(1E6)),Mf=({values:a},b)=>a[b],
Nf=({values:a},b)=>a[b],Of=({values:a},b)=>a[b],Pf=({values:a},b)=>a[b],Qf=(a,b)=>xf.visit(a.children[a.type.typeIdToChildIndex[a.typeIds[b]]],a.valueOffsets[b]),Rf=(a,b)=>xf.visit(a.children[a.type.typeIdToChildIndex[a.typeIds[b]]],b),Sf=({values:a},b)=>a.subarray(2*b,2*(b+1)),Tf=({values:a},b)=>{a=a[b];b=new Int32Array(2);b[0]=Math.trunc(a/12);b[1]=Math.trunc(a%12);return b},Uf=({values:a},b)=>a[b],Vf=({values:a},b)=>a[b],Wf=({values:a},b)=>a[b],Xf=({values:a},b)=>a[b];g=Af.prototype;
g.visitNull=M(()=>null);g.visitBool=M(({offset:a,values:b},c)=>{a+=c;return 0!==(b[a>>3]&1<<a%8)});g.visitInt=M(({values:a},b)=>a[b]);g.visitInt8=M(Ef);g.visitInt16=M(Ef);g.visitInt32=M(Ef);g.visitInt64=M(Ff);g.visitUint8=M(Ef);g.visitUint16=M(Ef);g.visitUint32=M(Ef);g.visitUint64=M(Ff);g.visitFloat=M(({type:a,values:b},c)=>a.precision!==v.HALF?b[c]:Fe(b[c]));g.visitFloat16=M(({stride:a,values:b},c)=>Fe(b[a*c]));g.visitFloat32=M(Ef);g.visitFloat64=M(Ef);g.visitUtf8=M(Hf);g.visitLargeUtf8=M(Hf);
g.visitBinary=M(Gf);g.visitLargeBinary=M(Gf);g.visitFixedSizeBinary=M(({stride:a,values:b},c)=>b.subarray(a*c,a*(c+1)));g.visitDate=M((a,b)=>a.type.unit===Ca.DAY?Cf(a,b):Df(a,b));g.visitDateDay=M(Cf);g.visitDateMillisecond=M(Df);g.visitTimestamp=M((a,b)=>{switch(a.type.unit){case w.SECOND:return If(a,b);case w.MILLISECOND:return Jf(a,b);case w.MICROSECOND:return Kf(a,b);case w.NANOSECOND:return Lf(a,b)}});g.visitTimestampSecond=M(If);g.visitTimestampMillisecond=M(Jf);g.visitTimestampMicrosecond=M(Kf);
g.visitTimestampNanosecond=M(Lf);g.visitTime=M((a,b)=>{switch(a.type.unit){case w.SECOND:return Mf(a,b);case w.MILLISECOND:return Nf(a,b);case w.MICROSECOND:return Of(a,b);case w.NANOSECOND:return Pf(a,b)}});g.visitTimeSecond=M(Mf);g.visitTimeMillisecond=M(Nf);g.visitTimeMicrosecond=M(Of);g.visitTimeNanosecond=M(Pf);g.visitDecimal=M(({values:a,stride:b},c)=>new mc(a.subarray(b*c,b*(c+1))));g.visitList=M((a,b)=>{const c=a.stride,d=a.children,{[b*c]:e,[b*c+1]:f}=a.valueOffsets;a=d[0].slice(e,f-e);return new L([a])});
g.visitStruct=M((a,b)=>new vf(a,b));g.visitUnion=M((a,b)=>a.type.mode===u.Dense?Qf(a,b):Rf(a,b));g.visitDenseUnion=M(Qf);g.visitSparseUnion=M(Rf);g.visitDictionary=M((a,b)=>a.dictionary?.get(a.values[b]));g.visitInterval=M((a,b)=>a.type.unit===Fa.DAY_TIME?Sf(a,b):Tf(a,b));g.visitIntervalDayTime=M(Sf);g.visitIntervalYearMonth=M(Tf);
g.visitDuration=M((a,b)=>{switch(a.type.unit){case w.SECOND:return Uf(a,b);case w.MILLISECOND:return Vf(a,b);case w.MICROSECOND:return Wf(a,b);case w.NANOSECOND:return Xf(a,b)}});g.visitDurationSecond=M(Uf);g.visitDurationMillisecond=M(Vf);g.visitDurationMicrosecond=M(Wf);g.visitDurationNanosecond=M(Xf);g.visitFixedSizeList=M((a,b)=>{const c=a.stride;a=a.children[0].slice(b*c,c);return new L([a])});
g.visitMap=M((a,b)=>{const c=a.children,{[b]:d,[b+1]:e}=a.valueOffsets;return new Yf(c[0].slice(d,e-d))});const xf=new Af;const Zf=Symbol.for("keys"),$f=Symbol.for("vals"),ag=Symbol.for("kKeysAsStrings"),bg=Symbol.for("_kKeysAsStrings");
class Yf{constructor(a){this[Zf]=(new L([a.children[0]])).memoize();this[$f]=a.children[1];return new Proxy(this,new cg)}get [ag](){return this[bg]||(this[bg]=Array.from(this[Zf].toArray(),String))}[Symbol.iterator](){return new dg(this[Zf],this[$f])}get size(){return this[Zf].length}toArray(){return Object.values(this.toJSON())}toJSON(){const a=this[Zf],b=this[$f],c={};for(let d=-1,e=a.length;++d<e;)c[a.get(d)]=xf.visit(b,d);return c}toString(){return`{${[...this].map(([a,b])=>`${cc(a)}: ${cc(b)}`).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}}
class dg{constructor(a,b){this.keys=a;this.Tb=b;this.xb=0;this.Qb=a.length}[Symbol.iterator](){return this}next(){const a=this.xb;if(a===this.Qb)return{done:!0,value:null};this.xb++;return{done:!1,value:[this.keys.get(a),xf.visit(this.Tb,a)]}}}
class cg{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(a){return a[ag]}has(a,b){return a[ag].includes(b)}getOwnPropertyDescriptor(a,b){if(-1!==a[ag].indexOf(b))return{writable:!0,enumerable:!0,configurable:!0}}get(a,b){if(Reflect.has(a,b))return a[b];var c=a[ag].indexOf(b);if(-1!==c)return c=xf.visit(Reflect.get(a,$f),c),Reflect.set(a,b,c),c}set(a,b,c){const d=a[ag].indexOf(b);return-1!==d?(hf.visit(Reflect.get(a,$f),d,c),Reflect.set(a,b,c)):Reflect.has(a,
b)?Reflect.set(a,b,c):!1}}Object.defineProperties(Yf.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[Zf]:{writable:!0,enumerable:!1,configurable:!1,value:null},[$f]:{writable:!0,enumerable:!1,configurable:!1,value:null},[bg]:{writable:!0,enumerable:!1,configurable:!1,value:null}});let eg;function fg(a,b,c,d){const {length:e=0}=a;b="number"!==typeof b?0:b;c="number"!==typeof c?e:c;0>b&&(b=(b%e+e)%e);0>c&&(c=(c%e+e)%e);c<b&&(eg=b,b=c,c=eg);c>e&&(c=e);return d?d(a,b,c):[b,c]}const gg=a=>a!==a;function hg(a){if("object"!==typeof a||null===a)return a!==a?gg:b=>b===a;if(a instanceof Date){const b=a.valueOf();return c=>c instanceof Date?c.valueOf()===b:!1}return ArrayBuffer.isView(a)?b=>b?pa(a,b):!1:a instanceof Map?ig(a):Array.isArray(a)?jg(a):a instanceof L?kg(a):lg(a)}
function jg(a){const b=[];for(let c=-1,d=a.length;++c<d;)b[c]=hg(a[c]);return mg(b)}function ig(a){let b=-1;const c=[];for(const d of a.values())c[++b]=hg(d);return mg(c)}function kg(a){const b=[];for(let c=-1,d=a.length;++c<d;)b[c]=hg(a.get(c));return mg(b)}function lg(a){const b=Object.keys(a),c=[];for(let d=-1,e=b.length;++d<e;)c[d]=hg(a[b[d]]);return mg(c,b)}
function mg(a,b){return c=>{if(!c||"object"!==typeof c)return!1;switch(c.constructor){case Array:a:{var d=a.length;if(c.length!==d)c=!1;else{for(var e=-1;++e<d;)if(!a[e](c[e])){c=!1;break a}c=!0}}return c;case Map:return ng(a,c,c.keys());case Yf:case vf:case Object:case void 0:return ng(a,c,b||Object.keys(c))}if(c instanceof L)a:if(d=a.length,c.length!==d)c=!1;else{for(e=-1;++e<d;)if(!a[e](c.get(e))){c=!1;break a}c=!0}else c=!1;return c}}
function ng(a,b,c){c=c[Symbol.iterator]();const d=b instanceof Map?b.keys():Object.keys(b)[Symbol.iterator]();b=b instanceof Map?b.values():Object.values(b)[Symbol.iterator]();let e=0;const f=a.length;let h=b.next(),k=c.next(),n=d.next();for(;e<f&&!k.done&&!n.done&&!h.done&&k.value===n.value&&a[e](h.value);++e,k=c.next(),n=d.next(),h=b.next());if(e===f&&k.done&&n.done&&h.done)return!0;c.return&&c.return();d.return&&d.return();b.return&&b.return();return!1}var og={};og.clampRange=fg;
og.createElementComparator=hg;og.wrapIndex=(a,b)=>0>a?b+a:a;function pg(a,b,c,d){return 0!==(c&1<<d)}function qg(a,b,c,d){return(c&1<<d)>>d}function rg(a,b,c){const d=c.byteLength+7&-8;if(0<a||c.byteLength<d){const e=new Uint8Array(d);e.set(0===a%8?c.subarray(a>>3):sg(new tg(c,a,b,null,pg)).subarray(0,d));return e}return c}function sg(a){const b=[];let c=0,d=0,e=0;for(const f of a)f&&(e|=1<<d),8===++d&&(b[c++]=e,e=d=0);if(0===c||0<d)b[c++]=e;a=new Uint8Array(b.length+7&-8);a.set(b);return a}
class tg{constructor(a,b,c,d,e){this.R=a;this.length=c;this.context=d;this.get=e;this.Va=b%8;this.sb=b>>3;this.rb=a[this.sb++];this.index=0}next(){return this.index<this.length?(8===this.Va&&(this.Va=0,this.rb=this.R[this.sb++]),{value:this.get(this.context,this.index++,this.rb,this.Va++)}):{done:!0,value:null}}[Symbol.iterator](){return this}}
function ug(a,b,c){if(0>=c-b)return 0;if(8>c-b){var d=0;for(var e of new tg(a,b,c-b,a,qg))d+=e;return d}d=c>>3<<3;e=b+(0===b%8?0:8-b%8);return ug(a,b,e)+ug(a,d,c)+vg(a,e>>3,d-e>>3)}function vg(a,b,c){let d=0;b=Math.trunc(b);const e=new DataView(a.buffer,a.byteOffset,a.byteLength);for(a=void 0===c?a.byteLength:b+c;4<=a-b;)d+=wg(e.getUint32(b)),b+=4;for(;2<=a-b;)d+=wg(e.getUint16(b)),b+=2;for(;1<=a-b;)d+=wg(e.getUint8(b)),b+=1;return d}
function wg(a){a=Math.trunc(a);a-=a>>>1&1431655765;a=(a&858993459)+(a>>>2&858993459);return 16843009*(a+(a>>>4)&252645135)>>>24}var xg={};xg.BitIterator=tg;xg.getBit=qg;xg.getBool=pg;xg.packBools=sg;xg.popcnt_array=vg;xg.popcnt_bit_range=ug;xg.popcnt_uint32=wg;xg.setBool=function(a,b,c){return c?!!(a[b>>3]|=1<<b%8)||!0:!(a[b>>3]&=~(1<<b%8))&&!1};xg.truncateBitmap=rg;function yg(a,b,c){return a.map(d=>d.slice(b,c))}
class N{get typeId(){return this.type.typeId}get ArrayType(){return this.type.ArrayType}get buffers(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}get nullable(){if(0!==this.N){const a=this.type;return I.isSparseUnion(a)?this.children.some(b=>b.nullable):I.isDenseUnion(a)?this.children.some(b=>b.nullable):this.nullBitmap&&0<this.nullBitmap.byteLength}return!0}get byteLength(){let a=0;const b=this.valueOffsets,c=this.values,d=this.nullBitmap,e=this.typeIds;b&&(a+=b.byteLength);
c&&(a+=c.byteLength);d&&(a+=d.byteLength);e&&(a+=e.byteLength);return this.children.reduce((f,h)=>f+h.byteLength,a)}get nullCount(){if(I.isUnion(this.type))return this.children.reduce((c,d)=>c+d.nullCount,0);let a=this.N,b;-1>=a&&(b=this.nullBitmap)&&(this.N=a=0===b.length?0:this.length-ug(b,this.offset,this.offset+this.length));return a}constructor(a,b,c,d,e,f=[],h){this.type=a;this.children=f;this.dictionary=h;this.offset=Math.floor(Math.max(b||0,0));this.length=Math.floor(Math.max(c||0,0));this.N=
Math.floor(Math.max(d||0,-1));let k;e instanceof N?(this.stride=e.stride,this.values=e.values,this.typeIds=e.typeIds,this.nullBitmap=e.nullBitmap,this.valueOffsets=e.valueOffsets):(this.stride=ye(a),e&&((k=e[0])&&(this.valueOffsets=k),(k=e[1])&&(this.values=k),(k=e[2])&&(this.nullBitmap=k),(k=e[3])&&(this.typeIds=k)))}getValid(a){const b=this.type;return I.isUnion(b)?this.children[b.typeIdToChildIndex[this.typeIds[a]]].getValid(b.mode===u.Dense?this.valueOffsets[a]:a):this.nullable&&0<this.nullCount?
(a=this.offset+a,0!==(this.nullBitmap[a>>3]&1<<a%8)):!0}setValid(a,b){var c=this.type;if(I.isUnion(c)){var d=this.children[c.typeIdToChildIndex[this.typeIds[a]]];a=c.mode===u.Dense?this.valueOffsets[a]:a;c=d.getValid(a);d.setValid(a,b)}else{({nullBitmap:d}=this);c=this.offset;var e=this.length,f=c+a;a=1<<f%8;f>>=3;if(!d||d.byteLength<=f)d=(new Uint8Array((c+e+63&-64)>>3)).fill(255),0<this.nullCount?(d.set(rg(c,e,this.nullBitmap),0),Object.assign(this,{nullBitmap:d})):Object.assign(this,{nullBitmap:d,
N:0});e=d[f];c=0!==(e&a);d[f]=b?e|a:e&~a}c!==!!b&&(this.N=this.nullCount+(b?-1:1));return b}clone(a=this.type,b=this.offset,c=this.length,d=this.N,e=this,f=this.children){return new N(a,b,c,d,e,f,this.dictionary)}slice(a,b){const c=this.stride,d=this.typeId,e=this.children,f=+(0===this.N)-1,h=16===d?c:1;let k;const n=this.buffers;(k=n[ac.TYPE])&&(n[ac.TYPE]=k.subarray(a,a+b));(k=n[ac.OFFSET])&&(n[ac.OFFSET]=k.subarray(a,a+b+1))||(k=n[ac.DATA])&&(n[ac.DATA]=6===d?k:k.subarray(c*a,c*(a+b)));return this.clone(this.type,
this.offset+a,b,f,n,0===e.length||this.valueOffsets?e:yg(e,h*a,h*b))}Ja(a){if(this.typeId===F.Null)return this.clone(this.type,0,a,0);const b=this.length,c=this.nullCount,d=(new Uint8Array((a+63&-64)>>3)).fill(255,0,b>>3);d[b>>3]=(1<<b-(b&-8))-1;0<c&&d.set(rg(this.offset,b,this.nullBitmap),0);const e=this.buffers;e[ac.VALIDITY]=d;return this.clone(this.type,0,a,c+(a-b),e)}}N.prototype.children=Object.freeze([]);
class zg extends ze{visit(a){return this.getVisitFn(a.type).call(this,a)}visitNull(a){const {type:b,offset:c=0,length:d=0}=a;return new N(b,c,d,d)}visitBool(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.nullBitmap),e=p(b.ArrayType,a.data),{length:f=e.length>>3,nullCount:h=a.nullBitmap?-1:0}=a;return new N(b,c,f,h,[void 0,e,d])}visitInt(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.nullBitmap),e=p(b.ArrayType,a.data),{length:f=e.length,nullCount:h=a.nullBitmap?-1:0}=a;return new N(b,c,f,h,[void 0,
e,d])}visitFloat(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.nullBitmap),e=p(b.ArrayType,a.data),{length:f=e.length,nullCount:h=a.nullBitmap?-1:0}=a;return new N(b,c,f,h,[void 0,e,d])}visitUtf8(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.data),e=p(Uint8Array,a.nullBitmap),f=p(Int32Array,a.valueOffsets),{length:h=f.length-1,nullCount:k=a.nullBitmap?-1:0}=a;return new N(b,c,h,k,[f,d,e])}visitLargeUtf8(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.data),e=p(Uint8Array,a.nullBitmap),f=p(BigInt64Array,
a.valueOffsets),{length:h=f.length-1,nullCount:k=a.nullBitmap?-1:0}=a;return new N(b,c,h,k,[f,d,e])}visitBinary(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.data),e=p(Uint8Array,a.nullBitmap),f=p(Int32Array,a.valueOffsets),{length:h=f.length-1,nullCount:k=a.nullBitmap?-1:0}=a;return new N(b,c,h,k,[f,d,e])}visitLargeBinary(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.data),e=p(Uint8Array,a.nullBitmap),f=p(BigInt64Array,a.valueOffsets),{length:h=f.length-1,nullCount:k=a.nullBitmap?-1:0}=a;return new N(b,
c,h,k,[f,d,e])}visitFixedSizeBinary(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.nullBitmap),e=p(b.ArrayType,a.data),{length:f=e.length/ye(b),nullCount:h=a.nullBitmap?-1:0}=a;return new N(b,c,f,h,[void 0,e,d])}visitDate(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.nullBitmap),e=p(b.ArrayType,a.data),{length:f=e.length/ye(b),nullCount:h=a.nullBitmap?-1:0}=a;return new N(b,c,f,h,[void 0,e,d])}visitTimestamp(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.nullBitmap),e=p(b.ArrayType,a.data),{length:f=
e.length/ye(b),nullCount:h=a.nullBitmap?-1:0}=a;return new N(b,c,f,h,[void 0,e,d])}visitTime(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.nullBitmap),e=p(b.ArrayType,a.data),{length:f=e.length/ye(b),nullCount:h=a.nullBitmap?-1:0}=a;return new N(b,c,f,h,[void 0,e,d])}visitDecimal(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.nullBitmap),e=p(b.ArrayType,a.data),{length:f=e.length/ye(b),nullCount:h=a.nullBitmap?-1:0}=a;return new N(b,c,f,h,[void 0,e,d])}visitList(a){const {type:b,offset:c=0,child:d}=
a,e=p(Uint8Array,a.nullBitmap),f=p(Int32Array,a.valueOffsets),{length:h=f.length-1,nullCount:k=a.nullBitmap?-1:0}=a;return new N(b,c,h,k,[f,void 0,e],[d])}visitStruct(a){const {type:b,offset:c=0,children:d=[]}=a,e=p(Uint8Array,a.nullBitmap),{length:f=d.reduce((k,{length:n})=>Math.max(k,n),0),nullCount:h=a.nullBitmap?-1:0}=a;return new N(b,c,f,h,[void 0,void 0,e],d)}visitUnion(a){const {type:b,offset:c=0,children:d=[]}=a,e=p(b.ArrayType,a.typeIds),{length:f=e.length,nullCount:h=-1}=a;if(I.isSparseUnion(b))return new N(b,
c,f,h,[void 0,void 0,void 0,e],d);a=p(Int32Array,a.valueOffsets);return new N(b,c,f,h,[a,void 0,void 0,e],d)}visitDictionary(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.nullBitmap),e=p(b.indices.ArrayType,a.data),{dictionary:f=new L([(new zg).visit({type:b.dictionary})])}=a,{length:h=e.length,nullCount:k=a.nullBitmap?-1:0}=a;return new N(b,c,h,k,[void 0,e,d],[],f)}visitInterval(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.nullBitmap),e=p(b.ArrayType,a.data),{length:f=e.length/ye(b),nullCount:h=
a.nullBitmap?-1:0}=a;return new N(b,c,f,h,[void 0,e,d])}visitDuration(a){const {type:b,offset:c=0}=a,d=p(Uint8Array,a.nullBitmap),e=p(b.ArrayType,a.data),{length:f=e.length,nullCount:h=a.nullBitmap?-1:0}=a;return new N(b,c,f,h,[void 0,e,d])}visitFixedSizeList(a){const {type:b,offset:c=0,child:d=(new zg).visit({type:b.valueType})}=a,e=p(Uint8Array,a.nullBitmap),{length:f=d.length/ye(b),nullCount:h=a.nullBitmap?-1:0}=a;return new N(b,c,f,h,[void 0,void 0,e],[d])}visitMap(a){const {type:b,offset:c=0,
child:d=(new zg).visit({type:b.childType})}=a,e=p(Uint8Array,a.nullBitmap),f=p(Int32Array,a.valueOffsets),{length:h=f.length-1,nullCount:k=a.nullBitmap?-1:0}=a;return new N(b,c,h,k,[f,void 0,e],[d])}}const Ag=new zg;function O(a){return Ag.visit(a)};class Bg{constructor(a=0,b){this.yb=a;this.wb=b;this.Xa=0;this.vb=this.wb(0)}next(){for(;this.Xa<this.yb;){const a=this.vb.next();if(!a.done)return a;++this.Xa<this.yb&&(this.vb=this.wb(this.Xa))}return{done:!0,value:null}}[Symbol.iterator](){return this}}function Cg(a){return a.some(b=>b.nullable)}function Dg(a){return a.reduce((b,c)=>b+c.nullCount,0)}function Eg(a){return a.reduce((b,c,d)=>{b[d+1]=b[d]+c.length;return b},new Uint32Array(a.length+1))}
function Fg(a,b,c,d){const e=[];for(let f=-1,h=a.length;++f<h;){const k=a[f],n=b[f],q=k.length;if(n>=d)break;if(c>=n+q)continue;if(n>=c&&n+q<=d){e.push(k);continue}const B=Math.max(0,c-n);e.push(k.slice(B,Math.min(d-n,q)-B))}0===e.length&&e.push(a[0].slice(0,0));return e}function Gg(a,b,c,d){let e=0,f,h=b.length-1;do{if(e>=h-1)return c<b[h]?d(a,e,c-b[e]):null;f=e+Math.trunc(.5*(h-e));c<b[f]?h=f:e=f}while(e<h)}function Hg(a,b){return a.getValid(b)}
function Ig(a){function b(c,d,e){return a(c[d],e)}return function(c){return Gg(this.data,this.j,c,b)}}function Jg(a){function b(d,e,f){return a(d[e],f,c)}let c;return function(d,e){const f=this.data;c=e;d=Gg(f,this.j,d,b);c=void 0;return d}}function Kg(a){function b(d,e,f){var h=f;f=0;for(let k=e-1,n=d.length;++k<n;){e=d[k];if(~(h=a(e,c,h)))return f+h;h=0;f+=e.length}return-1}let c;return function(d,e){c=d;d=this.data;e="number"!==typeof e?b(d,0,0):Gg(d,this.j,e,b);c=void 0;return e}};class Lg extends ze{}function P(a,b,c){if(void 0===b)return-1;if(null===b)switch(a.typeId){case F.Union:break;case F.Dictionary:break;default:a:{const e=a.nullBitmap;if(e&&!(0>=a.nullCount)){b=0;for(var d of new tg(e,a.offset+(c||0),a.length,e,pg)){if(!d){a=b;break a}++b}}a=-1}return a}d=xf.getVisitFn(a);b=hg(b);for(let e=(c||0)-1,f=a.length;++e<f;)if(b(d(a,e)))return e;return-1}
function Mg(a,b,c){const d=xf.getVisitFn(a);b=hg(b);for(let e=(c||0)-1,f=a.length;++e<f;)if(b(d(a,e)))return e;return-1}g=Lg.prototype;g.visitNull=function(a,b){return null===b&&0<a.length?0:-1};g.visitBool=P;g.visitInt=P;g.visitInt8=P;g.visitInt16=P;g.visitInt32=P;g.visitInt64=P;g.visitUint8=P;g.visitUint16=P;g.visitUint32=P;g.visitUint64=P;g.visitFloat=P;g.visitFloat16=P;g.visitFloat32=P;g.visitFloat64=P;g.visitUtf8=P;g.visitLargeUtf8=P;g.visitBinary=P;g.visitLargeBinary=P;
g.visitFixedSizeBinary=P;g.visitDate=P;g.visitDateDay=P;g.visitDateMillisecond=P;g.visitTimestamp=P;g.visitTimestampSecond=P;g.visitTimestampMillisecond=P;g.visitTimestampMicrosecond=P;g.visitTimestampNanosecond=P;g.visitTime=P;g.visitTimeSecond=P;g.visitTimeMillisecond=P;g.visitTimeMicrosecond=P;g.visitTimeNanosecond=P;g.visitDecimal=P;g.visitList=P;g.visitStruct=P;g.visitUnion=P;g.visitDenseUnion=Mg;g.visitSparseUnion=Mg;g.visitDictionary=P;g.visitInterval=P;g.visitIntervalDayTime=P;
g.visitIntervalYearMonth=P;g.visitDuration=P;g.visitDurationSecond=P;g.visitDurationMillisecond=P;g.visitDurationMicrosecond=P;g.visitDurationNanosecond=P;g.visitFixedSizeList=P;g.visitMap=P;const Ng=new Lg;class Og extends ze{}function Q(a){const b=a.type;if(0===a.nullCount&&1===a.stride&&(I.isInt(b)&&64!==b.bitWidth||I.isTime(b)&&64!==b.bitWidth||I.isFloat(b)&&b.precision!==v.HALF))return new Bg(a.data.length,d=>{d=a.data[d];return d.values.subarray(0,d.length)[Symbol.iterator]()});let c=0;return new Bg(a.data.length,d=>{d=a.data[d].length;const e=a.slice(c,c+d);c+=d;return new Pg(e)})}
class Pg{constructor(a){this.zb=a;this.index=0}next(){return this.index<this.zb.length?{value:this.zb.get(this.index++)}:{done:!0,value:null}}[Symbol.iterator](){return this}}g=Og.prototype;g.visitNull=Q;g.visitBool=Q;g.visitInt=Q;g.visitInt8=Q;g.visitInt16=Q;g.visitInt32=Q;g.visitInt64=Q;g.visitUint8=Q;g.visitUint16=Q;g.visitUint32=Q;g.visitUint64=Q;g.visitFloat=Q;g.visitFloat16=Q;g.visitFloat32=Q;g.visitFloat64=Q;g.visitUtf8=Q;g.visitLargeUtf8=Q;g.visitBinary=Q;g.visitLargeBinary=Q;
g.visitFixedSizeBinary=Q;g.visitDate=Q;g.visitDateDay=Q;g.visitDateMillisecond=Q;g.visitTimestamp=Q;g.visitTimestampSecond=Q;g.visitTimestampMillisecond=Q;g.visitTimestampMicrosecond=Q;g.visitTimestampNanosecond=Q;g.visitTime=Q;g.visitTimeSecond=Q;g.visitTimeMillisecond=Q;g.visitTimeMicrosecond=Q;g.visitTimeNanosecond=Q;g.visitDecimal=Q;g.visitList=Q;g.visitStruct=Q;g.visitUnion=Q;g.visitDenseUnion=Q;g.visitSparseUnion=Q;g.visitDictionary=Q;g.visitInterval=Q;g.visitIntervalDayTime=Q;
g.visitIntervalYearMonth=Q;g.visitDuration=Q;g.visitDurationSecond=Q;g.visitDurationMillisecond=Q;g.visitDurationMicrosecond=Q;g.visitDurationNanosecond=Q;g.visitFixedSizeList=Q;g.visitMap=Q;const Qg=new Og;const Rg={},Sg={};
class L{constructor(a){a=a[0]instanceof L?a.flatMap(c=>c.data):a;if(0===a.length||a.some(c=>!(c instanceof N)))throw new TypeError("Vector constructor expects an Array of Data instances.");const b=a[0]?.type;switch(a.length){case 0:this.j=[0];break;case 1:const {get:c,set:d,indexOf:e}=Rg[b.typeId],f=a[0];this.isValid=h=>f.getValid(h);this.get=h=>c(f,h);this.set=(h,k)=>d(f,h,k);this.indexOf=h=>e(f,h);this.j=[0,f.length];break;default:Object.setPrototypeOf(this,Sg[b.typeId]),this.j=Eg(a)}this.data=
a;this.type=b;this.stride=ye(b);this.numChildren=b.children?.length??0;this.length=this.j.at(-1)}get byteLength(){return this.data.reduce((a,b)=>a+b.byteLength,0)}get nullable(){return Cg(this.data)}get nullCount(){return Dg(this.data)}get ArrayType(){return this.type.ArrayType}get [Symbol.toStringTag](){return`${this.VectorName}<${this.type[Symbol.toStringTag]}>`}get VectorName(){return`${F[this.type.typeId]}Vector`}isValid(){return!1}get(){return null}at(a){return this.get(0>a?this.length+a:a)}set(){}indexOf(){return-1}includes(a,
b){return-1<this.indexOf(a,b)}[Symbol.iterator](){return Qg.visit(this)}concat(...a){return new L(this.data.concat(a.flatMap(b=>b.data).flat(Number.POSITIVE_INFINITY)))}slice(a,b){return new L(fg(this,a,b,({data:c,j:d},e,f)=>Fg(c,d,e,f)))}toJSON(){return[...this]}toArray(){const a=this.data,b=this.length,c=this.stride,d=this.ArrayType;switch(this.type.typeId){case F.Int:case F.Float:case F.Decimal:case F.Time:case F.Timestamp:switch(a.length){case 0:return new d;case 1:return a[0].values.subarray(0,
b*c);default:return a.reduce((e,{values:f,length:h})=>{e.qb.set(f.subarray(0,h*c),e.offset);e.offset+=h*c;return e},{qb:new d(b*c),offset:0}).qb}}return[...this]}toString(){return`[${[...this].join()}]`}getChild(a){return this.getChildAt(this.type.children?.findIndex(b=>b.name===a))}getChildAt(a){return-1<a&&a<this.numChildren?new L(this.data.map(({children:b})=>b[a])):null}get isMemoized(){return I.isDictionary(this.type)?this.data[0].dictionary.isMemoized:!1}memoize(){if(I.isDictionary(this.type)){const a=
new Tg(this.data[0].dictionary),b=this.data.map(c=>{c=c.clone();c.dictionary=a;return c});return new L(b)}return new Tg(this)}unmemoize(){if(I.isDictionary(this.type)&&this.isMemoized){const a=this.data[0].dictionary.unmemoize(),b=this.data.map(c=>{c=c.clone();c.dictionary=a;return c});return new L(b)}return this}}
L[Symbol.toStringTag]=(a=>{a.type=I.prototype;a.data=[];a.length=0;a.stride=1;a.numChildren=0;a.j=new Uint32Array([0]);a[Symbol.isConcatSpreadable]=!0;var b=Object.keys(F).map(c=>F[c]).filter(c=>"number"===typeof c&&c!==F.NONE);for(const c of b){b=xf.getVisitFnByTypeId(c);const d=hf.getVisitFnByTypeId(c),e=Ng.getVisitFnByTypeId(c);Rg[c]={get:b,set:d,indexOf:e};Sg[c]=Object.create(a,{isValid:{value:Ig(Hg)},get:{value:Ig(xf.getVisitFnByTypeId(c))},set:{value:Jg(hf.getVisitFnByTypeId(c))},indexOf:{value:Kg(Ng.getVisitFnByTypeId(c))}})}return"Vector"})(L.prototype);
class Tg extends L{constructor(a){super(a.data);const b=this.get,c=this.set,d=this.slice,e=Array(this.length);Object.defineProperty(this,"get",{value(f){var h=e[f];if(void 0!==h)return h;h=b.call(this,f);return e[f]=h}});Object.defineProperty(this,"set",{value(f,h){c.call(this,f,h);e[f]=h}});Object.defineProperty(this,"slice",{value:(f,h)=>new Tg(d.call(this,f,h))});Object.defineProperty(this,"isMemoized",{value:!0});Object.defineProperty(this,"unmemoize",{value:()=>new L(this.data)});Object.defineProperty(this,
"memoize",{value:()=>this})}}
function Ug(a){if(a){if(a instanceof N)return new L([a]);if(a instanceof L)return new L(a.data);if(a.type instanceof I)return new L([O(a)]);if(Array.isArray(a))return new L(a.flatMap(b=>b instanceof N?[b]:b instanceof L?b.data:Ug(b).data));if(ArrayBuffer.isView(a)){a instanceof DataView&&(a=new Uint8Array(a.buffer));const b={offset:0,length:a.length,nullCount:-1,data:a};if(a instanceof Int8Array)return new L([O({...b,type:new zc})]);if(a instanceof Int16Array)return new L([O({...b,type:new Ac})]);
if(a instanceof Int32Array)return new L([O({...b,type:new Bc})]);if(a instanceof BigInt64Array)return new L([O({...b,type:new Cc})]);if(a instanceof Uint8Array||a instanceof Uint8ClampedArray)return new L([O({...b,type:new Dc})]);if(a instanceof Uint16Array)return new L([O({...b,type:new Ec})]);if(a instanceof Uint32Array)return new L([O({...b,type:new Fc})]);if(a instanceof BigUint64Array)return new L([O({...b,type:new Gc})]);if(a instanceof Float32Array)return new L([O({...b,type:new Mc})]);if(a instanceof
Float64Array)return new L([O({...b,type:new Nc})])}}throw Error("Unrecognized input");};function Vg(a){if(!a||0>=a.length)return function(){return!0};let b="";const c=a.filter(d=>d===d);0<c.length&&(b=`
    switch (x) {${c.map(d=>`
        case ${"bigint"!==typeof d?cc(d):`${cc(d)}n`}:`).join("")}
            return false;
    }`);a.length!==c.length&&(b=`if (x !== x) return false;\n${b}`);return new Function("x",`${b}\nreturn true;`)};function Wg(a,b){a=Math.ceil(a)*b-1;return(a-a%64+64||64)/b}function Xg(a,b=0){return a.length>=b?a.subarray(0,b):ja(new a.constructor(b),a,0)}function Yg(a,b){if(0<b){a.length+=b;b=a.length*a.stride;const c=a.buffer.length;b>=c&&(a.buffer=Xg(a.buffer,0===c?Wg(1*b,a.BYTES_PER_ELEMENT):Wg(2*b,a.BYTES_PER_ELEMENT)))}return a}
class Zg{constructor(a,b=0,c=1){this.length=Math.ceil(b/c);this.buffer=new a(this.length);this.stride=c;this.BYTES_PER_ELEMENT=a.BYTES_PER_ELEMENT;this.ArrayType=a}get byteLength(){return Math.ceil(this.length*this.stride)*this.BYTES_PER_ELEMENT}get reservedLength(){return this.buffer.length/this.stride}get reservedByteLength(){return this.buffer.byteLength}set(){return this}append(a){return this.set(this.length,a)}flush(a=this.length){a=Wg(a*this.stride,this.BYTES_PER_ELEMENT);a=Xg(this.buffer,a);
this.clear();return a}clear(){this.length=0;this.buffer=new this.ArrayType;return this}}class $g extends Zg{get(a){return this.buffer[a]}set(a,b){Yg(this,a-this.length+1);this.buffer[a*this.stride]=b;return this}}
class ah extends $g{constructor(){super(Uint8Array,0,.125);this.Ha=0}get Pb(){return this.length-this.Ha}get(a){return this.buffer[a>>3]>>a%8&1}set(a,b){const {buffer:c}=Yg(this,a-this.length+1),d=a>>3;a%=8;const e=c[d]>>a&1;b?0===e&&(c[d]|=1<<a,++this.Ha):1===e&&(c[d]&=~(1<<a),--this.Ha);return this}clear(){this.Ha=0;return super.clear()}}
class bh extends $g{constructor(a){super(a.OffsetArrayType,1,1)}append(a){return this.set(this.length-1,a)}set(a,b){const c=this.length-1,d=Yg(this,a-c+1).buffer;c<a++&&0<=c&&d.fill(d[c],c,a);d[a]=d[a-1]+b;return this}flush(a=this.length-1){a>this.length&&this.set(a-1,4<this.BYTES_PER_ELEMENT?BigInt(0):0);return super.flush(a+1)}};class ch{static throughNode(){throw Error('"throughNode" not available in this environment');}static throughDOM(){throw Error('"throughDOM" not available in this environment');}constructor({type:a,nullValues:b}){this.length=0;this.finished=!1;this.type=a;this.children=[];this.nullValues=b;this.stride=ye(a);this.D=new ah;b&&0<b.length&&(this.lb=Vg(b))}toVector(){return new L([this.flush()])}get ArrayType(){return this.type.ArrayType}get nullCount(){return this.D.Pb}get numChildren(){return this.children.length}get byteLength(){let a=
0;const b=this.j,c=this.o,d=this.D,e=this.X,f=this.children;b&&(a+=b.byteLength);c&&(a+=c.byteLength);d&&(a+=d.byteLength);e&&(a+=e.byteLength);return f.reduce((h,k)=>h+k.byteLength,a)}get reservedLength(){return this.D.reservedLength}get reservedByteLength(){let a=0;this.j&&(a+=this.j.reservedByteLength);this.o&&(a+=this.o.reservedByteLength);this.D&&(a+=this.D.reservedByteLength);this.X&&(a+=this.X.reservedByteLength);return this.children.reduce((b,c)=>b+c.reservedByteLength,a)}get valueOffsets(){return this.j?
this.j.buffer:null}get values(){return this.o?this.o.buffer:null}get nullBitmap(){return this.D?this.D.buffer:null}get typeIds(){return this.X?this.X.buffer:null}append(a){return this.set(this.length,a)}isValid(a){return this.lb(a)}set(a,b){this.setValid(a,this.isValid(b))&&this.setValue(a,b);return this}setValue(a,b){this.A(this,a,b)}setValid(a,b){this.length=this.D.set(a,+b).length;return b}addChild(){throw Error(`Cannot append children to non-nested type "${this.type}"`);}getChildAt(a){return this.children[a]||
null}flush(){let a,b,c,d;const e=this.type,f=this.length,h=this.nullCount;var k=this.j;const n=this.o,q=this.D;(b=this.X?.flush(f))?d=k?.flush(f):a=(d=k?.flush(f))?n?.flush(k.get(k.length-1)):n?.flush(f);0<h&&(c=q?.flush(f));k=this.children.map(B=>B.flush());this.clear();return O({type:e,length:f,nullCount:h,children:k,child:k[0],data:a,typeIds:b,nullBitmap:c,valueOffsets:d})}finish(){this.finished=!0;for(const a of this.children)a.finish();return this}clear(){this.length=0;this.D?.clear();this.o?.clear();
this.j?.clear();this.X?.clear();for(const a of this.children)a.clear();return this}}g=ch.prototype;g.length=1;g.stride=1;g.children=null;g.finished=!1;g.nullValues=null;g.lb=()=>!0;class dh extends ch{constructor(a){super(a);this.o=new $g(this.ArrayType,0,this.stride)}setValue(a,b){const c=this.o;Yg(c,a-c.length+1);return super.setValue(a,b)}}function eh(a){const b=a.G,c=a.J;a.J=0;a.G=void 0;b&&0<b.size&&a.S(b,c)}
class fh extends ch{constructor(a){super(a);this.J=0;this.j=new bh(a.type)}setValue(a,b){const c=this.G||(this.G=new Map),d=c.get(a);d&&(this.J-=d.length);this.J+=b instanceof Yf?b[Zf].length:b.length;c.set(a,b)}setValid(a,b){return super.setValid(a,b)?!0:((this.G||(this.G=new Map)).set(a,void 0),!1)}clear(){this.J=0;this.G=void 0;return super.clear()}flush(){eh(this);return super.flush()}finish(){eh(this);return super.finish()}};class gh{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}offset(){return La(this.g,this.h)}Ya(){return this.g.u(this.h+8)}bodyLength(){return La(this.g,this.h+16)}};class hh{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}version(){const a=x(this.g,this.h,4);return a?Ka(this.g,this.h+a):t.V1}schema(a){const b=x(this.g,this.h,6);return b?(a||new Vb).i(Pa(this.g,this.h+b),this.g):null}dictionaries(a,b){const c=x(this.g,this.h,8);return c?(b||new gh).i(Qa(this.g,this.h+c)+24*a,this.g):null}cb(a){const b=x(this.g,this.h,10);return b?(new gh).i(Qa(this.g,this.h+b)+24*a,this.g):null}Ca(a){const b=x(this.g,this.h,12);return b?(new Cb).i(Pa(this.g,
Qa(this.g,this.h+b)+4*a),this.g):null}Da(){const a=x(this.g,this.h,12);return a?Ra(this.g,this.h+a):0}};class R{constructor(a=[],b,c,d=t.V5){this.fields=a||[];this.metadata=b||new Map;c||=ih(this.fields);this.dictionaries=c;this.Ga=d}get [Symbol.toStringTag](){return"Schema"}get names(){return this.fields.map(a=>a.name)}toString(){return`Schema<{ ${this.fields.map((a,b)=>`${b}: ${a}`).join(", ")} }>`}select(a){const b=new Set(a);a=this.fields.filter(c=>b.has(c.name));return new R(a,this.metadata)}selectAt(a){a=a.map(b=>this.fields[b]).filter(Boolean);return new R(a,this.metadata)}assign(...a){var b=
a[0]instanceof R?a[0]:Array.isArray(a[0])?new R(a[0]):new R(a);const c=[...this.fields];a=jh(jh(new Map,this.metadata),b.metadata);b=b.fields.filter(e=>{const f=c.findIndex(h=>h.name===e.name);return~f?(c[f]=e.clone({metadata:jh(jh(new Map,c[f].metadata),e.metadata)}))&&!1:!0});const d=ih(b,new Map);return new R([...c,...b],a,new Map([...this.dictionaries,...d]))}}R.prototype.fields=null;R.prototype.metadata=null;R.prototype.dictionaries=null;
class S{static new(...a){let [b,c,d,e]=a;a[0]&&"object"===typeof a[0]&&({name:b}=a[0],void 0===c&&(c=a[0].type),void 0===d&&(d=a[0].nullable),void 0===e&&(e=a[0].metadata));return new S(`${b}`,c,d,e)}constructor(a,b,c=!1,d){this.name=a;this.type=b;this.nullable=c;this.metadata=d||new Map}get typeId(){return this.type.typeId}get [Symbol.toStringTag](){return"Field"}toString(){return`${this.name}: ${this.type}`}clone(...a){let [b,c,d,e]=a;a[0]&&"object"===typeof a[0]?{name:b=this.name,type:c=this.type,
nullable:d=this.nullable,metadata:e=this.metadata}=a[0]:[b=this.name,c=this.type,d=this.nullable,e=this.metadata]=a;return S.new(b,c,d,e)}}S.prototype.type=null;S.prototype.name=null;S.prototype.nullable=null;S.prototype.metadata=null;function jh(a,b){return new Map([...(a||new Map),...(b||new Map)])}
function ih(a,b=new Map){for(let c=-1,d=a.length;++c<d;){const e=a[c].type;if(I.isDictionary(e))if(!b.has(e.id))b.set(e.id,e.dictionary);else if(b.get(e.id)!==e.dictionary)throw Error("Cannot create Schema containing two different dictionaries with the same Id");e.children&&0<e.children.length&&ih(e.children,b)}return b};function*kh(a){for(let b,c=-1,d=a.numDictionaries;++c<d;)if(b=a.Ea(c))yield b}
class lh{static decode(a){a=new Sa(p(Uint8Array,a));a=(new hh).i(a.u(a.position())+a.position(),a);const b=R.decode(a.schema(),new Map,a.version());return new mh(b,a)}static encode(a){const b=new gb;var c=R.encode(b,a.schema);db(b,24,a.numRecordBatches,8);for(var d of[...a.cb()].slice().reverse())nh.encode(b,d);d=eb(b);db(b,24,a.numDictionaries,8);for(const e of[...kh(a)].slice().reverse())nh.encode(b,e);a=eb(b);z(b,5);y(b,1,c);Za(b,t.V5,t.V1);y(b,3,d);y(b,2,a);c=A(b);b.finish(c);return Ta(b)}get numRecordBatches(){return this.Z.length}get numDictionaries(){return this.jb.length}constructor(a,
b=t.V5,c,d){this.schema=a;this.version=b;c&&(this.Z=c);d&&(this.jb=d)}*cb(){for(let a,b=-1,c=this.numRecordBatches;++b<c;)if(a=this.ca(b))yield a}ca(a){return 0<=a&&a<this.numRecordBatches&&this.Z[a]||null}Ea(a){return 0<=a&&a<this.numDictionaries&&this.jb[a]||null}}
class mh extends lh{get numRecordBatches(){var a=this.m;const b=x(a.g,a.h,10);return b?Ra(a.g,a.h+b):0}get numDictionaries(){var a=this.m;const b=x(a.g,a.h,8);return b?Ra(a.g,a.h+b):0}constructor(a,b){super(a,b.version());this.m=b}ca(a){return 0<=a&&a<this.numRecordBatches&&(a=this.m.cb(a))?nh.decode(a):null}Ea(a){return 0<=a&&a<this.numDictionaries&&(a=this.m.dictionaries(a))?nh.decode(a):null}}
class nh{static decode(a){return new nh(a.Ya(),a.bodyLength(),a.offset())}static encode(a,b){var c=BigInt(b.offset),d=b.Ya;b=BigInt(b.bodyLength);Va(a,8,24);a.Y(BigInt(b??0));Ua(a,4);a.V(d);a.Y(BigInt(c??0));return a.offset()}constructor(a,b,c){this.Ya=a;this.offset=H(c);this.bodyLength=H(b)}};const T=Object.freeze({done:!0,value:void 0});class oh{constructor(a){this.ja=a}get schema(){return this.ja.schema}get batches(){return this.ja.batches||[]}get dictionaries(){return this.ja.dictionaries||[]}}function ph(a){return a.hb||(a.hb=a.toDOMStream())}function qh(a,b,c){return qh(a.nb||(a.nb=a.toNodeStream()),b,c)}class rh{tee(){return ph(this).tee()}pipeTo(a,b){return ph(this).pipeTo(a,b)}pipeThrough(a,b){return ph(this).pipeThrough(a,b)}}
class sh extends rh{constructor(){super();this.o=[];this.ea=[];this.Ka=new Promise(a=>this.K=a)}get closed(){return this.Ka}async cancel(a){await this.return(a)}write(a){if(!this.K)throw Error("AsyncQueue is closed");0>=this.ea.length?this.o.push(a):this.ea.shift().resolve({done:!1,value:a})}abort(a){this.K&&(0>=this.ea.length?this.Na={error:a}:this.ea.shift().reject({done:!0,value:a}))}close(){if(this.K){const a=this.ea;for(;0<a.length;)a.shift().resolve(T);this.K();this.K=void 0}}[Symbol.asyncIterator](){return this}toDOMStream(a){return qa(this.K||
this.Na?this:this.o,a)}toNodeStream(){return ra()}async throw(a){await this.abort(a);return T}async return(){await this.close();return T}async read(a){return(await this.next(a,"read")).value}async peek(a){return(await this.next(a,"peek")).value}next(){return 0<this.o.length?Promise.resolve({done:!1,value:this.o.shift()}):this.Na?Promise.reject({done:!0,value:this.Na.error}):this.K?new Promise((a,b)=>{this.ea.push({resolve:a,reject:b})}):Promise.resolve(T)}};class th extends sh{write(a){if(0<(a=p(Uint8Array,a)).byteLength)return super.write(a)}toString(a=!1){return a?aa.decode(this.toUint8Array(!0)):this.toUint8Array(!1).then(ba)}toUint8Array(a=!1){return a?ka(this.o)[0]:(async()=>{const b=[];let c=0;for await(const d of this)b.push(d),c+=d.byteLength;return ka(b,c)[0]})()}}
class uh{constructor(a){a&&(this.source=new vh(sa(ta(a))))}[Symbol.iterator](){return this}next(a){return this.source.next(a)}throw(a){return this.source.throw(a)}return(a){return this.source.return(a)}peek(a){return this.source.peek(a)}read(a){return this.source.read(a)}}
class wh{constructor(a){a instanceof wh?this.source=a.source:a instanceof th?this.source=new xh(sa(ua(a))):fa(a)?this.source=new xh(sa(ya(a))):ea(a)?this.source=new xh(sa(va(a))):m(a)&&ea(a.body)?this.source=new xh(sa(va(a.body))):m(a)&&l(a[Symbol.iterator])?this.source=new xh(sa(ta(a))):m(a)&&l(a.then)?this.source=new xh(sa(ua(a))):m(a)&&l(a[Symbol.asyncIterator])&&(this.source=new xh(sa(ua(a))))}[Symbol.asyncIterator](){return this}next(a){return this.source.next(a)}throw(a){return this.source.throw(a)}return(a){return this.source.return(a)}get closed(){return this.source.closed}cancel(a){return this.source.cancel(a)}peek(a){return this.source.peek(a)}read(a){return this.source.read(a)}}
class vh{constructor(a){this.source=a}cancel(a){this.return(a)}peek(a){return this.next(a,"peek").value}read(a){return this.next(a,"read").value}next(a,b="read"){return this.source.next({M:b,size:a})}throw(a){return Object.create(this.source.throw&&this.source.throw(a)||T)}return(a){return Object.create(this.source.return&&this.source.return(a)||T)}}
class xh{constructor(a){this.source=a;this.Ka=new Promise(b=>this.K=b)}async cancel(a){await this.return(a)}get closed(){return this.Ka}async read(a){return(await this.next(a,"read")).value}async peek(a){return(await this.next(a,"peek")).value}async next(a,b="read"){return await this.source.next({M:b,size:a})}async throw(a){a=this.source.throw&&await this.source.throw(a)||T;this.K&&this.K();this.K=void 0;return Object.create(a)}async return(a){a=this.source.return&&await this.source.return(a)||T;
this.K&&this.K();this.K=void 0;return Object.create(a)}};class yh extends uh{constructor(a){super();this.position=0;this.buffer=p(Uint8Array,a);this.size=this.buffer.byteLength}u(a){const {buffer:b,byteOffset:c}=this.da(a,4);return(new DataView(b,c)).getInt32(0,!0)}seek(a){this.position=Math.min(a,this.size);return a<this.size}read(a){const b=this.buffer,c=this.size,d=this.position;return b&&d<c?("number"!==typeof a&&(a=Number.POSITIVE_INFINITY),this.position=Math.min(c,d+Math.min(c-d,a)),b.subarray(d,this.position)):null}da(a,b){const c=this.buffer,d=
Math.min(this.size,a+b);return c?c.subarray(a,d):new Uint8Array(b)}close(){this.buffer&&(this.buffer=null)}throw(a){this.close();return{done:!0,value:a}}return(a){this.close();return{done:!0,value:a}}}
class zh extends wh{constructor(a,b){super();this.position=0;this.I=a;"number"===typeof b?this.size=b:this.G=(async()=>{this.size=(await a.stat()).size;delete this.G})()}async u(a){const {buffer:b,byteOffset:c}=await this.da(a,4);return(new DataView(b,c)).getInt32(0,!0)}async seek(a){this.G&&await this.G;this.position=Math.min(a,this.size);return a<this.size}async read(a){this.G&&await this.G;const b=this.I;var c=this.size,d=this.position;if(b&&d<c){"number"!==typeof a&&(a=Number.POSITIVE_INFINITY);
let e=0,f=0;a=Math.min(c,d+Math.min(c-d,a));for(c=new Uint8Array(Math.max(0,(this.position=a)-d));(d+=f)<a&&(e+=f)<c.byteLength;)({Xb:f}=await b.read(c,e,c.byteLength-e,d));return c}return null}async da(a,b){this.G&&await this.G;const c=this.I;var d=this.size;return c&&a+b<d?(d=new Uint8Array(Math.min(d,a+b)-a),(await c.read(d,0,b,a)).buffer):new Uint8Array(b)}async close(){const a=this.I;this.I=null;a&&await a.close()}async throw(a){await this.close();return{done:!0,value:a}}async return(a){await this.close();
return{done:!0,value:a}}};function Ah(a){0>a&&(a=4294967295+a+1);return`0x${a.toString(16)}`}const Bh=[1,10,100,1E3,1E4,1E5,1E6,1E7,1E8];
function Ch(a,b){const c=new Uint32Array([a.buffer[1]>>>16,a.buffer[1]&65535,a.buffer[0]>>>16,a.buffer[0]&65535]);b=new Uint32Array([b.buffer[1]>>>16,b.buffer[1]&65535,b.buffer[0]>>>16,b.buffer[0]&65535]);let d=c[3]*b[3];a.buffer[0]=d&65535;let e=d>>>16;d=c[2]*b[3];e+=d;d=c[3]*b[2]>>>0;e+=d;a.buffer[0]+=e<<16;a.buffer[1]=e>>>0<d?65536:0;a.buffer[1]+=e>>>16;a.buffer[1]+=c[1]*b[3]+c[2]*b[2]+c[3]*b[1];a.buffer[1]+=c[0]*b[3]+c[1]*b[2]+c[2]*b[1]+c[3]*b[0]<<16}
function Dh(a,b){const c=a.buffer[0]+b.buffer[0]>>>0;a.buffer[1]+=b.buffer[1];c<a.buffer[0]>>>0&&++a.buffer[1];a.buffer[0]=c}class Eh{constructor(a){this.buffer=a}high(){return this.buffer[1]}low(){return this.buffer[0]}lessThan(a){return this.buffer[1]<a.buffer[1]||this.buffer[1]===a.buffer[1]&&this.buffer[0]<a.buffer[0]}equals(a){return this.buffer[1]===a.buffer[1]&&this.buffer[0]==a.buffer[0]}greaterThan(a){return a.lessThan(this)}hex(){return`${Ah(this.buffer[1])} ${Ah(this.buffer[0])}`}}
class U extends Eh{times(a){Ch(this,a);return this}plus(a){Dh(this,a);return this}static from(a,b=new Uint32Array(2)){return U.fromString("string"===typeof a?a:a.toString(),b)}static fromNumber(a,b=new Uint32Array(2)){return U.fromString(a.toString(),b)}static fromString(a,b=new Uint32Array(2)){const c=a.length;b=new U(b);for(let d=0;d<c;){const e=8<c-d?8:c-d,f=new U(new Uint32Array([Number.parseInt(a.slice(d,d+e),10),0])),h=new U(new Uint32Array([Bh[e],0]));b.times(h);b.plus(f);d+=e}return b}static convertArray(a){const b=
new Uint32Array(2*a.length);for(let c=-1,d=a.length;++c<d;)U.from(a[c],new Uint32Array(b.buffer,b.byteOffset+8*c,2));return b}static multiply(a,b){return(new U(new Uint32Array(a.buffer))).times(b)}static add(a,b){return(new U(new Uint32Array(a.buffer))).plus(b)}}
class Fh extends Eh{negate(){this.buffer[0]=~this.buffer[0]+1;this.buffer[1]=~this.buffer[1];0==this.buffer[0]&&++this.buffer[1];return this}times(a){Ch(this,a);return this}plus(a){Dh(this,a);return this}lessThan(a){const b=this.buffer[1]<<0,c=a.buffer[1]<<0;return b<c||b===c&&this.buffer[0]<a.buffer[0]}static from(a,b=new Uint32Array(2)){return Fh.fromString("string"===typeof a?a:a.toString(),b)}static fromNumber(a,b=new Uint32Array(2)){return Fh.fromString(a.toString(),b)}static fromString(a,b=
new Uint32Array(2)){const c=a.startsWith("-"),d=a.length;b=new Fh(b);for(let e=c?1:0;e<d;){const f=8<d-e?8:d-e,h=new Fh(new Uint32Array([Number.parseInt(a.slice(e,e+f),10),0])),k=new Fh(new Uint32Array([Bh[f],0]));b.times(k);b.plus(h);e+=f}return c?b.negate():b}static convertArray(a){const b=new Uint32Array(2*a.length);for(let c=-1,d=a.length;++c<d;)Fh.from(a[c],new Uint32Array(b.buffer,b.byteOffset+8*c,2));return b}static multiply(a,b){return(new Fh(new Uint32Array(a.buffer))).times(b)}static add(a,
b){return(new Fh(new Uint32Array(a.buffer))).plus(b)}}
class Gh{constructor(a){this.buffer=a}high(){return new Fh(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}low(){return new Fh(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}negate(){this.buffer[0]=~this.buffer[0]+1;this.buffer[1]=~this.buffer[1];this.buffer[2]=~this.buffer[2];this.buffer[3]=~this.buffer[3];0==this.buffer[0]&&++this.buffer[1];0==this.buffer[1]&&++this.buffer[2];0==this.buffer[2]&&++this.buffer[3];return this}times(a){const b=new U(new Uint32Array([this.buffer[3],
0])),c=new U(new Uint32Array([this.buffer[2],0])),d=new U(new Uint32Array([this.buffer[1],0])),e=new U(new Uint32Array([this.buffer[0],0])),f=new U(new Uint32Array([a.buffer[3],0])),h=new U(new Uint32Array([a.buffer[2],0])),k=new U(new Uint32Array([a.buffer[1],0]));a=new U(new Uint32Array([a.buffer[0],0]));let n=U.multiply(e,a);this.buffer[0]=n.low();const q=new U(new Uint32Array([n.high(),0]));n=U.multiply(d,a);q.plus(n);n=U.multiply(e,k);q.plus(n);this.buffer[1]=q.low();this.buffer[3]=q.lessThan(n)?
1:0;this.buffer[2]=q.high();(new U(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))).plus(U.multiply(c,a)).plus(U.multiply(d,k)).plus(U.multiply(e,h));this.buffer[3]+=U.multiply(b,a).plus(U.multiply(c,k)).plus(U.multiply(d,h)).plus(U.multiply(e,f)).low();return this}plus(a){const b=new Uint32Array(4);b[3]=this.buffer[3]+a.buffer[3]>>>0;b[2]=this.buffer[2]+a.buffer[2]>>>0;b[1]=this.buffer[1]+a.buffer[1]>>>0;b[0]=this.buffer[0]+a.buffer[0]>>>0;b[0]<this.buffer[0]>>>0&&++b[1];b[1]<this.buffer[1]>>>
0&&++b[2];b[2]<this.buffer[2]>>>0&&++b[3];this.buffer[3]=b[3];this.buffer[2]=b[2];this.buffer[1]=b[1];this.buffer[0]=b[0];return this}hex(){return`${Ah(this.buffer[3])} ${Ah(this.buffer[2])} ${Ah(this.buffer[1])} ${Ah(this.buffer[0])}`}static multiply(a,b){return(new Gh(new Uint32Array(a.buffer))).times(b)}static add(a,b){return(new Gh(new Uint32Array(a.buffer))).plus(b)}static from(a,b=new Uint32Array(4)){return Gh.fromString("string"===typeof a?a:a.toString(),b)}static fromNumber(a,b=new Uint32Array(4)){return Gh.fromString(a.toString(),
b)}static fromString(a,b=new Uint32Array(4)){const c=a.startsWith("-"),d=a.length;b=new Gh(b);for(let e=c?1:0;e<d;){const f=8<d-e?8:d-e,h=new Gh(new Uint32Array([Number.parseInt(a.slice(e,e+f),10),0,0,0])),k=new Gh(new Uint32Array([Bh[f],0,0,0]));b.times(k);b.plus(h);e+=f}return c?b.negate():b}static convertArray(a){const b=new Uint32Array(4*a.length);for(let c=-1,d=a.length;++c<d;)Gh.from(a[c],new Uint32Array(b.buffer,b.byteOffset+16*c,4));return b}}var Hh={};Hh.BaseInt64=Eh;Hh.Int128=Gh;
Hh.Int64=Fh;Hh.Uint64=U;function V(a){return a.L[++a.Ob]}function Ih(a){return a.buffers[++a.Nb]}
class Jh extends ze{constructor(a,b,c,d,e=t.V5){super();this.Nb=this.Ob=-1;this.R=a;this.L=b;this.buffers=c;this.dictionaries=d;this.Ga=e}visit(a){return super.visit(a instanceof S?a.type:a)}visitNull(a,{length:b}=V(this)){return O({type:a,length:b})}visitBool(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitInt(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitFloat(a,
{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitUtf8(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),valueOffsets:this.$(a),data:this.F(a)})}visitLargeUtf8(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),valueOffsets:this.$(a),data:this.F(a)})}visitBinary(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,
c),valueOffsets:this.$(a),data:this.F(a)})}visitLargeBinary(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),valueOffsets:this.$(a),data:this.F(a)})}visitFixedSizeBinary(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitDate(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitTimestamp(a,{length:b,nullCount:c}=V(this)){return O({type:a,
length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitTime(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitDecimal(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitList(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),valueOffsets:this.$(a),child:this.visit(a.children[0])})}visitStruct(a,{length:b,nullCount:c}=
V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),children:this.visitMany(a.children)})}visitUnion(a,{length:b,nullCount:c}=V(this)){this.Ga<t.V5&&this.C(a,c);return a.mode===u.Sparse?this.visitSparseUnion(a,{length:b,nullCount:c}):this.visitDenseUnion(a,{length:b,nullCount:c})}visitDenseUnion(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,typeIds:this.bb(a),valueOffsets:this.$(a),children:this.visitMany(a.children)})}visitSparseUnion(a,{length:b,nullCount:c}=
V(this)){return O({type:a,length:b,nullCount:c,typeIds:this.bb(a),children:this.visitMany(a.children)})}visitDictionary(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a.indices),dictionary:this.dictionaries.get(a.id)})}visitInterval(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitDuration(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,
c),data:this.F(a)})}visitFixedSizeList(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),child:this.visit(a.children[0])})}visitMap(a,{length:b,nullCount:c}=V(this)){return O({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),valueOffsets:this.$(a),child:this.visit(a.children[0])})}C(a,b){var c=Ih(this);return 0<b&&this.F(a,c)||new Uint8Array(0)}$(a){return this.F(a)}bb(a){return this.F(a)}F(a,{length:b,offset:c}=Ih(this)){return this.R.subarray(c,c+b)}}
class Kh extends Jh{constructor(a,b,c,d,e){super(new Uint8Array(0),b,c,d,e);this.sources=a}C(a,b){({offset:a}=Ih(this));return 0>=b?new Uint8Array(0):sg(this.sources[a])}$(a){var {offset:b}=Ih(this);return p(Uint8Array,p(a.OffsetArrayType,this.sources[b]))}bb(a){var {offset:b}=Ih(this);return p(Uint8Array,p(a.ArrayType,this.sources[b]))}F(a,{offset:b}=Ih(this)){const c=this.sources;return I.isTimestamp(a)||(I.isInt(a)||I.isTime(a))&&64===a.bitWidth||I.isDuration(a)||I.isDate(a)&&a.unit===Ca.MILLISECOND?
p(Uint8Array,Fh.convertArray(c[b])):I.isDecimal(a)?p(Uint8Array,Gh.convertArray(c[b])):I.isBinary(a)||I.isLargeBinary(a)||I.isFixedSizeBinary(a)?Lh(c[b]):I.isBool(a)?sg(c[b]):I.isUtf8(a)||I.isLargeUtf8(a)?ca.encode(c[b].join("")):p(Uint8Array,p(a.ArrayType,c[b].map(d=>+d)))}}function Lh(a){a=a.join("");const b=new Uint8Array(a.length/2);for(let c=0;c<a.length;c+=2)b[c>>1]=Number.parseInt(a.slice(c,c+2),16);return b};class Mh extends fh{constructor(a){super(a);this.o=new Zg(Uint8Array)}get byteLength(){let a=this.J+4*this.length;this.j&&(a+=this.j.byteLength);this.o&&(a+=this.o.byteLength);this.D&&(a+=this.D.byteLength);return a}setValue(a,b){return super.setValue(a,p(Uint8Array,b))}S(a,b){const c=this.j;b=Yg(this.o,b).buffer;let d=0;for(const [e,f]of a)void 0===f?c.set(e,0):(a=f.length,b.set(f,d),c.set(e,a),d+=a)}};class Nh extends fh{constructor(a){super(a);this.o=new Zg(Uint8Array)}get byteLength(){let a=this.J+4*this.length;this.j&&(a+=this.j.byteLength);this.o&&(a+=this.o.byteLength);this.D&&(a+=this.D.byteLength);return a}setValue(a,b){return super.setValue(a,p(Uint8Array,b))}S(a,b){const c=this.j;b=Yg(this.o,b).buffer;let d=0;for(const [e,f]of a)void 0===f?c.set(e,BigInt(0)):(a=f.length,b.set(f,d),c.set(e,BigInt(a)),d+=a)}};class Oh extends ch{constructor(a){super(a);this.o=new ah}setValue(a,b){this.o.set(a,+b)}};class Ph extends dh{}Ph.prototype.A=Se;class Qh extends Ph{}Qh.prototype.A=Ne;class Rh extends Ph{}Rh.prototype.A=Oe;class Sh extends dh{}Sh.prototype.A=cf;class Th extends ch{constructor({type:a,nullValues:b,dictionaryHashFunction:c}){super({type:new ue(a.dictionary,a.indices,a.id,a.isOrdered)});this.D=null;this.va=0;this.mb=Object.create(null);this.indices=Uh({type:this.type.indices,nullValues:b});this.dictionary=Uh({type:this.type.dictionary,nullValues:null});"function"===typeof c&&(this.valueToKey=c)}get values(){return this.indices.values}get nullCount(){return this.indices.nullCount}get nullBitmap(){return this.indices.nullBitmap}get byteLength(){return this.indices.byteLength+
this.dictionary.byteLength}get reservedLength(){return this.indices.reservedLength+this.dictionary.reservedLength}get reservedByteLength(){return this.indices.reservedByteLength+this.dictionary.reservedByteLength}isValid(a){return this.indices.isValid(a)}setValid(a,b){const c=this.indices;b=c.setValid(a,b);this.length=c.length;return b}setValue(a,b){const c=this.mb,d=this.valueToKey(b);let e=c[d];void 0===e&&(c[d]=e=this.va+this.dictionary.append(b).length-1);return this.indices.setValue(a,e)}flush(){var a=
this.type;const b=this.Gb,c=this.dictionary.toVector();a=this.indices.flush().clone(a);a.dictionary=b?b.concat(c):c;this.finished||(this.va+=c.length);this.Gb=a.dictionary;this.clear();return a}finish(){this.indices.finish();this.dictionary.finish();this.va=0;this.mb=Object.create(null);return super.finish()}clear(){this.indices.clear();this.dictionary.clear();return super.clear()}valueToKey(a){return"string"===typeof a?a:`${a}`}};class Vh extends dh{}Vh.prototype.A=Pe;class Wh extends ch{setValue(a,b){const [c]=this.children;a*=this.stride;for(let d=-1,e=b.length;++d<e;)c.set(a+d,b[d])}addChild(a,b="0"){if(0<this.numChildren)throw Error("FixedSizeListBuilder can only have one child.");const c=this.children.push(a);this.type=new le(this.type.listSize,new S(b,a.type,!0));return c}};class Xh extends dh{setValue(a,b){this.o.set(a,b)}}class Yh extends Xh{setValue(a,b){super.setValue(a,Ge(b))}}class Zh extends Xh{}class $h extends Xh{};class ai extends dh{}ai.prototype.A=nf;class bi extends ai{}bi.prototype.A=lf;class ci extends ai{}ci.prototype.A=mf;class di extends dh{}di.prototype.A=sf;class ei extends di{}ei.prototype.A=of;class fi extends di{}fi.prototype.A=pf;class gi extends di{}gi.prototype.A=qf;class hi extends di{}hi.prototype.A=rf;class ii extends dh{setValue(a,b){this.o.set(a,b)}}class ji extends ii{}class ki extends ii{}class li extends ii{}class mi extends ii{}class ni extends ii{}class oi extends ii{}class pi extends ii{}class qi extends ii{};class ri extends fh{constructor(a){super(a);this.j=new bh(a.type)}addChild(a,b="0"){if(0<this.numChildren)throw Error("ListBuilder can only have one child.");this.children[this.numChildren]=a;this.type=new Vd(new S(b,a.type,!0));return this.numChildren-1}S(a){const b=this.j,[c]=this.children;for(const [d,e]of a)if("undefined"===typeof e)b.set(d,0);else{a=e;const f=a.length,h=b.set(d,f).buffer[d];for(let k=-1;++k<f;)c.set(h+k,a[k])}}};class si extends fh{set(a,b){return super.set(a,b)}setValue(a,b){b=b instanceof Map?b:new Map(Object.entries(b));const c=this.G||(this.G=new Map),d=c.get(a);d&&(this.J-=d.size);this.J+=b.size;c.set(a,b)}addChild(a,b=`${this.numChildren}`){if(0<this.numChildren)throw Error("ListBuilder can only have one child.");this.children[this.numChildren]=a;this.type=new pe(new S(b,a.type,!0),this.type.keysSorted);return this.numChildren-1}S(a){const b=this.j,[c]=this.children;for(const [d,e]of a)if(void 0===
e)b.set(d,0);else{let {[d]:f,[d+1]:h}=b.set(d,e.size).buffer;for(const k of e.entries())if(c.set(f,k),++f>=h)break}}};class ti extends ch{setValue(){}setValid(a,b){this.length=Math.max(a+1,this.length);return b}};class ui extends ch{setValue(a,b){const c=this.children,d=this.type;switch(Array.isArray(b)||b.constructor){case !0:return d.children.forEach((e,f)=>c[f].set(a,b[f]));case Map:return d.children.forEach((e,f)=>c[f].set(a,b.get(e.name)));default:return d.children.forEach((e,f)=>c[f].set(a,b[e.name]))}}setValid(a,b){super.setValid(a,b)||this.children.forEach(c=>c.setValid(a,b));return b}addChild(a,b=`${this.numChildren}`){const c=this.children.push(a);this.type=new J([...this.type.children,new S(b,a.type,
!0)]);return c}};class vi extends dh{}vi.prototype.A=Xe;class wi extends vi{}wi.prototype.A=Te;class xi extends vi{}xi.prototype.A=Ue;class yi extends vi{}yi.prototype.A=Ve;class zi extends vi{}zi.prototype.A=We;class Ai extends dh{}Ai.prototype.A=bf;class Bi extends Ai{}Bi.prototype.A=Ye;class Ci extends Ai{}Ci.prototype.A=Ze;class Di extends Ai{}Di.prototype.A=$e;class Ei extends Ai{}Ei.prototype.A=af;class Fi extends ch{constructor(a){super(a);this.X=new $g(Int8Array,0,1);"function"===typeof a.valueToChildTypeId&&(this.ob=a.valueToChildTypeId)}get typeIdToChildIndex(){return this.type.typeIdToChildIndex}append(a,b){return this.set(this.length,a,b)}set(a,b,c){void 0===c&&(c=this.ob(this,b,a));this.setValue(a,b,c);return this}setValue(a,b,c){this.X.set(a,c);this.children[this.type.typeIdToChildIndex[c]]?.set(a,b)}addChild(a,b=`${this.children.length}`){const c=this.children.push(a),d=this.type,
e=d.mode,f=d.typeIds;a=[...d.children,new S(b,a.type)];this.type=new be(e,[...f,c],a);return c}ob(){throw Error("Cannot map UnionBuilder value to child typeId. Pass the `childTypeId` as the second argument to unionBuilder.append(), or supply a `valueToChildTypeId` function as part of the UnionBuilder constructor options.");}}class Gi extends Fi{}
class Hi extends Fi{constructor(a){super(a);this.j=new $g(Int32Array)}setValue(a,b,c){c=this.X.set(a,c).buffer[a];c=this.getChildAt(this.type.typeIdToChildIndex[c]);a=this.j.set(a,c.length).buffer[a];c?.set(a,b)}};class Ii extends fh{constructor(a){super(a);this.o=new Zg(Uint8Array)}get byteLength(){let a=this.J+4*this.length;this.j&&(a+=this.j.byteLength);this.o&&(a+=this.o.byteLength);this.D&&(a+=this.D.byteLength);return a}setValue(a,b){return super.setValue(a,ca.encode(b))}S(){}}Ii.prototype.S=Mh.prototype.S;class Mi extends fh{constructor(a){super(a);this.o=new Zg(Uint8Array)}get byteLength(){let a=this.J+4*this.length;this.j&&(a+=this.j.byteLength);this.o&&(a+=this.o.byteLength);this.D&&(a+=this.D.byteLength);return a}setValue(a,b){return super.setValue(a,ca.encode(b))}S(){}}Mi.prototype.S=Nh.prototype.S;class Ni extends ze{visitNull(){return ti}visitBool(){return Oh}visitInt(){return ii}visitInt8(){return ji}visitInt16(){return ki}visitInt32(){return li}visitInt64(){return mi}visitUint8(){return ni}visitUint16(){return oi}visitUint32(){return pi}visitUint64(){return qi}visitFloat(){return Xh}visitFloat16(){return Yh}visitFloat32(){return Zh}visitFloat64(){return $h}visitUtf8(){return Ii}visitLargeUtf8(){return Mi}visitBinary(){return Mh}visitLargeBinary(){return Nh}visitFixedSizeBinary(){return Vh}visitDate(){return Ph}visitDateDay(){return Qh}visitDateMillisecond(){return Rh}visitTimestamp(){return vi}visitTimestampSecond(){return wi}visitTimestampMillisecond(){return xi}visitTimestampMicrosecond(){return yi}visitTimestampNanosecond(){return zi}visitTime(){return Ai}visitTimeSecond(){return Bi}visitTimeMillisecond(){return Ci}visitTimeMicrosecond(){return Di}visitTimeNanosecond(){return Ei}visitDecimal(){return Sh}visitList(){return ri}visitStruct(){return ui}visitUnion(){return Fi}visitDenseUnion(){return Hi}visitSparseUnion(){return Gi}visitDictionary(){return Th}visitInterval(){return ai}visitIntervalDayTime(){return bi}visitIntervalYearMonth(){return ci}visitDuration(){return di}visitDurationSecond(){return ei}visitDurationMillisecond(){return fi}visitDurationMicrosecond(){return gi}visitDurationNanosecond(){return hi}visitFixedSizeList(){return Wh}visitMap(){return si}}
const Oi=new Ni;function Pi(a,b,c){return b===c||Array.isArray(b)&&Array.isArray(c)&&b.length===c.length&&b.every((d,e)=>a.compareFields(d,c[e]))}class Qi extends ze{compareSchemas(a,b){return a===b||b instanceof a.constructor&&Pi(this,a.fields,b.fields)}compareFields(a,b){return a===b||b instanceof a.constructor&&a.name===b.name&&a.nullable===b.nullable&&this.visit(a.type,b.type)}}function Ri(a,b){return b instanceof a.constructor}function Si(a,b){return a===b||Ri(a,b)}
function Ti(a,b){return a===b||Ri(a,b)&&a.bitWidth===b.bitWidth&&a.isSigned===b.isSigned}function Ui(a,b){return a===b||Ri(a,b)&&a.precision===b.precision}function Vi(a,b){return a===b||Ri(a,b)&&a.unit===b.unit}function Wi(a,b){return a===b||Ri(a,b)&&a.unit===b.unit&&a.timezone===b.timezone}function Xi(a,b){return a===b||Ri(a,b)&&a.unit===b.unit&&a.bitWidth===b.bitWidth}
function Yi(a,b){return a===b||Ri(a,b)&&a.mode===b.mode&&a.typeIds.every((c,d)=>c===b.typeIds[d])&&Pi(Zi,a.children,b.children)}function $i(a,b){return a===b||Ri(a,b)&&a.unit===b.unit}function aj(a,b){return a===b||Ri(a,b)&&a.unit===b.unit}g=Qi.prototype;g.visitNull=Si;g.visitBool=Si;g.visitInt=Ti;g.visitInt8=Ti;g.visitInt16=Ti;g.visitInt32=Ti;g.visitInt64=Ti;g.visitUint8=Ti;g.visitUint16=Ti;g.visitUint32=Ti;g.visitUint64=Ti;g.visitFloat=Ui;g.visitFloat16=Ui;g.visitFloat32=Ui;g.visitFloat64=Ui;
g.visitUtf8=Si;g.visitLargeUtf8=Si;g.visitBinary=Si;g.visitLargeBinary=Si;g.visitFixedSizeBinary=function(a,b){return a===b||Ri(a,b)&&a.byteWidth===b.byteWidth};g.visitDate=Vi;g.visitDateDay=Vi;g.visitDateMillisecond=Vi;g.visitTimestamp=Wi;g.visitTimestampSecond=Wi;g.visitTimestampMillisecond=Wi;g.visitTimestampMicrosecond=Wi;g.visitTimestampNanosecond=Wi;g.visitTime=Xi;g.visitTimeSecond=Xi;g.visitTimeMillisecond=Xi;g.visitTimeMicrosecond=Xi;g.visitTimeNanosecond=Xi;g.visitDecimal=Si;
g.visitList=function(a,b){return a===b||Ri(a,b)&&a.children.length===b.children.length&&Pi(Zi,a.children,b.children)};g.visitStruct=function(a,b){return a===b||Ri(a,b)&&a.children.length===b.children.length&&Pi(Zi,a.children,b.children)};g.visitUnion=Yi;g.visitDenseUnion=Yi;g.visitSparseUnion=Yi;g.visitDictionary=function(a,b){return a===b||Ri(a,b)&&a.id===b.id&&a.isOrdered===b.isOrdered&&Zi.visit(a.indices,b.indices)&&Zi.visit(a.dictionary,b.dictionary)};g.visitInterval=$i;
g.visitIntervalDayTime=$i;g.visitIntervalYearMonth=$i;g.visitDuration=aj;g.visitDurationSecond=aj;g.visitDurationMillisecond=aj;g.visitDurationMicrosecond=aj;g.visitDurationNanosecond=aj;g.visitFixedSizeList=function(a,b){return a===b||Ri(a,b)&&a.listSize===b.listSize&&a.children.length===b.children.length&&Pi(Zi,a.children,b.children)};g.visitMap=function(a,b){return a===b||Ri(a,b)&&a.keysSorted===b.keysSorted&&a.children.length===b.children.length&&Pi(Zi,a.children,b.children)};const Zi=new Qi;
function bj(a,b){return Zi.compareSchemas(a,b)}function cj(a,b){return Zi.visit(a,b)};function Uh(a){var b=a.type;const c=new (Oi.getVisitFn(b)())(a);if(b.children&&0<b.children.length){const d=a.children||[],e={nullValues:a.nullValues};a=Array.isArray(d)?(f,h)=>d[h]||e:({name:f})=>d[f]||e;for(const [f,h]of b.children.entries()){b=h.type;const k=a(h,f);c.children.push(Uh({...k,type:b}))}}return c}
function dj(a,b){if(a instanceof N||a instanceof L||a.type instanceof I||ArrayBuffer.isView(a))return Ug(a);b={type:b??ej(a),nullValues:[null]};a=[...fj(b)(a)];a=1===a.length?a[0]:a.reduce((c,d)=>c.concat(d));return I.isDictionary(a.type)?a.memoize():a}
function ej(a){if(0===a.length)return new uc;var b=0;let c=0,d=0,e=0,f=0,h=0,k=0,n=0;for(const q of a)if(null==q)++b;else{switch(typeof q){case "bigint":++h;continue;case "boolean":++k;continue;case "number":++e;continue;case "string":++f;continue;case "object":Array.isArray(q)?++c:"[object Date]"===Object.prototype.toString.call(q)?++n:++d;continue}throw new TypeError("Unable to infer Vector type from input values, explicit type declaration expected.");}if(e+b===a.length)return new Nc;if(f+b===a.length)return new ue(new Wc,
new Bc);if(h+b===a.length)return new Cc;if(k+b===a.length)return new dd;if(n+b===a.length)return new Ed;if(c+b===a.length){const q=ej(a[a.findIndex(B=>null!=B)]);if(a.every(B=>null==B||cj(q,ej(B))))return new Vd(new S("",q,!0))}else if(d+b===a.length){b=new Map;for(const q of a)for(const B of Object.keys(q))b.has(B)||null==q[B]||b.set(B,new S(B,ej([q[B]]),!0));return new J([...b.values()])}throw new TypeError("Unable to infer Vector type from input values, explicit type declaration expected.");}
function fj(a){const {queueingStrategy:b="count"}=a,{highWaterMark:c="bytes"!==b?Number.POSITIVE_INFINITY:16384}=a,d="bytes"!==b?"length":"byteLength";return function*(e){let f=0;const h=Uh(a);for(const k of e)h.append(k)[d]>=c&&++f&&(yield h.toVector());if(0<h.finish().length||0===f)yield h.toVector()}};function gj(a,b){return hj(a,b.map(c=>c.data.concat()))}function hj(a,b){const c=[...a.fields],d=[],e={Za:b.reduce((Na,ib)=>Math.max(Na,ib.length),0)};let f=0,h=0,k=-1;const n=b.length;let q,B=[];for(;0<e.Za--;){h=Number.POSITIVE_INFINITY;for(k=-1;++k<n;)B[k]=q=b[k].shift(),h=Math.min(h,q?q.length:h);Number.isFinite(h)&&(B=ij(c,h,B,b,e),0<h&&(d[f++]=O({type:new J(c),length:h,nullCount:0,children:B.slice()})))}return[a=a.assign(c),d.map(Na=>new W(a,Na))]}
function ij(a,b,c,d,e){const f=(b+63&-64)>>3;for(let k=-1,n=d.length;++k<n;){const q=c[k];var h=q?.length;h>=b?h===b?c[k]=q:(c[k]=q.slice(0,b),e.Za=Math.max(e.Za,d[k].unshift(q.slice(b,h-b)))):(h=a[k],a[k]=h.clone({nullable:!0}),c[k]=q?.Ja(b)??O({type:h.type,length:b,nullCount:b,nullBitmap:new Uint8Array(f)}))}return c};class X{constructor(...a){if(0===a.length)return this.batches=[],this.schema=new R([]),this.j=[0],this;let b,c;a[0]instanceof R&&(b=a.shift());a.at(-1)instanceof Uint32Array&&(c=a.pop());const d=e=>{if(e){if(e instanceof W)return[e];if(e instanceof X)return e.batches;if(e instanceof N){if(e.type instanceof J)return[new W(new R(e.type.children),e)]}else{if(Array.isArray(e))return e.flatMap(h=>d(h));if("function"===typeof e[Symbol.iterator])return[...e].flatMap(h=>d(h));if("object"===typeof e){var f=
Object.keys(e);const h=f.map(k=>new L([e[k]]));f=b??new R(f.map((k,n)=>new S(String(k),h[n].type,h[n].nullable)));[,f]=gj(f,h);return 0===f.length?[new W(e)]:f}}}return[]};a=a.flatMap(e=>d(e));b=b??a[0]?.schema??new R([]);if(!(b instanceof R))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");for(const e of a){if(!(e instanceof W))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");if(!bj(b,e.schema))throw new TypeError("Table and inner RecordBatch schemas must be equivalent.");
}this.schema=b;this.batches=a;this.j=c??Eg(this.data)}get data(){return this.batches.map(({data:a})=>a)}get numCols(){return this.schema.fields.length}get numRows(){return this.data.reduce((a,b)=>a+b.length,0)}get nullCount(){-1===this.N&&(this.N=Dg(this.data));return this.N}isValid(){return!1}get(){return null}at(a){return this.get(0>a?this.numRows+a:a)}set(){}indexOf(){return-1}[Symbol.iterator](){return 0<this.batches.length?Qg.visit(new L(this.data)):[][Symbol.iterator]()}toArray(){return[...this]}toString(){return`[\n  ${this.toArray().join(",\n  ")}\n]`}concat(...a){const b=
this.schema;a=this.data.concat(a.flatMap(({data:c})=>c));return new X(b,a.map(c=>new W(b,c)))}slice(a,b){const c=this.schema;[a,b]=fg({length:this.numRows},a,b);a=Fg(this.data,this.j,a,b);return new X(c,a.map(d=>new W(c,d)))}getChild(a){return this.getChildAt(this.schema.fields.findIndex(b=>b.name===a))}getChildAt(a){if(-1<a&&a<this.schema.fields.length){const c=this.data.map(d=>d.children[a]);if(0===c.length){var {type:b}=this.schema.fields[a];b=O({type:b,length:0,nullCount:0});c.push(b.Ja(this.numRows))}return new L(c)}return null}setChild(a,
b){return this.setChildAt(this.schema.fields?.findIndex(c=>c.name===a),b)}setChildAt(a,b){let c=this.schema;var d=[...this.batches];if(-1<a&&a<this.numCols){b||=new L([O({type:new uc,length:this.numRows})]);d=c.fields.slice();const e=d[a].clone({type:b.type}),f=this.schema.fields.map((h,k)=>this.getChildAt(k));[d[a],f[a]]=[e,b];[c,d]=gj(c,f)}return new X(c,d)}select(a){const b=this.schema.fields.reduce((c,d,e)=>c.set(d.name,e),new Map);return this.selectAt(a.map(c=>b.get(c)).filter(c=>-1<c))}selectAt(a){const b=
this.schema.selectAt(a),c=this.batches.map(d=>d.selectAt(a));return new X(b,c)}assign(a){const b=this.schema.fields,[c,d]=a.schema.fields.reduce((h,k,n)=>{const [q,B]=h,Na=b.findIndex(ib=>ib.name===k.name);~Na?B[Na]=n:q.push(n);return h},[[],[]]),e=this.schema.assign(a.schema),f=[...b.map((h,k)=>[k,d[k]]).map(([h,k])=>void 0===k?this.getChildAt(h):a.getChildAt(k)),...c.map(h=>a.getChildAt(h))].filter(Boolean);return new X(...gj(e,f))}}var jj=Symbol.toStringTag,kj=X.prototype;kj.schema=null;
kj.batches=[];kj.j=new Uint32Array([0]);kj.N=-1;kj[Symbol.isConcatSpreadable]=!0;kj.isValid=Ig(Hg);kj.get=Ig(xf.getVisitFn(F.Struct));kj.set=Jg(hf.getVisitFn(F.Struct));kj.indexOf=Kg(Ng.getVisitFn(F.Struct));X[jj]="Table";class W{constructor(...a){switch(a.length){case 2:[this.schema]=a;if(!(this.schema instanceof R))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");[,this.data=O({nullCount:0,type:new J(this.schema.fields),children:this.schema.fields.map(h=>O({type:h.type,nullCount:0}))})]=a;if(!(this.data instanceof N))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");[this.schema,this.data]=lj(this.schema,this.data.children);break;case 1:const [b]=a,{fields:c,
children:d,length:e}=Object.keys(b).reduce((h,k,n)=>{h.children[n]=b[k];h.length=Math.max(h.length,b[k].length);h.fields[n]=S.new({name:k,type:b[k].type,nullable:!0});return h},{length:0,fields:[],children:[]});a=new R(c);const f=O({type:new J(c),length:e,children:d,nullCount:0});[this.schema,this.data]=lj(a,f.children,e);break;default:throw new TypeError("RecordBatch constructor expects an Object mapping names to child Data, or a [Schema, Data] pair.");}}get dictionaries(){return this.Fb||(this.Fb=
mj(this.schema.fields,this.data.children))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.length}get nullCount(){return this.data.nullCount}isValid(a){return this.data.getValid(a)}get(a){return xf.visit(this.data,a)}at(a){return this.get(0>a?this.numRows+a:a)}set(a,b){return hf.visit(this.data,a,b)}indexOf(a,b){return Ng.visit(this.data,a,b)}[Symbol.iterator](){return Qg.visit(new L([this.data]))}toArray(){return[...this]}concat(...a){return new X(this.schema,[this,
...a])}slice(a,b){[a]=(new L([this.data])).slice(a,b).data;return new W(this.schema,a)}getChild(a){return this.getChildAt(this.schema.fields?.findIndex(b=>b.name===a))}getChildAt(a){return-1<a&&a<this.schema.fields.length?new L([this.data.children[a]]):null}setChild(a,b){return this.setChildAt(this.schema.fields?.findIndex(c=>c.name===a),b)}setChildAt(a,b){var c=this.schema,d=this.data;if(-1<a&&a<this.numCols){b||=new L([O({type:new uc,length:this.numRows})]);const e=c.fields.slice();d=d.children.slice();
c=e[a].clone({type:b.type});[e[a],d[a]]=[c,b.data[0]];c=new R(e,new Map(this.schema.metadata));d=O({type:new J(e),children:d})}return new W(c,d)}select(a){const b=this.schema.select(a),c=new J(b.fields),d=[];for(const e of a)a=this.schema.fields.findIndex(f=>f.name===e),~a&&(d[a]=this.data.children[a]);return new W(b,O({type:c,length:this.numRows,children:d}))}selectAt(a){const b=this.schema.selectAt(a);a=a.map(c=>this.data.children[c]).filter(Boolean);a=O({type:new J(b.fields),length:this.numRows,
children:a});return new W(b,a)}}var nj=Symbol.toStringTag,oj=W.prototype;oj.N=-1;oj[Symbol.isConcatSpreadable]=!0;W[nj]="RecordBatch";function lj(a,b,c=b.reduce((d,e)=>Math.max(d,e.length),0)){const d=[...a.fields],e=[...b],f=(c+63&-64)>>3;for(const [h,k]of a.fields.entries()){const n=b[h];n&&n.length===c||(d[h]=k.clone({nullable:!0}),e[h]=n?.Ja(c)??O({type:k.type,length:c,nullCount:c,nullBitmap:new Uint8Array(f)}))}return[a.assign(d),O({type:new J(d),length:c,children:e})]}
function mj(a,b,c=new Map){if(0<(a?.length??0)&&a?.length===b?.length)for(let e=-1,f=a.length;++e<f;){var {type:d}=a[e];const h=b[e];for(const k of[h,...(h?.dictionary?.data||[])])mj(d.children,k?.children,c);if(I.isDictionary(d))if(d=d.id,!c.has(d))h?.dictionary&&c.set(d,h.dictionary);else if(c.get(d)!==h.dictionary)throw Error("Cannot create Schema containing two different dictionaries with the same Id");}return c}
class pj extends W{constructor(a){var b=a.fields.map(c=>O({type:c.type}));b=O({type:new J(a.fields),nullCount:0,children:b});super(a,b)}};class qj{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}version(){const a=x(this.g,this.h,4);return a?Ka(this.g,this.h+a):t.V1}headerType(){const a=x(this.g,this.h,6);return a?this.g.l[this.h+a]:E.NONE}header(a){const b=x(this.g,this.h,8);return b?Ma(this.g,a,this.h+b):null}bodyLength(){const a=x(this.g,this.h,10);return a?La(this.g,this.h+a):BigInt("0")}Ca(a){const b=x(this.g,this.h,12);return b?(new Cb).i(Pa(this.g,Qa(this.g,this.h+b)+4*a),this.g):null}Da(){const a=x(this.g,
this.h,12);return a?Ra(this.g,this.h+a):0}};class rj extends ze{visit(a,b){return null==a||null==b?void 0:super.visit(a,b)}visitNull(a,b){z(b,0);return A(b)}visitInt(a,b){z(b,2);$a(b,0,a.bitWidth,0);Ya(b,1,+a.isSigned,0);return A(b)}visitFloat(a,b){z(b,1);Za(b,a.precision,v.HALF);return A(b)}visitBinary(a,b){z(b,0);return A(b)}visitLargeBinary(a,b){z(b,0);return A(b)}visitBool(a,b){z(b,0);return A(b)}visitUtf8(a,b){z(b,0);return A(b)}visitLargeUtf8(a,b){z(b,0);return A(b)}visitDecimal(a,b){z(b,3);$a(b,1,a.scale,0);$a(b,0,a.precision,0);$a(b,
2,a.bitWidth,128);return A(b)}visitDate(a,b){z(b,1);Za(b,a.unit,Ca.MILLISECOND);return A(b)}visitTime(a,b){z(b,2);Za(b,a.unit,w.MILLISECOND);$a(b,1,a.bitWidth,32);return A(b)}visitTimestamp(a,b){const c=a.timezone&&fb(b,a.timezone)||void 0;z(b,2);Za(b,a.unit,w.SECOND);void 0!==c&&y(b,1,c);return A(b)}visitInterval(a,b){z(b,1);Za(b,a.unit,Fa.YEAR_MONTH);return A(b)}visitDuration(a,b){z(b,1);Za(b,a.unit,w.MILLISECOND);return A(b)}visitList(a,b){z(b,0);return A(b)}visitStruct(a,b){z(b,0);return A(b)}visitUnion(a,
b){db(b,4,a.typeIds.length,4);var c=a.typeIds;db(b,4,c.length,4);for(let d=c.length-1;0<=d;d--)Xa(b,c[d]);c=eb(b);z(b,2);Za(b,a.mode,u.Sparse);y(b,1,c);return A(b)}visitDictionary(a,b){const c=this.visit(a.indices,b);z(b,4);ab(b,0,BigInt(a.id));Ya(b,2,+a.isOrdered,0);void 0!==c&&y(b,1,c);return A(b)}visitFixedSizeBinary(a,b){z(b,1);$a(b,0,a.byteWidth,0);return A(b)}visitFixedSizeList(a,b){z(b,1);$a(b,0,a.listSize,0);return A(b)}visitMap(a,b){z(b,1);Ya(b,0,+a.keysSorted,0);return A(b)}}const sj=new rj;function tj(a){return new uj(a.count,vj(a.columns),wj(a.columns))}function xj(a,b){return(a.fields||[]).filter(Boolean).map(c=>S.fromJSON(c,b))}function yj(a,b){return(a.children||[]).filter(Boolean).map(c=>S.fromJSON(c,b))}function vj(a){return(a||[]).reduce((b,c)=>[...b,new zj(c.count,Aj(c.VALIDITY)),...vj(c.children)],[])}
function wj(a,b=[]){for(let c=-1,d=(a||[]).length;++c<d;){const e=a[c];e.VALIDITY&&b.push(new Bj(b.length,e.VALIDITY.length));e.TYPE_ID&&b.push(new Bj(b.length,e.TYPE_ID.length));e.OFFSET&&b.push(new Bj(b.length,e.OFFSET.length));e.DATA&&b.push(new Bj(b.length,e.DATA.length));b=wj(e.children,b)}return b}function Aj(a){return(a||[]).reduce((b,c)=>b+ +(0===c),0)}function Cj(a=[]){return new Map(a.map(({key:b,value:c})=>[b,c]))}function Dj(a){return new vc(a.isSigned,a.bitWidth)}
function Ej(a,b){const c=a.type.name;switch(c){case "NONE":return new uc;case "null":return new uc;case "binary":return new Oc;case "largebinary":return new Sc;case "utf8":return new Wc;case "largeutf8":return new $c;case "bool":return new dd;case "list":return new Vd((b||[])[0]);case "struct":return new J(b||[]);case "struct_":return new J(b||[])}switch(c){case "int":return b=a.type,new vc(b.isSigned,b.bitWidth);case "floatingpoint":return new Hc(v[a.type.precision]);case "decimal":return b=a.type,
new hd(b.scale,b.precision,b.bitWidth);case "date":return new ld(Ca[a.type.unit]);case "time":return b=a.type,new rd(w[b.unit],b.bitWidth);case "timestamp":return b=a.type,new zd(w[b.unit],b.timezone);case "interval":return new Hd(Fa[a.type.unit]);case "duration":return new Nd(w[a.type.unit]);case "union":a=a.type;const [d,...e]=(a.mode+"").toLowerCase();return new be(u[d.toUpperCase()+e.join("")],a.typeIds||[],b||[]);case "fixedsizebinary":return new he(a.type.byteWidth);case "fixedsizelist":return new le(a.type.listSize,
(b||[])[0]);case "map":return new pe((b||[])[0],a.type.keysSorted)}throw Error(`Unrecognized type: "${c}"`);};class Fj{static fromJSON(a,b){const c=new Fj(0,t.V5,b);c.La=Gj(a,b);return c}static decode(a){a=new Sa(p(Uint8Array,a));a=(new qj).i(a.u(a.position())+a.position(),a);var b=a.bodyLength();const c=a.version(),d=a.headerType();b=new Fj(b,c,d);b.La=Hj(a,d);return b}static encode(a){const b=new gb;let c=-1;a.isSchema()?c=R.encode(b,a.header()):a.isRecordBatch()?c=uj.encode(b,a.header()):a.isDictionaryBatch()&&(c=Ij.encode(b,a.header()));z(b,5);Za(b,t.V5,t.V1);y(b,2,c);Ya(b,1,a.headerType,E.NONE);ab(b,
3,BigInt(a.bodyLength));a=A(b);b.finish(a);return Ta(b)}static from(a,b=0){if(a instanceof R)return new Fj(0,t.V5,E.Schema,a);if(a instanceof uj)return new Fj(b,t.V5,E.RecordBatch,a);if(a instanceof Ij)return new Fj(b,t.V5,E.DictionaryBatch,a);throw Error(`Unrecognized Message header: ${a}`);}get type(){return this.headerType}get version(){return this.Mb}get headerType(){return this.Ib}get bodyLength(){return this.Cb}header(){return this.La()}isSchema(){return this.headerType===E.Schema}isRecordBatch(){return this.headerType===
E.RecordBatch}isDictionaryBatch(){return this.headerType===E.DictionaryBatch}constructor(a,b,c,d){this.Mb=b;this.Ib=c;this.body=new Uint8Array(0);d&&(this.La=()=>d);this.Cb=H(a)}}class uj{get L(){return this.Pa}get length(){return this.Lb}get buffers(){return this.Ia}constructor(a,b,c){this.Pa=b;this.Ia=c;this.Lb=H(a)}}
class Ij{get id(){return this.Jb}get data(){return this.Eb}get Fa(){return this.Kb}get length(){return this.data.length}get L(){return this.data.L}get buffers(){return this.data.buffers}constructor(a,b,c=!1){this.Eb=a;this.Kb=c;this.Jb=H(b)}}class Bj{constructor(a,b){this.offset=H(a);this.length=H(b)}}class zj{constructor(a,b){this.length=H(a);this.nullCount=H(b)}}
function Gj(a,b){return()=>{switch(b){case E.Schema:return R.fromJSON(a);case E.RecordBatch:return uj.fromJSON(a);case E.DictionaryBatch:return Ij.fromJSON(a)}throw Error(`Unrecognized Message type: { name: ${E[b]}, type: ${b} }`);}}
function Hj(a,b){return()=>{switch(b){case E.Schema:return R.decode(a.header(new Vb),new Map,a.version());case E.RecordBatch:return uj.decode(a.header(new rb),a.version());case E.DictionaryBatch:return Ij.decode(a.header(new sb),a.version())}throw Error(`Unrecognized Message type: { name: ${E[b]}, type: ${b} }`);}}S.encode=Jj;S.decode=Kj;
S.fromJSON=function(a,b){var c;let d,e;b&&(e=a.dictionary)?(b.has(c=e.id)?(d=(d=e.indexType)?Dj(d):new Bc,c=new ue(b.get(c),d,c,e.isOrdered)):(d=(d=e.indexType)?Dj(d):new Bc,b.set(c,b=Ej(a,yj(a,b))),c=new ue(b,d,c,e.isOrdered)),a=new S(a.name,c,a.nullable,Cj(a.metadata))):(b=Ej(a,yj(a,b)),a=new S(a.name,b,a.nullable,Cj(a.metadata)));return a||null};R.encode=Lj;R.decode=Mj;R.fromJSON=function(a,b=new Map){return new R(xj(a,b),Cj(a.metadata),b)};uj.encode=Nj;uj.decode=Oj;uj.fromJSON=tj;Ij.encode=Pj;
Ij.decode=Qj;Ij.fromJSON=function(a){return new Ij(tj(a.data),a.id,a.isDelta)};zj.encode=Rj;zj.decode=Sj;Bj.encode=Tj;Bj.decode=Uj;function Mj(a,b=new Map,c=t.V5){const d=[];for(let e,f=-1,h=-1,k=Ub(a);++f<k;)if(e=a.fields(f))d[++h]=S.decode(e,b);return new R(d,Vj(a),b,c)}
function Oj(a,b=t.V5){var c=x(a.g,a.h,10);if(null!==(c?(new mb).i(Pa(a.g,a.h+c),a.g):null))throw Error("Record batch compression not implemented");c=a.length();const d=[];for(let f,h=-1,k=-1,n=pb(a);++h<n;)if(f=a.L(h))d[++k]=zj.decode(f);const e=[];for(let f,h=-1,k=-1,n=qb(a);++h<n;)if(f=a.buffers(h))b<t.V4&&(f.h+=8*(h+1)),e[++k]=Bj.decode(f);return new uj(c,d,e)}function Qj(a,b=t.V5){return new Ij(uj.decode(a.data(),b),a.id(),a.Fa())}function Uj(a){return new Bj(a.offset(),a.length())}
function Sj(a){return new zj(a.length(),a.nullCount())}function Wj(a,b){const c=[];for(let d,e=-1,f=-1,h=Qb(a);++e<h;)if(d=a.children(e))c[++f]=S.decode(d,b);return c}function Kj(a,b){var c;let d,e;b&&(e=a.dictionary())?(b.has(c=H(e.id()))?(d=(d=Ab(e))?Xj(d):new Bc,c=new ue(b.get(c),d,c,e.isOrdered())):(d=(d=Ab(e))?Xj(d):new Bc,b.set(c,b=Yj(a,Wj(a,b))),c=new ue(b,d,c,e.isOrdered())),a=new S(a.name(),c,a.nullable(),Vj(a))):(b=Yj(a,Wj(a,b)),a=new S(a.name(),b,a.nullable(),Vj(a)));return a||null}
function Vj(a){const b=new Map;if(a)for(let c,d,e=-1,f=Math.trunc(a.Da());++e<f;)(c=a.Ca(e))&&null!=(d=c.key())&&b.set(d,c.value());return b}function Xj(a){return new vc(a.isSigned(),a.bitWidth())}
function Yj(a,b){var c=(c=x(a.g,a.h,8))?a.g.l[a.h+c]:C.NONE;switch(c){case C.NONE:return new uc;case C.Null:return new uc;case C.Binary:return new Oc;case C.LargeBinary:return new Sc;case C.Utf8:return new Wc;case C.LargeUtf8:return new $c;case C.Bool:return new dd;case C.List:return new Vd((b||[])[0]);case C.Struct_:return new J(b||[])}switch(c){case C.Int:return b=a.type(new zb),new vc(b.isSigned(),b.bitWidth());case C.FloatingPoint:return b=a.type(new Ib),new Hc(b.precision());case C.Decimal:return b=
a.type(new Eb),new hd(b.scale(),b.precision(),b.bitWidth());case C.Date:return b=a.type(new Db),new ld(b.unit());case C.Time:return b=a.type(new Lb),new rd(b.unit(),b.bitWidth());case C.Timestamp:return b=a.type(new Mb),new zd(b.unit(),b.timezone());case C.Interval:return b=a.type(new Jb),new Hd(b.unit());case C.Duration:return b=a.type(new Fb),new Nd(b.unit());case C.Union:c=a.type(new Nb);a=c.mode();const d=x(c.g,c.h,6);c=d?new Int32Array(c.g.R().buffer,c.g.R().byteOffset+Qa(c.g,c.h+d),Ra(c.g,c.h+
d)):null;return new be(a,c||[],b||[]);case C.FixedSizeBinary:return b=a.type(new Gb),new he(b.byteWidth());case C.FixedSizeList:return a=a.type(new Hb),new le(a.listSize(),(b||[])[0]);case C.Map:return a=a.type(new Kb),new pe((b||[])[0],a.keysSorted())}throw Error(`Unrecognized type: "${C[c]}" (${c})`);}
function Lj(a,b){var c=b.fields.map(d=>S.encode(a,d));db(a,4,c.length,4);c=Sb(a,c);b=b.metadata&&0<b.metadata.size?Tb(a,[...b.metadata].map(([d,e])=>{d=fb(a,`${d}`);e=fb(a,`${e}`);z(a,2);y(a,0,d);y(a,1,e);return A(a)})):-1;z(a,4);y(a,1,c);Za(a,Zj?tb.Bb:tb.Vb,tb.Bb);-1!==b&&y(a,2,b);return A(a)}
function Jj(a,b){let c=-1,d=-1,e=-1;var f=b.type;let h=b.typeId;I.isDictionary(f)?(h=f.dictionary.typeId,e=sj.visit(f,a),d=sj.visit(f.dictionary,a)):d=sj.visit(f,a);f=(f.children||[]).map(n=>S.encode(a,n));f=Ob(a,f);const k=b.metadata&&0<b.metadata.size?Pb(a,[...b.metadata].map(([n,q])=>{n=fb(a,`${n}`);q=fb(a,`${q}`);z(a,2);y(a,0,n);y(a,1,q);return A(a)})):-1;b.name&&(c=fb(a,b.name));z(a,7);y(a,3,d);Ya(a,2,h,C.NONE);y(a,5,f);Ya(a,1,+!!b.nullable,0);-1!==c&&y(a,0,c);-1!==e&&y(a,4,e);-1!==k&&y(a,6,
k);return A(a)}function Nj(a,b){var c=b.L||[],d=b.buffers||[];db(a,16,c.length,8);for(const e of c.slice().reverse())zj.encode(a,e);c=eb(a);db(a,16,d.length,8);for(const e of d.slice().reverse())Bj.encode(a,e);d=eb(a);z(a,4);ab(a,0,BigInt(b.length));y(a,1,c);y(a,2,d);return A(a)}function Pj(a,b){const c=uj.encode(a,b.data);z(a,3);ab(a,0,BigInt(b.id));Ya(a,2,+b.Fa,0);y(a,1,c);return A(a)}
function Rj(a,b){var c=BigInt(b.length);b=BigInt(b.nullCount);Va(a,8,16);a.Y(BigInt(b??0));a.Y(BigInt(c??0));return a.offset()}function Tj(a,b){var c=BigInt(b.offset);b=BigInt(b.length);Va(a,8,16);a.Y(BigInt(b??0));a.Y(BigInt(c??0));return a.offset()}const ak=new ArrayBuffer(2);(new DataView(ak)).setInt16(0,256,!0);const Zj=256===(new Int16Array(ak))[0];const bk=a=>`Expected ${E[a]} Message in stream, but was null or length 0.`,ck=a=>`Header pointer of flatbuffer-encoded ${E[a]} Message is null or length 0.`,dk=(a,b)=>`Expected to read ${a} metadata bytes, but only read ${b}.`,ek=(a,b)=>`Expected to read ${a} bytes for message body, but only read ${b}.`;
class fk{constructor(a){this.source=a instanceof uh?a:new uh(a)}[Symbol.iterator](){return this}next(){let a;return(a=this.readMetadataLength()).done||-1===a.value&&(a=this.readMetadataLength()).done||(a=this.readMetadata(a.value)).done?T:a}throw(a){return this.source.throw(a)}return(a){return this.source.return(a)}readMessage(a){let b;if((b=this.next()).done)return null;if(null!=a&&b.value.headerType!==a)throw Error(bk(a));return b.value}readMessageBody(a){if(0>=a)return new Uint8Array(0);const b=
p(Uint8Array,this.source.read(a));if(b.byteLength<a)throw Error(ek(a,b.byteLength));return 0===b.byteOffset%8&&b.byteOffset+b.byteLength<=b.buffer.byteLength?b:b.slice()}readSchema(a=!1){const b=E.Schema,c=this.readMessage(b)?.header();if(a&&!c)throw Error(ck(b));return c}readMetadataLength(){var a=this.source.read(4);a=(a&&new Sa(a))?.u(0)||0;return{done:0===a,value:a}}readMetadata(a){const b=this.source.read(a);if(!b)return T;if(b.byteLength<a)throw Error(dk(a,b.byteLength));return{done:!1,value:Fj.decode(b)}}}
class gk{constructor(a,b){this.source=a instanceof wh?a:m(a)&&l(a.stat)&&"number"===typeof a.fd?new zh(a,b):new wh(a)}[Symbol.asyncIterator](){return this}async next(){let a;return(a=await this.readMetadataLength()).done||-1===a.value&&(a=await this.readMetadataLength()).done||(a=await this.readMetadata(a.value)).done?T:a}async throw(a){return await this.source.throw(a)}async return(a){return await this.source.return(a)}async readMessage(a){let b;if((b=await this.next()).done)return null;if(null!=
a&&b.value.headerType!==a)throw Error(bk(a));return b.value}async readMessageBody(a){if(0>=a)return new Uint8Array(0);const b=p(Uint8Array,await this.source.read(a));if(b.byteLength<a)throw Error(ek(a,b.byteLength));return 0===b.byteOffset%8&&b.byteOffset+b.byteLength<=b.buffer.byteLength?b:b.slice()}async readSchema(a=!1){const b=E.Schema,c=(await this.readMessage(b))?.header();if(a&&!c)throw Error(ck(b));return c}async readMetadataLength(){var a=await this.source.read(4);a=(a&&new Sa(a))?.u(0)||
0;return{done:0===a,value:a}}async readMetadata(a){const b=await this.source.read(a);if(!b)return T;if(b.byteLength<a)throw Error(dk(a,b.byteLength));return{done:!1,value:Fj.decode(b)}}}
class hk extends fk{constructor(a){super(new Uint8Array(0));this.P=!1;this.ta=[];this.W=this.ib=0;this.ja=a instanceof oh?a:new oh(a)}next(){var a=this.ja;if(!this.P)return this.P=!0,{done:!1,value:Fj.fromJSON(a.schema,E.Schema)};if(this.W<a.dictionaries.length)return a=a.dictionaries[this.W++],this.ta=a.data.columns,{done:!1,value:Fj.fromJSON(a,E.DictionaryBatch)};if(this.ib<a.batches.length)return a=a.batches[this.ib++],this.ta=a.columns,{done:!1,value:Fj.fromJSON(a,E.RecordBatch)};this.ta=[];return T}readMessageBody(){function a(b){return(b||
[]).reduce((c,d)=>[...c,...(d.VALIDITY&&[d.VALIDITY]||[]),...(d.TYPE_ID&&[d.TYPE_ID]||[]),...(d.OFFSET&&[d.OFFSET]||[]),...(d.DATA&&[d.DATA]||[]),...a(d.children)],[])}return a(this.ta)}readMessage(a){let b;if((b=this.next()).done)return null;if(null!=a&&b.value.headerType!==a)throw Error(bk(a));return b.value}readSchema(){const a=E.Schema,b=this.readMessage(a),c=b?.header();if(!b||!c)throw Error(ck(a));return c}}const ik=new Uint8Array(6);for(let a=0;6>a;a+=1)ik[a]="ARROW1".codePointAt(a);
function jk(a){for(let b=-1,c=ik.length;++b<c;)if(ik[b]!==a[0+b])return!1;return!0}const kk=ik.length,lk=kk+4,mk=2*kk+4;class nk extends rh{constructor(a){super();this.s=a}get closed(){return this.s.closed}get schema(){return this.s.schema}get autoDestroy(){return this.s.autoDestroy}get dictionaries(){return this.s.dictionaries}get numDictionaries(){return this.s.numDictionaries}get numRecordBatches(){return this.s.numRecordBatches}get footer(){return this.s.isFile()?this.s.footer:null}isSync(){return this.s.isSync()}isAsync(){return this.s.isAsync()}isFile(){return this.s.isFile()}isStream(){return this.s.isStream()}next(){return this.s.next()}throw(a){return this.s.throw(a)}return(a){return this.s.return(a)}cancel(){return this.s.cancel()}reset(a){this.s.reset(a);
this.nb=this.hb=void 0;return this}open(a){a=this.s.open(a);return m(a)&&l(a.then)?a.then(()=>this):this}readRecordBatch(a){return this.s.isFile()?this.s.readRecordBatch(a):null}[Symbol.iterator](){return this.s[Symbol.iterator]()}[Symbol.asyncIterator](){return this.s[Symbol.asyncIterator]()}toDOMStream(){return qa(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}toNodeStream(){return ra(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}static throughNode(){throw Error('"throughNode" not available in this environment');
}static throughDOM(){throw Error('"throughDOM" not available in this environment');}static from(a){return a instanceof nk?a:m(a)&&m(a.schema)?new ok(new pk(a)):m(a)&&l(a.stat)&&"number"===typeof a.fd?qk(a):m(a)&&l(a.then)?(async()=>await nk.from(await a))():m(a)&&ea(a.body)||ea(a)||fa(a)||m(a)&&l(a[Symbol.asyncIterator])?rk(new wh(a)):sk(new uh(a))}static readAll(a){return a instanceof nk?a.isSync()?tk(a):uk(a):m(a)&&m(a.schema)||ArrayBuffer.isView(a)||m(a)&&l(a[Symbol.iterator])||m(a)&&"done"in a&&
"value"in a?tk(a):uk(a)}}class ok extends nk{constructor(a){super(a);this.s=a}readAll(){return[...this]}[Symbol.iterator](){return this.s[Symbol.iterator]()}async*[Symbol.asyncIterator](){yield*this[Symbol.iterator]()}}class vk extends nk{constructor(a){super(a);this.s=a}async readAll(){const a=[];for await(const b of this)a.push(b);return a}[Symbol.iterator](){throw Error("AsyncRecordBatchStreamReader is not Iterable");}[Symbol.asyncIterator](){return this.s[Symbol.asyncIterator]()}}
class wk extends ok{constructor(a){super(a);this.s=a}}class xk extends vk{constructor(a){super(a);this.s=a}}function yk(a,b,c){c=a.Oa(b,c,a.schema.fields);b=O({type:new J(a.schema.fields),length:b.length,children:c});return new W(a.schema,b)}function zk(a,b,c){var d=b.id;const e=b.Fa,f=a.schema,h=a.dictionaries.get(d);d=f.dictionaries.get(d);a=a.Oa(b.data,c,[d]);return(h&&e?h.concat(new L(a)):new L(a)).memoize()}
class Ak{get numDictionaries(){return this.W}get numRecordBatches(){return this.O}constructor(a=new Map){this.closed=!1;this.autoDestroy=!0;this.O=this.W=0;this.dictionaries=a}isSync(){return!1}isAsync(){return!1}isFile(){return!1}isStream(){return!1}reset(a){this.O=this.W=0;this.schema=a;this.dictionaries=new Map;return this}Oa(a,b,c){return(new Jh(b,a.L,a.buffers,this.dictionaries,this.schema.Ga)).visitMany(c)}}
class Bk extends Ak{constructor(a){super();this.v=m(a)&&m(a.schema)?new hk(this.I=a):new fk(this.I=a)}isSync(){return!0}isStream(){return!0}[Symbol.iterator](){return this}cancel(){!this.closed&&(this.closed=!0)&&(this.reset().v.return(),this.dictionaries=this.v=null)}open(a){this.closed||(this.autoDestroy=Ck(this,a),this.schema||(this.schema=this.v.readSchema())||this.cancel());return this}throw(a){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset().v.throw(a):T}return(a){return!this.closed&&
this.autoDestroy&&(this.closed=!0)?this.reset().v.return(a):T}next(){if(this.closed)return T;for(var a,b=this.v;a=this.la();)if(a.isSchema())this.reset(a.header());else{if(a.isRecordBatch()){this.O++;var c=a.header();b=b.readMessageBody(a.bodyLength);return{done:!1,value:yk(this,c,b)}}a.isDictionaryBatch()&&(this.W++,c=a.header(),a=b.readMessageBody(a.bodyLength),a=zk(this,c,a),this.dictionaries.set(c.id,a))}return this.schema&&0===this.O?(this.O++,{done:!1,value:new pj(this.schema)}):this.return()}la(){return this.v.readMessage(void 0)}}
class Dk extends Ak{constructor(a,b){super(b);this.v=new gk(this.I=a)}isAsync(){return!0}isStream(){return!0}[Symbol.asyncIterator](){return this}async cancel(){!this.closed&&(this.closed=!0)&&(await this.reset().v.return(),this.dictionaries=this.v=null)}async open(a){this.closed||(this.autoDestroy=Ck(this,a),this.schema||(this.schema=await this.v.readSchema())||await this.cancel());return this}async throw(a){return!this.closed&&this.autoDestroy&&(this.closed=!0)?await this.reset().v.throw(a):T}async return(a){return!this.closed&&
this.autoDestroy&&(this.closed=!0)?await this.reset().v.return(a):T}async next(){if(this.closed)return T;for(var a,b=this.v;a=await this.la();)if(a.isSchema())await this.reset(a.header());else{if(a.isRecordBatch()){this.O++;var c=a.header();b=await b.readMessageBody(a.bodyLength);return{done:!1,value:yk(this,c,b)}}a.isDictionaryBatch()&&(this.W++,c=a.header(),a=await b.readMessageBody(a.bodyLength),a=zk(this,c,a),this.dictionaries.set(c.id,a))}return this.schema&&0===this.O?(this.O++,{done:!1,value:new pj(this.schema)}):
await this.return()}async la(){return await this.v.readMessage(void 0)}}
class Ek extends Bk{get footer(){return this.m}get numDictionaries(){return this.m?this.m.numDictionaries:0}get numRecordBatches(){return this.m?this.m.numRecordBatches:0}constructor(a){super(a instanceof yh?a:new yh(a))}isSync(){return!0}isFile(){return!0}open(a){if(!this.closed&&!this.m){this.schema=(this.m=this.Sa()).schema;for(const b of kh(this.m))b&&this.Ra(this.W++)}return super.open(a)}readRecordBatch(a){if(this.closed)return null;this.m||this.open();if((a=this.m?.ca(a))&&this.I.seek(a.offset)){var b=
this.v.readMessage(E.RecordBatch);if(b?.isRecordBatch())return a=b.header(),b=this.v.readMessageBody(b.bodyLength),yk(this,a,b)}return null}Ra(a){if((a=this.m?.Ea(a))&&this.I.seek(a.offset)){var b=this.v.readMessage(E.DictionaryBatch);b?.isDictionaryBatch()&&(a=b.header(),b=this.v.readMessageBody(b.bodyLength),b=zk(this,a,b),this.dictionaries.set(a.id,b))}}Sa(){var a=this.I;const b=a.size-lk,c=a.u(b);a=a.da(b-c,c);return lh.decode(a)}la(){this.m||this.open();if(this.m&&this.O<this.numRecordBatches){const a=
this.m?.ca(this.O);if(a&&this.I.seek(a.offset))return this.v.readMessage(void 0)}return null}}
class Fk extends Dk{get footer(){return this.m}get numDictionaries(){return this.m?this.m.numDictionaries:0}get numRecordBatches(){return this.m?this.m.numRecordBatches:0}constructor(a){var b=[];const c="number"!==typeof b[0]?b.shift():void 0;b=b[0]instanceof Map?b.shift():void 0;super(a instanceof zh?a:new zh(a,c),b)}isFile(){return!0}isAsync(){return!0}async open(a){if(!this.closed&&!this.m){this.schema=(this.m=await this.Sa()).schema;for(const b of kh(this.m))b&&await this.Ra(this.W++)}return await super.open(a)}async readRecordBatch(a){if(this.closed)return null;
this.m||await this.open();if((a=this.m?.ca(a))&&await this.I.seek(a.offset)){var b=await this.v.readMessage(E.RecordBatch);if(b?.isRecordBatch())return a=b.header(),b=await this.v.readMessageBody(b.bodyLength),yk(this,a,b)}return null}async Ra(a){if((a=this.m?.Ea(a))&&await this.I.seek(a.offset)){var b=await this.v.readMessage(E.DictionaryBatch);b?.isDictionaryBatch()&&(a=b.header(),b=await this.v.readMessageBody(b.bodyLength),b=zk(this,a,b),this.dictionaries.set(a.id,b))}}async Sa(){var a=this.I;
a.G&&await a.G;const b=a.size-lk,c=await a.u(b);a=await a.da(b-c,c);return lh.decode(a)}async la(){this.m||await this.open();if(this.m&&this.O<this.numRecordBatches){const a=this.m.ca(this.O);if(a&&await this.I.seek(a.offset))return await this.v.readMessage(void 0)}return null}}class pk extends Bk{Oa(a,b,c){return(new Kh(b,a.L,a.buffers,this.dictionaries,this.schema.Ga)).visitMany(c)}}function Ck(a,b){return b&&"boolean"===typeof b.autoDestroy?b.autoDestroy:a.autoDestroy}
function*tk(a){a=nk.from(a);try{if(!a.open({autoDestroy:!1}).closed){do yield a;while(!a.reset().open().closed)}}finally{a.cancel()}}async function*uk(a){a=await nk.from(a);try{if(!(await a.open({autoDestroy:!1})).closed){do yield a;while(!(await a.reset().open()).closed)}}finally{await a.cancel()}}function sk(a){const b=a.peek(kk+7&-8);return b&&4<=b.byteLength?jk(b)?new wk(new Ek(a.read())):new ok(new Bk(a)):new ok(new Bk(function*(){}()))}
async function rk(a){const b=await a.peek(kk+7&-8);return b&&4<=b.byteLength?jk(b)?new wk(new Ek(await a.read())):new vk(new Dk(a)):new vk(new Dk(async function*(){}()))}async function qk(a){const {size:b}=await a.stat();a=new zh(a,b);return b>=mk&&jk(await a.da(0,kk+7&-8))?new xk(new Fk(a)):new vk(new Dk(a))};class Gk extends ze{static oa(...a){const b=d=>d.flatMap(e=>Array.isArray(e)?b(e):e instanceof W?e.data.children:e.data),c=new Gk;c.visitMany(b(a));return c}constructor(){super();this.ua=0;this.Pa=[];this.Ia=[];this.Db=[]}visit(a){if(a instanceof L)return this.visitMany(a.data),this;const b=a.type;if(!I.isDictionary(b)){const c=a.length;if(2147483647<c)throw new RangeError("Cannot write arrays larger than 2^31 - 1 in length");if(I.isUnion(b))this.L.push(new zj(c,0));else{const d=a.nullCount;I.isNull(b)||
Hk.call(this,0>=d?new Uint8Array(0):rg(a.offset,c,a.nullBitmap));this.L.push(new zj(c,d))}}return super.visit(a)}visitNull(){return this}visitDictionary(a){return this.visit(a.clone(a.type.indices))}get L(){return this.Pa}get buffers(){return this.Ia}get byteLength(){return this.ua}get Wa(){return this.Db}}function Hk(a){const b=a.byteLength+7&-8;this.buffers.push(a);this.Wa.push(new Bj(this.ua,b));this.ua+=b;return this}function Ik(a){return Hk.call(this,a.values.subarray(0,a.length*a.stride))}
function Jk(a){const b=a.length,c=a.values;a=a.valueOffsets;const d=H(a[0]);var e=H(a[b]);e=Math.min(e-d,c.byteLength-d);Hk.call(this,oa(-d,b+1,a));Hk.call(this,c.subarray(d,d+e));return this}function Kk(a){const b=a.length,c=a.valueOffsets;if(c){const {[0]:d,[b]:e}=c;Hk.call(this,oa(-d,b+1,c));return this.visit(a.children[0].slice(d,e-d))}return this.visit(a.children[0])}function Lk(a){return this.visitMany(a.type.children.map((b,c)=>a.children[c]).filter(Boolean))[0]}g=Gk.prototype;
g.visitBool=function(a){let b;return a.nullCount>=a.length?Hk.call(this,new Uint8Array(0)):(b=a.values)instanceof Uint8Array?Hk.call(this,rg(a.offset,a.length,b)):Hk.call(this,sg(a.values))};g.visitInt=Ik;g.visitFloat=Ik;g.visitUtf8=Jk;g.visitLargeUtf8=Jk;g.visitBinary=Jk;g.visitLargeBinary=Jk;g.visitFixedSizeBinary=Ik;g.visitDate=Ik;g.visitTimestamp=Ik;g.visitTime=Ik;g.visitDecimal=Ik;g.visitList=Kk;g.visitStruct=Lk;
g.visitUnion=function(a){const b=a.type,c=a.length,d=a.typeIds,e=a.valueOffsets;Hk.call(this,d);if(b.mode===u.Sparse)return Lk.call(this,a);if(b.mode===u.Dense){if(0>=a.offset)return Hk.call(this,e),Lk.call(this,a);const f=new Int32Array(c),h=Object.create(null),k=Object.create(null);for(let n,q,B=-1;++B<c;)void 0!==(n=d[B])&&(void 0===(q=h[n])&&(q=h[n]=e[B]),f[B]=e[B]-q,k[n]=(k[n]??0)+1);Hk.call(this,f);this.visitMany(a.children.map((n,q)=>{q=b.typeIds[q];return n.slice(h[q],Math.min(c,k[q]))}))}return this};
g.visitInterval=Ik;g.visitDuration=Ik;g.visitFixedSizeList=Kk;g.visitMap=Kk;class Mk extends ze{visit(a){return null==a?void 0:super.visit(a)}visitNull({typeId:a}){return{name:C[a].toLowerCase()}}visitInt({typeId:a,bitWidth:b,isSigned:c}){return{name:C[a].toLowerCase(),bitWidth:b,isSigned:c}}visitFloat({typeId:a,precision:b}){return{name:C[a].toLowerCase(),precision:v[b]}}visitBinary({typeId:a}){return{name:C[a].toLowerCase()}}visitLargeBinary({typeId:a}){return{name:C[a].toLowerCase()}}visitBool({typeId:a}){return{name:C[a].toLowerCase()}}visitUtf8({typeId:a}){return{name:C[a].toLowerCase()}}visitLargeUtf8({typeId:a}){return{name:C[a].toLowerCase()}}visitDecimal({typeId:a,
scale:b,precision:c,bitWidth:d}){return{name:C[a].toLowerCase(),scale:b,precision:c,bitWidth:d}}visitDate({typeId:a,unit:b}){return{name:C[a].toLowerCase(),unit:Ca[b]}}visitTime({typeId:a,unit:b,bitWidth:c}){return{name:C[a].toLowerCase(),unit:w[b],bitWidth:c}}visitTimestamp({typeId:a,timezone:b,unit:c}){return{name:C[a].toLowerCase(),unit:w[c],timezone:b}}visitInterval({typeId:a,unit:b}){return{name:C[a].toLowerCase(),unit:Fa[b]}}visitDuration({typeId:a,unit:b}){return{name:C[a].toLocaleLowerCase(),
unit:w[b]}}visitList({typeId:a}){return{name:C[a].toLowerCase()}}visitStruct({typeId:a}){return{name:C[a].toLowerCase()}}visitUnion({typeId:a,mode:b,typeIds:c}){return{name:C[a].toLowerCase(),mode:u[b].toUpperCase(),typeIds:[...c]}}visitDictionary(a){return this.visit(a.dictionary)}visitFixedSizeBinary({typeId:a,byteWidth:b}){return{name:C[a].toLowerCase(),byteWidth:b}}visitFixedSizeList({typeId:a,listSize:b}){return{name:C[a].toLowerCase(),listSize:b}}visitMap({typeId:a,keysSorted:b}){return{name:C[a].toLowerCase(),
keysSorted:b}}};class Nk extends ze{static oa(...a){const b=new Nk;return a.map(({schema:c,data:d})=>b.visitMany(c.fields,d.children))}visit({name:a},b){const c=b.length,d=b.offset,e=b.nullCount,f=b.nullBitmap,h=I.isDictionary(b.type)?b.type.indices:b.type,k=Object.assign([],b.buffers,{[ac.VALIDITY]:void 0});return{name:a,count:c,VALIDITY:I.isNull(h)||I.isUnion(h)?void 0:0>=e?Array.from({length:c},()=>1):[...(new tg(f,d,c,null,qg))],...super.visit(b.clone(h,d,c,0,k))}}visitNull(){return{}}visitBool({values:a,offset:b,
length:c}){return{DATA:[...(new tg(a,b,c,null,pg))]}}visitInt(a){return{DATA:64>a.type.bitWidth?[...a.values]:[...Ok(a.values,2)]}}visitFloat(a){return{DATA:[...a.values]}}visitUtf8(a){return{DATA:[...(new L([a]))],OFFSET:[...a.valueOffsets]}}visitLargeUtf8(a){return{DATA:[...(new L([a]))],OFFSET:[...Ok(a.valueOffsets,2)]}}visitBinary(a){return{DATA:[...Pk(new L([a]))],OFFSET:[...a.valueOffsets]}}visitLargeBinary(a){return{DATA:[...Pk(new L([a]))],OFFSET:[...Ok(a.valueOffsets,2)]}}visitFixedSizeBinary(a){return{DATA:[...Pk(new L([a]))]}}visitDate(a){return{DATA:a.type.unit===
Ca.DAY?[...a.values]:[...Ok(a.values,2)]}}visitTimestamp(a){return{DATA:[...Ok(a.values,2)]}}visitTime(a){return{DATA:a.type.unit<w.MICROSECOND?[...a.values]:[...Ok(a.values,2)]}}visitDecimal(a){return{DATA:[...Ok(a.values,4)]}}visitList(a){return{OFFSET:[...a.valueOffsets],children:this.visitMany(a.type.children,a.children)}}visitStruct(a){return{children:this.visitMany(a.type.children,a.children)}}visitUnion(a){return{TYPE_ID:[...a.typeIds],OFFSET:a.type.mode===u.Dense?[...a.valueOffsets]:void 0,
children:this.visitMany(a.type.children,a.children)}}visitInterval(a){return{DATA:[...a.values]}}visitDuration(a){return{DATA:[...Ok(a.values,2)]}}visitFixedSizeList(a){return{children:this.visitMany(a.type.children,a.children)}}visitMap(a){return{OFFSET:[...a.valueOffsets],children:this.visitMany(a.type.children,a.children)}}}function*Pk(a){for(const b of a)yield b.reduce((c,d)=>`${c}${("0"+(d&255).toString(16)).slice(-2)}`,"").toUpperCase()}
function*Ok(a,b){a=new Uint32Array(a.buffer);for(let c=-1,d=a.length/b;++c<d;)yield`${pc.new(a.subarray((c+0)*b,(c+1)*b),!1)}`};function Y(a,b){a.ma&&(b=p(Uint8Array,b))&&0<b.byteLength&&(a.H.write(b),a.ka+=b.byteLength);return a}function Qk(a,b){return 0<b?Y(a,new Uint8Array(b)):a}function Rk(a,b){let c,d,e;for(let f=-1,h=b.length;++f<h;)(c=b[f])&&0<(d=c.byteLength)&&(Y(a,c),0<(e=(d+7&-8)-d)&&Qk(a,e));return a}
class Sk extends rh{static throughNode(){throw Error('"throughNode" not available in this environment');}static throughDOM(){throw Error('"throughDOM" not available in this environment');}constructor(a){super();this.ka=0;this.ma=!1;this.H=new th;this.P=null;this.ia=[];this.wa=[];this.ya=new Map;this.Ma=new Map;m(a)||(a={autoDestroy:!0,Ab:!1});this.sa="boolean"===typeof a.autoDestroy?a.autoDestroy:!0;this.Ta="boolean"===typeof a.Ab?a.Ab:!1}toString(a=!1){return this.H.toString(a)}toUint8Array(a=!1){return this.H.toUint8Array(a)}writeAll(a){return m(a)&&
l(a.then)?a.then(b=>this.writeAll(b)):m(a)&&l(a[Symbol.asyncIterator])?Tk(this,a):Uk(this,a)}get closed(){return this.H.closed}[Symbol.asyncIterator](){return this.H[Symbol.asyncIterator]()}toDOMStream(a){return this.H.toDOMStream(a)}toNodeStream(a){return this.H.toNodeStream(a)}close(){return this.reset().H.close()}abort(a){return this.reset().H.abort(a)}finish(){this.sa?this.close():this.reset(this.H,this.P);return this}reset(a=this.H,b=null){a===this.H||a instanceof th?this.H=a:(this.H=new th,
a&&m(a)&&l(a.abort)&&l(a.getWriter)&&!da(a)?this.toDOMStream({type:"bytes"}).pipeTo(a):a&&m(a)&&l(a.end)&&l(a.write)&&"boolean"===typeof a.writable&&!da(a)&&qh(this.toNodeStream({Yb:!1}),a));this.ma&&this.P&&this.Aa(this.P);this.ma=!1;this.ia=[];this.wa=[];this.ya=new Map;this.Ma=new Map;b&&bj(b,this.P)||(null==b?(this.ka=0,this.P=null):(this.ma=!0,this.P=b,this.Ua(b)));return this}write(a){let b=null;if(this.H){if(null==a||a instanceof X&&!(b=a.schema)||a instanceof W&&!(b=a.schema))return this.finish()&&
void 0}else throw Error("RecordBatchWriter is closed");if(b&&!bj(b,this.P)){if(this.ma&&this.sa)return this.close();this.reset(this.H,b)}a instanceof W?a instanceof pj||this.pb(a):a instanceof X?this.writeAll(a.batches):m(a)&&l(a[Symbol.iterator])&&this.writeAll(a)}Ba(a){const b=Fj.encode(a),c=b.byteLength,d=this.Ta?4:8,e=c+d+7&-8,f=e-c-d;a.headerType===E.RecordBatch?this.wa.push(new nh(e,a.bodyLength,this.ka)):a.headerType===E.DictionaryBatch&&this.ia.push(new nh(e,a.bodyLength,this.ka));this.Ta||
Y(this,Int32Array.of(-1));Y(this,Int32Array.of(e-d));0<c&&Y(this,b);return Qk(this,f)}Ua(a){this.Ba(Fj.from(a))}Aa(){return this.Ta?Y(this,Int32Array.of(0)):Y(this,Int32Array.of(-1,0))}pb(a){const {byteLength:b,L:c,Wa:d,buffers:e}=Gk.oa(a);var f=new uj(a.numRows,c,d);f=Fj.from(f,b);Rk(this.za(a).Ba(f),e)}na(a,b,c=!1){const {byteLength:d,L:e,Wa:f,buffers:h}=Gk.oa(new L([a]));a=new uj(a.length,e,f);b=new Ij(a,b,c);b=Fj.from(b,d);return Rk(this.Ba(b),h)}za(a){for(const [b,c]of a.dictionaries){a=c?.data??
[];const d=this.ya.get(b),e=this.Ma.get(b)??0;if(!d||d.data[0]!==a[0])for(const [f,h]of a.entries())this.na(h,b,0<f);else if(e<a.length)for(const f of a.slice(e))this.na(f,b,!0);this.ya.set(b,c);this.Ma.set(b,a.length)}return this}}class Vk extends Sk{static writeAll(a,b){const c=new Vk(b);return m(a)&&l(a.then)?a.then(d=>c.writeAll(d)):m(a)&&l(a[Symbol.asyncIterator])?Tk(c,a):Uk(c,a)}}
class Wk extends Sk{static writeAll(a){const b=new Wk;return m(a)&&l(a.then)?a.then(c=>b.writeAll(c)):m(a)&&l(a[Symbol.asyncIterator])?Tk(b,a):Uk(b,a)}constructor(){super();this.sa=!0}Ua(){Qk(Y(this,ik),2)}na(a,b,c=!1){if(!c&&this.ya.has(b))throw Error("The Arrow File format does not support replacement dictionaries. ");return super.na(a,b,c)}Aa(a){const b=lh.encode(new lh(a,t.V5,this.wa,this.ia));return Y(Y(Y(super.Aa(a),b),Int32Array.of(b.byteLength)),ik)}}
class Xk extends Sk{static writeAll(a){return(new Xk).writeAll(a)}constructor(){super();this.sa=!0;this.Z=[];this.xa=[]}Ba(){return this}Aa(){return this}Ua(a){Y(this,`{\n  "schema": ${JSON.stringify({fields:a.fields.map(b=>Yk(b))},null,2)}`)}za(a){0<a.dictionaries.size&&this.xa.push(a);return this}na(a,b,c=!1){Y(this,0===this.ia.length?"    ":",\n    ");Y(this,Zk(a,b,c));this.ia.push(new nh(0,0,0));return this}pb(a){this.za(a);this.Z.push(a)}close(){if(0<this.xa.length){Y(this,',\n  "dictionaries": [\n');
for(var a of this.xa)super.za(a);Y(this,"\n  ]")}if(0<this.Z.length){for(let b=-1,c=this.Z.length;++b<c;){Y(this,0===b?',\n  "batches": [\n    ':",\n    ");a=this.Z[b];const [d]=Nk.oa(a);a=JSON.stringify({count:a.numRows,columns:d},null,2);Y(this,a);this.wa.push(new nh(0,0,0))}Y(this,"\n  ]")}this.P&&Y(this,"\n}");this.xa=[];this.Z=[];return super.close()}}function Uk(a,b){let c=b;b instanceof X&&(c=b.batches,a.reset(void 0,b.schema));for(const d of c)a.write(d);return a.finish()}
async function Tk(a,b){for await(const c of b)a.write(c);return a.finish()}function Yk({name:a,type:b,nullable:c}){const d=new Mk;return{name:a,nullable:c,type:d.visit(b),children:(b.children||[]).map(e=>Yk(e)),dictionary:I.isDictionary(b)?{id:b.id,isOrdered:b.isOrdered,indexType:d.visit(b.indices)}:void 0}}function Zk(a,b,c=!1){const [d]=Nk.oa(new W({[b]:a}));return JSON.stringify({id:b,isDelta:c,data:{count:a.length,columns:d}},null,2)};function $k(a,b){function c(f,h){let k,n,q=f.desiredSize||null;for(;!(n=h.next(e?q:null)).done;)if(ArrayBuffer.isView(n.value)&&(k=p(Uint8Array,n.value))&&(null!=q&&e&&(q=q-k.byteLength+1),n.value=k),f.enqueue(n.value),null!=q&&0>=--q)return;f.close()}let d=null;const e="bytes"===b?.type||!1;return new ReadableStream({...b,start(f){c(f,d||=a[Symbol.iterator]())},pull(f){d?c(f,d):f.close()},cancel(){d?.return&&d.return();d=null}},{highWaterMark:e?b?.highWaterMark||2**24:void 0,...b})}
function al(a,b){async function c(f,h){let k,n,q=f.desiredSize||null;for(;!(n=await h.next(e?q:null)).done;)if(ArrayBuffer.isView(n.value)&&(k=p(Uint8Array,n.value))&&(null!=q&&e&&(q=q-k.byteLength+1),n.value=k),f.enqueue(n.value),null!=q&&0>=--q)return;f.close()}let d=null;const e="bytes"===b?.type||!1;return new ReadableStream({...b,async start(f){await c(f,d||=a[Symbol.asyncIterator]())},async pull(f){d?await c(f,d):f.close()},async cancel(){d?.return&&await d.return();d=null}},{highWaterMark:e?
b?.highWaterMark||2**24:void 0,...b})};function bl(a,b,c){null!=c&&(a.ga>=c.desiredSize&&++a.Qa&&cl(a,c,b.toVector()),b.finished&&((0<b.length||0===a.Qa)&&++a.Qa&&cl(a,c,b.toVector()),!a.kb&&(a.kb=!0)&&cl(a,c,null)))}function cl(a,b,c){a.ga=0;a.ha=null;null==c?b.close():b.enqueue(c)}
class dl{constructor(a){this.Qa=0;this.kb=!1;this.ga=0;const {readableStrategy:b,writableStrategy:c,queueingStrategy:d="count",...e}=a;this.ha=null;this.aa=Uh(e);this.Hb="bytes"!==d?el:fl;({highWaterMark:a="bytes"===d?16384:1E3}={...b});const {highWaterMark:f="bytes"===d?16384:1E3}={...c};this.readable=new ReadableStream({cancel:()=>{this.aa.clear()},pull:h=>{bl(this,this.aa,this.ha=h)},start:h=>{bl(this,this.aa,this.ha=h)}},{highWaterMark:a,size:"bytes"!==d?el:fl});this.writable=new WritableStream({abort:()=>
{this.aa.clear()},write:()=>{bl(this,this.aa,this.ha)},close:()=>{bl(this,this.aa.finish(),this.ha)}},{highWaterMark:f,size:h=>{const k=this.ga;this.ga=this.Hb(this.aa.append(h));return this.ga-k}})}}const el=a=>a?.length??0,fl=a=>a?.byteLength??0;function gl(a,b){async function c(){return await (await nk.from(e)).open(b)}async function d(k,n){let q=k.desiredSize,B;for(;!(B=await n.next()).done;)if(k.enqueue(B.value),null!=q&&0>=--q)return;k.close()}const e=new th;let f=null;const h=new ReadableStream({async cancel(){await e.close()},async start(k){await d(k,f||=await c())},async pull(k){f?await d(k,f):k.close()}});return{writable:new WritableStream(e,{highWaterMark:16384,...a}),readable:h}};function hl(a,b){async function c(f){let h,k=f.desiredSize;for(;h=await e.read(k||null);)if(f.enqueue(h),null!=k&&0>=(k-=h.byteLength))return;f.close()}const d=new this(a),e=new wh(d);b=new ReadableStream({async cancel(){await e.cancel()},async pull(f){await c(f)},async start(f){await c(f)}},{highWaterMark:16384,...b});return{writable:new WritableStream(d,a),readable:b}};function il(a){a=nk.from(a);return m(a)&&l(a.then)?a.then(b=>il(b)):a.isAsync()?a.readAll().then(b=>new X(b)):new X(a.readAll())};const jl={...qc,...Hh,...xg,...He,...r,...og,...dc,compareSchemas:bj,compareFields:function(a,b){return Zi.compareFields(a,b)},compareTypes:cj};qa=function(a,b){if(m(a)&&l(a[Symbol.asyncIterator]))return al(a,b);if(m(a)&&l(a[Symbol.iterator]))return $k(a,b);throw Error("toDOMStream() must be called with an Iterable or AsyncIterable");};ch.throughDOM=function(a){return new dl(a)};nk.throughDOM=gl;wk.throughDOM=gl;ok.throughDOM=gl;Sk.throughDOM=hl;Wk.throughDOM=hl;Vk.throughDOM=hl;var Z={};Z.AsyncByteQueue=th;Z.AsyncByteStream=wh;Z.AsyncMessageReader=gk;Z.AsyncRecordBatchFileReader=xk;Z.AsyncRecordBatchStreamReader=vk;Z.Binary=Oc;
Z.BinaryBuilder=Mh;Z.Bool=dd;Z.BoolBuilder=Oh;Z.BufferType=ac;Z.Builder=ch;Z.ByteStream=uh;Z.Data=N;Z.DataType=I;Z.DateBuilder=Ph;Z.DateDay=pd;Z.DateDayBuilder=Qh;Z.DateMillisecond=qd;Z.DateMillisecondBuilder=Rh;Z.DateUnit=Ca;Z.Date_=ld;Z.Decimal=hd;Z.DecimalBuilder=Sh;Z.DenseUnion=fe;Z.DenseUnionBuilder=Hi;Z.Dictionary=ue;Z.DictionaryBuilder=Th;Z.Duration=Nd;Z.DurationBuilder=di;Z.DurationMicrosecond=Td;Z.DurationMicrosecondBuilder=gi;Z.DurationMillisecond=Sd;Z.DurationMillisecondBuilder=fi;
Z.DurationNanosecond=Ud;Z.DurationNanosecondBuilder=hi;Z.DurationSecond=Rd;Z.DurationSecondBuilder=ei;Z.Field=S;Z.FixedSizeBinary=he;Z.FixedSizeBinaryBuilder=Vh;Z.FixedSizeList=le;Z.FixedSizeListBuilder=Wh;Z.Float=Hc;Z.Float16=Lc;Z.Float16Builder=Yh;Z.Float32=Mc;Z.Float32Builder=Zh;Z.Float64=Nc;Z.Float64Builder=$h;Z.FloatBuilder=Xh;Z.Int=vc;Z.Int16=Ac;Z.Int16Builder=ki;Z.Int32=Bc;Z.Int32Builder=li;Z.Int64=Cc;Z.Int64Builder=mi;Z.Int8=zc;Z.Int8Builder=ji;Z.IntBuilder=ii;Z.Interval=Hd;
Z.IntervalBuilder=ai;Z.IntervalDayTime=Ld;Z.IntervalDayTimeBuilder=bi;Z.IntervalUnit=Fa;Z.IntervalYearMonth=Md;Z.IntervalYearMonthBuilder=ci;Z.JSONMessageReader=hk;Z.LargeBinary=Sc;Z.LargeBinaryBuilder=Nh;Z.LargeUtf8=$c;Z.LargeUtf8Builder=Mi;Z.List=Vd;Z.ListBuilder=ri;Z.MapBuilder=si;Z.MapRow=Yf;Z.Map_=pe;Z.Message=Fj;Z.MessageHeader=E;Z.MessageReader=fk;Z.MetadataVersion=t;Z.Null=uc;Z.NullBuilder=ti;Z.Precision=v;Z.RecordBatch=W;Z.RecordBatchFileReader=wk;Z.RecordBatchFileWriter=Wk;
Z.RecordBatchJSONWriter=Xk;Z.RecordBatchReader=nk;Z.RecordBatchStreamReader=ok;Z.RecordBatchStreamWriter=Vk;Z.RecordBatchWriter=Sk;Z.Schema=R;Z.SparseUnion=ge;Z.SparseUnionBuilder=Gi;Z.Struct=J;Z.StructBuilder=ui;Z.StructRow=vf;Z.Table=X;Z.Time=rd;Z.TimeBuilder=Ai;Z.TimeMicrosecond=xd;Z.TimeMicrosecondBuilder=Di;Z.TimeMillisecond=wd;Z.TimeMillisecondBuilder=Ci;Z.TimeNanosecond=yd;Z.TimeNanosecondBuilder=Ei;Z.TimeSecond=vd;Z.TimeSecondBuilder=Bi;Z.TimeUnit=w;Z.Timestamp=zd;Z.TimestampBuilder=vi;
Z.TimestampMicrosecond=Fd;Z.TimestampMicrosecondBuilder=yi;Z.TimestampMillisecond=Ed;Z.TimestampMillisecondBuilder=xi;Z.TimestampNanosecond=Gd;Z.TimestampNanosecondBuilder=zi;Z.TimestampSecond=Dd;Z.TimestampSecondBuilder=wi;Z.Type=F;Z.Uint16=Ec;Z.Uint16Builder=oi;Z.Uint32=Fc;Z.Uint32Builder=pi;Z.Uint64=Gc;Z.Uint64Builder=qi;Z.Uint8=Dc;Z.Uint8Builder=ni;Z.Union=be;Z.UnionBuilder=Fi;Z.UnionMode=u;Z.Utf8=Wc;Z.Utf8Builder=Ii;Z.Vector=L;Z.Visitor=ze;
Z.builderThroughAsyncIterable=function(a){const {queueingStrategy:b="count"}=a,{highWaterMark:c="bytes"!==b?Number.POSITIVE_INFINITY:16384}=a,d="bytes"!==b?"length":"byteLength";return async function*(e){let f=0;const h=Uh(a);for await(const k of e)h.append(k)[d]>=c&&++f&&(yield h.toVector());if(0<h.finish().length||0===f)yield h.toVector()}};Z.builderThroughIterable=fj;Z.makeBuilder=Uh;Z.makeData=O;Z.makeTable=function(a){const b={};a=Object.entries(a);for(const [c,d]of a)b[c]=Ug(d);return new X(b)};
Z.makeVector=Ug;Z.tableFromArrays=function(a){const b={};a=Object.entries(a);for(const [c,d]of a)b[c]=dj(d);return new X(b)};Z.tableFromIPC=il;Z.tableFromJSON=function(a){a=dj(a);a=new W(new R(a.type.children),a.data[0]);return new X(a)};Z.tableToIPC=function(a,b="stream"){return("stream"===b?Vk:Wk).writeAll(a).toUint8Array(!0)};Z.util=jl;Z.vectorFromArray=dj;Object.assign(arguments[0],Z);}.bind(this))));

//# sourceMappingURL=Arrow.esnext.min.js.map
