{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-opengraph.ts"], "names": ["resolveImages", "resolveOpenGraph", "resolveTwitter", "Og<PERSON><PERSON><PERSON><PERSON>s", "article", "song", "playlist", "radio", "video", "basic", "resolveAndValidateImage", "item", "metadataBase", "isMetadataBaseMissing", "isStandaloneMode", "undefined", "isItemUrl", "isStringOrURL", "inputUrl", "url", "isNonVercelDeployment", "process", "env", "VERCEL", "NODE_ENV", "validateResolvedImageUrl", "resolveUrl", "images", "resolvedImages", "resolveAsArrayOrUndefined", "fallbackMetadataBase", "getSocialImageFallbackMetadataBase", "nonNullableImages", "resolvedItem", "push", "ogTypeToFields", "book", "getFieldsByOgType", "ogType", "concat", "isFullStringUrl", "warnOnce", "origin", "openGraph", "metadataContext", "titleTemplate", "resolveProps", "target", "og", "type", "keys", "k", "key", "value", "arrayValue", "resolved", "title", "resolveTitle", "resolveAbsoluteUrlWithPathname", "TwitterBasicInfoKeys", "twitter", "card", "infoKey", "length", "players", "app"], "mappings": ";;;;;;;;;;;;;;;;IAgFgBA,aAAa;eAAbA;;IA6DHC,gBAAgB;eAAhBA;;IAoDAC,cAAc;eAAdA;;;uBAtL6B;4BAMnC;8BACsB;qBACG;qBACP;AAKzB,MAAMC,eAAe;IACnBC,SAAS;QAAC;QAAW;KAAO;IAC5BC,MAAM;QAAC;QAAU;KAAY;IAC7BC,UAAU;QAAC;QAAU;KAAY;IACjCC,OAAO;QAAC;KAAW;IACnBC,OAAO;QAAC;QAAU;QAAa;QAAW;KAAO;IACjDC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,SAASC,wBACPC,IAA2D,EAC3DC,YAA+C,EAC/CC,qBAA8B,EAC9BC,gBAAyB;IAEzB,IAAI,CAACH,MAAM,OAAOI;IAClB,MAAMC,YAAYC,IAAAA,yBAAa,EAACN;IAChC,MAAMO,WAAWF,YAAYL,OAAOA,KAAKQ,GAAG;IAC5C,IAAI,CAACD,UAAU,OAAOH;IAEtB,MAAMK,wBACJ,CAACC,QAAQC,GAAG,CAACC,MAAM,IAAIF,QAAQC,GAAG,CAACE,QAAQ,KAAK;IAClD,qEAAqE;IACrE,IAAIV,oBAAoBM,uBAAuB;QAC7CK,yBAAyBP,UAAUN,cAAcC;IACnD;IAEA,OAAOG,YACH;QACEG,KAAKO,IAAAA,sBAAU,EAACR,UAAUN;IAC5B,IACA;QACE,GAAGD,IAAI;QACP,8BAA8B;QAC9BQ,KAAKO,IAAAA,sBAAU,EAACR,UAAUN;IAC5B;AACN;AAYO,SAASZ,cACd2B,MAA+C,EAC/Cf,YAAkC,EAClCE,gBAAyB;IAIzB,MAAMc,iBAAiBC,IAAAA,gCAAyB,EAACF;IACjD,IAAI,CAACC,gBAAgB,OAAOA;IAE5B,MAAM,EAAEf,qBAAqB,EAAEiB,oBAAoB,EAAE,GACnDC,IAAAA,8CAAkC,EAACnB;IACrC,MAAMoB,oBAAoB,EAAE;IAC5B,KAAK,MAAMrB,QAAQiB,eAAgB;QACjC,MAAMK,eAAevB,wBACnBC,MACAmB,sBACAjB,uBACAC;QAEF,IAAI,CAACmB,cAAc;QAEnBD,kBAAkBE,IAAI,CAACD;IACzB;IAEA,OAAOD;AACT;AAEA,MAAMG,iBAAoD;IACxD/B,SAASD,aAAaC,OAAO;IAC7BgC,MAAMjC,aAAaC,OAAO;IAC1B,cAAcD,aAAaE,IAAI;IAC/B,eAAeF,aAAaE,IAAI;IAChC,kBAAkBF,aAAaG,QAAQ;IACvC,uBAAuBH,aAAaI,KAAK;IACzC,eAAeJ,aAAaK,KAAK;IACjC,iBAAiBL,aAAaK,KAAK;AACrC;AAEA,SAAS6B,kBAAkBC,MAAiC;IAC1D,IAAI,CAACA,UAAU,CAAEA,CAAAA,UAAUH,cAAa,GAAI,OAAOhC,aAAaM,KAAK;IACrE,OAAO0B,cAAc,CAACG,OAAO,CAACC,MAAM,CAACpC,aAAaM,KAAK;AACzD;AAEA,SAASgB,yBACPP,QAAsB,EACtBY,oBAAuD,EACvDjB,qBAA8B;IAE9B,yEAAyE;IACzE,IACE,OAAOK,aAAa,YACpB,CAACsB,IAAAA,oBAAe,EAACtB,aACjBL,uBACA;QACA4B,IAAAA,aAAQ,EACN,CAAC,8GAA8G,EAAEX,qBAAqBY,MAAM,CAAC,yFAAyF,CAAC;IAE3O;AACF;AAEO,MAAMzC,mBAGT,CAAC0C,WAAW/B,cAAcgC,iBAAiBC;IAC7C,IAAI,CAACF,WAAW,OAAO;IAEvB,SAASG,aAAaC,MAAyB,EAAEC,EAAa;QAC5D,MAAMV,SAASU,MAAM,UAAUA,KAAKA,GAAGC,IAAI,GAAGlC;QAC9C,MAAMmC,OAAOb,kBAAkBC;QAC/B,KAAK,MAAMa,KAAKD,KAAM;YACpB,MAAME,MAAMD;YACZ,IAAIC,OAAOJ,MAAMI,QAAQ,OAAO;gBAC9B,MAAMC,QAAQL,EAAE,CAACI,IAAI;gBACrB,IAAIC,OAAO;oBACT,MAAMC,aAAazB,IAAAA,gCAAyB,EAACwB;oBAE3CN,MAAc,CAACK,IAAI,GAAGE;gBAC1B;YACF;QACF;QACAP,OAAOpB,MAAM,GAAG3B,cACdgD,GAAGrB,MAAM,EACTf,cACAgC,gBAAgB9B,gBAAgB;IAEpC;IAEA,MAAMyC,WAAW;QACf,GAAGZ,SAAS;QACZa,OAAOC,IAAAA,0BAAY,EAACd,UAAUa,KAAK,EAAEX;IACvC;IACAC,aAAaS,UAAUZ;IAEvBY,SAASpC,GAAG,GAAGwB,UAAUxB,GAAG,GACxBuC,IAAAA,0CAA8B,EAC5Bf,UAAUxB,GAAG,EACbP,cACAgC,mBAEF;IAEJ,OAAOW;AACT;AAEA,MAAMI,uBAAuB;IAC3B;IACA;IACA;IACA;IACA;CACD;AAEM,MAAMzD,iBAGT,CAAC0D,SAAShD,cAAcgC,iBAAiBC;QAiB3BU;IAhBhB,IAAI,CAACK,SAAS,OAAO;IACrB,IAAIC,OAAO,UAAUD,UAAUA,QAAQC,IAAI,GAAG9C;IAC9C,MAAMwC,WAAW;QACf,GAAGK,OAAO;QACVJ,OAAOC,IAAAA,0BAAY,EAACG,QAAQJ,KAAK,EAAEX;IACrC;IACA,KAAK,MAAMiB,WAAWH,qBAAsB;QAC1CJ,QAAQ,CAACO,QAAQ,GAAGF,OAAO,CAACE,QAAQ,IAAI;IAC1C;IAEAP,SAAS5B,MAAM,GAAG3B,cAChB4D,QAAQjC,MAAM,EACdf,cACAgC,gBAAgB9B,gBAAgB;IAGlC+C,OAAOA,QAASN,CAAAA,EAAAA,mBAAAA,SAAS5B,MAAM,qBAAf4B,iBAAiBQ,MAAM,IAAG,wBAAwB,SAAQ;IAC1ER,SAASM,IAAI,GAAGA;IAEhB,IAAI,UAAUN,UAAU;QACtB,OAAQA,SAASM,IAAI;YACnB,KAAK;gBAAU;oBACbN,SAASS,OAAO,GAAGnC,IAAAA,gCAAyB,EAAC0B,SAASS,OAAO,KAAK,EAAE;oBACpE;gBACF;YACA,KAAK;gBAAO;oBACVT,SAASU,GAAG,GAAGV,SAASU,GAAG,IAAI,CAAC;oBAChC;gBACF;YACA;gBACE;QACJ;IACF;IAEA,OAAOV;AACT"}