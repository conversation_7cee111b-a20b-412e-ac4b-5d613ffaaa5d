declare const _default: {
    readonly IS_READ_ONLY: false;
    /**
     * Decreases the value of a time series by a given amount
     * @param args - Arguments passed to the parseIncrByArguments function
     */
    readonly parseCommand: (this: void, parser: import("@redis/client").CommandParser, key: import("@redis/client/dist/lib/RESP/types").RedisArgument, value: number, options?: import("./INCRBY").TsIncrByOptions | undefined) => void;
    readonly transformReply: () => import("@redis/client/dist/lib/RESP/types").NumberReply<number>;
};
export default _default;
//# sourceMappingURL=DECRBY.d.ts.map