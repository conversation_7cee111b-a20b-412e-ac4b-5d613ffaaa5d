{"Commands:": "命令：", "Options:": "选项：", "Examples:": "示例：", "boolean": "布尔", "count": "计数", "string": "字符串", "number": "数字", "array": "数组", "required": "必需", "default": "默认值", "default:": "默认值:", "choices:": "可选值:", "generated-value": "生成的值", "Not enough non-option arguments: got %s, need at least %s": {"one": "缺少 non-option 参数：传入了 %s 个, 至少需要 %s 个", "other": "缺少 non-option 参数：传入了 %s 个, 至少需要 %s 个"}, "Too many non-option arguments: got %s, maximum of %s": {"one": "non-option 参数过多：传入了 %s 个, 最大允许 %s 个", "other": "non-option 参数过多：传入了 %s 个, 最大允许 %s 个"}, "Missing argument value: %s": {"one": "没有给此选项指定值：%s", "other": "没有给这些选项指定值：%s"}, "Missing required argument: %s": {"one": "缺少必须的选项：%s", "other": "缺少这些必须的选项：%s"}, "Unknown argument: %s": {"one": "无法识别的选项：%s", "other": "无法识别这些选项：%s"}, "Invalid values:": "无效的选项值：", "Argument: %s, Given: %s, Choices: %s": "选项名称: %s, 传入的值: %s, 可选的值：%s", "Argument check failed: %s": "选项值验证失败：%s", "Implications failed:": "缺少依赖的选项：", "Not enough arguments following: %s": "没有提供足够的值给此选项：%s", "Invalid JSON config file: %s": "无效的 JSON 配置文件：%s", "Path to JSON config file": "JSON 配置文件的路径", "Show help": "显示帮助信息", "Show version number": "显示版本号", "Did you mean %s?": "是指 %s?", "Arguments %s and %s are mutually exclusive": "选项 %s 和 %s 是互斥的", "Positionals:": "位置：", "command": "命令"}