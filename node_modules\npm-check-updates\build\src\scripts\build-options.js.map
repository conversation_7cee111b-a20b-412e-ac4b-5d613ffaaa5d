{"version": 3, "file": "build-options.js", "sourceRoot": "", "sources": ["../../../src/scripts/build-options.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAA4B;AAC5B,gEAAgC;AAChC,8DAA+D;AAC/D,wCAAwC;AAGxC,MAAM,aAAa,GACjB,+IAA+I,CAAA;AAEjJ,8DAA8D;AAC9D,MAAM,QAAQ,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;AAE1F,iHAAiH;AACjH,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;IAC9B,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,CAAA;IACzD,IAAI,MAAM,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;IACnD,MAAM,UAAU,GAAG,qBAAU;SAC1B,GAAG,CAAC,MAAM,CAAC,EAAE;QACZ,OAAO;UACH,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,GAC1G,MAAM,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAChC,GAAG,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;UACnF,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QACtG,CAAA;IACJ,CAAC,CAAC;SACD,IAAI,CAAC,IAAI,CAAC,CAAA;IAEb,6BAA6B;IAC7B,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,GAAG,wBAAwB,CAAC,MAAM,CAAA;IAC/F,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAA;IACvE,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC;EACzC,aAAa;;;EAGb,UAAU;;;EAGV,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAA;IAE1B,sCAAsC;IACtC,6HAA6H;IAC7H,MAAM,IAAA,iBAAS,GAAE,CAAA;IACjB,MAAM,oBAAoB,GACxB,MAAM,CAAC,OAAO,CAAC,iCAAiC,CAAC,GAAG,iCAAiC,CAAC,MAAM,CAAA;IAC9F,MAAM,kBAAkB,GAAG,MAAM,CAAC,OAAO,CAAC,+BAA+B,EAAE,oBAAoB,CAAC,CAAA;IAChG,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,oBAAoB,CAAC;EACjD,aAAa;;EAEb,qBAAU;SACT,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;SAC7B,GAAG,CACF,MAAM,CAAC,EAAE,CAAC,MAAM,MAAM,CAAC,IAAI;;EAE7B,SAAS,CAAC,IAAA,gCAAkB,EAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;CAC1D,CACE;SACA,IAAI,CAAC,IAAI,CAAC;EACX,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAA;IAElC,OAAO,MAAM,CAAA;AACf,CAAC,CAAA;AAED,8DAA8D;AAC9D,MAAM,YAAY,GAAG,CAAC,MAA0B,EAAE,EAAE;IAClD,gFAAgF;IAChF,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAA;IAE5G,yIAAyI;IACzI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAA;IAE5D,MAAM,QAAQ;IACZ,qCAAqC;IACrC,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QAC7E,CAAC,CAAC,yBAAyB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;QAC/D,CAAC,CAAC,EAAE,CAAA;IAER,2BAA2B;IAC3B,OAAO,SAAS,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB,MAAM,CAAC,IAAI,gBAAgB,CAAC,CAAC,CAAC,EAAE,GAAG,QAAQ;IAC3G,MAAM,CAAC,IAAI,MAAM,IAAI;CACxB,CAAA;AACD,CAAC,CAAA;AAED,4FAA4F;AAC5F,MAAM,kBAAkB,GAAG,CAAC,OAA6B,EAAE,EAAE;IAC3D,MAAM,MAAM,GAAG;;;;;;;;;CAShB,CAAA;IAEC,MAAM,MAAM,GAAG,KAAK,CAAA;IAEpB,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAE5D,MAAM,MAAM,GAAG,GAAG,MAAM,GAAG,eAAe,GAAG,MAAM,EAAE,CAAA;IAErD,OAAO,MAAM,CAAA;AACf,CAAC,CAAA;AAED,kDAAkD;AAClD,MAAM,4BAA4B,GAAG,KAAK,IAAqB,EAAE;AAC/D,8FAA8F;AAC9F,0CAA0C;AAC1C,IAAA,sBAAK,EAAC,wBAAwB,EAAE,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC,CAEjE;AAAA,CAAC,KAAK,IAAI,EAAE;IACX,MAAM,kBAAE,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,YAAY,EAAE,CAAC,CAAA;IACrD,MAAM,kBAAE,CAAC,SAAS,CAAC,yBAAyB,EAAE,kBAAkB,CAAC,qBAAU,CAAC,CAAC,CAAA;IAC7E,MAAM,kBAAE,CAAC,SAAS,CAAC,2BAA2B,EAAE,MAAM,4BAA4B,EAAE,CAAC,CAAA;IACrF,MAAM,IAAA,sBAAK,EAAC,UAAU,EAAE,CAAC,IAAI,EAAE,2BAA2B,CAAC,CAAC,CAAA;AAC9D,CAAC,CAAC,EAAE,CAAA"}