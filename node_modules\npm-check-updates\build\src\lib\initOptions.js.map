{"version": 3, "file": "initOptions.js", "sourceRoot": "", "sources": ["../../../src/lib/initOptions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6DAAoC;AACpC,mEAA0C;AAC1C,8DAA0D;AAC1D,4CAAsC;AACtC,2EAAiD;AAKjD,oDAA4B;AAC5B,wFAA+D;AAC/D,sDAA6B;AAC7B,8DAAqC;AACrC,kEAAyC;AAIzC,mEAAmE;AACnE,SAAS,qBAAqB,CAAC,gBAA2C;IACxE,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE;QACxC,OAAO,gBAAgB,CAAC,IAAI,EAAE,CAAA;KAC/B;SAAM,IACL,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC;QAC/B,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,gBAAgB,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,EAC1E;QACA,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;QACjG,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAA;KAClD;SAAM;QACL,OAAO,gBAAgB,CAAA;KACxB;AACH,CAAC;AAED,+EAA+E;AAC/E,KAAK,UAAU,WAAW,CAAC,UAAsB,EAAE,EAAE,GAAG,KAAwB,EAAE;;IAChF,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA;IAC9D,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,CAAA;IAEvE,yFAAyF;IACzF,IAAI,CAAC,GAAG,EAAE;QACR,oEAAoE;QACpE,MAAM,WAAW,GAAG,qBAAU,CAAC,MAAM,CACnC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACd,GAAG,GAAG;YACN,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;SACjE,CAAC,EACF,EAAE,CACH,CAAA;QAED,wDAAwD;QACxD,MAAM,cAAc,GAAY;YAC9B,YAAY,EAAE,IAAI;YAClB,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,KAAK,SAAS,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YACvF,IAAI,EAAE,EAAE;SACT,CAAA;QAED,UAAU,GAAG,EAAE,GAAG,WAAW,EAAE,GAAG,cAAc,EAAE,GAAG,UAAU,EAAE,CAAA;KAClE;IAED,iEAAiE;IACjE,MAAM,OAAO,GAAY;QACvB,GAAG,UAAU;QACb,GAAG,CAAC,UAAU,CAAC,WAAW,IAAI,OAAO,UAAU,CAAC,WAAW,KAAK,QAAQ;YACtE,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAQ,EAAE;YACzE,CAAC,CAAC,IAAI,CAAC;QACT,GAAG;KACJ,CAAA;IAED,uBAAuB;IACvB,MAAM,QAAQ,GACZ,OAAO,CAAC,MAAM,KAAI,MAAA,OAAO,CAAC,MAAM,0CAAE,QAAQ,CAAC,OAAO,CAAC,CAAA,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAA;IAEjH,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;SAC9B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;SAC3C,IAAI,CAAC,IAAA,oBAAU,EAAC,OAAO,CAAC,CAAC,CAAA;IAE5B,IAAI,CAAC,IAAI,IAAI,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QAC7E,IAAA,eAAK,EAAC,OAAO,EAAE,qBAAqB,OAAO,CAAC,YAAY,EAAE,CAAC,CAAA;KAC5D;IAED,gCAAgC;IAChC,MAAM,iBAAiB,GAAG,qBAAU,CAAC,MAAM,CACzC,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,EAAE,CACvB,CAAC,UAAU,IAAI,OAAO,CAAC,IAAqB,CAAC,CAAC;QAC9C,8DAA8D;QAC9D,CAAC,IAAI,KAAK,gBAAgB,IAAI,OAAO,CAAC,cAAc,KAAK,gBAAgB,CAAC,CAC7E,CAAA;IACD,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAChC,iBAAiB,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE;YAClD,MAAM,kBAAkB,GACtB,IAAI,KAAK,gBAAgB;gBACvB,CAAC,CAAC,yEAAyE;gBAC3E,CAAC,CAAC,KAAK,IAAI,KAAK,WAAW,EAAE,CAAA;YACjC,IAAA,eAAK,EAAC,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,EAAE,MAAM,CAAC,CAAA;QAC1D,CAAC,CAAC,CAAA;QACF,IAAA,eAAK,EAAC,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,CAAA;KAC3B;IAED,2CAA2C;IAC3C,qBAAU,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;QACvC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAM;QAC5C,MAAM,KAAK,GAAG,OAAO,CAAC,IAAqB,CAAC,CAAA;QAC5C,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;QACrD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAM;QAC/B,sCAAsC;QACtC,uEAAuE;QACvE,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;YACnD,IAAA,sBAAY,EAAC,OAAO,EAAE,2BAA2B,IAAI,IAAI,KAAK,uBAAuB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;SAC5G;IACH,CAAC,CAAC,CAAA;IAEF,uBAAuB;IACvB,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,MAAM,IAAA,gBAAM,EAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;QAC/C,IAAA,sBAAY,EAAC,OAAO,EAAE,sBAAsB,OAAO,CAAC,GAAG,EAAE,CAAC,CAAA;KAC3D;IAED,mBAAmB;IACnB,wCAAwC;IACxC,MAAM,IAAI,GAAG,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;IAChD,MAAM,MAAM,GAAG,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IACpD,MAAM,aAAa,GAAG,qBAAqB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;IAClE,MAAM,MAAM,GAAG,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IACpD,MAAM,aAAa,GAAG,qBAAqB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;IAElE,4CAA4C;IAC5C,8CAA8C;IAC9C,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC,IAAA,iBAAO,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE;QACzG,IAAA,sBAAY,EACV,OAAO,EACP,sLAAsL,CACvL,CAAA;KACF;IACD,kCAAkC;SAC7B,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI,EAAE;QAC5C,IAAA,sBAAY,EACV,OAAO,EACP,sGAAsG,CACvG,CAAA;KACF;IACD,6CAA6C;SACxC,IAAI,CAAA,MAAA,OAAO,CAAC,MAAM,0CAAE,QAAQ,CAAC,OAAO,CAAC,KAAI,OAAO,CAAC,YAAY,EAAE;QAClE,IAAA,sBAAY,EAAC,OAAO,EAAE,wDAAwD,CAAC,CAAA;KAChF;SAAM,IAAI,CAAA,MAAA,OAAO,CAAC,MAAM,0CAAE,QAAQ,CAAC,OAAO,CAAC,KAAI,OAAO,CAAC,OAAO,EAAE;QAC/D,IAAA,sBAAY,EAAC,OAAO,EAAE,mDAAmD,CAAC,CAAA;KAC3E;SAAM,IAAI,CAAA,MAAA,OAAO,CAAC,MAAM,0CAAE,QAAQ,CAAC,OAAO,CAAC,KAAI,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QACzE,IAAA,sBAAY,EAAC,OAAO,EAAE,0DAA0D,CAAC,CAAA;KAClF;IACD,wCAAwC;SACnC,IAAI,CAAA,MAAA,OAAO,CAAC,SAAS,0CAAE,MAAM,KAAI,OAAO,CAAC,UAAU,EAAE;QACxD,IAAA,sBAAY,EAAC,OAAO,EAAE,mDAAmD,CAAC,CAAA;KAC3E;IACD,qCAAqC;SAChC,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,CAAA,MAAA,OAAO,CAAC,SAAS,0CAAE,MAAM,KAAI,OAAO,CAAC,UAAU,CAAC,EAAE;QAC1E,IAAA,sBAAY,EAAC,OAAO,EAAE,6CAA6C,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;KACrG;IACD,uCAAuC;SAClC,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,CAAA,MAAA,OAAO,CAAC,SAAS,0CAAE,MAAM,KAAI,OAAO,CAAC,UAAU,CAAC,EAAE;QAC5E,IAAA,sBAAY,EAAC,OAAO,EAAE,0DAA0D,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;KAClH;IACD,yDAAyD;SACpD,IAAI,OAAO,CAAC,cAAc,KAAK,gBAAgB,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;QACzE,IAAA,sBAAY,EACV,OAAO,EACP,sHAAsH,CACvH,CAAA;KACF;SAAM,IAAI,OAAO,CAAC,YAAY,KAAK,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;QAC/D,IAAA,sBAAY,EACV,OAAO,EACP,oJAAoJ,CACrJ,CAAA;KACF;IAED,MAAM,MAAM,GAAW,OAAO,CAAC,MAAM,IAAI,QAAQ,CAAA;IAEjD,MAAM,OAAO,GAAG,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,UAAU,CAAA;IAE5D,MAAM,cAAc,GAAG,MAAM,IAAA,iCAAuB,EAAC,OAAO,CAAC,CAAA;IAE7D,MAAM,eAAe,GAAY;QAC/B,GAAG,OAAO;QACV,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7D,GAAG,CAAC,cAAc,KAAK,MAAM,IAAI,OAAO,CAAC,GAAG,KAAK,2BAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QACzG,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QACpF,MAAM,EAAE,IAAI,IAAI,MAAM;QACtB,aAAa;QACb,mDAAmD;QACnD,IAAI;QACJ,QAAQ;QACR,OAAO,EAAE,OAAO,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO;QAChE,2DAA2D;QAC3D,wHAAwH;QACxH,GAAG,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QACnG,MAAM;QACN,aAAa;QACb,MAAM;QACN,6EAA6E;QAC7E,GAAG,CAAC,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QACrF,cAAc;QACd,GAAG,CAAC,OAAO,CAAC,MAAM;YAChB,CAAC,CAAC;gBACE,0EAA0E;gBAC1E,MAAM,EAAE,MAAM,CAAC,CAAA,MAAA,0BAAe,CAAC,cAAc,IAAI,EAAE,CAAC,0CAAE,aAAa,KAAI,0BAAe,CAAC,GAAG,CAAC,aAAc,CAAC,CACxG,OAAO,CACR;aACF;YACH,CAAC,CAAC,IAAI,CAAC;QACT,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,CAAC,CAAA,MAAA,OAAO,CAAC,QAAQ,0CAAE,QAAQ,CAAC,OAAO,CAAC,EAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;KAC7F,CAAA;IACD,eAAe,CAAC,MAAM,GAAG,MAAM,IAAA,eAAM,EAAC,eAAe,CAAC,CAAA;IAEtD,0BAA0B;IAC1B,MAAM,uBAAuB,GAAY,IAAA,oBAAU,EACjD,eAA4D,EAC5D,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAChE,CAAA;IAED,gDAAgD;IAChD,mDAAmD;IACnD,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,EAAE;QACvD,IAAA,eAAK,EAAC,uBAAuB,EAAE,SAAS,cAAc,EAAE,CAAC,CAAA;KAC1D;IAED,OAAO,uBAAuB,CAAA;AAChC,CAAC;AAED,kBAAe,WAAW,CAAA"}