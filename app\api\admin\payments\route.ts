'use server'

import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    // Authenticate request - only admins can access
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      )
    }

    // Get query parameters
    const url = new URL(request.url)
    const status = url.searchParams.get('status')
    const gateway = url.searchParams.get('gateway')
    const limit = parseInt(url.searchParams.get('limit') || '50')
    const offset = parseInt(url.searchParams.get('offset') || '0')

    // Build query
    let whereClause = '1=1'
    const params: any[] = []

    if (status && status !== 'all') {
      whereClause += ' AND p.status = ?'
      params.push(status)
    }

    if (gateway && gateway !== 'all') {
      whereClause += ' AND p.gateway = ?'
      params.push(gateway)
    }

    // Get payments with user and plan information
    const [paymentsRows] = await db.query(`
      SELECT 
        p.*,
        u.username as userName,
        u.email as userEmail,
        pl.name as planName
      FROM payments p
      LEFT JOIN users u ON p.user_id = u.id
      LEFT JOIN plans pl ON p.plan_id = pl.id
      WHERE ${whereClause}
      ORDER BY p.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, limit, offset])

    // Get payment statistics
    const [statsRows] = await db.query(`
      SELECT 
        COALESCE(SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END), 0) as totalRevenue,
        COALESCE(SUM(CASE WHEN status = 'paid' AND DATE(created_at) = CURDATE() THEN amount ELSE 0 END), 0) as todayRevenue,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pendingPayments,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as successfulPayments
      FROM payments
    `)

    const stats = (statsRows as any[])[0] || {
      totalRevenue: 0,
      todayRevenue: 0,
      pendingPayments: 0,
      successfulPayments: 0
    }

    return NextResponse.json({
      success: true,
      data: {
        payments: paymentsRows,
        stats: stats
      }
    })

  } catch (error) {
    console.error('Error getting payments:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get payments'
      },
      { status: 500 }
    )
  }
}
