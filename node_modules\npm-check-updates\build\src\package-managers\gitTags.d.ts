import { GetVersion } from '../types/GetVersion';
import { Options } from '../types/Options';
import { VersionLevel } from '../types/VersionLevel';
import { VersionResult } from '../types/VersionResult';
import { VersionSpec } from '../types/VersionSpec';
/** Return the highest non-prerelease numbered tag on a remote Git URL. */
export declare const latest: GetVersion;
/** Return the highest numbered tag on a remote Git URL. */
export declare const greatest: GetVersion;
/** Returns a function that returns the highest version at the given level. */
export declare const greatestLevel: (level: VersionLevel) => (name: string, declaration: VersionSpec, options?: Options) => Promise<VersionResult>;
export declare const minor: (name: string, declaration: VersionSpec, options?: Options) => Promise<VersionResult>;
export declare const patch: (name: string, declaration: VersionSpec, options?: Options) => Promise<VersionResult>;
/** All git tags are exact versions, so --target semver should never upgrade git tags. */
export declare const semver: GetVersion;
export declare const newest: GetVersion;
