'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import DashboardLayout from '@/components/DashboardLayout'
import { Shield, ArrowRight } from 'lucide-react'

export default function AdminPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to new admin dashboard
    router.push('/admindashboard')
  }, [router])

  return (
    <DashboardLayout>
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="relative mb-8">
            <Shield className="h-24 w-24 text-cyber-primary mx-auto animate-cyber-glow" />
            <div className="absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse"></div>
          </div>

          <h1 className="text-4xl font-bold mb-4">
            <span className="text-cyber-glow">Admin</span>{' '}
            <span className="text-cyber-pink">Redirect</span>
          </h1>

          <p className="text-gray-300 text-lg mb-8">
            Redirecting to admin dashboard...
          </p>

          <div className="flex items-center justify-center space-x-2 text-cyber-primary">
            <div className="animate-spin rounded-full h-6 w-6 border-2 border-cyber-primary border-t-transparent"></div>
            <span>Loading admin console</span>
            <ArrowRight className="h-5 w-5 animate-pulse" />
          </div>

          <div className="mt-8">
            <button
              onClick={() => router.push('/admindashboard')}
              className="btn-cyber-primary"
            >
              Go to Admin Dashboard
            </button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
      {
        id: '2',
        type: 'system',
        message: 'High API usage detected',
        severity: 'low',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString()
      },
      {
        id: '3',
        type: 'user',
        message: 'New premium subscription activated',
        severity: 'low',
        timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString()
      }
    ],
    systemServices: [
      { name: 'Database', status: 'online', uptime: 99.9, lastCheck: new Date().toISOString() },
      { name: 'API Gateway', status: 'online', uptime: 99.8, lastCheck: new Date().toISOString() },
      { name: 'Scanner Engine', status: 'online', uptime: 98.5, lastCheck: new Date().toISOString() },
      { name: 'OSINT Service', status: 'online', uptime: 99.2, lastCheck: new Date().toISOString() },
      { name: 'File Analyzer', status: 'maintenance', uptime: 95.1, lastCheck: new Date().toISOString() },
      { name: 'Bot Services', status: 'online', uptime: 97.8, lastCheck: new Date().toISOString() }
    ]
  })
  const [loading, setLoading] = useState(false)

  const adminModules = [
    {
      title: 'Dashboard',
      description: 'System monitoring and analytics',
      icon: BarChart3,
      href: '/admin/dashboard',
      color: 'blue',
      stats: `${overview.quickStats.totalUsers} users`
    },
    {
      title: 'User Management',
      description: 'Manage users, roles, and permissions',
      icon: Users,
      href: '/admin/users',
      color: 'green',
      stats: `${overview.quickStats.activeToday} active today`
    },
    {
      title: 'System Settings',
      description: 'Configure system parameters',
      icon: Settings,
      href: '/admin/settings',
      color: 'purple',
      stats: 'Global config'
    },
    {
      title: 'Bot Management',
      description: 'WhatsApp & Telegram bot controls',
      icon: Bot,
      href: '/admin/bots',
      color: 'cyan',
      stats: '2 bots active'
    },
    {
      title: 'Payment System',
      description: 'Subscription and billing management',
      icon: CreditCard,
      href: '/admin/payments',
      color: 'yellow',
      stats: 'Revenue tracking'
    },
    {
      title: 'System Monitor',
      description: 'Real-time system monitoring',
      icon: Monitor,
      href: '/admin/monitoring',
      color: 'red',
      stats: `${overview.systemHealth.uptime}% uptime`
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-400'
      case 'offline': return 'text-red-400'
      case 'maintenance': return 'text-yellow-400'
      case 'healthy': return 'text-green-400'
      case 'warning': return 'text-yellow-400'
      case 'critical': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-blue-400'
      case 'medium': return 'text-yellow-400'
      case 'high': return 'text-orange-400'
      case 'critical': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Admin Overview Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              <span className="text-red-400">🔧 Admin</span>{' '}
              <span className="text-cyber-pink">Overview</span>
            </h1>
            <p className="text-gray-300 text-lg">
              System overview and administrative controls
            </p>
            <div className="flex items-center space-x-6 mt-3">
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${overview.systemHealth.status === 'healthy' ? 'bg-green-400' : overview.systemHealth.status === 'warning' ? 'bg-yellow-400' : 'bg-red-400'} animate-pulse`}></div>
                <span className={`text-sm font-medium ${getStatusColor(overview.systemHealth.status)}`}>
                  System {overview.systemHealth.status.charAt(0).toUpperCase() + overview.systemHealth.status.slice(1)}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="w-5 h-5 text-blue-400" />
                <span className="text-sm text-blue-400 font-medium">{overview.quickStats.totalUsers} Total Users</span>
              </div>
              <div className="flex items-center space-x-2">
                <Activity className="w-5 h-5 text-green-400" />
                <span className="text-sm text-green-400 font-medium">{overview.quickStats.activeToday} Active Today</span>
              </div>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5 text-yellow-400" />
                <span className="text-sm text-yellow-400 font-medium">{overview.quickStats.alertsCount} Alerts</span>
              </div>
            </div>
          </div>

          <div className="mt-6 lg:mt-0 flex items-center space-x-4">
            <div className="text-right">
              <div className="text-sm text-gray-400">System Uptime</div>
              <div className="text-lg font-mono text-green-400">
                {overview.systemHealth.uptime}%
              </div>
            </div>
            <button
              onClick={() => setLoading(!loading)}
              disabled={loading}
              className="btn-cyber-primary"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {loading ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        </div>

        {/* Quick Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="card-cyber">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Users</p>
                <p className="text-2xl font-bold text-white">{overview.quickStats.totalUsers.toLocaleString()}</p>
              </div>
              <Users className="h-8 w-8 text-blue-400" />
            </div>
          </div>

          <div className="card-cyber">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Active Today</p>
                <p className="text-2xl font-bold text-white">{overview.quickStats.activeToday}</p>
              </div>
              <Activity className="h-8 w-8 text-green-400" />
            </div>
          </div>

          <div className="card-cyber">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Scans</p>
                <p className="text-2xl font-bold text-white">{overview.quickStats.totalScans.toLocaleString()}</p>
              </div>
              <Shield className="h-8 w-8 text-cyber-primary" />
            </div>
          </div>

          <div className="card-cyber">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Active Alerts</p>
                <p className="text-2xl font-bold text-white">{overview.quickStats.alertsCount}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-400" />
            </div>
          </div>
        </div>

        {/* Admin Modules Grid */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-6">Administrative Modules</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {adminModules.map((module, index) => {
              const Icon = module.icon
              return (
                <div
                  key={index}
                  onClick={() => router.push(module.href)}
                  className="card-cyber hover:border-cyber-primary transition-all duration-300 cursor-pointer group"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className={`p-3 rounded-lg bg-${module.color}-500/20`}>
                      <Icon className={`h-6 w-6 text-${module.color}-400`} />
                    </div>
                    <ArrowUp className="h-5 w-5 text-gray-400 group-hover:text-cyber-primary transition-colors transform group-hover:-translate-y-1" />
                  </div>

                  <h3 className="text-lg font-semibold text-white mb-2">{module.title}</h3>
                  <p className="text-gray-400 text-sm mb-3">{module.description}</p>

                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">{module.stats}</span>
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* System Services & Recent Alerts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* System Services */}
          <div className="card-cyber">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-white">System Services</h3>
              <Server className="h-6 w-6 text-cyber-primary" />
            </div>

            <div className="space-y-4">
              {overview.systemServices.map((service, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-cyber-dark/50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${service.status === 'online' ? 'bg-green-400' : service.status === 'maintenance' ? 'bg-yellow-400' : 'bg-red-400'} animate-pulse`}></div>
                    <div>
                      <p className="text-white font-medium">{service.name}</p>
                      <p className="text-gray-400 text-xs">Uptime: {service.uptime}%</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`text-sm font-medium ${getStatusColor(service.status)}`}>
                      {service.status.charAt(0).toUpperCase() + service.status.slice(1)}
                    </span>
                    <p className="text-gray-500 text-xs">
                      {new Date(service.lastCheck).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Alerts */}
          <div className="card-cyber">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-white">Recent Alerts</h3>
              <AlertTriangle className="h-6 w-6 text-yellow-400" />
            </div>

            <div className="space-y-4">
              {overview.recentAlerts.map((alert, index) => (
                <div key={index} className="p-3 bg-cyber-dark/50 rounded-lg border-l-4 border-l-yellow-400">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className={`text-xs px-2 py-1 rounded-full bg-${alert.type === 'security' ? 'red' : alert.type === 'system' ? 'yellow' : 'blue'}-500/20 text-${alert.type === 'security' ? 'red' : alert.type === 'system' ? 'yellow' : 'blue'}-400`}>
                          {alert.type.toUpperCase()}
                        </span>
                        <span className={`text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                          {alert.severity.toUpperCase()}
                        </span>
                      </div>
                      <p className="text-white text-sm">{alert.message}</p>
                      <p className="text-gray-500 text-xs mt-1">
                        {new Date(alert.timestamp).toLocaleString()}
                      </p>
                    </div>
                    <button className="text-gray-400 hover:text-white transition-colors">
                      <Eye className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}

              <div className="text-center pt-4">
                <button
                  onClick={() => router.push('/admin/monitoring')}
                  className="text-cyber-primary hover:text-cyber-secondary transition-colors text-sm"
                >
                  View All Alerts →
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card-cyber">
          <h3 className="text-xl font-semibold text-white mb-6">Quick Actions</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button
              onClick={() => router.push('/admin/users')}
              className="btn-cyber-secondary text-sm"
            >
              <Users className="h-4 w-4 mr-2" />
              Manage Users
            </button>
            <button
              onClick={() => router.push('/admin/settings')}
              className="btn-cyber-secondary text-sm"
            >
              <Settings className="h-4 w-4 mr-2" />
              System Config
            </button>
            <button
              onClick={() => router.push('/admin/monitoring')}
              className="btn-cyber-secondary text-sm"
            >
              <Monitor className="h-4 w-4 mr-2" />
              Monitor System
            </button>
            <button
              onClick={() => router.push('/admin/dashboard')}
              className="btn-cyber-primary text-sm"
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              Full Dashboard
            </button>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
