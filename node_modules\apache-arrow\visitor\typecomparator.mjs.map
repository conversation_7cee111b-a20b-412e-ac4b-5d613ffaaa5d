{"version": 3, "sources": ["visitor/typecomparator.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;AAIrB,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAwExC,cAAc;AACd,MAAM,OAAO,cAAe,SAAQ,OAAO;IACvC,cAAc,CAAoB,MAAiB,EAAE,KAAqB;QACtE,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,CACzB,KAAK,YAAY,MAAM,CAAC,WAAW;YACnC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CACtD,CAAC;IACN,CAAC;IACD,iBAAiB,CAAoB,MAA2B,EAAE,MAAuB;QACrF,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,CAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YACrB,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;YACrB,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM;YAC/B,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAC3D,CAAC;IACN,CAAC;IACD,aAAa,CAA2B,KAAe,EAAE,KAAoB;QACzE,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,CACxB,KAAK,YAAY,KAAK,CAAC,WAAW;YAClC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;YACzB,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ;YACjC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CACrC,CAAC;IACN,CAAC;CACJ;AAED,SAAS,kBAAkB,CAAqB,IAAO,EAAE,KAAuB;IAC5E,OAAO,KAAK,YAAY,IAAI,CAAC,WAAW,CAAC;AAC7C,CAAC;AAED,SAAS,UAAU,CAAqB,IAAO,EAAE,KAAuB;IACpE,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC/D,CAAC;AAED,SAAS,UAAU,CAAgB,IAAO,EAAE,KAAuB;IAC/D,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CACvB,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ;QAChC,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,CACnC,CAAC;AACN,CAAC;AAED,SAAS,YAAY,CAAkB,IAAO,EAAE,KAAuB;IACnE,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CACvB,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS,CACrC,CAAC;AACN,CAAC;AAED,SAAS,sBAAsB,CAA4B,IAAO,EAAE,KAAuB;IACvF,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CACvB,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS,CACrC,CAAC;AACN,CAAC;AAED,SAAS,WAAW,CAAkB,IAAO,EAAE,KAAuB;IAClE,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CACvB,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAC3B,CAAC;AACN,CAAC;AAED,SAAS,gBAAgB,CAAsB,IAAO,EAAE,KAAuB;IAC3E,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CACvB,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;QACxB,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,CACnC,CAAC;AACN,CAAC;AAED,SAAS,WAAW,CAAiB,IAAO,EAAE,KAAuB;IACjE,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CACvB,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;QACxB,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,CACnC,CAAC;AACN,CAAC;AAED,SAAS,WAAW,CAAiB,IAAO,EAAE,KAAuB;IACjE,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CACvB,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,QAAQ,CAAC,MAAM;QAC9C,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,CAC5D,CAAC;AACN,CAAC;AAED,SAAS,aAAa,CAAmB,IAAO,EAAE,KAAuB;IACrE,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CACvB,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,QAAQ,CAAC,MAAM;QAC9C,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,CAC5D,CAAC;AACN,CAAC;AAED,SAAS,YAAY,CAAkB,IAAO,EAAE,KAAuB;IACnE,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CACvB,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;QACxB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpD,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,CAC5D,CAAC;AACN,CAAC;AAED,SAAS,iBAAiB,CAAuB,IAAO,EAAE,KAAuB;IAC7E,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CACvB,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE;QACpB,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,SAAS;QAClC,QAAQ,CAAC,KAAK,CAAM,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC;QAChD,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CACpD,CAAC;AACN,CAAC;AAED,SAAS,eAAe,CAAqB,IAAO,EAAE,KAAuB;IACzE,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CACvB,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAC3B,CAAC;AACN,CAAC;AAED,SAAS,eAAe,CAAqB,IAAO,EAAE,KAAuB;IACzE,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CACvB,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAC3B,CAAC;AACN,CAAC;AAED,SAAS,oBAAoB,CAA0B,IAAO,EAAE,KAAuB;IACnF,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CACvB,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ;QAChC,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,QAAQ,CAAC,MAAM;QAC9C,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,CAC5D,CAAC;AACN,CAAC;AAED,SAAS,UAAU,CAAiB,IAAO,EAAE,KAAuB;IAChE,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CACvB,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;QAC/B,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,UAAU;QACpC,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,KAAK,CAAC,QAAQ,CAAC,MAAM;QAC9C,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,CAC5D,CAAC;AACN,CAAC;AAED,cAAc,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,CAAC;AAChD,cAAc,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,CAAC;AAChD,cAAc,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,CAAC;AAC/C,cAAc,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,CAAC;AAChD,cAAc,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,CAAC;AACjD,cAAc,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,CAAC;AACjD,cAAc,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,CAAC;AACjD,cAAc,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,CAAC;AACjD,cAAc,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,CAAC;AAClD,cAAc,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,CAAC;AAClD,cAAc,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,CAAC;AAClD,cAAc,CAAC,SAAS,CAAC,UAAU,GAAG,YAAY,CAAC;AACnD,cAAc,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY,CAAC;AACrD,cAAc,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY,CAAC;AACrD,cAAc,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY,CAAC;AACrD,cAAc,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,CAAC;AAChD,cAAc,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,CAAC;AACrD,cAAc,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,CAAC;AAClD,cAAc,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,CAAC;AACvD,cAAc,CAAC,SAAS,CAAC,oBAAoB,GAAG,sBAAsB,CAAC;AACvE,cAAc,CAAC,SAAS,CAAC,SAAS,GAAG,WAAW,CAAC;AACjD,cAAc,CAAC,SAAS,CAAC,YAAY,GAAG,WAAW,CAAC;AACpD,cAAc,CAAC,SAAS,CAAC,oBAAoB,GAAG,WAAW,CAAC;AAC5D,cAAc,CAAC,SAAS,CAAC,cAAc,GAAG,gBAAgB,CAAC;AAC3D,cAAc,CAAC,SAAS,CAAC,oBAAoB,GAAG,gBAAgB,CAAC;AACjE,cAAc,CAAC,SAAS,CAAC,yBAAyB,GAAG,gBAAgB,CAAC;AACtE,cAAc,CAAC,SAAS,CAAC,yBAAyB,GAAG,gBAAgB,CAAC;AACtE,cAAc,CAAC,SAAS,CAAC,wBAAwB,GAAG,gBAAgB,CAAC;AACrE,cAAc,CAAC,SAAS,CAAC,SAAS,GAAG,WAAW,CAAC;AACjD,cAAc,CAAC,SAAS,CAAC,eAAe,GAAG,WAAW,CAAC;AACvD,cAAc,CAAC,SAAS,CAAC,oBAAoB,GAAG,WAAW,CAAC;AAC5D,cAAc,CAAC,SAAS,CAAC,oBAAoB,GAAG,WAAW,CAAC;AAC5D,cAAc,CAAC,SAAS,CAAC,mBAAmB,GAAG,WAAW,CAAC;AAC3D,cAAc,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,CAAC;AACnD,cAAc,CAAC,SAAS,CAAC,SAAS,GAAG,WAAW,CAAC;AACjD,cAAc,CAAC,SAAS,CAAC,WAAW,GAAG,aAAa,CAAC;AACrD,cAAc,CAAC,SAAS,CAAC,UAAU,GAAG,YAAY,CAAC;AACnD,cAAc,CAAC,SAAS,CAAC,eAAe,GAAG,YAAY,CAAC;AACxD,cAAc,CAAC,SAAS,CAAC,gBAAgB,GAAG,YAAY,CAAC;AACzD,cAAc,CAAC,SAAS,CAAC,eAAe,GAAG,iBAAiB,CAAC;AAC7D,cAAc,CAAC,SAAS,CAAC,aAAa,GAAG,eAAe,CAAC;AACzD,cAAc,CAAC,SAAS,CAAC,oBAAoB,GAAG,eAAe,CAAC;AAChE,cAAc,CAAC,SAAS,CAAC,sBAAsB,GAAG,eAAe,CAAC;AAClE,cAAc,CAAC,SAAS,CAAC,aAAa,GAAG,eAAe,CAAC;AACzD,cAAc,CAAC,SAAS,CAAC,mBAAmB,GAAG,eAAe,CAAC;AAC/D,cAAc,CAAC,SAAS,CAAC,wBAAwB,GAAG,eAAe,CAAC;AACpE,cAAc,CAAC,SAAS,CAAC,wBAAwB,GAAG,eAAe,CAAC;AACpE,cAAc,CAAC,SAAS,CAAC,uBAAuB,GAAG,eAAe,CAAC;AACnE,cAAc,CAAC,SAAS,CAAC,kBAAkB,GAAG,oBAAoB,CAAC;AACnE,cAAc,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,CAAC;AAE/C,cAAc;AACd,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;AAE7C,MAAM,UAAU,cAAc,CAAoB,MAAiB,EAAE,KAAqB;IACtF,OAAO,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAClD,CAAC;AAED,MAAM,UAAU,aAAa,CAA2B,KAAe,EAAE,KAAoB;IACzF,OAAO,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAChD,CAAC;AAED,MAAM,UAAU,YAAY,CAA2B,IAAO,EAAE,KAAgB;IAC5E,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACvC,CAAC", "file": "typecomparator.mjs", "sourceRoot": "../src"}