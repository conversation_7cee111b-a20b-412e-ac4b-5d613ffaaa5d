{"version": 3, "file": "telegraf.d.ts", "sourceRoot": "", "sources": ["../src/telegraf.ts"], "names": [], "mappings": ";;AACA,OAAO,KAAK,IAAI,MAAM,MAAM,CAAA;AAE5B,OAAO,KAAK,EAAE,MAAM,uBAAuB,CAAA;AAC3C,OAAO,KAAK,EAAE,MAAM,kBAAkB,CAAA;AACtC,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAA;AACrC,OAAO,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAA;AAClD,OAAO,SAAS,MAAM,uBAAuB,CAAA;AAE7C,OAAO,OAAO,MAAM,WAAW,CAAA;AAK/B,OAAO,QAAQ,MAAM,YAAY,CAAA;AACjC,OAAO,EAAE,UAAU,EAAE,MAAM,KAAK,CAAA;AAiBhC,yBAAiB,QAAQ,CAAC;IACxB,UAAiB,OAAO,CAAC,QAAQ,SAAS,OAAO;QAC/C,WAAW,EAAE,KACX,GAAG,IAAI,EAAE,qBAAqB,CAAC,OAAO,OAAO,CAAC,KAC3C,QAAQ,CAAA;QACb,cAAc,EAAE,MAAM,CAAA;QACtB,QAAQ,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;KACtC;IAED,UAAiB,aAAa;QAC5B,kBAAkB,CAAC,EAAE,OAAO,CAAA;QAC5B,6DAA6D;QAC7D,cAAc,CAAC,EAAE,EAAE,CAAC,UAAU,EAAE,CAAA;QAChC,iEAAiE;QACjE,OAAO,CAAC,EAAE;YACR,iCAAiC;YACjC,MAAM,EAAE,MAAM,CAAA;YAEd;;;iBAGK;YACL,QAAQ,CAAC,EAAE,MAAM,CAAA;YAEjB,yEAAyE;YACzE,IAAI,CAAC,EAAE,MAAM,CAAA;YAEb,IAAI,CAAC,EAAE,MAAM,CAAA;YACb,IAAI,CAAC,EAAE,MAAM,CAAA;YAEb,sHAAsH;YACtH,SAAS,CAAC,EAAE,MAAM,CAAA;YAElB;;;eAGG;YACH,cAAc,CAAC,EAAE,MAAM,CAAA;YAEvB,4CAA4C;YAC5C,UAAU,CAAC,EAAE,UAAU,CAAA;YAEvB;;;;eAIG;YACH,WAAW,CAAC,EAAE,MAAM,CAAA;YAEpB;;;eAGG;YACH,WAAW,CAAC,EAAE,EAAE,CAAC,SAAS,CAAA;YAE1B,EAAE,CAAC,EAAE,IAAI,CAAC,eAAe,CAAA;SAC1B,CAAA;KACF;CACF;AAID,qBAAa,QAAQ,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,CAAE,SAAQ,QAAQ,CAAC,CAAC,CAAC;IACpE,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAqB;IAC7C,OAAO,CAAC,aAAa,CAAC,CAA4B;IAClD,OAAO,CAAC,OAAO,CAAC,CAAS;IACzB,mFAAmF;IAC5E,OAAO,CAAC,EAAE,EAAE,CAAC,aAAa,CAAA;IAC1B,QAAQ,EAAE,QAAQ,CAAA;IACzB,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAK;IAEjC;;;OAGG;IACI,aAAa,SAGZ;QACJ,mCAAmC;QACnC,QAAQ,EAAE,MAAM,CAAA;QAChB,IAAI,EAAE,MAAM,CAAA;QACZ,WAAW,CAAC,EAAE,MAAM,CAAA;KACrB,OACI,KAAK,eAAe,aAiB1B;IAED,OAAO,CAAC,WAAW,CAOlB;gBAEW,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAWjE,OAAO,KAAK,KAAK,GAEhB;IAED,kDAAkD;IAClD,IAAI,YAAY,CAAC,YAAY,EAAE,OAAO,EAErC;IAED,kDAAkD;IAClD,IAAI,YAAY,IALe,OAAO,CAOrC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,KAAK,YAAY,CAAC,IAAI,CAAC;IAK3D;;;OAGG;IACH,eAAe,CAAC,IAAI,SAAM,EAAE,IAAI,GAAE;QAAE,WAAW,CAAC,EAAE,MAAM,CAAA;KAAO;;;IAS/D,OAAO,CAAC,aAAa;IAiBrB;;;OAGG;IACG,aAAa,CACjB,IAAI,EAAE;QAAE,MAAM,EAAE,MAAM,CAAC;QAAC,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GAAG,EAAE,CAAC,eAAe;;;IAc9D,OAAO,CAAC,YAAY;IAOpB,OAAO,CAAC,YAAY;IAuBpB,mBAAmB;IAQb,MAAM,CAAC,QAAQ,CAAC,EAAE,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IAC5C,MAAM,CACV,MAAM,EAAE,QAAQ,CAAC,aAAa,EAC9B,QAAQ,CAAC,EAAE,MAAM,IAAI,GACpB,OAAO,CAAC,IAAI,CAAC;IAgDhB,IAAI,CAAC,MAAM,SAAgB;IAU3B,OAAO,CAAC,WAAW,CAAC,CAA2B;IACzC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC,EAAE,IAAI,CAAC,cAAc;CA0B5E"}