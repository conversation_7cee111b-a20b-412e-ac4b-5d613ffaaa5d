import { IgnoredUpgrade } from '../types/IgnoredUpgrade';
import { Index } from '../types/IndexType';
import { Options } from '../types/Options';
import { VersionResult } from '../types/VersionResult';
import { VersionSpec } from '../types/VersionSpec';
type LogLevel = 'silent' | 'error' | 'warn' | 'info' | 'verbose' | 'silly' | null;
/**
 * Prints a message if it is included within options.loglevel.
 *
 * @param options    Command line options. These will be compared to the loglevel parameter to determine if the message gets printed.
 * @param message    The message to print
 * @param loglevel   silent|error|warn|info|verbose|silly
 * @param method     The console method to call. Default: 'log'.
 */
export declare function print(options: Options, message: any, loglevel?: LogLevel, method?: 'log' | 'warn' | 'info' | 'error'): void;
/** Pretty print a JSON object. */
export declare function printJson(options: Options, object: any): void;
/** Print JSON object keys as string joined by character. */
export declare function printSimpleJoinedString(object: any, join: string): void;
/** Prints an object sorted by key. */
export declare function printSorted<T extends {
    [key: string]: any;
}>(options: Options, obj: T, loglevel: LogLevel): void;
/**
 * Renders a color-coded table of upgrades.
 *
 * @param args
 * @param args.from
 * @param args.to
 * @param args.ownersChangedDeps
 * @param args.format
 */
export declare function toDependencyTable({ from: fromDeps, to: toDeps, format, ownersChangedDeps, pkgFile, time, }: {
    from: Index<VersionSpec>;
    to: Index<VersionSpec>;
    format?: string[];
    ownersChangedDeps?: Index<boolean>;
    /** See: logging/getPackageRepo pkgFile param. */
    pkgFile?: string;
    time?: Index<string>;
}): Promise<string>;
/**
 * Renders one or more color-coded tables with all upgrades. Supports different formats from the --format option.
 *
 * @param args
 * @param args.current
 * @param args.upgraded
 * @param args.ownersChangedDeps
 * @param options
 */
export declare function printUpgradesTable({ current, upgraded, ownersChangedDeps, pkgFile, time, }: {
    current: Index<VersionSpec>;
    upgraded: Index<VersionSpec>;
    ownersChangedDeps?: Index<boolean>;
    pkgFile?: string;
    time?: Index<string>;
}, options: Options): Promise<void>;
/**
 * @param args.current -
 * @param args.latest -
 * @param args.upgraded -
 * @param args.total -
 * @param args.ownersChangedDeps -
 */
export declare function printUpgrades(options: Options, { current, latest, upgraded, total, ownersChangedDeps, pkgFile, time, errors, }: {
    current: Index<VersionSpec>;
    latest?: Index<VersionResult>;
    upgraded: Index<VersionSpec>;
    total: number;
    ownersChangedDeps?: Index<boolean>;
    pkgFile?: string;
    time?: Index<string>;
    errors?: Index<string>;
}): Promise<void>;
/** Print updates that were ignored due to incompatible peer dependencies. */
export declare function printIgnoredUpdates(options: Options, ignoredUpdates: Index<IgnoredUpgrade>): void;
export {};
