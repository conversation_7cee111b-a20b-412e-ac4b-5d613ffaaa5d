{"version": 3, "sources": ["fb/fixed-size-binary.ts"], "names": [], "mappings": ";AAAA,qEAAqE;;;AAErE,2CAA2C;AAE3C,MAAa,eAAe;IAA5B;QACE,OAAE,GAAgC,IAAI,CAAC;QACvC,WAAM,GAAG,CAAC,CAAC;IA0Cb,CAAC;IAzCC,MAAM,CAAC,CAAQ,EAAE,EAAyB;QAC1C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,wBAAwB,CAAC,EAAyB,EAAE,GAAoB;QAC7E,OAAO,CAAC,GAAG,IAAI,IAAI,eAAe,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IAChG,CAAC;IAED,MAAM,CAAC,oCAAoC,CAAC,EAAyB,EAAE,GAAoB;QACzF,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,IAAI,IAAI,eAAe,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IAChG,CAAC;IAED;;OAEG;IACH,SAAS;QACP,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,OAA2B;QACrD,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAA2B,EAAE,SAAgB;QAC/D,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;IACzC,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,OAA2B;QACnD,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,qBAAqB,CAAC,OAA2B,EAAE,SAAgB;QACxE,eAAe,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC9C,eAAe,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACjD,OAAO,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACrD,CAAC;CACA;AA5CD,0CA4CC", "file": "fixed-size-binary.js", "sourceRoot": "../src"}