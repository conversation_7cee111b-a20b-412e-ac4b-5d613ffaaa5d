// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.
(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
    typeof define === 'function' && define.amd ? define(['exports'], factory) :
    (factory(global.Arrow = global.Arrow || {}));
}(this, (function (exports) {var h;function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var ba="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
function ca(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var da=ca(this);function ea(a,b){if(b)a:{var c=da;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&ba(c,a,{configurable:!0,writable:!0,value:b})}}
ea("Symbol",function(a){function b(f){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c(d+(f||"")+"_"+e++,f)}function c(f,g){this.Hb=f;ba(this,"description",{configurable:!0,writable:!0,value:g})}if(a)return a;c.prototype.toString=function(){return this.Hb};var d="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",e=0;return b});
ea("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=da[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return fa(aa(this))}})}return a});ea("Symbol.asyncIterator",function(a){return a?a:Symbol("Symbol.asyncIterator")});
function fa(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}function ha(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(g){g.done?d(g.value):Promise.resolve(g.value).then(b,c).then(f,e)}f(a.next())})}function l(a){return ha(a())}
function ia(a){var b=a[Symbol.asyncIterator];if(void 0!==b)a=b.call(a);else{b=ja;var c="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];if(c)a=c.call(a);else if("number"==typeof a.length)a={next:aa(a)};else throw Error(String(a)+" is not an iterable or ArrayLike");a=new b(a)}return a}
function ja(a){this[Symbol.asyncIterator]=function(){return this};this[Symbol.iterator]=function(){return a};this.next=function(b){return Promise.resolve(a.next(b))};void 0!==a["throw"]&&(this["throw"]=function(b){return Promise.resolve(a["throw"](b))});void 0!==a["return"]&&(this["return"]=function(b){return Promise.resolve(a["return"](b))})}function n(a,b){this.action=a;this.value=b}function ka(a,b,c,d){this.method=a;this.ea=b;this.resolve=c;this.reject=d}
function la(a){this.frame=a;this.next=null}function ma(){this.Ma=this.V=null}ma.prototype.first=function(){if(this.V)return this.V.frame;throw Error("no frames in executionQueue");};function na(a){a.V&&(a.V=a.V.next,a.V||(a.Ma=null))}ma.prototype.enqueue=function(a){a=new la(a);this.Ma?this.Ma.next=a:this.V=a;this.Ma=a};
function oa(a){this.Bb=a;this.R=null;this.K=new ma;this[Symbol.asyncIterator]=function(){return this};var b=this;this.Vb=function(c){var d=b.K.first();!0===c.done?(b.R=null,d.method="next",d.ea=c.value):(d.resolve({value:c.value,done:!1}),na(b.K));pa(b)};this.Ub=function(c){qa(b,c)};this.$a=function(c){ra(b,c)}}function ta(a,b,c){return new Promise(function(d,e){var f=null===a.K.V;a.K.enqueue(new ka(b,c,d,e));f&&pa(a)})}oa.prototype.next=function(a){return ta(this,"next",a)};
oa.prototype.return=function(a){return ta(this,"return",new n(0,a))};oa.prototype.throw=function(a){return ta(this,"throw",a)};function pa(a){if(null!==a.K.V)try{if(a.R){if(!a.R)throw Error("no delegate to perform execution");var b=a.K.first();if(b.method in a.R)try{a.R[b.method](b.ea).then(a.Vb,a.Ub).catch(a.$a)}catch(c){qa(a,c)}else a.R=null,pa(a)}else ua(a)}catch(c){ra(a,c)}}
function ua(a){var b=a.K.first();try{var c=a.Bb[b.method](b.ea);if(c.value instanceof n)switch(c.value.action){case 0:Promise.resolve(c.value.value).then(function(d){b.resolve({value:d,done:c.done});na(a.K);pa(a)},function(d){b.reject(d);na(a.K);pa(a)}).catch(a.$a);break;case 1:a.R=ia(c.value.value);b.method="next";b.ea=void 0;pa(a);break;case 2:Promise.resolve(c.value.value).then(function(d){b.method="next";b.ea=d;pa(a)},function(d){b.method="throw";b.ea=d;pa(a)}).catch(a.$a);break;default:throw Error("Unrecognized AsyncGeneratorWrapper$ActionEnum");
}else b.resolve(c),na(a.K),pa(a)}catch(d){b.reject(d),na(a.K),pa(a)}}function qa(a,b){var c=a.K.first();a.R=null;c.method="throw";c.ea=b;pa(a)}function ra(a,b){null!==a.K.V&&(a.K.first().reject(b),na(a.K));a.R&&"return"in a.R&&(a.R["return"](void 0),a.R=null);a.Bb["return"](void 0);pa(a)}const va=new TextDecoder("utf-8"),wa=a=>va.decode(a),xa=new TextEncoder;const q=a=>"function"===typeof a,t=a=>null!=a&&Object(a)===a,ya=a=>"_getDOMStream"in a&&"_getNodeStream"in a,za=a=>t(a)&&q(a.cancel)&&q(a.getReader)&&!ya(a),Aa=a=>t(a)&&q(a.read)&&q(a.pipe)&&"boolean"===typeof a.readable&&!ya(a);const Ba="undefined"!==typeof SharedArrayBuffer?SharedArrayBuffer:ArrayBuffer;function Ca(a){const b=a[0]?[a[0]]:[];let c,d,e,f;for(let g,k,m=0,p=0,r=a.length;++m<r;)g=b[p],k=a[m],!g||!k||g.buffer!==k.buffer||k.byteOffset<g.byteOffset?k&&(b[++p]=k):({byteOffset:c,byteLength:e}=g,{byteOffset:d,byteLength:f}=k,c+e<d||d+f<c?k&&(b[++p]=k):b[p]=new Uint8Array(g.buffer,c,d-c+f));return b}
function Da(a,b,c=0,d=b.byteLength){const e=a.byteLength,f=new Uint8Array(a.buffer,a.byteOffset,e);b=new Uint8Array(b.buffer,b.byteOffset,Math.min(d,e));f.set(b,c);return a}
function Ea(a,b){a=Ca(a);const c=a.reduce((m,p)=>m+p.byteLength,0);let d,e,f=0,g=-1;const k=Math.min(b||Number.POSITIVE_INFINITY,c);for(const m=a.length;++g<m;){b=a[g];d=b.subarray(0,Math.min(b.length,k-f));if(k<=f+d.length){d.length<b.length?a[g]=b.subarray(d.length):d.length===b.length&&g++;e?Da(e,d,f):e=d;break}Da(e||(e=new Uint8Array(k)),d,f);f+=d.length}return[e||new Uint8Array(0),a.slice(g),c-(e?e.byteLength:0)]}
function u(a,b){b=t(b)&&"done"in b&&"value"in b?b.value:b;if(b instanceof a)return a===Uint8Array?new a(b.buffer,b.byteOffset,b.byteLength):b;if(!b)return new a(0);"string"===typeof b&&(b=xa.encode(b));return b instanceof ArrayBuffer||b instanceof Ba?new a(b):t(b)&&q(b.clear)&&q(b.bytes)&&q(b.position)&&q(b.setPosition)&&q(b.capacity)&&q(b.getBufferIdentifier)&&q(b.createLong)?u(a,b.T()):ArrayBuffer.isView(b)?0>=b.byteLength?new a(0):new a(b.buffer,b.byteOffset,b.byteLength/a.BYTES_PER_ELEMENT):a.from(b)}
const Fa=a=>{a.next();return a};function*Ga(a,b){function*c(d){yield d}b="string"===typeof b?c(b):ArrayBuffer.isView(b)?c(b):b instanceof ArrayBuffer?c(b):b instanceof Ba?c(b):t(b)&&q(b[Symbol.iterator])?b:c(b);yield*Fa(function*(d){let e=null;do e=d.next(yield u(a,e));while(!e.done)}(b[Symbol.iterator]()));return new a}
function Ha(a,b){return new oa(function*(){function c(f){return new oa(function*(){yield new n(1,Fa(function*(g){let k=null;do{let m;k=g.next(yield null==(m=k)?void 0:m.value)}while(!k.done)}(f[Symbol.iterator]())))}())}function d(f){return new oa(function*(){yield new n(0,yield new n(2,f))}())}if(t(b)&&q(b.then))return new n(0,yield new n(1,Ha(a,yield new n(2,b))));const e="string"===typeof b?d(b):ArrayBuffer.isView(b)?d(b):b instanceof ArrayBuffer?d(b):b instanceof Ba?d(b):t(b)&&q(b[Symbol.iterator])?
c(b):t(b)&&q(b[Symbol.asyncIterator])?b:d(b);yield new n(1,Fa(function(f){return new oa(function*(){let g=null;do g=yield new n(2,f.next(yield new n(0,u(a,g))));while(!g.done)}())}(e[Symbol.asyncIterator]())));return new n(0,new a)}())}function Ja(a,b,c){if(0!==a){c=c.slice(0,b);for(let d=-1,e=c.length;++d<e;)c[d]+=a}return c.subarray(0,b)}function Ka(a,b){let c=0;const d=a.length;if(d!==b.length)return!1;if(0<d){do if(a[c]!==b[c])return!1;while(++c<d)}return!0}var v={};v.compareArrayLike=Ka;
v.joinUint8Arrays=Ea;v.memcpy=Da;v.rebaseValueOffsets=Ja;v.toArrayBufferView=u;v.toArrayBufferViewAsyncIterator=Ha;v.toArrayBufferViewIterator=Ga;v.toBigInt64Array=a=>u(BigInt64Array,a);v.toBigUint64Array=a=>u(BigUint64Array,a);v.toFloat32Array=a=>u(Float32Array,a);v.toFloat32ArrayAsyncIterator=a=>Ha(Float32Array,a);v.toFloat32ArrayIterator=a=>Ga(Float32Array,a);v.toFloat64Array=a=>u(Float64Array,a);v.toFloat64ArrayAsyncIterator=a=>Ha(Float64Array,a);v.toFloat64ArrayIterator=a=>Ga(Float64Array,a);
v.toInt16Array=a=>u(Int16Array,a);v.toInt16ArrayAsyncIterator=a=>Ha(Int16Array,a);v.toInt16ArrayIterator=a=>Ga(Int16Array,a);v.toInt32Array=a=>u(Int32Array,a);v.toInt32ArrayAsyncIterator=a=>Ha(Int32Array,a);v.toInt32ArrayIterator=a=>Ga(Int32Array,a);v.toInt8Array=a=>u(Int8Array,a);v.toInt8ArrayAsyncIterator=a=>Ha(Int8Array,a);v.toInt8ArrayIterator=a=>Ga(Int8Array,a);v.toUint16Array=a=>u(Uint16Array,a);v.toUint16ArrayAsyncIterator=a=>Ha(Uint16Array,a);v.toUint16ArrayIterator=a=>Ga(Uint16Array,a);
v.toUint32Array=a=>u(Uint32Array,a);v.toUint32ArrayAsyncIterator=a=>Ha(Uint32Array,a);v.toUint32ArrayIterator=a=>Ga(Uint32Array,a);v.toUint8Array=a=>u(Uint8Array,a);v.toUint8ArrayAsyncIterator=a=>Ha(Uint8Array,a);v.toUint8ArrayIterator=a=>Ga(Uint8Array,a);v.toUint8ClampedArray=a=>u(Uint8ClampedArray,a);v.toUint8ClampedArrayAsyncIterator=a=>Ha(Uint8ClampedArray,a);v.toUint8ClampedArrayIterator=a=>Ga(Uint8ClampedArray,a);function La(){throw Error('"toDOMStream" not available in this environment');}function Ma(){throw Error('"toNodeStream" not available in this environment');}const Na=a=>{a.next();return a};
function*Oa(a){let b,c=!1,d=[],e,f,g,k=0;({N:f,size:g}=(yield null)||{N:"read",size:0});a=Ga(Uint8Array,a)[Symbol.iterator]();try{do if({done:b,value:e}=Number.isNaN(g-k)?a.next():a.next(g-k),!b&&0<e.byteLength&&(d.push(e),k+=e.byteLength),b||g<=k){do{if("peek"===f)var m=Ea(d,g)[0];else[e,d,k]=Ea(d,g),m=e;({N:f,size:g}=yield m)}while(g<k)}while(!b)}catch(p){(c=!0,"function"===typeof a.throw)&&a.throw(p)}finally{!1===c&&"function"===typeof a.return&&a.return(null)}return null}
function Pa(a){return new oa(function*(){let b,c=!1,d=[],e,f,g,k=0;({N:f,size:g}=(yield new n(0,null))||{N:"read",size:0});const m=Ha(Uint8Array,a)[Symbol.asyncIterator]();try{do if({done:b,value:e}=Number.isNaN(g-k)?yield new n(2,m.next()):yield new n(2,m.next(g-k)),!b&&0<e.byteLength&&(d.push(e),k+=e.byteLength),b||g<=k){do{if("peek"===f)var p=Ea(d,g)[0];else[e,d,k]=Ea(d,g),p=e;({N:f,size:g}=yield new n(0,p))}while(g<k)}while(!b)}catch(r){(c=!0,"function"===typeof m.throw)&&(yield new n(2,m.throw(r)))}finally{!1===
c&&"function"===typeof m.return&&(yield new n(2,m.return(new Uint8Array(0))))}return new n(0,null)}())}
function Qa(a){return new oa(function*(){let b=!1,c=!1,d=[],e,f,g,k=0;({N:f,size:g}=(yield new n(0,null))||{N:"read",size:0});const m=new Ra(a);try{do if({done:b,value:e}=Number.isNaN(g-k)?yield new n(2,m.read()):yield new n(2,m.read(g-k)),!b&&0<e.byteLength&&(d.push(u(Uint8Array,e)),k+=e.byteLength),b||g<=k){do{if("peek"===f)var p=Ea(d,g)[0];else[e,d,k]=Ea(d,g),p=e;({N:f,size:g}=yield new n(0,p))}while(g<k)}while(!b)}catch(r){c=!0,yield new n(2,m.cancel(r))}finally{!1===c?yield new n(2,m.cancel()):
a.locked&&m.releaseLock()}return new n(0,null)}())}
class Ra{constructor(a){this.source=a;this.W=null;this.W=this.source.getReader();this.W.closed.catch(()=>{})}get closed(){return this.W?this.W.closed.catch(()=>{}):Promise.resolve()}releaseLock(){this.W&&this.W.releaseLock();this.W=null}cancel(a){const b=this;return l(function*(){const c=b.W,d=b.source;c&&(yield c.cancel(a).catch(()=>{}));d&&d.locked&&b.releaseLock()})}read(a){const b=this;return l(function*(){if(0===a)return{done:null==b.W,value:new Uint8Array(0)};const c=yield b.W.read();!c.done&&
(c.value=u(Uint8Array,c));return c})}}const Sa=(a,b)=>{const c=e=>d([b,e]);let d;return[b,c,new Promise(e=>(d=e)&&a.once(b,c))]};
function Ta(a){return new oa(function*(){function b(sa,Ia){r=p=null;return new Promise((Jc,ij)=>{for(const [Db,jj]of sa)a.off(Db,jj);try{const Db=a.destroy;Db&&Db.call(a,Ia);Ia=void 0}catch(Db){Ia=Db||Ia}finally{null!=Ia?ij(Ia):Jc()}})}const c=[];let d="error",e=!1,f=null,g,k,m=0,p=[],r;({N:g,size:k}=(yield new n(0,null))||{N:"read",size:0});if(a.isTTY)return yield new n(0,new Uint8Array(0)),new n(0,null);try{c[0]=Sa(a,"end");c[1]=Sa(a,"error");do{c[2]=Sa(a,"readable");[d,f]=yield new n(2,Promise.race(c.map(sa=>
sa[2])));if("error"===d)break;(e="end"===d)||(Number.isFinite(k-m)?(r=u(Uint8Array,a.read(k-m)),r.byteLength<k-m&&(r=u(Uint8Array,a.read()))):r=u(Uint8Array,a.read()),0<r.byteLength&&(p.push(r),m+=r.byteLength));if(e||k<=m){do{if("peek"===g)var T=Ea(p,k)[0];else[r,p,m]=Ea(p,k),T=r;({N:g,size:k}=yield new n(0,T))}while(k<m)}}while(!e)}finally{yield new n(2,b(c,"error"===d?f:null))}return new n(0,null)}())};var w,Ua=w||(w={});Ua[Ua.V1=0]="V1";Ua[Ua.V2=1]="V2";Ua[Ua.V3=2]="V3";Ua[Ua.V4=3]="V4";Ua[Ua.V5=4]="V5";var x,Va=x||(x={});Va[Va.Sparse=0]="Sparse";Va[Va.Dense=1]="Dense";var y,Wa=y||(y={});Wa[Wa.HALF=0]="HALF";Wa[Wa.SINGLE=1]="SINGLE";Wa[Wa.DOUBLE=2]="DOUBLE";var Xa,Ya=Xa||(Xa={});Ya[Ya.DAY=0]="DAY";Ya[Ya.MILLISECOND=1]="MILLISECOND";var z,Za=z||(z={});Za[Za.SECOND=0]="SECOND";Za[Za.MILLISECOND=1]="MILLISECOND";Za[Za.MICROSECOND=2]="MICROSECOND";Za[Za.NANOSECOND=3]="NANOSECOND";var $a,ab=$a||($a={});ab[ab.YEAR_MONTH=0]="YEAR_MONTH";ab[ab.DAY_TIME=1]="DAY_TIME";ab[ab.MONTH_DAY_NANO=2]="MONTH_DAY_NANO";const bb=new Int32Array(2);new Float32Array(bb.buffer);new Float64Array(bb.buffer);new Uint16Array((new Uint8Array([1,0])).buffer);var cb,db=cb||(cb={});db[db.UTF8_BYTES=1]="UTF8_BYTES";db[db.UTF16_STRING=2]="UTF16_STRING";function eb(a,b){return(a.l[b]|a.l[b+1]<<8)<<16>>16}function fb(a,b){return BigInt.asIntN(64,BigInt(a.u(b)>>>0)+(BigInt(a.u(b+4)>>>0)<<BigInt(32)))}function A(a,b,c){b-=a.u(b);return c<eb(a,b)?eb(a,b+c):0}function gb(a,b,c){b.h=c+a.u(c);b.g=a;return b}function hb(a,b,c){b+=a.u(b);const d=a.u(b);b+=4;b=a.l.subarray(b,b+d);return c===cb.UTF8_BYTES?b:a.$b.decode(b)}function ib(a,b){return b+a.u(b)}function jb(a,b){return b+a.u(b)+4}function kb(a,b){return a.u(b+a.u(b))}
class lb{constructor(a){this.l=a;this.gb=0;this.$b=new TextDecoder}clear(){this.gb=0}T(){return this.l}position(){return this.gb}setPosition(a){this.gb=a}u(a){return this.l[a]|this.l[a+1]<<8|this.l[a+2]<<16|this.l[a+3]<<24}ja(a,b){this.l[a]=b}lb(a,b){this.l[a]=b;this.l[a+1]=b>>8}Y(a,b){this.l[a]=b;this.l[a+1]=b>>8;this.l[a+2]=b>>16;this.l[a+3]=b>>24}aa(a,b){this.Y(a,Number(BigInt.asIntN(32,b)));this.Y(a+4,Number(BigInt.asIntN(32,b>>BigInt(32))))}};function mb(a){return a.g.T().subarray(a.g.position(),a.g.position()+a.offset())}function nb(a,b){for(let c=0;c<b;c++)a.g.ja(--a.B,0)}function ob(a,b,c){b>a.ua&&(a.ua=b);const d=~(a.g.l.length-a.B+c)+1&b-1;for(;a.B<d+b+c;){const g=a.g.l.length;var e=a,f=a.g;const k=f.l.length;if(k&3221225472)throw Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");const m=k<<1,p=new lb(new Uint8Array(m));p.setPosition(m-k);p.T().set(f.T(),m-k);e.g=p;a.B+=a.g.l.length-g}nb(a,d)}
function pb(a,b){ob(a,2,0);a.lb(b)}function qb(a,b){ob(a,4,0);a.Y(b)}function rb(a,b,c,d){if(a.fa||c!=d)ob(a,1,0),a.ja(c),a.slot(b)}function sb(a,b,c){if(a.fa||b!=c)pb(a,b),a.slot(0)}function tb(a,b,c,d){if(a.fa||c!=d)qb(a,c),a.slot(b)}function ub(a,b,c){var d=BigInt("0");if(a.fa||c!==d)ob(a,8,0),a.aa(c),a.slot(b)}function vb(a,b){ob(a,4,0);a.Y(a.offset()-b+4)}function B(a,b,c){if(a.fa||0!=c)vb(a,c),a.slot(b)}
function wb(a){if(a.ta)throw new TypeError("FlatBuffers: object serialization must not be nested.");}function C(a,b){wb(a);null==a.X&&(a.X=[]);a.kb=b;for(let c=0;c<b;c++)a.X[c]=0;a.ta=!0;a.fb=a.offset()}
function D(a){if(null==a.X||!a.ta)throw Error("FlatBuffers: endObject called without startObject");qb(a,0);const b=a.offset();let c=a.kb-1;for(;0<=c&&0==a.X[c];c--);for(var d=c+1;0<=c;c--)pb(a,0!=a.X[c]?b-a.X[c]:0);pb(a,b-a.fb);d=2*(d+2);pb(a,d);let e=0;const f=a.B;c=0;a:for(;c<a.va.length;c++){const g=a.g.l.length-a.va[c];if(d==eb(a.g,g)){for(let k=2;k<d;k+=2)if(eb(a.g,f+k)!=eb(a.g,g+k))continue a;e=a.va[c];break}}e?(a.B=a.g.l.length-b,a.g.Y(a.B,e-b)):(a.va.push(a.offset()),a.g.Y(a.g.l.length-b,
a.offset()-b));a.ta=!1;return b}function xb(a,b,c,d){wb(a);a.jb=c;ob(a,4,b*c);ob(a,d,b*c)}function yb(a){a.Y(a.jb);return a.offset()}function zb(a,b){if(null===b||void 0===b)return 0;b=b instanceof Uint8Array?b:a.ac.encode(b);ob(a,1,0);a.ja(0);xb(a,1,b.length,1);a.g.setPosition(a.B-=b.length);a.g.T().set(b,a.B);return yb(a)}
class Ab{constructor(){this.ua=1;this.X=null;this.kb=0;this.ta=!1;this.fb=0;this.va=[];this.jb=0;this.fa=!1;this.ac=new TextEncoder;this.g=new lb(new Uint8Array(1024));this.B=1024}clear(){this.g.clear();this.B=this.g.l.length;this.ua=1;this.X=null;this.kb=0;this.ta=!1;this.fb=0;this.va=[];this.jb=0;this.fa=!1}ja(a){this.g.ja(--this.B,a)}lb(a){this.g.lb(this.B-=2,a)}Y(a){this.g.Y(this.B-=4,a)}aa(a){this.g.aa(this.B-=8,a)}slot(a){null!==this.X&&(this.X[a]=this.offset())}offset(){return this.g.l.length-
this.B}finish(a,b,c){c=c?4:0;if(b){ob(this,this.ua,8+c);if(4!=b.length)throw new TypeError("FlatBuffers: file identifier must be length 4");for(let d=3;0<=d;d--)this.ja(b.charCodeAt(d))}ob(this,this.ua,4+c);vb(this,a);c&&qb(this,this.g.l.length-this.B);this.g.setPosition(this.B)}};var Bb,Cb=Bb||(Bb={});Cb[Cb.BUFFER=0]="BUFFER";var Eb,Fb=Eb||(Eb={});Fb[Fb.LZ4_FRAME=0]="LZ4_FRAME";Fb[Fb.ZSTD=1]="ZSTD";class Gb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}codec(){const a=A(this.g,this.h,4);return a?this.g.l[this.h+a]<<24>>24:Eb.ec}method(){const a=A(this.g,this.h,6);return a?this.g.l[this.h+a]<<24>>24:Bb.cc}};class Hb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}offset(){return fb(this.g,this.h)}length(){return fb(this.g,this.h+8)}};class Ib{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}length(){return fb(this.g,this.h)}nullCount(){return fb(this.g,this.h+8)}};function Jb(a){const b=A(a.g,a.h,6);return b?kb(a.g,a.h+b):0}function Kb(a){const b=A(a.g,a.h,8);return b?kb(a.g,a.h+b):0}class Lb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}length(){const a=A(this.g,this.h,4);return a?fb(this.g,this.h+a):BigInt("0")}M(a,b){const c=A(this.g,this.h,6);return c?(b||new Ib).i(jb(this.g,this.h+c)+16*a,this.g):null}buffers(a,b){const c=A(this.g,this.h,8);return c?(b||new Hb).i(jb(this.g,this.h+c)+16*a,this.g):null}};class Mb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}id(){const a=A(this.g,this.h,4);return a?fb(this.g,this.h+a):BigInt("0")}data(a){const b=A(this.g,this.h,6);return b?(a||new Lb).i(ib(this.g,this.h+b),this.g):null}Ja(){const a=A(this.g,this.h,8);return a?!!(this.g.l[this.h+a]<<24>>24):!1}};var Nb,Ob=Nb||(Nb={});Ob[Ob.Little=0]="Little";Ob[Ob.Big=1]="Big";var Pb,Qb=Pb||(Pb={});Qb[Qb.DenseArray=0]="DenseArray";class Rb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}bitWidth(){const a=A(this.g,this.h,4);return a?this.g.u(this.h+a):0}isSigned(){const a=A(this.g,this.h,6);return a?!!(this.g.l[this.h+a]<<24>>24):!1}};function Sb(a){const b=A(a.g,a.h,6);return b?(new Rb).i(ib(a.g,a.h+b),a.g):null}class Tb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}id(){const a=A(this.g,this.h,4);return a?fb(this.g,this.h+a):BigInt("0")}isOrdered(){const a=A(this.g,this.h,8);return a?!!(this.g.l[this.h+a]<<24>>24):!1}};class Ub{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}key(a){const b=A(this.g,this.h,4);return b?hb(this.g,this.h+b,a):null}value(a){const b=A(this.g,this.h,6);return b?hb(this.g,this.h+b,a):null}};class Vb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}unit(){const a=A(this.g,this.h,4);return a?eb(this.g,this.h+a):Xa.MILLISECOND}};class Wb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}precision(){const a=A(this.g,this.h,4);return a?this.g.u(this.h+a):0}scale(){const a=A(this.g,this.h,6);return a?this.g.u(this.h+a):0}bitWidth(){const a=A(this.g,this.h,8);return a?this.g.u(this.h+a):128}};class Xb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}unit(){const a=A(this.g,this.h,4);return a?eb(this.g,this.h+a):z.MILLISECOND}};class Yb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}byteWidth(){const a=A(this.g,this.h,4);return a?this.g.u(this.h+a):0}};class Zb{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}listSize(){const a=A(this.g,this.h,4);return a?this.g.u(this.h+a):0}};class $b{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}precision(){const a=A(this.g,this.h,4);return a?eb(this.g,this.h+a):y.HALF}};class ac{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}unit(){const a=A(this.g,this.h,4);return a?eb(this.g,this.h+a):$a.YEAR_MONTH}};class bc{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}keysSorted(){const a=A(this.g,this.h,4);return a?!!(this.g.l[this.h+a]<<24>>24):!1}};class cc{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}unit(){const a=A(this.g,this.h,4);return a?eb(this.g,this.h+a):z.MILLISECOND}bitWidth(){const a=A(this.g,this.h,6);return a?this.g.u(this.h+a):32}};class dc{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}unit(){const a=A(this.g,this.h,4);return a?eb(this.g,this.h+a):z.SECOND}timezone(a){const b=A(this.g,this.h,6);return b?hb(this.g,this.h+b,a):null}};class ec{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}mode(){const a=A(this.g,this.h,4);return a?eb(this.g,this.h+a):x.Sparse}typeIds(a){const b=A(this.g,this.h,6);return b?this.g.u(jb(this.g,this.h+b)+4*a):0}};var E,F=E||(E={});F[F.NONE=0]="NONE";F[F.Null=1]="Null";F[F.Int=2]="Int";F[F.FloatingPoint=3]="FloatingPoint";F[F.Binary=4]="Binary";F[F.Utf8=5]="Utf8";F[F.Bool=6]="Bool";F[F.Decimal=7]="Decimal";F[F.Date=8]="Date";F[F.Time=9]="Time";F[F.Timestamp=10]="Timestamp";F[F.Interval=11]="Interval";F[F.List=12]="List";F[F.Struct_=13]="Struct_";F[F.Union=14]="Union";F[F.FixedSizeBinary=15]="FixedSizeBinary";F[F.FixedSizeList=16]="FixedSizeList";F[F.Map=17]="Map";F[F.Duration=18]="Duration";
F[F.LargeBinary=19]="LargeBinary";F[F.LargeUtf8=20]="LargeUtf8";F[F.LargeList=21]="LargeList";F[F.RunEndEncoded=22]="RunEndEncoded";function fc(a,b){xb(a,4,b.length,4);for(let c=b.length-1;0<=c;c--)vb(a,b[c]);return yb(a)}function gc(a,b){xb(a,4,b.length,4);for(let c=b.length-1;0<=c;c--)vb(a,b[c]);return yb(a)}function hc(a){const b=A(a.g,a.h,14);return b?kb(a.g,a.h+b):0}
class ic{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}name(a){const b=A(this.g,this.h,4);return b?hb(this.g,this.h+b,a):null}nullable(){const a=A(this.g,this.h,6);return a?!!(this.g.l[this.h+a]<<24>>24):!1}type(a){const b=A(this.g,this.h,10);return b?gb(this.g,a,this.h+b):null}dictionary(a){const b=A(this.g,this.h,12);return b?(a||new Tb).i(ib(this.g,this.h+b),this.g):null}children(a,b){const c=A(this.g,this.h,14);return c?(b||new ic).i(ib(this.g,jb(this.g,this.h+c)+4*a),
this.g):null}Ga(a){const b=A(this.g,this.h,16);return b?(new Ub).i(ib(this.g,jb(this.g,this.h+b)+4*a),this.g):null}Ha(){const a=A(this.g,this.h,16);return a?kb(this.g,this.h+a):0}};function jc(a,b){xb(a,4,b.length,4);for(let c=b.length-1;0<=c;c--)vb(a,b[c]);return yb(a)}function kc(a,b){xb(a,4,b.length,4);for(let c=b.length-1;0<=c;c--)vb(a,b[c]);return yb(a)}function lc(a){const b=A(a.g,a.h,6);return b?kb(a.g,a.h+b):0}
class mc{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}fields(a,b){const c=A(this.g,this.h,6);return c?(b||new ic).i(ib(this.g,jb(this.g,this.h+c)+4*a),this.g):null}Ga(a){const b=A(this.g,this.h,8);return b?(new Ub).i(ib(this.g,jb(this.g,this.h+b)+4*a),this.g):null}Ha(){const a=A(this.g,this.h,8);return a?kb(this.g,this.h+a):0}features(a){const b=A(this.g,this.h,10);return b?fb(this.g,jb(this.g,this.h+b)+8*a):BigInt(0)}};var nc,oc=nc||(nc={});oc[oc.Row=0]="Row";oc[oc.Column=1]="Column";var pc,qc=pc||(pc={});qc[qc.NONE=0]="NONE";qc[qc.SparseTensorIndexCOO=1]="SparseTensorIndexCOO";qc[qc.SparseMatrixIndexCSX=2]="SparseMatrixIndexCSX";qc[qc.SparseTensorIndexCSF=3]="SparseTensorIndexCSF";var G,rc=G||(G={});rc[rc.NONE=0]="NONE";rc[rc.Schema=1]="Schema";rc[rc.DictionaryBatch=2]="DictionaryBatch";rc[rc.RecordBatch=3]="RecordBatch";rc[rc.Tensor=4]="Tensor";rc[rc.SparseTensor=5]="SparseTensor";var H,I=H||(H={});I[I.NONE=0]="NONE";I[I.Null=1]="Null";I[I.Int=2]="Int";I[I.Float=3]="Float";I[I.Binary=4]="Binary";I[I.Utf8=5]="Utf8";I[I.Bool=6]="Bool";I[I.Decimal=7]="Decimal";I[I.Date=8]="Date";I[I.Time=9]="Time";I[I.Timestamp=10]="Timestamp";I[I.Interval=11]="Interval";I[I.List=12]="List";I[I.Struct=13]="Struct";I[I.Union=14]="Union";I[I.FixedSizeBinary=15]="FixedSizeBinary";I[I.FixedSizeList=16]="FixedSizeList";I[I.Map=17]="Map";I[I.Duration=18]="Duration";I[I.LargeBinary=19]="LargeBinary";
I[I.LargeUtf8=20]="LargeUtf8";I[I.Dictionary=-1]="Dictionary";I[I.Int8=-2]="Int8";I[I.Int16=-3]="Int16";I[I.Int32=-4]="Int32";I[I.Int64=-5]="Int64";I[I.Uint8=-6]="Uint8";I[I.Uint16=-7]="Uint16";I[I.Uint32=-8]="Uint32";I[I.Uint64=-9]="Uint64";I[I.Float16=-10]="Float16";I[I.Float32=-11]="Float32";I[I.Float64=-12]="Float64";I[I.DateDay=-13]="DateDay";I[I.DateMillisecond=-14]="DateMillisecond";I[I.TimestampSecond=-15]="TimestampSecond";I[I.TimestampMillisecond=-16]="TimestampMillisecond";
I[I.TimestampMicrosecond=-17]="TimestampMicrosecond";I[I.TimestampNanosecond=-18]="TimestampNanosecond";I[I.TimeSecond=-19]="TimeSecond";I[I.TimeMillisecond=-20]="TimeMillisecond";I[I.TimeMicrosecond=-21]="TimeMicrosecond";I[I.TimeNanosecond=-22]="TimeNanosecond";I[I.DenseUnion=-23]="DenseUnion";I[I.SparseUnion=-24]="SparseUnion";I[I.IntervalDayTime=-25]="IntervalDayTime";I[I.IntervalYearMonth=-26]="IntervalYearMonth";I[I.DurationSecond=-27]="DurationSecond";I[I.DurationMillisecond=-28]="DurationMillisecond";
I[I.DurationMicrosecond=-29]="DurationMicrosecond";I[I.DurationNanosecond=-30]="DurationNanosecond";var sc,tc=sc||(sc={});tc[tc.OFFSET=0]="OFFSET";tc[tc.DATA=1]="DATA";tc[tc.VALIDITY=2]="VALIDITY";tc[tc.TYPE=3]="TYPE";function uc(a){if(null===a)return"null";if(void 0===a)return"undefined";switch(typeof a){case "number":return`${a}`;case "bigint":return`${a}`;case "string":return`"${a}"`}return"function"===typeof a[Symbol.toPrimitive]?a[Symbol.toPrimitive]("string"):ArrayBuffer.isView(a)?a instanceof BigInt64Array||a instanceof BigUint64Array?`[${[...a].map(b=>uc(b))}]`:`[${a}]`:ArrayBuffer.isView(a)?`[${a}]`:JSON.stringify(a,(b,c)=>"bigint"===typeof c?`${c}`:c)}var vc={};vc.valueToString=uc;function J(a){if("bigint"===typeof a&&(a<Number.MIN_SAFE_INTEGER||a>Number.MAX_SAFE_INTEGER))throw new TypeError(`${a} is not safe to convert to a number.`);return Number(a)}function wc(a,b){return J(a/b)+J(a%b)/J(b)};const xc=Symbol.for("isArrowBigNum");function yc(a,...b){return 0===b.length?Object.setPrototypeOf(u(this.TypedArray,a),this.constructor.prototype):Object.setPrototypeOf(new this.TypedArray(a,...b),this.constructor.prototype)}yc.prototype[xc]=!0;yc.prototype.toJSON=function(){return`"${zc(this)}"`};yc.prototype.valueOf=function(a){return Ac(this,a)};yc.prototype.toString=function(){return zc(this)};yc.prototype[Symbol.toPrimitive]=function(a="default"){switch(a){case "number":return Ac(this);case "default":return Bc(this)}return zc(this)};
function Cc(...a){return yc.apply(this,a)}function Dc(...a){return yc.apply(this,a)}function Ec(...a){return yc.apply(this,a)}Object.setPrototypeOf(Cc.prototype,Object.create(Int32Array.prototype));Object.setPrototypeOf(Dc.prototype,Object.create(Uint32Array.prototype));Object.setPrototypeOf(Ec.prototype,Object.create(Uint32Array.prototype));Object.assign(Cc.prototype,yc.prototype,{constructor:Cc,signed:!0,TypedArray:Int32Array,BigIntArray:BigInt64Array});
Object.assign(Dc.prototype,yc.prototype,{constructor:Dc,signed:!1,TypedArray:Uint32Array,BigIntArray:BigUint64Array});Object.assign(Ec.prototype,yc.prototype,{constructor:Ec,signed:!0,TypedArray:Uint32Array,BigIntArray:BigUint64Array});const Fc=BigInt(4294967296)*BigInt(4294967296)-BigInt(1);
function Ac(a,b){const {buffer:c,byteOffset:d,byteLength:e,signed:f}=a,g=new BigUint64Array(c,d,e/8),k=f&&g.at(-1)&BigInt(1)<<BigInt(63);a=BigInt(0);let m=0;if(k){for(var p of g)a|=(p^Fc)*(BigInt(1)<<BigInt(64*m++));a*=BigInt(-1);a-=BigInt(1)}else for(const r of g)a|=r*(BigInt(1)<<BigInt(64*m++));return"number"===typeof b?(b=BigInt(Math.pow(10,b)),p=a%b,J(a/b)+J(p)/J(b)):J(a)}
function zc(a){if(8===a.byteLength)return`${(new a.BigIntArray(a.buffer,a.byteOffset,1))[0]}`;if(!a.signed)return Gc(a);let b=new Uint16Array(a.buffer,a.byteOffset,a.byteLength/2);if(0<=(new Int16Array([b.at(-1)]))[0])return Gc(a);b=b.slice();a=1;for(let c=0;c<b.length;c++){const d=b[c];b[c]=~d+a;a&=0===d?1:0}return`-${Gc(b)}`}function Bc(a){return 8===a.byteLength?(new a.BigIntArray(a.buffer,a.byteOffset,1))[0]:zc(a)}
function Gc(a){let b="";const c=new Uint32Array(2);a=new Uint16Array(a.buffer,a.byteOffset,a.byteLength/2);const d=new Uint32Array((a=(new Uint16Array(a)).reverse()).buffer);let e;const f=a.length-1;do{for(c[0]=a[e=0];e<f;)a[e++]=c[1]=c[0]/10,c[0]=(c[0]-10*c[1]<<16)+a[e];a[e]=c[1]=c[0]/10;c[0]-=10*c[1];b=`${c[0]}${b}`}while(d[0]||d[1]||d[2]||d[3]);let g;return null!=(g=b)?g:"0"}
class Hc{static new(a,b){switch(b){case !0:return new Cc(a);case !1:return new Dc(a)}switch(a.constructor){case Int8Array:case Int16Array:case Int32Array:case BigInt64Array:return new Cc(a)}return 16===a.byteLength?new Ec(a):new Dc(a)}static ic(a){return new Cc(a)}constructor(a,b){return Hc.new(a,b)}}var Ic={};Ic.BN=Hc;Ic.bigNumToBigInt=Bc;Ic.bigNumToNumber=Ac;Ic.bigNumToString=zc;Ic.isArrowBigNumSymbol=xc;class K{static isNull(a){return(null==a?void 0:a.typeId)===H.Null}static isInt(a){return(null==a?void 0:a.typeId)===H.Int}static isFloat(a){return(null==a?void 0:a.typeId)===H.Float}static isBinary(a){return(null==a?void 0:a.typeId)===H.Binary}static isLargeBinary(a){return(null==a?void 0:a.typeId)===H.LargeBinary}static isUtf8(a){return(null==a?void 0:a.typeId)===H.Utf8}static isLargeUtf8(a){return(null==a?void 0:a.typeId)===H.LargeUtf8}static isBool(a){return(null==a?void 0:a.typeId)===H.Bool}static isDecimal(a){return(null==
a?void 0:a.typeId)===H.Decimal}static isDate(a){return(null==a?void 0:a.typeId)===H.Date}static isTime(a){return(null==a?void 0:a.typeId)===H.Time}static isTimestamp(a){return(null==a?void 0:a.typeId)===H.Timestamp}static isInterval(a){return(null==a?void 0:a.typeId)===H.Interval}static isDuration(a){return(null==a?void 0:a.typeId)===H.Duration}static isList(a){return(null==a?void 0:a.typeId)===H.List}static isStruct(a){return(null==a?void 0:a.typeId)===H.Struct}static isUnion(a){return(null==a?void 0:
a.typeId)===H.Union}static isFixedSizeBinary(a){return(null==a?void 0:a.typeId)===H.FixedSizeBinary}static isFixedSizeList(a){return(null==a?void 0:a.typeId)===H.FixedSizeList}static isMap(a){return(null==a?void 0:a.typeId)===H.Map}static isDictionary(a){return(null==a?void 0:a.typeId)===H.Dictionary}static isDenseUnion(a){return K.isUnion(a)&&a.mode===x.Dense}static isSparseUnion(a){return K.isUnion(a)&&a.mode===x.Sparse}constructor(a){this.typeId=a}}var Kc=Symbol.toStringTag,Lc,Mc=K.prototype;
Mc.children=null;Mc.ArrayType=Array;Mc.OffsetArrayType=Int32Array;Lc=Mc[Symbol.toStringTag]="DataType";K[Kc]=Lc;class Nc extends K{constructor(){super(H.Null)}toString(){return"Null"}}Nc[Symbol.toStringTag]=Nc.prototype[Symbol.toStringTag]="Null";
class Oc extends K{constructor(a,b){super(H.Int);this.isSigned=a;this.bitWidth=b}get ArrayType(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:return this.isSigned?Int32Array:Uint32Array;case 64:return this.isSigned?BigInt64Array:BigUint64Array}throw Error(`Unrecognized ${this[Symbol.toStringTag]} type`);}toString(){return`${this.isSigned?"I":"Ui"}nt${this.bitWidth}`}}var Pc=Symbol.toStringTag,Qc,Rc=Oc.prototype;
Rc.isSigned=null;Rc.bitWidth=null;Qc=Rc[Symbol.toStringTag]="Int";Oc[Pc]=Qc;class Sc extends Oc{constructor(){super(!0,8)}get ArrayType(){return Int8Array}}class Tc extends Oc{constructor(){super(!0,16)}get ArrayType(){return Int16Array}}class Uc extends Oc{constructor(){super(!0,32)}get ArrayType(){return Int32Array}}class Vc extends Oc{constructor(){super(!0,64)}get ArrayType(){return BigInt64Array}}class Wc extends Oc{constructor(){super(!1,8)}get ArrayType(){return Uint8Array}}
class Xc extends Oc{constructor(){super(!1,16)}get ArrayType(){return Uint16Array}}class Yc extends Oc{constructor(){super(!1,32)}get ArrayType(){return Uint32Array}}class Zc extends Oc{constructor(){super(!1,64)}get ArrayType(){return BigUint64Array}}Object.defineProperty(Sc.prototype,"ArrayType",{value:Int8Array});Object.defineProperty(Tc.prototype,"ArrayType",{value:Int16Array});Object.defineProperty(Uc.prototype,"ArrayType",{value:Int32Array});Object.defineProperty(Vc.prototype,"ArrayType",{value:BigInt64Array});
Object.defineProperty(Wc.prototype,"ArrayType",{value:Uint8Array});Object.defineProperty(Xc.prototype,"ArrayType",{value:Uint16Array});Object.defineProperty(Yc.prototype,"ArrayType",{value:Uint32Array});Object.defineProperty(Zc.prototype,"ArrayType",{value:BigUint64Array});
class $c extends K{constructor(a){super(H.Float);this.precision=a}get ArrayType(){switch(this.precision){case y.HALF:return Uint16Array;case y.SINGLE:return Float32Array;case y.DOUBLE:return Float64Array}throw Error(`Unrecognized ${this[Symbol.toStringTag]} type`);}toString(){return`Float${this.precision<<5||16}`}}var ad=Symbol.toStringTag,bd,cd=$c.prototype;cd.precision=null;bd=cd[Symbol.toStringTag]="Float";$c[ad]=bd;class dd extends $c{constructor(){super(y.HALF)}}
class ed extends $c{constructor(){super(y.SINGLE)}}class fd extends $c{constructor(){super(y.DOUBLE)}}Object.defineProperty(dd.prototype,"ArrayType",{value:Uint16Array});Object.defineProperty(ed.prototype,"ArrayType",{value:Float32Array});Object.defineProperty(fd.prototype,"ArrayType",{value:Float64Array});class gd extends K{constructor(){super(H.Binary)}toString(){return"Binary"}}var hd=Symbol.toStringTag,id,jd=gd.prototype;jd.ArrayType=Uint8Array;id=jd[Symbol.toStringTag]="Binary";gd[hd]=id;
class kd extends K{constructor(){super(H.LargeBinary)}toString(){return"LargeBinary"}}var ld=Symbol.toStringTag,md,nd=kd.prototype;nd.ArrayType=Uint8Array;nd.OffsetArrayType=BigInt64Array;md=nd[Symbol.toStringTag]="LargeBinary";kd[ld]=md;class od extends K{constructor(){super(H.Utf8)}toString(){return"Utf8"}}var pd=Symbol.toStringTag,qd,rd=od.prototype;rd.ArrayType=Uint8Array;qd=rd[Symbol.toStringTag]="Utf8";od[pd]=qd;
class sd extends K{constructor(){super(H.LargeUtf8)}toString(){return"LargeUtf8"}}var td=Symbol.toStringTag,ud,vd=sd.prototype;vd.ArrayType=Uint8Array;vd.OffsetArrayType=BigInt64Array;ud=vd[Symbol.toStringTag]="LargeUtf8";sd[td]=ud;class wd extends K{constructor(){super(H.Bool)}toString(){return"Bool"}}var xd=Symbol.toStringTag,yd,zd=wd.prototype;zd.ArrayType=Uint8Array;yd=zd[Symbol.toStringTag]="Bool";wd[xd]=yd;
class Ad extends K{constructor(a,b,c=128){super(H.Decimal);this.scale=a;this.precision=b;this.bitWidth=c}toString(){return`Decimal[${this.precision}e${0<this.scale?"+":""}${this.scale}]`}}var Bd=Symbol.toStringTag,Cd,Dd=Ad.prototype;Dd.scale=null;Dd.precision=null;Dd.ArrayType=Uint32Array;Cd=Dd[Symbol.toStringTag]="Decimal";Ad[Bd]=Cd;
class Ed extends K{constructor(a){super(H.Date);this.unit=a}toString(){return`Date${32*(this.unit+1)}<${Xa[this.unit]}>`}get ArrayType(){return this.unit===Xa.DAY?Int32Array:BigInt64Array}}var Fd=Symbol.toStringTag,Gd,Hd=Ed.prototype;Hd.unit=null;Gd=Hd[Symbol.toStringTag]="Date";Ed[Fd]=Gd;class Id extends Ed{constructor(){super(Xa.DAY)}}class Jd extends Ed{constructor(){super(Xa.MILLISECOND)}}
class Kd extends K{constructor(a,b){super(H.Time);this.unit=a;this.bitWidth=b}toString(){return`Time${this.bitWidth}<${z[this.unit]}>`}get ArrayType(){switch(this.bitWidth){case 32:return Int32Array;case 64:return BigInt64Array}throw Error(`Unrecognized ${this[Symbol.toStringTag]} type`);}}var Ld=Symbol.toStringTag,Md,Nd=Kd.prototype;Nd.unit=null;Nd.bitWidth=null;Md=Nd[Symbol.toStringTag]="Time";Kd[Ld]=Md;class Od extends Kd{constructor(){super(z.SECOND,32)}}
class Pd extends Kd{constructor(){super(z.MILLISECOND,32)}}class Qd extends Kd{constructor(){super(z.MICROSECOND,64)}}class Rd extends Kd{constructor(){super(z.NANOSECOND,64)}}class Sd extends K{constructor(a,b){super(H.Timestamp);this.unit=a;this.timezone=b}toString(){return`Timestamp<${z[this.unit]}${this.timezone?`, ${this.timezone}`:""}>`}}var Td=Symbol.toStringTag,Ud,Vd=Sd.prototype;Vd.unit=null;Vd.timezone=null;Vd.ArrayType=BigInt64Array;Ud=Vd[Symbol.toStringTag]="Timestamp";Sd[Td]=Ud;
class Wd extends Sd{constructor(a){super(z.SECOND,a)}}class Xd extends Sd{constructor(a){super(z.MILLISECOND,a)}}class Yd extends Sd{constructor(a){super(z.MICROSECOND,a)}}class Zd extends Sd{constructor(a){super(z.NANOSECOND,a)}}class $d extends K{constructor(a){super(H.Interval);this.unit=a}toString(){return`Interval<${$a[this.unit]}>`}}var ae=Symbol.toStringTag,be,ce=$d.prototype;ce.unit=null;ce.ArrayType=Int32Array;be=ce[Symbol.toStringTag]="Interval";$d[ae]=be;
class de extends $d{constructor(){super($a.DAY_TIME)}}class ee extends $d{constructor(){super($a.YEAR_MONTH)}}class fe extends K{constructor(a){super(H.Duration);this.unit=a}toString(){return`Duration<${z[this.unit]}>`}}var ge=Symbol.toStringTag,he,ie=fe.prototype;ie.unit=null;ie.ArrayType=BigInt64Array;he=ie[Symbol.toStringTag]="Duration";fe[ge]=he;class je extends fe{constructor(){super(z.SECOND)}}class ke extends fe{constructor(){super(z.MILLISECOND)}}
class le extends fe{constructor(){super(z.MICROSECOND)}}class me extends fe{constructor(){super(z.NANOSECOND)}}class ne extends K{constructor(a){super(H.List);this.children=[a]}toString(){return`List<${this.valueType}>`}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}}var oe=Symbol.toStringTag,pe,qe=ne.prototype;qe.children=null;pe=qe[Symbol.toStringTag]="List";ne[oe]=pe;
class re extends K{constructor(a){super(H.Struct);this.children=a}toString(){return`Struct<{${this.children.map(a=>`${a.name}:${a.type}`).join(", ")}}>`}}var se=Symbol.toStringTag,te,ue=re.prototype;ue.children=null;te=ue[Symbol.toStringTag]="Struct";re[se]=te;
class ve extends K{constructor(a,b,c){super(H.Union);this.mode=a;this.children=c;this.typeIds=b=Int32Array.from(b);this.typeIdToChildIndex=b.reduce((d,e,f)=>(d[e]=f)&&d||d,Object.create(null))}toString(){return`${this[Symbol.toStringTag]}<${this.children.map(a=>`${a.type}`).join(" | ")}>`}}var we=Symbol.toStringTag,xe,ye=ve.prototype;ye.mode=null;ye.typeIds=null;ye.children=null;ye.typeIdToChildIndex=null;ye.ArrayType=Int8Array;xe=ye[Symbol.toStringTag]="Union";ve[we]=xe;
class ze extends ve{constructor(a,b){super(x.Dense,a,b)}}class Ae extends ve{constructor(a,b){super(x.Sparse,a,b)}}class Be extends K{constructor(a){super(H.FixedSizeBinary);this.byteWidth=a}toString(){return`FixedSizeBinary[${this.byteWidth}]`}}var Ce=Symbol.toStringTag,De,Ee=Be.prototype;Ee.byteWidth=null;Ee.ArrayType=Uint8Array;De=Ee[Symbol.toStringTag]="FixedSizeBinary";Be[Ce]=De;
class Fe extends K{constructor(a,b){super(H.FixedSizeList);this.listSize=a;this.children=[b]}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}toString(){return`FixedSizeList[${this.listSize}]<${this.valueType}>`}}var Ge=Symbol.toStringTag,He,Ie=Fe.prototype;Ie.children=null;Ie.listSize=null;He=Ie[Symbol.toStringTag]="FixedSizeList";Fe[Ge]=He;
class Je extends K{constructor(a,b=!1){super(H.Map);this.children=[a];this.keysSorted=b;if(a){a.name="entries";let c;if(null==a?0:null==(c=a.type)?0:c.children){let d;(b=null==a?void 0:null==(d=a.type)?void 0:d.children[0])&&(b.name="key");let e;(a=null==a?void 0:null==(e=a.type)?void 0:e.children[1])&&(a.name="value")}}}get keyType(){return this.children[0].type.children[0].type}get valueType(){return this.children[0].type.children[1].type}get childType(){return this.children[0].type}toString(){return`Map<{${this.children[0].type.children.map(a=>
`${a.name}:${a.type}`).join(", ")}}>`}}var Ke=Symbol.toStringTag,Le,Me=Je.prototype;Me.children=null;Me.keysSorted=null;Le=Me[Symbol.toStringTag]="Map_";Je[Ke]=Le;const Ne=(a=>()=>++a)(-1);
class Oe extends K{constructor(a,b,c,d){super(H.Dictionary);this.indices=b;this.dictionary=a;this.isOrdered=d||!1;this.id=null==c?Ne():J(c)}get children(){return this.dictionary.children}get valueType(){return this.dictionary}get ArrayType(){return this.dictionary.ArrayType}toString(){return`Dictionary<${this.indices}, ${this.dictionary}>`}}var Pe=Symbol.toStringTag,Qe,Re=Oe.prototype;Re.id=null;Re.indices=null;Re.isOrdered=null;Re.dictionary=null;Qe=Re[Symbol.toStringTag]="Dictionary";Oe[Pe]=Qe;
function Se(a){switch(a.typeId){case H.Decimal:return a.bitWidth/32;case H.Interval:return 1+a.unit;case H.FixedSizeList:return a.listSize;case H.FixedSizeBinary:return a.byteWidth;default:return 1}};class Te{visitMany(a,...b){return a.map((c,d)=>this.visit(c,...b.map(e=>e[d])))}visit(...a){return this.getVisitFn(a[0],!1).apply(this,a)}getVisitFn(a,b=!0){return Ue(this,a,b)}getVisitFnByTypeId(a,b=!0){return Ve(this,a,b)}visitNull(){return null}visitBool(){return null}visitInt(){return null}visitFloat(){return null}visitUtf8(){return null}visitLargeUtf8(){return null}visitBinary(){return null}visitLargeBinary(){return null}visitFixedSizeBinary(){return null}visitDate(){return null}visitTimestamp(){return null}visitTime(){return null}visitDecimal(){return null}visitList(){return null}visitStruct(){return null}visitUnion(){return null}visitDictionary(){return null}visitInterval(){return null}visitDuration(){return null}visitFixedSizeList(){return null}visitMap(){return null}}
function Ue(a,b,c=!0){return"number"===typeof b?Ve(a,b,c):"string"===typeof b&&b in H?Ve(a,H[b],c):b&&b instanceof K?Ve(a,We(b),c):(null==b?0:b.type)&&b.type instanceof K?Ve(a,We(b.type),c):Ve(a,H.NONE,c)}
function Ve(a,b,c=!0){let d=null;switch(b){case H.Null:d=a.visitNull;break;case H.Bool:d=a.visitBool;break;case H.Int:d=a.visitInt;break;case H.Int8:d=a.visitInt8||a.visitInt;break;case H.Int16:d=a.visitInt16||a.visitInt;break;case H.Int32:d=a.visitInt32||a.visitInt;break;case H.Int64:d=a.visitInt64||a.visitInt;break;case H.Uint8:d=a.visitUint8||a.visitInt;break;case H.Uint16:d=a.visitUint16||a.visitInt;break;case H.Uint32:d=a.visitUint32||a.visitInt;break;case H.Uint64:d=a.visitUint64||a.visitInt;
break;case H.Float:d=a.visitFloat;break;case H.Float16:d=a.visitFloat16||a.visitFloat;break;case H.Float32:d=a.visitFloat32||a.visitFloat;break;case H.Float64:d=a.visitFloat64||a.visitFloat;break;case H.Utf8:d=a.visitUtf8;break;case H.LargeUtf8:d=a.visitLargeUtf8;break;case H.Binary:d=a.visitBinary;break;case H.LargeBinary:d=a.visitLargeBinary;break;case H.FixedSizeBinary:d=a.visitFixedSizeBinary;break;case H.Date:d=a.visitDate;break;case H.DateDay:d=a.visitDateDay||a.visitDate;break;case H.DateMillisecond:d=
a.visitDateMillisecond||a.visitDate;break;case H.Timestamp:d=a.visitTimestamp;break;case H.TimestampSecond:d=a.visitTimestampSecond||a.visitTimestamp;break;case H.TimestampMillisecond:d=a.visitTimestampMillisecond||a.visitTimestamp;break;case H.TimestampMicrosecond:d=a.visitTimestampMicrosecond||a.visitTimestamp;break;case H.TimestampNanosecond:d=a.visitTimestampNanosecond||a.visitTimestamp;break;case H.Time:d=a.visitTime;break;case H.TimeSecond:d=a.visitTimeSecond||a.visitTime;break;case H.TimeMillisecond:d=
a.visitTimeMillisecond||a.visitTime;break;case H.TimeMicrosecond:d=a.visitTimeMicrosecond||a.visitTime;break;case H.TimeNanosecond:d=a.visitTimeNanosecond||a.visitTime;break;case H.Decimal:d=a.visitDecimal;break;case H.List:d=a.visitList;break;case H.Struct:d=a.visitStruct;break;case H.Union:d=a.visitUnion;break;case H.DenseUnion:d=a.visitDenseUnion||a.visitUnion;break;case H.SparseUnion:d=a.visitSparseUnion||a.visitUnion;break;case H.Dictionary:d=a.visitDictionary;break;case H.Interval:d=a.visitInterval;
break;case H.IntervalDayTime:d=a.visitIntervalDayTime||a.visitInterval;break;case H.IntervalYearMonth:d=a.visitIntervalYearMonth||a.visitInterval;break;case H.Duration:d=a.visitDuration;break;case H.DurationSecond:d=a.visitDurationSecond||a.visitDuration;break;case H.DurationMillisecond:d=a.visitDurationMillisecond||a.visitDuration;break;case H.DurationMicrosecond:d=a.visitDurationMicrosecond||a.visitDuration;break;case H.DurationNanosecond:d=a.visitDurationNanosecond||a.visitDuration;break;case H.FixedSizeList:d=
a.visitFixedSizeList;break;case H.Map:d=a.visitMap}if("function"===typeof d)return d;if(!c)return()=>null;throw Error(`Unrecognized type '${H[b]}'`);}
function We(a){switch(a.typeId){case H.Null:return H.Null;case H.Int:const b=a.isSigned;switch(a.bitWidth){case 8:return b?H.Int8:H.Uint8;case 16:return b?H.Int16:H.Uint16;case 32:return b?H.Int32:H.Uint32;case 64:return b?H.Int64:H.Uint64}return H.Int;case H.Float:switch(a.precision){case y.HALF:return H.Float16;case y.SINGLE:return H.Float32;case y.DOUBLE:return H.Float64}return H.Float;case H.Binary:return H.Binary;case H.LargeBinary:return H.LargeBinary;case H.Utf8:return H.Utf8;case H.LargeUtf8:return H.LargeUtf8;
case H.Bool:return H.Bool;case H.Decimal:return H.Decimal;case H.Time:switch(a.unit){case z.SECOND:return H.TimeSecond;case z.MILLISECOND:return H.TimeMillisecond;case z.MICROSECOND:return H.TimeMicrosecond;case z.NANOSECOND:return H.TimeNanosecond}return H.Time;case H.Timestamp:switch(a.unit){case z.SECOND:return H.TimestampSecond;case z.MILLISECOND:return H.TimestampMillisecond;case z.MICROSECOND:return H.TimestampMicrosecond;case z.NANOSECOND:return H.TimestampNanosecond}return H.Timestamp;case H.Date:switch(a.unit){case Xa.DAY:return H.DateDay;
case Xa.MILLISECOND:return H.DateMillisecond}return H.Date;case H.Interval:switch(a.unit){case $a.DAY_TIME:return H.IntervalDayTime;case $a.YEAR_MONTH:return H.IntervalYearMonth}return H.Interval;case H.Duration:switch(a.unit){case z.SECOND:return H.DurationSecond;case z.MILLISECOND:return H.DurationMillisecond;case z.MICROSECOND:return H.DurationMicrosecond;case z.NANOSECOND:return H.DurationNanosecond}return H.Duration;case H.Map:return H.Map;case H.List:return H.List;case H.Struct:return H.Struct;
case H.Union:switch(a.mode){case x.Dense:return H.DenseUnion;case x.Sparse:return H.SparseUnion}return H.Union;case H.FixedSizeBinary:return H.FixedSizeBinary;case H.FixedSizeList:return H.FixedSizeList;case H.Dictionary:return H.Dictionary}throw Error(`Unrecognized type '${H[a.typeId]}'`);}h=Te.prototype;h.visitInt8=null;h.visitInt16=null;h.visitInt32=null;h.visitInt64=null;h.visitUint8=null;h.visitUint16=null;h.visitUint32=null;h.visitUint64=null;h.visitFloat16=null;h.visitFloat32=null;
h.visitFloat64=null;h.visitDateDay=null;h.visitDateMillisecond=null;h.visitTimestampSecond=null;h.visitTimestampMillisecond=null;h.visitTimestampMicrosecond=null;h.visitTimestampNanosecond=null;h.visitTimeSecond=null;h.visitTimeMillisecond=null;h.visitTimeMicrosecond=null;h.visitTimeNanosecond=null;h.visitDenseUnion=null;h.visitSparseUnion=null;h.visitIntervalDayTime=null;h.visitIntervalYearMonth=null;h.visitDuration=null;h.visitDurationSecond=null;h.visitDurationMillisecond=null;
h.visitDurationMicrosecond=null;h.visitDurationNanosecond=null;const Xe=new Float64Array(1),Ye=new Uint32Array(Xe.buffer);function Ze(a){const b=(a&31744)>>10,c=(a&1023)/1024;a=Math.pow(-1,(a&32768)>>15);switch(b){case 31:return a*(c?Number.NaN:1/0);case 0:return a*(c?6.103515625E-5*c:0)}return a*Math.pow(2,b-15)*(1+c)}
function $e(a){if(a!==a)return 32256;Xe[0]=a;a=(Ye[1]&2147483648)>>16&65535;let b=Ye[1]&2146435072,c=0;1089470464<=b?0<Ye[0]?b=31744:(b=(b&2080374784)>>16,c=(Ye[1]&1048575)>>10):1056964608>=b?(c=1048576+(Ye[1]&1048575),c=1048576+(c<<(b>>20)-998)>>21,b=0):(b=b-1056964608>>10,c=(Ye[1]&1048575)+512>>10);return a|b|c&65535}var af={};af.float64ToUint16=$e;af.uint16ToFloat64=Ze;class bf extends Te{}function L(a){return(b,c,d)=>{if(b.setValid(c,null!=d))return a(b,c,d)}}
const cf=(a,b,c,d)=>{if(c+1<b.length){const e=J(b[c]);b=J(b[c+1]);a.set(d.subarray(0,b-e),e)}},df=({values:a},b,c)=>{a[b]=c},ef=({values:a},b,c)=>{a[b]=c},ff=({values:a},b,c)=>{a[b]=$e(c)},gf=({values:a},b,c)=>{a[b]=Math.floor(c.valueOf()/864E5)},hf=({values:a},b,c)=>{a[b]=BigInt(c)},jf=({stride:a,values:b},c,d)=>{b.set(d.subarray(0,a),a*c)},kf=({values:a,valueOffsets:b},c,d)=>cf(a,b,c,d),lf=({values:a,valueOffsets:b},c,d)=>cf(a,b,c,xa.encode(d)),mf=(a,b,c)=>{a.type.unit===Xa.DAY?gf(a,b,c):hf(a,b,
c)},nf=({values:a},b,c)=>{a[b]=BigInt(c/1E3)},of=({values:a},b,c)=>{a[b]=BigInt(c)},pf=({values:a},b,c)=>{a[b]=BigInt(1E3*c)},qf=({values:a},b,c)=>{a[b]=BigInt(1E6*c)},rf=(a,b,c)=>{switch(a.type.unit){case z.SECOND:return nf(a,b,c);case z.MILLISECOND:return of(a,b,c);case z.MICROSECOND:return pf(a,b,c);case z.NANOSECOND:return qf(a,b,c)}},sf=({values:a},b,c)=>{a[b]=c},tf=({values:a},b,c)=>{a[b]=c},uf=({values:a},b,c)=>{a[b]=c},vf=({values:a},b,c)=>{a[b]=c},wf=(a,b,c)=>{switch(a.type.unit){case z.SECOND:return sf(a,
b,c);case z.MILLISECOND:return tf(a,b,c);case z.MICROSECOND:return uf(a,b,c);case z.NANOSECOND:return vf(a,b,c)}},xf=({values:a,stride:b},c,d)=>{a.set(d.subarray(0,b),b*c)},yf=(a,b)=>(c,d,e,f)=>d&&c(d,a,b[f]),zf=(a,b)=>(c,d,e,f)=>d&&c(d,a,b.get(f)),Af=(a,b)=>(c,d,e)=>d&&c(d,a,b.get(e.name)),Bf=(a,b)=>(c,d,e)=>d&&c(d,a,b[e.name]),Df=(a,b,c)=>{Cf.visit(a.children[a.type.typeIdToChildIndex[a.typeIds[b]]],a.valueOffsets[b],c)},Ef=(a,b,c)=>{Cf.visit(a.children[a.type.typeIdToChildIndex[a.typeIds[b]]],
b,c)},Hf=(a,b,c)=>{a.type.unit===$a.DAY_TIME?Ff(a,b,c):Gf(a,b,c)},Ff=({values:a},b,c)=>{a.set(c.subarray(0,2),2*b)},Gf=({values:a},b,c)=>{a[b]=12*c[0]+c[1]%12},If=({values:a},b,c)=>{a[b]=c},Jf=({values:a},b,c)=>{a[b]=c},Kf=({values:a},b,c)=>{a[b]=c},Lf=({values:a},b,c)=>{a[b]=c},Mf=(a,b,c)=>{switch(a.type.unit){case z.SECOND:return If(a,b,c);case z.MILLISECOND:return Jf(a,b,c);case z.MICROSECOND:return Kf(a,b,c);case z.NANOSECOND:return Lf(a,b,c)}};h=bf.prototype;
h.visitBool=L(({offset:a,values:b},c,d)=>{a+=c;d?b[a>>3]|=1<<a%8:b[a>>3]&=~(1<<a%8)});h.visitInt=L(df);h.visitInt8=L(df);h.visitInt16=L(df);h.visitInt32=L(df);h.visitInt64=L(df);h.visitUint8=L(df);h.visitUint16=L(df);h.visitUint32=L(df);h.visitUint64=L(df);h.visitFloat=L((a,b,c)=>{switch(a.type.precision){case y.HALF:return ff(a,b,c);case y.SINGLE:case y.DOUBLE:return ef(a,b,c)}});h.visitFloat16=L(ff);h.visitFloat32=L(ef);h.visitFloat64=L(ef);h.visitUtf8=L(lf);h.visitLargeUtf8=L(lf);
h.visitBinary=L(kf);h.visitLargeBinary=L(kf);h.visitFixedSizeBinary=L(jf);h.visitDate=L(mf);h.visitDateDay=L(gf);h.visitDateMillisecond=L(hf);h.visitTimestamp=L(rf);h.visitTimestampSecond=L(nf);h.visitTimestampMillisecond=L(of);h.visitTimestampMicrosecond=L(pf);h.visitTimestampNanosecond=L(qf);h.visitTime=L(wf);h.visitTimeSecond=L(sf);h.visitTimeMillisecond=L(tf);h.visitTimeMicrosecond=L(uf);h.visitTimeNanosecond=L(vf);h.visitDecimal=L(xf);
h.visitList=L((a,b,c)=>{const d=a.children[0];a=a.valueOffsets;const e=Cf.getVisitFn(d);if(Array.isArray(c))for(let f=-1,g=a[b],k=a[b+1];g<k;)e(d,g++,c[++f]);else for(let f=-1,g=a[b],k=a[b+1];g<k;)e(d,g++,c.get(++f))});h.visitStruct=L((a,b,c)=>{const d=a.type.children.map(f=>Cf.getVisitFn(f.type)),e=c instanceof Map?Af(b,c):c instanceof M?zf(b,c):Array.isArray(c)?yf(b,c):Bf(b,c);a.type.children.forEach((f,g)=>e(d[g],a.children[g],f,g))});
h.visitUnion=L((a,b,c)=>{a.type.mode===x.Dense?Df(a,b,c):Ef(a,b,c)});h.visitDenseUnion=L(Df);h.visitSparseUnion=L(Ef);h.visitDictionary=L((a,b,c)=>{let d;null==(d=a.dictionary)||d.set(a.values[b],c)});h.visitInterval=L(Hf);h.visitIntervalDayTime=L(Ff);h.visitIntervalYearMonth=L(Gf);h.visitDuration=L(Mf);h.visitDurationSecond=L(If);h.visitDurationMillisecond=L(Jf);h.visitDurationMicrosecond=L(Kf);h.visitDurationNanosecond=L(Lf);
h.visitFixedSizeList=L((a,b,c)=>{const d=a.stride;a=a.children[0];const e=Cf.getVisitFn(a);if(Array.isArray(c))for(let f=-1,g=b*d;++f<d;)e(a,g+f,c[f]);else for(let f=-1,g=b*d;++f<d;)e(a,g+f,c.get(f))});h.visitMap=L((a,b,c)=>{const d=a.children[0],e=a.valueOffsets;a=Cf.getVisitFn(d);let {[b]:f,[b+1]:g}=e;b=c instanceof Map?c.entries():Object.entries(c);for(const k of b)if(a(d,f,k),++f>=g)break});const Cf=new bf;const Nf=Symbol.for("parent"),Of=Symbol.for("rowIndex");
class Pf{constructor(a,b){this[Nf]=a;this[Of]=b;return new Proxy(this,Qf)}toArray(){return Object.values(this.toJSON())}toJSON(){const a=this[Of],b=this[Nf],c=b.type.children,d={};for(let e=-1,f=c.length;++e<f;)d[c[e].name]=Rf.visit(b.children[e],a);return d}toString(){return`{${[...this].map(([a,b])=>`${uc(a)}: ${uc(b)}`).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}[Symbol.iterator](){return new Sf(this[Nf],this[Of])}}
class Sf{constructor(a,b){this.zb=0;this.children=a.children;this.rowIndex=b;this.yb=a.type.children;this.numChildren=this.yb.length}[Symbol.iterator](){return this}next(){const a=this.zb;return a<this.numChildren?(this.zb=a+1,{done:!1,value:[this.yb[a].name,Rf.visit(this.children[a],this.rowIndex)]}):{done:!0,value:null}}}
Object.defineProperties(Pf.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[Nf]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Of]:{writable:!0,enumerable:!1,configurable:!1,value:-1}});
class Tf{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(a){return a[Nf].type.children.map(b=>b.name)}has(a,b){return a[Nf].type.children.some(c=>c.name===b)}getOwnPropertyDescriptor(a,b){if(a[Nf].type.children.some(c=>c.name===b))return{writable:!0,enumerable:!0,configurable:!0}}get(a,b){if(Reflect.has(a,b))return a[b];var c=a[Nf].type.children.findIndex(d=>d.name===b);if(-1!==c)return c=Rf.visit(a[Nf].children[c],a[Of]),Reflect.set(a,b,c),c}set(a,b,c){const d=
a[Nf].type.children.findIndex(e=>e.name===b);return-1!==d?(Cf.visit(a[Nf].children[d],a[Of],c),Reflect.set(a,b,c)):Reflect.has(a,b)||"symbol"===typeof b?Reflect.set(a,b,c):!1}}const Qf=new Tf;class Uf extends Te{}function N(a){return(b,c)=>b.getValid(c)?a(b,c):null}
const Vf=(a,b,c)=>{if(c+1>=b.length)return null;const d=J(b[c]);b=J(b[c+1]);return a.subarray(d,b)},Wf=({values:a},b)=>864E5*a[b],Xf=({values:a},b)=>J(a[b]),Yf=({stride:a,values:b},c)=>b[a*c],Zf=({values:a},b)=>a[b],$f=({values:a,valueOffsets:b},c)=>Vf(a,b,c),ag=({values:a,valueOffsets:b},c)=>{a=Vf(a,b,c);return null!==a?va.decode(a):null},bg=({values:a},b)=>1E3*J(a[b]),cg=({values:a},b)=>J(a[b]),dg=({values:a},b)=>wc(a[b],BigInt(1E3)),eg=({values:a},b)=>wc(a[b],BigInt(1E6)),fg=({values:a},b)=>a[b],
gg=({values:a},b)=>a[b],hg=({values:a},b)=>a[b],ig=({values:a},b)=>a[b],jg=(a,b)=>Rf.visit(a.children[a.type.typeIdToChildIndex[a.typeIds[b]]],a.valueOffsets[b]),kg=(a,b)=>Rf.visit(a.children[a.type.typeIdToChildIndex[a.typeIds[b]]],b),lg=({values:a},b)=>a.subarray(2*b,2*(b+1)),mg=({values:a},b)=>{a=a[b];b=new Int32Array(2);b[0]=Math.trunc(a/12);b[1]=Math.trunc(a%12);return b},ng=({values:a},b)=>a[b],og=({values:a},b)=>a[b],pg=({values:a},b)=>a[b],qg=({values:a},b)=>a[b];h=Uf.prototype;
h.visitNull=N(()=>null);h.visitBool=N(({offset:a,values:b},c)=>{a+=c;return 0!==(b[a>>3]&1<<a%8)});h.visitInt=N(({values:a},b)=>a[b]);h.visitInt8=N(Yf);h.visitInt16=N(Yf);h.visitInt32=N(Yf);h.visitInt64=N(Zf);h.visitUint8=N(Yf);h.visitUint16=N(Yf);h.visitUint32=N(Yf);h.visitUint64=N(Zf);h.visitFloat=N(({type:a,values:b},c)=>a.precision!==y.HALF?b[c]:Ze(b[c]));h.visitFloat16=N(({stride:a,values:b},c)=>Ze(b[a*c]));h.visitFloat32=N(Yf);h.visitFloat64=N(Yf);h.visitUtf8=N(ag);h.visitLargeUtf8=N(ag);
h.visitBinary=N($f);h.visitLargeBinary=N($f);h.visitFixedSizeBinary=N(({stride:a,values:b},c)=>b.subarray(a*c,a*(c+1)));h.visitDate=N((a,b)=>a.type.unit===Xa.DAY?Wf(a,b):Xf(a,b));h.visitDateDay=N(Wf);h.visitDateMillisecond=N(Xf);h.visitTimestamp=N((a,b)=>{switch(a.type.unit){case z.SECOND:return bg(a,b);case z.MILLISECOND:return cg(a,b);case z.MICROSECOND:return dg(a,b);case z.NANOSECOND:return eg(a,b)}});h.visitTimestampSecond=N(bg);h.visitTimestampMillisecond=N(cg);h.visitTimestampMicrosecond=N(dg);
h.visitTimestampNanosecond=N(eg);h.visitTime=N((a,b)=>{switch(a.type.unit){case z.SECOND:return fg(a,b);case z.MILLISECOND:return gg(a,b);case z.MICROSECOND:return hg(a,b);case z.NANOSECOND:return ig(a,b)}});h.visitTimeSecond=N(fg);h.visitTimeMillisecond=N(gg);h.visitTimeMicrosecond=N(hg);h.visitTimeNanosecond=N(ig);h.visitDecimal=N(({values:a,stride:b},c)=>new Ec(a.subarray(b*c,b*(c+1))));h.visitList=N((a,b)=>{const c=a.stride,d=a.children,{[b*c]:e,[b*c+1]:f}=a.valueOffsets;a=d[0].slice(e,f-e);return new M([a])});
h.visitStruct=N((a,b)=>new Pf(a,b));h.visitUnion=N((a,b)=>a.type.mode===x.Dense?jg(a,b):kg(a,b));h.visitDenseUnion=N(jg);h.visitSparseUnion=N(kg);h.visitDictionary=N((a,b)=>{let c;return null==(c=a.dictionary)?void 0:c.get(a.values[b])});h.visitInterval=N((a,b)=>a.type.unit===$a.DAY_TIME?lg(a,b):mg(a,b));h.visitIntervalDayTime=N(lg);h.visitIntervalYearMonth=N(mg);
h.visitDuration=N((a,b)=>{switch(a.type.unit){case z.SECOND:return ng(a,b);case z.MILLISECOND:return og(a,b);case z.MICROSECOND:return pg(a,b);case z.NANOSECOND:return qg(a,b)}});h.visitDurationSecond=N(ng);h.visitDurationMillisecond=N(og);h.visitDurationMicrosecond=N(pg);h.visitDurationNanosecond=N(qg);h.visitFixedSizeList=N((a,b)=>{const c=a.stride;a=a.children[0].slice(b*c,c);return new M([a])});
h.visitMap=N((a,b)=>{const c=a.children,{[b]:d,[b+1]:e}=a.valueOffsets;return new rg(c[0].slice(d,e-d))});const Rf=new Uf;const sg=Symbol.for("keys"),tg=Symbol.for("vals"),ug=Symbol.for("kKeysAsStrings"),vg=Symbol.for("_kKeysAsStrings");
class rg{constructor(a){this[sg]=(new M([a.children[0]])).memoize();this[tg]=a.children[1];return new Proxy(this,new wg)}get [ug](){return this[vg]||(this[vg]=Array.from(this[sg].toArray(),String))}[Symbol.iterator](){return new xg(this[sg],this[tg])}get size(){return this[sg].length}toArray(){return Object.values(this.toJSON())}toJSON(){const a=this[sg],b=this[tg],c={};for(let d=-1,e=a.length;++d<e;)c[a.get(d)]=Rf.visit(b,d);return c}toString(){return`{${[...this].map(([a,b])=>`${uc(a)}: ${uc(b)}`).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}}
class xg{constructor(a,b){this.keys=a;this.bc=b;this.Db=0;this.Zb=a.length}[Symbol.iterator](){return this}next(){const a=this.Db;if(a===this.Zb)return{done:!0,value:null};this.Db++;return{done:!1,value:[this.keys.get(a),Rf.visit(this.bc,a)]}}}
class wg{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(a){return a[ug]}has(a,b){return a[ug].includes(b)}getOwnPropertyDescriptor(a,b){if(-1!==a[ug].indexOf(b))return{writable:!0,enumerable:!0,configurable:!0}}get(a,b){if(Reflect.has(a,b))return a[b];var c=a[ug].indexOf(b);if(-1!==c)return c=Rf.visit(Reflect.get(a,tg),c),Reflect.set(a,b,c),c}set(a,b,c){const d=a[ug].indexOf(b);return-1!==d?(Cf.visit(Reflect.get(a,tg),d,c),Reflect.set(a,b,c)):Reflect.has(a,
b)?Reflect.set(a,b,c):!1}}Object.defineProperties(rg.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[sg]:{writable:!0,enumerable:!1,configurable:!1,value:null},[tg]:{writable:!0,enumerable:!1,configurable:!1,value:null},[vg]:{writable:!0,enumerable:!1,configurable:!1,value:null}});let yg;function zg(a,b,c,d){const {length:e=0}=a;b="number"!==typeof b?0:b;c="number"!==typeof c?e:c;0>b&&(b=(b%e+e)%e);0>c&&(c=(c%e+e)%e);c<b&&(yg=b,b=c,c=yg);c>e&&(c=e);return d?d(a,b,c):[b,c]}const Ag=a=>a!==a;function Bg(a){if("object"!==typeof a||null===a)return a!==a?Ag:b=>b===a;if(a instanceof Date){const b=a.valueOf();return c=>c instanceof Date?c.valueOf()===b:!1}return ArrayBuffer.isView(a)?b=>b?Ka(a,b):!1:a instanceof Map?Cg(a):Array.isArray(a)?Dg(a):a instanceof M?Eg(a):Fg(a)}
function Dg(a){const b=[];for(let c=-1,d=a.length;++c<d;)b[c]=Bg(a[c]);return Gg(b)}function Cg(a){let b=-1;const c=[];for(const d of a.values())c[++b]=Bg(d);return Gg(c)}function Eg(a){const b=[];for(let c=-1,d=a.length;++c<d;)b[c]=Bg(a.get(c));return Gg(b)}function Fg(a){const b=Object.keys(a),c=[];for(let d=-1,e=b.length;++d<e;)c[d]=Bg(a[b[d]]);return Gg(c,b)}
function Gg(a,b){return c=>{if(!c||"object"!==typeof c)return!1;switch(c.constructor){case Array:a:{var d=a.length;if(c.length!==d)c=!1;else{for(var e=-1;++e<d;)if(!a[e](c[e])){c=!1;break a}c=!0}}return c;case Map:return Hg(a,c,c.keys());case rg:case Pf:case Object:case void 0:return Hg(a,c,b||Object.keys(c))}if(c instanceof M)a:if(d=a.length,c.length!==d)c=!1;else{for(e=-1;++e<d;)if(!a[e](c.get(e))){c=!1;break a}c=!0}else c=!1;return c}}
function Hg(a,b,c){c=c[Symbol.iterator]();const d=b instanceof Map?b.keys():Object.keys(b)[Symbol.iterator]();b=b instanceof Map?b.values():Object.values(b)[Symbol.iterator]();let e=0;const f=a.length;let g=b.next(),k=c.next(),m=d.next();for(;e<f&&!k.done&&!m.done&&!g.done&&k.value===m.value&&a[e](g.value);++e,k=c.next(),m=d.next(),g=b.next());if(e===f&&k.done&&m.done&&g.done)return!0;c.return&&c.return();d.return&&d.return();b.return&&b.return();return!1}var Ig={};Ig.clampRange=zg;
Ig.createElementComparator=Bg;Ig.wrapIndex=(a,b)=>0>a?b+a:a;function Jg(a,b,c,d){return 0!==(c&1<<d)}function Kg(a,b,c,d){return(c&1<<d)>>d}function Lg(a,b,c){const d=c.byteLength+7&-8;if(0<a||c.byteLength<d){const e=new Uint8Array(d);e.set(0===a%8?c.subarray(a>>3):Mg(new Ng(c,a,b,null,Jg)).subarray(0,d));return e}return c}function Mg(a){const b=[];let c=0,d=0,e=0;for(const f of a)f&&(e|=1<<d),8===++d&&(b[c++]=e,e=d=0);if(0===c||0<d)b[c++]=e;a=new Uint8Array(b.length+7&-8);a.set(b);return a}
class Ng{constructor(a,b,c,d,e){this.T=a;this.length=c;this.context=d;this.get=e;this.Za=b%8;this.xb=b>>3;this.wb=a[this.xb++];this.index=0}next(){return this.index<this.length?(8===this.Za&&(this.Za=0,this.wb=this.T[this.xb++]),{value:this.get(this.context,this.index++,this.wb,this.Za++)}):{done:!0,value:null}}[Symbol.iterator](){return this}}
function Og(a,b,c){if(0>=c-b)return 0;if(8>c-b){var d=0;for(var e of new Ng(a,b,c-b,a,Kg))d+=e;return d}d=c>>3<<3;e=b+(0===b%8?0:8-b%8);return Og(a,b,e)+Og(a,d,c)+Pg(a,e>>3,d-e>>3)}function Pg(a,b,c){let d=0;b=Math.trunc(b);const e=new DataView(a.buffer,a.byteOffset,a.byteLength);for(a=void 0===c?a.byteLength:b+c;4<=a-b;)d+=Qg(e.getUint32(b)),b+=4;for(;2<=a-b;)d+=Qg(e.getUint16(b)),b+=2;for(;1<=a-b;)d+=Qg(e.getUint8(b)),b+=1;return d}
function Qg(a){a=Math.trunc(a);a-=a>>>1&1431655765;a=(a&858993459)+(a>>>2&858993459);return 16843009*(a+(a>>>4)&252645135)>>>24}var Rg={};Rg.BitIterator=Ng;Rg.getBit=Kg;Rg.getBool=Jg;Rg.packBools=Mg;Rg.popcnt_array=Pg;Rg.popcnt_bit_range=Og;Rg.popcnt_uint32=Qg;Rg.setBool=function(a,b,c){return c?!!(a[b>>3]|=1<<b%8)||!0:!(a[b>>3]&=~(1<<b%8))&&!1};Rg.truncateBitmap=Lg;function Sg(a,b,c){return a.map(d=>d.slice(b,c))}function Tg(a,b){if(a.typeId===H.Null)return a.clone(a.type,0,b,0);const c=a.length,d=a.nullCount,e=(new Uint8Array((b+63&-64)>>3)).fill(255,0,c>>3);e[c>>3]=(1<<c-(c&-8))-1;0<d&&e.set(Lg(a.offset,c,a.nullBitmap),0);const f=a.buffers;f[sc.VALIDITY]=e;return a.clone(a.type,0,b,d+(b-c),f)}
class O{get typeId(){return this.type.typeId}get ArrayType(){return this.type.ArrayType}get buffers(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}get nullable(){if(0!==this.O){const a=this.type;return K.isSparseUnion(a)?this.children.some(b=>b.nullable):K.isDenseUnion(a)?this.children.some(b=>b.nullable):this.nullBitmap&&0<this.nullBitmap.byteLength}return!0}get byteLength(){let a=0;const b=this.valueOffsets,c=this.values,d=this.nullBitmap,e=this.typeIds;b&&(a+=b.byteLength);
c&&(a+=c.byteLength);d&&(a+=d.byteLength);e&&(a+=e.byteLength);return this.children.reduce((f,g)=>f+g.byteLength,a)}get nullCount(){if(K.isUnion(this.type))return this.children.reduce((c,d)=>c+d.nullCount,0);let a=this.O,b;-1>=a&&(b=this.nullBitmap)&&(this.O=a=0===b.length?0:this.length-Og(b,this.offset,this.offset+this.length));return a}constructor(a,b,c,d,e,f=[],g){this.type=a;this.children=f;this.dictionary=g;this.offset=Math.floor(Math.max(b||0,0));this.length=Math.floor(Math.max(c||0,0));this.O=
Math.floor(Math.max(d||0,-1));let k;e instanceof O?(this.stride=e.stride,this.values=e.values,this.typeIds=e.typeIds,this.nullBitmap=e.nullBitmap,this.valueOffsets=e.valueOffsets):(this.stride=Se(a),e&&((k=e[0])&&(this.valueOffsets=k),(k=e[1])&&(this.values=k),(k=e[2])&&(this.nullBitmap=k),(k=e[3])&&(this.typeIds=k)))}getValid(a){const b=this.type;return K.isUnion(b)?this.children[b.typeIdToChildIndex[this.typeIds[a]]].getValid(b.mode===x.Dense?this.valueOffsets[a]:a):this.nullable&&0<this.nullCount?
(a=this.offset+a,0!==(this.nullBitmap[a>>3]&1<<a%8)):!0}setValid(a,b){var c=this.type;if(K.isUnion(c)){var d=this.children[c.typeIdToChildIndex[this.typeIds[a]]];a=c.mode===x.Dense?this.valueOffsets[a]:a;c=d.getValid(a);d.setValid(a,b)}else{({nullBitmap:d}=this);c=this.offset;var e=this.length,f=c+a;a=1<<f%8;f>>=3;if(!d||d.byteLength<=f)d=(new Uint8Array((c+e+63&-64)>>3)).fill(255),0<this.nullCount?(d.set(Lg(c,e,this.nullBitmap),0),Object.assign(this,{nullBitmap:d})):Object.assign(this,{nullBitmap:d,
O:0});e=d[f];c=0!==(e&a);d[f]=b?e|a:e&~a}c!==!!b&&(this.O=this.nullCount+(b?-1:1));return b}clone(a=this.type,b=this.offset,c=this.length,d=this.O,e=this,f=this.children){return new O(a,b,c,d,e,f,this.dictionary)}slice(a,b){const c=this.stride,d=this.typeId,e=this.children,f=+(0===this.O)-1,g=16===d?c:1;let k;const m=this.buffers;(k=m[sc.TYPE])&&(m[sc.TYPE]=k.subarray(a,a+b));(k=m[sc.OFFSET])&&(m[sc.OFFSET]=k.subarray(a,a+b+1))||(k=m[sc.DATA])&&(m[sc.DATA]=6===d?k:k.subarray(c*a,c*(a+b)));return this.clone(this.type,
this.offset+a,b,f,m,0===e.length||this.valueOffsets?e:Sg(e,g*a,g*b))}}O.prototype.children=Object.freeze([]);
class Ug extends Te{visit(a){return this.getVisitFn(a.type).call(this,a)}visitNull(a){const {type:b,offset:c=0,length:d=0}=a;return new O(b,c,d,d)}visitBool(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.nullBitmap),e=u(b.ArrayType,a.data),{length:f=e.length>>3,nullCount:g=a.nullBitmap?-1:0}=a;return new O(b,c,f,g,[void 0,e,d])}visitInt(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.nullBitmap),e=u(b.ArrayType,a.data),{length:f=e.length,nullCount:g=a.nullBitmap?-1:0}=a;return new O(b,c,f,g,[void 0,
e,d])}visitFloat(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.nullBitmap),e=u(b.ArrayType,a.data),{length:f=e.length,nullCount:g=a.nullBitmap?-1:0}=a;return new O(b,c,f,g,[void 0,e,d])}visitUtf8(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.data),e=u(Uint8Array,a.nullBitmap),f=u(Int32Array,a.valueOffsets),{length:g=f.length-1,nullCount:k=a.nullBitmap?-1:0}=a;return new O(b,c,g,k,[f,d,e])}visitLargeUtf8(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.data),e=u(Uint8Array,a.nullBitmap),f=u(BigInt64Array,
a.valueOffsets),{length:g=f.length-1,nullCount:k=a.nullBitmap?-1:0}=a;return new O(b,c,g,k,[f,d,e])}visitBinary(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.data),e=u(Uint8Array,a.nullBitmap),f=u(Int32Array,a.valueOffsets),{length:g=f.length-1,nullCount:k=a.nullBitmap?-1:0}=a;return new O(b,c,g,k,[f,d,e])}visitLargeBinary(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.data),e=u(Uint8Array,a.nullBitmap),f=u(BigInt64Array,a.valueOffsets),{length:g=f.length-1,nullCount:k=a.nullBitmap?-1:0}=a;return new O(b,
c,g,k,[f,d,e])}visitFixedSizeBinary(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.nullBitmap),e=u(b.ArrayType,a.data),{length:f=e.length/Se(b),nullCount:g=a.nullBitmap?-1:0}=a;return new O(b,c,f,g,[void 0,e,d])}visitDate(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.nullBitmap),e=u(b.ArrayType,a.data),{length:f=e.length/Se(b),nullCount:g=a.nullBitmap?-1:0}=a;return new O(b,c,f,g,[void 0,e,d])}visitTimestamp(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.nullBitmap),e=u(b.ArrayType,a.data),{length:f=
e.length/Se(b),nullCount:g=a.nullBitmap?-1:0}=a;return new O(b,c,f,g,[void 0,e,d])}visitTime(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.nullBitmap),e=u(b.ArrayType,a.data),{length:f=e.length/Se(b),nullCount:g=a.nullBitmap?-1:0}=a;return new O(b,c,f,g,[void 0,e,d])}visitDecimal(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.nullBitmap),e=u(b.ArrayType,a.data),{length:f=e.length/Se(b),nullCount:g=a.nullBitmap?-1:0}=a;return new O(b,c,f,g,[void 0,e,d])}visitList(a){const {type:b,offset:c=0,child:d}=
a,e=u(Uint8Array,a.nullBitmap),f=u(Int32Array,a.valueOffsets),{length:g=f.length-1,nullCount:k=a.nullBitmap?-1:0}=a;return new O(b,c,g,k,[f,void 0,e],[d])}visitStruct(a){const {type:b,offset:c=0,children:d=[]}=a,e=u(Uint8Array,a.nullBitmap),{length:f=d.reduce((k,{length:m})=>Math.max(k,m),0),nullCount:g=a.nullBitmap?-1:0}=a;return new O(b,c,f,g,[void 0,void 0,e],d)}visitUnion(a){const {type:b,offset:c=0,children:d=[]}=a,e=u(b.ArrayType,a.typeIds),{length:f=e.length,nullCount:g=-1}=a;if(K.isSparseUnion(b))return new O(b,
c,f,g,[void 0,void 0,void 0,e],d);a=u(Int32Array,a.valueOffsets);return new O(b,c,f,g,[a,void 0,void 0,e],d)}visitDictionary(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.nullBitmap),e=u(b.indices.ArrayType,a.data),{dictionary:f=new M([(new Ug).visit({type:b.dictionary})])}=a,{length:g=e.length,nullCount:k=a.nullBitmap?-1:0}=a;return new O(b,c,g,k,[void 0,e,d],[],f)}visitInterval(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.nullBitmap),e=u(b.ArrayType,a.data),{length:f=e.length/Se(b),nullCount:g=
a.nullBitmap?-1:0}=a;return new O(b,c,f,g,[void 0,e,d])}visitDuration(a){const {type:b,offset:c=0}=a,d=u(Uint8Array,a.nullBitmap),e=u(b.ArrayType,a.data),{length:f=e.length,nullCount:g=a.nullBitmap?-1:0}=a;return new O(b,c,f,g,[void 0,e,d])}visitFixedSizeList(a){const {type:b,offset:c=0,child:d=(new Ug).visit({type:b.valueType})}=a,e=u(Uint8Array,a.nullBitmap),{length:f=d.length/Se(b),nullCount:g=a.nullBitmap?-1:0}=a;return new O(b,c,f,g,[void 0,void 0,e],[d])}visitMap(a){const {type:b,offset:c=0,
child:d=(new Ug).visit({type:b.childType})}=a,e=u(Uint8Array,a.nullBitmap),f=u(Int32Array,a.valueOffsets),{length:g=f.length-1,nullCount:k=a.nullBitmap?-1:0}=a;return new O(b,c,g,k,[f,void 0,e],[d])}}const Vg=new Ug;function P(a){return Vg.visit(a)};class Wg{constructor(a=0,b){this.Eb=a;this.Cb=b;this.bb=0;this.Ab=this.Cb(0)}next(){for(;this.bb<this.Eb;){const a=this.Ab.next();if(!a.done)return a;++this.bb<this.Eb&&(this.Ab=this.Cb(this.bb))}return{done:!0,value:null}}[Symbol.iterator](){return this}}function Xg(a){return a.some(b=>b.nullable)}function Yg(a){return a.reduce((b,c)=>b+c.nullCount,0)}function Zg(a){return a.reduce((b,c,d)=>{b[d+1]=b[d]+c.length;return b},new Uint32Array(a.length+1))}
function $g(a,b,c,d){const e=[];for(let f=-1,g=a.length;++f<g;){const k=a[f],m=b[f],p=k.length;if(m>=d)break;if(c>=m+p)continue;if(m>=c&&m+p<=d){e.push(k);continue}const r=Math.max(0,c-m);e.push(k.slice(r,Math.min(d-m,p)-r))}0===e.length&&e.push(a[0].slice(0,0));return e}function ah(a,b,c,d){let e=0,f,g=b.length-1;do{if(e>=g-1)return c<b[g]?d(a,e,c-b[e]):null;f=e+Math.trunc(.5*(g-e));c<b[f]?g=f:e=f}while(e<g)}function bh(a,b){return a.getValid(b)}
function ch(a){function b(c,d,e){return a(c[d],e)}return function(c){return ah(this.data,this.j,c,b)}}function dh(a){function b(d,e,f){return a(d[e],f,c)}let c;return function(d,e){const f=this.data;c=e;d=ah(f,this.j,d,b);c=void 0;return d}}function eh(a){function b(d,e,f){var g=f;f=0;for(let k=e-1,m=d.length;++k<m;){e=d[k];if(~(g=a(e,c,g)))return f+g;g=0;f+=e.length}return-1}let c;return function(d,e){c=d;d=this.data;e="number"!==typeof e?b(d,0,0):ah(d,this.j,e,b);c=void 0;return e}};class fh extends Te{}function Q(a,b,c){if(void 0===b)return-1;if(null===b)switch(a.typeId){case H.Union:break;case H.Dictionary:break;default:a:{const e=a.nullBitmap;if(e&&!(0>=a.nullCount)){b=0;for(var d of new Ng(e,a.offset+(c||0),a.length,e,Jg)){if(!d){a=b;break a}++b}}a=-1}return a}d=Rf.getVisitFn(a);b=Bg(b);for(let e=(c||0)-1,f=a.length;++e<f;)if(b(d(a,e)))return e;return-1}
function gh(a,b,c){const d=Rf.getVisitFn(a);b=Bg(b);for(let e=(c||0)-1,f=a.length;++e<f;)if(b(d(a,e)))return e;return-1}h=fh.prototype;h.visitNull=function(a,b){return null===b&&0<a.length?0:-1};h.visitBool=Q;h.visitInt=Q;h.visitInt8=Q;h.visitInt16=Q;h.visitInt32=Q;h.visitInt64=Q;h.visitUint8=Q;h.visitUint16=Q;h.visitUint32=Q;h.visitUint64=Q;h.visitFloat=Q;h.visitFloat16=Q;h.visitFloat32=Q;h.visitFloat64=Q;h.visitUtf8=Q;h.visitLargeUtf8=Q;h.visitBinary=Q;h.visitLargeBinary=Q;
h.visitFixedSizeBinary=Q;h.visitDate=Q;h.visitDateDay=Q;h.visitDateMillisecond=Q;h.visitTimestamp=Q;h.visitTimestampSecond=Q;h.visitTimestampMillisecond=Q;h.visitTimestampMicrosecond=Q;h.visitTimestampNanosecond=Q;h.visitTime=Q;h.visitTimeSecond=Q;h.visitTimeMillisecond=Q;h.visitTimeMicrosecond=Q;h.visitTimeNanosecond=Q;h.visitDecimal=Q;h.visitList=Q;h.visitStruct=Q;h.visitUnion=Q;h.visitDenseUnion=gh;h.visitSparseUnion=gh;h.visitDictionary=Q;h.visitInterval=Q;h.visitIntervalDayTime=Q;
h.visitIntervalYearMonth=Q;h.visitDuration=Q;h.visitDurationSecond=Q;h.visitDurationMillisecond=Q;h.visitDurationMicrosecond=Q;h.visitDurationNanosecond=Q;h.visitFixedSizeList=Q;h.visitMap=Q;const hh=new fh;class ih extends Te{}function R(a){const b=a.type;if(0===a.nullCount&&1===a.stride&&(K.isInt(b)&&64!==b.bitWidth||K.isTime(b)&&64!==b.bitWidth||K.isFloat(b)&&b.precision!==y.HALF))return new Wg(a.data.length,d=>{d=a.data[d];return d.values.subarray(0,d.length)[Symbol.iterator]()});let c=0;return new Wg(a.data.length,d=>{d=a.data[d].length;const e=a.slice(c,c+d);c+=d;return new jh(e)})}
class jh{constructor(a){this.Fb=a;this.index=0}next(){return this.index<this.Fb.length?{value:this.Fb.get(this.index++)}:{done:!0,value:null}}[Symbol.iterator](){return this}}h=ih.prototype;h.visitNull=R;h.visitBool=R;h.visitInt=R;h.visitInt8=R;h.visitInt16=R;h.visitInt32=R;h.visitInt64=R;h.visitUint8=R;h.visitUint16=R;h.visitUint32=R;h.visitUint64=R;h.visitFloat=R;h.visitFloat16=R;h.visitFloat32=R;h.visitFloat64=R;h.visitUtf8=R;h.visitLargeUtf8=R;h.visitBinary=R;h.visitLargeBinary=R;
h.visitFixedSizeBinary=R;h.visitDate=R;h.visitDateDay=R;h.visitDateMillisecond=R;h.visitTimestamp=R;h.visitTimestampSecond=R;h.visitTimestampMillisecond=R;h.visitTimestampMicrosecond=R;h.visitTimestampNanosecond=R;h.visitTime=R;h.visitTimeSecond=R;h.visitTimeMillisecond=R;h.visitTimeMicrosecond=R;h.visitTimeNanosecond=R;h.visitDecimal=R;h.visitList=R;h.visitStruct=R;h.visitUnion=R;h.visitDenseUnion=R;h.visitSparseUnion=R;h.visitDictionary=R;h.visitInterval=R;h.visitIntervalDayTime=R;
h.visitIntervalYearMonth=R;h.visitDuration=R;h.visitDurationSecond=R;h.visitDurationMillisecond=R;h.visitDurationMicrosecond=R;h.visitDurationNanosecond=R;h.visitFixedSizeList=R;h.visitMap=R;const kh=new ih;const lh={},mh={};
class M{constructor(a){a=a[0]instanceof M?a.flatMap(f=>f.data):a;if(0===a.length||a.some(f=>!(f instanceof O)))throw new TypeError("Vector constructor expects an Array of Data instances.");let b;const c=null==(b=a[0])?void 0:b.type;switch(a.length){case 0:this.j=[0];break;case 1:const {get:f,set:g,indexOf:k}=lh[c.typeId],m=a[0];this.isValid=p=>m.getValid(p);this.get=p=>f(m,p);this.set=(p,r)=>g(m,p,r);this.indexOf=p=>k(m,p);this.j=[0,m.length];break;default:Object.setPrototypeOf(this,mh[c.typeId]),
this.j=Zg(a)}this.data=a;this.type=c;this.stride=Se(c);let d,e;this.numChildren=null!=(e=null==(d=c.children)?void 0:d.length)?e:0;this.length=this.j.at(-1)}get byteLength(){return this.data.reduce((a,b)=>a+b.byteLength,0)}get nullable(){return Xg(this.data)}get nullCount(){return Yg(this.data)}get ArrayType(){return this.type.ArrayType}get [Symbol.toStringTag](){return`${this.VectorName}<${this.type[Symbol.toStringTag]}>`}get VectorName(){return`${H[this.type.typeId]}Vector`}isValid(){return!1}get(){return null}at(a){return this.get(0>
a?this.length+a:a)}set(){}indexOf(){return-1}includes(a,b){return-1<this.indexOf(a,b)}[Symbol.iterator](){return kh.visit(this)}concat(...a){return new M(this.data.concat(a.flatMap(b=>b.data).flat(Number.POSITIVE_INFINITY)))}slice(a,b){return new M(zg(this,a,b,({data:c,j:d},e,f)=>$g(c,d,e,f)))}toJSON(){return[...this]}toArray(){const a=this.data,b=this.length,c=this.stride,d=this.ArrayType;switch(this.type.typeId){case H.Int:case H.Float:case H.Decimal:case H.Time:case H.Timestamp:switch(a.length){case 0:return new d;
case 1:return a[0].values.subarray(0,b*c);default:return a.reduce((e,{values:f,length:g})=>{e.vb.set(f.subarray(0,g*c),e.offset);e.offset+=g*c;return e},{vb:new d(b*c),offset:0}).vb}}return[...this]}toString(){return`[${[...this].join()}]`}getChild(a){let b;return this.getChildAt(null==(b=this.type.children)?void 0:b.findIndex(c=>c.name===a))}getChildAt(a){return-1<a&&a<this.numChildren?new M(this.data.map(({children:b})=>b[a])):null}get isMemoized(){return K.isDictionary(this.type)?this.data[0].dictionary.isMemoized:
!1}memoize(){if(K.isDictionary(this.type)){const a=new nh(this.data[0].dictionary),b=this.data.map(c=>{c=c.clone();c.dictionary=a;return c});return new M(b)}return new nh(this)}unmemoize(){if(K.isDictionary(this.type)&&this.isMemoized){const a=this.data[0].dictionary.unmemoize(),b=this.data.map(c=>{c=c.clone();c.dictionary=a;return c});return new M(b)}return this}}
M[Symbol.toStringTag]=(a=>{a.type=K.prototype;a.data=[];a.length=0;a.stride=1;a.numChildren=0;a.j=new Uint32Array([0]);a[Symbol.isConcatSpreadable]=!0;var b=Object.keys(H).map(c=>H[c]).filter(c=>"number"===typeof c&&c!==H.NONE);for(const c of b){b=Rf.getVisitFnByTypeId(c);const d=Cf.getVisitFnByTypeId(c),e=hh.getVisitFnByTypeId(c);lh[c]={get:b,set:d,indexOf:e};mh[c]=Object.create(a,{isValid:{value:ch(bh)},get:{value:ch(Rf.getVisitFnByTypeId(c))},set:{value:dh(Cf.getVisitFnByTypeId(c))},indexOf:{value:eh(hh.getVisitFnByTypeId(c))}})}return"Vector"})(M.prototype);
class nh extends M{constructor(a){super(a.data);const b=this.get,c=this.set,d=this.slice,e=Array(this.length);Object.defineProperty(this,"get",{value(f){var g=e[f];if(void 0!==g)return g;g=b.call(this,f);return e[f]=g}});Object.defineProperty(this,"set",{value(f,g){c.call(this,f,g);e[f]=g}});Object.defineProperty(this,"slice",{value:(f,g)=>new nh(d.call(this,f,g))});Object.defineProperty(this,"isMemoized",{value:!0});Object.defineProperty(this,"unmemoize",{value:()=>new M(this.data)});Object.defineProperty(this,
"memoize",{value:()=>this})}}
function oh(a){if(a){if(a instanceof O)return new M([a]);if(a instanceof M)return new M(a.data);if(a.type instanceof K)return new M([P(a)]);if(Array.isArray(a))return new M(a.flatMap(b=>b instanceof O?[b]:b instanceof M?b.data:oh(b).data));if(ArrayBuffer.isView(a)){a instanceof DataView&&(a=new Uint8Array(a.buffer));const b={offset:0,length:a.length,nullCount:-1,data:a};if(a instanceof Int8Array)return new M([P(Object.assign({},b,{type:new Sc}))]);if(a instanceof Int16Array)return new M([P(Object.assign({},b,
{type:new Tc}))]);if(a instanceof Int32Array)return new M([P(Object.assign({},b,{type:new Uc}))]);if(a instanceof BigInt64Array)return new M([P(Object.assign({},b,{type:new Vc}))]);if(a instanceof Uint8Array||a instanceof Uint8ClampedArray)return new M([P(Object.assign({},b,{type:new Wc}))]);if(a instanceof Uint16Array)return new M([P(Object.assign({},b,{type:new Xc}))]);if(a instanceof Uint32Array)return new M([P(Object.assign({},b,{type:new Yc}))]);if(a instanceof BigUint64Array)return new M([P(Object.assign({},
b,{type:new Zc}))]);if(a instanceof Float32Array)return new M([P(Object.assign({},b,{type:new ed}))]);if(a instanceof Float64Array)return new M([P(Object.assign({},b,{type:new fd}))])}}throw Error("Unrecognized input");};function ph(a){if(!a||0>=a.length)return function(){return!0};let b="";const c=a.filter(d=>d===d);0<c.length&&(b=`
    switch (x) {${c.map(d=>`
        case ${"bigint"!==typeof d?uc(d):`${uc(d)}n`}:`).join("")}
            return false;
    }`);a.length!==c.length&&(b=`if (x !== x) return false;\n${b}`);return new Function("x",`${b}\nreturn true;`)};function qh(a,b){a=Math.ceil(a)*b-1;return(a-a%64+64||64)/b}function rh(a,b=0){return a.length>=b?a.subarray(0,b):Da(new a.constructor(b),a,0)}function sh(a,b){if(0<b){a.length+=b;b=a.length*a.stride;const c=a.buffer.length;b>=c&&(a.buffer=rh(a.buffer,0===c?qh(1*b,a.BYTES_PER_ELEMENT):qh(2*b,a.BYTES_PER_ELEMENT)))}return a}
class th{constructor(a,b=0,c=1){this.length=Math.ceil(b/c);this.buffer=new a(this.length);this.stride=c;this.BYTES_PER_ELEMENT=a.BYTES_PER_ELEMENT;this.ArrayType=a}get byteLength(){return Math.ceil(this.length*this.stride)*this.BYTES_PER_ELEMENT}get reservedLength(){return this.buffer.length/this.stride}get reservedByteLength(){return this.buffer.byteLength}set(){return this}append(a){return this.set(this.length,a)}flush(a=this.length){a=qh(a*this.stride,this.BYTES_PER_ELEMENT);a=rh(this.buffer,a);
this.clear();return a}clear(){this.length=0;this.buffer=new this.ArrayType;return this}}class uh extends th{get(a){return this.buffer[a]}set(a,b){sh(this,a-this.length+1);this.buffer[a*this.stride]=b;return this}}
class vh extends uh{constructor(){super(Uint8Array,0,.125);this.La=0}get Yb(){return this.length-this.La}get(a){return this.buffer[a>>3]>>a%8&1}set(a,b){const {buffer:c}=sh(this,a-this.length+1),d=a>>3;a%=8;const e=c[d]>>a&1;b?0===e&&(c[d]|=1<<a,++this.La):1===e&&(c[d]&=~(1<<a),--this.La);return this}clear(){this.La=0;return super.clear()}}
class wh extends uh{constructor(a){super(a.OffsetArrayType,1,1)}append(a){return this.set(this.length-1,a)}set(a,b){const c=this.length-1,d=sh(this,a-c+1).buffer;c<a++&&0<=c&&d.fill(d[c],c,a);d[a]=d[a-1]+b;return this}flush(a=this.length-1){a>this.length&&this.set(a-1,4<this.BYTES_PER_ELEMENT?BigInt(0):0);return super.flush(a+1)}};class xh{static throughNode(){throw Error('"throughNode" not available in this environment');}static throughDOM(){throw Error('"throughDOM" not available in this environment');}constructor({type:a,nullValues:b}){this.length=0;this.finished=!1;this.type=a;this.children=[];this.nullValues=b;this.stride=Se(a);this.D=new vh;b&&0<b.length&&(this.qb=ph(b))}toVector(){return new M([this.flush()])}get ArrayType(){return this.type.ArrayType}get nullCount(){return this.D.Yb}get numChildren(){return this.children.length}get byteLength(){let a=
0;const b=this.j,c=this.o,d=this.D,e=this.$,f=this.children;b&&(a+=b.byteLength);c&&(a+=c.byteLength);d&&(a+=d.byteLength);e&&(a+=e.byteLength);return f.reduce((g,k)=>g+k.byteLength,a)}get reservedLength(){return this.D.reservedLength}get reservedByteLength(){let a=0;this.j&&(a+=this.j.reservedByteLength);this.o&&(a+=this.o.reservedByteLength);this.D&&(a+=this.D.reservedByteLength);this.$&&(a+=this.$.reservedByteLength);return this.children.reduce((b,c)=>b+c.reservedByteLength,a)}get valueOffsets(){return this.j?
this.j.buffer:null}get values(){return this.o?this.o.buffer:null}get nullBitmap(){return this.D?this.D.buffer:null}get typeIds(){return this.$?this.$.buffer:null}append(a){return this.set(this.length,a)}isValid(a){return this.qb(a)}set(a,b){this.setValid(a,this.isValid(b))&&this.setValue(a,b);return this}setValue(a,b){this.A(this,a,b)}setValid(a,b){this.length=this.D.set(a,+b).length;return b}addChild(){throw Error(`Cannot append children to non-nested type "${this.type}"`);}getChildAt(a){return this.children[a]||
null}flush(){let a,b,c,d;const e=this.type,f=this.length,g=this.nullCount;var k=this.j;const m=this.o,p=this.D;let r;(b=null==(r=this.$)?void 0:r.flush(f))?d=null==k?void 0:k.flush(f):a=(d=null==k?void 0:k.flush(f))?null==m?void 0:m.flush(k.get(k.length-1)):null==m?void 0:m.flush(f);0<g&&(c=null==p?void 0:p.flush(f));k=this.children.map(T=>T.flush());this.clear();return P({type:e,length:f,nullCount:g,children:k,child:k[0],data:a,typeIds:b,nullBitmap:c,valueOffsets:d})}finish(){this.finished=!0;for(const a of this.children)a.finish();
return this}clear(){this.length=0;let a;null==(a=this.D)||a.clear();let b;null==(b=this.o)||b.clear();let c;null==(c=this.j)||c.clear();let d;null==(d=this.$)||d.clear();for(const e of this.children)e.clear();return this}}h=xh.prototype;h.length=1;h.stride=1;h.children=null;h.finished=!1;h.nullValues=null;h.qb=()=>!0;class yh extends xh{constructor(a){super(a);this.o=new uh(this.ArrayType,0,this.stride)}setValue(a,b){const c=this.o;sh(c,a-c.length+1);return super.setValue(a,b)}}
function zh(a){const b=a.G,c=a.J;a.J=0;a.G=void 0;b&&0<b.size&&a.U(b,c)}
class Ah extends xh{constructor(a){super(a);this.J=0;this.j=new wh(a.type)}setValue(a,b){const c=this.G||(this.G=new Map),d=c.get(a);d&&(this.J-=d.length);this.J+=b instanceof rg?b[sg].length:b.length;c.set(a,b)}setValid(a,b){return super.setValid(a,b)?!0:((this.G||(this.G=new Map)).set(a,void 0),!1)}clear(){this.J=0;this.G=void 0;return super.clear()}flush(){zh(this);return super.flush()}finish(){zh(this);return super.finish()}};class Bh{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}offset(){return fb(this.g,this.h)}cb(){return this.g.u(this.h+8)}bodyLength(){return fb(this.g,this.h+16)}};class Ch{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}version(){const a=A(this.g,this.h,4);return a?eb(this.g,this.h+a):w.V1}schema(a){const b=A(this.g,this.h,6);return b?(a||new mc).i(ib(this.g,this.h+b),this.g):null}dictionaries(a,b){const c=A(this.g,this.h,8);return c?(b||new Bh).i(jb(this.g,this.h+c)+24*a,this.g):null}ib(a){const b=A(this.g,this.h,10);return b?(new Bh).i(jb(this.g,this.h+b)+24*a,this.g):null}Ga(a){const b=A(this.g,this.h,12);return b?(new Ub).i(ib(this.g,
jb(this.g,this.h+b)+4*a),this.g):null}Ha(){const a=A(this.g,this.h,12);return a?kb(this.g,this.h+a):0}};class S{constructor(a=[],b,c,d=w.V5){this.fields=a||[];this.metadata=b||new Map;c||(c=Dh(this.fields));this.dictionaries=c;this.Ka=d}get [Symbol.toStringTag](){return"Schema"}get names(){return this.fields.map(a=>a.name)}toString(){return`Schema<{ ${this.fields.map((a,b)=>`${b}: ${a}`).join(", ")} }>`}select(a){const b=new Set(a);a=this.fields.filter(c=>b.has(c.name));return new S(a,this.metadata)}selectAt(a){a=a.map(b=>this.fields[b]).filter(Boolean);return new S(a,this.metadata)}assign(...a){var b=
a[0]instanceof S?a[0]:Array.isArray(a[0])?new S(a[0]):new S(a);const c=[...this.fields];a=Eh(Eh(new Map,this.metadata),b.metadata);b=b.fields.filter(e=>{const f=c.findIndex(g=>g.name===e.name);return~f?(c[f]=e.clone({metadata:Eh(Eh(new Map,c[f].metadata),e.metadata)}))&&!1:!0});const d=Dh(b,new Map);return new S([...c,...b],a,new Map([...this.dictionaries,...d]))}}S.prototype.fields=null;S.prototype.metadata=null;S.prototype.dictionaries=null;
class U{static new(...a){let [b,c,d,e]=a;a[0]&&"object"===typeof a[0]&&({name:b}=a[0],void 0===c&&(c=a[0].type),void 0===d&&(d=a[0].nullable),void 0===e&&(e=a[0].metadata));return new U(`${b}`,c,d,e)}constructor(a,b,c=!1,d){this.name=a;this.type=b;this.nullable=c;this.metadata=d||new Map}get typeId(){return this.type.typeId}get [Symbol.toStringTag](){return"Field"}toString(){return`${this.name}: ${this.type}`}clone(...a){let [b,c,d,e]=a;a[0]&&"object"===typeof a[0]?{name:b=this.name,type:c=this.type,
nullable:d=this.nullable,metadata:e=this.metadata}=a[0]:[b=this.name,c=this.type,d=this.nullable,e=this.metadata]=a;return U.new(b,c,d,e)}}U.prototype.type=null;U.prototype.name=null;U.prototype.nullable=null;U.prototype.metadata=null;function Eh(a,b){return new Map([...(a||new Map),...(b||new Map)])}
function Dh(a,b=new Map){for(let c=-1,d=a.length;++c<d;){const e=a[c].type;if(K.isDictionary(e))if(!b.has(e.id))b.set(e.id,e.dictionary);else if(b.get(e.id)!==e.dictionary)throw Error("Cannot create Schema containing two different dictionaries with the same Id");e.children&&0<e.children.length&&Dh(e.children,b)}return b};function*Fh(a){for(let b,c=-1,d=a.numDictionaries;++c<d;)if(b=a.Ia(c))yield b}
class Gh{static decode(a){a=new lb(u(Uint8Array,a));a=(new Ch).i(a.u(a.position())+a.position(),a);const b=S.decode(a.schema(),new Map,a.version());return new Hh(b,a)}static encode(a){const b=new Ab;var c=S.encode(b,a.schema);xb(b,24,a.numRecordBatches,8);for(var d of[...a.ib()].slice().reverse())Ih.encode(b,d);d=yb(b);xb(b,24,a.numDictionaries,8);for(const e of[...Fh(a)].slice().reverse())Ih.encode(b,e);a=yb(b);C(b,5);B(b,1,c);sb(b,w.V5,w.V1);B(b,3,d);B(b,2,a);c=D(b);b.finish(c);return mb(b)}get numRecordBatches(){return this.ba.length}get numDictionaries(){return this.ob.length}constructor(a,
b=w.V5,c,d){this.schema=a;this.version=b;c&&(this.ba=c);d&&(this.ob=d)}*ib(){for(let a,b=-1,c=this.numRecordBatches;++b<c;)if(a=this.ga(b))yield a}ga(a){return 0<=a&&a<this.numRecordBatches&&this.ba[a]||null}Ia(a){return 0<=a&&a<this.numDictionaries&&this.ob[a]||null}}
class Hh extends Gh{get numRecordBatches(){var a=this.m;const b=A(a.g,a.h,10);return b?kb(a.g,a.h+b):0}get numDictionaries(){var a=this.m;const b=A(a.g,a.h,8);return b?kb(a.g,a.h+b):0}constructor(a,b){super(a,b.version());this.m=b}ga(a){return 0<=a&&a<this.numRecordBatches&&(a=this.m.ib(a))?Ih.decode(a):null}Ia(a){return 0<=a&&a<this.numDictionaries&&(a=this.m.dictionaries(a))?Ih.decode(a):null}}
class Ih{static decode(a){return new Ih(a.cb(),a.bodyLength(),a.offset())}static encode(a,b){var c=BigInt(b.offset),d=b.cb;b=BigInt(b.bodyLength);ob(a,8,24);a.aa(BigInt(null!=b?b:0));nb(a,4);a.Y(d);a.aa(BigInt(null!=c?c:0));return a.offset()}constructor(a,b,c){this.cb=a;this.offset=J(c);this.bodyLength=J(b)}};const Jh=Object.freeze({done:!0,value:void 0});class Kh{constructor(a){this.na=a}get schema(){return this.na.schema}get batches(){return this.na.batches||[]}get dictionaries(){return this.na.dictionaries||[]}}function Lh(a){return a.mb||(a.mb=a.toDOMStream())}function Mh(a,b,c){return Mh(a.sb||(a.sb=a.toNodeStream()),b,c)}class Nh{tee(){return Lh(this).tee()}pipeTo(a,b){return Lh(this).pipeTo(a,b)}pipeThrough(a,b){return Lh(this).pipeThrough(a,b)}}
class Oh extends Nh{constructor(){super();this.o=[];this.ia=[];this.Oa=new Promise(a=>this.L=a)}get closed(){return this.Oa}cancel(a){const b=this;return l(function*(){yield b.return(a)})}write(a){if(!this.L)throw Error("AsyncQueue is closed");0>=this.ia.length?this.o.push(a):this.ia.shift().resolve({done:!1,value:a})}abort(a){this.L&&(0>=this.ia.length?this.Ra={error:a}:this.ia.shift().reject({done:!0,value:a}))}close(){if(this.L){const a=this.ia;for(;0<a.length;)a.shift().resolve(Jh);this.L();this.L=
void 0}}[Symbol.asyncIterator](){return this}toDOMStream(a){return La(this.L||this.Ra?this:this.o,a)}toNodeStream(){return Ma()}throw(a){const b=this;return l(function*(){yield b.abort(a);return Jh})}return(){const a=this;return l(function*(){yield a.close();return Jh})}read(a){const b=this;return l(function*(){return(yield b.next(a,"read")).value})}peek(a){const b=this;return l(function*(){return(yield b.next(a,"peek")).value})}next(){return 0<this.o.length?Promise.resolve({done:!1,value:this.o.shift()}):
this.Ra?Promise.reject({done:!0,value:this.Ra.error}):this.L?new Promise((a,b)=>{this.ia.push({resolve:a,reject:b})}):Promise.resolve(Jh)}};class Ph extends Oh{write(a){if(0<(a=u(Uint8Array,a)).byteLength)return super.write(a)}toString(a=!1){return a?va.decode(this.toUint8Array(!0)):this.toUint8Array(!1).then(wa)}toUint8Array(a=!1){return a?Ea(this.o)[0]:(()=>{const b=this;return l(function*(){const c=[];let d=0;var e;try{for(var f=ia(b);;){var g=yield f.next();if(g.done)break;const m=g.value;c.push(m);d+=m.byteLength}}catch(m){var k={error:m}}finally{try{g&&!g.done&&(e=f.return)&&(yield e.call(f))}finally{if(k)throw k.error;}}return Ea(c,
d)[0]})})()}}class Qh{constructor(a){a&&(this.source=new Rh(Na(Oa(a))))}[Symbol.iterator](){return this}next(a){return this.source.next(a)}throw(a){return this.source.throw(a)}return(a){return this.source.return(a)}peek(a){return this.source.peek(a)}read(a){return this.source.read(a)}}
class Sh{constructor(a){a instanceof Sh?this.source=a.source:a instanceof Ph?this.source=new Th(Na(Pa(a))):Aa(a)?this.source=new Th(Na(Ta(a))):za(a)?this.source=new Th(Na(Qa(a))):t(a)&&za(a.body)?this.source=new Th(Na(Qa(a.body))):t(a)&&q(a[Symbol.iterator])?this.source=new Th(Na(Oa(a))):t(a)&&q(a.then)?this.source=new Th(Na(Pa(a))):t(a)&&q(a[Symbol.asyncIterator])&&(this.source=new Th(Na(Pa(a))))}[Symbol.asyncIterator](){return this}next(a){return this.source.next(a)}throw(a){return this.source.throw(a)}return(a){return this.source.return(a)}get closed(){return this.source.closed}cancel(a){return this.source.cancel(a)}peek(a){return this.source.peek(a)}read(a){return this.source.read(a)}}
class Rh{constructor(a){this.source=a}cancel(a){this.return(a)}peek(a){return this.next(a,"peek").value}read(a){return this.next(a,"read").value}next(a,b="read"){return this.source.next({N:b,size:a})}throw(a){return Object.create(this.source.throw&&this.source.throw(a)||Jh)}return(a){return Object.create(this.source.return&&this.source.return(a)||Jh)}}
class Th{constructor(a){this.source=a;this.Oa=new Promise(b=>this.L=b)}cancel(a){const b=this;return l(function*(){yield b.return(a)})}get closed(){return this.Oa}read(a){const b=this;return l(function*(){return(yield b.next(a,"read")).value})}peek(a){const b=this;return l(function*(){return(yield b.next(a,"peek")).value})}next(a,b="read"){const c=this;return l(function*(){return yield c.source.next({N:b,size:a})})}throw(a){const b=this;return l(function*(){const c=b.source.throw&&(yield b.source.throw(a))||
Jh;b.L&&b.L();b.L=void 0;return Object.create(c)})}return(a){const b=this;return l(function*(){const c=b.source.return&&(yield b.source.return(a))||Jh;b.L&&b.L();b.L=void 0;return Object.create(c)})}};class Uh extends Qh{constructor(a){super();this.position=0;this.buffer=u(Uint8Array,a);this.size=this.buffer.byteLength}u(a){const {buffer:b,byteOffset:c}=this.ha(a,4);return(new DataView(b,c)).getInt32(0,!0)}seek(a){this.position=Math.min(a,this.size);return a<this.size}read(a){const b=this.buffer,c=this.size,d=this.position;return b&&d<c?("number"!==typeof a&&(a=Number.POSITIVE_INFINITY),this.position=Math.min(c,d+Math.min(c-d,a)),b.subarray(d,this.position)):null}ha(a,b){const c=this.buffer,d=
Math.min(this.size,a+b);return c?c.subarray(a,d):new Uint8Array(b)}close(){this.buffer&&(this.buffer=null)}throw(a){this.close();return{done:!0,value:a}}return(a){this.close();return{done:!0,value:a}}}
class Vh extends Sh{constructor(a,b){super();this.position=0;this.I=a;"number"===typeof b?this.size=b:this.G=(()=>{const c=this;return l(function*(){c.size=(yield a.stat()).size;delete c.G})})()}u(a){const b=this;return l(function*(){const {buffer:c,byteOffset:d}=yield b.ha(a,4);return(new DataView(c,d)).getInt32(0,!0)})}seek(a){const b=this;return l(function*(){b.G&&(yield b.G);b.position=Math.min(a,b.size);return a<b.size})}read(a){const b=this;return l(function*(){b.G&&(yield b.G);const c=b.I;
var d=b.size,e=b.position;if(c&&e<d){"number"!==typeof a&&(a=Number.POSITIVE_INFINITY);let f=0,g=0;d=Math.min(d,e+Math.min(d-e,a));const k=new Uint8Array(Math.max(0,(b.position=d)-e));for(;(e+=g)<d&&(f+=g)<k.byteLength;)({fc:g}=yield c.read(k,f,k.byteLength-f,e));return k}return null})}ha(a,b){const c=this;return l(function*(){c.G&&(yield c.G);const d=c.I;var e=c.size;return d&&a+b<e?(e=new Uint8Array(Math.min(e,a+b)-a),(yield d.read(e,0,b,a)).buffer):new Uint8Array(b)})}close(){const a=this;return l(function*(){const b=
a.I;a.I=null;b&&(yield b.close())})}throw(a){const b=this;return l(function*(){yield b.close();return{done:!0,value:a}})}return(a){const b=this;return l(function*(){yield b.close();return{done:!0,value:a}})}};function Wh(a){0>a&&(a=4294967295+a+1);return`0x${a.toString(16)}`}const Xh=[1,10,100,1E3,1E4,1E5,1E6,1E7,1E8];
function Yh(a,b){const c=new Uint32Array([a.buffer[1]>>>16,a.buffer[1]&65535,a.buffer[0]>>>16,a.buffer[0]&65535]);b=new Uint32Array([b.buffer[1]>>>16,b.buffer[1]&65535,b.buffer[0]>>>16,b.buffer[0]&65535]);let d=c[3]*b[3];a.buffer[0]=d&65535;let e=d>>>16;d=c[2]*b[3];e+=d;d=c[3]*b[2]>>>0;e+=d;a.buffer[0]+=e<<16;a.buffer[1]=e>>>0<d?65536:0;a.buffer[1]+=e>>>16;a.buffer[1]+=c[1]*b[3]+c[2]*b[2]+c[3]*b[1];a.buffer[1]+=c[0]*b[3]+c[1]*b[2]+c[2]*b[1]+c[3]*b[0]<<16}
function Zh(a,b){const c=a.buffer[0]+b.buffer[0]>>>0;a.buffer[1]+=b.buffer[1];c<a.buffer[0]>>>0&&++a.buffer[1];a.buffer[0]=c}class $h{constructor(a){this.buffer=a}high(){return this.buffer[1]}low(){return this.buffer[0]}lessThan(a){return this.buffer[1]<a.buffer[1]||this.buffer[1]===a.buffer[1]&&this.buffer[0]<a.buffer[0]}equals(a){return this.buffer[1]===a.buffer[1]&&this.buffer[0]==a.buffer[0]}greaterThan(a){return a.lessThan(this)}hex(){return`${Wh(this.buffer[1])} ${Wh(this.buffer[0])}`}}
class V extends $h{times(a){Yh(this,a);return this}plus(a){Zh(this,a);return this}static from(a,b=new Uint32Array(2)){return V.fromString("string"===typeof a?a:a.toString(),b)}static fromNumber(a,b=new Uint32Array(2)){return V.fromString(a.toString(),b)}static fromString(a,b=new Uint32Array(2)){const c=a.length;b=new V(b);for(let d=0;d<c;){const e=8<c-d?8:c-d,f=new V(new Uint32Array([Number.parseInt(a.slice(d,d+e),10),0])),g=new V(new Uint32Array([Xh[e],0]));b.times(g);b.plus(f);d+=e}return b}static convertArray(a){const b=
new Uint32Array(2*a.length);for(let c=-1,d=a.length;++c<d;)V.from(a[c],new Uint32Array(b.buffer,b.byteOffset+8*c,2));return b}static multiply(a,b){return(new V(new Uint32Array(a.buffer))).times(b)}static add(a,b){return(new V(new Uint32Array(a.buffer))).plus(b)}}
class ai extends $h{negate(){this.buffer[0]=~this.buffer[0]+1;this.buffer[1]=~this.buffer[1];0==this.buffer[0]&&++this.buffer[1];return this}times(a){Yh(this,a);return this}plus(a){Zh(this,a);return this}lessThan(a){const b=this.buffer[1]<<0,c=a.buffer[1]<<0;return b<c||b===c&&this.buffer[0]<a.buffer[0]}static from(a,b=new Uint32Array(2)){return ai.fromString("string"===typeof a?a:a.toString(),b)}static fromNumber(a,b=new Uint32Array(2)){return ai.fromString(a.toString(),b)}static fromString(a,b=
new Uint32Array(2)){const c=a.startsWith("-"),d=a.length;b=new ai(b);for(let e=c?1:0;e<d;){const f=8<d-e?8:d-e,g=new ai(new Uint32Array([Number.parseInt(a.slice(e,e+f),10),0])),k=new ai(new Uint32Array([Xh[f],0]));b.times(k);b.plus(g);e+=f}return c?b.negate():b}static convertArray(a){const b=new Uint32Array(2*a.length);for(let c=-1,d=a.length;++c<d;)ai.from(a[c],new Uint32Array(b.buffer,b.byteOffset+8*c,2));return b}static multiply(a,b){return(new ai(new Uint32Array(a.buffer))).times(b)}static add(a,
b){return(new ai(new Uint32Array(a.buffer))).plus(b)}}
class bi{constructor(a){this.buffer=a}high(){return new ai(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}low(){return new ai(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}negate(){this.buffer[0]=~this.buffer[0]+1;this.buffer[1]=~this.buffer[1];this.buffer[2]=~this.buffer[2];this.buffer[3]=~this.buffer[3];0==this.buffer[0]&&++this.buffer[1];0==this.buffer[1]&&++this.buffer[2];0==this.buffer[2]&&++this.buffer[3];return this}times(a){const b=new V(new Uint32Array([this.buffer[3],
0])),c=new V(new Uint32Array([this.buffer[2],0])),d=new V(new Uint32Array([this.buffer[1],0])),e=new V(new Uint32Array([this.buffer[0],0])),f=new V(new Uint32Array([a.buffer[3],0])),g=new V(new Uint32Array([a.buffer[2],0])),k=new V(new Uint32Array([a.buffer[1],0]));a=new V(new Uint32Array([a.buffer[0],0]));let m=V.multiply(e,a);this.buffer[0]=m.low();const p=new V(new Uint32Array([m.high(),0]));m=V.multiply(d,a);p.plus(m);m=V.multiply(e,k);p.plus(m);this.buffer[1]=p.low();this.buffer[3]=p.lessThan(m)?
1:0;this.buffer[2]=p.high();(new V(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))).plus(V.multiply(c,a)).plus(V.multiply(d,k)).plus(V.multiply(e,g));this.buffer[3]+=V.multiply(b,a).plus(V.multiply(c,k)).plus(V.multiply(d,g)).plus(V.multiply(e,f)).low();return this}plus(a){const b=new Uint32Array(4);b[3]=this.buffer[3]+a.buffer[3]>>>0;b[2]=this.buffer[2]+a.buffer[2]>>>0;b[1]=this.buffer[1]+a.buffer[1]>>>0;b[0]=this.buffer[0]+a.buffer[0]>>>0;b[0]<this.buffer[0]>>>0&&++b[1];b[1]<this.buffer[1]>>>
0&&++b[2];b[2]<this.buffer[2]>>>0&&++b[3];this.buffer[3]=b[3];this.buffer[2]=b[2];this.buffer[1]=b[1];this.buffer[0]=b[0];return this}hex(){return`${Wh(this.buffer[3])} ${Wh(this.buffer[2])} ${Wh(this.buffer[1])} ${Wh(this.buffer[0])}`}static multiply(a,b){return(new bi(new Uint32Array(a.buffer))).times(b)}static add(a,b){return(new bi(new Uint32Array(a.buffer))).plus(b)}static from(a,b=new Uint32Array(4)){return bi.fromString("string"===typeof a?a:a.toString(),b)}static fromNumber(a,b=new Uint32Array(4)){return bi.fromString(a.toString(),
b)}static fromString(a,b=new Uint32Array(4)){const c=a.startsWith("-"),d=a.length;b=new bi(b);for(let e=c?1:0;e<d;){const f=8<d-e?8:d-e,g=new bi(new Uint32Array([Number.parseInt(a.slice(e,e+f),10),0,0,0])),k=new bi(new Uint32Array([Xh[f],0,0,0]));b.times(k);b.plus(g);e+=f}return c?b.negate():b}static convertArray(a){const b=new Uint32Array(4*a.length);for(let c=-1,d=a.length;++c<d;)bi.from(a[c],new Uint32Array(b.buffer,b.byteOffset+16*c,4));return b}}var ci={};ci.BaseInt64=$h;ci.Int128=bi;
ci.Int64=ai;ci.Uint64=V;function W(a){return a.M[++a.Xb]}function di(a){return a.buffers[++a.Wb]}
class ei extends Te{constructor(a,b,c,d,e=w.V5){super();this.Wb=this.Xb=-1;this.T=a;this.M=b;this.buffers=c;this.dictionaries=d;this.Ka=e}visit(a){return super.visit(a instanceof U?a.type:a)}visitNull(a,{length:b}=W(this)){return P({type:a,length:b})}visitBool(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitInt(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitFloat(a,
{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitUtf8(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),valueOffsets:this.ca(a),data:this.F(a)})}visitLargeUtf8(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),valueOffsets:this.ca(a),data:this.F(a)})}visitBinary(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,
c),valueOffsets:this.ca(a),data:this.F(a)})}visitLargeBinary(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),valueOffsets:this.ca(a),data:this.F(a)})}visitFixedSizeBinary(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitDate(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitTimestamp(a,{length:b,nullCount:c}=W(this)){return P({type:a,
length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitTime(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitDecimal(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitList(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),valueOffsets:this.ca(a),child:this.visit(a.children[0])})}visitStruct(a,{length:b,nullCount:c}=
W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),children:this.visitMany(a.children)})}visitUnion(a,{length:b,nullCount:c}=W(this)){this.Ka<w.V5&&this.C(a,c);return a.mode===x.Sparse?this.visitSparseUnion(a,{length:b,nullCount:c}):this.visitDenseUnion(a,{length:b,nullCount:c})}visitDenseUnion(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,typeIds:this.hb(a),valueOffsets:this.ca(a),children:this.visitMany(a.children)})}visitSparseUnion(a,{length:b,nullCount:c}=
W(this)){return P({type:a,length:b,nullCount:c,typeIds:this.hb(a),children:this.visitMany(a.children)})}visitDictionary(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a.indices),dictionary:this.dictionaries.get(a.id)})}visitInterval(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),data:this.F(a)})}visitDuration(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,
c),data:this.F(a)})}visitFixedSizeList(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),child:this.visit(a.children[0])})}visitMap(a,{length:b,nullCount:c}=W(this)){return P({type:a,length:b,nullCount:c,nullBitmap:this.C(a,c),valueOffsets:this.ca(a),child:this.visit(a.children[0])})}C(a,b){var c=di(this);return 0<b&&this.F(a,c)||new Uint8Array(0)}ca(a){return this.F(a)}hb(a){return this.F(a)}F(a,{length:b,offset:c}=di(this)){return this.T.subarray(c,c+
b)}}
class fi extends ei{constructor(a,b,c,d,e){super(new Uint8Array(0),b,c,d,e);this.sources=a}C(a,b){({offset:a}=di(this));return 0>=b?new Uint8Array(0):Mg(this.sources[a])}ca(a){var {offset:b}=di(this);return u(Uint8Array,u(a.OffsetArrayType,this.sources[b]))}hb(a){var {offset:b}=di(this);return u(Uint8Array,u(a.ArrayType,this.sources[b]))}F(a,{offset:b}=di(this)){const c=this.sources;return K.isTimestamp(a)||(K.isInt(a)||K.isTime(a))&&64===a.bitWidth||K.isDuration(a)||K.isDate(a)&&a.unit===Xa.MILLISECOND?u(Uint8Array,
ai.convertArray(c[b])):K.isDecimal(a)?u(Uint8Array,bi.convertArray(c[b])):K.isBinary(a)||K.isLargeBinary(a)||K.isFixedSizeBinary(a)?gi(c[b]):K.isBool(a)?Mg(c[b]):K.isUtf8(a)||K.isLargeUtf8(a)?xa.encode(c[b].join("")):u(Uint8Array,u(a.ArrayType,c[b].map(d=>+d)))}}function gi(a){a=a.join("");const b=new Uint8Array(a.length/2);for(let c=0;c<a.length;c+=2)b[c>>1]=Number.parseInt(a.slice(c,c+2),16);return b};class hi extends Ah{constructor(a){super(a);this.o=new th(Uint8Array)}get byteLength(){let a=this.J+4*this.length;this.j&&(a+=this.j.byteLength);this.o&&(a+=this.o.byteLength);this.D&&(a+=this.D.byteLength);return a}setValue(a,b){return super.setValue(a,u(Uint8Array,b))}U(a,b){const c=this.j;b=sh(this.o,b).buffer;let d=0;for(const [e,f]of a)void 0===f?c.set(e,0):(a=f.length,b.set(f,d),c.set(e,a),d+=a)}};class ii extends Ah{constructor(a){super(a);this.o=new th(Uint8Array)}get byteLength(){let a=this.J+4*this.length;this.j&&(a+=this.j.byteLength);this.o&&(a+=this.o.byteLength);this.D&&(a+=this.D.byteLength);return a}setValue(a,b){return super.setValue(a,u(Uint8Array,b))}U(a,b){const c=this.j;b=sh(this.o,b).buffer;let d=0;for(const [e,f]of a)void 0===f?c.set(e,BigInt(0)):(a=f.length,b.set(f,d),c.set(e,BigInt(a)),d+=a)}};class ji extends xh{constructor(a){super(a);this.o=new vh}setValue(a,b){this.o.set(a,+b)}};class ki extends yh{}ki.prototype.A=mf;class li extends ki{}li.prototype.A=gf;class mi extends ki{}mi.prototype.A=hf;class ni extends yh{}ni.prototype.A=xf;class oi extends xh{constructor({type:a,nullValues:b,dictionaryHashFunction:c}){super({type:new Oe(a.dictionary,a.indices,a.id,a.isOrdered)});this.D=null;this.za=0;this.rb=Object.create(null);this.indices=pi({type:this.type.indices,nullValues:b});this.dictionary=pi({type:this.type.dictionary,nullValues:null});"function"===typeof c&&(this.valueToKey=c)}get values(){return this.indices.values}get nullCount(){return this.indices.nullCount}get nullBitmap(){return this.indices.nullBitmap}get byteLength(){return this.indices.byteLength+
this.dictionary.byteLength}get reservedLength(){return this.indices.reservedLength+this.dictionary.reservedLength}get reservedByteLength(){return this.indices.reservedByteLength+this.dictionary.reservedByteLength}isValid(a){return this.indices.isValid(a)}setValid(a,b){const c=this.indices;b=c.setValid(a,b);this.length=c.length;return b}setValue(a,b){const c=this.rb,d=this.valueToKey(b);let e=c[d];void 0===e&&(c[d]=e=this.za+this.dictionary.append(b).length-1);return this.indices.setValue(a,e)}flush(){var a=
this.type;const b=this.Nb,c=this.dictionary.toVector();a=this.indices.flush().clone(a);a.dictionary=b?b.concat(c):c;this.finished||(this.za+=c.length);this.Nb=a.dictionary;this.clear();return a}finish(){this.indices.finish();this.dictionary.finish();this.za=0;this.rb=Object.create(null);return super.finish()}clear(){this.indices.clear();this.dictionary.clear();return super.clear()}valueToKey(a){return"string"===typeof a?a:`${a}`}};class qi extends yh{}qi.prototype.A=jf;class ri extends xh{setValue(a,b){const [c]=this.children;a*=this.stride;for(let d=-1,e=b.length;++d<e;)c.set(a+d,b[d])}addChild(a,b="0"){if(0<this.numChildren)throw Error("FixedSizeListBuilder can only have one child.");const c=this.children.push(a);this.type=new Fe(this.type.listSize,new U(b,a.type,!0));return c}};class si extends yh{setValue(a,b){this.o.set(a,b)}}class ti extends si{setValue(a,b){super.setValue(a,$e(b))}}class ui extends si{}class vi extends si{};class wi extends yh{}wi.prototype.A=Hf;class xi extends wi{}xi.prototype.A=Ff;class yi extends wi{}yi.prototype.A=Gf;class zi extends yh{}zi.prototype.A=Mf;class Ai extends zi{}Ai.prototype.A=If;class Bi extends zi{}Bi.prototype.A=Jf;class Ci extends zi{}Ci.prototype.A=Kf;class Di extends zi{}Di.prototype.A=Lf;class Ei extends yh{setValue(a,b){this.o.set(a,b)}}class Fi extends Ei{}class Gi extends Ei{}class Hi extends Ei{}class Ii extends Ei{}class Ji extends Ei{}class Ki extends Ei{}class Li extends Ei{}class Mi extends Ei{};class Ni extends Ah{constructor(a){super(a);this.j=new wh(a.type)}addChild(a,b="0"){if(0<this.numChildren)throw Error("ListBuilder can only have one child.");this.children[this.numChildren]=a;this.type=new ne(new U(b,a.type,!0));return this.numChildren-1}U(a){const b=this.j,[c]=this.children;for(const [d,e]of a)if("undefined"===typeof e)b.set(d,0);else{a=e;const f=a.length,g=b.set(d,f).buffer[d];for(let k=-1;++k<f;)c.set(g+k,a[k])}}};class Oi extends Ah{set(a,b){return super.set(a,b)}setValue(a,b){b=b instanceof Map?b:new Map(Object.entries(b));const c=this.G||(this.G=new Map),d=c.get(a);d&&(this.J-=d.size);this.J+=b.size;c.set(a,b)}addChild(a,b=`${this.numChildren}`){if(0<this.numChildren)throw Error("ListBuilder can only have one child.");this.children[this.numChildren]=a;this.type=new Je(new U(b,a.type,!0),this.type.keysSorted);return this.numChildren-1}U(a){const b=this.j,[c]=this.children;for(const [d,e]of a)if(void 0===
e)b.set(d,0);else{let {[d]:f,[d+1]:g}=b.set(d,e.size).buffer;for(const k of e.entries())if(c.set(f,k),++f>=g)break}}};class Pi extends xh{setValue(){}setValid(a,b){this.length=Math.max(a+1,this.length);return b}};class Qi extends xh{setValue(a,b){const c=this.children,d=this.type;switch(Array.isArray(b)||b.constructor){case !0:return d.children.forEach((e,f)=>c[f].set(a,b[f]));case Map:return d.children.forEach((e,f)=>c[f].set(a,b.get(e.name)));default:return d.children.forEach((e,f)=>c[f].set(a,b[e.name]))}}setValid(a,b){super.setValid(a,b)||this.children.forEach(c=>c.setValid(a,b));return b}addChild(a,b=`${this.numChildren}`){const c=this.children.push(a);this.type=new re([...this.type.children,new U(b,
a.type,!0)]);return c}};class Ri extends yh{}Ri.prototype.A=rf;class Si extends Ri{}Si.prototype.A=nf;class Ti extends Ri{}Ti.prototype.A=of;class Ui extends Ri{}Ui.prototype.A=pf;class Vi extends Ri{}Vi.prototype.A=qf;class Wi extends yh{}Wi.prototype.A=wf;class Xi extends Wi{}Xi.prototype.A=sf;class Yi extends Wi{}Yi.prototype.A=tf;class Zi extends Wi{}Zi.prototype.A=uf;class $i extends Wi{}$i.prototype.A=vf;class aj extends xh{constructor(a){super(a);this.$=new uh(Int8Array,0,1);"function"===typeof a.valueToChildTypeId&&(this.tb=a.valueToChildTypeId)}get typeIdToChildIndex(){return this.type.typeIdToChildIndex}append(a,b){return this.set(this.length,a,b)}set(a,b,c){void 0===c&&(c=this.tb(this,b,a));this.setValue(a,b,c);return this}setValue(a,b,c){this.$.set(a,c);let d;null==(d=this.children[this.type.typeIdToChildIndex[c]])||d.set(a,b)}addChild(a,b=`${this.children.length}`){const c=this.children.push(a),
d=this.type,e=d.mode,f=d.typeIds;a=[...d.children,new U(b,a.type)];this.type=new ve(e,[...f,c],a);return c}tb(){throw Error("Cannot map UnionBuilder value to child typeId. Pass the `childTypeId` as the second argument to unionBuilder.append(), or supply a `valueToChildTypeId` function as part of the UnionBuilder constructor options.");}}class bj extends aj{}
class cj extends aj{constructor(a){super(a);this.j=new uh(Int32Array)}setValue(a,b,c){c=this.$.set(a,c).buffer[a];c=this.getChildAt(this.type.typeIdToChildIndex[c]);a=this.j.set(a,c.length).buffer[a];null==c||c.set(a,b)}};class dj extends Ah{constructor(a){super(a);this.o=new th(Uint8Array)}get byteLength(){let a=this.J+4*this.length;this.j&&(a+=this.j.byteLength);this.o&&(a+=this.o.byteLength);this.D&&(a+=this.D.byteLength);return a}setValue(a,b){return super.setValue(a,xa.encode(b))}U(){}}dj.prototype.U=hi.prototype.U;class ej extends Ah{constructor(a){super(a);this.o=new th(Uint8Array)}get byteLength(){let a=this.J+4*this.length;this.j&&(a+=this.j.byteLength);this.o&&(a+=this.o.byteLength);this.D&&(a+=this.D.byteLength);return a}setValue(a,b){return super.setValue(a,xa.encode(b))}U(){}}ej.prototype.U=ii.prototype.U;class fj extends Te{visitNull(){return Pi}visitBool(){return ji}visitInt(){return Ei}visitInt8(){return Fi}visitInt16(){return Gi}visitInt32(){return Hi}visitInt64(){return Ii}visitUint8(){return Ji}visitUint16(){return Ki}visitUint32(){return Li}visitUint64(){return Mi}visitFloat(){return si}visitFloat16(){return ti}visitFloat32(){return ui}visitFloat64(){return vi}visitUtf8(){return dj}visitLargeUtf8(){return ej}visitBinary(){return hi}visitLargeBinary(){return ii}visitFixedSizeBinary(){return qi}visitDate(){return ki}visitDateDay(){return li}visitDateMillisecond(){return mi}visitTimestamp(){return Ri}visitTimestampSecond(){return Si}visitTimestampMillisecond(){return Ti}visitTimestampMicrosecond(){return Ui}visitTimestampNanosecond(){return Vi}visitTime(){return Wi}visitTimeSecond(){return Xi}visitTimeMillisecond(){return Yi}visitTimeMicrosecond(){return Zi}visitTimeNanosecond(){return $i}visitDecimal(){return ni}visitList(){return Ni}visitStruct(){return Qi}visitUnion(){return aj}visitDenseUnion(){return cj}visitSparseUnion(){return bj}visitDictionary(){return oi}visitInterval(){return wi}visitIntervalDayTime(){return xi}visitIntervalYearMonth(){return yi}visitDuration(){return zi}visitDurationSecond(){return Ai}visitDurationMillisecond(){return Bi}visitDurationMicrosecond(){return Ci}visitDurationNanosecond(){return Di}visitFixedSizeList(){return ri}visitMap(){return Oi}}
const gj=new fj;function hj(a,b,c){return b===c||Array.isArray(b)&&Array.isArray(c)&&b.length===c.length&&b.every((d,e)=>a.compareFields(d,c[e]))}class kj extends Te{compareSchemas(a,b){return a===b||b instanceof a.constructor&&hj(this,a.fields,b.fields)}compareFields(a,b){return a===b||b instanceof a.constructor&&a.name===b.name&&a.nullable===b.nullable&&this.visit(a.type,b.type)}}function lj(a,b){return b instanceof a.constructor}function mj(a,b){return a===b||lj(a,b)}
function nj(a,b){return a===b||lj(a,b)&&a.bitWidth===b.bitWidth&&a.isSigned===b.isSigned}function oj(a,b){return a===b||lj(a,b)&&a.precision===b.precision}function pj(a,b){return a===b||lj(a,b)&&a.unit===b.unit}function qj(a,b){return a===b||lj(a,b)&&a.unit===b.unit&&a.timezone===b.timezone}function rj(a,b){return a===b||lj(a,b)&&a.unit===b.unit&&a.bitWidth===b.bitWidth}
function sj(a,b){return a===b||lj(a,b)&&a.mode===b.mode&&a.typeIds.every((c,d)=>c===b.typeIds[d])&&hj(tj,a.children,b.children)}function uj(a,b){return a===b||lj(a,b)&&a.unit===b.unit}function vj(a,b){return a===b||lj(a,b)&&a.unit===b.unit}h=kj.prototype;h.visitNull=mj;h.visitBool=mj;h.visitInt=nj;h.visitInt8=nj;h.visitInt16=nj;h.visitInt32=nj;h.visitInt64=nj;h.visitUint8=nj;h.visitUint16=nj;h.visitUint32=nj;h.visitUint64=nj;h.visitFloat=oj;h.visitFloat16=oj;h.visitFloat32=oj;h.visitFloat64=oj;
h.visitUtf8=mj;h.visitLargeUtf8=mj;h.visitBinary=mj;h.visitLargeBinary=mj;h.visitFixedSizeBinary=function(a,b){return a===b||lj(a,b)&&a.byteWidth===b.byteWidth};h.visitDate=pj;h.visitDateDay=pj;h.visitDateMillisecond=pj;h.visitTimestamp=qj;h.visitTimestampSecond=qj;h.visitTimestampMillisecond=qj;h.visitTimestampMicrosecond=qj;h.visitTimestampNanosecond=qj;h.visitTime=rj;h.visitTimeSecond=rj;h.visitTimeMillisecond=rj;h.visitTimeMicrosecond=rj;h.visitTimeNanosecond=rj;h.visitDecimal=mj;
h.visitList=function(a,b){return a===b||lj(a,b)&&a.children.length===b.children.length&&hj(tj,a.children,b.children)};h.visitStruct=function(a,b){return a===b||lj(a,b)&&a.children.length===b.children.length&&hj(tj,a.children,b.children)};h.visitUnion=sj;h.visitDenseUnion=sj;h.visitSparseUnion=sj;h.visitDictionary=function(a,b){return a===b||lj(a,b)&&a.id===b.id&&a.isOrdered===b.isOrdered&&tj.visit(a.indices,b.indices)&&tj.visit(a.dictionary,b.dictionary)};h.visitInterval=uj;
h.visitIntervalDayTime=uj;h.visitIntervalYearMonth=uj;h.visitDuration=vj;h.visitDurationSecond=vj;h.visitDurationMillisecond=vj;h.visitDurationMicrosecond=vj;h.visitDurationNanosecond=vj;h.visitFixedSizeList=function(a,b){return a===b||lj(a,b)&&a.listSize===b.listSize&&a.children.length===b.children.length&&hj(tj,a.children,b.children)};h.visitMap=function(a,b){return a===b||lj(a,b)&&a.keysSorted===b.keysSorted&&a.children.length===b.children.length&&hj(tj,a.children,b.children)};const tj=new kj;
function wj(a,b){return tj.compareSchemas(a,b)}function xj(a,b){return tj.visit(a,b)};function pi(a){var b=a.type;const c=new (gj.getVisitFn(b)())(a);if(b.children&&0<b.children.length){const d=a.children||[],e={nullValues:a.nullValues};a=Array.isArray(d)?(f,g)=>d[g]||e:({name:f})=>d[f]||e;for(const [f,g]of b.children.entries()){b=g.type;const k=a(g,f);c.children.push(pi(Object.assign({},k,{type:b})))}}return c}
function yj(a,b){if(a instanceof O||a instanceof M||a.type instanceof K||ArrayBuffer.isView(a))return oh(a);b={type:null!=b?b:zj(a),nullValues:[null]};a=[...Aj(b)(a)];a=1===a.length?a[0]:a.reduce((c,d)=>c.concat(d));return K.isDictionary(a.type)?a.memoize():a}
function zj(a){if(0===a.length)return new Nc;var b=0;let c=0,d=0,e=0,f=0,g=0,k=0,m=0;for(const p of a)if(null==p)++b;else{switch(typeof p){case "bigint":++g;continue;case "boolean":++k;continue;case "number":++e;continue;case "string":++f;continue;case "object":Array.isArray(p)?++c:"[object Date]"===Object.prototype.toString.call(p)?++m:++d;continue}throw new TypeError("Unable to infer Vector type from input values, explicit type declaration expected.");}if(e+b===a.length)return new fd;if(f+b===a.length)return new Oe(new od,
new Uc);if(g+b===a.length)return new Vc;if(k+b===a.length)return new wd;if(m+b===a.length)return new Xd;if(c+b===a.length){const p=zj(a[a.findIndex(r=>null!=r)]);if(a.every(r=>null==r||xj(p,zj(r))))return new ne(new U("",p,!0))}else if(d+b===a.length){b=new Map;for(const p of a)for(const r of Object.keys(p))b.has(r)||null==p[r]||b.set(r,new U(r,zj([p[r]]),!0));return new re([...b.values()])}throw new TypeError("Unable to infer Vector type from input values, explicit type declaration expected.");}
function Aj(a){const {queueingStrategy:b="count"}=a,{highWaterMark:c="bytes"!==b?Number.POSITIVE_INFINITY:Math.pow(2,14)}=a,d="bytes"!==b?"length":"byteLength";return function*(e){let f=0;const g=pi(a);for(const k of e)g.append(k)[d]>=c&&++f&&(yield g.toVector());if(0<g.finish().length||0===f)yield g.toVector()}};function Bj(a,b){return Cj(a,b.map(c=>c.data.concat()))}function Cj(a,b){const c=[...a.fields],d=[],e={eb:b.reduce((T,sa)=>Math.max(T,sa.length),0)};let f=0,g=0,k=-1;const m=b.length;let p,r=[];for(;0<e.eb--;){g=Number.POSITIVE_INFINITY;for(k=-1;++k<m;)r[k]=p=b[k].shift(),g=Math.min(g,p?p.length:g);Number.isFinite(g)&&(r=Dj(c,g,r,b,e),0<g&&(d[f++]=P({type:new re(c),length:g,nullCount:0,children:r.slice()})))}return[a=a.assign(c),d.map(T=>new X(a,T))]}
function Dj(a,b,c,d,e){const f=(b+63&-64)>>3;for(let k=-1,m=d.length;++k<m;){const p=c[k];let r;var g=null==(r=p)?void 0:r.length;if(g>=b)g===b?c[k]=p:(c[k]=p.slice(0,b),e.eb=Math.max(e.eb,d[k].unshift(p.slice(b,g-b))));else{g=a[k];a[k]=g.clone({nullable:!0});let T,sa;c[k]=null!=(sa=null==(T=p)?void 0:Tg(T,b))?sa:P({type:g.type,length:b,nullCount:b,nullBitmap:new Uint8Array(f)})}}return c};class Ej{constructor(...a){if(0===a.length)return this.batches=[],this.schema=new S([]),this.j=[0],this;let b,c;a[0]instanceof S&&(b=a.shift());a.at(-1)instanceof Uint32Array&&(c=a.pop());const d=m=>{if(m){if(m instanceof X)return[m];if(m instanceof Ej)return m.batches;if(m instanceof O){if(m.type instanceof re)return[new X(new S(m.type.children),m)]}else{if(Array.isArray(m))return m.flatMap(r=>d(r));if("function"===typeof m[Symbol.iterator])return[...m].flatMap(r=>d(r));if("object"===typeof m){var p=
Object.keys(m);const r=p.map(Ia=>new M([m[Ia]]));let T;const sa=null!=(T=b)?T:new S(p.map((Ia,Jc)=>new U(String(Ia),r[Jc].type,r[Jc].nullable)));[,p]=Bj(sa,r);return 0===p.length?[new X(m)]:p}}}return[]};a=a.flatMap(m=>d(m));let e,f,g;b=null!=(g=null!=(f=b)?f:null==(e=a[0])?void 0:e.schema)?g:new S([]);if(!(b instanceof S))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");for(const m of a){if(!(m instanceof X))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");
if(!wj(b,m.schema))throw new TypeError("Table and inner RecordBatch schemas must be equivalent.");}this.schema=b;this.batches=a;let k;this.j=null!=(k=c)?k:Zg(this.data)}get data(){return this.batches.map(({data:a})=>a)}get numCols(){return this.schema.fields.length}get numRows(){return this.data.reduce((a,b)=>a+b.length,0)}get nullCount(){-1===this.O&&(this.O=Yg(this.data));return this.O}isValid(){return!1}get(){return null}at(a){return this.get(0>a?this.numRows+a:a)}set(){}indexOf(){return-1}[Symbol.iterator](){return 0<
this.batches.length?kh.visit(new M(this.data)):[][Symbol.iterator]()}toArray(){return[...this]}toString(){return`[\n  ${this.toArray().join(",\n  ")}\n]`}concat(...a){const b=this.schema;a=this.data.concat(a.flatMap(({data:c})=>c));return new Ej(b,a.map(c=>new X(b,c)))}slice(a,b){const c=this.schema;[a,b]=zg({length:this.numRows},a,b);a=$g(this.data,this.j,a,b);return new Ej(c,a.map(d=>new X(c,d)))}getChild(a){return this.getChildAt(this.schema.fields.findIndex(b=>b.name===a))}getChildAt(a){if(-1<
a&&a<this.schema.fields.length){const c=this.data.map(d=>d.children[a]);if(0===c.length){var {type:b}=this.schema.fields[a];b=P({type:b,length:0,nullCount:0});c.push(Tg(b,this.numRows))}return new M(c)}return null}setChild(a,b){let c;return this.setChildAt(null==(c=this.schema.fields)?void 0:c.findIndex(d=>d.name===a),b)}setChildAt(a,b){let c=this.schema;var d=[...this.batches];if(-1<a&&a<this.numCols){b||(b=new M([P({type:new Nc,length:this.numRows})]));d=c.fields.slice();const e=d[a].clone({type:b.type}),
f=this.schema.fields.map((g,k)=>this.getChildAt(k));[d[a],f[a]]=[e,b];[c,d]=Bj(c,f)}return new Ej(c,d)}select(a){const b=this.schema.fields.reduce((c,d,e)=>c.set(d.name,e),new Map);return this.selectAt(a.map(c=>b.get(c)).filter(c=>-1<c))}selectAt(a){const b=this.schema.selectAt(a),c=this.batches.map(d=>d.selectAt(a));return new Ej(b,c)}assign(a){const b=this.schema.fields,[c,d]=a.schema.fields.reduce((g,k,m)=>{const [p,r]=g,T=b.findIndex(sa=>sa.name===k.name);~T?r[T]=m:p.push(m);return g},[[],[]]),
e=this.schema.assign(a.schema),f=[...b.map((g,k)=>[k,d[k]]).map(([g,k])=>void 0===k?this.getChildAt(g):a.getChildAt(k)),...c.map(g=>a.getChildAt(g))].filter(Boolean);return new Ej(...Bj(e,f))}}var Fj=Symbol.toStringTag,Gj=Ej.prototype;Gj.schema=null;Gj.batches=[];Gj.j=new Uint32Array([0]);Gj.O=-1;Gj[Symbol.isConcatSpreadable]=!0;Gj.isValid=ch(bh);Gj.get=ch(Rf.getVisitFn(H.Struct));Gj.set=dh(Cf.getVisitFn(H.Struct));Gj.indexOf=eh(hh.getVisitFn(H.Struct));Ej[Fj]="Table";class X{constructor(...a){switch(a.length){case 2:[this.schema]=a;if(!(this.schema instanceof S))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");[,this.data=P({nullCount:0,type:new re(this.schema.fields),children:this.schema.fields.map(g=>P({type:g.type,nullCount:0}))})]=a;if(!(this.data instanceof O))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");[this.schema,this.data]=Hj(this.schema,this.data.children);break;case 1:const [b]=a,{fields:c,
children:d,length:e}=Object.keys(b).reduce((g,k,m)=>{g.children[m]=b[k];g.length=Math.max(g.length,b[k].length);g.fields[m]=U.new({name:k,type:b[k].type,nullable:!0});return g},{length:0,fields:[],children:[]});a=new S(c);const f=P({type:new re(c),length:e,children:d,nullCount:0});[this.schema,this.data]=Hj(a,f.children,e);break;default:throw new TypeError("RecordBatch constructor expects an Object mapping names to child Data, or a [Schema, Data] pair.");}}get dictionaries(){return this.Mb||(this.Mb=
Ij(this.schema.fields,this.data.children))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.length}get nullCount(){return this.data.nullCount}isValid(a){return this.data.getValid(a)}get(a){return Rf.visit(this.data,a)}at(a){return this.get(0>a?this.numRows+a:a)}set(a,b){return Cf.visit(this.data,a,b)}indexOf(a,b){return hh.visit(this.data,a,b)}[Symbol.iterator](){return kh.visit(new M([this.data]))}toArray(){return[...this]}concat(...a){return new Ej(this.schema,[this,
...a])}slice(a,b){[a]=(new M([this.data])).slice(a,b).data;return new X(this.schema,a)}getChild(a){let b;return this.getChildAt(null==(b=this.schema.fields)?void 0:b.findIndex(c=>c.name===a))}getChildAt(a){return-1<a&&a<this.schema.fields.length?new M([this.data.children[a]]):null}setChild(a,b){let c;return this.setChildAt(null==(c=this.schema.fields)?void 0:c.findIndex(d=>d.name===a),b)}setChildAt(a,b){var c=this.schema,d=this.data;if(-1<a&&a<this.numCols){b||(b=new M([P({type:new Nc,length:this.numRows})]));
const e=c.fields.slice();d=d.children.slice();c=e[a].clone({type:b.type});[e[a],d[a]]=[c,b.data[0]];c=new S(e,new Map(this.schema.metadata));d=P({type:new re(e),children:d})}return new X(c,d)}select(a){const b=this.schema.select(a),c=new re(b.fields),d=[];for(const e of a)a=this.schema.fields.findIndex(f=>f.name===e),~a&&(d[a]=this.data.children[a]);return new X(b,P({type:c,length:this.numRows,children:d}))}selectAt(a){const b=this.schema.selectAt(a);a=a.map(c=>this.data.children[c]).filter(Boolean);
a=P({type:new re(b.fields),length:this.numRows,children:a});return new X(b,a)}}var Jj=Symbol.toStringTag,Kj=X.prototype;Kj.O=-1;Kj[Symbol.isConcatSpreadable]=!0;X[Jj]="RecordBatch";
function Hj(a,b,c=b.reduce((d,e)=>Math.max(d,e.length),0)){const d=[...a.fields],e=[...b],f=(c+63&-64)>>3;for(const [g,k]of a.fields.entries()){const m=b[g];if(!m||m.length!==c){d[g]=k.clone({nullable:!0});let p,r;e[g]=null!=(r=null==(p=m)?void 0:Tg(p,c))?r:P({type:k.type,length:c,nullCount:c,nullBitmap:new Uint8Array(f)})}}return[a.assign(d),P({type:new re(d),length:c,children:e})]}
function Ij(a,b,c=new Map){var d;if(0<(null!=(d=null==a?void 0:a.length)?d:0)&&(null==a?void 0:a.length)===(null==b?void 0:b.length))for(let f=-1,g=a.length;++f<g;){var {type:e}=a[f];d=b[f];let k,m;for(const p of[d,...((null==(k=d)?void 0:null==(m=k.dictionary)?void 0:m.data)||[])]){let r;Ij(e.children,null==(r=p)?void 0:r.children,c)}if(K.isDictionary(e))if(e=e.id,!c.has(e)){let p;(null==(p=d)?0:p.dictionary)&&c.set(e,d.dictionary)}else if(c.get(e)!==d.dictionary)throw Error("Cannot create Schema containing two different dictionaries with the same Id");
}return c}class Lj extends X{constructor(a){var b=a.fields.map(c=>P({type:c.type}));b=P({type:new re(a.fields),nullCount:0,children:b});super(a,b)}};class Mj{constructor(){this.g=null;this.h=0}i(a,b){this.h=a;this.g=b;return this}version(){const a=A(this.g,this.h,4);return a?eb(this.g,this.h+a):w.V1}headerType(){const a=A(this.g,this.h,6);return a?this.g.l[this.h+a]:G.NONE}header(a){const b=A(this.g,this.h,8);return b?gb(this.g,a,this.h+b):null}bodyLength(){const a=A(this.g,this.h,10);return a?fb(this.g,this.h+a):BigInt("0")}Ga(a){const b=A(this.g,this.h,12);return b?(new Ub).i(ib(this.g,jb(this.g,this.h+b)+4*a),this.g):null}Ha(){const a=A(this.g,
this.h,12);return a?kb(this.g,this.h+a):0}};class Nj extends Te{visit(a,b){return null==a||null==b?void 0:super.visit(a,b)}visitNull(a,b){C(b,0);return D(b)}visitInt(a,b){C(b,2);tb(b,0,a.bitWidth,0);rb(b,1,+a.isSigned,0);return D(b)}visitFloat(a,b){C(b,1);sb(b,a.precision,y.HALF);return D(b)}visitBinary(a,b){C(b,0);return D(b)}visitLargeBinary(a,b){C(b,0);return D(b)}visitBool(a,b){C(b,0);return D(b)}visitUtf8(a,b){C(b,0);return D(b)}visitLargeUtf8(a,b){C(b,0);return D(b)}visitDecimal(a,b){C(b,3);tb(b,1,a.scale,0);tb(b,0,a.precision,0);tb(b,
2,a.bitWidth,128);return D(b)}visitDate(a,b){C(b,1);sb(b,a.unit,Xa.MILLISECOND);return D(b)}visitTime(a,b){C(b,2);sb(b,a.unit,z.MILLISECOND);tb(b,1,a.bitWidth,32);return D(b)}visitTimestamp(a,b){const c=a.timezone&&zb(b,a.timezone)||void 0;C(b,2);sb(b,a.unit,z.SECOND);void 0!==c&&B(b,1,c);return D(b)}visitInterval(a,b){C(b,1);sb(b,a.unit,$a.YEAR_MONTH);return D(b)}visitDuration(a,b){C(b,1);sb(b,a.unit,z.MILLISECOND);return D(b)}visitList(a,b){C(b,0);return D(b)}visitStruct(a,b){C(b,0);return D(b)}visitUnion(a,
b){xb(b,4,a.typeIds.length,4);var c=a.typeIds;xb(b,4,c.length,4);for(let d=c.length-1;0<=d;d--)qb(b,c[d]);c=yb(b);C(b,2);sb(b,a.mode,x.Sparse);B(b,1,c);return D(b)}visitDictionary(a,b){const c=this.visit(a.indices,b);C(b,4);ub(b,0,BigInt(a.id));rb(b,2,+a.isOrdered,0);void 0!==c&&B(b,1,c);return D(b)}visitFixedSizeBinary(a,b){C(b,1);tb(b,0,a.byteWidth,0);return D(b)}visitFixedSizeList(a,b){C(b,1);tb(b,0,a.listSize,0);return D(b)}visitMap(a,b){C(b,1);rb(b,0,+a.keysSorted,0);return D(b)}}const Oj=new Nj;function Pj(a){return new Qj(a.count,Rj(a.columns),Sj(a.columns))}function Tj(a,b){return(a.fields||[]).filter(Boolean).map(c=>U.fromJSON(c,b))}function Uj(a,b){return(a.children||[]).filter(Boolean).map(c=>U.fromJSON(c,b))}function Rj(a){return(a||[]).reduce((b,c)=>[...b,new Vj(c.count,Wj(c.VALIDITY)),...Rj(c.children)],[])}
function Sj(a,b=[]){for(let c=-1,d=(a||[]).length;++c<d;){const e=a[c];e.VALIDITY&&b.push(new Xj(b.length,e.VALIDITY.length));e.TYPE_ID&&b.push(new Xj(b.length,e.TYPE_ID.length));e.OFFSET&&b.push(new Xj(b.length,e.OFFSET.length));e.DATA&&b.push(new Xj(b.length,e.DATA.length));b=Sj(e.children,b)}return b}function Wj(a){return(a||[]).reduce((b,c)=>b+ +(0===c),0)}function Yj(a=[]){return new Map(a.map(({key:b,value:c})=>[b,c]))}function Zj(a){return new Oc(a.isSigned,a.bitWidth)}
function ak(a,b){const c=a.type.name;switch(c){case "NONE":return new Nc;case "null":return new Nc;case "binary":return new gd;case "largebinary":return new kd;case "utf8":return new od;case "largeutf8":return new sd;case "bool":return new wd;case "list":return new ne((b||[])[0]);case "struct":return new re(b||[]);case "struct_":return new re(b||[])}switch(c){case "int":return b=a.type,new Oc(b.isSigned,b.bitWidth);case "floatingpoint":return new $c(y[a.type.precision]);case "decimal":return b=a.type,
new Ad(b.scale,b.precision,b.bitWidth);case "date":return new Ed(Xa[a.type.unit]);case "time":return b=a.type,new Kd(z[b.unit],b.bitWidth);case "timestamp":return b=a.type,new Sd(z[b.unit],b.timezone);case "interval":return new $d($a[a.type.unit]);case "duration":return new fe(z[a.type.unit]);case "union":a=a.type;const [d,...e]=(a.mode+"").toLowerCase();return new ve(x[d.toUpperCase()+e.join("")],a.typeIds||[],b||[]);case "fixedsizebinary":return new Be(a.type.byteWidth);case "fixedsizelist":return new Fe(a.type.listSize,
(b||[])[0]);case "map":return new Je((b||[])[0],a.type.keysSorted)}throw Error(`Unrecognized type: "${c}"`);};class bk{static fromJSON(a,b){const c=new bk(0,w.V5,b);c.Pa=ck(a,b);return c}static decode(a){a=new lb(u(Uint8Array,a));a=(new Mj).i(a.u(a.position())+a.position(),a);var b=a.bodyLength();const c=a.version(),d=a.headerType();b=new bk(b,c,d);b.Pa=dk(a,d);return b}static encode(a){const b=new Ab;let c=-1;a.isSchema()?c=S.encode(b,a.header()):a.isRecordBatch()?c=Qj.encode(b,a.header()):a.isDictionaryBatch()&&(c=ek.encode(b,a.header()));C(b,5);sb(b,w.V5,w.V1);B(b,2,c);rb(b,1,a.headerType,G.NONE);ub(b,
3,BigInt(a.bodyLength));a=D(b);b.finish(a);return mb(b)}static from(a,b=0){if(a instanceof S)return new bk(0,w.V5,G.Schema,a);if(a instanceof Qj)return new bk(b,w.V5,G.RecordBatch,a);if(a instanceof ek)return new bk(b,w.V5,G.DictionaryBatch,a);throw Error(`Unrecognized Message header: ${a}`);}get type(){return this.headerType}get version(){return this.Tb}get headerType(){return this.Pb}get bodyLength(){return this.Jb}header(){return this.Pa()}isSchema(){return this.headerType===G.Schema}isRecordBatch(){return this.headerType===
G.RecordBatch}isDictionaryBatch(){return this.headerType===G.DictionaryBatch}constructor(a,b,c,d){this.Tb=b;this.Pb=c;this.body=new Uint8Array(0);d&&(this.Pa=()=>d);this.Jb=J(a)}}class Qj{get M(){return this.Ta}get length(){return this.Sb}get buffers(){return this.Na}constructor(a,b,c){this.Ta=b;this.Na=c;this.Sb=J(a)}}
class ek{get id(){return this.Qb}get data(){return this.Lb}get Ja(){return this.Rb}get length(){return this.data.length}get M(){return this.data.M}get buffers(){return this.data.buffers}constructor(a,b,c=!1){this.Lb=a;this.Rb=c;this.Qb=J(b)}}class Xj{constructor(a,b){this.offset=J(a);this.length=J(b)}}class Vj{constructor(a,b){this.length=J(a);this.nullCount=J(b)}}
function ck(a,b){return()=>{switch(b){case G.Schema:return S.fromJSON(a);case G.RecordBatch:return Qj.fromJSON(a);case G.DictionaryBatch:return ek.fromJSON(a)}throw Error(`Unrecognized Message type: { name: ${G[b]}, type: ${b} }`);}}
function dk(a,b){return()=>{switch(b){case G.Schema:return S.decode(a.header(new mc),new Map,a.version());case G.RecordBatch:return Qj.decode(a.header(new Lb),a.version());case G.DictionaryBatch:return ek.decode(a.header(new Mb),a.version())}throw Error(`Unrecognized Message type: { name: ${G[b]}, type: ${b} }`);}}U.encode=fk;U.decode=gk;
U.fromJSON=function(a,b){var c;let d,e;b&&(e=a.dictionary)?(b.has(c=e.id)?(d=(d=e.indexType)?Zj(d):new Uc,c=new Oe(b.get(c),d,c,e.isOrdered)):(d=(d=e.indexType)?Zj(d):new Uc,b.set(c,b=ak(a,Uj(a,b))),c=new Oe(b,d,c,e.isOrdered)),a=new U(a.name,c,a.nullable,Yj(a.metadata))):(b=ak(a,Uj(a,b)),a=new U(a.name,b,a.nullable,Yj(a.metadata)));return a||null};S.encode=hk;S.decode=ik;S.fromJSON=function(a,b=new Map){return new S(Tj(a,b),Yj(a.metadata),b)};Qj.encode=jk;Qj.decode=kk;Qj.fromJSON=Pj;ek.encode=lk;
ek.decode=mk;ek.fromJSON=function(a){return new ek(Pj(a.data),a.id,a.isDelta)};Vj.encode=nk;Vj.decode=ok;Xj.encode=pk;Xj.decode=qk;function ik(a,b=new Map,c=w.V5){const d=[];for(let e,f=-1,g=-1,k=lc(a);++f<k;)if(e=a.fields(f))d[++g]=U.decode(e,b);return new S(d,rk(a),b,c)}
function kk(a,b=w.V5){var c=A(a.g,a.h,10);if(null!==(c?(new Gb).i(ib(a.g,a.h+c),a.g):null))throw Error("Record batch compression not implemented");c=a.length();const d=[];for(let f,g=-1,k=-1,m=Jb(a);++g<m;)if(f=a.M(g))d[++k]=Vj.decode(f);const e=[];for(let f,g=-1,k=-1,m=Kb(a);++g<m;)if(f=a.buffers(g))b<w.V4&&(f.h+=8*(g+1)),e[++k]=Xj.decode(f);return new Qj(c,d,e)}function mk(a,b=w.V5){return new ek(Qj.decode(a.data(),b),a.id(),a.Ja())}function qk(a){return new Xj(a.offset(),a.length())}
function ok(a){return new Vj(a.length(),a.nullCount())}function sk(a,b){const c=[];for(let d,e=-1,f=-1,g=hc(a);++e<g;)if(d=a.children(e))c[++f]=U.decode(d,b);return c}function gk(a,b){var c;let d,e;b&&(e=a.dictionary())?(b.has(c=J(e.id()))?(d=(d=Sb(e))?tk(d):new Uc,c=new Oe(b.get(c),d,c,e.isOrdered())):(d=(d=Sb(e))?tk(d):new Uc,b.set(c,b=uk(a,sk(a,b))),c=new Oe(b,d,c,e.isOrdered())),a=new U(a.name(),c,a.nullable(),rk(a))):(b=uk(a,sk(a,b)),a=new U(a.name(),b,a.nullable(),rk(a)));return a||null}
function rk(a){const b=new Map;if(a)for(let c,d,e=-1,f=Math.trunc(a.Ha());++e<f;)(c=a.Ga(e))&&null!=(d=c.key())&&b.set(d,c.value());return b}function tk(a){return new Oc(a.isSigned(),a.bitWidth())}
function uk(a,b){var c=(c=A(a.g,a.h,8))?a.g.l[a.h+c]:E.NONE;switch(c){case E.NONE:return new Nc;case E.Null:return new Nc;case E.Binary:return new gd;case E.LargeBinary:return new kd;case E.Utf8:return new od;case E.LargeUtf8:return new sd;case E.Bool:return new wd;case E.List:return new ne((b||[])[0]);case E.Struct_:return new re(b||[])}switch(c){case E.Int:return b=a.type(new Rb),new Oc(b.isSigned(),b.bitWidth());case E.FloatingPoint:return b=a.type(new $b),new $c(b.precision());case E.Decimal:return b=
a.type(new Wb),new Ad(b.scale(),b.precision(),b.bitWidth());case E.Date:return b=a.type(new Vb),new Ed(b.unit());case E.Time:return b=a.type(new cc),new Kd(b.unit(),b.bitWidth());case E.Timestamp:return b=a.type(new dc),new Sd(b.unit(),b.timezone());case E.Interval:return b=a.type(new ac),new $d(b.unit());case E.Duration:return b=a.type(new Xb),new fe(b.unit());case E.Union:c=a.type(new ec);a=c.mode();const d=A(c.g,c.h,6);c=d?new Int32Array(c.g.T().buffer,c.g.T().byteOffset+jb(c.g,c.h+d),kb(c.g,c.h+
d)):null;return new ve(a,c||[],b||[]);case E.FixedSizeBinary:return b=a.type(new Yb),new Be(b.byteWidth());case E.FixedSizeList:return a=a.type(new Zb),new Fe(a.listSize(),(b||[])[0]);case E.Map:return a=a.type(new bc),new Je((b||[])[0],a.keysSorted())}throw Error(`Unrecognized type: "${E[c]}" (${c})`);}
function hk(a,b){var c=b.fields.map(d=>U.encode(a,d));xb(a,4,c.length,4);c=jc(a,c);b=b.metadata&&0<b.metadata.size?kc(a,[...b.metadata].map(([d,e])=>{d=zb(a,`${d}`);e=zb(a,`${e}`);C(a,2);B(a,0,d);B(a,1,e);return D(a)})):-1;C(a,4);B(a,1,c);sb(a,vk?Nb.Ib:Nb.dc,Nb.Ib);-1!==b&&B(a,2,b);return D(a)}
function fk(a,b){let c=-1,d=-1,e=-1;var f=b.type;let g=b.typeId;K.isDictionary(f)?(g=f.dictionary.typeId,e=Oj.visit(f,a),d=Oj.visit(f.dictionary,a)):d=Oj.visit(f,a);f=(f.children||[]).map(m=>U.encode(a,m));f=fc(a,f);const k=b.metadata&&0<b.metadata.size?gc(a,[...b.metadata].map(([m,p])=>{m=zb(a,`${m}`);p=zb(a,`${p}`);C(a,2);B(a,0,m);B(a,1,p);return D(a)})):-1;b.name&&(c=zb(a,b.name));C(a,7);B(a,3,d);rb(a,2,g,E.NONE);B(a,5,f);rb(a,1,+!!b.nullable,0);-1!==c&&B(a,0,c);-1!==e&&B(a,4,e);-1!==k&&B(a,6,
k);return D(a)}function jk(a,b){var c=b.M||[],d=b.buffers||[];xb(a,16,c.length,8);for(const e of c.slice().reverse())Vj.encode(a,e);c=yb(a);xb(a,16,d.length,8);for(const e of d.slice().reverse())Xj.encode(a,e);d=yb(a);C(a,4);ub(a,0,BigInt(b.length));B(a,1,c);B(a,2,d);return D(a)}function lk(a,b){const c=Qj.encode(a,b.data);C(a,3);ub(a,0,BigInt(b.id));rb(a,2,+b.Ja,0);B(a,1,c);return D(a)}
function nk(a,b){var c=BigInt(b.length);b=BigInt(b.nullCount);ob(a,8,16);a.aa(BigInt(null!=b?b:0));a.aa(BigInt(null!=c?c:0));return a.offset()}function pk(a,b){var c=BigInt(b.offset);b=BigInt(b.length);ob(a,8,16);a.aa(BigInt(null!=b?b:0));a.aa(BigInt(null!=c?c:0));return a.offset()}const wk=new ArrayBuffer(2);(new DataView(wk)).setInt16(0,256,!0);const vk=256===(new Int16Array(wk))[0];const xk=a=>`Expected ${G[a]} Message in stream, but was null or length 0.`,yk=a=>`Header pointer of flatbuffer-encoded ${G[a]} Message is null or length 0.`,zk=(a,b)=>`Expected to read ${a} metadata bytes, but only read ${b}.`,Ak=(a,b)=>`Expected to read ${a} bytes for message body, but only read ${b}.`;
class Bk{constructor(a){this.source=a instanceof Qh?a:new Qh(a)}[Symbol.iterator](){return this}next(){let a;return(a=this.readMetadataLength()).done||-1===a.value&&(a=this.readMetadataLength()).done||(a=this.readMetadata(a.value)).done?Jh:a}throw(a){return this.source.throw(a)}return(a){return this.source.return(a)}readMessage(a){let b;if((b=this.next()).done)return null;if(null!=a&&b.value.headerType!==a)throw Error(xk(a));return b.value}readMessageBody(a){if(0>=a)return new Uint8Array(0);const b=
u(Uint8Array,this.source.read(a));if(b.byteLength<a)throw Error(Ak(a,b.byteLength));return 0===b.byteOffset%8&&b.byteOffset+b.byteLength<=b.buffer.byteLength?b:b.slice()}readSchema(a=!1){const b=G.Schema;let c;const d=null==(c=this.readMessage(b))?void 0:c.header();if(a&&!d)throw Error(yk(b));return d}readMetadataLength(){var a=this.source.read(4);let b;a=(null==(b=a&&new lb(a))?void 0:b.u(0))||0;return{done:0===a,value:a}}readMetadata(a){const b=this.source.read(a);if(!b)return Jh;if(b.byteLength<
a)throw Error(zk(a,b.byteLength));return{done:!1,value:bk.decode(b)}}}
class Ck{constructor(a,b){this.source=a instanceof Sh?a:t(a)&&q(a.stat)&&"number"===typeof a.fd?new Vh(a,b):new Sh(a)}[Symbol.asyncIterator](){return this}next(){const a=this;return l(function*(){let b;return(b=yield a.readMetadataLength()).done||-1===b.value&&(b=yield a.readMetadataLength()).done||(b=yield a.readMetadata(b.value)).done?Jh:b})}throw(a){const b=this;return l(function*(){return yield b.source.throw(a)})}return(a){const b=this;return l(function*(){return yield b.source.return(a)})}readMessage(a){const b=
this;return l(function*(){let c;if((c=yield b.next()).done)return null;if(null!=a&&c.value.headerType!==a)throw Error(xk(a));return c.value})}readMessageBody(a){const b=this;return l(function*(){if(0>=a)return new Uint8Array(0);const c=u(Uint8Array,yield b.source.read(a));if(c.byteLength<a)throw Error(Ak(a,c.byteLength));return 0===c.byteOffset%8&&c.byteOffset+c.byteLength<=c.buffer.byteLength?c:c.slice()})}readSchema(a=!1){const b=this;return l(function*(){const c=G.Schema;let d;const e=null==(d=
yield b.readMessage(c))?void 0:d.header();if(a&&!e)throw Error(yk(c));return e})}readMetadataLength(){const a=this;return l(function*(){var b=yield a.source.read(4);let c;b=(null==(c=b&&new lb(b))?void 0:c.u(0))||0;return{done:0===b,value:b}})}readMetadata(a){const b=this;return l(function*(){const c=yield b.source.read(a);if(!c)return Jh;if(c.byteLength<a)throw Error(zk(a,c.byteLength));return{done:!1,value:bk.decode(c)}})}}
class Dk extends Bk{constructor(a){super(new Uint8Array(0));this.S=!1;this.xa=[];this.Z=this.nb=0;this.na=a instanceof Kh?a:new Kh(a)}next(){var a=this.na;if(!this.S)return this.S=!0,{done:!1,value:bk.fromJSON(a.schema,G.Schema)};if(this.Z<a.dictionaries.length)return a=a.dictionaries[this.Z++],this.xa=a.data.columns,{done:!1,value:bk.fromJSON(a,G.DictionaryBatch)};if(this.nb<a.batches.length)return a=a.batches[this.nb++],this.xa=a.columns,{done:!1,value:bk.fromJSON(a,G.RecordBatch)};this.xa=[];return Jh}readMessageBody(){function a(b){return(b||
[]).reduce((c,d)=>[...c,...(d.VALIDITY&&[d.VALIDITY]||[]),...(d.TYPE_ID&&[d.TYPE_ID]||[]),...(d.OFFSET&&[d.OFFSET]||[]),...(d.DATA&&[d.DATA]||[]),...a(d.children)],[])}return a(this.xa)}readMessage(a){let b;if((b=this.next()).done)return null;if(null!=a&&b.value.headerType!==a)throw Error(xk(a));return b.value}readSchema(){const a=G.Schema,b=this.readMessage(a),c=null==b?void 0:b.header();if(!b||!c)throw Error(yk(a));return c}}const Ek=new Uint8Array(6);for(let a=0;6>a;a+=1)Ek[a]="ARROW1".codePointAt(a);
function Fk(a){for(let b=-1,c=Ek.length;++b<c;)if(Ek[b]!==a[0+b])return!1;return!0}const Gk=Ek.length,Hk=Gk+4,Ik=2*Gk+4;class Jk extends Nh{constructor(a){super();this.s=a}get closed(){return this.s.closed}get schema(){return this.s.schema}get autoDestroy(){return this.s.autoDestroy}get dictionaries(){return this.s.dictionaries}get numDictionaries(){return this.s.numDictionaries}get numRecordBatches(){return this.s.numRecordBatches}get footer(){return this.s.isFile()?this.s.footer:null}isSync(){return this.s.isSync()}isAsync(){return this.s.isAsync()}isFile(){return this.s.isFile()}isStream(){return this.s.isStream()}next(){return this.s.next()}throw(a){return this.s.throw(a)}return(a){return this.s.return(a)}cancel(){return this.s.cancel()}reset(a){this.s.reset(a);
this.sb=this.mb=void 0;return this}open(a){a=this.s.open(a);return t(a)&&q(a.then)?a.then(()=>this):this}readRecordBatch(a){return this.s.isFile()?this.s.readRecordBatch(a):null}[Symbol.iterator](){return this.s[Symbol.iterator]()}[Symbol.asyncIterator](){return this.s[Symbol.asyncIterator]()}toDOMStream(){return La(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}toNodeStream(){return Ma(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}static throughNode(){throw Error('"throughNode" not available in this environment');
}static throughDOM(){throw Error('"throughDOM" not available in this environment');}static from(a){return a instanceof Jk?a:t(a)&&t(a.schema)?new Kk(new Lk(a)):t(a)&&q(a.stat)&&"number"===typeof a.fd?Mk(a):t(a)&&q(a.then)?(()=>l(function*(){return yield Jk.from(yield a)}))():t(a)&&za(a.body)||za(a)||Aa(a)||t(a)&&q(a[Symbol.asyncIterator])?Nk(new Sh(a)):Ok(new Qh(a))}static readAll(a){return a instanceof Jk?a.isSync()?Pk(a):Qk(a):t(a)&&t(a.schema)||ArrayBuffer.isView(a)||t(a)&&q(a[Symbol.iterator])||
t(a)&&"done"in a&&"value"in a?Pk(a):Qk(a)}}class Kk extends Jk{constructor(a){super(a);this.s=a}readAll(){return[...this]}[Symbol.iterator](){return this.s[Symbol.iterator]()}[Symbol.asyncIterator](){const a=this;return new oa(function*(){yield new n(1,a[Symbol.iterator]())}())}}
class Rk extends Jk{constructor(a){super(a);this.s=a}readAll(){const a=this;return l(function*(){const b=[];var c;try{for(var d=ia(a);;){var e=yield d.next();if(e.done)break;b.push(e.value)}}catch(g){var f={error:g}}finally{try{e&&!e.done&&(c=d.return)&&(yield c.call(d))}finally{if(f)throw f.error;}}return b})}[Symbol.iterator](){throw Error("AsyncRecordBatchStreamReader is not Iterable");}[Symbol.asyncIterator](){return this.s[Symbol.asyncIterator]()}}
class Sk extends Kk{constructor(a){super(a);this.s=a}}class Tk extends Rk{constructor(a){super(a);this.s=a}}function Uk(a,b,c){c=a.Sa(b,c,a.schema.fields);b=P({type:new re(a.schema.fields),length:b.length,children:c});return new X(a.schema,b)}function Vk(a,b,c){var d=b.id;const e=b.Ja,f=a.schema,g=a.dictionaries.get(d);d=f.dictionaries.get(d);a=a.Sa(b.data,c,[d]);return(g&&e?g.concat(new M(a)):new M(a)).memoize()}
class Wk{get numDictionaries(){return this.Z}get numRecordBatches(){return this.P}constructor(a=new Map){this.closed=!1;this.autoDestroy=!0;this.P=this.Z=0;this.dictionaries=a}isSync(){return!1}isAsync(){return!1}isFile(){return!1}isStream(){return!1}reset(a){this.P=this.Z=0;this.schema=a;this.dictionaries=new Map;return this}Sa(a,b,c){return(new ei(b,a.M,a.buffers,this.dictionaries,this.schema.Ka)).visitMany(c)}}
class Xk extends Wk{constructor(a){super();this.v=t(a)&&t(a.schema)?new Dk(this.I=a):new Bk(this.I=a)}isSync(){return!0}isStream(){return!0}[Symbol.iterator](){return this}cancel(){!this.closed&&(this.closed=!0)&&(this.reset().v.return(),this.dictionaries=this.v=null)}open(a){this.closed||(this.autoDestroy=Yk(this,a),this.schema||(this.schema=this.v.readSchema())||this.cancel());return this}throw(a){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset().v.throw(a):Jh}return(a){return!this.closed&&
this.autoDestroy&&(this.closed=!0)?this.reset().v.return(a):Jh}next(){if(this.closed)return Jh;for(var a,b=this.v;a=this.pa();)if(a.isSchema())this.reset(a.header());else{if(a.isRecordBatch()){this.P++;var c=a.header();b=b.readMessageBody(a.bodyLength);return{done:!1,value:Uk(this,c,b)}}a.isDictionaryBatch()&&(this.Z++,c=a.header(),a=b.readMessageBody(a.bodyLength),a=Vk(this,c,a),this.dictionaries.set(c.id,a))}return this.schema&&0===this.P?(this.P++,{done:!1,value:new Lj(this.schema)}):this.return()}pa(){return this.v.readMessage(void 0)}}
class Zk extends Wk{constructor(a,b){super(b);this.v=new Ck(this.I=a)}isAsync(){return!0}isStream(){return!0}[Symbol.asyncIterator](){return this}cancel(){const a=this;return l(function*(){!a.closed&&(a.closed=!0)&&(yield a.reset().v.return(),a.v=null,a.dictionaries=null)})}open(a){const b=this;return l(function*(){b.closed||(b.autoDestroy=Yk(b,a),b.schema||(b.schema=yield b.v.readSchema())||(yield b.cancel()));return b})}throw(a){const b=this;return l(function*(){return!b.closed&&b.autoDestroy&&
(b.closed=!0)?yield b.reset().v.throw(a):Jh})}return(a){const b=this;return l(function*(){return!b.closed&&b.autoDestroy&&(b.closed=!0)?yield b.reset().v.return(a):Jh})}next(){const a=this;return l(function*(){if(a.closed)return Jh;for(var b,c=a.v;b=yield a.pa();)if(b.isSchema())yield a.reset(b.header());else{if(b.isRecordBatch()){a.P++;var d=b.header();c=yield c.readMessageBody(b.bodyLength);return{done:!1,value:Uk(a,d,c)}}b.isDictionaryBatch()&&(a.Z++,d=b.header(),b=yield c.readMessageBody(b.bodyLength),
b=Vk(a,d,b),a.dictionaries.set(d.id,b))}return a.schema&&0===a.P?(a.P++,{done:!1,value:new Lj(a.schema)}):yield a.return()})}pa(){const a=this;return l(function*(){return yield a.v.readMessage(void 0)})}}
class $k extends Xk{get footer(){return this.m}get numDictionaries(){return this.m?this.m.numDictionaries:0}get numRecordBatches(){return this.m?this.m.numRecordBatches:0}constructor(a){super(a instanceof Uh?a:new Uh(a))}isSync(){return!0}isFile(){return!0}open(a){if(!this.closed&&!this.m){this.schema=(this.m=this.Wa()).schema;for(const b of Fh(this.m))b&&this.Va(this.Z++)}return super.open(a)}readRecordBatch(a){if(this.closed)return null;this.m||this.open();var b;const c=null==(b=this.m)?void 0:
b.ga(a);return c&&this.I.seek(c.offset)&&(b=this.v.readMessage(G.RecordBatch),null==b?0:b.isRecordBatch())?(a=b.header(),b=this.v.readMessageBody(b.bodyLength),Uk(this,a,b)):null}Va(a){var b;const c=null==(b=this.m)?void 0:b.Ia(a);c&&this.I.seek(c.offset)&&(b=this.v.readMessage(G.DictionaryBatch),null==b?0:b.isDictionaryBatch())&&(a=b.header(),b=this.v.readMessageBody(b.bodyLength),b=Vk(this,a,b),this.dictionaries.set(a.id,b))}Wa(){var a=this.I;const b=a.size-Hk,c=a.u(b);a=a.ha(b-c,c);return Gh.decode(a)}pa(){this.m||
this.open();if(this.m&&this.P<this.numRecordBatches){let a;const b=null==(a=this.m)?void 0:a.ga(this.P);if(b&&this.I.seek(b.offset))return this.v.readMessage(void 0)}return null}}
class al extends Zk{get footer(){return this.m}get numDictionaries(){return this.m?this.m.numDictionaries:0}get numRecordBatches(){return this.m?this.m.numRecordBatches:0}constructor(a){var b=[];const c="number"!==typeof b[0]?b.shift():void 0;b=b[0]instanceof Map?b.shift():void 0;super(a instanceof Vh?a:new Vh(a,c),b)}isFile(){return!0}isAsync(){return!0}open(a){const b=this,c=()=>super.open;return l(function*(){if(!b.closed&&!b.m){b.schema=(b.m=yield b.Wa()).schema;for(const d of Fh(b.m))d&&(yield b.Va(b.Z++))}return yield c().call(b,
a)})}readRecordBatch(a){const b=this;return l(function*(){if(b.closed)return null;b.m||(yield b.open());var c,d=null==(c=b.m)?void 0:c.ga(a);return d&&(yield b.I.seek(d.offset))&&(d=yield b.v.readMessage(G.RecordBatch),null==d?0:d.isRecordBatch())?(c=d.header(),d=yield b.v.readMessageBody(d.bodyLength),Uk(b,c,d)):null})}Va(a){const b=this;return l(function*(){var c,d=null==(c=b.m)?void 0:c.Ia(a);d&&(yield b.I.seek(d.offset))&&(d=yield b.v.readMessage(G.DictionaryBatch),null==d?0:d.isDictionaryBatch())&&
(c=d.header(),d=yield b.v.readMessageBody(d.bodyLength),d=Vk(b,c,d),b.dictionaries.set(c.id,d))})}Wa(){const a=this;return l(function*(){var b=a.I;b.G&&(yield b.G);const c=b.size-Hk,d=yield b.u(c);b=yield b.ha(c-d,d);return Gh.decode(b)})}pa(){const a=this;return l(function*(){a.m||(yield a.open());if(a.m&&a.P<a.numRecordBatches){const b=a.m.ga(a.P);if(b&&(yield a.I.seek(b.offset)))return yield a.v.readMessage(void 0)}return null})}}
class Lk extends Xk{Sa(a,b,c){return(new fi(b,a.M,a.buffers,this.dictionaries,this.schema.Ka)).visitMany(c)}}function Yk(a,b){return b&&"boolean"===typeof b.autoDestroy?b.autoDestroy:a.autoDestroy}function*Pk(a){a=Jk.from(a);try{if(!a.open({autoDestroy:!1}).closed){do yield a;while(!a.reset().open().closed)}}finally{a.cancel()}}
function Qk(a){return new oa(function*(){const b=yield new n(2,Jk.from(a));try{if(!(yield new n(2,b.open({autoDestroy:!1}))).closed){do yield new n(0,b);while(!(yield new n(2,b.reset().open())).closed)}}finally{yield new n(2,b.cancel())}}())}function Ok(a){const b=a.peek(Gk+7&-8);return b&&4<=b.byteLength?Fk(b)?new Sk(new $k(a.read())):new Kk(new Xk(a)):new Kk(new Xk(function*(){}()))}
function Nk(a){return l(function*(){const b=yield a.peek(Gk+7&-8);return b&&4<=b.byteLength?Fk(b)?new Sk(new $k(yield a.read())):new Rk(new Zk(a)):new Rk(new Zk(function(){return new oa(function*(){}())}()))})}function Mk(a){return l(function*(){const {size:b}=yield a.stat(),c=new Vh(a,b);return b>=Ik&&Fk(yield c.ha(0,Gk+7&-8))?new Tk(new al(c)):new Rk(new Zk(c))})};class bl extends Te{static sa(...a){const b=d=>d.flatMap(e=>Array.isArray(e)?b(e):e instanceof X?e.data.children:e.data),c=new bl;c.visitMany(b(a));return c}constructor(){super();this.ya=0;this.Ta=[];this.Na=[];this.Kb=[]}visit(a){if(a instanceof M)return this.visitMany(a.data),this;const b=a.type;if(!K.isDictionary(b)){const c=a.length;if(2147483647<c)throw new RangeError("Cannot write arrays larger than 2^31 - 1 in length");if(K.isUnion(b))this.M.push(new Vj(c,0));else{const d=a.nullCount;K.isNull(b)||
cl.call(this,0>=d?new Uint8Array(0):Lg(a.offset,c,a.nullBitmap));this.M.push(new Vj(c,d))}}return super.visit(a)}visitNull(){return this}visitDictionary(a){return this.visit(a.clone(a.type.indices))}get M(){return this.Ta}get buffers(){return this.Na}get byteLength(){return this.ya}get ab(){return this.Kb}}function cl(a){const b=a.byteLength+7&-8;this.buffers.push(a);this.ab.push(new Xj(this.ya,b));this.ya+=b;return this}function dl(a){return cl.call(this,a.values.subarray(0,a.length*a.stride))}
function el(a){const b=a.length,c=a.values;a=a.valueOffsets;const d=J(a[0]);var e=J(a[b]);e=Math.min(e-d,c.byteLength-d);cl.call(this,Ja(-d,b+1,a));cl.call(this,c.subarray(d,d+e));return this}function fl(a){const b=a.length,c=a.valueOffsets;if(c){const {[0]:d,[b]:e}=c;cl.call(this,Ja(-d,b+1,c));return this.visit(a.children[0].slice(d,e-d))}return this.visit(a.children[0])}function gl(a){return this.visitMany(a.type.children.map((b,c)=>a.children[c]).filter(Boolean))[0]}h=bl.prototype;
h.visitBool=function(a){let b;return a.nullCount>=a.length?cl.call(this,new Uint8Array(0)):(b=a.values)instanceof Uint8Array?cl.call(this,Lg(a.offset,a.length,b)):cl.call(this,Mg(a.values))};h.visitInt=dl;h.visitFloat=dl;h.visitUtf8=el;h.visitLargeUtf8=el;h.visitBinary=el;h.visitLargeBinary=el;h.visitFixedSizeBinary=dl;h.visitDate=dl;h.visitTimestamp=dl;h.visitTime=dl;h.visitDecimal=dl;h.visitList=fl;h.visitStruct=gl;
h.visitUnion=function(a){const b=a.type,c=a.length,d=a.typeIds,e=a.valueOffsets;cl.call(this,d);if(b.mode===x.Sparse)return gl.call(this,a);if(b.mode===x.Dense){if(0>=a.offset)return cl.call(this,e),gl.call(this,a);const f=new Int32Array(c),g=Object.create(null),k=Object.create(null);for(let m,p,r=-1;++r<c;){if(void 0===(m=d[r]))continue;void 0===(p=g[m])&&(p=g[m]=e[r]);f[r]=e[r]-p;let T;k[m]=(null!=(T=k[m])?T:0)+1}cl.call(this,f);this.visitMany(a.children.map((m,p)=>{p=b.typeIds[p];return m.slice(g[p],
Math.min(c,k[p]))}))}return this};h.visitInterval=dl;h.visitDuration=dl;h.visitFixedSizeList=fl;h.visitMap=fl;class hl extends Te{visit(a){return null==a?void 0:super.visit(a)}visitNull({typeId:a}){return{name:E[a].toLowerCase()}}visitInt({typeId:a,bitWidth:b,isSigned:c}){return{name:E[a].toLowerCase(),bitWidth:b,isSigned:c}}visitFloat({typeId:a,precision:b}){return{name:E[a].toLowerCase(),precision:y[b]}}visitBinary({typeId:a}){return{name:E[a].toLowerCase()}}visitLargeBinary({typeId:a}){return{name:E[a].toLowerCase()}}visitBool({typeId:a}){return{name:E[a].toLowerCase()}}visitUtf8({typeId:a}){return{name:E[a].toLowerCase()}}visitLargeUtf8({typeId:a}){return{name:E[a].toLowerCase()}}visitDecimal({typeId:a,
scale:b,precision:c,bitWidth:d}){return{name:E[a].toLowerCase(),scale:b,precision:c,bitWidth:d}}visitDate({typeId:a,unit:b}){return{name:E[a].toLowerCase(),unit:Xa[b]}}visitTime({typeId:a,unit:b,bitWidth:c}){return{name:E[a].toLowerCase(),unit:z[b],bitWidth:c}}visitTimestamp({typeId:a,timezone:b,unit:c}){return{name:E[a].toLowerCase(),unit:z[c],timezone:b}}visitInterval({typeId:a,unit:b}){return{name:E[a].toLowerCase(),unit:$a[b]}}visitDuration({typeId:a,unit:b}){return{name:E[a].toLocaleLowerCase(),
unit:z[b]}}visitList({typeId:a}){return{name:E[a].toLowerCase()}}visitStruct({typeId:a}){return{name:E[a].toLowerCase()}}visitUnion({typeId:a,mode:b,typeIds:c}){return{name:E[a].toLowerCase(),mode:x[b].toUpperCase(),typeIds:[...c]}}visitDictionary(a){return this.visit(a.dictionary)}visitFixedSizeBinary({typeId:a,byteWidth:b}){return{name:E[a].toLowerCase(),byteWidth:b}}visitFixedSizeList({typeId:a,listSize:b}){return{name:E[a].toLowerCase(),listSize:b}}visitMap({typeId:a,keysSorted:b}){return{name:E[a].toLowerCase(),
keysSorted:b}}};class il extends Te{static sa(...a){const b=new il;return a.map(({schema:c,data:d})=>b.visitMany(c.fields,d.children))}visit({name:a},b){const c=b.length,d=b.offset,e=b.nullCount,f=b.nullBitmap,g=K.isDictionary(b.type)?b.type.indices:b.type,k=Object.assign([],b.buffers,{[sc.VALIDITY]:void 0});return Object.assign({},{name:a,count:c,VALIDITY:K.isNull(g)||K.isUnion(g)?void 0:0>=e?Array.from({length:c},()=>1):[...(new Ng(f,d,c,null,Kg))]},super.visit(b.clone(g,d,c,0,k)))}visitNull(){return{}}visitBool({values:a,
offset:b,length:c}){return{DATA:[...(new Ng(a,b,c,null,Jg))]}}visitInt(a){return{DATA:64>a.type.bitWidth?[...a.values]:[...jl(a.values,2)]}}visitFloat(a){return{DATA:[...a.values]}}visitUtf8(a){return{DATA:[...(new M([a]))],OFFSET:[...a.valueOffsets]}}visitLargeUtf8(a){return{DATA:[...(new M([a]))],OFFSET:[...jl(a.valueOffsets,2)]}}visitBinary(a){return{DATA:[...kl(new M([a]))],OFFSET:[...a.valueOffsets]}}visitLargeBinary(a){return{DATA:[...kl(new M([a]))],OFFSET:[...jl(a.valueOffsets,2)]}}visitFixedSizeBinary(a){return{DATA:[...kl(new M([a]))]}}visitDate(a){return{DATA:a.type.unit===
Xa.DAY?[...a.values]:[...jl(a.values,2)]}}visitTimestamp(a){return{DATA:[...jl(a.values,2)]}}visitTime(a){return{DATA:a.type.unit<z.MICROSECOND?[...a.values]:[...jl(a.values,2)]}}visitDecimal(a){return{DATA:[...jl(a.values,4)]}}visitList(a){return{OFFSET:[...a.valueOffsets],children:this.visitMany(a.type.children,a.children)}}visitStruct(a){return{children:this.visitMany(a.type.children,a.children)}}visitUnion(a){return{TYPE_ID:[...a.typeIds],OFFSET:a.type.mode===x.Dense?[...a.valueOffsets]:void 0,
children:this.visitMany(a.type.children,a.children)}}visitInterval(a){return{DATA:[...a.values]}}visitDuration(a){return{DATA:[...jl(a.values,2)]}}visitFixedSizeList(a){return{children:this.visitMany(a.type.children,a.children)}}visitMap(a){return{OFFSET:[...a.valueOffsets],children:this.visitMany(a.type.children,a.children)}}}function*kl(a){for(const b of a)yield b.reduce((c,d)=>`${c}${("0"+(d&255).toString(16)).slice(-2)}`,"").toUpperCase()}
function*jl(a,b){a=new Uint32Array(a.buffer);for(let c=-1,d=a.length/b;++c<d;)yield`${Hc.new(a.subarray((c+0)*b,(c+1)*b),!1)}`};function Y(a,b){a.qa&&(b=u(Uint8Array,b))&&0<b.byteLength&&(a.H.write(b),a.oa+=b.byteLength);return a}function ll(a,b){return 0<b?Y(a,new Uint8Array(b)):a}function ml(a,b){let c,d,e;for(let f=-1,g=b.length;++f<g;)(c=b[f])&&0<(d=c.byteLength)&&(Y(a,c),0<(e=(d+7&-8)-d)&&ll(a,e));return a}
class nl extends Nh{static throughNode(){throw Error('"throughNode" not available in this environment');}static throughDOM(){throw Error('"throughDOM" not available in this environment');}constructor(a){super();this.oa=0;this.qa=!1;this.H=new Ph;this.S=null;this.ma=[];this.Aa=[];this.Ca=new Map;this.Qa=new Map;t(a)||(a={autoDestroy:!0,Gb:!1});this.wa="boolean"===typeof a.autoDestroy?a.autoDestroy:!0;this.Xa="boolean"===typeof a.Gb?a.Gb:!1}toString(a=!1){return this.H.toString(a)}toUint8Array(a=!1){return this.H.toUint8Array(a)}writeAll(a){return t(a)&&
q(a.then)?a.then(b=>this.writeAll(b)):t(a)&&q(a[Symbol.asyncIterator])?ol(this,a):pl(this,a)}get closed(){return this.H.closed}[Symbol.asyncIterator](){return this.H[Symbol.asyncIterator]()}toDOMStream(a){return this.H.toDOMStream(a)}toNodeStream(a){return this.H.toNodeStream(a)}close(){return this.reset().H.close()}abort(a){return this.reset().H.abort(a)}finish(){this.wa?this.close():this.reset(this.H,this.S);return this}reset(a=this.H,b=null){a===this.H||a instanceof Ph?this.H=a:(this.H=new Ph,
a&&t(a)&&q(a.abort)&&q(a.getWriter)&&!ya(a)?this.toDOMStream({type:"bytes"}).pipeTo(a):a&&t(a)&&q(a.end)&&q(a.write)&&"boolean"===typeof a.writable&&!ya(a)&&Mh(this.toNodeStream({hc:!1}),a));this.qa&&this.S&&this.Ea(this.S);this.qa=!1;this.ma=[];this.Aa=[];this.Ca=new Map;this.Qa=new Map;b&&wj(b,this.S)||(null==b?(this.oa=0,this.S=null):(this.qa=!0,this.S=b,this.Ya(b)));return this}write(a){let b=null;if(this.H){if(null==a||a instanceof Ej&&!(b=a.schema)||a instanceof X&&!(b=a.schema))return this.finish()&&
void 0}else throw Error("RecordBatchWriter is closed");if(b&&!wj(b,this.S)){if(this.qa&&this.wa)return this.close();this.reset(this.H,b)}a instanceof X?a instanceof Lj||this.ub(a):a instanceof Ej?this.writeAll(a.batches):t(a)&&q(a[Symbol.iterator])&&this.writeAll(a)}Fa(a){const b=bk.encode(a),c=b.byteLength,d=this.Xa?4:8,e=c+d+7&-8,f=e-c-d;a.headerType===G.RecordBatch?this.Aa.push(new Ih(e,a.bodyLength,this.oa)):a.headerType===G.DictionaryBatch&&this.ma.push(new Ih(e,a.bodyLength,this.oa));this.Xa||
Y(this,Int32Array.of(-1));Y(this,Int32Array.of(e-d));0<c&&Y(this,b);return ll(this,f)}Ya(a){this.Fa(bk.from(a))}Ea(){return this.Xa?Y(this,Int32Array.of(0)):Y(this,Int32Array.of(-1,0))}ub(a){const {byteLength:b,M:c,ab:d,buffers:e}=bl.sa(a);var f=new Qj(a.numRows,c,d);f=bk.from(f,b);ml(this.Da(a).Fa(f),e)}ra(a,b,c=!1){const {byteLength:d,M:e,ab:f,buffers:g}=bl.sa(new M([a]));a=new Qj(a.length,e,f);b=new ek(a,b,c);b=bk.from(b,d);return ml(this.Fa(b),g)}Da(a){for(const [b,c]of a.dictionaries){let d,
e;a=null!=(e=null==(d=c)?void 0:d.data)?e:[];const f=this.Ca.get(b);let g;const k=null!=(g=this.Qa.get(b))?g:0;if(!f||f.data[0]!==a[0])for(const [m,p]of a.entries())this.ra(p,b,0<m);else if(k<a.length)for(const m of a.slice(k))this.ra(m,b,!0);this.Ca.set(b,c);this.Qa.set(b,a.length)}return this}}class ql extends nl{static writeAll(a,b){const c=new ql(b);return t(a)&&q(a.then)?a.then(d=>c.writeAll(d)):t(a)&&q(a[Symbol.asyncIterator])?ol(c,a):pl(c,a)}}
class rl extends nl{static writeAll(a){const b=new rl;return t(a)&&q(a.then)?a.then(c=>b.writeAll(c)):t(a)&&q(a[Symbol.asyncIterator])?ol(b,a):pl(b,a)}constructor(){super();this.wa=!0}Ya(){ll(Y(this,Ek),2)}ra(a,b,c=!1){if(!c&&this.Ca.has(b))throw Error("The Arrow File format does not support replacement dictionaries. ");return super.ra(a,b,c)}Ea(a){const b=Gh.encode(new Gh(a,w.V5,this.Aa,this.ma));return Y(Y(Y(super.Ea(a),b),Int32Array.of(b.byteLength)),Ek)}}
class sl extends nl{static writeAll(a){return(new sl).writeAll(a)}constructor(){super();this.wa=!0;this.ba=[];this.Ba=[]}Fa(){return this}Ea(){return this}Ya(a){Y(this,`{\n  "schema": ${JSON.stringify({fields:a.fields.map(b=>tl(b))},null,2)}`)}Da(a){0<a.dictionaries.size&&this.Ba.push(a);return this}ra(a,b,c=!1){Y(this,0===this.ma.length?"    ":",\n    ");Y(this,ul(a,b,c));this.ma.push(new Ih(0,0,0));return this}ub(a){this.Da(a);this.ba.push(a)}close(){if(0<this.Ba.length){Y(this,',\n  "dictionaries": [\n');
for(var a of this.Ba)super.Da(a);Y(this,"\n  ]")}if(0<this.ba.length){for(let b=-1,c=this.ba.length;++b<c;){Y(this,0===b?',\n  "batches": [\n    ':",\n    ");a=this.ba[b];const [d]=il.sa(a);a=JSON.stringify({count:a.numRows,columns:d},null,2);Y(this,a);this.Aa.push(new Ih(0,0,0))}Y(this,"\n  ]")}this.S&&Y(this,"\n}");this.Ba=[];this.ba=[];return super.close()}}function pl(a,b){let c=b;b instanceof Ej&&(c=b.batches,a.reset(void 0,b.schema));for(const d of c)a.write(d);return a.finish()}
function ol(a,b){return l(function*(){var c;try{for(var d=ia(b);;){var e=yield d.next();if(e.done)break;a.write(e.value)}}catch(g){var f={error:g}}finally{try{e&&!e.done&&(c=d.return)&&(yield c.call(d))}finally{if(f)throw f.error;}}return a.finish()})}function tl({name:a,type:b,nullable:c}){const d=new hl;return{name:a,nullable:c,type:d.visit(b),children:(b.children||[]).map(e=>tl(e)),dictionary:K.isDictionary(b)?{id:b.id,isOrdered:b.isOrdered,indexType:d.visit(b.indices)}:void 0}}
function ul(a,b,c=!1){const [d]=il.sa(new X({[b]:a}));return JSON.stringify({id:b,isDelta:c,data:{count:a.length,columns:d}},null,2)};function vl(a,b){function c(g,k){let m,p,r=g.desiredSize||null;for(;!(p=k.next(e?r:null)).done;)if(ArrayBuffer.isView(p.value)&&(m=u(Uint8Array,p.value))&&(null!=r&&e&&(r=r-m.byteLength+1),p.value=m),g.enqueue(p.value),null!=r&&0>=--r)return;g.close()}let d=null;const e="bytes"===(null==b?void 0:b.type)||!1,f=(null==b?void 0:b.highWaterMark)||Math.pow(2,24);return new ReadableStream(Object.assign({},b,{start(g){c(g,d||(d=a[Symbol.iterator]()))},pull(g){d?c(g,d):g.close()},cancel(){let g;(null==(g=
d)?0:g.return)&&d.return();d=null}}),Object.assign({},{highWaterMark:e?f:void 0},b))}
function wl(a,b){function c(g,k){return l(function*(){let m,p,r=g.desiredSize||null;for(;!(p=yield k.next(e?r:null)).done;)if(ArrayBuffer.isView(p.value)&&(m=u(Uint8Array,p.value))&&(null!=r&&e&&(r=r-m.byteLength+1),p.value=m),g.enqueue(p.value),null!=r&&0>=--r)return;g.close()})}let d=null;const e="bytes"===(null==b?void 0:b.type)||!1,f=(null==b?void 0:b.highWaterMark)||Math.pow(2,24);return new ReadableStream(Object.assign({},b,{start(g){return l(function*(){yield c(g,d||(d=a[Symbol.asyncIterator]()))})},
pull(g){return l(function*(){d?yield c(g,d):g.close()})},cancel(){return l(function*(){let g;(null==(g=d)?0:g.return)&&(yield d.return());d=null})}}),Object.assign({},{highWaterMark:e?f:void 0},b))};function xl(a,b,c){null!=c&&(a.ka>=c.desiredSize&&++a.Ua&&yl(a,c,b.toVector()),b.finished&&((0<b.length||0===a.Ua)&&++a.Ua&&yl(a,c,b.toVector()),!a.pb&&(a.pb=!0)&&yl(a,c,null)))}function yl(a,b,c){a.ka=0;a.la=null;null==c?b.close():b.enqueue(c)}
class zl{constructor(a){this.Ua=0;this.pb=!1;this.ka=0;var b=Object.assign({},a),c=a.readableStrategy,d=a.writableStrategy;a=a.queueingStrategy;a=void 0===a?"count":a;b=(delete b.readableStrategy,delete b.writableStrategy,delete b.queueingStrategy,b);this.la=null;this.da=pi(b);this.Ob="bytes"!==a?Al:Bl;({highWaterMark:c="bytes"===a?Math.pow(2,14):1E3}=Object.assign({},c));({highWaterMark:d="bytes"===a?Math.pow(2,14):1E3}=Object.assign({},d));this.readable=new ReadableStream({cancel:()=>{this.da.clear()},
pull:e=>{xl(this,this.da,this.la=e)},start:e=>{xl(this,this.da,this.la=e)}},{highWaterMark:c,size:"bytes"!==a?Al:Bl});this.writable=new WritableStream({abort:()=>{this.da.clear()},write:()=>{xl(this,this.da,this.la)},close:()=>{xl(this,this.da.finish(),this.la)}},{highWaterMark:d,size:e=>{const f=this.ka;this.ka=this.Ob(this.da.append(e));return this.ka-f}})}}const Al=a=>{let b;return null!=(b=null==a?void 0:a.length)?b:0},Bl=a=>{let b;return null!=(b=null==a?void 0:a.byteLength)?b:0};function Cl(a,b){function c(){return l(function*(){return yield(yield Jk.from(e)).open(b)})}function d(k,m){return l(function*(){let p=k.desiredSize,r;for(;!(r=yield m.next()).done;)if(k.enqueue(r.value),null!=p&&0>=--p)return;k.close()})}const e=new Ph;let f=null;const g=new ReadableStream({cancel(){return l(function*(){yield e.close()})},start(k){return l(function*(){yield d(k,f||(f=yield c()))})},pull(k){return l(function*(){f?yield d(k,f):k.close()})}});return{writable:new WritableStream(e,Object.assign({},
{highWaterMark:Math.pow(2,14)},a)),readable:g}};function Dl(a,b){function c(f){return l(function*(){let g,k=f.desiredSize;for(;g=yield e.read(k||null);)if(f.enqueue(g),null!=k&&0>=(k-=g.byteLength))return;f.close()})}const d=new this(a),e=new Sh(d);b=new ReadableStream({cancel(){return l(function*(){yield e.cancel()})},pull(f){return l(function*(){yield c(f)})},start(f){return l(function*(){yield c(f)})}},Object.assign({},{highWaterMark:Math.pow(2,14)},b));return{writable:new WritableStream(d,a),readable:b}};function El(a){a=Jk.from(a);return t(a)&&q(a.then)?a.then(b=>El(b)):a.isAsync()?a.readAll().then(b=>new Ej(b)):new Ej(a.readAll())};const Fl=Object.assign({},Ic,ci,Rg,af,v,Ig,vc,{compareSchemas:wj,compareFields:function(a,b){return tj.compareFields(a,b)},compareTypes:xj});La=function(a,b){if(t(a)&&q(a[Symbol.asyncIterator]))return wl(a,b);if(t(a)&&q(a[Symbol.iterator]))return vl(a,b);throw Error("toDOMStream() must be called with an Iterable or AsyncIterable");};xh.throughDOM=function(a){return new zl(a)};Jk.throughDOM=Cl;Sk.throughDOM=Cl;Kk.throughDOM=Cl;nl.throughDOM=Dl;rl.throughDOM=Dl;ql.throughDOM=Dl;var Z={};Z.AsyncByteQueue=Ph;Z.AsyncByteStream=Sh;Z.AsyncMessageReader=Ck;Z.AsyncRecordBatchFileReader=Tk;Z.AsyncRecordBatchStreamReader=Rk;Z.Binary=gd;
Z.BinaryBuilder=hi;Z.Bool=wd;Z.BoolBuilder=ji;Z.BufferType=sc;Z.Builder=xh;Z.ByteStream=Qh;Z.Data=O;Z.DataType=K;Z.DateBuilder=ki;Z.DateDay=Id;Z.DateDayBuilder=li;Z.DateMillisecond=Jd;Z.DateMillisecondBuilder=mi;Z.DateUnit=Xa;Z.Date_=Ed;Z.Decimal=Ad;Z.DecimalBuilder=ni;Z.DenseUnion=ze;Z.DenseUnionBuilder=cj;Z.Dictionary=Oe;Z.DictionaryBuilder=oi;Z.Duration=fe;Z.DurationBuilder=zi;Z.DurationMicrosecond=le;Z.DurationMicrosecondBuilder=Ci;Z.DurationMillisecond=ke;Z.DurationMillisecondBuilder=Bi;
Z.DurationNanosecond=me;Z.DurationNanosecondBuilder=Di;Z.DurationSecond=je;Z.DurationSecondBuilder=Ai;Z.Field=U;Z.FixedSizeBinary=Be;Z.FixedSizeBinaryBuilder=qi;Z.FixedSizeList=Fe;Z.FixedSizeListBuilder=ri;Z.Float=$c;Z.Float16=dd;Z.Float16Builder=ti;Z.Float32=ed;Z.Float32Builder=ui;Z.Float64=fd;Z.Float64Builder=vi;Z.FloatBuilder=si;Z.Int=Oc;Z.Int16=Tc;Z.Int16Builder=Gi;Z.Int32=Uc;Z.Int32Builder=Hi;Z.Int64=Vc;Z.Int64Builder=Ii;Z.Int8=Sc;Z.Int8Builder=Fi;Z.IntBuilder=Ei;Z.Interval=$d;
Z.IntervalBuilder=wi;Z.IntervalDayTime=de;Z.IntervalDayTimeBuilder=xi;Z.IntervalUnit=$a;Z.IntervalYearMonth=ee;Z.IntervalYearMonthBuilder=yi;Z.JSONMessageReader=Dk;Z.LargeBinary=kd;Z.LargeBinaryBuilder=ii;Z.LargeUtf8=sd;Z.LargeUtf8Builder=ej;Z.List=ne;Z.ListBuilder=Ni;Z.MapBuilder=Oi;Z.MapRow=rg;Z.Map_=Je;Z.Message=bk;Z.MessageHeader=G;Z.MessageReader=Bk;Z.MetadataVersion=w;Z.Null=Nc;Z.NullBuilder=Pi;Z.Precision=y;Z.RecordBatch=X;Z.RecordBatchFileReader=Sk;Z.RecordBatchFileWriter=rl;
Z.RecordBatchJSONWriter=sl;Z.RecordBatchReader=Jk;Z.RecordBatchStreamReader=Kk;Z.RecordBatchStreamWriter=ql;Z.RecordBatchWriter=nl;Z.Schema=S;Z.SparseUnion=Ae;Z.SparseUnionBuilder=bj;Z.Struct=re;Z.StructBuilder=Qi;Z.StructRow=Pf;Z.Table=Ej;Z.Time=Kd;Z.TimeBuilder=Wi;Z.TimeMicrosecond=Qd;Z.TimeMicrosecondBuilder=Zi;Z.TimeMillisecond=Pd;Z.TimeMillisecondBuilder=Yi;Z.TimeNanosecond=Rd;Z.TimeNanosecondBuilder=$i;Z.TimeSecond=Od;Z.TimeSecondBuilder=Xi;Z.TimeUnit=z;Z.Timestamp=Sd;Z.TimestampBuilder=Ri;
Z.TimestampMicrosecond=Yd;Z.TimestampMicrosecondBuilder=Ui;Z.TimestampMillisecond=Xd;Z.TimestampMillisecondBuilder=Ti;Z.TimestampNanosecond=Zd;Z.TimestampNanosecondBuilder=Vi;Z.TimestampSecond=Wd;Z.TimestampSecondBuilder=Si;Z.Type=H;Z.Uint16=Xc;Z.Uint16Builder=Ki;Z.Uint32=Yc;Z.Uint32Builder=Li;Z.Uint64=Zc;Z.Uint64Builder=Mi;Z.Uint8=Wc;Z.Uint8Builder=Ji;Z.Union=ve;Z.UnionBuilder=aj;Z.UnionMode=x;Z.Utf8=od;Z.Utf8Builder=dj;Z.Vector=M;Z.Visitor=Te;
Z.builderThroughAsyncIterable=function(a){const {queueingStrategy:b="count"}=a,{highWaterMark:c="bytes"!==b?Number.POSITIVE_INFINITY:Math.pow(2,14)}=a,d="bytes"!==b?"length":"byteLength";return function(e){return new oa(function*(){let f=0;const g=pi(a);var k;try{for(var m=ia(e);;){var p=yield new n(2,m.next());if(p.done)break;g.append(p.value)[d]>=c&&++f&&(yield new n(0,g.toVector()))}}catch(T){var r={error:T}}finally{try{p&&!p.done&&(k=m.return)&&(yield new n(2,k.call(m)))}finally{if(r)throw r.error;
}}if(0<g.finish().length||0===f)yield new n(0,g.toVector())}())}};Z.builderThroughIterable=Aj;Z.makeBuilder=pi;Z.makeData=P;Z.makeTable=function(a){const b={};a=Object.entries(a);for(const [c,d]of a)b[c]=oh(d);return new Ej(b)};Z.makeVector=oh;Z.tableFromArrays=function(a){const b={};a=Object.entries(a);for(const [c,d]of a)b[c]=yj(d);return new Ej(b)};Z.tableFromIPC=El;Z.tableFromJSON=function(a){a=yj(a);a=new X(new S(a.type.children),a.data[0]);return new Ej(a)};
Z.tableToIPC=function(a,b="stream"){return("stream"===b?ql:rl).writeAll(a).toUint8Array(!0)};Z.util=Fl;Z.vectorFromArray=yj;Object.assign(arguments[0],Z);}.bind(this))));

//# sourceMappingURL=Arrow.es2015.min.js.map
