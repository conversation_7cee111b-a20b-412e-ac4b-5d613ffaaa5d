{"version": 3, "file": "HTTPRequest.d.ts", "sourceRoot": "", "sources": ["../../../../src/api/HTTPRequest.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;AACH,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAChD,OAAO,KAAK,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AACtC,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,mBAAmB,CAAC;AAEpD;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CAClC;AAED;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC,MAAM,EAAE,yBAAyB,CAAC;IAClC,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED;;;;GAIG;AACH,MAAM,WAAW,kBAAkB;IACjC,MAAM,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACjC,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC;CACvB;AAED;;;;GAIG;AACH,MAAM,MAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAEpE;;;;GAIG;AACH,eAAO,MAAM,qCAAqC,IAAI,CAAC;AAEvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,8BAAsB,WAAW;IAC/B;;OAEG;IACH,UAAU,SAAM;IAChB;;OAEG;IACH,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;IACpC;;OAEG;IACH,YAAY,EAAE,MAAM,GAAG,IAAI,CAAQ;IACnC;;OAEG;IACH,SAAS,EAAE,YAAY,GAAG,IAAI,CAAQ;IACtC;;OAEG;IACH,gBAAgB,UAAS;IACzB;;OAEG;IACH,cAAc,EAAE,WAAW,EAAE,CAAM;IAEnC;;;;OAIG;IACH,QAAQ,KAAK,MAAM,IAAI,UAAU,CAAC;IAElC;;OAEG;;IAGH;;OAEG;IACH,QAAQ,CAAC,GAAG,IAAI,MAAM;IAEtB;;;;OAIG;IACH,QAAQ,CAAC,wBAAwB,IAAI,wBAAwB;IAE7D;;;OAGG;IACH,QAAQ,CAAC,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,CAAC,GAAG,IAAI;IAEjE;;OAEG;IACH,QAAQ,CAAC,gBAAgB,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI;IAEhE;;;;;;;;;;OAUG;IACH,QAAQ,CAAC,wBAAwB,IAAI,wBAAwB;IAE7D;;;OAGG;IACH,QAAQ,CAAC,4BAA4B,IAAI,OAAO;IAEhD;;;;;OAKG;IACH,QAAQ,CAAC,sBAAsB,CAC7B,cAAc,EAAE,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,GAChD,IAAI;IAEP;;;OAGG;IACH,QAAQ,CAAC,qBAAqB,IAAI,OAAO,CAAC,IAAI,CAAC;IAE/C;;;OAGG;IACH,QAAQ,CAAC,YAAY,IAAI,YAAY;IAErC;;OAEG;IACH,QAAQ,CAAC,MAAM,IAAI,MAAM;IAEzB;;OAEG;IACH,QAAQ,CAAC,QAAQ,IAAI,MAAM,GAAG,SAAS;IAEvC;;;;;OAKG;IACH,QAAQ,CAAC,WAAW,IAAI,OAAO;IAE/B;;OAEG;IACH,QAAQ,CAAC,aAAa,IAAI,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;IAErD;;;OAGG;IACH,QAAQ,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAE1C;;;OAGG;IACH,QAAQ,CAAC,QAAQ,IAAI,YAAY,GAAG,IAAI;IAExC;;;OAGG;IACH,QAAQ,CAAC,KAAK,IAAI,KAAK,GAAG,IAAI;IAE9B;;OAEG;IACH,QAAQ,CAAC,mBAAmB,IAAI,OAAO;IAEvC;;OAEG;IACH,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS;IAE5D;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,QAAQ,CAAC,aAAa,IAAI,WAAW,EAAE;IAEvC;;;;;;;;;;;;;;;;;;;OAmBG;IACH,QAAQ,CAAC,OAAO,IAAI;QAAC,SAAS,EAAE,MAAM,CAAA;KAAC,GAAG,IAAI;IAE9C;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACH,QAAQ,CAAC,QAAQ,CACf,SAAS,CAAC,EAAE,wBAAwB,EACpC,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,IAAI,CAAC;IAEhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH,QAAQ,CAAC,OAAO,CACd,QAAQ,EAAE,OAAO,CAAC,kBAAkB,CAAC,EACrC,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,IAAI,CAAC;IAEhB;;;;;;;;;;;;;OAaG;IACH,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;CACxE;AAED;;GAEG;AACH,oBAAY,yBAAyB;IACnC,KAAK,UAAU;IACf,OAAO,YAAY;IACnB,QAAQ,aAAa;IACrB,QAAQ,aAAa;IACrB,IAAI,SAAS;IACb,cAAc,oBAAoB;CACnC;AAED;;;;GAIG;AACH,MAAM,MAAM,2BAA2B,GAAG,yBAAyB,CAAC;AAEpE;;GAEG;AACH,MAAM,MAAM,SAAS,GACjB,SAAS,GACT,cAAc,GACd,oBAAoB,GACpB,iBAAiB,GACjB,mBAAmB,GACnB,mBAAmB,GACnB,kBAAkB,GAClB,kBAAkB,GAClB,mBAAmB,GACnB,iBAAiB,GACjB,sBAAsB,GACtB,iBAAiB,GACjB,UAAU,GACV,QAAQ,CAAC;AAEb;;GAEG;AACH,MAAM,MAAM,YAAY,GAAG,UAAU,GAAG,OAAO,GAAG,SAAS,CAAC;AAE5D;;GAEG;AACH,wBAAgB,YAAY,CAC1B,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC,GACzC,KAAK,CAAC;IAAC,IAAI,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,MAAM,CAAA;CAAC,CAAC,CAgBtC;AAED;;;;;;GAMG;AACH,eAAO,MAAM,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAgEtC,CAAC"}