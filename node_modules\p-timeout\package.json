{"name": "p-timeout", "version": "4.1.0", "description": "Timeout a promise after a specified amount of time", "license": "MIT", "repository": "sindresorhus/p-timeout", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "timeout", "error", "invalidate", "async", "await", "promises", "time", "out", "cancel", "bluebird"], "devDependencies": {"ava": "^2.4.0", "delay": "^4.4.0", "p-cancelable": "^2.0.0", "tsd": "^0.13.1", "xo": "^0.35.0", "in-range": "^2.0.0", "time-span": "^4.0.0"}}