"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const promises_1 = __importDefault(require("fs/promises"));
const findLockfile_1 = __importDefault(require("./findLockfile"));
// map lockfiles to package managers
const packageManagerLockfileMap = {
    'package-lock': 'npm',
    yarn: 'yarn',
    'pnpm-lock': 'pnpm',
    deno: 'deno',
    bun: 'bun',
};
/**
 * If the packageManager option was not provided, look at the lockfiles to
 * determine which package manager is being used.
 */
const determinePackageManager = async (options, 
// only for testing
readdir = promises_1.default.readdir) => {
    var _a;
    if (options.packageManager)
        return options.packageManager;
    else if (options.global)
        return 'npm';
    const lockfileName = (_a = (await (0, findLockfile_1.default)(options, readdir))) === null || _a === void 0 ? void 0 : _a.filename;
    return lockfileName ? packageManagerLockfileMap[lockfileName.split('.')[0]] : 'npm';
};
exports.default = determinePackageManager;
//# sourceMappingURL=determinePackageManager.js.map