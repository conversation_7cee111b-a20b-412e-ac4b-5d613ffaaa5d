import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { RedisArgument, SimpleStringReply } from '@redis/client/dist/lib/RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Removes an existing alias from a RediSearch index.
     * @param parser - The command parser
     * @param alias - The alias to remove
     */
    readonly parseCommand: (this: void, parser: CommandParser, alias: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply<'OK'>;
};
export default _default;
//# sourceMappingURL=ALIASDEL.d.ts.map