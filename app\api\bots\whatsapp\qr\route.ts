'use server'

import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { WhatsAppBotService } from '@/lib/services/whatsapp-bot'
import { z } from 'zod'

const qrSchema = z.object({
  sessionId: z.string().min(1)
})

// Global bot service instance
let botService: WhatsAppBotService | null = null

function getBotService(): WhatsAppBotService {
  if (!botService) {
    botService = new WhatsAppBotService()
  }
  return botService
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate request - only admins can get QR codes
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const validatedData = qrSchema.parse(body)

    // Get bot service
    const service = getBotService()

    // Get session
    const session = await service.getSession(validatedData.sessionId)
    if (!session) {
      return NextResponse.json(
        { success: false, message: 'Session not found' },
        { status: 404 }
      )
    }

    // Check if QR code is available
    if (!session.qrCode) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'QR code not available',
          data: {
            sessionId: session.id,
            status: session.status
          }
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        sessionId: session.id,
        qrCode: session.qrCode,
        status: session.status
      }
    })

  } catch (error) {
    console.error('Error getting WhatsApp QR code:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to get WhatsApp QR code',
        error: error instanceof z.ZodError ? error.errors : undefined
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  const url = new URL(request.url)
  const sessionId = url.searchParams.get('sessionId')

  if (!sessionId) {
    return NextResponse.json(
      { success: false, message: 'Session ID is required' },
      { status: 400 }
    )
  }

  try {
    // Authenticate request
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      )
    }

    // Get bot service
    const service = getBotService()

    // Get session
    const session = await service.getSession(sessionId)
    if (!session) {
      return NextResponse.json(
        { success: false, message: 'Session not found' },
        { status: 404 }
      )
    }

    // Check if QR code is available
    if (!session.qrCode) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'QR code not available',
          data: {
            sessionId: session.id,
            status: session.status
          }
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        sessionId: session.id,
        qrCode: session.qrCode,
        status: session.status
      }
    })

  } catch (error) {
    console.error('Error getting WhatsApp QR code:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get WhatsApp QR code'
      },
      { status: 500 }
    )
  }
}
