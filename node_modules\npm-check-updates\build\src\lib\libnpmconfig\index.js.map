{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/lib/libnpmconfig/index.js"], "names": [], "mappings": ";AAAA;;;;;;EAME;AAEF,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;AACxB,MAAM,YAAY,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAA;AAChD,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAA;AAC1B,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;AACxB,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAA;AAE5B,MAAM,SAAS,GAAG,YAAY,CAC5B,EAAE,EACF;IACE,8BAA8B;IAC9B,KAAK;QACH,OAAO,IAAI,CAAA;IACb,CAAC;CACF,CACF,CAAA;AAED,MAAM,UAAU,GAAG,YAAY,CAAC;IAC9B,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE,MAAM,CAAC,EAAE;IACvE,WAAW,EAAE,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;IAC7C,SAAS,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE;IACvC,GAAG,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE;IACrC,YAAY,EAAE;QACZ,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC;KAC5D;IACD,UAAU,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE,QAAQ,CAAC,EAAE;CAC/E,CAAC,CAAA;AAEF,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,YAAY,CAAA;AAElC,2BAA2B;AAC3B,SAAS,YAAY,CAAC,KAAK,EAAE,QAAQ;IACnC,MAAM,OAAO,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAA;IACpC,MAAM,GAAG,GAAG,EAAE,CAAA;IACd,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACrC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;YAAE,OAAM;QACzC,MAAM,MAAM,GAAG,GAAG;aACf,WAAW,EAAE;aACb,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;aAC9B,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAA;QAC1B,GAAG,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IAChC,CAAC,CAAC,CAAA;IACF,MAAM,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAA;IAC5B,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU,CAAA;IAC3E,MAAM,IAAI,GAAG,YAAY,IAAI,YAAY,CAAC,YAAY,CAAC,CAAA;IACvD,MAAM,cAAc,GAAG,OAAO,CAAC,YAAY,IAAI,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,YAAY,CAAA;IACnF,MAAM,MAAM,GAAG,cAAc,IAAI,YAAY,CAAC,cAAc,CAAC,CAAA;IAC7D,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAA;IAC3E,IAAI,IAAI,GAAG,EAAE,CAAA;IACb,IAAI,YAAY,IAAI,YAAY,KAAK,YAAY,EAAE;QACjD,IAAI,GAAG,YAAY,CAAC,YAAY,CAAC,CAAA;KAClC;IACD,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;IAChE,IAAI,OAAO,CAAC,KAAK,EAAE;QACjB,OAAO,OAAO,CAAC,MAAM,CAAC;YACpB,KAAK,EAAE,IAAI,CAAC,OAAO,CACjB,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK;gBACpB,CAAC,CAAC,OAAO,CAAC,GAAG;gBACb,CAAC,CAAC,IAAI,CAAC,KAAK;oBACZ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;oBAC5B,CAAC,CAAC,IAAI,CAAC,KAAK;wBACZ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;wBAC5B,CAAC,CAAC,MAAM,CAAC,KAAK;4BACd,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;4BAC9B,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAC9B,OAAO,CAAC,KAAK,CACd;SACF,CAAC,CAAA;KACH;SAAM;QACL,OAAO,OAAO,CAAA;KACf;AACH,CAAC;AAED,sCAAsC;AACtC,SAAS,YAAY,CAAC,CAAC;IACrB,IAAI,GAAG,CAAA;IACP,IAAI;QACF,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;KACjC;IAAC,OAAO,GAAG,EAAE;QACZ,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE;YACzB,OAAO,EAAE,CAAA;SACV;aAAM;YACL,MAAM,GAAG,CAAA;SACV;KACF;IACD,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;AACvB,CAAC;AAED,kCAAkC;AAClC,SAAS,eAAe;IACtB,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE;QACtB,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAA;KAC1B;SAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;QACvC,uCAAuC;QACvC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;KACtC;SAAM;QACL,4CAA4C;QAC5C,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;QACvD,oCAAoC;QACpC,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE;YACvB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;SAC5C;QACD,OAAO,IAAI,CAAA;KACZ;AACH,CAAC"}