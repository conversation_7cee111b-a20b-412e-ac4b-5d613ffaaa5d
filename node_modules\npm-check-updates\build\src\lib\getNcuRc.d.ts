interface Options {
    color?: boolean;
    configFileName?: string;
    configFilePath?: string;
    global?: boolean;
    packageFile?: string;
}
/**
 * Loads the .ncurc config file.
 *
 * @param [cfg]
 * @param [cfg.configFileName=.ncurc]
 * @param [cfg.configFilePath]
 * @param [cfg.packageFile]
 * @returns
 */
declare function getNcuRc({ color, configFileName, configFilePath, packageFile, global }?: Options): Promise<{
    args: string[];
    filePath: string | undefined;
    config: Pick<{}, never>;
} | null>;
export default getNcuRc;
