{"version": 3, "sources": ["visitor/typector.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;AAIrB,OAAO,KAAK,IAAI,MAAM,YAAY,CAAC;AAGnC,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAWxC,cAAc;AACd,MAAM,OAAO,sBAAuB,SAAQ,OAAO;IACxC,SAAS,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjC,SAAS,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjC,QAAQ,KAAK,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/B,SAAS,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjC,UAAU,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACnC,UAAU,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACnC,UAAU,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACnC,UAAU,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACnC,WAAW,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACrC,WAAW,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACrC,WAAW,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACrC,UAAU,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACnC,YAAY,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACvC,YAAY,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACvC,YAAY,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACvC,SAAS,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjC,cAAc,KAAK,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC3C,WAAW,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACrC,gBAAgB,KAAK,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IAC/C,oBAAoB,KAAK,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IACvD,SAAS,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAClC,YAAY,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACvC,oBAAoB,KAAK,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IACvD,cAAc,KAAK,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC3C,oBAAoB,KAAK,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IACvD,yBAAyB,KAAK,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;IACjE,yBAAyB,KAAK,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;IACjE,wBAAwB,KAAK,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;IAC/D,SAAS,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjC,eAAe,KAAK,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IAC7C,oBAAoB,KAAK,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IACvD,oBAAoB,KAAK,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IACvD,mBAAmB,KAAK,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;IACrD,YAAY,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACvC,SAAS,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjC,WAAW,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACrC,UAAU,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACnC,eAAe,KAAK,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IAC7C,gBAAgB,KAAK,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IAC/C,eAAe,KAAK,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IAC7C,aAAa,KAAK,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACzC,oBAAoB,KAAK,OAAO,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IACvD,sBAAsB,KAAK,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;IAC3D,aAAa,KAAK,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACzC,mBAAmB,KAAK,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;IACrD,wBAAwB,KAAK,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;IAC/D,wBAAwB,KAAK,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;IAC/D,uBAAuB,KAAK,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;IAC7D,kBAAkB,KAAK,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;IACnD,QAAQ,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;CAC1C;AAED,cAAc;AACd,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAI,sBAAsB,EAAE,CAAC", "file": "typector.mjs", "sourceRoot": "../src"}