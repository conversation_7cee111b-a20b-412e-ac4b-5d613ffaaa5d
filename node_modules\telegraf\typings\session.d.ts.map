{"version": 3, "file": "session.d.ts", "sourceRoot": "", "sources": ["../src/session.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAA;AACnC,OAAO,EAAE,aAAa,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAA;AACjE,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAA;AAI3C,MAAM,WAAW,gBAAgB,CAAC,CAAC;IACjC,GAAG,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,CAAA;IACpC,GAAG,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,IAAI,CAAA;IACrC,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,CAAA;CAC/B;AAED,MAAM,WAAW,iBAAiB,CAAC,CAAC;IAClC,GAAG,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO,CAAC,CAAC,GAAG,SAAS,CAAC,CAAA;IAC7C,GAAG,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,CAAA;IACjD,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO,CAAC,OAAO,CAAC,CAAA;CAC3C;AAED,MAAM,MAAM,YAAY,CAAC,CAAC,IAAI,gBAAgB,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAA;AAExE,UAAU,cAAc,CAAC,CAAC,EAAE,CAAC,SAAS,OAAO,EAAE,CAAC,SAAS,MAAM;IAC7D,yFAAyF;IACzF,QAAQ,CAAC,EAAE,CAAC,CAAA;IACZ,aAAa,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,YAAY,CAAC,MAAM,GAAG,SAAS,CAAC,CAAA;IAC5D,KAAK,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAA;IACvB,cAAc,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAA;CAC/B;AAED,uFAAuF;AACvF,MAAM,WAAW,cAAc,CAAC,CAAC,SAAS,MAAM,CAAE,SAAQ,OAAO;IAC/D,OAAO,CAAC,EAAE,CAAC,CAAA;CACZ;AAED;;;;;;;;;;;;;GAaG;AACH,wBAAgB,OAAO,CACrB,CAAC,SAAS,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC3B,CAAC,SAAS,OAAO,GAAG;KAAG,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CAAE,EACzC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,GAAG,SAAS,GAAG,SAAS,EAGtE,OAAO,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CA6GpD;AASD,4BAA4B;AAC5B,qBAAa,kBAAkB,CAAC,CAAC,CAAE,YAAW,gBAAgB,CAAC,CAAC,CAAC;IAGnD,OAAO,CAAC,QAAQ,CAAC,GAAG;IAFhC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAqD;gBAE9C,GAAG,SAAW;IAE3C,GAAG,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS;IAWhC,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,GAAG,IAAI;IAKjC,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;CAG3B;AAED,iGAAiG;AACjG,wBAAgB,gBAAgB,CAAC,CAAC,SAAS,MAAM,EAC/C,GAAG,EAAE,OAAO,GACX,GAAG,IAAI,cAAc,CAAC,CAAC,CAAC,CAE1B"}