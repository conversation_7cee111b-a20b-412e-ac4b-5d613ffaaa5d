"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    NOT_KEYED_COMMAND: true,
    IS_READ_ONLY: true,
    /**
     * Updates the index pointed to by an existing alias.
     * @param parser - The command parser
     * @param alias - The existing alias to update
     * @param index - The new index name that the alias should point to
     */
    parseCommand(parser, alias, index) {
        parser.push('FT.ALIASUPDATE', alias, index);
    },
    transformReply: undefined
};
//# sourceMappingURL=ALIASUPDATE.js.map