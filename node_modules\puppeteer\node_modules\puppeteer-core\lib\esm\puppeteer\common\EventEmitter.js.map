{"version": 3, "file": "EventEmitter.js", "sourceRoot": "", "sources": ["../../../../src/common/EventEmitter.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,IAAoB,MAAM,gCAAgC,CAAC;AAClE,OAAO,EAAC,aAAa,EAAC,MAAM,uBAAuB,CAAC;AAmDpD;;;;;;;;;;;GAWG;AACH,MAAM,OAAO,YAAY;IAGvB,QAAQ,CAA6D;IACrE,SAAS,GAAG,IAAI,GAAG,EAA2C,CAAC;IAE/D;;;;OAIG;IACH,YACE,UAAsE,IAAI,CACxE,IAAI,GAAG,EAAE,CACV;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACH,EAAE,CACA,IAAS,EACT,OAAiD;QAEjD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,GAAG,CACD,IAAS,EACT,OAAkD;QAElD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAChD,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACnC,CAAC;YACD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,IAAI,CACF,IAAS,EACT,KAAsC;QAEtC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACH,cAAc,CACZ,IAAS,EACT,OAAiD;QAEjD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACH,WAAW,CACT,IAAS,EACT,OAAiD;QAEjD,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAChC,CAAC;IAED;;;;;OAKG;IACH,IAAI,CACF,IAAS,EACT,OAAiD;QAEjD,MAAM,WAAW,GAA6C,SAAS,CAAC,EAAE;YACxE,OAAO,CAAC,SAAS,CAAC,CAAC;YACnB,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAC9B,CAAC,CAAC;QAEF,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACH,aAAa,CAAC,IAAsC;QAClD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;OAMG;IACH,kBAAkB,CAAC,IAAuC;QACxD,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,CAAC,aAAa,CAAC;QACb,KAAK,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC9C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC/B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAK5B,OAAO,CAAS;IAChB,KAAK,CAAO;IACZ,QAAQ,CAAiB;IAEzB,YAAY,MAAc,EAAE,IAAU,EAAE,OAAuB;QAC7D,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAED,CAAC,aAAa,CAAC;QACb,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;CACF"}