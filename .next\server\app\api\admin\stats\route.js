"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/stats/route";
exports.ids = ["app/api/admin/stats/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_Kode_XGuard_app_api_admin_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/admin/stats/route.ts */ \"(rsc)/./app/api/admin/stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/stats/route\",\n        pathname: \"/api/admin/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/stats/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\api\\\\admin\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_Kode_XGuard_app_api_admin_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/admin/stats/route.ts":
/*!**************************************!*\
  !*** ./app/api/admin/stats/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth_simple__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth-simple */ \"(rsc)/./lib/auth-simple.ts\");\n\n\nasync function GET(request) {\n    try {\n        // Get authenticated user and verify admin access\n        const authResult = await _lib_auth_simple__WEBPACK_IMPORTED_MODULE_1__.SimpleAuthService.authenticateRequest(request);\n        if (!authResult.success || !authResult.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin\n        if (authResult.user.role !== \"admin\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Get all users from SimpleAuthService\n        const allUsers = _lib_auth_simple__WEBPACK_IMPORTED_MODULE_1__.SimpleAuthService.getAllUsers();\n        // Calculate statistics based on real user data\n        const totalUsers = allUsers.length;\n        const activeUsers = allUsers.filter((user)=>{\n            const lastActive = new Date(user.last_active);\n            const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\n            return lastActive > oneDayAgo;\n        }).length;\n        const newUsersToday = allUsers.filter((user)=>{\n            const createdAt = new Date(user.created_at);\n            const today = new Date();\n            return createdAt.toDateString() === today.toDateString();\n        }).length;\n        // Generate realistic statistics based on user data\n        const totalScans = allUsers.reduce((sum, user)=>sum + Math.floor(user.score / 10), 0) + Math.floor(Math.random() * 500);\n        const scansToday = Math.floor(totalScans * 0.05) + Math.floor(Math.random() * 50);\n        const totalVulnerabilities = Math.floor(totalScans * 0.3) + Math.floor(Math.random() * 200);\n        const criticalVulnerabilities = Math.floor(totalVulnerabilities * 0.15);\n        // Calculate users by plan\n        const usersByPlan = allUsers.reduce((acc, user)=>{\n            acc[user.plan] = (acc[user.plan] || 0) + 1;\n            return acc;\n        }, {});\n        // Generate revenue statistics\n        const planPrices = {\n            \"Free\": 0,\n            \"Student\": 5,\n            \"Hobby\": 15,\n            \"Bughunter\": 50,\n            \"Cybersecurity\": 100,\n            \"Pro\": 200,\n            \"Expert\": 500,\n            \"Elite\": 1000\n        };\n        const monthlyRevenue = allUsers.reduce((sum, user)=>{\n            return sum + (planPrices[user.plan] || 0);\n        }, 0);\n        // Generate recent activities based on user data\n        const recentActivities = [\n            {\n                id: \"1\",\n                type: \"user_registration\",\n                description: `New user ${allUsers[allUsers.length - 1]?.username || \"admin\"} registered`,\n                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                severity: \"low\",\n                userId: allUsers[allUsers.length - 1]?.id,\n                username: allUsers[allUsers.length - 1]?.username\n            },\n            {\n                id: \"2\",\n                type: \"scan_completed\",\n                description: \"Vulnerability scan completed on target.example.com\",\n                timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n                severity: \"medium\",\n                userId: 1,\n                username: \"admin\"\n            },\n            {\n                id: \"3\",\n                type: \"threat_detected\",\n                description: \"Critical threat detected in uploaded file\",\n                timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n                severity: \"critical\",\n                userId: 1,\n                username: \"admin\"\n            },\n            {\n                id: \"4\",\n                type: \"system_alert\",\n                description: \"High server load detected\",\n                timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n                severity: \"high\"\n            },\n            {\n                id: \"5\",\n                type: \"scan_completed\",\n                description: \"OSINT investigation completed\",\n                timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),\n                severity: \"low\",\n                userId: 1,\n                username: \"admin\"\n            }\n        ];\n        // Generate chart data (last 30 days)\n        const userGrowthChart = Array.from({\n            length: 30\n        }, (_, i)=>{\n            const date = new Date();\n            date.setDate(date.getDate() - (29 - i));\n            return {\n                date: date.toISOString().split(\"T\")[0],\n                users: Math.floor(Math.random() * 10) + i * 0.5,\n                registrations: Math.floor(Math.random() * 5)\n            };\n        });\n        const activityChart = Array.from({\n            length: 30\n        }, (_, i)=>{\n            const date = new Date();\n            date.setDate(date.getDate() - (29 - i));\n            return {\n                date: date.toISOString().split(\"T\")[0],\n                scans: Math.floor(Math.random() * 50) + 10,\n                osint: Math.floor(Math.random() * 30) + 5,\n                files: Math.floor(Math.random() * 20) + 2\n            };\n        });\n        // Bot status\n        const botStatus = {\n            whatsapp: {\n                status: \"online\",\n                uptime: 99.5,\n                lastActivity: new Date(Date.now() - 5 * 60 * 1000).toISOString()\n            },\n            telegram: {\n                status: \"online\",\n                uptime: 98.8,\n                lastActivity: new Date(Date.now() - 10 * 60 * 1000).toISOString()\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                overview: {\n                    totalUsers,\n                    activeUsers,\n                    newUsersToday,\n                    totalScans,\n                    scansToday,\n                    totalVulnerabilities,\n                    criticalVulnerabilities,\n                    systemUptime: \"99.8%\",\n                    serverLoad: Math.floor(Math.random() * 30) + 20\n                },\n                users: {\n                    total: totalUsers,\n                    active: activeUsers,\n                    inactive: totalUsers - activeUsers,\n                    byPlan: usersByPlan,\n                    growth: userGrowthChart,\n                    list: allUsers.map((user)=>({\n                            id: user.id,\n                            username: user.username,\n                            email: user.email,\n                            plan: user.plan,\n                            level: user.level,\n                            score: user.score,\n                            joinedAt: user.created_at,\n                            lastActive: user.last_active,\n                            status: new Date(user.last_active) > new Date(Date.now() - 24 * 60 * 60 * 1000) ? \"active\" : \"inactive\",\n                            emailVerified: user.email_verified\n                        }))\n                },\n                scans: {\n                    total: totalScans,\n                    today: scansToday,\n                    vulnerabilities: totalVulnerabilities,\n                    critical: criticalVulnerabilities,\n                    activity: activityChart\n                },\n                files: {\n                    total: Math.floor(totalScans * 0.3),\n                    threatsDetected: Math.floor(totalScans * 0.05),\n                    threatRate: 15\n                },\n                osint: {\n                    total: Math.floor(totalScans * 0.8),\n                    cveSearches: Math.floor(totalScans * 0.2),\n                    dorkingQueries: Math.floor(totalScans * 0.1)\n                },\n                system: {\n                    uptime: \"99.8%\",\n                    serverLoad: Math.floor(Math.random() * 30) + 20,\n                    memoryUsage: Math.floor(Math.random() * 40) + 40,\n                    diskUsage: Math.floor(Math.random() * 30) + 15\n                },\n                revenue: {\n                    total: monthlyRevenue * 12,\n                    thisMonth: monthlyRevenue,\n                    lastMonth: monthlyRevenue * 0.9,\n                    growth: 10,\n                    byPlan: Object.entries(usersByPlan).reduce((acc, [plan, count])=>{\n                        acc[plan] = count * (planPrices[plan] || 0);\n                        return acc;\n                    }, {}),\n                    projectedAnnual: monthlyRevenue * 12 * 1.2\n                },\n                bots: botStatus,\n                activities: recentActivities,\n                charts: {\n                    userGrowth: userGrowthChart,\n                    activity: activityChart\n                }\n            }\n        });\n    } catch (error) {\n        console.error(\"Admin stats error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: \"Internal server error\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth-simple.ts":
/*!****************************!*\
  !*** ./lib/auth-simple.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleAuthService: () => (/* binding */ SimpleAuthService)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key\";\nconst JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || \"your-super-secret-refresh-key\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"1h\";\nconst JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || \"7d\";\nconst BCRYPT_ROUNDS = 12;\n// Mock users for testing\nconst mockUsers = [\n    {\n        id: 1,\n        username: \"admin\",\n        email: \"<EMAIL>\",\n        full_name: \"Administrator\",\n        role: \"admin\",\n        plan: \"Elite\",\n        level: 100,\n        score: 10000,\n        streak_days: 365,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"admin123\"\n    },\n    {\n        id: 2,\n        username: \"testuser\",\n        email: \"<EMAIL>\",\n        full_name: \"Test User\",\n        role: \"user\",\n        plan: \"Free\",\n        level: 1,\n        score: 0,\n        streak_days: 0,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"test123\"\n    }\n];\nclass SimpleAuthService {\n    // Hash password\n    static async hashPassword(password) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, BCRYPT_ROUNDS);\n    }\n    // Verify password\n    static async verifyPassword(password, hash) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hash);\n    }\n    // Generate JWT tokens\n    static generateTokens(user) {\n        const payload = {\n            id: user.id,\n            username: user.username,\n            email: user.email,\n            role: user.role,\n            plan: user.plan\n        };\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n        const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_REFRESH_SECRET, {\n            expiresIn: JWT_REFRESH_EXPIRES_IN\n        });\n        return {\n            accessToken,\n            refreshToken,\n            expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds\n        };\n    }\n    // Verify JWT token\n    static verifyToken(token, isRefreshToken = false) {\n        try {\n            const secret = isRefreshToken ? JWT_REFRESH_SECRET : JWT_SECRET;\n            console.log(\"\\uD83D\\uDD0D Verifying token with secret length:\", secret.length);\n            console.log(\"\\uD83D\\uDD0D Token length:\", token.length);\n            const result = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n            console.log(\"✅ Token verification successful:\", result);\n            return result;\n        } catch (error) {\n            console.log(\"❌ Token verification error:\", error instanceof Error ? error.message : String(error));\n            return null;\n        }\n    }\n    // Register new user\n    static async register(data) {\n        try {\n            // Check if user already exists\n            const existingUser = mockUsers.find((u)=>u.email === data.email || u.username === data.username);\n            if (existingUser) {\n                return {\n                    success: false,\n                    message: \"User already exists with this email or username\"\n                };\n            }\n            // Create new user\n            const newUser = {\n                id: mockUsers.length + 1,\n                username: data.username,\n                email: data.email,\n                full_name: data.fullName || \"\",\n                role: \"user\",\n                plan: \"Free\",\n                level: 1,\n                score: 0,\n                streak_days: 0,\n                email_verified: false,\n                status: \"active\",\n                last_active: new Date(),\n                created_at: new Date(),\n                password: data.password\n            };\n            // Add to mock users\n            mockUsers.push(newUser);\n            // Remove password from user object\n            const { password, ...userWithoutPassword } = newUser;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                message: \"Registration failed\"\n            };\n        }\n    }\n    // Login user\n    static async login(email, password) {\n        try {\n            // Find user by email\n            const user = mockUsers.find((u)=>u.email === email && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Verify password (simple comparison for testing)\n            if (password !== user.password) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Remove password from user object\n            const { password: userPassword, ...userWithoutPassword } = user;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"Login failed\"\n            };\n        }\n    }\n    // Authenticate request\n    static async authenticateRequest(request) {\n        try {\n            // Get token from Authorization header or cookies\n            let token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n            if (!token) {\n                // Try to get from cookies\n                const cookies = request.headers.get(\"cookie\");\n                console.log(\"\\uD83C\\uDF6A Cookies in middleware:\", cookies);\n                if (cookies) {\n                    const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n                    token = tokenMatch ? tokenMatch[1] : undefined;\n                    console.log(\"\\uD83D\\uDD11 Token from cookies:\", token ? \"Found\" : \"Not found\");\n                }\n            }\n            if (!token) {\n                console.log(\"❌ No token found in headers or cookies\");\n                return {\n                    success: false,\n                    message: \"No token provided\"\n                };\n            }\n            // Verify token\n            console.log(\"\\uD83D\\uDD0D Verifying token in middleware...\");\n            const decoded = this.verifyToken(token);\n            if (!decoded) {\n                console.log(\"❌ Token verification failed in middleware\");\n                return {\n                    success: false,\n                    message: \"Invalid token\"\n                };\n            }\n            console.log(\"✅ Token decoded successfully:\", decoded.id, decoded.username);\n            // Get user from mock data\n            const user = mockUsers.find((u)=>u.id === decoded.id);\n            if (!user) {\n                console.log(\"❌ User not found in mock data:\", decoded.id);\n                return {\n                    success: false,\n                    message: \"User not found\"\n                };\n            }\n            console.log(\"✅ User found in middleware:\", user.username);\n            const { password, ...userWithoutPassword } = user;\n            return {\n                success: true,\n                user: userWithoutPassword\n            };\n        } catch (error) {\n            console.error(\"Authentication error:\", error);\n            return {\n                success: false,\n                message: \"Authentication failed\"\n            };\n        }\n    }\n    // Logout user (for mock, just return success)\n    static async logout(userId, _sessionToken) {\n        // In a real implementation, this would remove the session from database\n        console.log(`User ${userId} logged out`);\n    }\n    // Refresh token\n    static async refreshToken(refreshToken) {\n        try {\n            // Verify refresh token\n            const decoded = this.verifyToken(refreshToken, true);\n            if (!decoded) {\n                return {\n                    success: false,\n                    message: \"Invalid refresh token\"\n                };\n            }\n            // Get user data\n            const user = mockUsers.find((u)=>u.id === decoded.id && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"User not found or inactive\"\n                };\n            }\n            const { password, ...userWithoutPassword } = user;\n            // Generate new tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update user last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            return {\n                success: false,\n                message: \"Token refresh failed\"\n            };\n        }\n    }\n    // Get user by ID\n    static async getUserById(userId) {\n        try {\n            const user = mockUsers.find((u)=>u.id === userId);\n            if (!user) return null;\n            const { password, ...userWithoutPassword } = user;\n            return userWithoutPassword;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            return null;\n        }\n    }\n    // Get all users (admin only)\n    static getAllUsers() {\n        return mockUsers.map((user)=>{\n            const { password, ...userWithoutPassword } = user;\n            return userWithoutPassword;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvYXV0aC1zaW1wbGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNkI7QUFDQztBQWtEOUIsTUFBTUUsYUFBYUMsUUFBUUMsR0FBRyxDQUFDRixVQUFVLElBQUk7QUFDN0MsTUFBTUcscUJBQXFCRixRQUFRQyxHQUFHLENBQUNDLGtCQUFrQixJQUFJO0FBQzdELE1BQU1DLGlCQUFpQkgsUUFBUUMsR0FBRyxDQUFDRSxjQUFjLElBQUk7QUFDckQsTUFBTUMseUJBQXlCSixRQUFRQyxHQUFHLENBQUNHLHNCQUFzQixJQUFJO0FBQ3JFLE1BQU1DLGdCQUFnQjtBQUV0Qix5QkFBeUI7QUFDekIsTUFBTUMsWUFBNkM7SUFDakQ7UUFDRUMsSUFBSTtRQUNKQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsV0FBVztRQUNYQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsT0FBTztRQUNQQyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsZ0JBQWdCO1FBQ2hCQyxRQUFRO1FBQ1JDLGFBQWEsSUFBSUM7UUFDakJDLFlBQVksSUFBSUQ7UUFDaEJFLFVBQVU7SUFDWjtJQUNBO1FBQ0VkLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFdBQVc7UUFDWEMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsT0FBTztRQUNQQyxhQUFhO1FBQ2JDLGdCQUFnQjtRQUNoQkMsUUFBUTtRQUNSQyxhQUFhLElBQUlDO1FBQ2pCQyxZQUFZLElBQUlEO1FBQ2hCRSxVQUFVO0lBQ1o7Q0FDRDtBQUVNLE1BQU1DO0lBQ1gsZ0JBQWdCO0lBQ2hCLGFBQWFDLGFBQWFGLFFBQWdCLEVBQW1CO1FBQzNELE9BQU8sTUFBTXhCLG9EQUFXLENBQUN3QixVQUFVaEI7SUFDckM7SUFFQSxrQkFBa0I7SUFDbEIsYUFBYW9CLGVBQWVKLFFBQWdCLEVBQUVHLElBQVksRUFBb0I7UUFDNUUsT0FBTyxNQUFNM0IsdURBQWMsQ0FBQ3dCLFVBQVVHO0lBQ3hDO0lBRUEsc0JBQXNCO0lBQ3RCLE9BQU9HLGVBQWVDLElBQVUsRUFBYztRQUM1QyxNQUFNQyxVQUFVO1lBQ2R0QixJQUFJcUIsS0FBS3JCLEVBQUU7WUFDWEMsVUFBVW9CLEtBQUtwQixRQUFRO1lBQ3ZCQyxPQUFPbUIsS0FBS25CLEtBQUs7WUFDakJFLE1BQU1pQixLQUFLakIsSUFBSTtZQUNmQyxNQUFNZ0IsS0FBS2hCLElBQUk7UUFDakI7UUFFQSxNQUFNa0IsY0FBY2hDLHdEQUFRLENBQUMrQixTQUFTOUIsWUFBWTtZQUFFaUMsV0FBVzdCO1FBQWU7UUFDOUUsTUFBTThCLGVBQWVuQyx3REFBUSxDQUFDK0IsU0FBUzNCLG9CQUFvQjtZQUFFOEIsV0FBVzVCO1FBQXVCO1FBRS9GLE9BQU87WUFDTDBCO1lBQ0FHO1lBQ0FELFdBQVcsSUFBSSxLQUFLLEtBQUssR0FBRyxvQkFBb0I7UUFDbEQ7SUFDRjtJQUVBLG1CQUFtQjtJQUNuQixPQUFPRSxZQUFZQyxLQUFhLEVBQUVDLGlCQUFpQixLQUFLLEVBQU87UUFDN0QsSUFBSTtZQUNGLE1BQU1DLFNBQVNELGlCQUFpQmxDLHFCQUFxQkg7WUFDckR1QyxRQUFRQyxHQUFHLENBQUMsb0RBQTBDRixPQUFPRyxNQUFNO1lBQ25FRixRQUFRQyxHQUFHLENBQUMsOEJBQW9CSixNQUFNSyxNQUFNO1lBQzVDLE1BQU1DLFNBQVMzQywwREFBVSxDQUFDcUMsT0FBT0U7WUFDakNDLFFBQVFDLEdBQUcsQ0FBQyxvQ0FBb0NFO1lBQ2hELE9BQU9BO1FBQ1QsRUFBRSxPQUFPRSxPQUFPO1lBQ2RMLFFBQVFDLEdBQUcsQ0FBQywrQkFBK0JJLGlCQUFpQkMsUUFBUUQsTUFBTUUsT0FBTyxHQUFHQyxPQUFPSDtZQUMzRixPQUFPO1FBQ1Q7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQixhQUFhSSxTQUFTQyxJQUFrQixFQUF1QjtRQUM3RCxJQUFJO1lBQ0YsK0JBQStCO1lBQy9CLE1BQU1DLGVBQWUzQyxVQUFVNEMsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFMUMsS0FBSyxLQUFLdUMsS0FBS3ZDLEtBQUssSUFBSTBDLEVBQUUzQyxRQUFRLEtBQUt3QyxLQUFLeEMsUUFBUTtZQUMvRixJQUFJeUMsY0FBYztnQkFDaEIsT0FBTztvQkFBRUcsU0FBUztvQkFBT1AsU0FBUztnQkFBa0Q7WUFDdEY7WUFFQSxrQkFBa0I7WUFDbEIsTUFBTVEsVUFBdUM7Z0JBQzNDOUMsSUFBSUQsVUFBVWtDLE1BQU0sR0FBRztnQkFDdkJoQyxVQUFVd0MsS0FBS3hDLFFBQVE7Z0JBQ3ZCQyxPQUFPdUMsS0FBS3ZDLEtBQUs7Z0JBQ2pCQyxXQUFXc0MsS0FBS00sUUFBUSxJQUFJO2dCQUM1QjNDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BDLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLGdCQUFnQjtnQkFDaEJDLFFBQVE7Z0JBQ1JDLGFBQWEsSUFBSUM7Z0JBQ2pCQyxZQUFZLElBQUlEO2dCQUNoQkUsVUFBVTJCLEtBQUszQixRQUFRO1lBQ3pCO1lBRUEsb0JBQW9CO1lBQ3BCZixVQUFVaUQsSUFBSSxDQUFDRjtZQUVmLG1DQUFtQztZQUNuQyxNQUFNLEVBQUVoQyxRQUFRLEVBQUUsR0FBR21DLHFCQUFxQixHQUFHSDtZQUU3QyxrQkFBa0I7WUFDbEIsTUFBTUksU0FBUyxJQUFJLENBQUM5QixjQUFjLENBQUM2QjtZQUVuQyxPQUFPO2dCQUFFSixTQUFTO2dCQUFNeEIsTUFBTTRCO2dCQUFxQkM7WUFBTztRQUM1RCxFQUFFLE9BQU9kLE9BQU87WUFDZEwsUUFBUUssS0FBSyxDQUFDLHVCQUF1QkE7WUFDckMsT0FBTztnQkFBRVMsU0FBUztnQkFBT1AsU0FBUztZQUFzQjtRQUMxRDtJQUNGO0lBRUEsYUFBYTtJQUNiLGFBQWFhLE1BQU1qRCxLQUFhLEVBQUVZLFFBQWdCLEVBQXVCO1FBQ3ZFLElBQUk7WUFDRixxQkFBcUI7WUFDckIsTUFBTU8sT0FBT3RCLFVBQVU0QyxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUUxQyxLQUFLLEtBQUtBLFNBQVUwQyxDQUFBQSxFQUFFbEMsTUFBTSxLQUFLLFlBQVksQ0FBQ2tDLEVBQUVsQyxNQUFNO1lBRXpGLElBQUksQ0FBQ1csTUFBTTtnQkFDVCxPQUFPO29CQUFFd0IsU0FBUztvQkFBT1AsU0FBUztnQkFBNEI7WUFDaEU7WUFFQSxrREFBa0Q7WUFDbEQsSUFBSXhCLGFBQWFPLEtBQUtQLFFBQVEsRUFBRTtnQkFDOUIsT0FBTztvQkFBRStCLFNBQVM7b0JBQU9QLFNBQVM7Z0JBQTRCO1lBQ2hFO1lBRUEsbUNBQW1DO1lBQ25DLE1BQU0sRUFBRXhCLFVBQVVzQyxZQUFZLEVBQUUsR0FBR0gscUJBQXFCLEdBQUc1QjtZQUUzRCxrQkFBa0I7WUFDbEIsTUFBTTZCLFNBQVMsSUFBSSxDQUFDOUIsY0FBYyxDQUFDNkI7WUFFbkMscUJBQXFCO1lBQ3JCNUIsS0FBS1YsV0FBVyxHQUFHLElBQUlDO1lBRXZCLE9BQU87Z0JBQUVpQyxTQUFTO2dCQUFNeEIsTUFBTTRCO2dCQUFxQkM7WUFBTztRQUM1RCxFQUFFLE9BQU9kLE9BQU87WUFDZEwsUUFBUUssS0FBSyxDQUFDLGdCQUFnQkE7WUFDOUIsT0FBTztnQkFBRVMsU0FBUztnQkFBT1AsU0FBUztZQUFlO1FBQ25EO0lBQ0Y7SUFFQSx1QkFBdUI7SUFDdkIsYUFBYWUsb0JBQW9CQyxPQUFvQixFQUFnRTtRQUNuSCxJQUFJO1lBQ0YsaURBQWlEO1lBQ2pELElBQUkxQixRQUFRMEIsUUFBUUMsT0FBTyxDQUFDQyxHQUFHLENBQUMsa0JBQWtCQyxRQUFRLFdBQVc7WUFFckUsSUFBSSxDQUFDN0IsT0FBTztnQkFDViwwQkFBMEI7Z0JBQzFCLE1BQU04QixVQUFVSixRQUFRQyxPQUFPLENBQUNDLEdBQUcsQ0FBQztnQkFDcEN6QixRQUFRQyxHQUFHLENBQUMsdUNBQTZCMEI7Z0JBQ3pDLElBQUlBLFNBQVM7b0JBQ1gsTUFBTUMsYUFBYUQsUUFBUUUsS0FBSyxDQUFDO29CQUNqQ2hDLFFBQVErQixhQUFhQSxVQUFVLENBQUMsRUFBRSxHQUFHRTtvQkFDckM5QixRQUFRQyxHQUFHLENBQUMsb0NBQTBCSixRQUFRLFVBQVU7Z0JBQzFEO1lBQ0Y7WUFFQSxJQUFJLENBQUNBLE9BQU87Z0JBQ1ZHLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixPQUFPO29CQUFFYSxTQUFTO29CQUFPUCxTQUFTO2dCQUFvQjtZQUN4RDtZQUVBLGVBQWU7WUFDZlAsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTThCLFVBQVUsSUFBSSxDQUFDbkMsV0FBVyxDQUFDQztZQUNqQyxJQUFJLENBQUNrQyxTQUFTO2dCQUNaL0IsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLE9BQU87b0JBQUVhLFNBQVM7b0JBQU9QLFNBQVM7Z0JBQWdCO1lBQ3BEO1lBRUFQLFFBQVFDLEdBQUcsQ0FBQyxpQ0FBaUM4QixRQUFROUQsRUFBRSxFQUFFOEQsUUFBUTdELFFBQVE7WUFFekUsMEJBQTBCO1lBQzFCLE1BQU1vQixPQUFPdEIsVUFBVTRDLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRTVDLEVBQUUsS0FBSzhELFFBQVE5RCxFQUFFO1lBQ3BELElBQUksQ0FBQ3FCLE1BQU07Z0JBQ1RVLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0M4QixRQUFROUQsRUFBRTtnQkFDeEQsT0FBTztvQkFBRTZDLFNBQVM7b0JBQU9QLFNBQVM7Z0JBQWlCO1lBQ3JEO1lBRUFQLFFBQVFDLEdBQUcsQ0FBQywrQkFBK0JYLEtBQUtwQixRQUFRO1lBQ3hELE1BQU0sRUFBRWEsUUFBUSxFQUFFLEdBQUdtQyxxQkFBcUIsR0FBRzVCO1lBRTdDLE9BQU87Z0JBQUV3QixTQUFTO2dCQUFNeEIsTUFBTTRCO1lBQW9CO1FBQ3BELEVBQUUsT0FBT2IsT0FBTztZQUNkTCxRQUFRSyxLQUFLLENBQUMseUJBQXlCQTtZQUN2QyxPQUFPO2dCQUFFUyxTQUFTO2dCQUFPUCxTQUFTO1lBQXdCO1FBQzVEO0lBQ0Y7SUFFQSw4Q0FBOEM7SUFDOUMsYUFBYXlCLE9BQU9DLE1BQWMsRUFBRUMsYUFBcUIsRUFBaUI7UUFDeEUsd0VBQXdFO1FBQ3hFbEMsUUFBUUMsR0FBRyxDQUFDLENBQUMsS0FBSyxFQUFFZ0MsT0FBTyxXQUFXLENBQUM7SUFDekM7SUFFQSxnQkFBZ0I7SUFDaEIsYUFBYXRDLGFBQWFBLFlBQW9CLEVBQXVCO1FBQ25FLElBQUk7WUFDRix1QkFBdUI7WUFDdkIsTUFBTW9DLFVBQVUsSUFBSSxDQUFDbkMsV0FBVyxDQUFDRCxjQUFjO1lBQy9DLElBQUksQ0FBQ29DLFNBQVM7Z0JBQ1osT0FBTztvQkFBRWpCLFNBQVM7b0JBQU9QLFNBQVM7Z0JBQXdCO1lBQzVEO1lBRUEsZ0JBQWdCO1lBQ2hCLE1BQU1qQixPQUFPdEIsVUFBVTRDLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRTVDLEVBQUUsS0FBSzhELFFBQVE5RCxFQUFFLElBQUs0QyxDQUFBQSxFQUFFbEMsTUFBTSxLQUFLLFlBQVksQ0FBQ2tDLEVBQUVsQyxNQUFNO1lBQzNGLElBQUksQ0FBQ1csTUFBTTtnQkFDVCxPQUFPO29CQUFFd0IsU0FBUztvQkFBT1AsU0FBUztnQkFBNkI7WUFDakU7WUFFQSxNQUFNLEVBQUV4QixRQUFRLEVBQUUsR0FBR21DLHFCQUFxQixHQUFHNUI7WUFFN0Msc0JBQXNCO1lBQ3RCLE1BQU02QixTQUFTLElBQUksQ0FBQzlCLGNBQWMsQ0FBQzZCO1lBRW5DLDBCQUEwQjtZQUMxQjVCLEtBQUtWLFdBQVcsR0FBRyxJQUFJQztZQUV2QixPQUFPO2dCQUFFaUMsU0FBUztnQkFBTXhCLE1BQU00QjtnQkFBcUJDO1lBQU87UUFDNUQsRUFBRSxPQUFPZCxPQUFPO1lBQ2RMLFFBQVFLLEtBQUssQ0FBQyx3QkFBd0JBO1lBQ3RDLE9BQU87Z0JBQUVTLFNBQVM7Z0JBQU9QLFNBQVM7WUFBdUI7UUFDM0Q7SUFDRjtJQUVBLGlCQUFpQjtJQUNqQixhQUFhNEIsWUFBWUYsTUFBYyxFQUF3QjtRQUM3RCxJQUFJO1lBQ0YsTUFBTTNDLE9BQU90QixVQUFVNEMsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFNUMsRUFBRSxLQUFLZ0U7WUFDMUMsSUFBSSxDQUFDM0MsTUFBTSxPQUFPO1lBRWxCLE1BQU0sRUFBRVAsUUFBUSxFQUFFLEdBQUdtQyxxQkFBcUIsR0FBRzVCO1lBQzdDLE9BQU80QjtRQUNULEVBQUUsT0FBT2IsT0FBTztZQUNkTCxRQUFRSyxLQUFLLENBQUMsbUJBQW1CQTtZQUNqQyxPQUFPO1FBQ1Q7SUFDRjtJQUVBLDZCQUE2QjtJQUM3QixPQUFPK0IsY0FBc0I7UUFDM0IsT0FBT3BFLFVBQVVxRSxHQUFHLENBQUMvQyxDQUFBQTtZQUNuQixNQUFNLEVBQUVQLFFBQVEsRUFBRSxHQUFHbUMscUJBQXFCLEdBQUc1QjtZQUM3QyxPQUFPNEI7UUFDVDtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYl9rb2RleGd1YXJkLy4vbGliL2F1dGgtc2ltcGxlLnRzPzZlMjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGJjcnlwdCBmcm9tICdiY3J5cHRqcydcbmltcG9ydCBqd3QgZnJvbSAnanNvbndlYnRva2VuJ1xuaW1wb3J0IHsgTmV4dFJlcXVlc3QgfSBmcm9tICduZXh0L3NlcnZlcidcblxuZXhwb3J0IGludGVyZmFjZSBVc2VyIHtcbiAgaWQ6IG51bWJlclxuICB1c2VybmFtZTogc3RyaW5nXG4gIGVtYWlsOiBzdHJpbmdcbiAgZnVsbF9uYW1lPzogc3RyaW5nXG4gIHJvbGU6ICd1c2VyJyB8ICdhZG1pbicgfCAnc3VwZXJfYWRtaW4nIHwgJ21vZGVyYXRvcidcbiAgcGxhbjogJ0ZyZWUnIHwgJ1N0dWRlbnQnIHwgJ0hvYmJ5JyB8ICdCdWdodW50ZXInIHwgJ0N5YmVyc2VjdXJpdHknIHwgJ1BybycgfCAnRXhwZXJ0JyB8ICdFbGl0ZSdcbiAgbGV2ZWw6IG51bWJlclxuICBzY29yZTogbnVtYmVyXG4gIHN0cmVha19kYXlzOiBudW1iZXJcbiAgZW1haWxfdmVyaWZpZWQ6IGJvb2xlYW5cbiAgc3RhdHVzPzogJ2FjdGl2ZScgfCAnaW5hY3RpdmUnIHwgJ3N1c3BlbmRlZCcgfCAnYmFubmVkJ1xuICBwaG9uZT86IHN0cmluZ1xuICB0ZWxlZ3JhbV9pZD86IG51bWJlclxuICB3aGF0c2FwcF9udW1iZXI/OiBzdHJpbmdcbiAgYXZhdGFyX3VybD86IHN0cmluZ1xuICB0aW1lem9uZT86IHN0cmluZ1xuICBsYW5ndWFnZT86IHN0cmluZ1xuICBsYXN0X2FjdGl2ZTogRGF0ZVxuICBjcmVhdGVkX2F0OiBEYXRlXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQXV0aFRva2VucyB7XG4gIGFjY2Vzc1Rva2VuOiBzdHJpbmdcbiAgcmVmcmVzaFRva2VuOiBzdHJpbmdcbiAgZXhwaXJlc0luOiBudW1iZXJcbn1cblxuZXhwb3J0IGludGVyZmFjZSBMb2dpbkNyZWRlbnRpYWxzIHtcbiAgZW1haWw6IHN0cmluZ1xuICBwYXNzd29yZDogc3RyaW5nXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUmVnaXN0ZXJEYXRhIHtcbiAgdXNlcm5hbWU6IHN0cmluZ1xuICBlbWFpbDogc3RyaW5nXG4gIHBhc3N3b3JkOiBzdHJpbmdcbiAgZnVsbE5hbWU/OiBzdHJpbmdcbn1cblxuaW50ZXJmYWNlIEF1dGhSZXN1bHQge1xuICBzdWNjZXNzOiBib29sZWFuXG4gIHVzZXI/OiBVc2VyXG4gIHRva2Vucz86IEF1dGhUb2tlbnNcbiAgbWVzc2FnZT86IHN0cmluZ1xufVxuXG5jb25zdCBKV1RfU0VDUkVUID0gcHJvY2Vzcy5lbnYuSldUX1NFQ1JFVCB8fCAneW91ci1zdXBlci1zZWNyZXQtand0LWtleSdcbmNvbnN0IEpXVF9SRUZSRVNIX1NFQ1JFVCA9IHByb2Nlc3MuZW52LkpXVF9SRUZSRVNIX1NFQ1JFVCB8fCAneW91ci1zdXBlci1zZWNyZXQtcmVmcmVzaC1rZXknXG5jb25zdCBKV1RfRVhQSVJFU19JTiA9IHByb2Nlc3MuZW52LkpXVF9FWFBJUkVTX0lOIHx8ICcxaCdcbmNvbnN0IEpXVF9SRUZSRVNIX0VYUElSRVNfSU4gPSBwcm9jZXNzLmVudi5KV1RfUkVGUkVTSF9FWFBJUkVTX0lOIHx8ICc3ZCdcbmNvbnN0IEJDUllQVF9ST1VORFMgPSAxMlxuXG4vLyBNb2NrIHVzZXJzIGZvciB0ZXN0aW5nXG5jb25zdCBtb2NrVXNlcnM6IChVc2VyICYgeyBwYXNzd29yZDogc3RyaW5nIH0pW10gPSBbXG4gIHtcbiAgICBpZDogMSxcbiAgICB1c2VybmFtZTogJ2FkbWluJyxcbiAgICBlbWFpbDogJ2FkbWluQGtvZGV4Z3VhcmQuY29tJyxcbiAgICBmdWxsX25hbWU6ICdBZG1pbmlzdHJhdG9yJyxcbiAgICByb2xlOiAnYWRtaW4nLFxuICAgIHBsYW46ICdFbGl0ZScsXG4gICAgbGV2ZWw6IDEwMCxcbiAgICBzY29yZTogMTAwMDAsXG4gICAgc3RyZWFrX2RheXM6IDM2NSxcbiAgICBlbWFpbF92ZXJpZmllZDogdHJ1ZSxcbiAgICBzdGF0dXM6ICdhY3RpdmUnLFxuICAgIGxhc3RfYWN0aXZlOiBuZXcgRGF0ZSgpLFxuICAgIGNyZWF0ZWRfYXQ6IG5ldyBEYXRlKCksXG4gICAgcGFzc3dvcmQ6ICdhZG1pbjEyMydcbiAgfSxcbiAge1xuICAgIGlkOiAyLFxuICAgIHVzZXJuYW1lOiAndGVzdHVzZXInLFxuICAgIGVtYWlsOiAndGVzdEBrb2RleGd1YXJkLmNvbScsXG4gICAgZnVsbF9uYW1lOiAnVGVzdCBVc2VyJyxcbiAgICByb2xlOiAndXNlcicsXG4gICAgcGxhbjogJ0ZyZWUnLFxuICAgIGxldmVsOiAxLFxuICAgIHNjb3JlOiAwLFxuICAgIHN0cmVha19kYXlzOiAwLFxuICAgIGVtYWlsX3ZlcmlmaWVkOiB0cnVlLFxuICAgIHN0YXR1czogJ2FjdGl2ZScsXG4gICAgbGFzdF9hY3RpdmU6IG5ldyBEYXRlKCksXG4gICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKSxcbiAgICBwYXNzd29yZDogJ3Rlc3QxMjMnXG4gIH1cbl1cblxuZXhwb3J0IGNsYXNzIFNpbXBsZUF1dGhTZXJ2aWNlIHtcbiAgLy8gSGFzaCBwYXNzd29yZFxuICBzdGF0aWMgYXN5bmMgaGFzaFBhc3N3b3JkKHBhc3N3b3JkOiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZz4ge1xuICAgIHJldHVybiBhd2FpdCBiY3J5cHQuaGFzaChwYXNzd29yZCwgQkNSWVBUX1JPVU5EUylcbiAgfVxuXG4gIC8vIFZlcmlmeSBwYXNzd29yZFxuICBzdGF0aWMgYXN5bmMgdmVyaWZ5UGFzc3dvcmQocGFzc3dvcmQ6IHN0cmluZywgaGFzaDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgcmV0dXJuIGF3YWl0IGJjcnlwdC5jb21wYXJlKHBhc3N3b3JkLCBoYXNoKVxuICB9XG5cbiAgLy8gR2VuZXJhdGUgSldUIHRva2Vuc1xuICBzdGF0aWMgZ2VuZXJhdGVUb2tlbnModXNlcjogVXNlcik6IEF1dGhUb2tlbnMge1xuICAgIGNvbnN0IHBheWxvYWQgPSB7XG4gICAgICBpZDogdXNlci5pZCxcbiAgICAgIHVzZXJuYW1lOiB1c2VyLnVzZXJuYW1lLFxuICAgICAgZW1haWw6IHVzZXIuZW1haWwsXG4gICAgICByb2xlOiB1c2VyLnJvbGUsXG4gICAgICBwbGFuOiB1c2VyLnBsYW5cbiAgICB9XG5cbiAgICBjb25zdCBhY2Nlc3NUb2tlbiA9IGp3dC5zaWduKHBheWxvYWQsIEpXVF9TRUNSRVQsIHsgZXhwaXJlc0luOiBKV1RfRVhQSVJFU19JTiB9KVxuICAgIGNvbnN0IHJlZnJlc2hUb2tlbiA9IGp3dC5zaWduKHBheWxvYWQsIEpXVF9SRUZSRVNIX1NFQ1JFVCwgeyBleHBpcmVzSW46IEpXVF9SRUZSRVNIX0VYUElSRVNfSU4gfSlcblxuICAgIHJldHVybiB7XG4gICAgICBhY2Nlc3NUb2tlbixcbiAgICAgIHJlZnJlc2hUb2tlbixcbiAgICAgIGV4cGlyZXNJbjogNyAqIDI0ICogNjAgKiA2MCAvLyA3IGRheXMgaW4gc2Vjb25kc1xuICAgIH1cbiAgfVxuXG4gIC8vIFZlcmlmeSBKV1QgdG9rZW5cbiAgc3RhdGljIHZlcmlmeVRva2VuKHRva2VuOiBzdHJpbmcsIGlzUmVmcmVzaFRva2VuID0gZmFsc2UpOiBhbnkge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBzZWNyZXQgPSBpc1JlZnJlc2hUb2tlbiA/IEpXVF9SRUZSRVNIX1NFQ1JFVCA6IEpXVF9TRUNSRVRcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFZlcmlmeWluZyB0b2tlbiB3aXRoIHNlY3JldCBsZW5ndGg6Jywgc2VjcmV0Lmxlbmd0aClcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFRva2VuIGxlbmd0aDonLCB0b2tlbi5sZW5ndGgpXG4gICAgICBjb25zdCByZXN1bHQgPSBqd3QudmVyaWZ5KHRva2VuLCBzZWNyZXQpXG4gICAgICBjb25zb2xlLmxvZygn4pyFIFRva2VuIHZlcmlmaWNhdGlvbiBzdWNjZXNzZnVsOicsIHJlc3VsdClcbiAgICAgIHJldHVybiByZXN1bHRcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5sb2coJ+KdjCBUb2tlbiB2ZXJpZmljYXRpb24gZXJyb3I6JywgZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBTdHJpbmcoZXJyb3IpKVxuICAgICAgcmV0dXJuIG51bGxcbiAgICB9XG4gIH1cblxuICAvLyBSZWdpc3RlciBuZXcgdXNlclxuICBzdGF0aWMgYXN5bmMgcmVnaXN0ZXIoZGF0YTogUmVnaXN0ZXJEYXRhKTogUHJvbWlzZTxBdXRoUmVzdWx0PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIENoZWNrIGlmIHVzZXIgYWxyZWFkeSBleGlzdHNcbiAgICAgIGNvbnN0IGV4aXN0aW5nVXNlciA9IG1vY2tVc2Vycy5maW5kKHUgPT4gdS5lbWFpbCA9PT0gZGF0YS5lbWFpbCB8fCB1LnVzZXJuYW1lID09PSBkYXRhLnVzZXJuYW1lKVxuICAgICAgaWYgKGV4aXN0aW5nVXNlcikge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgbWVzc2FnZTogJ1VzZXIgYWxyZWFkeSBleGlzdHMgd2l0aCB0aGlzIGVtYWlsIG9yIHVzZXJuYW1lJyB9XG4gICAgICB9XG5cbiAgICAgIC8vIENyZWF0ZSBuZXcgdXNlclxuICAgICAgY29uc3QgbmV3VXNlcjogVXNlciAmIHsgcGFzc3dvcmQ6IHN0cmluZyB9ID0ge1xuICAgICAgICBpZDogbW9ja1VzZXJzLmxlbmd0aCArIDEsXG4gICAgICAgIHVzZXJuYW1lOiBkYXRhLnVzZXJuYW1lLFxuICAgICAgICBlbWFpbDogZGF0YS5lbWFpbCxcbiAgICAgICAgZnVsbF9uYW1lOiBkYXRhLmZ1bGxOYW1lIHx8ICcnLFxuICAgICAgICByb2xlOiAndXNlcicsXG4gICAgICAgIHBsYW46ICdGcmVlJyxcbiAgICAgICAgbGV2ZWw6IDEsXG4gICAgICAgIHNjb3JlOiAwLFxuICAgICAgICBzdHJlYWtfZGF5czogMCxcbiAgICAgICAgZW1haWxfdmVyaWZpZWQ6IGZhbHNlLFxuICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLFxuICAgICAgICBsYXN0X2FjdGl2ZTogbmV3IERhdGUoKSxcbiAgICAgICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKSxcbiAgICAgICAgcGFzc3dvcmQ6IGRhdGEucGFzc3dvcmRcbiAgICAgIH1cblxuICAgICAgLy8gQWRkIHRvIG1vY2sgdXNlcnNcbiAgICAgIG1vY2tVc2Vycy5wdXNoKG5ld1VzZXIpXG5cbiAgICAgIC8vIFJlbW92ZSBwYXNzd29yZCBmcm9tIHVzZXIgb2JqZWN0XG4gICAgICBjb25zdCB7IHBhc3N3b3JkLCAuLi51c2VyV2l0aG91dFBhc3N3b3JkIH0gPSBuZXdVc2VyXG5cbiAgICAgIC8vIEdlbmVyYXRlIHRva2Vuc1xuICAgICAgY29uc3QgdG9rZW5zID0gdGhpcy5nZW5lcmF0ZVRva2Vucyh1c2VyV2l0aG91dFBhc3N3b3JkKVxuXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCB1c2VyOiB1c2VyV2l0aG91dFBhc3N3b3JkLCB0b2tlbnMgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdSZWdpc3RyYXRpb24gZXJyb3I6JywgZXJyb3IpXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgbWVzc2FnZTogJ1JlZ2lzdHJhdGlvbiBmYWlsZWQnIH1cbiAgICB9XG4gIH1cblxuICAvLyBMb2dpbiB1c2VyXG4gIHN0YXRpYyBhc3luYyBsb2dpbihlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKTogUHJvbWlzZTxBdXRoUmVzdWx0PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEZpbmQgdXNlciBieSBlbWFpbFxuICAgICAgY29uc3QgdXNlciA9IG1vY2tVc2Vycy5maW5kKHUgPT4gdS5lbWFpbCA9PT0gZW1haWwgJiYgKHUuc3RhdHVzID09PSAnYWN0aXZlJyB8fCAhdS5zdGF0dXMpKVxuXG4gICAgICBpZiAoIXVzZXIpIHtcbiAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIG1lc3NhZ2U6ICdJbnZhbGlkIGVtYWlsIG9yIHBhc3N3b3JkJyB9XG4gICAgICB9XG5cbiAgICAgIC8vIFZlcmlmeSBwYXNzd29yZCAoc2ltcGxlIGNvbXBhcmlzb24gZm9yIHRlc3RpbmcpXG4gICAgICBpZiAocGFzc3dvcmQgIT09IHVzZXIucGFzc3dvcmQpIHtcbiAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIG1lc3NhZ2U6ICdJbnZhbGlkIGVtYWlsIG9yIHBhc3N3b3JkJyB9XG4gICAgICB9XG5cbiAgICAgIC8vIFJlbW92ZSBwYXNzd29yZCBmcm9tIHVzZXIgb2JqZWN0XG4gICAgICBjb25zdCB7IHBhc3N3b3JkOiB1c2VyUGFzc3dvcmQsIC4uLnVzZXJXaXRob3V0UGFzc3dvcmQgfSA9IHVzZXJcblxuICAgICAgLy8gR2VuZXJhdGUgdG9rZW5zXG4gICAgICBjb25zdCB0b2tlbnMgPSB0aGlzLmdlbmVyYXRlVG9rZW5zKHVzZXJXaXRob3V0UGFzc3dvcmQpXG5cbiAgICAgIC8vIFVwZGF0ZSBsYXN0IGFjdGl2ZVxuICAgICAgdXNlci5sYXN0X2FjdGl2ZSA9IG5ldyBEYXRlKClcblxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgdXNlcjogdXNlcldpdGhvdXRQYXNzd29yZCwgdG9rZW5zIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignTG9naW4gZXJyb3I6JywgZXJyb3IpXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgbWVzc2FnZTogJ0xvZ2luIGZhaWxlZCcgfVxuICAgIH1cbiAgfVxuXG4gIC8vIEF1dGhlbnRpY2F0ZSByZXF1ZXN0XG4gIHN0YXRpYyBhc3luYyBhdXRoZW50aWNhdGVSZXF1ZXN0KHJlcXVlc3Q6IE5leHRSZXF1ZXN0KTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IHVzZXI/OiBVc2VyOyBtZXNzYWdlPzogc3RyaW5nIH0+IHtcbiAgICB0cnkge1xuICAgICAgLy8gR2V0IHRva2VuIGZyb20gQXV0aG9yaXphdGlvbiBoZWFkZXIgb3IgY29va2llc1xuICAgICAgbGV0IHRva2VuID0gcmVxdWVzdC5oZWFkZXJzLmdldCgnYXV0aG9yaXphdGlvbicpPy5yZXBsYWNlKCdCZWFyZXIgJywgJycpXG5cbiAgICAgIGlmICghdG9rZW4pIHtcbiAgICAgICAgLy8gVHJ5IHRvIGdldCBmcm9tIGNvb2tpZXNcbiAgICAgICAgY29uc3QgY29va2llcyA9IHJlcXVlc3QuaGVhZGVycy5nZXQoJ2Nvb2tpZScpXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn42qIENvb2tpZXMgaW4gbWlkZGxld2FyZTonLCBjb29raWVzKVxuICAgICAgICBpZiAoY29va2llcykge1xuICAgICAgICAgIGNvbnN0IHRva2VuTWF0Y2ggPSBjb29raWVzLm1hdGNoKC9hY2Nlc3NUb2tlbj0oW147XSspLylcbiAgICAgICAgICB0b2tlbiA9IHRva2VuTWF0Y2ggPyB0b2tlbk1hdGNoWzFdIDogdW5kZWZpbmVkXG4gICAgICAgICAgY29uc29sZS5sb2coJ/CflJEgVG9rZW4gZnJvbSBjb29raWVzOicsIHRva2VuID8gJ0ZvdW5kJyA6ICdOb3QgZm91bmQnKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGlmICghdG9rZW4pIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KdjCBObyB0b2tlbiBmb3VuZCBpbiBoZWFkZXJzIG9yIGNvb2tpZXMnKVxuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgbWVzc2FnZTogJ05vIHRva2VuIHByb3ZpZGVkJyB9XG4gICAgICB9XG5cbiAgICAgIC8vIFZlcmlmeSB0b2tlblxuICAgICAgY29uc29sZS5sb2coJ/CflI0gVmVyaWZ5aW5nIHRva2VuIGluIG1pZGRsZXdhcmUuLi4nKVxuICAgICAgY29uc3QgZGVjb2RlZCA9IHRoaXMudmVyaWZ5VG9rZW4odG9rZW4pXG4gICAgICBpZiAoIWRlY29kZWQpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KdjCBUb2tlbiB2ZXJpZmljYXRpb24gZmFpbGVkIGluIG1pZGRsZXdhcmUnKVxuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgbWVzc2FnZTogJ0ludmFsaWQgdG9rZW4nIH1cbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ+KchSBUb2tlbiBkZWNvZGVkIHN1Y2Nlc3NmdWxseTonLCBkZWNvZGVkLmlkLCBkZWNvZGVkLnVzZXJuYW1lKVxuXG4gICAgICAvLyBHZXQgdXNlciBmcm9tIG1vY2sgZGF0YVxuICAgICAgY29uc3QgdXNlciA9IG1vY2tVc2Vycy5maW5kKHUgPT4gdS5pZCA9PT0gZGVjb2RlZC5pZClcbiAgICAgIGlmICghdXNlcikge1xuICAgICAgICBjb25zb2xlLmxvZygn4p2MIFVzZXIgbm90IGZvdW5kIGluIG1vY2sgZGF0YTonLCBkZWNvZGVkLmlkKVxuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgbWVzc2FnZTogJ1VzZXIgbm90IGZvdW5kJyB9XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfinIUgVXNlciBmb3VuZCBpbiBtaWRkbGV3YXJlOicsIHVzZXIudXNlcm5hbWUpXG4gICAgICBjb25zdCB7IHBhc3N3b3JkLCAuLi51c2VyV2l0aG91dFBhc3N3b3JkIH0gPSB1c2VyXG5cbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIHVzZXI6IHVzZXJXaXRob3V0UGFzc3dvcmQgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdBdXRoZW50aWNhdGlvbiBlcnJvcjonLCBlcnJvcilcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBtZXNzYWdlOiAnQXV0aGVudGljYXRpb24gZmFpbGVkJyB9XG4gICAgfVxuICB9XG5cbiAgLy8gTG9nb3V0IHVzZXIgKGZvciBtb2NrLCBqdXN0IHJldHVybiBzdWNjZXNzKVxuICBzdGF0aWMgYXN5bmMgbG9nb3V0KHVzZXJJZDogbnVtYmVyLCBfc2Vzc2lvblRva2VuOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICAvLyBJbiBhIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgcmVtb3ZlIHRoZSBzZXNzaW9uIGZyb20gZGF0YWJhc2VcbiAgICBjb25zb2xlLmxvZyhgVXNlciAke3VzZXJJZH0gbG9nZ2VkIG91dGApXG4gIH1cblxuICAvLyBSZWZyZXNoIHRva2VuXG4gIHN0YXRpYyBhc3luYyByZWZyZXNoVG9rZW4ocmVmcmVzaFRva2VuOiBzdHJpbmcpOiBQcm9taXNlPEF1dGhSZXN1bHQ+IHtcbiAgICB0cnkge1xuICAgICAgLy8gVmVyaWZ5IHJlZnJlc2ggdG9rZW5cbiAgICAgIGNvbnN0IGRlY29kZWQgPSB0aGlzLnZlcmlmeVRva2VuKHJlZnJlc2hUb2tlbiwgdHJ1ZSlcbiAgICAgIGlmICghZGVjb2RlZCkge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgbWVzc2FnZTogJ0ludmFsaWQgcmVmcmVzaCB0b2tlbicgfVxuICAgICAgfVxuXG4gICAgICAvLyBHZXQgdXNlciBkYXRhXG4gICAgICBjb25zdCB1c2VyID0gbW9ja1VzZXJzLmZpbmQodSA9PiB1LmlkID09PSBkZWNvZGVkLmlkICYmICh1LnN0YXR1cyA9PT0gJ2FjdGl2ZScgfHwgIXUuc3RhdHVzKSlcbiAgICAgIGlmICghdXNlcikge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgbWVzc2FnZTogJ1VzZXIgbm90IGZvdW5kIG9yIGluYWN0aXZlJyB9XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHsgcGFzc3dvcmQsIC4uLnVzZXJXaXRob3V0UGFzc3dvcmQgfSA9IHVzZXJcblxuICAgICAgLy8gR2VuZXJhdGUgbmV3IHRva2Vuc1xuICAgICAgY29uc3QgdG9rZW5zID0gdGhpcy5nZW5lcmF0ZVRva2Vucyh1c2VyV2l0aG91dFBhc3N3b3JkKVxuXG4gICAgICAvLyBVcGRhdGUgdXNlciBsYXN0IGFjdGl2ZVxuICAgICAgdXNlci5sYXN0X2FjdGl2ZSA9IG5ldyBEYXRlKClcblxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgdXNlcjogdXNlcldpdGhvdXRQYXNzd29yZCwgdG9rZW5zIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignVG9rZW4gcmVmcmVzaCBlcnJvcjonLCBlcnJvcilcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBtZXNzYWdlOiAnVG9rZW4gcmVmcmVzaCBmYWlsZWQnIH1cbiAgICB9XG4gIH1cblxuICAvLyBHZXQgdXNlciBieSBJRFxuICBzdGF0aWMgYXN5bmMgZ2V0VXNlckJ5SWQodXNlcklkOiBudW1iZXIpOiBQcm9taXNlPFVzZXIgfCBudWxsPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXIgPSBtb2NrVXNlcnMuZmluZCh1ID0+IHUuaWQgPT09IHVzZXJJZClcbiAgICAgIGlmICghdXNlcikgcmV0dXJuIG51bGxcblxuICAgICAgY29uc3QgeyBwYXNzd29yZCwgLi4udXNlcldpdGhvdXRQYXNzd29yZCB9ID0gdXNlclxuICAgICAgcmV0dXJuIHVzZXJXaXRob3V0UGFzc3dvcmRcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignR2V0IHVzZXIgZXJyb3I6JywgZXJyb3IpXG4gICAgICByZXR1cm4gbnVsbFxuICAgIH1cbiAgfVxuXG4gIC8vIEdldCBhbGwgdXNlcnMgKGFkbWluIG9ubHkpXG4gIHN0YXRpYyBnZXRBbGxVc2VycygpOiBVc2VyW10ge1xuICAgIHJldHVybiBtb2NrVXNlcnMubWFwKHVzZXIgPT4ge1xuICAgICAgY29uc3QgeyBwYXNzd29yZCwgLi4udXNlcldpdGhvdXRQYXNzd29yZCB9ID0gdXNlclxuICAgICAgcmV0dXJuIHVzZXJXaXRob3V0UGFzc3dvcmRcbiAgICB9KVxuICB9XG59XG4iXSwibmFtZXMiOlsiYmNyeXB0Iiwiand0IiwiSldUX1NFQ1JFVCIsInByb2Nlc3MiLCJlbnYiLCJKV1RfUkVGUkVTSF9TRUNSRVQiLCJKV1RfRVhQSVJFU19JTiIsIkpXVF9SRUZSRVNIX0VYUElSRVNfSU4iLCJCQ1JZUFRfUk9VTkRTIiwibW9ja1VzZXJzIiwiaWQiLCJ1c2VybmFtZSIsImVtYWlsIiwiZnVsbF9uYW1lIiwicm9sZSIsInBsYW4iLCJsZXZlbCIsInNjb3JlIiwic3RyZWFrX2RheXMiLCJlbWFpbF92ZXJpZmllZCIsInN0YXR1cyIsImxhc3RfYWN0aXZlIiwiRGF0ZSIsImNyZWF0ZWRfYXQiLCJwYXNzd29yZCIsIlNpbXBsZUF1dGhTZXJ2aWNlIiwiaGFzaFBhc3N3b3JkIiwiaGFzaCIsInZlcmlmeVBhc3N3b3JkIiwiY29tcGFyZSIsImdlbmVyYXRlVG9rZW5zIiwidXNlciIsInBheWxvYWQiLCJhY2Nlc3NUb2tlbiIsInNpZ24iLCJleHBpcmVzSW4iLCJyZWZyZXNoVG9rZW4iLCJ2ZXJpZnlUb2tlbiIsInRva2VuIiwiaXNSZWZyZXNoVG9rZW4iLCJzZWNyZXQiLCJjb25zb2xlIiwibG9nIiwibGVuZ3RoIiwicmVzdWx0IiwidmVyaWZ5IiwiZXJyb3IiLCJFcnJvciIsIm1lc3NhZ2UiLCJTdHJpbmciLCJyZWdpc3RlciIsImRhdGEiLCJleGlzdGluZ1VzZXIiLCJmaW5kIiwidSIsInN1Y2Nlc3MiLCJuZXdVc2VyIiwiZnVsbE5hbWUiLCJwdXNoIiwidXNlcldpdGhvdXRQYXNzd29yZCIsInRva2VucyIsImxvZ2luIiwidXNlclBhc3N3b3JkIiwiYXV0aGVudGljYXRlUmVxdWVzdCIsInJlcXVlc3QiLCJoZWFkZXJzIiwiZ2V0IiwicmVwbGFjZSIsImNvb2tpZXMiLCJ0b2tlbk1hdGNoIiwibWF0Y2giLCJ1bmRlZmluZWQiLCJkZWNvZGVkIiwibG9nb3V0IiwidXNlcklkIiwiX3Nlc3Npb25Ub2tlbiIsImdldFVzZXJCeUlkIiwiZ2V0QWxsVXNlcnMiLCJtYXAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth-simple.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();