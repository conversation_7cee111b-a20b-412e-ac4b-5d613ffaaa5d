{"version": 3, "file": "runGlobal.js", "sourceRoot": "", "sources": ["../../../src/lib/runGlobal.ts"], "names": [], "mappings": ";;;;;AAAA,uDAA8B;AAC9B,4CAA6E;AAG7E,oDAA2B;AAC3B,kFAAyD;AACzD,6CAAyC;AACzC,kEAAyC;AACzC,4FAAmE;AAEnE,+CAA+C;AAC/C,KAAK,UAAU,SAAS,CAAC,OAAgB;IACvC,IAAA,eAAK,EAAC,OAAO,EAAE,YAAY,EAAE,SAAS,CAAC,CAAA;IACvC,IAAA,qBAAW,EAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,CAAA;IAExC,IAAA,eAAK,EAAC,OAAO,EAAE,8BAA8B,EAAE,SAAS,CAAC,CAAA;IACzD,IAAI,cAAc,GAAkB,EAAE,CAAA;IACtC,IAAI;QACF,cAAc,GAAG,MAAM,IAAA,8BAAoB,EACzC,IAAA,cAAI,EAAC,OAAO,EAAE;YACZ,KAAK;YACL,KAAK;YACL,QAAQ;YACR,eAAe;YACf,QAAQ;YACR,gBAAgB;YAChB,QAAQ;YACR,QAAQ;YACR,eAAe;SAChB,CAAC,CACH,CAAA;KACF;IAAC,OAAO,CAAM,EAAE;QACf,IAAA,sBAAY,EAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAA;KACjC;IAED,IAAA,eAAK,EAAC,OAAO,EAAE,iBAAiB,EAAE,SAAS,CAAC,CAAA;IAC5C,IAAA,eAAK,EAAC,OAAO,EAAE,cAAc,EAAE,SAAS,CAAC,CAAA;IACzC,IAAA,eAAK,EAAC,OAAO,EAAE,EAAE,EAAE,SAAS,CAAC,CAAA;IAC7B,IAAA,eAAK,EAAC,OAAO,EAAE,YAAY,OAAO,CAAC,MAAM,WAAW,EAAE,SAAS,CAAC,CAAA;IAEhE,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,GAAG,MAAM,IAAA,mCAAyB,EAAC,cAAc,EAAE,OAAO,CAAC,CAAA;IACnF,IAAA,eAAK,EAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAA;IAEjC,MAAM,IAAI,GAAG,IAAA,uBAAU,EAAC,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;IAE/F,MAAM,oBAAoB,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAClD,MAAM,IAAA,uBAAa,EAAC,OAAO,EAAE;QAC3B,OAAO,EAAE,cAAc;QACvB,QAAQ;QACR,MAAM;QACN,KAAK,EAAE,oBAAoB,CAAC,MAAM;QAClC,IAAI;KACL,CAAC,CAAA;IAEF,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAA;IAEjH,IAAI,OAAO,CAAC,IAAI,EAAE;QAChB,sHAAsH;QACtH,IAAA,mBAAS,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;KAC7B;SAAM,IAAI,WAAW,CAAC,MAAM,EAAE;QAC7B,MAAM,UAAU,GACd,OAAO,CAAC,cAAc,KAAK,MAAM;YAC/B,CAAC,CAAC,qBAAqB;YACvB,CAAC,CAAC,OAAO,CAAC,cAAc,KAAK,MAAM;gBACnC,CAAC,CAAC,aAAa;gBACf,CAAC,CAAC,OAAO,CAAC,cAAc,KAAK,KAAK;oBAClC,CAAC,CAAC,YAAY;oBACd,CAAC,CAAC,gBAAgB,CAAA;QAEtB,IAAA,eAAK,EACH,OAAO,EACP,IAAI;YACF,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC;YACjB,gGAAgG;YAChG,eAAK,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG,GAAG,WAAW,CAAC;YAC1C,IAAI,CACP,CAAA;KACF;IAED,oDAAoD;IACpD,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,UAAU,KAAK,CAAC,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC9E,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KAChB;IACD,OAAO,QAAQ,CAAA;AACjB,CAAC;AAED,kBAAe,SAAS,CAAA"}