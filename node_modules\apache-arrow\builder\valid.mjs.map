{"version": 3, "sources": ["builder/valid.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;AAGrB,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAElD;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,qBAAqB,CAAwC,UAAiC;IAE1G,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;QACxC,aAAa;QACb,OAAO,SAAS,OAAO,CAAC,KAAU,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IAEjD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACpB,MAAM,GAAG;kBACC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;eACrB,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;MAEpC,CAAC;IACH,CAAC;IAED,uEAAuE;IACvE,2EAA2E;IAC3E,IAAI,UAAU,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;QACtC,MAAM,GAAG,+BAA+B,MAAM,EAAE,CAAC;IACrD,CAAC;IAED,OAAO,IAAI,QAAQ,CAAC,GAAG,EAAE,GAAG,MAAM,gBAAgB,CAA4C,CAAC;AACnG,CAAC;AAED,cAAc;AACd,SAAS,WAAW,CAAC,CAAM;IACvB,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;QACxB,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IACD,OAAO,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC;AAClC,CAAC", "file": "valid.mjs", "sourceRoot": "../src"}