{"version": 3, "sources": ["../../../src/export/helpers/create-incremental-cache.ts"], "names": ["createIncrementalCache", "cache<PERSON><PERSON><PERSON>", "cacheMaxMemorySize", "fetchCacheKeyPrefix", "distDir", "dir", "enabledDirectories", "experimental", "flushToDisk", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interopDefault", "formatDynamicImportPath", "then", "mod", "default", "incrementalCache", "IncrementalCache", "dev", "requestHeaders", "fetchCache", "maxMemoryCacheSize", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "preview", "previewModeEncryptionKey", "previewModeId", "previewModeSigningKey", "notFoundRoutes", "fs", "nodeFs", "pagesDir", "pages", "appDir", "app", "serverDistDir", "path", "join", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minimalMode", "hasNextSupport", "globalThis", "__incrementalCache"], "mappings": ";;;;+BASsBA;;;eAAAA;;;6DAPL;kCACgB;wBACF;+BACR;gCACQ;yCACS;;;;;;AAEjC,eAAeA,uBAAuB,EAC3CC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,OAAO,EACPC,GAAG,EACHC,kBAAkB,EAClBC,YAAY,EACZC,WAAW,EAUZ;IACC,kCAAkC;IAClC,IAAIC;IACJ,IAAIR,cAAc;QAChBQ,eAAeC,IAAAA,8BAAc,EAC3B,MAAM,MAAM,CAACC,IAAAA,gDAAuB,EAACN,KAAKJ,eAAeW,IAAI,CAC3D,CAACC,MAAQA,IAAIC,OAAO,IAAID;IAG9B;IAEA,MAAME,mBAAmB,IAAIC,kCAAgB,CAAC;QAC5CC,KAAK;QACLC,gBAAgB,CAAC;QACjBV;QACAW,YAAY;QACZC,oBAAoBlB;QACpBC;QACAkB,sBAAsB,IAAO,CAAA;gBAC3BC,SAAS;gBACTC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,SAAS;oBACPC,0BAA0B;oBAC1BC,eAAe;oBACfC,uBAAuB;gBACzB;gBACAC,gBAAgB,EAAE;YACpB,CAAA;QACAC,IAAIC,qBAAM;QACVC,UAAU1B,mBAAmB2B,KAAK;QAClCC,QAAQ5B,mBAAmB6B,GAAG;QAC9BC,eAAeC,aAAI,CAACC,IAAI,CAAClC,SAAS;QAClCmC,iBAAiB9B;QACjB+B,aAAaC,sBAAc;QAC3BlC;IACF;IAEEmC,WAAmBC,kBAAkB,GAAG5B;IAE1C,OAAOA;AACT"}