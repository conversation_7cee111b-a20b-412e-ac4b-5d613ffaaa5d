{"version": 3, "file": "index-_DliZFxU.js", "sources": ["../node_modules/strip-json-comments/index.js"], "sourcesContent": ["const singleComment = Symbol('singleComment');\nconst multiComment = Symbol('multiComment');\n\nconst stripWithoutWhitespace = () => '';\nconst stripWithWhitespace = (string, start, end) => string.slice(start, end).replace(/\\S/g, ' ');\n\nconst isEscaped = (jsonString, quotePosition) => {\n\tlet index = quotePosition - 1;\n\tlet backslashCount = 0;\n\n\twhile (jsonString[index] === '\\\\') {\n\t\tindex -= 1;\n\t\tbackslashCount += 1;\n\t}\n\n\treturn Boolean(backslashCount % 2);\n};\n\nexport default function stripJsonComments(jsonString, {whitespace = true, trailingCommas = false} = {}) {\n\tif (typeof jsonString !== 'string') {\n\t\tthrow new TypeError(`Expected argument \\`jsonString\\` to be a \\`string\\`, got \\`${typeof jsonString}\\``);\n\t}\n\n\tconst strip = whitespace ? stripWithWhitespace : stripWithoutWhitespace;\n\n\tlet isInsideString = false;\n\tlet isInsideComment = false;\n\tlet offset = 0;\n\tlet buffer = '';\n\tlet result = '';\n\tlet commaIndex = -1;\n\n\tfor (let index = 0; index < jsonString.length; index++) {\n\t\tconst currentCharacter = jsonString[index];\n\t\tconst nextCharacter = jsonString[index + 1];\n\n\t\tif (!isInsideComment && currentCharacter === '\"') {\n\t\t\t// Enter or exit string\n\t\t\tconst escaped = isEscaped(jsonString, index);\n\t\t\tif (!escaped) {\n\t\t\t\tisInsideString = !isInsideString;\n\t\t\t}\n\t\t}\n\n\t\tif (isInsideString) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (!isInsideComment && currentCharacter + nextCharacter === '//') {\n\t\t\t// Enter single-line comment\n\t\t\tbuffer += jsonString.slice(offset, index);\n\t\t\toffset = index;\n\t\t\tisInsideComment = singleComment;\n\t\t\tindex++;\n\t\t} else if (isInsideComment === singleComment && currentCharacter + nextCharacter === '\\r\\n') {\n\t\t\t// Exit single-line comment via \\r\\n\n\t\t\tindex++;\n\t\t\tisInsideComment = false;\n\t\t\tbuffer += strip(jsonString, offset, index);\n\t\t\toffset = index;\n\t\t\tcontinue;\n\t\t} else if (isInsideComment === singleComment && currentCharacter === '\\n') {\n\t\t\t// Exit single-line comment via \\n\n\t\t\tisInsideComment = false;\n\t\t\tbuffer += strip(jsonString, offset, index);\n\t\t\toffset = index;\n\t\t} else if (!isInsideComment && currentCharacter + nextCharacter === '/*') {\n\t\t\t// Enter multiline comment\n\t\t\tbuffer += jsonString.slice(offset, index);\n\t\t\toffset = index;\n\t\t\tisInsideComment = multiComment;\n\t\t\tindex++;\n\t\t\tcontinue;\n\t\t} else if (isInsideComment === multiComment && currentCharacter + nextCharacter === '*/') {\n\t\t\t// Exit multiline comment\n\t\t\tindex++;\n\t\t\tisInsideComment = false;\n\t\t\tbuffer += strip(jsonString, offset, index + 1);\n\t\t\toffset = index + 1;\n\t\t\tcontinue;\n\t\t} else if (trailingCommas && !isInsideComment) {\n\t\t\tif (commaIndex !== -1) {\n\t\t\t\tif (currentCharacter === '}' || currentCharacter === ']') {\n\t\t\t\t\t// Strip trailing comma\n\t\t\t\t\tbuffer += jsonString.slice(offset, index);\n\t\t\t\t\tresult += strip(buffer, 0, 1) + buffer.slice(1);\n\t\t\t\t\tbuffer = '';\n\t\t\t\t\toffset = index;\n\t\t\t\t\tcommaIndex = -1;\n\t\t\t\t} else if (currentCharacter !== ' ' && currentCharacter !== '\\t' && currentCharacter !== '\\r' && currentCharacter !== '\\n') {\n\t\t\t\t\t// Hit non-whitespace following a comma; comma is not trailing\n\t\t\t\t\tbuffer += jsonString.slice(offset, index);\n\t\t\t\t\toffset = index;\n\t\t\t\t\tcommaIndex = -1;\n\t\t\t\t}\n\t\t\t} else if (currentCharacter === ',') {\n\t\t\t\t// Flush buffer prior to this point, and save new comma index\n\t\t\t\tresult += buffer + jsonString.slice(offset, index);\n\t\t\t\tbuffer = '';\n\t\t\t\toffset = index;\n\t\t\t\tcommaIndex = index;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn result + buffer + (isInsideComment ? strip(jsonString.slice(offset)) : jsonString.slice(offset));\n}\n"], "names": ["singleComment", "multiComment", "stripWithoutWhitespace", "stripWithWhitespace", "string", "start", "end", "isEscaped", "jsonString", "quotePosition", "index", "backslashCount", "stripJsonComments", "whitespace", "trailingCommas", "strip", "isInsideString", "isInsideComment", "offset", "buffer", "result", "commaIndex", "currentCharacter", "nextCharacter"], "mappings": "gFAAA,MAAMA,EAAgB,OAAO,eAAe,EACtCC,EAAe,OAAO,cAAc,EAEpCC,EAAyB,IAAM,GAC/BC,EAAsB,CAACC,EAAQC,EAAOC,IAAQF,EAAO,MAAMC,EAAOC,CAAG,EAAE,QAAQ,MAAO,GAAG,EAEzFC,EAAY,CAACC,EAAYC,IAAkB,CAChD,IAAIC,EAAQD,EAAgB,EACxBE,EAAiB,EAErB,KAAOH,EAAWE,CAAK,IAAM,MAC5BA,GAAS,EACTC,GAAkB,EAGnB,MAAO,GAAQA,EAAiB,EACjC,EAEe,SAASC,EAAkBJ,EAAY,CAAC,WAAAK,EAAa,GAAM,eAAAC,EAAiB,EAAK,EAAI,GAAI,CACvG,GAAI,OAAON,GAAe,SACzB,MAAM,IAAI,UAAU,8DAA8D,OAAOA,CAAU,IAAI,EAGxG,MAAMO,EAAQF,EAAaV,EAAsBD,EAEjD,IAAIc,EAAiB,GACjBC,EAAkB,GAClBC,EAAS,EACTC,EAAS,GACTC,EAAS,GACTC,EAAa,GAEjB,QAASX,EAAQ,EAAGA,EAAQF,EAAW,OAAQE,IAAS,CACvD,MAAMY,EAAmBd,EAAWE,CAAK,EACnCa,EAAgBf,EAAWE,EAAQ,CAAC,EAU1C,GARI,CAACO,GAAmBK,IAAqB,MAE5Bf,EAAUC,EAAYE,CAAK,IAE1CM,EAAiB,CAACA,IAIhB,CAAAA,EAIJ,GAAI,CAACC,GAAmBK,EAAmBC,IAAkB,KAE5DJ,GAAUX,EAAW,MAAMU,EAAQR,CAAK,EACxCQ,EAASR,EACTO,EAAkBjB,EAClBU,YACUO,IAAoBjB,GAAiBsB,EAAmBC,IAAkB;AAAA,EAAQ,CAE5Fb,IACAO,EAAkB,GAClBE,GAAUJ,EAAMP,EAAYU,EAAQR,CAAK,EACzCQ,EAASR,EACT,QACA,SAAUO,IAAoBjB,GAAiBsB,IAAqB;AAAA,EAEpEL,EAAkB,GAClBE,GAAUJ,EAAMP,EAAYU,EAAQR,CAAK,EACzCQ,EAASR,UACC,CAACO,GAAmBK,EAAmBC,IAAkB,KAAM,CAEzEJ,GAAUX,EAAW,MAAMU,EAAQR,CAAK,EACxCQ,EAASR,EACTO,EAAkBhB,EAClBS,IACA,QACA,SAAUO,IAAoBhB,GAAgBqB,EAAmBC,IAAkB,KAAM,CAEzFb,IACAO,EAAkB,GAClBE,GAAUJ,EAAMP,EAAYU,EAAQR,EAAQ,CAAC,EAC7CQ,EAASR,EAAQ,EACjB,QACH,MAAaI,GAAkB,CAACG,IACzBI,IAAe,GACdC,IAAqB,KAAOA,IAAqB,KAEpDH,GAAUX,EAAW,MAAMU,EAAQR,CAAK,EACxCU,GAAUL,EAAMI,EAAQ,EAAG,CAAC,EAAIA,EAAO,MAAM,CAAC,EAC9CA,EAAS,GACTD,EAASR,EACTW,EAAa,IACHC,IAAqB,KAAOA,IAAqB,KAAQA,IAAqB,MAAQA,IAAqB;AAAA,IAErHH,GAAUX,EAAW,MAAMU,EAAQR,CAAK,EACxCQ,EAASR,EACTW,EAAa,IAEJC,IAAqB,MAE/BF,GAAUD,EAASX,EAAW,MAAMU,EAAQR,CAAK,EACjDS,EAAS,GACTD,EAASR,EACTW,EAAaX,GAGf,CAED,OAAOU,EAASD,GAAUF,EAAkBF,EAAMP,EAAW,MAAMU,CAAM,CAAC,EAAIV,EAAW,MAAMU,CAAM,EACtG", "x_google_ignoreList": [0]}