{"version": 3, "sources": ["visitor/vectorloader.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAE,IAAI,EAAY,MAAM,YAAY,CAAC;AAC5C,OAAO,KAAK,IAAI,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,KAAK,EAAE,MAAM,cAAc,CAAC;AACrC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAIxC,OAAO,EAAuB,eAAe,EAAE,MAAM,YAAY,CAAC;AAElE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;AAErE,cAAc;AACd,MAAM,WAAW,YAAa,SAAQ,OAAO;IACzC,KAAK,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACvD,SAAS,CAAC,CAAC,SAAS,QAAQ,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;CACrE;AAED,cAAc;AACd,qBAAa,YAAa,SAAQ,OAAO;IACrC,OAAO,CAAC,KAAK,CAAa;IAC1B,OAAO,CAAC,KAAK,CAAc;IAC3B,OAAO,CAAC,UAAU,CAAM;IACxB,OAAO,CAAC,OAAO,CAAiB;IAChC,OAAO,CAAC,YAAY,CAAM;IAC1B,OAAO,CAAC,YAAY,CAA2B;IAC/C,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAkB;gBACtC,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,YAAY,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,eAAe,kBAAqB;IAajJ,SAAS,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,YAAuB;IAGzE,SAAS,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAGpF,QAAQ,CAAC,CAAC,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAGlF,UAAU,CAAC,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAGtF,SAAS,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAGpF,cAAc,CAAC,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAG9F,WAAW,CAAC,CAAC,SAAS,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAGxF,gBAAgB,CAAC,CAAC,SAAS,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAGlG,oBAAoB,CAAC,CAAC,SAAS,IAAI,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAG1G,SAAS,CAAC,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAGrF,cAAc,CAAC,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAG9F,SAAS,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAGpF,YAAY,CAAC,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAG1F,SAAS,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAGpF,WAAW,CAAC,CAAC,SAAS,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAGxF,UAAU,CAAC,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAQtF,eAAe,CAAC,CAAC,SAAS,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAGhG,gBAAgB,CAAC,CAAC,SAAS,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAGlG,eAAe,CAAC,CAAC,SAAS,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAGhG,aAAa,CAAC,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAG5F,aAAa,CAAC,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAG5F,kBAAkB,CAAC,CAAC,SAAS,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAGtG,QAAQ,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAuB;IAI1F,SAAS,CAAC,aAAa;IACvB,SAAS,CAAC,eAAe;IACzB,SAAS,CAAC,cAAc,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,eAAyB;IAGxG,SAAS,CAAC,WAAW,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,YAAY;IACxE,SAAS,CAAC,WAAW,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,YAAY;IACxE,SAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,eAAyB;IAG5F,SAAS,CAAC,cAAc,CAAC,CAAC,SAAS,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;CAGxF;AAED,cAAc;AACd,qBAAa,gBAAiB,SAAQ,YAAY;IAC9C,OAAO,CAAC,OAAO,CAAU;gBACb,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,YAAY,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,eAAe,EAAE,eAAe;IAInJ,SAAS,CAAC,cAAc,CAAC,CAAC,SAAS,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,eAAyB;IAG7G,SAAS,CAAC,WAAW,CAAC,CAAC,SAAS,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,eAAyB;IAGvF,SAAS,CAAC,WAAW,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,eAAyB;IAGtF,SAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,eAAyB;CAmBtF", "file": "vectorloader.d.ts", "sourceRoot": "../src"}