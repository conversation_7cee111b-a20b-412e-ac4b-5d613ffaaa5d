import axios from 'axios'
import crypto from 'crypto'
import { db } from '../database'

export interface PaymentRequest {
  userId: number
  planId: string
  amount: number
  currency: string
  duration: 'daily' | 'weekly' | 'monthly' | 'yearly'
  gateway: 'tripay' | 'midtrans' | 'xendit' | 'manual'
  customerInfo: {
    name: string
    email: string
    phone: string
  }
}

export interface PaymentResponse {
  success: boolean
  paymentId: string
  paymentUrl?: string
  qrCode?: string
  virtualAccount?: string
  instructions?: string
  expiresAt: Date
}

export interface PlanFeatures {
  maxScansPerDay: number
  maxOsintPerDay: number
  maxFilesPerDay: number
  maxFileSize: number
  apiAccess: boolean
  prioritySupport: boolean
  teamFeatures: boolean
  customIntegrations: boolean
  whiteLabel: boolean
}

export interface Plan {
  id: string
  name: string
  type: 'free' | 'student' | 'hobby' | 'bughunter' | 'cybersecurity'
  price: number
  currency: string
  duration: 'daily' | 'weekly' | 'monthly' | 'yearly'
  features: PlanFeatures
  isActive: boolean
  isPopular: boolean
  description: string
}

export class PaymentService {
  private tripayConfig = {
    merchantCode: process.env.TRIPAY_MERCHANT_CODE || '',
    apiKey: process.env.TRIPAY_API_KEY || '',
    privateKey: process.env.TRIPAY_PRIVATE_KEY || '',
    baseUrl: 'https://tripay.co.id/api'
  }

  private midtransConfig = {
    serverKey: process.env.MIDTRANS_SERVER_KEY || '',
    clientKey: process.env.MIDTRANS_CLIENT_KEY || '',
    baseUrl: 'https://api.midtrans.com/v2'
  }

  private xenditConfig = {
    secretKey: process.env.XENDIT_SECRET_KEY || '',
    baseUrl: 'https://api.xendit.co'
  }

  async getAvailablePlans(): Promise<Plan[]> {
    try {
      const [rows] = await db.query(`
        SELECT * FROM plans 
        WHERE is_active = true 
        ORDER BY 
          CASE type 
            WHEN 'free' THEN 1 
            WHEN 'student' THEN 2 
            WHEN 'hobby' THEN 3 
            WHEN 'bughunter' THEN 4 
            WHEN 'cybersecurity' THEN 5 
          END
      `)

      return (rows as any[]).map(row => ({
        id: row.id,
        name: row.name,
        type: row.type,
        price: parseFloat(row.price),
        currency: row.currency,
        duration: row.duration,
        features: JSON.parse(row.features),
        isActive: row.is_active,
        isPopular: row.is_popular,
        description: row.description
      }))
    } catch (error) {
      console.error('Error getting plans:', error)
      return this.getDefaultPlans()
    }
  }

  async getPlan(planId: string): Promise<Plan | null> {
    try {
      const [rows] = await db.query(
        'SELECT * FROM plans WHERE id = ? AND is_active = true',
        [planId]
      )

      if (!rows || (rows as any[]).length === 0) {
        return null
      }

      const row = (rows as any[])[0]
      return {
        id: row.id,
        name: row.name,
        type: row.type,
        price: parseFloat(row.price),
        currency: row.currency,
        duration: row.duration,
        features: JSON.parse(row.features),
        isActive: row.is_active,
        isPopular: row.is_popular,
        description: row.description
      }
    } catch (error) {
      console.error('Error getting plan:', error)
      return null
    }
  }

  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      // Get plan details
      const plan = await this.getPlan(request.planId)
      if (!plan) {
        throw new Error('Plan not found')
      }

      // Create payment record
      const paymentId = `pay_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      await db.query(`
        INSERT INTO payments (id, user_id, plan_id, amount, currency, gateway, status, created_at)
        VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())
      `, [paymentId, request.userId, request.planId, request.amount, request.currency, request.gateway])

      // Process payment based on gateway
      let paymentResponse: PaymentResponse

      switch (request.gateway) {
        case 'tripay':
          paymentResponse = await this.createTripayPayment(paymentId, request, plan)
          break
        case 'midtrans':
          paymentResponse = await this.createMidtransPayment(paymentId, request, plan)
          break
        case 'xendit':
          paymentResponse = await this.createXenditPayment(paymentId, request, plan)
          break
        case 'manual':
          paymentResponse = await this.createManualPayment(paymentId, request, plan)
          break
        default:
          throw new Error('Unsupported payment gateway')
      }

      // Update payment record with gateway response
      await db.query(`
        UPDATE payments 
        SET gateway_payment_id = ?, payment_url = ?, expires_at = ?
        WHERE id = ?
      `, [paymentResponse.paymentId, paymentResponse.paymentUrl, paymentResponse.expiresAt, paymentId])

      return paymentResponse

    } catch (error) {
      console.error('Payment creation error:', error)
      throw error
    }
  }

  private async createTripayPayment(paymentId: string, request: PaymentRequest, plan: Plan): Promise<PaymentResponse> {
    try {
      const merchantRef = paymentId
      const amount = request.amount
      
      // Create signature
      const signature = crypto
        .createHmac('sha256', this.tripayConfig.privateKey)
        .update(this.tripayConfig.merchantCode + merchantRef + amount)
        .digest('hex')

      const payload = {
        method: 'BRIVA', // BRI Virtual Account
        merchant_ref: merchantRef,
        amount: amount,
        customer_name: request.customerInfo.name,
        customer_email: request.customerInfo.email,
        customer_phone: request.customerInfo.phone,
        order_items: [
          {
            sku: plan.id,
            name: `${plan.name} - ${plan.duration}`,
            price: amount,
            quantity: 1
          }
        ],
        return_url: `${process.env.APP_URL}/payment/success`,
        expired_time: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
        signature: signature
      }

      const response = await axios.post(
        `${this.tripayConfig.baseUrl}/transaction/create`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.tripayConfig.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      )

      if (response.data.success) {
        return {
          success: true,
          paymentId: response.data.data.reference,
          paymentUrl: response.data.data.checkout_url,
          virtualAccount: response.data.data.pay_code,
          instructions: response.data.data.instructions,
          expiresAt: new Date(response.data.data.expired_time * 1000)
        }
      } else {
        throw new Error(response.data.message || 'Tripay payment creation failed')
      }
    } catch (error) {
      console.error('Tripay payment error:', error)
      throw new Error('Failed to create Tripay payment')
    }
  }

  private async createMidtransPayment(paymentId: string, request: PaymentRequest, plan: Plan): Promise<PaymentResponse> {
    try {
      const payload = {
        transaction_details: {
          order_id: paymentId,
          gross_amount: request.amount
        },
        customer_details: {
          first_name: request.customerInfo.name,
          email: request.customerInfo.email,
          phone: request.customerInfo.phone
        },
        item_details: [
          {
            id: plan.id,
            price: request.amount,
            quantity: 1,
            name: `${plan.name} - ${plan.duration}`
          }
        ],
        credit_card: {
          secure: true
        }
      }

      const response = await axios.post(
        `${this.midtransConfig.baseUrl}/charge`,
        payload,
        {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': `Basic ${Buffer.from(this.midtransConfig.serverKey + ':').toString('base64')}`
          }
        }
      )

      return {
        success: true,
        paymentId: response.data.transaction_id,
        paymentUrl: response.data.redirect_url,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      }
    } catch (error) {
      console.error('Midtrans payment error:', error)
      throw new Error('Failed to create Midtrans payment')
    }
  }

  private async createXenditPayment(paymentId: string, request: PaymentRequest, plan: Plan): Promise<PaymentResponse> {
    try {
      const payload = {
        external_id: paymentId,
        amount: request.amount,
        payer_email: request.customerInfo.email,
        description: `${plan.name} - ${plan.duration}`,
        success_redirect_url: `${process.env.APP_URL}/payment/success`,
        failure_redirect_url: `${process.env.APP_URL}/payment/failed`
      }

      const response = await axios.post(
        `${this.xenditConfig.baseUrl}/v2/invoices`,
        payload,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(this.xenditConfig.secretKey + ':').toString('base64')}`,
            'Content-Type': 'application/json'
          }
        }
      )

      return {
        success: true,
        paymentId: response.data.id,
        paymentUrl: response.data.invoice_url,
        expiresAt: new Date(response.data.expiry_date)
      }
    } catch (error) {
      console.error('Xendit payment error:', error)
      throw new Error('Failed to create Xendit payment')
    }
  }

  private async createManualPayment(paymentId: string, request: PaymentRequest, plan: Plan): Promise<PaymentResponse> {
    // Manual payment instructions
    const bankAccounts = [
      {
        bank: 'BCA',
        accountNumber: '**********',
        accountName: 'KodeXGuard Indonesia'
      },
      {
        bank: 'Mandiri',
        accountNumber: '**********',
        accountName: 'KodeXGuard Indonesia'
      }
    ]

    let instructions = `Manual Payment Instructions:\n\n`
    instructions += `Amount: ${request.currency} ${request.amount.toLocaleString()}\n`
    instructions += `Plan: ${plan.name} - ${plan.duration}\n\n`
    instructions += `Bank Transfer Options:\n`
    
    bankAccounts.forEach(account => {
      instructions += `${account.bank}: ${account.accountNumber} (${account.accountName})\n`
    })
    
    instructions += `\nAfter payment, please send proof to WhatsApp: +62-XXX-XXXX-XXXX`

    return {
      success: true,
      paymentId: paymentId,
      instructions: instructions,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    }
  }

  private getDefaultPlans(): Plan[] {
    return [
      {
        id: 'free',
        name: 'Free',
        type: 'free',
        price: 0,
        currency: 'IDR',
        duration: 'monthly',
        features: {
          maxScansPerDay: 5,
          maxOsintPerDay: 10,
          maxFilesPerDay: 3,
          maxFileSize: 10 * 1024 * 1024, // 10MB
          apiAccess: false,
          prioritySupport: false,
          teamFeatures: false,
          customIntegrations: false,
          whiteLabel: false
        },
        isActive: true,
        isPopular: false,
        description: 'Perfect for getting started with cybersecurity'
      },
      {
        id: 'student',
        name: 'Student',
        type: 'student',
        price: 50000,
        currency: 'IDR',
        duration: 'monthly',
        features: {
          maxScansPerDay: 50,
          maxOsintPerDay: 100,
          maxFilesPerDay: 25,
          maxFileSize: 100 * 1024 * 1024, // 100MB
          apiAccess: true,
          prioritySupport: true,
          teamFeatures: false,
          customIntegrations: false,
          whiteLabel: false
        },
        isActive: true,
        isPopular: true,
        description: 'Special pricing for students and researchers'
      },
      {
        id: 'cybersecurity',
        name: 'Cybersecurity Pro',
        type: 'cybersecurity',
        price: 500000,
        currency: 'IDR',
        duration: 'monthly',
        features: {
          maxScansPerDay: -1, // Unlimited
          maxOsintPerDay: -1, // Unlimited
          maxFilesPerDay: -1, // Unlimited
          maxFileSize: 1024 * 1024 * 1024, // 1GB
          apiAccess: true,
          prioritySupport: true,
          teamFeatures: true,
          customIntegrations: true,
          whiteLabel: true
        },
        isActive: true,
        isPopular: false,
        description: 'Enterprise-grade features for cybersecurity professionals'
      }
    ]
  }
}
