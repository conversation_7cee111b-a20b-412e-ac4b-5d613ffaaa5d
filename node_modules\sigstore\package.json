{"name": "sigstore", "version": "1.9.0", "description": "code-signing for npm packages", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "shx rm -rf dist *.tsbuildinfo", "build": "tsc --build", "test": "jest"}, "bin": {"sigstore": "bin/sigstore.js"}, "files": ["dist", "store"], "author": "<EMAIL>", "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/sigstore/sigstore-js.git"}, "bugs": {"url": "https://github.com/sigstore/sigstore-js/issues"}, "homepage": "https://github.com/sigstore/sigstore-js/tree/main/packages/client#readme", "publishConfig": {"provenance": true}, "devDependencies": {"@sigstore/rekor-types": "^1.0.0", "@sigstore/jest": "^0.0.0", "@sigstore/mock": "^0.2.0", "@tufjs/repo-mock": "^1.1.0", "@types/make-fetch-happen": "^10.0.0"}, "dependencies": {"@sigstore/bundle": "^1.1.0", "@sigstore/protobuf-specs": "^0.2.0", "@sigstore/sign": "^1.0.0", "@sigstore/tuf": "^1.0.3", "make-fetch-happen": "^11.0.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}