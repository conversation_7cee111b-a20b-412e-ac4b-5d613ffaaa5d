{"version": 3, "file": "ALTER.js", "sourceRoot": "", "sources": ["../../../lib/commands/ALTER.ts"], "names": [], "mappings": ";;AAEA,qCAAyD;AAEzD,kBAAe;IACb,iBAAiB,EAAE,IAAI;IACvB,YAAY,EAAE,IAAI;IAClB;;;;;OAKG;IACH,YAAY,CAAC,MAAqB,EAAE,KAAoB,EAAE,MAAwB;QAChF,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAChD,IAAA,oBAAW,EAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC9B,CAAC;IACD,cAAc,EAAE,SAAqD;CAC3C,CAAC"}