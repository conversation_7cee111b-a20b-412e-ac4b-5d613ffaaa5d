{"name": "npm-check-updates", "version": "16.14.20", "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "contributors": ["<PERSON><PERSON> (https://github.com/raineorshine)", "Imamuzzaki Abu <PERSON> <<EMAIL>>"], "description": "Find newer versions of dependencies than what your package.json allows", "keywords": ["npm", "check", "find", "discover", "updates", "upgrades", "dependencies", "package.json", "updater", "version", "management", "ncu", "bun", "yarn", "pnpm"], "engines": {"node": ">=14.14"}, "main": "build/src/index.js", "scripts": {"build": "npm run build:ts && npm run build:options", "build:ts": "tsc", "build:options": "node build/src/scripts/build-options.js && tsc", "lint": "cross-env FORCE_COLOR=1 npm-run-all --parallel --aggregate-output lint:*", "lint:lockfile": "lockfile-lint", "lint:markdown": "markdownlint \"**/*.md\" --ignore node_modules --ignore build --config .markdownlint.js", "lint:src": "eslint --cache --cache-location node_modules/.cache/.eslintcache --ignore-path .gitignore --report-unused-disable-directives .", "prepare": "src/scripts/install-hooks", "prepublishOnly": "npm run build", "prettier": "prettier .", "test": "mocha test test/package-managers/*", "ncu": "node build/src/bin/cli.js"}, "bin": {"npm-check-updates": "build/src/bin/cli.js", "ncu": "build/src/bin/cli.js"}, "repository": {"type": "git", "url": "git+https://github.com/raineorshine/npm-check-updates.git"}, "homepage": "https://github.com/raineorshine/npm-check-updates", "bugs": {"url": "https://github.com/raineorshine/npm-check-updates/issues"}, "dependencies": {"@types/semver-utils": "^1.1.1", "chalk": "^5.3.0", "cli-table3": "^0.6.3", "commander": "^10.0.1", "fast-memoize": "^2.5.2", "find-up": "5.0.0", "fp-and-or": "^0.1.4", "get-stdin": "^8.0.0", "globby": "^11.0.4", "hosted-git-info": "^5.1.0", "ini": "^4.1.1", "js-yaml": "^4.1.0", "json-parse-helpfulerror": "^1.0.3", "jsonlines": "^0.1.1", "lodash": "^4.17.21", "make-fetch-happen": "^11.1.1", "minimatch": "^9.0.3", "p-map": "^4.0.0", "pacote": "15.2.0", "parse-github-url": "^1.0.2", "progress": "^2.0.3", "prompts-ncu": "^3.0.0", "rc-config-loader": "^4.1.3", "remote-git-tags": "^3.0.0", "rimraf": "^5.0.5", "semver": "^7.5.4", "semver-utils": "^1.1.4", "source-map-support": "^0.5.21", "spawn-please": "^2.0.2", "strip-ansi": "^7.1.0", "strip-json-comments": "^5.0.1", "untildify": "^4.0.0", "update-notifier": "^6.0.2"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^4.2.0", "@types/chai": "^4.3.8", "@types/chai-as-promised": "^7.1.6", "@types/chai-string": "^1.4.3", "@types/cli-table": "^0.3.2", "@types/hosted-git-info": "^3.0.3", "@types/ini": "^1.3.31", "@types/js-yaml": "^4.0.7", "@types/json-parse-helpfulerror": "^1.0.1", "@types/jsonlines": "^0.1.3", "@types/lodash": "^4.14.199", "@types/make-fetch-happen": "^10.0.2", "@types/minimatch": "^5.1.2", "@types/mocha": "^10.0.2", "@types/node": "^18.18.4", "@types/pacote": "^11.1.6", "@types/parse-github-url": "^1.0.1", "@types/progress": "^2.0.5", "@types/prompts": "^2.4.6", "@types/remote-git-tags": "^4.0.0", "@types/rimraf": "^3.0.2", "@types/semver": "^7.5.3", "@types/sinon": "^10.0.19", "@types/update-notifier": "^6.0.5", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "bun": "^1.1.4", "c8": "^7.14.0", "chai": "^4.3.10", "chai-as-promised": "^7.1.1", "chai-string": "^1.5.0", "cross-env": "^7.0.3", "eslint": "^8.45.0", "eslint-config-prettier": "^8.10.0", "eslint-config-raine": "^0.4.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-fp": "^2.3.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsdoc": "^41.1.2", "eslint-plugin-n": "^15.7.0", "eslint-plugin-promise": "^6.1.1", "lockfile-lint": "^4.10.6", "markdownlint-cli": "0.34.0", "mocha": "^10.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "should": "^13.2.3", "sinon": "^15.2.0", "ts-node": "^10.9.1", "typescript": "5.1.6", "typescript-json-schema": "0.59.0", "yarn": "^1.22.19"}, "files": ["build", "!**/test/**"], "lockfile-lint": {"allowed-schemes": ["https:"], "allowed-hosts": ["npm"], "empty-hostname": false, "type": "npm ", "path": "package-lock.json"}, "mocha": {"check-leaks": true, "extension": ["test.ts"], "require": ["source-map-support/register", "ts-node/register"], "timeout": 60000, "trace-deprecation": true, "trace-warnings": true, "use_strict": true}, "c8": {"reporter": ["html", "lcov", "text-summary"]}}