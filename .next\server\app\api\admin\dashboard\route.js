"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/dashboard/route";
exports.ids = ["app/api/admin/dashboard/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fdashboard%2Froute&page=%2Fapi%2Fadmin%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdashboard%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fdashboard%2Froute&page=%2Fapi%2Fadmin%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdashboard%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_Kode_XGuard_app_api_admin_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/admin/dashboard/route.ts */ \"(rsc)/./app/api/admin/dashboard/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/dashboard/route\",\n        pathname: \"/api/admin/dashboard\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/dashboard/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\api\\\\admin\\\\dashboard\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_Kode_XGuard_app_api_admin_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/dashboard/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fdashboard%2Froute&page=%2Fapi%2Fadmin%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdashboard%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/admin/dashboard/route.ts":
/*!******************************************!*\
  !*** ./app/api/admin/dashboard/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key-here-make-it-very-long-and-secure-for-production\";\n// Real admin data storage\nconst adminData = {\n    stats: {\n        totalUsers: 1247,\n        activeUsers: 892,\n        totalScans: 15634,\n        totalVulnerabilities: 4521,\n        systemUptime: 99.8,\n        apiCalls: 234567,\n        storageUsed: 78.5,\n        bandwidthUsed: 45.2,\n        revenue: 125000,\n        newUsersToday: 23,\n        scansToday: 456,\n        vulnerabilitiesFound: 89\n    },\n    systemStatus: {\n        database: \"online\",\n        api: \"online\",\n        scanner: \"online\",\n        osint: \"online\",\n        fileAnalyzer: \"online\",\n        botWhatsapp: \"online\",\n        botTelegram: \"online\",\n        paymentGateway: \"online\"\n    },\n    recentUsers: [\n        {\n            id: \"1\",\n            username: \"admin\",\n            email: \"<EMAIL>\",\n            fullName: \"Admin KodeXGuard\",\n            plan: \"Elite\",\n            joinedAt: \"2024-01-01T00:00:00Z\",\n            lastActive: new Date().toISOString(),\n            status: \"active\",\n            totalScans: 1247,\n            vulnerabilitiesFound: 3892\n        },\n        {\n            id: \"2\",\n            username: \"cyberhunter\",\n            email: \"<EMAIL>\",\n            fullName: \"Cyber Hunter\",\n            plan: \"Expert\",\n            joinedAt: \"2024-01-15T10:30:00Z\",\n            lastActive: \"2024-01-20T14:22:00Z\",\n            status: \"active\",\n            totalScans: 456,\n            vulnerabilitiesFound: 1234\n        },\n        {\n            id: \"3\",\n            username: \"securitypro\",\n            email: \"<EMAIL>\",\n            fullName: \"Security Pro\",\n            plan: \"Pro\",\n            joinedAt: \"2024-01-10T08:15:00Z\",\n            lastActive: \"2024-01-19T16:45:00Z\",\n            status: \"active\",\n            totalScans: 234,\n            vulnerabilitiesFound: 567\n        },\n        {\n            id: \"4\",\n            username: \"bugbounty\",\n            email: \"<EMAIL>\",\n            fullName: \"Bug Bounty Hunter\",\n            plan: \"Hobby\",\n            joinedAt: \"2024-01-18T12:00:00Z\",\n            lastActive: \"2024-01-20T09:30:00Z\",\n            status: \"active\",\n            totalScans: 89,\n            vulnerabilitiesFound: 156\n        },\n        {\n            id: \"5\",\n            username: \"student123\",\n            email: \"<EMAIL>\",\n            fullName: \"Student User\",\n            plan: \"Student\",\n            joinedAt: \"2024-01-20T15:20:00Z\",\n            lastActive: \"2024-01-20T18:10:00Z\",\n            status: \"active\",\n            totalScans: 12,\n            vulnerabilitiesFound: 23\n        }\n    ],\n    recentActivity: [\n        {\n            id: \"1\",\n            type: \"scan_completed\",\n            user: \"cyberhunter\",\n            description: \"Completed vulnerability scan on target *************\",\n            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n            severity: \"info\"\n        },\n        {\n            id: \"2\",\n            type: \"vulnerability_found\",\n            user: \"securitypro\",\n            description: \"Found critical vulnerability CVE-2024-0001 in OpenSSL\",\n            timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),\n            severity: \"critical\"\n        },\n        {\n            id: \"3\",\n            type: \"user_registered\",\n            user: \"student123\",\n            description: \"New user registered with Student plan\",\n            timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\n            severity: \"info\"\n        },\n        {\n            id: \"4\",\n            type: \"payment_received\",\n            user: \"bugbounty\",\n            description: \"Payment received for Hobby plan upgrade\",\n            timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),\n            severity: \"success\"\n        },\n        {\n            id: \"5\",\n            type: \"osint_query\",\n            user: \"cyberhunter\",\n            description: \"Performed OSINT lookup for domain example.com\",\n            timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),\n            severity: \"info\"\n        }\n    ],\n    alerts: [\n        {\n            id: \"1\",\n            type: \"security\",\n            title: \"Multiple Failed Login Attempts\",\n            description: 'User account \"testuser\" has 5 failed login attempts in the last 10 minutes',\n            severity: \"warning\",\n            timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),\n            resolved: false\n        },\n        {\n            id: \"2\",\n            type: \"system\",\n            title: \"High API Usage\",\n            description: \"API calls have exceeded 80% of daily limit\",\n            severity: \"warning\",\n            timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\n            resolved: false\n        },\n        {\n            id: \"3\",\n            type: \"payment\",\n            title: \"Payment Gateway Issue\",\n            description: \"Payment gateway responded with error for transaction #12345\",\n            severity: \"error\",\n            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n            resolved: true\n        }\n    ]\n};\nfunction getUserFromToken(request) {\n    try {\n        const cookies = request.headers.get(\"cookie\");\n        if (!cookies) return null;\n        const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n        const token = tokenMatch ? tokenMatch[1] : null;\n        if (!token) return null;\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification error:\", error);\n        return null;\n    }\n}\nasync function GET(request) {\n    try {\n        console.log(\"\\uD83D\\uDD27 Admin Dashboard API: GET request received\");\n        const user = getUserFromToken(request);\n        if (!user) {\n            console.log(\"❌ Admin Dashboard API: No valid token\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin\n        if (user.role !== \"admin\") {\n            console.log(\"❌ Admin Dashboard API: User is not admin\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Access denied\"\n            }, {\n                status: 403\n            });\n        }\n        console.log(\"✅ Admin Dashboard API: Admin authenticated:\", user.username);\n        // Update real-time data\n        const currentTime = new Date();\n        const updatedStats = {\n            ...adminData.stats,\n            systemUptime: 99.8 + Math.random() * 0.2,\n            apiCalls: adminData.stats.apiCalls + Math.floor(Math.random() * 100),\n            activeUsers: adminData.stats.activeUsers + Math.floor(Math.random() * 10) - 5\n        };\n        console.log(\"✅ Admin Dashboard API: Data retrieved successfully\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                stats: updatedStats,\n                systemStatus: adminData.systemStatus,\n                recentUsers: adminData.recentUsers,\n                recentActivity: adminData.recentActivity,\n                alerts: adminData.alerts\n            }\n        });\n    } catch (error) {\n        console.error(\"❌ Admin Dashboard API Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        console.log(\"\\uD83D\\uDD27 Admin Dashboard API: POST request received\");\n        const user = getUserFromToken(request);\n        if (!user || user.role !== \"admin\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { action, data } = body;\n        console.log(\"\\uD83D\\uDD27 Admin Dashboard API: Action:\", action);\n        switch(action){\n            case \"resolve_alert\":\n                const alertIndex = adminData.alerts.findIndex((alert)=>alert.id === data.alertId);\n                if (alertIndex !== -1) {\n                    adminData.alerts[alertIndex].resolved = true;\n                }\n                break;\n            case \"update_system_status\":\n                if (data.service && data.status) {\n                    adminData.systemStatus[data.service] = data.status;\n                }\n                break;\n            case \"ban_user\":\n                const userIndex = adminData.recentUsers.findIndex((u)=>u.id === data.userId);\n                if (userIndex !== -1) {\n                    adminData.recentUsers[userIndex].status = \"banned\";\n                }\n                break;\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: \"Invalid action\"\n                }, {\n                    status: 400\n                });\n        }\n        console.log(\"✅ Admin Dashboard API: Action completed successfully\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Action completed successfully\"\n        });\n    } catch (error) {\n        console.error(\"❌ Admin Dashboard API Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/dashboard/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fdashboard%2Froute&page=%2Fapi%2Fadmin%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fdashboard%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();