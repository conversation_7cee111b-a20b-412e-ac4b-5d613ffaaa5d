"use strict";
// automatically generated by the FlatBuffers compiler, do not modify
Object.defineProperty(exports, "__esModule", { value: true });
exports.FixedSizeList = void 0;
const flatbuffers = require("flatbuffers");
class FixedSizeList {
    constructor() {
        this.bb = null;
        this.bb_pos = 0;
    }
    __init(i, bb) {
        this.bb_pos = i;
        this.bb = bb;
        return this;
    }
    static getRootAsFixedSizeList(bb, obj) {
        return (obj || new FixedSizeList()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
    }
    static getSizePrefixedRootAsFixedSizeList(bb, obj) {
        bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
        return (obj || new FixedSizeList()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
    }
    /**
     * Number of list items per value
     */
    listSize() {
        const offset = this.bb.__offset(this.bb_pos, 4);
        return offset ? this.bb.readInt32(this.bb_pos + offset) : 0;
    }
    static startFixedSizeList(builder) {
        builder.startObject(1);
    }
    static addListSize(builder, listSize) {
        builder.addFieldInt32(0, listSize, 0);
    }
    static endFixedSizeList(builder) {
        const offset = builder.endObject();
        return offset;
    }
    static createFixedSizeList(builder, listSize) {
        FixedSizeList.startFixedSizeList(builder);
        FixedSizeList.addListSize(builder, listSize);
        return FixedSizeList.endFixedSizeList(builder);
    }
}
exports.FixedSizeList = FixedSizeList;

//# sourceMappingURL=fixed-size-list.js.map
