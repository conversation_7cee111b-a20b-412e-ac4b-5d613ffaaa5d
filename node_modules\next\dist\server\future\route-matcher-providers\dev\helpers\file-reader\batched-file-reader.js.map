{"version": 3, "sources": ["../../../../../../../src/server/future/route-matcher-providers/dev/helpers/file-reader/batched-file-reader.ts"], "names": ["BatchedFileReader", "constructor", "reader", "schedule", "callback", "schedulePromise", "Promise", "resolve", "then", "process", "nextTick", "getOrCreateBatch", "batch", "completed", "directories", "callbacks", "length", "values", "load", "err", "reject", "i", "value", "Error", "unique", "Set", "results", "all", "map", "directory", "files", "error", "read", "found", "find", "result", "dir", "push", "promise"], "mappings": ";;;;+BAeaA;;;eAAAA;;;AAAN,MAAMA;IAGXC,YAAY,AAAiBC,MAAkB,CAAE;aAApBA,SAAAA;IAAqB;IAK1CC,SAASC,QAAkB,EAAE;QACnC,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;YACzB,IAAI,CAACA,eAAe,GAAGC,QAAQC,OAAO;QACxC;QACA,IAAI,CAACF,eAAe,CAACG,IAAI,CAAC;YACxBC,QAAQC,QAAQ,CAACN;QACnB;IACF;IAEQO,mBAAoC;QAC1C,uEAAuE;QACvE,IAAI,IAAI,CAACC,KAAK,IAAI,CAAC,IAAI,CAACA,KAAK,CAACC,SAAS,EAAE;YACvC,OAAO,IAAI,CAACD,KAAK;QACnB;QAEA,MAAMA,QAAyB;YAC7BC,WAAW;YACXC,aAAa,EAAE;YACfC,WAAW,EAAE;QACf;QAEA,IAAI,CAACH,KAAK,GAAGA;QAEb,IAAI,CAACT,QAAQ,CAAC;YACZS,MAAMC,SAAS,GAAG;YAClB,IAAID,MAAME,WAAW,CAACE,MAAM,KAAK,GAAG;YAEpC,oEAAoE;YACpE,gDAAgD;YAChD,IAAIC;YACJ,IAAI;gBACFA,SAAS,MAAM,IAAI,CAACC,IAAI,CAACN,MAAME,WAAW;YAC5C,EAAE,OAAOK,KAAK;gBACZ,4BAA4B;gBAC5B,KAAK,MAAM,EAAEC,MAAM,EAAE,IAAIR,MAAMG,SAAS,CAAE;oBACxCK,OAAOD;gBACT;gBACA;YACF;YAEA,2DAA2D;YAC3D,IAAK,IAAIE,IAAI,GAAGA,IAAIT,MAAMG,SAAS,CAACC,MAAM,EAAEK,IAAK;gBAC/C,MAAMC,QAAQL,MAAM,CAACI,EAAE;gBACvB,IAAIC,iBAAiBC,OAAO;oBAC1BX,MAAMG,SAAS,CAACM,EAAE,CAACD,MAAM,CAACE;gBAC5B,OAAO;oBACLV,MAAMG,SAAS,CAACM,EAAE,CAACd,OAAO,CAACe;gBAC7B;YACF;QACF;QAEA,OAAOV;IACT;IAEA,MAAcM,KACZJ,WAAkC,EACqB;QACvD,wEAAwE;QACxE,gCAAgC;QAChC,MAAMU,SAAS;eAAI,IAAIC,IAAIX;SAAa;QAExC,MAAMY,UAAU,MAAMpB,QAAQqB,GAAG,CAC/BH,OAAOI,GAAG,CAAC,OAAOC;YAChB,IAAIC;YACJ,IAAIC;YACJ,IAAI;gBACFD,QAAQ,MAAM,IAAI,CAAC5B,MAAM,CAAC8B,IAAI,CAACH;YACjC,EAAE,OAAOV,KAAK;gBACZ,IAAIA,eAAeI,OAAOQ,QAAQZ;YACpC;YAEA,OAAO;gBAAEU;gBAAWC;gBAAOC;YAAM;QACnC;QAGF,OAAOjB,YAAYc,GAAG,CAAC,CAACC;YACtB,MAAMI,QAAQP,QAAQQ,IAAI,CAAC,CAACC,SAAWA,OAAON,SAAS,KAAKA;YAC5D,IAAI,CAACI,OAAO,OAAO,EAAE;YAErB,IAAIA,MAAMH,KAAK,EAAE,OAAOG,MAAMH,KAAK;YACnC,IAAIG,MAAMF,KAAK,EAAE,OAAOE,MAAMF,KAAK;YAEnC,OAAO,EAAE;QACX;IACF;IAEA,MAAaC,KAAKI,GAAW,EAAkC;QAC7D,0CAA0C;QAC1C,MAAMxB,QAAQ,IAAI,CAACD,gBAAgB;QAEnC,iDAAiD;QACjDC,MAAME,WAAW,CAACuB,IAAI,CAACD;QAEvB,2EAA2E;QAC3E,yCAAyC;QACzC,MAAME,UAAU,IAAIhC,QAA+B,CAACC,SAASa;YAC3DR,MAAMG,SAAS,CAACsB,IAAI,CAAC;gBAAE9B;gBAASa;YAAO;QACzC;QAEA,OAAOkB;IACT;AACF"}