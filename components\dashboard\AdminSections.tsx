'use client'

import { useState, useEffect } from 'react'
import { 
  <PERSON>, 
  BarChart3, 
  Monitor, 
  Settings, 
  CreditCard, 
  Bot, 
  MessageSquare, 
  Send, 
  Cog, 
  ShieldCheck, 
  Database, 
  Server, 
  Webhook,
  Activity,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  DollarSign,
  UserCheck,
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  RefreshCw,
  Play,
  Pause,
  Square,
  Crown,
  Shield,
  Lock,
  Unlock,
  Ban,
  UserPlus,
  UserMinus,
  Mail,
  Phone,
  Calendar,
  Clock,
  Globe,
  MapPin,
  Wifi,
  WifiOff,
  Power,
  PowerOff,
  Zap,
  Lightning,
  Bolt,
  Battery,
  Cpu,
  HardDrive,
  MemoryStick,
  Network,
  Router,
  Smartphone,
  Tablet,
  Laptop,
  Desktop,
  Headphones,
  Mic,
  Camera,
  Video,
  Image,
  File,
  Folder,
  Archive,
  Package,
  Box,
  Container,
  Layers,
  Layers3,
  CloudCog,
  Cloud,
  CloudUpload,
  CloudDownload,
  CloudSync,
  CloudOff,
  Gauge,
  Speedometer,
  Timer,
  Stopwatch,
  Alarm,
  Bell,
  BellOff,
  Volume,
  VolumeOff,
  VolumeUp,
  VolumeDown,
  Volume<PERSON><PERSON>,
  Speaker,
  <PERSON><PERSON><PERSON>
} from 'lucide-react'

interface User {
  id: string
  username: string
  email: string
  role: 'user' | 'admin' | 'super_admin' | 'moderator'
  plan: 'Free' | 'Student' | 'Hobby' | '<PERSON>ughunter' | 'Cybersecurity'
  level: number
  score: number
  full_name?: string
  created_at: string
  last_active: string
  email_verified: boolean
  status: 'active' | 'suspended' | 'banned'
}

interface AdminSectionsProps {
  section: string
  user: User | null
}

export default function AdminSections({ section, user }: AdminSectionsProps) {
  const [loading, setLoading] = useState(false)
  const [users, setUsers] = useState<User[]>([])
  const [stats, setStats] = useState<any>({})

  useEffect(() => {
    loadSectionData()
  }, [section])

  const loadSectionData = async () => {
    try {
      setLoading(true)
      console.log(`🔄 Loading ${section} data...`)
      
      // Load different data based on section
      switch (section) {
        case 'users':
          await loadUsers()
          break
        case 'analytics':
          await loadAnalytics()
          break
        case 'monitoring':
          await loadMonitoring()
          break
        case 'website-settings':
          await loadWebsiteSettings()
          break
        case 'payments':
          await loadPayments()
          break
        case 'bots':
          await loadBots()
          break
        case 'whatsapp':
          await loadWhatsApp()
          break
        case 'telegram':
          await loadTelegram()
          break
        case 'system-config':
          await loadSystemConfig()
          break
        case 'security':
          await loadSecurity()
          break
        case 'database':
          await loadDatabase()
          break
        case 'server':
          await loadServer()
          break
        case 'api':
          await loadAPI()
          break
        default:
          console.log('Unknown section:', section)
      }
    } catch (error) {
      console.error(`❌ Error loading ${section} data:`, error)
    } finally {
      setLoading(false)
    }
  }

  const loadUsers = async () => {
    // Mock data for now
    setUsers([
      {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        plan: 'Elite',
        level: 10,
        score: 5000,
        full_name: 'System Administrator',
        created_at: '2024-01-01T00:00:00Z',
        last_active: new Date().toISOString(),
        email_verified: true,
        status: 'active'
      },
      {
        id: '2',
        username: 'superadmin',
        email: '<EMAIL>',
        role: 'super_admin',
        plan: 'Elite',
        level: 15,
        score: 10000,
        full_name: 'Super Administrator',
        created_at: '2024-01-01T00:00:00Z',
        last_active: new Date().toISOString(),
        email_verified: true,
        status: 'active'
      },
      {
        id: '3',
        username: 'testuser',
        email: '<EMAIL>',
        role: 'user',
        plan: 'Free',
        level: 3,
        score: 150,
        full_name: 'Test User',
        created_at: '2024-01-15T00:00:00Z',
        last_active: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        email_verified: true,
        status: 'active'
      }
    ])
  }

  const loadAnalytics = async () => {
    setStats({
      totalUsers: 1247,
      activeToday: 156,
      totalScans: 15634,
      revenue: 125000,
      newUsersThisMonth: 89,
      premiumUsers: 234,
      vulnerabilitiesFound: 2456,
      apiCalls: 45678
    })
  }

  const loadMonitoring = async () => {
    setStats({
      systemHealth: 'healthy',
      uptime: 99.8,
      cpuUsage: 45.2,
      memoryUsage: 67.8,
      diskUsage: 34.5,
      activeConnections: 1234,
      requestsPerMinute: 567,
      errorRate: 0.02
    })
  }

  const loadWebsiteSettings = async () => {
    setStats({
      siteName: 'KodeXGuard',
      siteDescription: 'Advanced Cybersecurity Platform',
      maintenanceMode: false,
      registrationEnabled: true,
      emailVerificationRequired: true,
      maxUsersPerPlan: {
        Free: 1000,
        Student: 500,
        Hobby: 200,
        Bughunter: 100,
        Cybersecurity: 50
      }
    })
  }

  const loadPayments = async () => {
    setStats({
      totalRevenue: 125000,
      monthlyRevenue: 15000,
      activeSubscriptions: 234,
      pendingPayments: 12,
      refunds: 3,
      chargeback: 1
    })
  }

  const loadBots = async () => {
    setStats({
      whatsappBot: {
        status: 'online',
        connectedUsers: 45,
        messagesProcessed: 1234,
        uptime: 99.5
      },
      telegramBot: {
        status: 'online',
        connectedUsers: 78,
        messagesProcessed: 2345,
        uptime: 98.9
      }
    })
  }

  const loadWhatsApp = async () => {
    setStats({
      status: 'connected',
      qrCode: null,
      connectedUsers: 45,
      messagesProcessed: 1234,
      lastMessage: new Date().toISOString(),
      features: {
        osintLookup: true,
        vulnerabilityAlerts: true,
        fileAnalysis: false,
        customCommands: true
      }
    })
  }

  const loadTelegram = async () => {
    setStats({
      status: 'connected',
      botToken: 'CONFIGURED',
      connectedUsers: 78,
      messagesProcessed: 2345,
      lastMessage: new Date().toISOString(),
      features: {
        osintLookup: true,
        vulnerabilityAlerts: true,
        fileAnalysis: true,
        customCommands: true,
        inlineQueries: true
      }
    })
  }

  const loadSystemConfig = async () => {
    setStats({
      environment: 'production',
      version: '1.0.0',
      database: 'connected',
      redis: 'connected',
      elasticsearch: 'connected',
      apiRateLimit: 1000,
      maxFileSize: '10MB',
      allowedFileTypes: ['.exe', '.pdf', '.doc', '.zip'],
      securitySettings: {
        twoFactorRequired: false,
        passwordMinLength: 8,
        sessionTimeout: 24,
        maxLoginAttempts: 5
      }
    })
  }

  const loadSecurity = async () => {
    setStats({
      activeThreats: 3,
      blockedIPs: 156,
      suspiciousActivity: 12,
      failedLogins: 45,
      securityAlerts: [
        {
          id: 1,
          type: 'brute_force',
          severity: 'high',
          ip: '*************',
          timestamp: new Date().toISOString(),
          blocked: true
        },
        {
          id: 2,
          type: 'suspicious_file',
          severity: 'medium',
          user: 'testuser',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          blocked: false
        }
      ]
    })
  }

  const loadDatabase = async () => {
    setStats({
      status: 'connected',
      size: '2.5GB',
      tables: 25,
      connections: 12,
      queries: 15678,
      slowQueries: 3,
      backupStatus: 'completed',
      lastBackup: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
    })
  }

  const loadServer = async () => {
    setStats({
      status: 'running',
      uptime: '15 days',
      cpu: 45.2,
      memory: 67.8,
      disk: 34.5,
      network: {
        inbound: '125 MB/s',
        outbound: '89 MB/s'
      },
      processes: 156,
      services: {
        nginx: 'running',
        mysql: 'running',
        redis: 'running',
        elasticsearch: 'running'
      }
    })
  }

  const loadAPI = async () => {
    setStats({
      totalEndpoints: 45,
      activeEndpoints: 42,
      requestsToday: 15678,
      averageResponseTime: '125ms',
      errorRate: '0.02%',
      topEndpoints: [
        { endpoint: '/api/osint/lookup', requests: 5678, avgTime: '89ms' },
        { endpoint: '/api/scanner/scan', requests: 3456, avgTime: '234ms' },
        { endpoint: '/api/cve/search', requests: 2345, avgTime: '45ms' }
      ]
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent"></div>
      </div>
    )
  }

  const renderSection = () => {
    switch (section) {
      case 'users':
        return renderUserManagement()
      case 'analytics':
        return renderAnalytics()
      case 'monitoring':
        return renderMonitoring()
      case 'website-settings':
        return renderWebsiteSettings()
      case 'payments':
        return renderPayments()
      case 'bots':
        return renderBots()
      case 'whatsapp':
        return renderWhatsApp()
      case 'telegram':
        return renderTelegram()
      case 'system-config':
        return renderSystemConfig()
      case 'security':
        return renderSecurity()
      case 'database':
        return renderDatabase()
      case 'server':
        return renderServer()
      case 'api':
        return renderAPI()
      default:
        return (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Section Not Found</h2>
            <p className="text-gray-400">The requested section "{section}" is not available.</p>
          </div>
        )
    }
  }

  const renderUserManagement = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">User Management</h2>
        <div className="flex items-center space-x-4">
          <button className="btn-cyber-secondary">
            <Search className="h-4 w-4 mr-2" />
            Search Users
          </button>
          <button className="btn-cyber-primary">
            <UserPlus className="h-4 w-4 mr-2" />
            Add User
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Users</p>
              <p className="text-2xl font-bold text-white">{users.length}</p>
            </div>
            <Users className="h-8 w-8 text-blue-400" />
          </div>
        </div>
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Users</p>
              <p className="text-2xl font-bold text-white">{users.filter(u => u.status === 'active').length}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-400" />
          </div>
        </div>
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Admins</p>
              <p className="text-2xl font-bold text-white">{users.filter(u => u.role === 'admin' || u.role === 'super_admin').length}</p>
            </div>
            <Crown className="h-8 w-8 text-yellow-400" />
          </div>
        </div>
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Premium Users</p>
              <p className="text-2xl font-bold text-white">{users.filter(u => u.plan !== 'Free').length}</p>
            </div>
            <Star className="h-8 w-8 text-purple-400" />
          </div>
        </div>
      </div>

      <div className="card-cyber">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-700">
                <th className="text-left py-3 px-4 text-gray-300">User</th>
                <th className="text-left py-3 px-4 text-gray-300">Role</th>
                <th className="text-left py-3 px-4 text-gray-300">Plan</th>
                <th className="text-left py-3 px-4 text-gray-300">Status</th>
                <th className="text-left py-3 px-4 text-gray-300">Last Active</th>
                <th className="text-left py-3 px-4 text-gray-300">Actions</th>
              </tr>
            </thead>
            <tbody>
              {users.map((user) => (
                <tr key={user.id} className="border-b border-gray-800 hover:bg-gray-800/50">
                  <td className="py-3 px-4">
                    <div>
                      <p className="text-white font-medium">{user.username}</p>
                      <p className="text-gray-400 text-sm">{user.email}</p>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      user.role === 'super_admin' ? 'bg-red-500/20 text-red-400' :
                      user.role === 'admin' ? 'bg-yellow-500/20 text-yellow-400' :
                      'bg-blue-500/20 text-blue-400'
                    }`}>
                      {user.role.replace('_', ' ').toUpperCase()}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-cyber-primary">{user.plan}</span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      user.status === 'active' ? 'bg-green-500/20 text-green-400' :
                      user.status === 'suspended' ? 'bg-yellow-500/20 text-yellow-400' :
                      'bg-red-500/20 text-red-400'
                    }`}>
                      {user.status.toUpperCase()}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-gray-400 text-sm">
                    {new Date(user.last_active).toLocaleDateString()}
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center space-x-2">
                      <button className="p-1 text-gray-400 hover:text-white">
                        <Eye className="h-4 w-4" />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-white">
                        <Edit className="h-4 w-4" />
                      </button>
                      {user.role !== 'super_admin' && (
                        <button className="p-1 text-gray-400 hover:text-red-400">
                          <Ban className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )

  const renderAnalytics = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Platform Analytics</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Users</p>
              <p className="text-2xl font-bold text-white">{stats.totalUsers?.toLocaleString()}</p>
            </div>
            <Users className="h-8 w-8 text-blue-400" />
          </div>
        </div>
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Revenue</p>
              <p className="text-2xl font-bold text-white">${stats.revenue?.toLocaleString()}</p>
            </div>
            <DollarSign className="h-8 w-8 text-green-400" />
          </div>
        </div>
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Scans</p>
              <p className="text-2xl font-bold text-white">{stats.totalScans?.toLocaleString()}</p>
            </div>
            <Shield className="h-8 w-8 text-cyber-primary" />
          </div>
        </div>
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">API Calls</p>
              <p className="text-2xl font-bold text-white">{stats.apiCalls?.toLocaleString()}</p>
            </div>
            <Webhook className="h-8 w-8 text-purple-400" />
          </div>
        </div>
      </div>
    </div>
  )

  const renderMonitoring = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">System Monitoring</h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card-cyber">
          <h3 className="text-lg font-semibold text-white mb-4">CPU Usage</h3>
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-300">Current</span>
            <span className="text-cyber-primary font-medium">{stats.cpuUsage?.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-cyber-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${stats.cpuUsage}%` }}
            ></div>
          </div>
        </div>

        <div className="card-cyber">
          <h3 className="text-lg font-semibold text-white mb-4">Memory Usage</h3>
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-300">Current</span>
            <span className="text-yellow-400 font-medium">{stats.memoryUsage?.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
              style={{ width: `${stats.memoryUsage}%` }}
            ></div>
          </div>
        </div>

        <div className="card-cyber">
          <h3 className="text-lg font-semibold text-white mb-4">Disk Usage</h3>
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-300">Current</span>
            <span className="text-green-400 font-medium">{stats.diskUsage?.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-green-400 h-2 rounded-full transition-all duration-300"
              style={{ width: `${stats.diskUsage}%` }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderWebsiteSettings = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Website Settings</h2>

      <div className="card-cyber">
        <h3 className="text-lg font-semibold text-white mb-4">General Settings</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-gray-300 text-sm font-medium mb-2">Site Name</label>
            <input
              type="text"
              value={stats.siteName || ''}
              className="input-cyber w-full"
              placeholder="Enter site name"
            />
          </div>
          <div>
            <label className="block text-gray-300 text-sm font-medium mb-2">Site Description</label>
            <textarea
              value={stats.siteDescription || ''}
              className="input-cyber w-full h-24"
              placeholder="Enter site description"
            />
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-300">Maintenance Mode</span>
            <button className={`px-4 py-2 rounded-lg font-medium ${
              stats.maintenanceMode ? 'bg-red-500/20 text-red-400' : 'bg-green-500/20 text-green-400'
            }`}>
              {stats.maintenanceMode ? 'Enabled' : 'Disabled'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )

  const renderPayments = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Payment Management</h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Revenue</p>
              <p className="text-2xl font-bold text-white">${stats.totalRevenue?.toLocaleString()}</p>
            </div>
            <DollarSign className="h-8 w-8 text-green-400" />
          </div>
        </div>
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Subscriptions</p>
              <p className="text-2xl font-bold text-white">{stats.activeSubscriptions}</p>
            </div>
            <CreditCard className="h-8 w-8 text-blue-400" />
          </div>
        </div>
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Pending Payments</p>
              <p className="text-2xl font-bold text-white">{stats.pendingPayments}</p>
            </div>
            <Clock className="h-8 w-8 text-yellow-400" />
          </div>
        </div>
      </div>
    </div>
  )

  const renderBots = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Bot Management</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="card-cyber">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">WhatsApp Bot</h3>
            <div className={`w-3 h-3 rounded-full ${
              stats.whatsappBot?.status === 'online' ? 'bg-green-400' : 'bg-red-400'
            } animate-pulse`}></div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Status</span>
              <span className="text-green-400">{stats.whatsappBot?.status}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Connected Users</span>
              <span className="text-white">{stats.whatsappBot?.connectedUsers}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Messages Processed</span>
              <span className="text-white">{stats.whatsappBot?.messagesProcessed}</span>
            </div>
          </div>
        </div>

        <div className="card-cyber">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Telegram Bot</h3>
            <div className={`w-3 h-3 rounded-full ${
              stats.telegramBot?.status === 'online' ? 'bg-green-400' : 'bg-red-400'
            } animate-pulse`}></div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Status</span>
              <span className="text-green-400">{stats.telegramBot?.status}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Connected Users</span>
              <span className="text-white">{stats.telegramBot?.connectedUsers}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Messages Processed</span>
              <span className="text-white">{stats.telegramBot?.messagesProcessed}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderWhatsApp = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">WhatsApp Bot Management</h2>

      <div className="card-cyber">
        <h3 className="text-lg font-semibold text-white mb-4">Bot Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Connection Status</span>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                stats.status === 'connected' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
              }`}>
                {stats.status}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Connected Users</span>
              <span className="text-white">{stats.connectedUsers}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Messages Processed</span>
              <span className="text-white">{stats.messagesProcessed}</span>
            </div>
          </div>
          <div className="space-y-4">
            <h4 className="text-white font-medium">Enabled Features</h4>
            {stats.features && Object.entries(stats.features).map(([feature, enabled]) => (
              <div key={feature} className="flex items-center justify-between">
                <span className="text-gray-300 capitalize">{feature.replace(/([A-Z])/g, ' $1')}</span>
                <div className={`w-3 h-3 rounded-full ${enabled ? 'bg-green-400' : 'bg-gray-600'}`}></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )

  const renderTelegram = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Telegram Bot Management</h2>

      <div className="card-cyber">
        <h3 className="text-lg font-semibold text-white mb-4">Bot Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Connection Status</span>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                stats.status === 'connected' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
              }`}>
                {stats.status}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Bot Token</span>
              <span className="text-green-400">{stats.botToken}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Connected Users</span>
              <span className="text-white">{stats.connectedUsers}</span>
            </div>
          </div>
          <div className="space-y-4">
            <h4 className="text-white font-medium">Enabled Features</h4>
            {stats.features && Object.entries(stats.features).map(([feature, enabled]) => (
              <div key={feature} className="flex items-center justify-between">
                <span className="text-gray-300 capitalize">{feature.replace(/([A-Z])/g, ' $1')}</span>
                <div className={`w-3 h-3 rounded-full ${enabled ? 'bg-green-400' : 'bg-gray-600'}`}></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )

  const renderSystemConfig = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">System Configuration</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="card-cyber">
          <h3 className="text-lg font-semibold text-white mb-4">System Info</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Environment</span>
              <span className="text-cyber-primary">{stats.environment}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Version</span>
              <span className="text-white">{stats.version}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Database</span>
              <span className="text-green-400">{stats.database}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Redis</span>
              <span className="text-green-400">{stats.redis}</span>
            </div>
          </div>
        </div>

        <div className="card-cyber">
          <h3 className="text-lg font-semibold text-white mb-4">Security Settings</h3>
          <div className="space-y-3">
            {stats.securitySettings && Object.entries(stats.securitySettings).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span className="text-gray-400 capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                <span className="text-white">{String(value)}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )

  const renderSecurity = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Security Center</h2>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Active Threats</p>
              <p className="text-2xl font-bold text-red-400">{stats.activeThreats}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-400" />
          </div>
        </div>
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Blocked IPs</p>
              <p className="text-2xl font-bold text-white">{stats.blockedIPs}</p>
            </div>
            <Shield className="h-8 w-8 text-cyber-primary" />
          </div>
        </div>
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Suspicious Activity</p>
              <p className="text-2xl font-bold text-yellow-400">{stats.suspiciousActivity}</p>
            </div>
            <Eye className="h-8 w-8 text-yellow-400" />
          </div>
        </div>
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Failed Logins</p>
              <p className="text-2xl font-bold text-orange-400">{stats.failedLogins}</p>
            </div>
            <Lock className="h-8 w-8 text-orange-400" />
          </div>
        </div>
      </div>
    </div>
  )

  const renderDatabase = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Database Administration</h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card-cyber">
          <h3 className="text-lg font-semibold text-white mb-4">Database Status</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Status</span>
              <span className="text-green-400">{stats.status}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Size</span>
              <span className="text-white">{stats.size}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Tables</span>
              <span className="text-white">{stats.tables}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Connections</span>
              <span className="text-white">{stats.connections}</span>
            </div>
          </div>
        </div>

        <div className="card-cyber">
          <h3 className="text-lg font-semibold text-white mb-4">Performance</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Total Queries</span>
              <span className="text-white">{stats.queries?.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Slow Queries</span>
              <span className="text-yellow-400">{stats.slowQueries}</span>
            </div>
          </div>
        </div>

        <div className="card-cyber">
          <h3 className="text-lg font-semibold text-white mb-4">Backup</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Status</span>
              <span className="text-green-400">{stats.backupStatus}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Last Backup</span>
              <span className="text-white">{stats.lastBackup && new Date(stats.lastBackup).toLocaleDateString()}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderServer = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Server Management</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="card-cyber">
          <h3 className="text-lg font-semibold text-white mb-4">Server Status</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Status</span>
              <span className="text-green-400">{stats.status}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Uptime</span>
              <span className="text-white">{stats.uptime}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Processes</span>
              <span className="text-white">{stats.processes}</span>
            </div>
          </div>
        </div>

        <div className="card-cyber">
          <h3 className="text-lg font-semibold text-white mb-4">Services</h3>
          <div className="space-y-3">
            {stats.services && Object.entries(stats.services).map(([service, status]) => (
              <div key={service} className="flex justify-between">
                <span className="text-gray-400 capitalize">{service}</span>
                <span className={`${status === 'running' ? 'text-green-400' : 'text-red-400'}`}>
                  {String(status)}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )

  const renderAPI = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">API Management</h2>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Endpoints</p>
              <p className="text-2xl font-bold text-white">{stats.totalEndpoints}</p>
            </div>
            <Webhook className="h-8 w-8 text-purple-400" />
          </div>
        </div>
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Requests Today</p>
              <p className="text-2xl font-bold text-white">{stats.requestsToday?.toLocaleString()}</p>
            </div>
            <Activity className="h-8 w-8 text-green-400" />
          </div>
        </div>
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Avg Response Time</p>
              <p className="text-2xl font-bold text-white">{stats.averageResponseTime}</p>
            </div>
            <Clock className="h-8 w-8 text-blue-400" />
          </div>
        </div>
        <div className="card-cyber">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Error Rate</p>
              <p className="text-2xl font-bold text-white">{stats.errorRate}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-yellow-400" />
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="space-y-8">
      {renderSection()}
    </div>
  )
}
