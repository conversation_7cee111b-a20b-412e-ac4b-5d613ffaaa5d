"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const SENTINEL_MASTER_1 = __importDefault(require("./SENTINEL_MASTER"));
const SENTINEL_MONITOR_1 = __importDefault(require("./SENTINEL_MONITOR"));
const SENTINEL_REPLICAS_1 = __importDefault(require("./SENTINEL_REPLICAS"));
const SENTINEL_SENTINELS_1 = __importDefault(require("./SENTINEL_SENTINELS"));
const SENTINEL_SET_1 = __importDefault(require("./SENTINEL_SET"));
exports.default = {
    SENTINEL_SENTINELS: SENTINEL_SENTINELS_1.default,
    sentinelSentinels: SENTINEL_SENTINELS_1.default,
    SENTINEL_MASTER: SENTINEL_MASTER_1.default,
    sentinelMaster: SENTINEL_MASTER_1.default,
    SENTINEL_REPLICAS: SENTINEL_REPLICAS_1.default,
    sentinelReplicas: SENTINEL_REPLICAS_1.default,
    SENTINEL_MONITOR: SENTINEL_MONITOR_1.default,
    sentinelMonitor: SENTINEL_MONITOR_1.default,
    SENTINEL_SET: SENTINEL_SET_1.default,
    sentinelSet: SENTINEL_SET_1.default
};
//# sourceMappingURL=index.js.map