{"version": 3, "file": "logging.js", "sourceRoot": "", "sources": ["../../../src/lib/logging.ts"], "names": [], "mappings": ";;;;;;AAAA;;GAEG;AACH,4DAA8B;AAC9B,iEAAwC;AAMxC,oDAA2B;AAC3B,kEAAyC;AACzC,8DAAqC;AACrC,iDAOuB;AAIvB,uCAAuC;AACvC,MAAM,SAAS,GAAG;IAChB,MAAM,EAAE,CAAC;IACT,KAAK,EAAE,CAAC;IACR,OAAO,EAAE,CAAC;IACV,IAAI,EAAE,CAAC;IACP,IAAI,EAAE,CAAC;IACP,OAAO,EAAE,CAAC;IACV,KAAK,EAAE,CAAC;CACT,CAAA;AAED,6FAA6F;AAC7F,MAAM,WAAW,GAAG,CAAC,IAAiB,EAAE,EAAE,CACxC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IACzB,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IACzB,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;IAC9B,4DAA4D;IAC5D,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AAE7B;;;;;;;GAOG;AACH,SAAgB,KAAK,CACnB,OAAgB,EAChB,OAAY,EACZ,WAAqB,IAAI,EACzB,SAA4C,KAAK;;IAEjD,mBAAmB;IACnB,aAAa;IACb,4CAA4C;IAC5C,IACE,CAAC,OAAO,CAAC,IAAI;QACb,OAAO,CAAC,QAAQ,KAAK,QAAQ;QAC7B,CAAC,QAAQ,IAAI,IAAI;YACf,SAAS,CAAC,CAAC,MAAA,OAAO,CAAC,QAAQ,mCAAI,MAAM,CAAsC,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,CAAC,EACtG;QACA,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAA;KACzB;AACH,CAAC;AAjBD,sBAiBC;AAED,kCAAkC;AAClC,SAAgB,SAAS,CAAC,OAAgB,EAAE,MAAW;IACrD,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;QACjC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;KAC7C;AACH,CAAC;AAJD,8BAIC;AAED,4DAA4D;AAC5D,SAAgB,uBAAuB,CAAC,MAAW,EAAE,IAAY;IAC/D,OAAO,CAAC,GAAG,CACT,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;SAChB,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;SACnC,IAAI,CAAC,IAAI,CAAC,CACd,CAAA;AACH,CAAC;AAND,0DAMC;AAED,sCAAsC;AACtC,SAAgB,WAAW,CAAmC,OAAgB,EAAE,GAAM,EAAE,QAAkB;IACxG,kDAAkD;IAClD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAiB,CAAA;IACzD,MAAM,SAAS,GAAG,IAAA,mBAAS,EACzB,UAAU,EACV,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACb,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAA;IACvB,CAAC,EACD,EAAO,CACR,CAAA;IACD,KAAK,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAA;AACrC,CAAC;AAXD,kCAWC;AAED,+FAA+F;AAC/F,SAAS,qBAAqB,CAAC,IAAgB;IAC7C,MAAM,KAAK,GAAG,IAAI,oBAAK,CAAC;QACtB,SAAS,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;QAC9D,KAAK,EAAE;YACL,GAAG,EAAE,EAAE;YACP,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,EAAE;YACd,WAAW,EAAE,EAAE;YACf,MAAM,EAAE,EAAE;YACV,YAAY,EAAE,EAAE;YAChB,aAAa,EAAE,EAAE;YACjB,cAAc,EAAE,EAAE;YAClB,IAAI,EAAE,EAAE;YACR,UAAU,EAAE,EAAE;YACd,GAAG,EAAE,EAAE;YACP,SAAS,EAAE,EAAE;YACb,KAAK,EAAE,EAAE;YACT,WAAW,EAAE,EAAE;YACf,MAAM,EAAE,EAAE;SACX;KACF,CAAC,CAAA;IAEF,kDAAkD;IAClD,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAA;IAEnB,6CAA6C;IAC7C,iDAAiD;IACjD,qGAAqG;IACrG,OAAO,KAAK;SACT,QAAQ,EAAE;SACV,KAAK,CAAC,IAAI,CAAC;SACX,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;SAC3B,IAAI,CAAC,IAAI,CAAC,CAAA;AACf,CAAC;AAED;;;;GAIG;AACH,SAAS,UAAU,CAAC,GAAW;IAC7B,OAAO,IAAA,0BAAW,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAA,8BAAe,EAAC,GAAG,CAAE,CAAC,CAAC,CAAC,IAAA,yBAAU,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAA,4BAAa,EAAC,GAAG,CAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;AAClG,CAAC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,iBAAiB,CAAC,EACtC,IAAI,EAAE,QAAQ,EACd,EAAE,EAAE,MAAM,EACV,MAAM,EACN,iBAAiB,EACjB,OAAO,EACP,IAAI,GASL;IACC,MAAM,KAAK,GAAG,qBAAqB,CACjC,MAAM,OAAO,CAAC,GAAG;IACf,kDAAkD;IAClD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;SAChB,IAAI,EAAE;SACN,GAAG,CAAC,KAAK,EAAC,GAAG,EAAC,EAAE;QACf,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA;QAChC,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA;QAC/B,MAAM,EAAE,GAAG,UAAU,CAAC,KAAK,CAAC,CAAA;QAC5B,MAAM,YAAY,GAAG,iBAAiB;YACpC,CAAC,CAAC,GAAG,IAAI,iBAAiB;gBACxB,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC;oBACtB,CAAC,CAAC,iBAAiB;oBACnB,CAAC,CAAC,EAAE;gBACN,CAAC,CAAC,WAAW;YACf,CAAC,CAAC,EAAE,CAAA;QACN,MAAM,WAAW,GAAG,IAAA,2BAAY,EAAC,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAA;QACtD,MAAM,OAAO,GAAG,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,QAAQ,CAAC,MAAM,CAAC,EAAC,CAAC,CAAC,CAAC,MAAM,IAAA,oBAAU,EAAC,GAAG,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QACrG,MAAM,WAAW,GAAG,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,QAAQ,CAAC,MAAM,CAAC,MAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAG,GAAG,CAAC,CAAA,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;QAC5E,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;IAC9F,CAAC,CAAC,CACL,CACF,CAAA;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAxCD,8CAwCC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,kBAAkB,CACtC,EACE,OAAO,EACP,QAAQ,EACR,iBAAiB,EACjB,OAAO,EACP,IAAI,GAOL,EACD,OAAgB;;IAEhB,QAAQ;IACR,IAAI,MAAA,OAAO,CAAC,MAAM,0CAAE,QAAQ,CAAC,OAAO,CAAC,EAAE;QACrC,MAAM,MAAM,GAAG,IAAA,kCAAmB,EAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;QAE9D,sFAAsF;QACtF,KAAK,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,MAAM,EAAE;YAC1C,KAAK,CAAC,OAAO,EAAE,IAAI,GAAG,OAAO,CAAC,CAAA;YAC9B,KAAK,CACH,OAAO,EACP,MAAM,iBAAiB,CAAC;gBACtB,IAAI,EAAE,OAAO;gBACb,EAAE,EAAE,QAAQ;gBACZ,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,iBAAiB;gBACjB,OAAO;gBACP,IAAI;aACL,CAAC,CACH,CAAA;SACF;KACF;SAAM;QACL,IAAI,MAAA,OAAO,CAAC,MAAM,0CAAE,QAAQ,CAAC,OAAO,CAAC,EAAE;YACrC,uBAAuB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;SACxC;aAAM;YACL,KAAK,CACH,OAAO,EACP,MAAM,iBAAiB,CAAC;gBACtB,IAAI,EAAE,OAAO;gBACb,EAAE,EAAE,QAAQ;gBACZ,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,iBAAiB;gBACjB,OAAO;gBACP,IAAI;aACL,CAAC,CACH,CAAA;SACF;KACF;AACH,CAAC;AApDD,gDAoDC;AAED,qBAAqB;AACrB,SAAS,WAAW,CAAC,OAAgB,EAAE,MAAsB;IAC3D,IAAI,CAAC,MAAM;QAAE,OAAM;IACnB,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;QAClC,MAAM,UAAU,GAAG,IAAI,oBAAK,CAAC;YAC3B,SAAS,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;YAC9D,KAAK,EAAE;gBACL,GAAG,EAAE,EAAE;gBACP,SAAS,EAAE,EAAE;gBACb,UAAU,EAAE,EAAE;gBACd,WAAW,EAAE,EAAE;gBACf,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,EAAE;gBAChB,aAAa,EAAE,EAAE;gBACjB,cAAc,EAAE,EAAE;gBAClB,IAAI,EAAE,EAAE;gBACR,UAAU,EAAE,EAAE;gBACd,GAAG,EAAE,EAAE;gBACP,SAAS,EAAE,EAAE;gBACb,KAAK,EAAE,EAAE;gBACT,WAAW,EAAE,EAAE;gBACf,MAAM,EAAE,EAAE;aACX;SACF,CAAC,CAAA;QAEF,kDAAkD;QAClD,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,eAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;QAE5F,KAAK,CAAC,OAAO,EAAE,IAAI,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAA;KAC7C;AACH,CAAC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,aAAa,CACjC,OAAgB,EAChB,EACE,OAAO,EACP,MAAM,EACN,QAAQ,EACR,KAAK,EACL,iBAAiB,EACjB,OAAO,EACP,IAAI,EACJ,MAAM,GAkBP;;IAED,IAAI,CAAC,CAAA,MAAA,OAAO,CAAC,MAAM,0CAAE,QAAQ,CAAC,OAAO,CAAC,CAAA,EAAE;QACtC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;KACnB;IAED,MAAM,MAAM,GAAG,eAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACrC,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,MAAM,CAAA;IAClD,MAAM,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAA;IAC7E,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAA;IAChD,IAAI,WAAW,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,SAAS,KAAK,CAAC,EAAE;QACvD,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YACrC,KAAK,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAA;SACnC;aAAM,IACL,MAAM;YACN,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC;YAChC,gHAAgH;YAChH,MAAM,CAAC,MAAM,CAAC,IAAA,sBAAY,EAAC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAClF;YACA,KAAK,CACH,OAAO,EACP,gFACE,OAAO,CAAC,cACV,8DAA8D,eAAK,CAAC,IAAI,CACtE,kCAAkC,CACnC,wCAAwC,CAC1C,CAAA;SACF;aAAM,IAAI,OAAO,CAAC,MAAM,EAAE;YACzB,KAAK,CAAC,OAAO,EAAE,sCAAsC,MAAM,EAAE,CAAC,CAAA;SAC/D;aAAM;YACL,KAAK,CAAC,OAAO,EAAE,8BAA8B,MAAM,qBAAqB,MAAM,EAAE,CAAC,CAAA;SAClF;KACF;SAAM,IAAI,WAAW,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;QACzC,KAAK,CAAC,OAAO,EAAE,4BAA4B,MAAM,EAAE,CAAC,CAAA;KACrD;IACD,cAAc;SACT,IAAI,WAAW,GAAG,CAAC,EAAE;QACxB,MAAM,kBAAkB,CACtB;YACE,OAAO;YACP,QAAQ;YACR,iBAAiB;YACjB,OAAO;YACP,IAAI;SACL,EACD,OAAO,CACR,CAAA;KACF;IAED,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;AAC9B,CAAC;AA9ED,sCA8EC;AAED,6EAA6E;AAC7E,SAAgB,mBAAmB,CAAC,OAAgB,EAAE,cAAqC;IACzF,KAAK,CAAC,OAAO,EAAE,uDAAuD,CAAC,CAAA;IACvE,MAAM,KAAK,GAAG,qBAAqB,CACjC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE;QACrE,MAAM,SAAS,GACb,UAAU;YACV,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;iBACnB,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC,SAAS,GAAG,YAAY,GAAG,WAAW,CAAC;iBACzE,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,IAAA,2BAAY,EAAC,IAAI,EAAE,EAAE,CAAC,EAAE,SAAS,CAAC,CAAA;IAChE,CAAC,CAAC,CACH,CAAA;IACD,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;AACvB,CAAC;AAbD,kDAaC"}