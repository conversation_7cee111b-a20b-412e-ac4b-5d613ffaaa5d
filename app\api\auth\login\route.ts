import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { z } from 'zod'

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional()
})

export async function POST(request: NextRequest) {
  const startTime = Date.now()

  try {
    console.log('🔐 Login attempt started')

    const body = await request.json()
    console.log('Login attempt for email:', body.email)

    const validation = loginSchema.safeParse(body)
    if (!validation.success) {
      console.log('❌ Validation failed:', validation.error.errors)
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const { email, password, rememberMe } = validation.data

    console.log('🔍 Attempting authentication for:', email)
    const result = await RealAuthService.login(email, password)

    if (!result.success) {
      console.log('❌ Login failed:', result.message)
      return NextResponse.json(
        {
          success: false,
          message: result.message
        },
        { status: 401 }
      )
    }

    console.log('✅ Login successful for user:', result.user.id)

    const response = NextResponse.json({
      success: true,
      message: 'Login successful',
      user: {
        id: result.user.id,
        username: result.user.username,
        email: result.user.email,
        fullName: result.user.full_name,
        role: result.user.role,
        plan: result.user.plan,
        level: result.user.level,
        score: result.user.score,
        streak: result.user.streak_days,
        emailVerified: result.user.email_verified,
        lastActive: result.user.last_active,
        createdAt: result.user.created_at
      },
      tokens: {
        accessToken: result.tokens.accessToken,
        refreshToken: result.tokens.refreshToken,
        expiresIn: result.tokens.expiresIn
      }
    })

    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      path: '/'
    }

    response.cookies.set('accessToken', result.tokens.accessToken, {
      ...cookieOptions,
      maxAge: rememberMe ? 7 * 24 * 60 * 60 : 24 * 60 * 60
    })

    response.cookies.set('refreshToken', result.tokens.refreshToken, {
      ...cookieOptions,
      maxAge: rememberMe ? 30 * 24 * 60 * 60 : 7 * 24 * 60 * 60
    })

    response.cookies.set('user', JSON.stringify({
      id: result.user.id,
      username: result.user.username,
      email: result.user.email,
      role: result.user.role,
      plan: result.user.plan
    }), {
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      path: '/',
      maxAge: rememberMe ? 7 * 24 * 60 * 60 : 24 * 60 * 60
    })

    console.log(`✅ Login completed in ${Date.now() - startTime}ms`)
    return response

  } catch (error) {
    console.error('❌ Login error:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred during login'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}