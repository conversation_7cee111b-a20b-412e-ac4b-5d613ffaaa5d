{"version": 3, "sources": ["visitor/vectorassembler.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;AAGrB,4CAAsC;AACtC,8CAAwC;AACxC,wCAA6C;AAC7C,sDAAgD;AAEhD,iDAAuD;AACvD,2CAA2D;AAC3D,2DAAqE;AACrE,wCAIoB;AACpB,iDAAmD;AA8BnD,cAAc;AACd,MAAa,eAAgB,SAAQ,oBAAO;IAExC,kBAAkB;IACX,MAAM,CAAC,QAAQ,CAAiC,GAAG,IAAiB;QACvE,MAAM,MAAM,GAAG,CAAC,KAAkB,EAAU,EAAE,CAC1C,KAAK,CAAC,OAAO,CAAC,CAAC,IAAa,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;YACjE,CAAC,IAAI,YAAY,4BAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxE,MAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;QACxC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QAClC,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;QAAwB,KAAK,EAAE,CAAC;QA4CtB,gBAAW,GAAG,CAAC,CAAC;QAChB,WAAM,GAAgB,EAAE,CAAC;QACzB,aAAQ,GAAsB,EAAE,CAAC;QACjC,mBAAc,GAAmB,EAAE,CAAC;IA/Cb,CAAC;IAE3B,KAAK,CAAqB,IAAyB;QACtD,IAAI,IAAI,YAAY,kBAAM,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,kBAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;YACxB,IAAI,MAAM,GAAG,UAAU,EAAE,CAAC;gBACtB,0BAA0B;gBAC1B,MAAM,IAAI,UAAU,CAAC,oDAAoD,CAAC,CAAC;YAC/E,CAAC;YACD,IAAI,kBAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,sBAAS,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC;iBAAM,CAAC;gBACJ,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;gBAC3B,IAAI,CAAC,kBAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;oBACzB,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC;wBAC/B,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,8BAA8B;wBAClD,CAAC,CAAC,IAAA,uBAAc,EAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CACzD,CAAC;gBACN,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,sBAAS,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;YACtD,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAEM,SAAS,CAAiB,KAAc;QAC3C,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,eAAe,CAAuB,IAAa;QACtD,8DAA8D;QAC9D,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,IAAW,KAAK,KAAK,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC1C,IAAW,OAAO,KAAK,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC9C,IAAW,UAAU,KAAK,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACpD,IAAW,aAAa,KAAK,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;CAM7D;AA5DD,0CA4DC;AAED,cAAc;AACd,SAAS,SAAS,CAAwB,MAAuB;IAC7D,MAAM,UAAU,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,8BAA8B;IAC/E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,yBAAY,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC;IACxE,IAAI,CAAC,WAAW,IAAI,UAAU,CAAC;IAC/B,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,cAAc;AACd,SAAS,aAAa,CAAyC,IAAa;;IACxE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC;IACrD,0CAA0C;IAC1C,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9B,kEAAkE;IAClE,IAAI,IAAI,CAAC,IAAI,KAAK,mBAAS,CAAC,MAAM,EAAE,CAAC;QACjC,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;SAAM,IAAI,IAAI,CAAC,IAAI,KAAK,mBAAS,CAAC,KAAK,EAAE,CAAC;QACvC,2FAA2F;QAC3F,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACnB,oEAAoE;YACpE,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YACnC,gDAAgD;YAChD,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACJ,oFAAoF;YACpF,yEAAyE;YACzE,4CAA4C;YAC5C,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAA2B,CAAC;YACnE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAA2B,CAAC;YACnE,uEAAuE;YACvE,uEAAuE;YACvE,wCAAwC;YACxC,KAAK,IAAI,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;gBACpD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;oBAC1C,SAAS;gBACb,CAAC;gBACD,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;oBAC/C,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;gBACvD,CAAC;gBACD,cAAc,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;gBACpD,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,MAAA,YAAY,CAAC,MAAM,CAAC,mCAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3D,CAAC;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YACrC,uCAAuC;YACvC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;gBACnD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACxC,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;gBACzC,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;gBACzC,OAAO,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC,CAAC;QACR,CAAC;IACL,CAAC;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,cAAc;AACd,SAAS,kBAAkB,CAAwC,IAAa;IAC5E,uFAAuF;IACvF,IAAI,MAAkB,CAAC;IACvB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;QAChC,qFAAqF;QACrF,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;SAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,UAAU,EAAE,CAAC;QACtD,kEAAkE;QAClE,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAA,uBAAc,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAClF,CAAC;IACD,uEAAuE;IACvE,sEAAsE;IACtE,mEAAmE;IACnE,oCAAoC;IACpC,0BAA0B;IAC1B,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAA,kBAAS,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACxD,CAAC;AAED,cAAc;AACd,SAAS,kBAAkB,CAA4H,IAAa;IAChK,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACpF,CAAC;AAED,cAAc;AACd,SAAS,sBAAsB,CAA2E,IAAa;IACnH,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC;IAC9C,MAAM,KAAK,GAAG,IAAA,0BAAc,EAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,MAAM,GAAG,GAAG,IAAA,0BAAc,EAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;IACjD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,EAAE,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC;IACpE,sDAAsD;IACtD,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAA,8BAAkB,EAAC,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE,YAAmB,CAAC,CAAC,CAAC,CAAC,4BAA4B;IAC/G,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,8BAA8B;IAChG,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,cAAc;AACd,SAAS,kBAAkB,CAA+D,IAAa;IACnG,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC;IACtC,0EAA0E;IAC1E,IAAI,YAAY,EAAE,CAAC;QACf,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,GAAG,YAAY,CAAC;QACnD,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAA,8BAAkB,EAAC,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;QAC3E,sCAAsC;QACtC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;IAClE,CAAC;IACD,sCAAsC;IACtC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,CAAC;AAED,cAAc;AACd,SAAS,oBAAoB,CAAkD,IAAa;IACxF,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjG,CAAC;AAED,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,kBAAkB,CAAC;AACzD,eAAe,CAAC,SAAS,CAAC,QAAQ,GAAG,kBAAkB,CAAC;AACxD,eAAe,CAAC,SAAS,CAAC,UAAU,GAAG,kBAAkB,CAAC;AAC1D,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,sBAAsB,CAAC;AAC7D,eAAe,CAAC,SAAS,CAAC,cAAc,GAAG,sBAAsB,CAAC;AAClE,eAAe,CAAC,SAAS,CAAC,WAAW,GAAG,sBAAsB,CAAC;AAC/D,eAAe,CAAC,SAAS,CAAC,gBAAgB,GAAG,sBAAsB,CAAC;AACpE,eAAe,CAAC,SAAS,CAAC,oBAAoB,GAAG,kBAAkB,CAAC;AACpE,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,kBAAkB,CAAC;AACzD,eAAe,CAAC,SAAS,CAAC,cAAc,GAAG,kBAAkB,CAAC;AAC9D,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,kBAAkB,CAAC;AACzD,eAAe,CAAC,SAAS,CAAC,YAAY,GAAG,kBAAkB,CAAC;AAC5D,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,kBAAkB,CAAC;AACzD,eAAe,CAAC,SAAS,CAAC,WAAW,GAAG,oBAAoB,CAAC;AAC7D,eAAe,CAAC,SAAS,CAAC,UAAU,GAAG,aAAa,CAAC;AACrD,eAAe,CAAC,SAAS,CAAC,aAAa,GAAG,kBAAkB,CAAC;AAC7D,eAAe,CAAC,SAAS,CAAC,aAAa,GAAG,kBAAkB,CAAC;AAC7D,eAAe,CAAC,SAAS,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AAClE,eAAe,CAAC,SAAS,CAAC,QAAQ,GAAG,kBAAkB,CAAC", "file": "vectorassembler.js", "sourceRoot": "../src"}