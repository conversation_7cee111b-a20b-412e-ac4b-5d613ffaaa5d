{"version": 3, "sources": ["io/node/reader.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;AAErB,OAAO,EAAE,MAAM,EAAiB,MAAM,aAAa,CAAC;AACpD,OAAO,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAC;AACpD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAIxD,cAAc;AACd,MAAM,UAAU,kCAAkC,CAA0B,OAAkD;IAC1H,OAAO,IAAI,uBAAuB,CAAI,OAAO,CAAC,CAAC;AACnD,CAAC;AAKD,cAAc;AACd,MAAM,uBAAiD,SAAQ,MAAM;IAKjE,YAAY,OAAkD;QAC1D,KAAK,+BAAG,aAAa,EAAE,KAAK,IAAK,OAAO,KAAE,kBAAkB,EAAE,IAAI,EAAE,kBAAkB,EAAE,KAAK,IAAG,CAAC;QAL7F,aAAQ,GAAG,KAAK,CAAC;QACjB,iBAAY,GAAG,IAAI,CAAC;QAKxB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,IAAI,cAAc,EAAE,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;IAC3G,CAAC;IACD,MAAM,CAAC,EAAO;QACV,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC;QAC5B,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,KAAK,EAAE,CAAC;QACZ,EAAE,IAAI,EAAE,EAAE,CAAC;IACf,CAAC;IACD,MAAM,CAAC,CAAM,EAAE,CAAS,EAAE,EAAM;QAC5B,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC;QAC5B,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QACb,EAAE,IAAI,EAAE,EAAE,CAAC;QACX,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,KAAK,CAAC,IAAY;QACd,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC;QAC5B,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC;YACjD,CAAC,GAAS,EAAE;gBACR,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;oBAChB,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACxC,CAAC;gBACD,IAAI,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACzD,CAAC,CAAA,CAAC,EAAE,CAAC;QACT,CAAC;IACL,CAAC;IACD,QAAQ,CAAC,GAAiB,EAAE,EAAiC;QACzD,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC;QAC5B,IAAI,EAAE,EAAE,CAAC;YAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;QAAC,CAAC;QAC7C,EAAE,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;IAC/C,CAAC;IACK,KAAK,CAAC,MAAsB;;YAC9B,OAAO,MAAM,CAAC,MAAM,iBAAiB,CAAC,IAAI,CAAI,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QACpG,CAAC;KAAA;IACK,KAAK,CAAC,IAAY,EAAE,MAA4B;;YAClD,IAAI,CAAC,GAA0C,IAAI,CAAC;YACpD,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;gBACtD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC;oBAAC,MAAM;gBAAC,CAAC;YACzE,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,IAAI,KAAI,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;gBAC9F,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChB,MAAM,MAAM,CAAC,MAAM,EAAE,CAAC;YAC1B,CAAC;YACD,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC1B,CAAC;KAAA;CACJ", "file": "reader.mjs", "sourceRoot": "../../src"}