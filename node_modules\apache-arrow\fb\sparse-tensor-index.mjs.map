{"version": 3, "sources": ["fb/sparse-tensor-index.ts"], "names": [], "mappings": "AAAA,qEAAqE;AAErE,OAAO,EAAE,oBAAoB,EAAE,MAAM,8BAA8B,CAAC;AACpE,OAAO,EAAE,oBAAoB,EAAE,MAAM,8BAA8B,CAAC;AACpE,OAAO,EAAE,oBAAoB,EAAE,MAAM,8BAA8B,CAAC;AAGpE,MAAM,CAAN,IAAY,iBAKX;AALD,WAAY,iBAAiB;IAC3B,yDAAQ,CAAA;IACR,yFAAwB,CAAA;IACxB,yFAAwB,CAAA;IACxB,yFAAwB,CAAA;AAC1B,CAAC,EALW,iBAAiB,KAAjB,iBAAiB,QAK5B;AAED,MAAM,UAAU,wBAAwB,CACtC,IAAuB,EACvB,QAAqJ;IAErJ,QAAO,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,KAAK,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC;QACzB,KAAK,sBAAsB,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,oBAAoB,EAAE,CAA0B,CAAC;QAClG,KAAK,sBAAsB,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,oBAAoB,EAAE,CAA0B,CAAC;QAClG,KAAK,sBAAsB,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,oBAAoB,EAAE,CAA0B,CAAC;QAClG,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC;IACvB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,4BAA4B,CAC1C,IAAuB,EACvB,QAAoK,EACpK,KAAa;IAEb,QAAO,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,KAAK,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC;QACzB,KAAK,sBAAsB,CAAC,CAAC,OAAO,QAAQ,CAAC,KAAK,EAAE,IAAI,oBAAoB,EAAE,CAA0B,CAAC;QACzG,KAAK,sBAAsB,CAAC,CAAC,OAAO,QAAQ,CAAC,KAAK,EAAE,IAAI,oBAAoB,EAAE,CAA0B,CAAC;QACzG,KAAK,sBAAsB,CAAC,CAAC,OAAO,QAAQ,CAAC,KAAK,EAAE,IAAI,oBAAoB,EAAE,CAA0B,CAAC;QACzG,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC;IACvB,CAAC;AACH,CAAC", "file": "sparse-tensor-index.mjs", "sourceRoot": "../src"}