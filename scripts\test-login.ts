#!/usr/bin/env ts-node

// Use global fetch (available in Node.js 18+)
declare const fetch: any

async function testLogin() {
  console.log('🧪 Testing Login API...\n')

  try {
    const loginData = {
      email: '<EMAIL>',
      password: 'admin123'
    }

    console.log('📤 Sending login request...')
    console.log('Email:', loginData.email)
    console.log('Password:', '[HIDDEN]')

    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData)
    })

    console.log('\n📥 Response received:')
    console.log('Status:', response.status, response.statusText)
    console.log('Headers:', Object.fromEntries(response.headers.entries()))

    const result = await response.json()
    console.log('\n📋 Response body:')
    console.log(JSON.stringify(result, null, 2))

    if (response.ok && result.success) {
      console.log('\n✅ LOGIN TEST PASSED!')
      console.log('🎉 User successfully authenticated')
      console.log('👤 User ID:', result.user?.id)
      console.log('📧 Email:', result.user?.email)
      console.log('🔑 Role:', result.user?.role)
      console.log('📦 Plan:', result.user?.plan)
      console.log('🎫 Access Token:', result.tokens?.accessToken ? 'Generated' : 'Missing')
      console.log('🔄 Refresh Token:', result.tokens?.refreshToken ? 'Generated' : 'Missing')
    } else {
      console.log('\n❌ LOGIN TEST FAILED!')
      console.log('Error:', result.message || 'Unknown error')
    }

  } catch (error) {
    console.error('\n💥 LOGIN TEST ERROR!')
    console.error('Error:', error)
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testLogin()
}
