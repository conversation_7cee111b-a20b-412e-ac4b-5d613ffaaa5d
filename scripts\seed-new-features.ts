#!/usr/bin/env ts-node

import { testDatabaseConnection, closeDatabaseConnections, db } from '../lib/database'
import { ScoringService } from '../lib/services/scoring'

async function seedNewFeatures() {
  console.log('🌱 Seeding new features data...\n')

  try {
    // Test database connection
    const connected = await testDatabaseConnection()
    if (!connected) {
      console.error('❌ Database connection failed')
      process.exit(1)
    }

    // Seed sample OSINT queries
    console.log('🔍 Seeding sample OSINT queries...')
    await seedOsintQueries()

    // Seed sample bot messages
    console.log('🤖 Seeding sample bot messages...')
    await seedBotMessages()

    // Seed sample payments
    console.log('💳 Seeding sample payments...')
    await seedPayments()

    // Seed sample achievements and scores
    console.log('🏆 Seeding sample achievements and scores...')
    await seedAchievementsAndScores()

    // Seed admin logs
    console.log('📋 Seeding sample admin logs...')
    await seedAdminLogs()

    console.log('\n✅ New features data seeded successfully!')
    
  } catch (error) {
    console.error('❌ Seeding failed:', error)
    process.exit(1)
  } finally {
    await closeDatabaseConnections()
  }
}

async function seedOsintQueries() {
  const sampleQueries = [
    {
      id: 'osint_001',
      user_id: 1,
      type: 'nik',
      value: '1234567890123456',
      sources: JSON.stringify(['dukcapil', 'leaked']),
      status: 'completed',
      results: JSON.stringify({
        found: true,
        sources: ['Dukcapil Leak 2019'],
        riskLevel: 'high'
      }),
      confidence_score: 0.85,
      risk_level: 'high'
    },
    {
      id: 'osint_002',
      user_id: 1,
      type: 'phone',
      value: '081234567890',
      sources: JSON.stringify(['carrier', 'leaked', 'social']),
      status: 'completed',
      results: JSON.stringify({
        carrier: 'Telkomsel',
        leaked: false,
        socialPresence: true
      }),
      confidence_score: 0.75,
      risk_level: 'medium'
    },
    {
      id: 'osint_003',
      user_id: 1,
      type: 'imei',
      value: '123456789012345',
      sources: JSON.stringify(['device', 'stolen']),
      status: 'completed',
      results: JSON.stringify({
        device: { brand: 'Apple', model: 'iPhone 12' },
        stolen: false
      }),
      confidence_score: 0.90,
      risk_level: 'low'
    }
  ]

  for (const query of sampleQueries) {
    await db.query(`
      INSERT IGNORE INTO osint_queries 
      (id, user_id, type, value, sources, status, results, confidence_score, risk_level, created_at, completed_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      query.id, query.user_id, query.type, query.value, query.sources,
      query.status, query.results, query.confidence_score, query.risk_level
    ])
  }

  console.log(`   ✅ Seeded ${sampleQueries.length} OSINT queries`)
}

async function seedBotMessages() {
  const sampleMessages = [
    {
      id: 'msg_001',
      bot_id: 'whatsapp_main',
      user_phone: '6281234567890',
      message_type: 'text',
      message_content: '/help',
      command: 'help',
      response: 'KodeXGuard Bot Commands: /help, /scan, /nik, /status',
      status: 'responded'
    },
    {
      id: 'msg_002',
      bot_id: 'telegram_main',
      user_telegram_id: 123456789,
      message_type: 'text',
      message_content: '/nik 1234567890123456',
      command: 'nik',
      response: 'NIK investigation completed. Found in leaked databases.',
      status: 'responded'
    }
  ]

  for (const message of sampleMessages) {
    await db.query(`
      INSERT IGNORE INTO bot_messages 
      (id, bot_id, user_phone, user_telegram_id, message_type, message_content, command, response, status, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      message.id, message.bot_id, message.user_phone, message.user_telegram_id,
      message.message_type, message.message_content, message.command, message.response, message.status
    ])
  }

  console.log(`   ✅ Seeded ${sampleMessages.length} bot messages`)
}

async function seedPayments() {
  const samplePayments = [
    {
      id: 'pay_001',
      user_id: 1,
      plan_id: 'student',
      amount: 50000,
      currency: 'IDR',
      gateway: 'manual',
      status: 'paid'
    },
    {
      id: 'pay_002',
      user_id: 1,
      plan_id: 'hobby',
      amount: 150000,
      currency: 'IDR',
      gateway: 'tripay',
      status: 'pending'
    }
  ]

  for (const payment of samplePayments) {
    await db.query(`
      INSERT IGNORE INTO payments 
      (id, user_id, plan_id, amount, currency, gateway, status, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      payment.id, payment.user_id, payment.plan_id, payment.amount,
      payment.currency, payment.gateway, payment.status
    ])

    // Add payment log
    await db.query(`
      INSERT IGNORE INTO payment_logs 
      (id, payment_id, user_id, action, details, created_at)
      VALUES (?, ?, ?, 'create', '{"gateway": "?"}', NOW())
    `, [`log_${payment.id}`, payment.id, payment.user_id, payment.gateway])
  }

  console.log(`   ✅ Seeded ${samplePayments.length} payments`)
}

async function seedAchievementsAndScores() {
  const scoringService = new ScoringService()

  // Award some points to user
  await scoringService.awardPoints(1, {
    type: 'osint_query',
    points: 10,
    description: 'Completed NIK investigation'
  })

  await scoringService.awardPoints(1, {
    type: 'vulnerability_scan',
    points: 25,
    description: 'Completed vulnerability scan'
  })

  // Add sample achievements
  const achievements = [
    {
      id: 'ach_001',
      user_id: 1,
      achievement_id: 'first_osint'
    },
    {
      id: 'ach_002',
      user_id: 1,
      achievement_id: 'vulnerability_hunter'
    }
  ]

  for (const achievement of achievements) {
    await db.query(`
      INSERT IGNORE INTO user_achievements 
      (id, user_id, achievement_id, unlocked_at)
      VALUES (?, ?, ?, NOW())
    `, [achievement.id, achievement.user_id, achievement.achievement_id])
  }

  // Add sample streaks
  await db.query(`
    INSERT IGNORE INTO user_streaks 
    (id, user_id, streak_type, current_streak, longest_streak, last_activity_date, created_at)
    VALUES ('streak_001', 1, 'daily_activity', 5, 12, CURDATE(), NOW())
  `)

  console.log(`   ✅ Seeded achievements and scoring data`)
}

async function seedAdminLogs() {
  const adminLogs = [
    {
      id: 'log_001',
      admin_id: 1,
      action: 'approve_payment',
      target_type: 'payment',
      target_id: 'pay_001',
      details: JSON.stringify({
        paymentAmount: 50000,
        planId: 'student'
      })
    },
    {
      id: 'log_002',
      admin_id: 1,
      action: 'update_user_status',
      target_type: 'user',
      target_id: '2',
      details: JSON.stringify({
        previousStatus: 'inactive',
        newStatus: 'active'
      })
    }
  ]

  for (const log of adminLogs) {
    await db.query(`
      INSERT IGNORE INTO admin_logs 
      (id, admin_id, action, target_type, target_id, details, created_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW())
    `, [log.id, log.admin_id, log.action, log.target_type, log.target_id, log.details])
  }

  console.log(`   ✅ Seeded ${adminLogs.length} admin logs`)
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedNewFeatures()
}
