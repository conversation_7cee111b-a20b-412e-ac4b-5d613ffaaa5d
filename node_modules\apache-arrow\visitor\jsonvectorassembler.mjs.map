{"version": 3, "sources": ["visitor/jsonvectorassembler.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;AAErB,OAAO,EAAE,EAAE,EAAE,MAAM,eAAe,CAAC;AAGnC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AAExC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,gBAAgB,CAAC;AAC9D,OAAO,EACH,QAAQ,GAGX,MAAM,YAAY,CAAC;AA+BpB,cAAc;AACd,MAAM,OAAO,mBAAoB,SAAQ,OAAO;IAE5C,kBAAkB;IACX,MAAM,CAAC,QAAQ,CAAwB,GAAG,OAAY;QACzD,MAAM,SAAS,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC5C,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE;YACpC,OAAO,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAqB,EAAE,IAAI,EAAS,EAAE,IAAa;QAC3D,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QACxB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAC/C,MAAM,IAAI,GAAG,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAC9E,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;QACtF,uBACI,MAAM,EAAE,IAAI,EACZ,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACzD,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;oBAC9C,CAAC,CAAC,CAAC,GAAG,IAAI,WAAW,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,IACrE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,EAC9D;IACN,CAAC;IACM,SAAS,KAAK,OAAO,EAAE,CAAC,CAAC,CAAC;IAC1B,SAAS,CAAiB,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAW;QAChE,OAAO,EAAE,MAAM,EAAE,CAAC,GAAG,IAAI,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC;IACnF,CAAC;IACM,QAAQ,CAAgB,IAAa;QACxC,OAAO;YACH,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE;gBAC3B,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;gBAClB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SAC9C,CAAC;IACN,CAAC;IACM,UAAU,CAAkB,IAAa;QAC5C,OAAO,EAAE,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;IACxC,CAAC;IACM,SAAS,CAAiB,IAAa;QAC1C,OAAO,EAAE,MAAM,EAAE,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;IACjF,CAAC;IACM,cAAc,CAAsB,IAAa;QACpD,OAAO,EAAE,MAAM,EAAE,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACtG,CAAC;IACM,WAAW,CAAmB,IAAa;QAC9C,OAAO,EAAE,MAAM,EAAE,CAAC,GAAG,cAAc,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;IACjG,CAAC;IACM,gBAAgB,CAAwB,IAAa;QACxD,OAAO,EAAE,MAAM,EAAE,CAAC,GAAG,cAAc,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACtH,CAAC;IACM,oBAAoB,CAA4B,IAAa;QAChE,OAAO,EAAE,MAAM,EAAE,CAAC,GAAG,cAAc,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/D,CAAC;IACM,SAAS,CAAkB,IAAa;QAC3C,OAAO;YACH,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,GAAG;gBACnC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;gBAClB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SAC9C,CAAC;IACN,CAAC;IACM,cAAc,CAAsB,IAAa;QACpD,OAAO,EAAE,MAAM,EAAE,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7D,CAAC;IACM,SAAS,CAAiB,IAAa;QAC1C,OAAO;YACH,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,WAAW;gBACzC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;gBAClB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SAC9C,CAAC;IACN,CAAC;IACM,YAAY,CAAoB,IAAa;QAChD,OAAO,EAAE,MAAM,EAAE,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7D,CAAC;IACM,SAAS,CAAiB,IAAa;QAC1C,OAAO;YACH,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;YAChC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC;SAChE,CAAC;IACN,CAAC;IACM,WAAW,CAAmB,IAAa;QAC9C,OAAO;YACH,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC;SAChE,CAAC;IACN,CAAC;IACM,UAAU,CAAkB,IAAa;QAC5C,OAAO;YACH,SAAS,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;YAC5B,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS;YACjF,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC;SAChE,CAAC;IACN,CAAC;IACM,aAAa,CAAqB,IAAa;QAClD,OAAO,EAAE,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;IACxC,CAAC;IACM,aAAa,CAAqB,IAAa;QAClD,OAAO,EAAE,MAAM,EAAE,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7D,CAAC;IACM,kBAAkB,CAA0B,IAAa;QAC5D,OAAO;YACH,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC;SAChE,CAAC;IACN,CAAC;IACM,QAAQ,CAAiB,IAAa;QACzC,OAAO;YACH,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;YAChC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC;SAChE,CAAC;IACN,CAAC;CACJ;AAED,cAAc;AACd,QAAQ,CAAC,CAAC,cAAc,CAAC,MAAsE;IAC3F,KAAK,MAAM,MAAM,IAAI,MAA8B,EAAE,CAAC;QAClD,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YAC9B,OAAO,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACnE,CAAC,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;IACzB,CAAC;AACL,CAAC;AAED,cAAc;AACd,QAAQ,CAAC,CAAC,gBAAgB,CAAC,MAA4E,EAAE,MAAc;IACnH,MAAM,IAAI,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;QAClD,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC;IAChF,CAAC;AACL,CAAC", "file": "jsonvectorassembler.mjs", "sourceRoot": "../src"}