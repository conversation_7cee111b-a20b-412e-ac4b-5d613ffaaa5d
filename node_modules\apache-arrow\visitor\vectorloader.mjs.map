{"version": 3, "sources": ["visitor/vectorloader.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;AAErB,OAAO,EAAQ,QAAQ,EAAE,MAAM,YAAY,CAAC;AAE5C,OAAO,EAAE,KAAK,EAAE,MAAM,cAAc,CAAC;AAErC,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAC7C,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAC/C,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAClE,OAAO,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AAStD,cAAc;AACd,MAAM,OAAO,YAAa,SAAQ,OAAO;IAQrC,YAAY,KAAiB,EAAE,KAAkB,EAAE,OAAuB,EAAE,YAAsC,EAAE,eAAe,GAAG,eAAe,CAAC,EAAE;QACpJ,KAAK,EAAE,CAAC;QANJ,eAAU,GAAG,CAAC,CAAC,CAAC;QAEhB,iBAAY,GAAG,CAAC,CAAC,CAAC;QAKtB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IAC3C,CAAC;IAEM,KAAK,CAAqB,IAAkB;QAC/C,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,YAAY,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACjE,CAAC;IAEM,SAAS,CAAsB,IAAO,EAAE,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QAC5E,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;IACtC,CAAC;IACM,SAAS,CAAsB,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACvF,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9H,CAAC;IACM,QAAQ,CAAqB,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACrF,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9H,CAAC;IACM,UAAU,CAAuB,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACzF,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9H,CAAC;IACM,SAAS,CAAsB,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACvF,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpK,CAAC;IACM,cAAc,CAA2B,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACjG,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpK,CAAC;IACM,WAAW,CAAwB,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QAC3F,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpK,CAAC;IACM,gBAAgB,CAA6B,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACrG,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpK,CAAC;IACM,oBAAoB,CAAiC,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QAC7G,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9H,CAAC;IACM,SAAS,CAAuB,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACxF,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9H,CAAC;IACM,cAAc,CAA2B,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACjG,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9H,CAAC;IACM,SAAS,CAAsB,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACvF,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9H,CAAC;IACM,YAAY,CAAyB,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QAC7F,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9H,CAAC;IACM,SAAS,CAAsB,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACvF,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChL,CAAC;IACM,WAAW,CAAwB,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QAC3F,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC5I,CAAC;IACM,UAAU,CAAuB,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACzF,IAAI,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC,EAAE,EAAE,CAAC;YAC5C,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACzC,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,MAAM;YACjC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAwB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;YACxE,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,IAAuB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;IAC/E,CAAC;IACM,eAAe,CAA4B,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACnG,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACjK,CAAC;IACM,gBAAgB,CAA6B,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACrG,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC3H,CAAC;IACM,eAAe,CAA4B,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACnG,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7K,CAAC;IACM,aAAa,CAA0B,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QAC/F,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9H,CAAC;IACM,aAAa,CAA0B,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QAC/F,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9H,CAAC;IACM,kBAAkB,CAA+B,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACzG,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1I,CAAC;IACM,QAAQ,CAAsB,IAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;QACtF,OAAO,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChL,CAAC;IAES,aAAa,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACzD,eAAe,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/D,cAAc,CAAqB,IAAO,EAAE,SAAiB,EAAE,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE;QACpG,OAAO,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;IAC7E,CAAC;IACS,WAAW,CAAqB,IAAO,EAAE,MAAqB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IACvG,WAAW,CAAqB,IAAO,EAAE,MAAqB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IACvG,QAAQ,CAAqB,KAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE;QACxF,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;IACxD,CAAC;IACS,cAAc,CAA4B,IAAO;QACvD,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAE,CAAC;IAC3C,CAAC;CACJ;AAED,cAAc;AACd,MAAM,OAAO,gBAAiB,SAAQ,YAAY;IAE9C,YAAY,OAAgB,EAAE,KAAkB,EAAE,OAAuB,EAAE,YAAsC,EAAE,eAAgC;QAC/I,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;QACxE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;IACS,cAAc,CAAqB,KAAQ,EAAE,SAAiB,EAAE,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE;QACzG,OAAO,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IAChF,CAAC;IACS,WAAW,CAAqB,KAAQ,EAAE,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE;QACnF,OAAO,iBAAiB,CAAC,UAAU,EAAE,iBAAiB,CAAC,KAAK,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACzG,CAAC;IACS,WAAW,CAAqB,IAAO,EAAE,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE;QAClF,OAAO,iBAAiB,CAAC,UAAU,EAAE,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAClG,CAAC;IACS,QAAQ,CAAqB,IAAO,EAAE,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE;QAC/E,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QACzB,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7B,OAAO,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAa,CAAC,CAAC,CAAC;QAC1F,CAAC;aAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,KAAK,EAAE,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9G,OAAO,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAa,CAAC,CAAC,CAAC;QAC1F,CAAC;aAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,WAAW,EAAE,CAAC;YACrE,OAAO,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAa,CAAC,CAAC,CAAC;QAC1F,CAAC;aAAM,IAAI,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,OAAO,iBAAiB,CAAC,UAAU,EAAE,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAa,CAAC,CAAC,CAAC;QAC3F,CAAC;aAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;YACrG,OAAO,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAa,CAAC,CAAC;QAC3D,CAAC;aAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,OAAO,SAAS,CAAC,OAAO,CAAC,MAAM,CAAa,CAAC,CAAC;QAClD,CAAC;aAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7D,OAAO,UAAU,CAAE,OAAO,CAAC,MAAM,CAAc,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,iBAAiB,CAAC,UAAU,EAAE,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5G,CAAC;CACJ;AAED,cAAc;AACd,SAAS,kBAAkB,CAAC,MAAgB;IACxC,8CAA8C;IAC9C,yEAAyE;IACzE,YAAY;IACZ,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC/B,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QACxC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC/D,CAAC;IACD,OAAO,IAAI,CAAC;AAChB,CAAC", "file": "vectorloader.mjs", "sourceRoot": "../src"}