{"version": 3, "file": "getPeerDependenciesFromRegistry.js", "sourceRoot": "", "sources": ["../../../src/lib/getPeerDependenciesFromRegistry.ts"], "names": [], "mappings": ";;;;;AAAA,wDAAkC;AAIlC,4EAAmD;AAEnD;;;;;;GAMG;AACH,KAAK,UAAU,+BAA+B,CAAC,UAA8B,EAAE,OAAgB;IAC7F,MAAM,cAAc,GAAG,IAAA,2BAAiB,EAAC,OAAO,EAAE,OAAO,CAAC,cAAc,CAAC,CAAA;IACzE,IAAI,CAAC,cAAc,CAAC,mBAAmB;QAAE,OAAO,EAAE,CAAA;IAElD,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAA;IAC/C,IAAI,GAAgB,CAAA;IACpB,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,IAAI,QAAQ,GAAG,CAAC,EAAE;QACpG,GAAG,GAAG,IAAI,kBAAW,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAA;QACxF,GAAG,CAAC,MAAM,EAAE,CAAA;KACb;IAED,OAAO,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,YAAY,EAAE,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,EAAE;QAC9E,MAAM,GAAG,GAAG,MAAM,cAAc,CAAC,mBAAoB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;QACnE,IAAI,GAAG,EAAE;YACP,GAAG,CAAC,IAAI,EAAE,CAAA;SACX;QACD,MAAM,KAAK,GAAG,MAAM,YAAY,CAAA;QAChC,OAAO,EAAE,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAA;IACjC,CAAC,EAAE,EAAE,CAAC,CAAA;AACR,CAAC;AAED,kBAAe,+BAA+B,CAAA"}