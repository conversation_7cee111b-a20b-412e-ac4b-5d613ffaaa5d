{"version": 3, "sources": ["util/buffer.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;;AAErB,6CAA6C;AAE7C,2CAAgH;AAGhH,cAAc;AACd,MAAM,cAAc,GAAG,CAAC,OAAO,iBAAiB,KAAK,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;AAEpG,cAAc;AACd,SAAS,4BAA4B,CAAC,MAAoB;IACtD,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5C,IAAI,OAAe,EAAE,OAAe,EAAE,IAAY,EAAE,IAAY,CAAC;IACjE,KAAK,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;QACvD,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACd,0FAA0F;QAC1F,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC;YACnE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,SAAS;QACb,CAAC;QACD,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;QAChD,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;QAChD,2DAA2D;QAC3D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,OAAO,EAAE,CAAC;YAC3D,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,SAAS;QACb,CAAC;QACD,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC,CAAC;IAC5E,CAAC;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,cAAc;AACd,SAAgB,MAAM,CAAmE,MAAe,EAAE,MAAe,EAAE,gBAAgB,GAAG,CAAC,EAAE,gBAAgB,GAAG,MAAM,CAAC,UAAU;IACjL,MAAM,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC;IAC3C,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;IAC/E,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,CAAC;IAC3G,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;AAClB,CAAC;AAND,wBAMC;AAED,cAAc;AACd,SAAgB,eAAe,CAAC,MAAoB,EAAE,IAAoB;IACtE,4FAA4F;IAC5F,gGAAgG;IAChG,gGAAgG;IAChG,MAAM,MAAM,GAAG,4BAA4B,CAAC,MAAM,CAAC,CAAC;IACpD,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IAChE,IAAI,MAAkB,EAAE,MAAkB,EAAE,MAA8B,CAAC;IAC3E,IAAI,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;IAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;IACtE,KAAK,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC;QACzC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QACvB,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;QACtE,IAAI,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACrC,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;gBAChC,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACnD,CAAC;iBAAM,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;gBAAC,KAAK,EAAE,CAAC;YAAC,CAAC;YACxD,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;YAC5D,MAAM;QACV,CAAC;QACD,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QACpE,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IACD,OAAO,CAAC,MAAM,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,UAAU,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7G,CAAC;AAvBD,0CAuBC;AAOD,cAAc;AACd,SAAgB,iBAAiB,CAE/B,mBAAwB,EAAE,KAA2B;IAEnD,IAAI,KAAK,GAAQ,IAAA,4BAAgB,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;IAE/D,IAAI,KAAK,YAAY,mBAAmB,EAAE,CAAC;QACvC,IAAI,mBAAmB,KAAK,UAAU,EAAE,CAAC;YACrC,8EAA8E;YAC9E,8EAA8E;YAC9E,OAAO,IAAI,mBAAmB,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;QACrF,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,IAAI,CAAC,KAAK,EAAE,CAAC;QAAC,OAAO,IAAI,mBAAmB,CAAC,CAAC,CAAC,CAAC;IAAC,CAAC;IAClD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAAC,KAAK,GAAG,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;IAAC,CAAC;IAC7D,IAAI,KAAK,YAAY,WAAW,EAAE,CAAC;QAAC,OAAO,IAAI,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAAC,CAAC;IAC5E,IAAI,KAAK,YAAY,cAAc,EAAE,CAAC;QAAC,OAAO,IAAI,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAAC,CAAC;IAC/E,IAAI,IAAA,mCAAuB,EAAC,KAAK,CAAC,EAAE,CAAC;QAAC,OAAO,iBAAiB,CAAC,mBAAmB,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;IAAC,CAAC;IACrG,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,mBAAmB,CAAC,CAAC,CAAC;QACrH,CAAC,CAAC,IAAI,mBAAmB,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,CAAC,CAAC;AAC7H,CAAC;AArBD,8CAqBC;AAED,cAAc,CAAQ,MAAM,WAAW,GAAG,CAAC,KAA2B,EAAE,EAAE,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAAnF,QAAA,WAAW,eAAwE;AAC/G,cAAc,CAAQ,MAAM,YAAY,GAAG,CAAC,KAA2B,EAAE,EAAE,CAAC,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAArF,QAAA,YAAY,gBAAyE;AACjH,cAAc,CAAQ,MAAM,YAAY,GAAG,CAAC,KAA2B,EAAE,EAAE,CAAC,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAArF,QAAA,YAAY,gBAAyE;AACjH,cAAc,CAAQ,MAAM,eAAe,GAAG,CAAC,KAA2B,EAAE,EAAE,CAAC,iBAAiB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AAA3F,QAAA,eAAe,mBAA4E;AACvH,cAAc,CAAQ,MAAM,YAAY,GAAG,CAAC,KAA2B,EAAE,EAAE,CAAC,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAArF,QAAA,YAAY,gBAAyE;AACjH,cAAc,CAAQ,MAAM,aAAa,GAAG,CAAC,KAA2B,EAAE,EAAE,CAAC,iBAAiB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AAAvF,QAAA,aAAa,iBAA0E;AACnH,cAAc,CAAQ,MAAM,aAAa,GAAG,CAAC,KAA2B,EAAE,EAAE,CAAC,iBAAiB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AAAvF,QAAA,aAAa,iBAA0E;AACnH,cAAc,CAAQ,MAAM,gBAAgB,GAAG,CAAC,KAA2B,EAAE,EAAE,CAAC,iBAAiB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAA7F,QAAA,gBAAgB,oBAA6E;AACzH,cAAc,CAAQ,MAAM,cAAc,GAAG,CAAC,KAA2B,EAAE,EAAE,CAAC,iBAAiB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AAAzF,QAAA,cAAc,kBAA2E;AACrH,cAAc,CAAQ,MAAM,cAAc,GAAG,CAAC,KAA2B,EAAE,EAAE,CAAC,iBAAiB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AAAzF,QAAA,cAAc,kBAA2E;AACrH,cAAc,CAAQ,MAAM,mBAAmB,GAAG,CAAC,KAA2B,EAAE,EAAE,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;AAAnG,QAAA,mBAAmB,uBAAgF;AAK/H,cAAc;AACd,MAAM,IAAI,GAAG,CAA+C,QAAW,EAAE,EAAE,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;AAElH,cAAc;AACd,QAAe,CAAC,CAAC,yBAAyB,CAAuB,SAAmC,EAAE,MAAoC;IACtI,MAAM,IAAI,GAAG,QAAQ,CAAC,EAAI,CAAI,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,MAAM,OAAO,GACT,CAAC,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QACvC,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;YACzC,CAAC,CAAC,CAAC,MAAM,YAAY,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC5C,CAAC,CAAC,CAAC,MAAM,YAAY,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;oBAC/C,CAAC,CAAC,CAAC,IAAA,sBAAU,EAAuB,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAExF,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,EAA2D;QAC/E,IAAI,CAAC,GAA6B,IAAI,CAAC;QACvC,GAAG,CAAC;YACA,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,iBAAiB,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE;IACtB,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAChC,OAAO,IAAI,SAAS,EAAE,CAAC;AAC3B,CAAC;AAhBD,8DAgBC;AAED,cAAc,CAAQ,MAAM,mBAAmB,GAAG,CAAC,KAAmC,EAAE,EAAE,CAAC,yBAAyB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAA3G,QAAA,mBAAmB,uBAAwF;AACvI,cAAc,CAAQ,MAAM,oBAAoB,GAAG,CAAC,KAAmC,EAAE,EAAE,CAAC,yBAAyB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAA7G,QAAA,oBAAoB,wBAAyF;AACzI,cAAc,CAAQ,MAAM,oBAAoB,GAAG,CAAC,KAAmC,EAAE,EAAE,CAAC,yBAAyB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAA7G,QAAA,oBAAoB,wBAAyF;AACzI,cAAc,CAAQ,MAAM,oBAAoB,GAAG,CAAC,KAAmC,EAAE,EAAE,CAAC,yBAAyB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAA7G,QAAA,oBAAoB,wBAAyF;AACzI,cAAc,CAAQ,MAAM,qBAAqB,GAAG,CAAC,KAAmC,EAAE,EAAE,CAAC,yBAAyB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AAA/G,QAAA,qBAAqB,yBAA0F;AAC3I,cAAc,CAAQ,MAAM,qBAAqB,GAAG,CAAC,KAAmC,EAAE,EAAE,CAAC,yBAAyB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AAA/G,QAAA,qBAAqB,yBAA0F;AAC3I,cAAc,CAAQ,MAAM,sBAAsB,GAAG,CAAC,KAAmC,EAAE,EAAE,CAAC,yBAAyB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AAAjH,QAAA,sBAAsB,0BAA2F;AAC7I,cAAc,CAAQ,MAAM,sBAAsB,GAAG,CAAC,KAAmC,EAAE,EAAE,CAAC,yBAAyB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AAAjH,QAAA,sBAAsB,0BAA2F;AAC7I,cAAc,CAAQ,MAAM,2BAA2B,GAAG,CAAC,KAAmC,EAAE,EAAE,CAAC,yBAAyB,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;AAA3H,QAAA,2BAA2B,+BAAgG;AAKvJ,cAAc;AACd,SAAuB,8BAA8B,CAAuB,SAAmC,EAAE,MAAyC;;QAEtJ,kEAAkE;QAClE,IAAI,IAAA,qBAAS,EAAuB,MAAM,CAAC,EAAE,CAAC;YAC1C,6BAAO,sBAAA,KAAK,CAAC,CAAC,yBAAA,sBAAA,8BAA8B,CAAC,SAAS,EAAE,sBAAM,MAAM,CAAA,CAAC,CAAA,CAAA,CAAA,EAAC;QAC1E,CAAC;QAED,MAAM,IAAI,GAAG,UAAmB,CAAI,oEAAI,4BAAM,sBAAM,CAAC,CAAA,CAAA,CAAC,CAAC,CAAC,IAAA,CAAC;QACzD,MAAM,IAAI,GAAG,UAA0C,MAAS;;gBAC5D,sBAAA,KAAK,CAAC,CAAC,yBAAA,sBAAA,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAiB;oBACrC,IAAI,CAAC,GAA6B,IAAI,CAAC;oBACvC,GAAG,CAAC;wBACA,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,KAAK,CAAC,CAAC;oBAChC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE;gBACtB,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAA,CAAA,CAAA,CAAC;YACnC,CAAC;SAAA,CAAC;QAEF,MAAM,OAAO,GACT,CAAC,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,8CAA8C;YACtF,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,kDAAkD;gBAC5F,CAAC,CAAC,CAAC,MAAM,YAAY,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,mDAAmD;oBAChG,CAAC,CAAC,CAAC,MAAM,YAAY,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,yDAAyD;wBACzG,CAAC,CAAC,IAAA,sBAAU,EAAuB,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,8EAA8E;4BACpI,CAAC,CAAC,CAAC,IAAA,2BAAe,EAAuB,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,oFAAoF;gCAChJ,CAAC,CAAC,MAAM,CAAC,CAAC,qCAAqC;QAE3E,sBAFsC,qCAAqC;QAE3E,KAAK,CAAC,CAAC,yBAAA,sBAAA,IAAI,CAAC,CAAC,UAAiB,EAAgE;;gBAC1F,IAAI,CAAC,GAA6B,IAAI,CAAC;gBACvC,GAAG,CAAC;oBACA,CAAC,GAAG,sBAAM,EAAE,CAAC,IAAI,CAAC,4BAAM,iBAAiB,CAAC,SAAS,EAAE,CAAC,CAAC,CAAA,CAAC,CAAA,CAAC;gBAC7D,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE;YACtB,CAAC;SAAA,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAA,CAAA,CAAA,CAAC;QACrC,6BAAO,IAAI,SAAS,EAAE,EAAC;IAC3B,CAAC;CAAA;AAjCD,wEAiCC;AAED,cAAc,CAAQ,MAAM,wBAAwB,GAAG,CAAC,KAAwC,EAAE,EAAE,CAAC,8BAA8B,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAA1H,QAAA,wBAAwB,4BAAkG;AACtJ,cAAc,CAAQ,MAAM,yBAAyB,GAAG,CAAC,KAAwC,EAAE,EAAE,CAAC,8BAA8B,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAA5H,QAAA,yBAAyB,6BAAmG;AACxJ,cAAc,CAAQ,MAAM,yBAAyB,GAAG,CAAC,KAAwC,EAAE,EAAE,CAAC,8BAA8B,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAA5H,QAAA,yBAAyB,6BAAmG;AACxJ,cAAc,CAAQ,MAAM,yBAAyB,GAAG,CAAC,KAAwC,EAAE,EAAE,CAAC,8BAA8B,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAA5H,QAAA,yBAAyB,6BAAmG;AACxJ,cAAc,CAAQ,MAAM,0BAA0B,GAAG,CAAC,KAAwC,EAAE,EAAE,CAAC,8BAA8B,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AAA9H,QAAA,0BAA0B,8BAAoG;AAC1J,cAAc,CAAQ,MAAM,0BAA0B,GAAG,CAAC,KAAwC,EAAE,EAAE,CAAC,8BAA8B,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;AAA9H,QAAA,0BAA0B,8BAAoG;AAC1J,cAAc,CAAQ,MAAM,2BAA2B,GAAG,CAAC,KAAwC,EAAE,EAAE,CAAC,8BAA8B,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AAAhI,QAAA,2BAA2B,+BAAqG;AAC5J,cAAc,CAAQ,MAAM,2BAA2B,GAAG,CAAC,KAAwC,EAAE,EAAE,CAAC,8BAA8B,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AAAhI,QAAA,2BAA2B,+BAAqG;AAC5J,cAAc,CAAQ,MAAM,gCAAgC,GAAG,CAAC,KAAwC,EAAE,EAAE,CAAC,8BAA8B,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;AAA1I,QAAA,gCAAgC,oCAA0G;AAKtK,SAAgB,kBAAkB,CAAC,MAAc,EAAE,MAAc,EAAE,YAAiB;IAChF,2EAA2E;IAC3E,mEAAmE;IACnE,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;QACf,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;YACjD,YAAY,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC;QAC9B,CAAC;IACL,CAAC;IACD,OAAO,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC5C,CAAC;AAVD,gDAUC;AAED,cAAc;AACd,SAAgB,gBAAgB,CAA2B,CAAI,EAAE,CAAI;IACjE,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IACnB,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;QAAC,OAAO,KAAK,CAAC;IAAC,CAAC;IACrC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QACR,GAAG,CAAC;YAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAAC,OAAO,KAAK,CAAC;YAAC,CAAC;QAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,EAAE;IAChE,CAAC;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AARD,4CAQC", "file": "buffer.js", "sourceRoot": "../src"}