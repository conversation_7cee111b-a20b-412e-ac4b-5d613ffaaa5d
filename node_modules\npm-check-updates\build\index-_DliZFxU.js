"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const p=Symbol("singleComment"),d=Symbol("multiComment"),h=()=>"",C=(t,a,o)=>t.slice(a,o).replace(/\S/g," "),b=(t,a)=>{let o=a-1,f=0;for(;t[o]==="\\";)o-=1,f+=1;return!!(f%2)};function y(t,{whitespace:a=!0,trailingCommas:o=!1}={}){if(typeof t!="string")throw new TypeError(`Expected argument \`jsonString\` to be a \`string\`, got \`${typeof t}\``);const f=a?C:h;let u=!1,s=!1,l=0,i="",r="",n=-1;for(let e=0;e<t.length;e++){const c=t[e],m=t[e+1];if(!s&&c==='"'&&(b(t,e)||(u=!u)),!u)if(!s&&c+m==="//")i+=t.slice(l,e),l=e,s=p,e++;else if(s===p&&c+m===`\r
`){e++,s=!1,i+=f(t,l,e),l=e;continue}else if(s===p&&c===`
`)s=!1,i+=f(t,l,e),l=e;else if(!s&&c+m==="/*"){i+=t.slice(l,e),l=e,s=d,e++;continue}else if(s===d&&c+m==="*/"){e++,s=!1,i+=f(t,l,e+1),l=e+1;continue}else o&&!s&&(n!==-1?c==="}"||c==="]"?(i+=t.slice(l,e),r+=f(i,0,1)+i.slice(1),i="",l=e,n=-1):c!==" "&&c!=="	"&&c!=="\r"&&c!==`
`&&(i+=t.slice(l,e),l=e,n=-1):c===","&&(r+=i+t.slice(l,e),i="",l=e,n=e))}return r+i+(s?f(t.slice(l)):t.slice(l))}exports.default=y;
//# sourceMappingURL=index-_DliZFxU.js.map
