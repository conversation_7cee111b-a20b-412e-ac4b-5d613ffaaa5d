{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+DAAsC;AACtC,gDAAuB;AACvB,8DAAiC;AACjC,gEAAgC;AAChC,+CAA6C;AAC7C,uCAAwC;AACxC,qDAA8C;AAC9C,4FAAmE;AACnE,0DAAiC;AACjC,0DAAiC;AACjC,oEAA2C;AAC3C,0EAAiD;AACjD,8DAAqC;AACrC,oEAA2C;AAC3C,2CAAgD;AAChD,sEAA6C;AAC7C,sEAA6C;AAC7C,gEAAuC;AACvC,8DAAqC;AAQrC,wEAAwE;AACxE,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE;IAC9B,qBAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAA;CACvD;AAED,8EAA8E;AAC9E,0EAA0E;AAC1E,oEAAoE;AACpE,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAsB,EAAE,EAAE;IAC1D,4DAA4D;IAC5D,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;AACvB,CAAC,CAAC,CAAA;AAEF;;;;;;GAMG;AACH,MAAM,OAAO,GAAG,CAAC,OAAgB,EAAE,EAAE;;IACnC,yEAAyE;IACzE,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,KAAI,MAAA,OAAO,CAAC,GAAG,CAAC,IAAI,0CAAE,QAAQ,CAAC,SAAS,CAAC,CAAA,CAAC,EAAE;QACzF,MAAM,OAAO,GACX,+DAA+D;YAC/D,iEAAiE;YACjE,qDAAqD;YACrD,oEAAoE;YACpE,gEAAgE;YAChE,mEAAmE;YACnE,mDAAmD,CAAA;QAErD,IAAA,eAAK,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;QAChC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KAChB;AACH,CAAC,CAAA;AAED,8OAA8O;AAC9O,MAAM,2BAA2B,GAAG,KAAK,EAAE,OAAgB,EAAE,OAAe,EAAE,EAAE;;IAC9E,qGAAqG;IACrG,IAAI,OAAO,CAAC,cAAc,KAAK,gBAAgB;QAAE,IAAA,iCAAuB,EAAC,EAAE,GAAG,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,CAAC,CAAA;SAC9G,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK;QAAE,OAAO,OAAO,CAAC,cAAc,CAAA;IACxE,MAAM,GAAG,GAAG,CAAA,MAAA,OAAO,CAAC,GAAG,mCAAI,OAAO,EAAC,CAAC,CAAC,GAAG,OAAO,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAA;IACpE,MAAM,YAAY,GAAG,MAAM,IAAA,gBAAM,EAAC,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC,CAAA;IACnE,OAAO,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAA;AACtC,CAAC,CAAA;AAED,4CAA4C;AAC5C,MAAM,YAAY,GAAG,CAAC,IAAc,EAAE,QAA0C,EAAE,EAAE;IAClF,mDAAmD;IACnD,4FAA4F;IAC5F,MAAM,kBAAkB,GACtB,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,QAAuB,EAAE,CAAC,CAAC,CAAE,QAA+B,CAAA;IAC/F,OAAO,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;AAC7F,CAAC,CAAA;AAED,sHAAsH;AACtH,MAAM,OAAO,GAAG,KAAK,EACnB,IAAc,EACd,QAA0C,EAC1C,OAAgB,EACE,EAAE;;IACpB,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,EAAE;QAC/B,IAAA,eAAK,EAAC,OAAO,EAAE,EAAE,CAAC,CAAA;QAClB,OAAM;KACP;IAED,gIAAgI;IAChI,kDAAkD;IAClD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC;QAAE,OAAM;IAEzC,kGAAkG;IAClG,kFAAkF;IAClF,MAAM,cAAc,GAAG,MAAM,2BAA2B,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;IAE1E,mDAAmD;IACnD,sHAAsH;IACtH,MAAM,WAAW,GAAG,OAAO,eAAK,CAAC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,GAChE,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAAC,EAChG,0BAA0B,CAAA;IAE1B,MAAM,aAAa,GAAG,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAA;IAEpE,wDAAwD;IACxD,IAAI,QAAQ,CAAA;IACZ,IAAI,aAAa,IAAI,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;QACjD,IAAA,eAAK,EAAC,OAAO,EAAE,EAAE,CAAC,CAAA;QAClB,QAAQ,GAAG,MAAM,IAAA,qBAAO,EAAC;YACvB,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,GAAG,WAAW,GAAG;YAC1B,OAAO,EAAE,IAAI;YACb,mCAAmC;YACnC,OAAO,EAAE,CAAC,KAAU,EAAE,EAAE;gBACtB,IAAI,KAAK,CAAC,OAAO,EAAE;oBACjB,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;iBACxC;YACH,CAAC;SACF,CAAC,CAAA;KACH;IAED,eAAe;IACf,IAAI,OAAO,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,aAAa,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;QACrE,IAAI,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;YAChC,IAAA,eAAK,EAAC,OAAO,EAAE,EAAE,CAAC,CAAA;SACnB;QACD,IAAA,eAAK,EAAC,OAAO,EAAE,4BAA4B,CAAC,CAAA;QAE5C,+DAA+D;QAC/D,uDAAuD;QACvD,MAAM,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,SAAS,0CAAE,MAAM,CAAA,CAAA;QACrE,MAAM,cAAc,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QAE5D,cAAc,CAAC,OAAO,CAAC,KAAK,EAAC,OAAO,EAAC,EAAE;YACrC,MAAM,cAAc,GAAG,MAAM,2BAA2B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;YAC1E,MAAM,GAAG,GAAG,cAAc,GAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,cAAc,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;YACrG,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,cAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;YACtD,IAAI,MAAM,GAAG,EAAE,CAAA;YACf,IAAI;gBACF,MAAM,IAAA,sBAAK,EAAC,GAAG,EAAE,CAAC,SAAS,CAAC,EAAE;oBAC5B,GAAG;oBACH,GAAG,EAAE;wBACH,GAAG,OAAO,CAAC,GAAG;wBACd,GAAG,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;wBAC3D,6FAA6F;wBAC7F,kFAAkF;wBAClF,kFAAkF;wBAClF,oKAAoK;wBACpK,mEAAmE;wBACnE,qEAAqE;wBACrE,GAAG,CAAC,cAAc,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,mCAAmC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;qBACvF;oBACD,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;wBACvB,MAAM,IAAI,IAAI,CAAA;oBAChB,CAAC;oBACD,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;wBACvB,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;oBAC3C,CAAC;iBACF,CAAC,CAAA;gBACF,IAAA,eAAK,EAAC,OAAO,EAAE,MAAM,CAAC,CAAA;gBACtB,IAAA,eAAK,EAAC,OAAO,EAAE,MAAM,CAAC,CAAA;aACvB;YAAC,OAAO,GAAQ,EAAE;gBACjB,8DAA8D;gBAC9D,oDAAoD;gBACpD,MAAM,IAAI,KAAK,CAAC,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,OAAO,KAAI,GAAG,IAAI,MAAM,CAAC,CAAA;aAC/C;QACH,CAAC,CAAC,CAAA;KACH;IACD,qDAAqD;SAChD,IAAI,CAAC,aAAa,EAAE;QACvB,IAAA,eAAK,EAAC,OAAO,EAAE,KAAK,WAAW,GAAG,CAAC,CAAA;KACpC;AACH,CAAC,CAAA;AAED,iGAAiG;AACjG,KAAK,UAAU,WAAW,CAAC,OAAgB,EAAE,OAAwB;;IACnE,MAAM,CAAC,oBAAoB,EAAE,iBAAiB,CAAC,GAA8B,MAAM,IAAA,wBAAc,EAAC,OAAO,CAAC,CAAA;IAE1G,MAAM,gBAAgB,GAAa,oBAAoB,CAAC,GAAG,CAAC,CAAC,WAAwB,EAAE,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;IAE/G,gGAAgG;IAChG,MAAM,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,SAAS,0CAAE,MAAM,CAAA,CAAA;IACrE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,WAAW,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAA;IAE7E,IAAI,QAAiD,CAAA;IACrD,IAAI,OAAO,CAAC,MAAM,EAAE;QAClB,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAS,EAAC,OAAO,CAAC,CAAA;QACzC,YAAY,CAAC,OAAO,CAAC,CAAA;QACrB,OAAO,QAAQ,CAAA;KAChB;SAAM,IAAI,OAAO,CAAC,IAAI,EAAE;QACvB,QAAQ,GAAG,MAAM,oBAAoB,CAAC,MAAM,CAAC,KAAK,EAAE,eAAe,EAAE,WAAwB,EAAE,EAAE;YAC/F,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAA;YACtC,+GAA+G;YAC/G,MAAM,QAAQ,GAAG,MAAM,IAAA,kBAAQ,EAAC,EAAE,WAAW,EAAE,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAA;YAC5F,IAAI,QAAQ,GAAG,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAA;YACjE,IAAI,OAAO,CAAC,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE;gBACvD,wBAAwB;gBACxB,QAAQ,GAAG,IAAA,sBAAY,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;aAC3C;YACD,MAAM,UAAU,GAAY;gBAC1B,GAAG,OAAO;gBACV,GAAG,QAAQ;gBACX,WAAW,EAAE,WAAW,CAAC,QAAQ;gBACjC,iBAAiB;aAClB,CAAA;YACD,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,MAAM,IAAA,qBAAW,EAAC,UAAU,CAAC,CAAA;YAC1D,OAAO;gBACL,GAAG,QAAQ;gBACX,8CAA8C;gBAC9C,CAAC,UAAU,CAAC,GAAG;oBACb,CAAC,CAAC,cAAI;yBACD,QAAQ,CAAC,cAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,OAAQ,CAAC;wBACjD,oDAAoD;yBACnD,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;oBACxB,CAAC,CAAC,OAAQ,CAAC,EAAE,MAAM,IAAA,kBAAQ,EAAC,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC;aAC5D,CAAA;QACH,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,EAAsC,CAAC,CAAC,CAAA;QAC3D,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,IAAA,mBAAS,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;SAC7B;KACF;SAAM;QACL,iEAAiE;QACjE,IACE,oBAAoB,CAAC,MAAM,KAAK,CAAC;YACjC,oBAAoB,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,CAAC,OAAO,CAAC,WAAW,IAAI,cAAc,CAAC,EAC5E;YACA,OAAO,CAAC,WAAW,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAA;SACvD;QACD,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,MAAM,IAAA,qBAAW,EAAC,OAAO,CAAC,CAAA;QACvD,QAAQ,GAAG,MAAM,IAAA,kBAAQ,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;KACrD;IACD,YAAY,CAAC,OAAO,CAAC,CAAA;IAErB,IAAI,OAAO,CAAC,UAAU,KAAK,CAAC,IAAI,YAAY,CAAC,gBAAgB,EAAE,QAAQ,CAAC,EAAE;QACxE,IAAA,sBAAY,EAAC,OAAO,EAAE,+BAA+B,CAAC,CAAA;KACvD;IAED,0CAA0C;IAC1C,IAAI,OAAO,CAAC,OAAO,EAAE;QACnB,wCAAwC;QACxC,4DAA4D;QAC5D,uFAAuF;QACvF,IAAI,OAAO,CAAC,cAAc,KAAK,MAAM,EAAE;YACrC,IAAA,eAAK,EAAC,OAAO,EAAE,EAAE,CAAC,CAAA;SACnB;aAAM;YACL,MAAM,OAAO,CAAC,gBAAgB,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;SACnD;KACF;IAED,OAAO,QAAQ,CAAA;AACjB,CAAC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,GAAG,CACvB,aAAyB,EAAE,EAC3B,EAAE,GAAG,KAAwB,EAAE;IAE/B,MAAM,OAAO,GAAG,MAAM,IAAA,qBAAW,EAAC,UAAU,EAAE,EAAE,GAAG,EAAE,CAAC,CAAA;IAEtD,mFAAmF;IACnF,0BAA0B;IAC1B,MAAM,IAAA,iBAAS,EAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAE9B,OAAO,CAAC,OAAO,CAAC,CAAA;IAEhB,IAAA,eAAK,EAAC,OAAO,EAAE,cAAc,EAAE,SAAS,CAAC,CAAA;IAEzC,IAAI,OAAO,CAAC,UAAU,EAAE;QACtB,MAAM,IAAA,kBAAU,EAAC,OAAO,CAAC,CAAA;KAC1B;IAED,IAAI,OAAmC,CAAA;IACvC,IAAI,cAAc,GAAkB,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAA;IAC3D,IAAI,OAAO,CAAC,OAAO,EAAE;QACnB,MAAM,SAAS,GAAG,IAAA,kBAAQ,EAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAA;QACpG,cAAc,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC/C,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBACxB,0EAA0E;gBAC1E,MAAM,KAAK,GAAG,8BAA8B,SAAS,IAAI,CAAA;gBACzD,MAAM,CAAC,KAAK,CAAC,CAAA;gBACb,IAAI;oBACF,IAAA,sBAAY,EAAC,OAAO,EAAE,KAAK,CAAC,CAAA;iBAC7B;gBAAC,OAAO,CAAC,EAAE;oBACV,UAAU;iBACX;YACH,CAAC,EAAE,SAAS,CAAC,CAAA;QACf,CAAC,CAAC,CAAA;KACH;IAED,cAAc;IACd,IAAI,OAAO,CAAC,MAAM,EAAE;QAClB,kBAAkB;QAClB,IAAI,OAAO,CAAC,OAAO,EAAE;YACnB,iGAAiG;YACjG,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,IAAA,gBAAM,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAA;SAC5D;QACD,uBAAuB;aAClB;YACH,MAAM,IAAI,GACR,OAAO,2BAAa,CAAC,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,2BAAa,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,2BAAa,CAAC,MAAM,CAAC,IAAI,CAAA;YAC7G,IAAA,eAAK,EAAC,OAAO,EAAE,0BAA0B,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA;SACzD;KACF;IACD,cAAc;SACT;QACH,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAA;KACrE;AACH,CAAC;AAtDD,kBAsDC;AAED,kBAAe,GAAG,CAAA"}