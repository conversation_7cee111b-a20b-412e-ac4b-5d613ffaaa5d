"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_Kode_XGuard_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/login/route.ts */ \"(rsc)/./app/api/auth/login/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\api\\\\auth\\\\login\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_Kode_XGuard_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/login/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/login/route.ts":
/*!*************************************!*\
  !*** ./app/api/auth/login/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth_simple__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth-simple */ \"(rsc)/./lib/auth-simple.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\n\n\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_2__.string().email(\"Invalid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_2__.string().min(1, \"Password is required\"),\n    rememberMe: zod__WEBPACK_IMPORTED_MODULE_2__.boolean().optional()\n});\nasync function POST(request) {\n    const startTime = Date.now();\n    try {\n        console.log(\"\\uD83D\\uDD10 Login attempt started\");\n        const body = await request.json();\n        console.log(\"Login attempt for email:\", body.email);\n        const validation = loginSchema.safeParse(body);\n        if (!validation.success) {\n            console.log(\"❌ Validation failed:\", validation.error.errors);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid input data\",\n                details: validation.error.errors\n            }, {\n                status: 400\n            });\n        }\n        const { email, password, rememberMe } = validation.data;\n        console.log(\"\\uD83D\\uDD0D Attempting authentication for:\", email);\n        const result = await _lib_auth_simple__WEBPACK_IMPORTED_MODULE_1__.SimpleAuthService.login(email, password);\n        if (!result.success) {\n            console.log(\"❌ Login failed:\", result.message);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: result.message\n            }, {\n                status: 401\n            });\n        }\n        console.log(\"✅ Login successful for user:\", result.user.id);\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Login successful\",\n            data: {\n                user: {\n                    id: result.user.id,\n                    username: result.user.username,\n                    email: result.user.email,\n                    fullName: result.user.full_name,\n                    role: result.user.role,\n                    plan: result.user.plan,\n                    level: result.user.level,\n                    score: result.user.score,\n                    streak: result.user.streak_days,\n                    emailVerified: result.user.email_verified,\n                    lastActive: result.user.last_active,\n                    createdAt: result.user.created_at\n                },\n                tokens: {\n                    accessToken: result.tokens.accessToken,\n                    refreshToken: result.tokens.refreshToken,\n                    expiresIn: result.tokens.expiresIn\n                }\n            }\n        });\n        const cookieOptions = {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"strict\",\n            path: \"/\"\n        };\n        response.cookies.set(\"accessToken\", result.tokens.accessToken, {\n            ...cookieOptions,\n            maxAge: rememberMe ? 7 * 24 * 60 * 60 : 24 * 60 * 60\n        });\n        response.cookies.set(\"refreshToken\", result.tokens.refreshToken, {\n            ...cookieOptions,\n            maxAge: rememberMe ? 30 * 24 * 60 * 60 : 7 * 24 * 60 * 60\n        });\n        response.cookies.set(\"user\", JSON.stringify({\n            id: result.user.id,\n            username: result.user.username,\n            email: result.user.email,\n            role: result.user.role,\n            plan: result.user.plan\n        }), {\n            secure: \"development\" === \"production\",\n            sameSite: \"strict\",\n            path: \"/\",\n            maxAge: rememberMe ? 7 * 24 * 60 * 60 : 24 * 60 * 60\n        });\n        console.log(`✅ Login completed in ${Date.now() - startTime}ms`);\n        return response;\n    } catch (error) {\n        console.error(\"❌ Login error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\",\n            message: \"An unexpected error occurred during login\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function OPTIONS(request) {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth-simple.ts":
/*!****************************!*\
  !*** ./lib/auth-simple.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleAuthService: () => (/* binding */ SimpleAuthService)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key\";\nconst JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || \"your-super-secret-refresh-key\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"1h\";\nconst JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || \"7d\";\nconst BCRYPT_ROUNDS = 12;\n// Mock users for testing\nconst mockUsers = [\n    {\n        id: 1,\n        username: \"admin\",\n        email: \"<EMAIL>\",\n        full_name: \"Administrator\",\n        role: \"admin\",\n        plan: \"Elite\",\n        level: 100,\n        score: 10000,\n        streak_days: 365,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"admin123\"\n    },\n    {\n        id: 2,\n        username: \"testuser\",\n        email: \"<EMAIL>\",\n        full_name: \"Test User\",\n        role: \"user\",\n        plan: \"Free\",\n        level: 1,\n        score: 0,\n        streak_days: 0,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"test123\"\n    }\n];\nclass SimpleAuthService {\n    // Hash password\n    static async hashPassword(password) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, BCRYPT_ROUNDS);\n    }\n    // Verify password\n    static async verifyPassword(password, hash) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hash);\n    }\n    // Generate JWT tokens\n    static generateTokens(user) {\n        const payload = {\n            id: user.id,\n            username: user.username,\n            email: user.email,\n            role: user.role,\n            plan: user.plan\n        };\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n        const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_REFRESH_SECRET, {\n            expiresIn: JWT_REFRESH_EXPIRES_IN\n        });\n        return {\n            accessToken,\n            refreshToken,\n            expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds\n        };\n    }\n    // Verify JWT token\n    static verifyToken(token, isRefreshToken = false) {\n        try {\n            const secret = isRefreshToken ? JWT_REFRESH_SECRET : JWT_SECRET;\n            return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        } catch (error) {\n            return null;\n        }\n    }\n    // Register new user\n    static async register(data) {\n        try {\n            // Check if user already exists\n            const existingUser = mockUsers.find((u)=>u.email === data.email || u.username === data.username);\n            if (existingUser) {\n                return {\n                    success: false,\n                    message: \"User already exists with this email or username\"\n                };\n            }\n            // Create new user\n            const newUser = {\n                id: mockUsers.length + 1,\n                username: data.username,\n                email: data.email,\n                full_name: data.fullName || \"\",\n                role: \"user\",\n                plan: \"Free\",\n                level: 1,\n                score: 0,\n                streak_days: 0,\n                email_verified: false,\n                status: \"active\",\n                last_active: new Date(),\n                created_at: new Date(),\n                password: data.password\n            };\n            // Add to mock users\n            mockUsers.push(newUser);\n            // Remove password from user object\n            const { password, ...userWithoutPassword } = newUser;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                message: \"Registration failed\"\n            };\n        }\n    }\n    // Login user\n    static async login(email, password) {\n        try {\n            // Find user by email\n            const user = mockUsers.find((u)=>u.email === email && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Verify password (simple comparison for testing)\n            if (password !== user.password) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Remove password from user object\n            const { password: userPassword, ...userWithoutPassword } = user;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"Login failed\"\n            };\n        }\n    }\n    // Authenticate request\n    static async authenticateRequest(request) {\n        try {\n            // Get token from Authorization header or cookies\n            let token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n            if (!token) {\n                // Try to get from cookies\n                const cookies = request.headers.get(\"cookie\");\n                if (cookies) {\n                    const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n                    token = tokenMatch ? tokenMatch[1] : undefined;\n                }\n            }\n            if (!token) {\n                return {\n                    success: false,\n                    message: \"No token provided\"\n                };\n            }\n            // Verify token\n            const decoded = this.verifyToken(token);\n            if (!decoded) {\n                return {\n                    success: false,\n                    message: \"Invalid token\"\n                };\n            }\n            // Get user from mock data\n            const user = mockUsers.find((u)=>u.id === decoded.id);\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"User not found\"\n                };\n            }\n            const { password, ...userWithoutPassword } = user;\n            return {\n                success: true,\n                user: userWithoutPassword\n            };\n        } catch (error) {\n            console.error(\"Authentication error:\", error);\n            return {\n                success: false,\n                message: \"Authentication failed\"\n            };\n        }\n    }\n    // Logout user (for mock, just return success)\n    static async logout(userId, _sessionToken) {\n        // In a real implementation, this would remove the session from database\n        console.log(`User ${userId} logged out`);\n    }\n    // Refresh token\n    static async refreshToken(refreshToken) {\n        try {\n            // Verify refresh token\n            const decoded = this.verifyToken(refreshToken, true);\n            if (!decoded) {\n                return {\n                    success: false,\n                    message: \"Invalid refresh token\"\n                };\n            }\n            // Get user data\n            const user = mockUsers.find((u)=>u.id === decoded.id && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"User not found or inactive\"\n                };\n            }\n            const { password, ...userWithoutPassword } = user;\n            // Generate new tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update user last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            return {\n                success: false,\n                message: \"Token refresh failed\"\n            };\n        }\n    }\n    // Get user by ID\n    static async getUserById(userId) {\n        try {\n            const user = mockUsers.find((u)=>u.id === userId);\n            if (!user) return null;\n            const { password, ...userWithoutPassword } = user;\n            return userWithoutPassword;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            return null;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth-simple.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/zod","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/bcryptjs","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();