/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/mysql2/lib sync recursive ^cardinal.*$":
/*!****************************************************!*\
  !*** ./node_modules/mysql2/lib/ sync ^cardinal.*$ ***!
  \****************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/mysql2/lib sync recursive ^cardinal.*$";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "timers":
/*!*************************!*\
  !*** external "timers" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("timers");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:timers/promises":
/*!***************************************!*\
  !*** external "node:timers/promises" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:timers/promises");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_Kode_XGuard_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/login/route.ts */ \"(rsc)/./app/api/auth/login/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\api\\\\auth\\\\login\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_Kode_XGuard_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/login/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/login/route.ts":
/*!*************************************!*\
  !*** ./app/api/auth/login/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth_real__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth-real */ \"(rsc)/./lib/auth-real.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n\n\n\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_2__.string().email(\"Invalid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_2__.string().min(1, \"Password is required\"),\n    rememberMe: zod__WEBPACK_IMPORTED_MODULE_2__.boolean().optional()\n});\nasync function POST(request) {\n    const startTime = Date.now();\n    try {\n        console.log(\"\\uD83D\\uDD10 Login attempt started\");\n        const body = await request.json();\n        console.log(\"Login attempt for email:\", body.email);\n        const validation = loginSchema.safeParse(body);\n        if (!validation.success) {\n            console.log(\"❌ Validation failed:\", validation.error.errors);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid input data\",\n                details: validation.error.errors\n            }, {\n                status: 400\n            });\n        }\n        const { email, password, rememberMe } = validation.data;\n        console.log(\"\\uD83D\\uDD0D Attempting authentication for:\", email);\n        const result = await _lib_auth_real__WEBPACK_IMPORTED_MODULE_1__.RealAuthService.login(email, password);\n        if (!result.success) {\n            console.log(\"❌ Login failed:\", result.message);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: result.message\n            }, {\n                status: 401\n            });\n        }\n        console.log(\"✅ Login successful for user:\", result.user.id);\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Login successful\",\n            user: {\n                id: result.user.id,\n                username: result.user.username,\n                email: result.user.email,\n                fullName: result.user.full_name,\n                role: result.user.role,\n                plan: result.user.plan,\n                level: result.user.level,\n                score: result.user.score,\n                streak: result.user.streak_days,\n                emailVerified: result.user.email_verified,\n                lastActive: result.user.last_active,\n                createdAt: result.user.created_at\n            },\n            tokens: {\n                accessToken: result.tokens.accessToken,\n                refreshToken: result.tokens.refreshToken,\n                expiresIn: result.tokens.expiresIn\n            }\n        });\n        const cookieOptions = {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"strict\",\n            path: \"/\"\n        };\n        response.cookies.set(\"accessToken\", result.tokens.accessToken, {\n            ...cookieOptions,\n            maxAge: rememberMe ? 7 * 24 * 60 * 60 : 24 * 60 * 60\n        });\n        response.cookies.set(\"refreshToken\", result.tokens.refreshToken, {\n            ...cookieOptions,\n            maxAge: rememberMe ? 30 * 24 * 60 * 60 : 7 * 24 * 60 * 60\n        });\n        response.cookies.set(\"user\", JSON.stringify({\n            id: result.user.id,\n            username: result.user.username,\n            email: result.user.email,\n            role: result.user.role,\n            plan: result.user.plan\n        }), {\n            secure: \"development\" === \"production\",\n            sameSite: \"strict\",\n            path: \"/\",\n            maxAge: rememberMe ? 7 * 24 * 60 * 60 : 24 * 60 * 60\n        });\n        console.log(`✅ Login completed in ${Date.now() - startTime}ms`);\n        return response;\n    } catch (error) {\n        console.error(\"❌ Login error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Internal server error\",\n            message: \"An unexpected error occurred during login\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function OPTIONS(request) {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth-real.ts":
/*!**************************!*\
  !*** ./lib/auth-real.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealAuthService: () => (/* binding */ RealAuthService)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./lib/database.ts\");\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key\";\nconst JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || \"your-super-secret-refresh-key\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"1h\";\nconst JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || \"7d\";\nconst BCRYPT_ROUNDS = 12;\nclass RealAuthService {\n    // Hash password\n    static async hashPassword(password) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, BCRYPT_ROUNDS);\n    }\n    // Verify password\n    static async verifyPassword(password, hash) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hash);\n    }\n    // Generate JWT tokens\n    static generateTokens(user) {\n        const payload = {\n            id: user.id,\n            username: user.username,\n            email: user.email,\n            role: user.role,\n            plan: user.plan\n        };\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n        const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_REFRESH_SECRET, {\n            expiresIn: JWT_REFRESH_EXPIRES_IN\n        });\n        return {\n            accessToken,\n            refreshToken,\n            expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds\n        };\n    }\n    // Verify JWT token\n    static verifyToken(token, isRefreshToken = false) {\n        try {\n            const secret = isRefreshToken ? JWT_REFRESH_SECRET : JWT_SECRET;\n            return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        } catch (error) {\n            return null;\n        }\n    }\n    // Register new user\n    static async register(data) {\n        try {\n            // Check if user already exists\n            const existingUsers = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"SELECT id FROM users WHERE email = ? OR username = ?\", [\n                data.email,\n                data.username\n            ]);\n            if (existingUsers.length > 0) {\n                return {\n                    success: false,\n                    message: \"User already exists with this email or username\"\n                };\n            }\n            // Hash password\n            const passwordHash = await this.hashPassword(data.password);\n            // Insert user\n            const result = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(`INSERT INTO users (username, email, password_hash, full_name, role, plan, level, score, streak_days, email_verified, created_at, updated_at, last_active) \n         VALUES (?, ?, ?, ?, 'user', 'Free', 1, 0, 0, false, NOW(), NOW(), NOW())`, [\n                data.username,\n                data.email,\n                passwordHash,\n                data.fullName || \"\"\n            ]);\n            const userId = result.insertId;\n            // Create user preferences\n            await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(`INSERT INTO user_preferences (user_id, created_at, updated_at) \n         VALUES (?, NOW(), NOW())`, [\n                userId\n            ]);\n            // Get created user\n            const users = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"SELECT * FROM users WHERE id = ?\", [\n                userId\n            ]);\n            const user = users[0];\n            // Generate tokens\n            const tokens = this.generateTokens(user);\n            // Save session\n            await this.saveSession(userId, tokens.accessToken, tokens.refreshToken);\n            return {\n                success: true,\n                user,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                message: \"Registration failed\"\n            };\n        }\n    }\n    static async login(emailOrCredentials, password) {\n        try {\n            // Handle both parameter formats\n            let email;\n            let pwd;\n            if (typeof emailOrCredentials === \"string\") {\n                email = emailOrCredentials;\n                pwd = password;\n            } else {\n                email = emailOrCredentials.email;\n                pwd = emailOrCredentials.password;\n            }\n            // Get user by email (optimized query)\n            const users = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query('SELECT id, username, email, full_name, role, plan, level, score, streak_days, email_verified, status, last_active, created_at, password_hash FROM users WHERE email = ? AND (status IS NULL OR status = \"active\") LIMIT 1', [\n                email\n            ]);\n            if (!users || users.length === 0) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            const user = users[0];\n            // Verify password (optimized)\n            const isValidPassword = await this.verifyPassword(pwd, user.password_hash);\n            if (!isValidPassword) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Remove password from user object before any async operations\n            const { password_hash, ...userWithoutPassword } = user;\n            // Generate tokens (synchronous operation)\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Async operations in parallel (non-blocking)\n            const updatePromises = [\n                // Update last active (non-blocking)\n                _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"UPDATE users SET last_active = NOW() WHERE id = ?\", [\n                    user.id\n                ]).catch((err)=>console.warn(\"Failed to update last_active:\", err)),\n                // Save session (non-blocking)\n                this.saveSession(user.id, tokens.accessToken, tokens.refreshToken).catch((err)=>console.warn(\"Failed to save session:\", err))\n            ];\n            // Don't wait for these operations to complete\n            Promise.all(updatePromises).catch(()=>{\n            // Ignore errors, login should still succeed\n            });\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"Login failed\"\n            };\n        }\n    }\n    // Authenticate request\n    static async authenticateRequest(request) {\n        try {\n            // Get token from Authorization header or cookies\n            let token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n            if (!token) {\n                // Try to get from cookies\n                const cookies = request.headers.get(\"cookie\");\n                if (cookies) {\n                    const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n                    token = tokenMatch ? tokenMatch[1] : null;\n                }\n            }\n            if (!token) {\n                return {\n                    success: false,\n                    message: \"No token provided\"\n                };\n            }\n            // Verify token\n            const decoded = this.verifyToken(token);\n            if (!decoded) {\n                return {\n                    success: false,\n                    message: \"Invalid token\"\n                };\n            }\n            // Get user from database\n            const users = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"SELECT * FROM users WHERE id = ?\", [\n                decoded.id\n            ]);\n            if (users.length === 0) {\n                return {\n                    success: false,\n                    message: \"User not found\"\n                };\n            }\n            const user = users[0];\n            // Check if session is valid\n            const sessions = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"SELECT id FROM user_sessions WHERE user_id = ? AND session_token = ? AND expires_at > NOW()\", [\n                user.id,\n                token\n            ]);\n            if (sessions.length === 0) {\n                return {\n                    success: false,\n                    message: \"Session expired\"\n                };\n            }\n            return {\n                success: true,\n                user\n            };\n        } catch (error) {\n            console.error(\"Authentication error:\", error);\n            return {\n                success: false,\n                message: \"Authentication failed\"\n            };\n        }\n    }\n    // Save user session\n    static async saveSession(userId, sessionToken, refreshToken, ipAddress, userAgent) {\n        try {\n            // Generate a UUID for the session\n            const sessionId = crypto.randomUUID();\n            // Convert userId to string if it's a number\n            const userIdStr = userId.toString();\n            await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(`INSERT INTO user_sessions (id, user_id, session_token, refresh_token, ip_address, user_agent, expires_at)\n         VALUES (?, ?, ?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 7 DAY))`, [\n                sessionId,\n                userIdStr,\n                sessionToken,\n                refreshToken,\n                ipAddress || null,\n                userAgent || null\n            ]);\n        } catch (error) {\n            console.error(\"Failed to save session:\", error);\n        // Don't throw error to allow login to continue\n        }\n    }\n    // Logout user\n    static async logout(userId, sessionToken) {\n        await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"DELETE FROM user_sessions WHERE user_id = ? AND session_token = ?\", [\n            userId,\n            sessionToken\n        ]);\n    }\n    // Get user by ID\n    static async getUserById(userId) {\n        try {\n            const users = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"SELECT * FROM users WHERE id = ?\", [\n                userId\n            ]);\n            return users.length > 0 ? users[0] : null;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            return null;\n        }\n    }\n    // Update user last active\n    static async updateLastActive(userId) {\n        await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"UPDATE users SET last_active = NOW() WHERE id = ?\", [\n            userId\n        ]);\n    }\n    // Change password\n    static async changePassword(userId, currentPassword, newPassword) {\n        try {\n            // Get user\n            const users = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"SELECT password_hash FROM users WHERE id = ?\", [\n                userId\n            ]);\n            if (users.length === 0) {\n                return {\n                    success: false,\n                    message: \"User not found\"\n                };\n            }\n            const user = users[0];\n            // Verify current password\n            const isValidPassword = await this.verifyPassword(currentPassword, user.password_hash);\n            if (!isValidPassword) {\n                return {\n                    success: false,\n                    message: \"Current password is incorrect\"\n                };\n            }\n            // Hash new password\n            const newPasswordHash = await this.hashPassword(newPassword);\n            // Update password\n            await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?\", [\n                newPasswordHash,\n                userId\n            ]);\n            // Revoke all sessions\n            await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"DELETE FROM user_sessions WHERE user_id = ?\", [\n                userId\n            ]);\n            return {\n                success: true,\n                message: \"Password changed successfully\"\n            };\n        } catch (error) {\n            console.error(\"Change password error:\", error);\n            return {\n                success: false,\n                message: \"Failed to change password\"\n            };\n        }\n    }\n    // Clean expired sessions\n    static async cleanExpiredSessions() {\n        await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"DELETE FROM user_sessions WHERE expires_at < NOW()\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth-real.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database-mock.ts":
/*!******************************!*\
  !*** ./lib/database-mock.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MockDatabase: () => (/* binding */ MockDatabase),\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n// Mock database implementation for testing without MySQL\n// This provides realistic data for all features to work\n// Mock data\nconst mockUsers = [\n    {\n        id: 1,\n        username: \"cyberwarrior\",\n        email: \"<EMAIL>\",\n        full_name: \"Alex Chen\",\n        role: \"user\",\n        plan: \"Pro\",\n        level: 15,\n        score: 2500,\n        streak_days: 7,\n        email_verified: true,\n        last_active: new Date(),\n        created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)\n    },\n    {\n        id: 2,\n        username: \"admin\",\n        email: \"<EMAIL>\",\n        full_name: \"System Administrator\",\n        role: \"admin\",\n        plan: \"Elite\",\n        level: 100,\n        score: 50000,\n        streak_days: 365,\n        email_verified: true,\n        last_active: new Date(),\n        created_at: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)\n    },\n    {\n        id: 3,\n        username: \"securityexpert\",\n        email: \"<EMAIL>\",\n        full_name: \"Sarah Johnson\",\n        role: \"user\",\n        plan: \"Expert\",\n        level: 28,\n        score: 8950,\n        streak_days: 12,\n        email_verified: true,\n        last_active: new Date(Date.now() - 2 * 60 * 60 * 1000),\n        created_at: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)\n    }\n];\nconst mockScans = [\n    {\n        id: 1,\n        user_id: 1,\n        target_url: \"https://example.com\",\n        scan_type: \"comprehensive\",\n        status: \"completed\",\n        vulnerabilities_found: 15,\n        severity_critical: 2,\n        severity_high: 5,\n        severity_medium: 6,\n        severity_low: 2,\n        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 1 * 60 * 60 * 1000)\n    },\n    {\n        id: 2,\n        user_id: 1,\n        target_url: \"https://testsite.org\",\n        scan_type: \"advanced\",\n        status: \"completed\",\n        vulnerabilities_found: 8,\n        severity_critical: 0,\n        severity_high: 2,\n        severity_medium: 4,\n        severity_low: 2,\n        created_at: new Date(Date.now() - 24 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 23 * 60 * 60 * 1000)\n    },\n    {\n        id: 3,\n        user_id: 2,\n        target_url: \"https://vulnerable-app.com\",\n        scan_type: \"comprehensive\",\n        status: \"completed\",\n        vulnerabilities_found: 23,\n        severity_critical: 5,\n        severity_high: 8,\n        severity_medium: 7,\n        severity_low: 3,\n        created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000)\n    }\n];\nconst mockOSINTQueries = [\n    {\n        id: 1,\n        user_id: 1,\n        query_type: \"email\",\n        query_value: \"<EMAIL>\",\n        status: \"completed\",\n        results: {\n            breaches: [\n                \"Adobe\",\n                \"LinkedIn\"\n            ],\n            found: true\n        },\n        created_at: new Date(Date.now() - 30 * 60 * 1000),\n        completed_at: new Date(Date.now() - 25 * 60 * 1000)\n    },\n    {\n        id: 2,\n        user_id: 1,\n        query_type: \"domain\",\n        query_value: \"example.com\",\n        status: \"completed\",\n        results: {\n            subdomains: [\n                \"www\",\n                \"api\",\n                \"admin\"\n            ],\n            found: true\n        },\n        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 2 * 60 * 60 * 1000 + 5 * 60 * 1000)\n    }\n];\nconst mockFileAnalyses = [\n    {\n        id: 1,\n        user_id: 1,\n        filename: \"suspicious.exe\",\n        file_size: 2048576,\n        threat_detected: true,\n        threat_type: \"malware\",\n        status: \"completed\",\n        created_at: new Date(Date.now() - 45 * 60 * 1000),\n        completed_at: new Date(Date.now() - 40 * 60 * 1000)\n    },\n    {\n        id: 2,\n        user_id: 1,\n        filename: \"clean-file.pdf\",\n        file_size: 1024000,\n        threat_detected: false,\n        status: \"completed\",\n        created_at: new Date(Date.now() - 3 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 3 * 60 * 60 * 1000 + 2 * 60 * 1000)\n    }\n];\n// Mock database class\nclass MockDatabase {\n    static async query(sql, params = []) {\n        console.log(`Mock DB Query: ${sql}`, params);\n        // Simulate database delay\n        await new Promise((resolve)=>setTimeout(resolve, 100));\n        // Parse SQL and return appropriate mock data\n        if (sql.includes(\"SELECT * FROM users WHERE id = ?\")) {\n            const userId = params[0];\n            return mockUsers.filter((u)=>u.id === userId);\n        }\n        if (sql.includes(\"SELECT COUNT(*) as count FROM users\")) {\n            return [\n                {\n                    count: mockUsers.length\n                }\n            ];\n        }\n        if (sql.includes(\"SELECT COUNT(*) as count FROM vulnerability_scans\")) {\n            return [\n                {\n                    count: mockScans.length\n                }\n            ];\n        }\n        if (sql.includes(\"SELECT * FROM vulnerability_scans WHERE user_id = ?\")) {\n            const userId = params[0];\n            return mockScans.filter((s)=>s.user_id === userId);\n        }\n        if (sql.includes(\"SELECT * FROM osint_queries WHERE user_id = ?\")) {\n            const userId = params[0];\n            return mockOSINTQueries.filter((q)=>q.user_id === userId);\n        }\n        if (sql.includes(\"SELECT * FROM file_analyses WHERE user_id = ?\")) {\n            const userId = params[0];\n            return mockFileAnalyses.filter((f)=>f.user_id === userId);\n        }\n        if (sql.includes(\"INSERT INTO\")) {\n            // Return mock insert result\n            return {\n                insertId: Math.floor(Math.random() * 1000) + 100\n            };\n        }\n        if (sql.includes(\"UPDATE\")) {\n            // Return mock update result\n            return {\n                affectedRows: 1\n            };\n        }\n        // Default return for other queries\n        return [];\n    }\n}\n// Export mock database as default db\nconst db = MockDatabase;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database-mock.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseUtils: () => (/* binding */ DatabaseUtils),\n/* harmony export */   RedisUtils: () => (/* binding */ RedisUtils),\n/* harmony export */   closeDatabaseConnections: () => (/* binding */ closeDatabaseConnections),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   elasticsearch: () => (/* binding */ elasticsearch),\n/* harmony export */   initRedis: () => (/* binding */ initRedis),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   initializeDatabases: () => (/* binding */ initializeDatabases),\n/* harmony export */   redis: () => (/* binding */ redis),\n/* harmony export */   testDatabaseConnection: () => (/* binding */ testDatabaseConnection),\n/* harmony export */   testElasticsearchConnection: () => (/* binding */ testElasticsearchConnection)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"(rsc)/./node_modules/mysql2/promise.js\");\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! redis */ \"(rsc)/./node_modules/redis/dist/index.js\");\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(redis__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @elastic/elasticsearch */ \"(rsc)/./node_modules/@elastic/elasticsearch/index.js\");\n/* harmony import */ var _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _database_mock__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./database-mock */ \"(rsc)/./lib/database-mock.ts\");\n\n\n\n\n\n// MySQL Database Configuration\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"rootkan\",\n    database: process.env.DB_NAME || \"db_kodexguard\",\n    charset: \"utf8mb4\",\n    timezone: \"+00:00\",\n    // Pool configuration (valid options for mysql2)\n    connectionLimit: 10,\n    queueLimit: 0,\n    // Connection configuration\n    connectTimeout: 60000,\n    // Additional MySQL2 options\n    multipleStatements: false,\n    namedPlaceholders: false\n};\n// Create MySQL connection pool\nconst mysqlPool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool(dbConfig);\n// For demo purposes, use mock database if MySQL is not available\n\nconst db = {\n    async query (sql, params = []) {\n        try {\n            // Use MySQL database\n            const [rows] = await mysqlPool.execute(sql, params);\n            return rows;\n        } catch (error) {\n            console.error(\"Database query error:\", error);\n            // For demo purposes, fallback to mock database\n            console.log(\"Falling back to mock database\");\n            return await _database_mock__WEBPACK_IMPORTED_MODULE_5__.MockDatabase.query(sql, params);\n        }\n    },\n    async close () {\n        await mysqlPool.end();\n    }\n};\n// Initialize database with schema\nasync function initializeDatabase() {\n    try {\n        console.log(\"\\uD83D\\uDDC4️ Initializing database schema...\");\n        // Read schema file\n        const schemaPath = (0,path__WEBPACK_IMPORTED_MODULE_4__.join)(process.cwd(), \"lib\", \"database\", \"schema.sql\");\n        const schema = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_3__.readFile)(schemaPath, \"utf-8\");\n        // Split schema into individual statements\n        const statements = schema.split(\";\").map((stmt)=>stmt.trim()).filter((stmt)=>stmt.length > 0 && !stmt.startsWith(\"--\"));\n        // Execute each statement\n        for (const statement of statements){\n            try {\n                await db.query(statement);\n            } catch (error) {\n                // Ignore \"table already exists\" errors\n                if (!error.message?.includes(\"already exists\")) {\n                    console.error(\"Schema error:\", error.message || error);\n                }\n            }\n        }\n        console.log(\"✅ Database schema initialized successfully\");\n        return true;\n    } catch (error) {\n        console.error(\"❌ Database schema initialization failed:\", error);\n        return false;\n    }\n}\n// Test database connection\nasync function testDatabaseConnection() {\n    try {\n        const connection = await mysqlPool.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"✅ MySQL database connected successfully\");\n        // Skip schema initialization to avoid foreign key constraint errors\n        // Database schema should be managed through migrations\n        // await initializeDatabase()\n        return true;\n    } catch (error) {\n        console.error(\"❌ MySQL database connection failed, using mock database\");\n        return true // Return true for mock database\n        ;\n    }\n}\n// Redis Configuration\nconst redisConfig = {\n    url: process.env.REDIS_URL || \"redis://localhost:6379\",\n    password: process.env.REDIS_PASSWORD || undefined,\n    socket: {\n        reconnectStrategy: (retries)=>Math.min(retries * 50, 500)\n    }\n};\n// Create Redis client\nconst redis = (0,redis__WEBPACK_IMPORTED_MODULE_1__.createClient)(redisConfig);\n// Redis connection handlers\nredis.on(\"error\", (err)=>{\n    console.error(\"❌ Redis Client Error:\", err);\n});\nredis.on(\"connect\", ()=>{\n    console.log(\"✅ Redis connected successfully\");\n});\nredis.on(\"reconnecting\", ()=>{\n    console.log(\"\\uD83D\\uDD04 Redis reconnecting...\");\n});\nredis.on(\"ready\", ()=>{\n    console.log(\"✅ Redis ready for operations\");\n});\n// Initialize Redis connection\nasync function initRedis() {\n    try {\n        if (!redis.isOpen) {\n            await redis.connect();\n        }\n        return true;\n    } catch (error) {\n        console.error(\"❌ Redis connection failed:\", error);\n        return false;\n    }\n}\n// Elasticsearch Configuration\nconst elasticsearchConfig = {\n    node: process.env.ELASTICSEARCH_URL || \"http://localhost:9200\",\n    auth: process.env.ELASTICSEARCH_USERNAME && process.env.ELASTICSEARCH_PASSWORD ? {\n        username: process.env.ELASTICSEARCH_USERNAME,\n        password: process.env.ELASTICSEARCH_PASSWORD\n    } : undefined,\n    requestTimeout: 30000,\n    pingTimeout: 3000,\n    maxRetries: 3\n};\n// Create Elasticsearch client\nconst elasticsearch = new _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__.Client(elasticsearchConfig);\n// Test Elasticsearch connection\nasync function testElasticsearchConnection() {\n    try {\n        const health = await elasticsearch.cluster.health();\n        console.log(\"✅ Elasticsearch connected successfully:\", health.cluster_name);\n        return true;\n    } catch (error) {\n        console.error(\"❌ Elasticsearch connection failed:\", error);\n        return false;\n    }\n}\n// Initialize all database connections\nasync function initializeDatabases() {\n    console.log(\"\\uD83D\\uDE80 Initializing database connections...\");\n    const results = {\n        mysql: await testDatabaseConnection(),\n        redis: await initRedis(),\n        elasticsearch: await testElasticsearchConnection()\n    };\n    const allConnected = Object.values(results).every(Boolean);\n    if (allConnected) {\n        console.log(\"✅ All databases connected successfully\");\n    } else {\n        console.warn(\"⚠️ Some database connections failed:\", results);\n    }\n    return results;\n}\n// Graceful shutdown\nasync function closeDatabaseConnections() {\n    console.log(\"\\uD83D\\uDD04 Closing database connections...\");\n    try {\n        await mysqlPool.end();\n        console.log(\"✅ MySQL connection closed\");\n    } catch (error) {\n        console.error(\"❌ Error closing MySQL connection:\", error);\n    }\n    try {\n        if (redis.isOpen) {\n            await redis.quit();\n        }\n        console.log(\"✅ Redis connection closed\");\n    } catch (error) {\n        console.error(\"❌ Error closing Redis connection:\", error);\n    }\n    try {\n        await elasticsearch.close();\n        console.log(\"✅ Elasticsearch connection closed\");\n    } catch (error) {\n        console.error(\"❌ Error closing Elasticsearch connection:\", error);\n    }\n}\n// Database utility functions\nclass DatabaseUtils {\n    // Execute query with error handling\n    static async query(sql, params) {\n        try {\n            return await db.query(sql, params || []);\n        } catch (error) {\n            console.error(\"Database query error:\", error);\n            throw error;\n        }\n    }\n    // Get single record\n    static async findOne(sql, params) {\n        const rows = await this.query(sql, params);\n        return Array.isArray(rows) && rows.length > 0 ? rows[0] : null;\n    }\n    // Get multiple records with pagination\n    static async findMany(sql, params, page = 1, limit = 10) {\n        const offset = (page - 1) * limit;\n        // Get total count\n        const countSql = sql.replace(/SELECT .+ FROM/, \"SELECT COUNT(*) as total FROM\");\n        const countResult = await this.findOne(countSql, params);\n        const total = countResult?.total || 0;\n        // Get paginated data\n        const dataSql = `${sql} LIMIT ${limit} OFFSET ${offset}`;\n        const data = await this.query(dataSql, params);\n        return {\n            data: Array.isArray(data) ? data : [],\n            total,\n            page,\n            limit\n        };\n    }\n    // Insert record\n    static async insert(table, data) {\n        const fields = Object.keys(data).join(\", \");\n        const placeholders = Object.keys(data).map(()=>\"?\").join(\", \");\n        const values = Object.values(data);\n        const sql = `INSERT INTO ${table} (${fields}) VALUES (${placeholders})`;\n        const result = await this.query(sql, values);\n        return result.insertId || data.id;\n    }\n    // Update record\n    static async update(table, data, where, whereParams) {\n        const setClause = Object.keys(data).map((key)=>`${key} = ?`).join(\", \");\n        const values = [\n            ...Object.values(data),\n            ...whereParams || []\n        ];\n        const sql = `UPDATE ${table} SET ${setClause} WHERE ${where}`;\n        const result = await this.query(sql, values);\n        return result.affectedRows > 0;\n    }\n    // Delete record\n    static async delete(table, where, whereParams) {\n        const sql = `DELETE FROM ${table} WHERE ${where}`;\n        const result = await this.query(sql, whereParams);\n        return result.affectedRows > 0;\n    }\n}\n// Redis utility functions\nclass RedisUtils {\n    // Set value with expiration\n    static async set(key, value, expireInSeconds) {\n        try {\n            const serializedValue = typeof value === \"string\" ? value : JSON.stringify(value);\n            if (expireInSeconds) {\n                await redis.setEx(key, expireInSeconds, serializedValue);\n            } else {\n                await redis.set(key, serializedValue);\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Redis set error:\", error);\n            return false;\n        }\n    }\n    // Get value\n    static async get(key) {\n        try {\n            const value = await redis.get(key);\n            if (!value) return null;\n            try {\n                return JSON.parse(value);\n            } catch  {\n                return value;\n            }\n        } catch (error) {\n            console.error(\"Redis get error:\", error);\n            return null;\n        }\n    }\n    // Delete key\n    static async del(key) {\n        try {\n            const result = await redis.del(key);\n            return result > 0;\n        } catch (error) {\n            console.error(\"Redis delete error:\", error);\n            return false;\n        }\n    }\n    // Check if key exists\n    static async exists(key) {\n        try {\n            const result = await redis.exists(key);\n            return result === 1;\n        } catch (error) {\n            console.error(\"Redis exists error:\", error);\n            return false;\n        }\n    }\n    // Increment counter\n    static async incr(key, expireInSeconds) {\n        try {\n            const result = await redis.incr(key);\n            if (expireInSeconds && result === 1) {\n                await redis.expire(key, expireInSeconds);\n            }\n            return result;\n        } catch (error) {\n            console.error(\"Redis increment error:\", error);\n            return 0;\n        }\n    }\n}\n// Export default database instance\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (db);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@redis","vendor-chunks/apache-arrow","vendor-chunks/@elastic","vendor-chunks/undici","vendor-chunks/mysql2","vendor-chunks/@opentelemetry","vendor-chunks/semver","vendor-chunks/next","vendor-chunks/iconv-lite","vendor-chunks/jsonwebtoken","vendor-chunks/zod","vendor-chunks/flatbuffers","vendor-chunks/jws","vendor-chunks/debug","vendor-chunks/aws-ssl-profiles","vendor-chunks/sqlstring","vendor-chunks/seq-queue","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/bcryptjs","vendor-chunks/tslib","vendor-chunks/secure-json-parse","vendor-chunks/lru-cache","vendor-chunks/long","vendor-chunks/supports-color","vendor-chunks/safer-buffer","vendor-chunks/safe-buffer","vendor-chunks/redis","vendor-chunks/named-placeholders","vendor-chunks/ms","vendor-chunks/lru.min","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/is-property","vendor-chunks/hpagent","vendor-chunks/has-flag","vendor-chunks/generate-function","vendor-chunks/denque","vendor-chunks/cluster-key-slot","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();