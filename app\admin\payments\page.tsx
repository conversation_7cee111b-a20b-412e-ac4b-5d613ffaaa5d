'use client'

import { useState, useEffect } from 'react'
import AdminLayout from '@/components/AdminLayout'
import { Card, StatsCard } from '@/components/Card'
import { DataTable } from '@/components/Table'
import { Modal } from '@/components/Modal'
import { Loading } from '@/components/Loading'
import { useToast } from '@/components/Toast'
import { 
  DollarSign,
  CreditCard,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  RefreshCw,
  Eye,
  Download,
  Filter,
  Calendar,
  TrendingUp,
  Users,
  Banknote
} from 'lucide-react'

interface Payment {
  id: string
  userId: number
  userName: string
  userEmail: string
  planId: string
  planName: string
  amount: number
  currency: string
  gateway: 'tripay' | 'midtrans' | 'xendit' | 'manual'
  status: 'pending' | 'paid' | 'failed' | 'expired' | 'cancelled'
  gatewayPaymentId?: string
  paymentUrl?: string
  createdAt: string
  paidAt?: string
  expiresAt?: string
}

interface PaymentStats {
  totalRevenue: number
  todayRevenue: number
  pendingPayments: number
  successfulPayments: number
}

export default function AdminPaymentsPage() {
  const [payments, setPayments] = useState<Payment[]>([])
  const [stats, setStats] = useState<PaymentStats>({
    totalRevenue: 0,
    todayRevenue: 0,
    pendingPayments: 0,
    successfulPayments: 0
  })
  const [loading, setLoading] = useState(true)
  const [showDetailsModal, setShowDetailsModal] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null)
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [gatewayFilter, setGatewayFilter] = useState<string>('all')
  const { showToast } = useToast()

  useEffect(() => {
    loadPayments()
  }, [statusFilter, gatewayFilter])

  const loadPayments = async () => {
    try {
      const params = new URLSearchParams()
      if (statusFilter !== 'all') params.append('status', statusFilter)
      if (gatewayFilter !== 'all') params.append('gateway', gatewayFilter)

      const response = await fetch(`/api/admin/payments?${params.toString()}`)
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setPayments(data.data.payments)
          setStats(data.data.stats)
        }
      }
    } catch (error) {
      console.error('Failed to load payments:', error)
      showToast('Failed to load payments', 'error')
    } finally {
      setLoading(false)
    }
  }

  const handleViewDetails = (payment: Payment) => {
    setSelectedPayment(payment)
    setShowDetailsModal(true)
  }

  const handleApproveManualPayment = async (paymentId: string) => {
    try {
      const response = await fetch(`/api/admin/payments/${paymentId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()
      
      if (data.success) {
        showToast('Payment approved successfully', 'success')
        await loadPayments()
      } else {
        showToast(data.message || 'Failed to approve payment', 'error')
      }
    } catch (error) {
      console.error('Approve payment error:', error)
      showToast('Failed to approve payment', 'error')
    }
  }

  const handleRejectPayment = async (paymentId: string) => {
    try {
      const response = await fetch(`/api/admin/payments/${paymentId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()
      
      if (data.success) {
        showToast('Payment rejected', 'success')
        await loadPayments()
      } else {
        showToast(data.message || 'Failed to reject payment', 'error')
      }
    } catch (error) {
      console.error('Reject payment error:', error)
      showToast('Failed to reject payment', 'error')
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'expired':
        return <AlertCircle className="h-4 w-4 text-gray-500" />
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-gray-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'text-green-500'
      case 'pending':
        return 'text-yellow-500'
      case 'failed':
        return 'text-red-500'
      case 'expired':
        return 'text-gray-500'
      case 'cancelled':
        return 'text-gray-500'
      default:
        return 'text-gray-500'
    }
  }

  const formatCurrency = (amount: number, currency: string) => {
    if (currency === 'IDR') {
      return `Rp ${amount.toLocaleString('id-ID')}`
    }
    return `${currency} ${amount.toLocaleString()}`
  }

  const columns = [
    {
      key: 'id',
      label: 'Payment ID',
      render: (value: string) => (
        <span className="font-mono text-sm">{value.substring(0, 12)}...</span>
      )
    },
    {
      key: 'userName',
      label: 'User',
      render: (value: string, row: Payment) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-sm text-gray-500">{row.userEmail}</div>
        </div>
      )
    },
    {
      key: 'planName',
      label: 'Plan',
      render: (value: string) => (
        <span className="font-medium">{value}</span>
      )
    },
    {
      key: 'amount',
      label: 'Amount',
      render: (value: number, row: Payment) => (
        <div className="flex items-center space-x-1">
          <DollarSign className="h-4 w-4 text-green-500" />
          <span className="font-medium">{formatCurrency(value, row.currency)}</span>
        </div>
      )
    },
    {
      key: 'gateway',
      label: 'Gateway',
      render: (value: string) => (
        <span className="capitalize px-2 py-1 bg-gray-100 rounded text-sm">{value}</span>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: string) => (
        <div className="flex items-center space-x-2">
          {getStatusIcon(value)}
          <span className={`capitalize ${getStatusColor(value)}`}>{value}</span>
        </div>
      )
    },
    {
      key: 'createdAt',
      label: 'Created',
      render: (value: string) => new Date(value).toLocaleDateString()
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, row: Payment) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleViewDetails(row)}
            className="p-1 text-blue-600 hover:text-blue-800"
            title="View Details"
          >
            <Eye className="h-4 w-4" />
          </button>
          {row.status === 'pending' && row.gateway === 'manual' && (
            <>
              <button
                onClick={() => handleApproveManualPayment(row.id)}
                className="p-1 text-green-600 hover:text-green-800"
                title="Approve Payment"
              >
                <CheckCircle className="h-4 w-4" />
              </button>
              <button
                onClick={() => handleRejectPayment(row.id)}
                className="p-1 text-red-600 hover:text-red-800"
                title="Reject Payment"
              >
                <XCircle className="h-4 w-4" />
              </button>
            </>
          )}
        </div>
      )
    }
  ]

  if (loading) {
    return (
      <AdminLayout>
        <Loading />
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Payment Management</h1>
            <p className="text-gray-600">Monitor and manage subscription payments</p>
          </div>
          <div className="flex items-center space-x-3">
            <button className="btn-secondary flex items-center space-x-2">
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
            <button
              onClick={loadPayments}
              className="btn-primary flex items-center space-x-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <StatsCard
            title="Total Revenue"
            value={formatCurrency(stats.totalRevenue, 'IDR')}
            icon={TrendingUp}
            color="green"
          />
          <StatsCard
            title="Today's Revenue"
            value={formatCurrency(stats.todayRevenue, 'IDR')}
            icon={Calendar}
            color="blue"
          />
          <StatsCard
            title="Pending Payments"
            value={stats.pendingPayments.toString()}
            icon={Clock}
            color="yellow"
          />
          <StatsCard
            title="Successful Payments"
            value={stats.successfulPayments.toString()}
            icon={CheckCircle}
            color="green"
          />
        </div>

        {/* Filters */}
        <Card>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Filters:</span>
            </div>
            
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="paid">Paid</option>
              <option value="failed">Failed</option>
              <option value="expired">Expired</option>
              <option value="cancelled">Cancelled</option>
            </select>
            
            <select
              value={gatewayFilter}
              onChange={(e) => setGatewayFilter(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm"
            >
              <option value="all">All Gateways</option>
              <option value="tripay">Tripay</option>
              <option value="midtrans">Midtrans</option>
              <option value="xendit">Xendit</option>
              <option value="manual">Manual</option>
            </select>
          </div>
        </Card>

        {/* Payments Table */}
        <Card>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Payment History</h2>
            <div className="text-sm text-gray-500">
              Showing {payments.length} payments
            </div>
          </div>
          
          <DataTable
            columns={columns}
            data={payments}
            searchable={true}
            emptyMessage="No payments found"
          />
        </Card>

        {/* Payment Details Modal */}
        <Modal
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          title="Payment Details"
        >
          {selectedPayment && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Payment ID</label>
                  <p className="font-mono text-sm">{selectedPayment.id}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(selectedPayment.status)}
                    <span className={`capitalize ${getStatusColor(selectedPayment.status)}`}>
                      {selectedPayment.status}
                    </span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">User</label>
                  <p>{selectedPayment.userName}</p>
                  <p className="text-sm text-gray-500">{selectedPayment.userEmail}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Plan</label>
                  <p>{selectedPayment.planName}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Amount</label>
                  <p className="font-medium">{formatCurrency(selectedPayment.amount, selectedPayment.currency)}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Gateway</label>
                  <p className="capitalize">{selectedPayment.gateway}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Created At</label>
                  <p>{new Date(selectedPayment.createdAt).toLocaleString()}</p>
                </div>
                {selectedPayment.paidAt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Paid At</label>
                    <p>{new Date(selectedPayment.paidAt).toLocaleString()}</p>
                  </div>
                )}
              </div>
              
              {selectedPayment.paymentUrl && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Payment URL</label>
                  <a 
                    href={selectedPayment.paymentUrl} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline text-sm break-all"
                  >
                    {selectedPayment.paymentUrl}
                  </a>
                </div>
              )}
              
              {selectedPayment.status === 'pending' && selectedPayment.gateway === 'manual' && (
                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <button
                    onClick={() => handleRejectPayment(selectedPayment.id)}
                    className="btn-danger flex items-center space-x-2"
                  >
                    <XCircle className="h-4 w-4" />
                    <span>Reject</span>
                  </button>
                  <button
                    onClick={() => handleApproveManualPayment(selectedPayment.id)}
                    className="btn-primary flex items-center space-x-2"
                  >
                    <CheckCircle className="h-4 w-4" />
                    <span>Approve</span>
                  </button>
                </div>
              )}
            </div>
          )}
        </Modal>
      </div>
    </AdminLayout>
  )
}
