{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/prepare-destination.ts"], "names": ["compile", "pathToRegexp", "escapeStringRegexp", "parseUrl", "INTERCEPTION_ROUTE_MARKERS", "isInterceptionRouteAppPath", "NEXT_RSC_UNION_QUERY", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getSafeParamName", "paramName", "newParamName", "i", "length", "charCode", "charCodeAt", "escapeSegment", "str", "segmentName", "replace", "RegExp", "unescapeSegments", "matchHas", "req", "query", "has", "missing", "params", "hasMatch", "hasItem", "value", "key", "type", "toLowerCase", "headers", "cookies", "host", "hostname", "split", "matcher", "matches", "Array", "isArray", "slice", "match", "groups", "Object", "keys", "for<PERSON>ach", "groupKey", "allMatch", "every", "item", "some", "compileNonPath", "includes", "validate", "prepareDestination", "args", "assign", "__next<PERSON><PERSON><PERSON>", "__nextDefaultLocale", "__nextDataReq", "__nextInferredLocaleFromDefault", "escapedDestination", "destination", "param", "parsedDestination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "destPath", "pathname", "hash", "destHostname", "destPathPara<PERSON><PERSON><PERSON>s", "destHostnameParamKeys", "destParams", "push", "name", "destPathCompiler", "destHostnameCompiler", "strOrArray", "entries", "map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "appendParamsToQuery", "newUrl", "segment", "marker", "find", "m", "startsWith", "search", "err", "message", "Error"], "mappings": "AAOA,SAASA,OAAO,EAAEC,YAAY,QAAQ,oCAAmC;AACzE,SAASC,kBAAkB,QAAQ,sBAAqB;AACxD,SAASC,QAAQ,QAAQ,cAAa;AACtC,SACEC,0BAA0B,EAC1BC,0BAA0B,QACrB,wDAAuD;AAC9D,SAASC,oBAAoB,QAAQ,mDAAkD;AACvF,SAASC,eAAe,QAAQ,iDAAgD;AAEhF;;;CAGC,GACD,SAASC,iBAAiBC,SAAiB;IACzC,IAAIC,eAAe;IAEnB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,UAAUG,MAAM,EAAED,IAAK;QACzC,MAAME,WAAWJ,UAAUK,UAAU,CAACH;QAEtC,IACE,AAACE,WAAW,MAAMA,WAAW,MAAO,MAAM;QACzCA,WAAW,MAAMA,WAAW,IAAK,MAAM;UACxC;YACAH,gBAAgBD,SAAS,CAACE,EAAE;QAC9B;IACF;IACA,OAAOD;AACT;AAEA,SAASK,cAAcC,GAAW,EAAEC,WAAmB;IACrD,OAAOD,IAAIE,OAAO,CAChB,IAAIC,OAAO,AAAC,MAAGjB,mBAAmBe,cAAgB,MAClD,AAAC,iBAAcA;AAEnB;AAEA,SAASG,iBAAiBJ,GAAW;IACnC,OAAOA,IAAIE,OAAO,CAAC,kBAAkB;AACvC;AAEA,OAAO,SAASG,SACdC,GAAsC,EACtCC,KAAa,EACbC,GAAoB,EACpBC,OAAwB;IADxBD,IAAAA,gBAAAA,MAAkB,EAAE;IACpBC,IAAAA,oBAAAA,UAAsB,EAAE;IAExB,MAAMC,SAAiB,CAAC;IAExB,MAAMC,WAAW,CAACC;QAChB,IAAIC;QACJ,IAAIC,MAAMF,QAAQE,GAAG;QAErB,OAAQF,QAAQG,IAAI;YAClB,KAAK;gBAAU;oBACbD,MAAMA,IAAKE,WAAW;oBACtBH,QAAQP,IAAIW,OAAO,CAACH,IAAI;oBACxB;gBACF;YACA,KAAK;gBAAU;oBACb,IAAI,aAAaR,KAAK;wBACpBO,QAAQP,IAAIY,OAAO,CAACN,QAAQE,GAAG,CAAC;oBAClC,OAAO;wBACL,MAAMI,UAAU3B,gBAAgBe,IAAIW,OAAO;wBAC3CJ,QAAQK,OAAO,CAACN,QAAQE,GAAG,CAAC;oBAC9B;oBAEA;gBACF;YACA,KAAK;gBAAS;oBACZD,QAAQN,KAAK,CAACO,IAAK;oBACnB;gBACF;YACA,KAAK;gBAAQ;oBACX,MAAM,EAAEK,IAAI,EAAE,GAAGb,CAAAA,uBAAAA,IAAKW,OAAO,KAAI,CAAC;oBAClC,mCAAmC;oBACnC,MAAMG,WAAWD,wBAAAA,KAAME,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACL,WAAW;oBACnDH,QAAQO;oBACR;gBACF;YACA;gBAAS;oBACP;gBACF;QACF;QAEA,IAAI,CAACR,QAAQC,KAAK,IAAIA,OAAO;YAC3BH,MAAM,CAAClB,iBAAiBsB,KAAM,GAAGD;YACjC,OAAO;QACT,OAAO,IAAIA,OAAO;YAChB,MAAMS,UAAU,IAAInB,OAAO,AAAC,MAAGS,QAAQC,KAAK,GAAC;YAC7C,MAAMU,UAAUC,MAAMC,OAAO,CAACZ,SAC1BA,MAAMa,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAACC,KAAK,CAACL,WACzBT,MAAMc,KAAK,CAACL;YAEhB,IAAIC,SAAS;gBACX,IAAIC,MAAMC,OAAO,CAACF,UAAU;oBAC1B,IAAIA,QAAQK,MAAM,EAAE;wBAClBC,OAAOC,IAAI,CAACP,QAAQK,MAAM,EAAEG,OAAO,CAAC,CAACC;4BACnCtB,MAAM,CAACsB,SAAS,GAAGT,QAAQK,MAAM,AAAC,CAACI,SAAS;wBAC9C;oBACF,OAAO,IAAIpB,QAAQG,IAAI,KAAK,UAAUQ,OAAO,CAAC,EAAE,EAAE;wBAChDb,OAAOS,IAAI,GAAGI,OAAO,CAAC,EAAE;oBAC1B;gBACF;gBACA,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,MAAMU,WACJzB,IAAI0B,KAAK,CAAC,CAACC,OAASxB,SAASwB,UAC7B,CAAC1B,QAAQ2B,IAAI,CAAC,CAACD,OAASxB,SAASwB;IAEnC,IAAIF,UAAU;QACZ,OAAOvB;IACT;IACA,OAAO;AACT;AAEA,OAAO,SAAS2B,eAAexB,KAAa,EAAEH,MAAc;IAC1D,IAAI,CAACG,MAAMyB,QAAQ,CAAC,MAAM;QACxB,OAAOzB;IACT;IAEA,KAAK,MAAMC,OAAOe,OAAOC,IAAI,CAACpB,QAAS;QACrC,IAAIG,MAAMyB,QAAQ,CAAC,AAAC,MAAGxB,MAAQ;YAC7BD,QAAQA,MACLX,OAAO,CACN,IAAIC,OAAO,AAAC,MAAGW,MAAI,OAAM,MACzB,AAAC,MAAGA,MAAI,6BAETZ,OAAO,CACN,IAAIC,OAAO,AAAC,MAAGW,MAAI,OAAM,MACzB,AAAC,MAAGA,MAAI,4BAETZ,OAAO,CAAC,IAAIC,OAAO,AAAC,MAAGW,MAAI,OAAM,MAAM,AAAC,MAAGA,MAAI,wBAC/CZ,OAAO,CACN,IAAIC,OAAO,AAAC,MAAGW,MAAI,WAAU,MAC7B,AAAC,0BAAuBA;QAE9B;IACF;IACAD,QAAQA,MACLX,OAAO,CAAC,6BAA6B,QACrCA,OAAO,CAAC,yBAAyB,KACjCA,OAAO,CAAC,0BAA0B,KAClCA,OAAO,CAAC,6BAA6B,KACrCA,OAAO,CAAC,8BAA8B;IAEzC,+DAA+D;IAC/D,YAAY;IACZ,OAAOlB,QAAQ,AAAC,MAAG6B,OAAS;QAAE0B,UAAU;IAAM,GAAG7B,QAAQgB,KAAK,CAAC;AACjE;AAEA,OAAO,SAASc,mBAAmBC,IAKlC;IACC,MAAMlC,QAAQsB,OAAOa,MAAM,CAAC,CAAC,GAAGD,KAAKlC,KAAK;IAC1C,OAAOA,MAAMoC,YAAY;IACzB,OAAOpC,MAAMqC,mBAAmB;IAChC,OAAOrC,MAAMsC,aAAa;IAC1B,OAAOtC,MAAMuC,+BAA+B;IAC5C,OAAOvC,KAAK,CAACjB,qBAAqB;IAElC,IAAIyD,qBAAqBN,KAAKO,WAAW;IAEzC,KAAK,MAAMC,SAASpB,OAAOC,IAAI,CAAC;QAAE,GAAGW,KAAK/B,MAAM;QAAE,GAAGH,KAAK;IAAC,GAAI;QAC7DwC,qBAAqBhD,cAAcgD,oBAAoBE;IACzD;IAEA,MAAMC,oBAAoB/D,SAAS4D;IACnC,MAAMI,YAAYD,kBAAkB3C,KAAK;IACzC,MAAM6C,WAAWhD,iBACf,AAAC,KAAE8C,kBAAkBG,QAAQ,GAAIH,CAAAA,kBAAkBI,IAAI,IAAI,EAAC;IAE9D,MAAMC,eAAenD,iBAAiB8C,kBAAkB9B,QAAQ,IAAI;IACpE,MAAMoC,oBAA2B,EAAE;IACnC,MAAMC,wBAA+B,EAAE;IACvCxE,aAAamE,UAAUI;IACvBvE,aAAasE,cAAcE;IAE3B,MAAMC,aAAkC,EAAE;IAE1CF,kBAAkBzB,OAAO,CAAC,CAACjB,MAAQ4C,WAAWC,IAAI,CAAC7C,IAAI8C,IAAI;IAC3DH,sBAAsB1B,OAAO,CAAC,CAACjB,MAAQ4C,WAAWC,IAAI,CAAC7C,IAAI8C,IAAI;IAE/D,MAAMC,mBAAmB7E,QACvBoE,UACA,oEAAoE;IACpE,oEAAoE;IACpE,0EAA0E;IAC1E,yEAAyE;IACzE,wEAAwE;IACxE,iDAAiD;IACjD;QAAEb,UAAU;IAAM;IAGpB,MAAMuB,uBAAuB9E,QAAQuE,cAAc;QAAEhB,UAAU;IAAM;IAErE,oCAAoC;IACpC,KAAK,MAAM,CAACzB,KAAKiD,WAAW,IAAIlC,OAAOmC,OAAO,CAACb,WAAY;QACzD,+DAA+D;QAC/D,YAAY;QACZ,IAAI3B,MAAMC,OAAO,CAACsC,aAAa;YAC7BZ,SAAS,CAACrC,IAAI,GAAGiD,WAAWE,GAAG,CAAC,CAACpD,QAC/BwB,eAAejC,iBAAiBS,QAAQ4B,KAAK/B,MAAM;QAEvD,OAAO,IAAI,OAAOqD,eAAe,UAAU;YACzCZ,SAAS,CAACrC,IAAI,GAAGuB,eAAejC,iBAAiB2D,aAAatB,KAAK/B,MAAM;QAC3E;IACF;IAEA,0DAA0D;IAC1D,+CAA+C;IAC/C,IAAIwD,YAAYrC,OAAOC,IAAI,CAACW,KAAK/B,MAAM,EAAEyD,MAAM,CAC7C,CAACP,OAASA,SAAS;IAGrB,IACEnB,KAAK2B,mBAAmB,IACxB,CAACF,UAAU9B,IAAI,CAAC,CAACtB,MAAQ4C,WAAWpB,QAAQ,CAACxB,OAC7C;QACA,KAAK,MAAMA,OAAOoD,UAAW;YAC3B,IAAI,CAAEpD,CAAAA,OAAOqC,SAAQ,GAAI;gBACvBA,SAAS,CAACrC,IAAI,GAAG2B,KAAK/B,MAAM,CAACI,IAAI;YACnC;QACF;IACF;IAEA,IAAIuD;IAEJ,uFAAuF;IACvF,6CAA6C;IAC7C,IAAIhF,2BAA2B+D,WAAW;QACxC,KAAK,MAAMkB,WAAWlB,SAAS/B,KAAK,CAAC,KAAM;YACzC,MAAMkD,SAASnF,2BAA2BoF,IAAI,CAAC,CAACC,IAC9CH,QAAQI,UAAU,CAACD;YAErB,IAAIF,QAAQ;gBACV9B,KAAK/B,MAAM,CAAC,IAAI,GAAG6D;gBACnB;YACF;QACF;IACF;IAEA,IAAI;QACFF,SAASR,iBAAiBpB,KAAK/B,MAAM;QAErC,MAAM,CAAC2C,UAAUC,KAAK,GAAGe,OAAOhD,KAAK,CAAC,KAAK;QAC3C6B,kBAAkB9B,QAAQ,GAAG0C,qBAAqBrB,KAAK/B,MAAM;QAC7DwC,kBAAkBG,QAAQ,GAAGA;QAC7BH,kBAAkBI,IAAI,GAAG,AAAC,KAAEA,CAAAA,OAAO,MAAM,EAAC,IAAIA,CAAAA,QAAQ,EAAC;QACvD,OAAO,AAACJ,kBAA0ByB,MAAM;IAC1C,EAAE,OAAOC,KAAU;QACjB,IAAIA,IAAIC,OAAO,CAAClD,KAAK,CAAC,iDAAiD;YACrE,MAAM,IAAImD,MACP;QAEL;QACA,MAAMF;IACR;IAEA,+CAA+C;IAC/C,8BAA8B;IAC9B,yBAAyB;IACzB,wCAAwC;IACxC1B,kBAAkB3C,KAAK,GAAG;QACxB,GAAGA,KAAK;QACR,GAAG2C,kBAAkB3C,KAAK;IAC5B;IAEA,OAAO;QACL8D;QACAlB;QACAD;IACF;AACF"}