'use client'

import { useState, useEffect } from 'react'
import AdminLayout from '@/components/AdminLayout'
import { Card, StatsCard } from '@/components/Card'
import { DataTable } from '@/components/Table'
import { Modal } from '@/components/Modal'
import { Loading } from '@/components/Loading'
import { useToast } from '@/components/Toast'
import { 
  MessageSquare, 
  Smartphone, 
  Wifi, 
  WifiOff,
  Plus,
  Trash2,
  RefreshCw,
  Settings,
  Users,
  Activity,
  AlertCircle,
  CheckCircle,
  Clock,
  Send
} from 'lucide-react'

interface TelegramSession {
  id: string
  botId: string
  botUsername: string
  status: 'connected' | 'disconnected' | 'error'
  lastActivity: string
  connectedAt?: string
  error?: string
}

interface TelegramStats {
  totalMessages: number
  todayMessages: number
  activeUsers: number
  commandsUsed: number
}

export default function AdminTelegramPage() {
  const [session, setSession] = useState<TelegramSession | null>(null)
  const [stats, setStats] = useState<TelegramStats>({
    totalMessages: 0,
    todayMessages: 0,
    activeUsers: 0,
    commandsUsed: 0
  })
  const [loading, setLoading] = useState(true)
  const [showConnectModal, setShowConnectModal] = useState(false)
  const [connectingBot, setConnectingBot] = useState(false)
  const [token, setToken] = useState('')
  const [webhookUrl, setWebhookUrl] = useState('')
  const [webhookSecret, setWebhookSecret] = useState('')
  const { showToast } = useToast()

  useEffect(() => {
    loadTelegramData()
    // Refresh data every 30 seconds
    const interval = setInterval(loadTelegramData, 30000)
    return () => clearInterval(interval)
  }, [])

  const loadTelegramData = async () => {
    try {
      // Load Telegram session
      const response = await fetch('/api/bots/telegram/connect')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setSession(data.data.session)
          
          // Mock stats for now
          if (data.data.session) {
            setStats({
              totalMessages: 1250,
              todayMessages: 89,
              activeUsers: 42,
              commandsUsed: 156
            })
          }
        }
      }
    } catch (error) {
      console.error('Failed to load Telegram data:', error)
      showToast('Failed to load Telegram data', 'error')
    } finally {
      setLoading(false)
    }
  }

  const handleConnectTelegram = async () => {
    if (!token) {
      showToast('Bot token is required', 'error')
      return
    }

    setConnectingBot(true)
    try {
      const response = await fetch('/api/bots/telegram/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token,
          webhookUrl: webhookUrl || undefined,
          webhookSecret: webhookSecret || undefined
        })
      })

      const data = await response.json()
      
      if (data.success) {
        showToast('Telegram bot connected successfully', 'success')
        setShowConnectModal(false)
        await loadTelegramData()
      } else {
        showToast(data.message || 'Failed to connect Telegram bot', 'error')
      }
    } catch (error) {
      console.error('Telegram connection error:', error)
      showToast('Failed to connect Telegram bot', 'error')
    } finally {
      setConnectingBot(false)
    }
  }

  const handleDisconnectBot = async () => {
    if (!session) return

    try {
      const response = await fetch('/api/bots/telegram/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ sessionId: session.id })
      })

      const data = await response.json()
      
      if (data.success) {
        showToast('Bot disconnected successfully', 'success')
        await loadTelegramData()
      } else {
        showToast(data.message || 'Failed to disconnect bot', 'error')
      }
    } catch (error) {
      console.error('Disconnect error:', error)
      showToast('Failed to disconnect bot', 'error')
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'disconnected':
        return <WifiOff className="h-4 w-4 text-gray-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <WifiOff className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'text-green-500'
      case 'disconnected':
        return 'text-gray-500'
      case 'error':
        return 'text-red-500'
      default:
        return 'text-gray-500'
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <Loading />
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Telegram Bot Management</h1>
            <p className="text-gray-600">Manage Telegram bot connection and settings</p>
          </div>
          <button
            onClick={() => setShowConnectModal(true)}
            className="btn-primary flex items-center space-x-2"
            disabled={!!session && session.status === 'connected'}
          >
            <Plus className="h-4 w-4" />
            <span>Connect Telegram</span>
          </button>
        </div>

        {/* Stats */}
        {session && session.status === 'connected' && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatsCard
              title="Total Messages"
              value={stats.totalMessages.toLocaleString()}
              icon={MessageSquare}
              color="blue"
            />
            <StatsCard
              title="Today's Messages"
              value={stats.todayMessages.toString()}
              icon={Send}
              color="green"
            />
            <StatsCard
              title="Active Users"
              value={stats.activeUsers.toString()}
              icon={Users}
              color="purple"
            />
            <StatsCard
              title="Commands Used"
              value={stats.commandsUsed.toString()}
              icon={Activity}
              color="orange"
            />
          </div>
        )}

        {/* Bot Status Card */}
        <Card>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Telegram Bot Status</h2>
            <button
              onClick={loadTelegramData}
              className="btn-secondary flex items-center space-x-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </button>
          </div>
          
          {session ? (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-gray-700">Status:</span>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(session.status)}
                      <span className={`capitalize ${getStatusColor(session.status)}`}>
                        {session.status}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-gray-700">Bot Username:</span>
                    <span>@{session.botUsername}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-gray-700">Bot ID:</span>
                    <span className="font-mono text-sm">{session.botId}</span>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-gray-700">Connected At:</span>
                    <span>{session.connectedAt ? new Date(session.connectedAt).toLocaleString() : '-'}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-gray-700">Last Activity:</span>
                    <span>{new Date(session.lastActivity).toLocaleString()}</span>
                  </div>
                  
                  {session.error && (
                    <div className="flex items-start space-x-2">
                      <span className="font-semibold text-gray-700">Error:</span>
                      <span className="text-red-500">{session.error}</span>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex justify-end space-x-3">
                {session.status === 'connected' && (
                  <button
                    onClick={handleDisconnectBot}
                    className="btn-danger flex items-center space-x-2"
                  >
                    <WifiOff className="h-4 w-4" />
                    <span>Disconnect Bot</span>
                  </button>
                )}
                
                <button
                  onClick={() => setShowConnectModal(true)}
                  className="btn-primary flex items-center space-x-2"
                  disabled={session.status === 'connected'}
                >
                  <Wifi className="h-4 w-4" />
                  <span>Reconnect Bot</span>
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Smartphone className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Telegram Bot Connected</h3>
              <p className="text-gray-600 mb-6">Connect your Telegram bot to enable automated responses and commands</p>
              <button
                onClick={() => setShowConnectModal(true)}
                className="btn-primary flex items-center space-x-2 mx-auto"
              >
                <Plus className="h-4 w-4" />
                <span>Connect Telegram Bot</span>
              </button>
            </div>
          )}
        </Card>

        {/* Connect Telegram Modal */}
        <Modal
          isOpen={showConnectModal}
          onClose={() => setShowConnectModal(false)}
          title="Connect Telegram Bot"
        >
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg">
              <Send className="h-8 w-8 text-blue-500" />
              <div>
                <h3 className="font-medium text-gray-900">Telegram Bot API</h3>
                <p className="text-sm text-gray-600">Connect your Telegram bot using BotFather token</p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Bot Token <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={token}
                  onChange={(e) => setToken(e.target.value)}
                  placeholder="123456789:ABCDefGhIJKlmNoPQRsTUVwxyZ"
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Get this from <a href="https://t.me/BotFather" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">@BotFather</a> on Telegram
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Webhook URL (Optional)
                </label>
                <input
                  type="text"
                  value={webhookUrl}
                  onChange={(e) => setWebhookUrl(e.target.value)}
                  placeholder="https://yourdomain.com/api/bots/telegram/webhook"
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Leave empty to use polling mode instead of webhooks
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Webhook Secret (Optional)
                </label>
                <input
                  type="text"
                  value={webhookSecret}
                  onChange={(e) => setWebhookSecret(e.target.value)}
                  placeholder="your-webhook-secret"
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Used to verify webhook requests
                </p>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowConnectModal(false)}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                onClick={handleConnectTelegram}
                disabled={connectingBot || !token}
                className="btn-primary flex items-center space-x-2"
              >
                {connectingBot ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Connecting...</span>
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4" />
                    <span>Connect Telegram</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </Modal>
      </div>
    </AdminLayout>
  )
}
