{"version": 3, "file": "queryVersions.js", "sourceRoot": "", "sources": ["../../../src/lib/queryVersions.ts"], "names": [], "mappings": ";;;;;AAAA,kDAAwB;AACxB,wDAAkC;AAClC,+CAAyC;AACzC,2EAAiD;AAIjD,4CAAyD;AAGzD,4EAAmD;AACnD,8DAAqC;AACrC,kEAAyC;AACzC,iDAAkF;AAElF;;;;;;GAMG;AACH,KAAK,UAAU,aAAa,CAAC,UAA8B,EAAE,UAAmB,EAAE;;IAChF,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA;IAC9D,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,CAAA;IACpE,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAC3C,MAAM,oBAAoB,GAAG,IAAA,2BAAiB,EAAC,OAAO,EAAE,OAAO,CAAC,cAAc,CAAC,CAAA;IAE/E,IAAI,GAA4B,CAAA;IAChC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;QAC9G,GAAG,GAAG,IAAI,kBAAW,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAA;QAClG,GAAG,CAAC,MAAM,EAAE,CAAA;KACb;IAED;;;;;;OAMG;IACH,KAAK,UAAU,0BAA0B,CAAC,GAAgB;;QACxD,MAAM,QAAQ,GAAG,IAAA,4BAAa,EAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAA;QAC/C,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,QAAQ,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAA;QAC1D,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,IAAI,QAAQ,CAAA;QAC/C,MAAM,YAAY,GAAG,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,IAAA,yBAAU,EAAC,OAAO,CAAC,CAAC,CAAA;QAC9G,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC;YACpD,CAAC,CAAC,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAA;QAE5B,MAAM,MAAM,GAAG,MAAA,OAAO,CAAC,MAAM,0CAAE,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QAChD,IAAI,MAAM,EAAE;YACV,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,EAAE,CAAA;YAEX,OAAO;gBACL,OAAO,EAAE,MAAM;aAChB,CAAA;SACF;QAED,IAAI,aAA4B,CAAA;QAChC,MAAM,kBAAkB,GAAG,IAAA,0BAAW,EAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAA;QAEvD,sEAAsE;QACtE,MAAM,cAAc,GAAG,kBAAkB,CAAC,CAAC,CAAC,0BAAe,CAAC,OAAO,CAAC,CAAC,CAAC,oBAAoB,CAAA;QAC1F,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,IAAI,KAAK,CAAA;QAE/F,MAAM,iBAAiB,GAAG,cAAc,CAAC,MAAqC,CAAe,CAAA;QAE7F,IAAI,CAAC,iBAAiB,EAAE;YACtB,MAAM,qCAAqC,GAAG,gCAAuB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,cAAc,CAAC,CAAA;YACtG,IAAA,sBAAY,EACV,OAAO,EACP,KAAK,CAAC,GAAG,CAAC,yBAAyB,MAAM,WAAW,kBAAkB,EAAE,CAAC;gBACvE,mCAAmC;gBACnC,qCAAqC,CAAC,IAAI,CAAC,IAAI,CAAC;gBAChD,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,EAAE,CAAC,EACxD,EAAE,KAAK,EAAE,KAAK,EAAE,CACjB,CAAA;SACF;QAED,IAAI;YACF,aAAa,GAAG,MAAM,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE;gBACrD,GAAG,OAAO;gBACV,OAAO;gBACP,sDAAsD;gBACtD,8CAA8C;gBAC9C,GAAG,EAAE,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAA,oBAAK,EAAC,OAAO,CAAC;gBACvF,KAAK,EAAE,MAAA,OAAO,CAAC,KAAK,mCAAI,CAAC;aAC1B,CAAC,CAAA;YAEF,aAAa,CAAC,OAAO;gBACnB,CAAC,kBAAkB,IAAI,QAAQ,KAAI,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,CAAA;oBACvD,CAAC,CAAC,IAAA,6BAAc,EAAC,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC;oBAC7C,CAAC,CAAC,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,mCAAI,IAAI,CAAA;SACrC;QAAC,OAAO,GAAQ,EAAE;YACjB,MAAM,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;YAC/D,IAAI,YAAY,CAAC,KAAK,CAAC,+BAA+B,CAAC,EAAE;gBACvD,OAAO;oBACL,KAAK,EAAE,GAAG,YAAY,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,SAClD,OAAO,CAAC,KACV,2KAA2K;iBAC5K,CAAA;aACF;iBAAM;gBACL,qEAAqE;gBACrE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,6BAA6B,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;oBAC9E,OAAO,CAAC,KAAK,CACX,MAAM;wBACJ,KAAK,CAAC,GAAG,CACP,sJAAsJ,CACvJ;wBACD,IAAI,CACP,CAAA;iBACF;gBAED,MAAM,GAAG,CAAA;aACV;SACF;QAED,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,EAAE,CAAA;QAEX,IAAI,aAAa,CAAC,OAAO,EAAE;YACzB,MAAA,OAAO,CAAC,MAAM,0CAAE,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,OAAO,CAAC,CAAA;SACzD;QAED,OAAO,aAAa,CAAA;IACtB,CAAC;IAED,MAAM,iBAAiB,GAAG,MAAM,IAAA,eAAI,EAAC,WAAW,EAAE,0BAA0B,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,CAAA;IAEnH,iDAAiD;IACjD,MAAM,CAAA,MAAA,OAAO,CAAC,MAAM,0CAAE,IAAI,EAAE,CAAA,CAAA;IAC5B,MAAA,OAAO,CAAC,MAAM,0CAAE,GAAG,EAAE,CAAA;IAErB,MAAM,mBAAmB,GAAG,IAAA,oBAAU,EAAC,iBAAiB,EAAE,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE,CAC7E,aAAa,CAAC,OAAO,IAAI,aAAa,CAAC,KAAK;QAC1C,CAAC,CAAC;YACE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa;SAChC;QACH,CAAC,CAAC,IAAI,CACT,CAAA;IAED,OAAO,mBAAmB,CAAA;AAC5B,CAAC;AAED,kBAAe,aAAa,CAAA"}