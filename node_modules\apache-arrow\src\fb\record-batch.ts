// automatically generated by the FlatBuffers compiler, do not modify

import * as flatbuffers from 'flatbuffers';

import { BodyCompression } from './body-compression.js';
import { Buff<PERSON> } from './buffer.js';
import { FieldNode } from './field-node.js';


/**
 * A data header describing the shared memory layout of a "record" or "row"
 * batch. Some systems call this a "row batch" internally and others a "record
 * batch".
 */
export class RecordBatch {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):RecordBatch {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsRecordBatch(bb:flatbuffers.ByteBuffer, obj?:RecordBatch):RecordBatch {
  return (obj || new RecordBatch()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsRecordBatch(bb:flatbuffers.ByteBuffer, obj?:RecordBatch):RecordBatch {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new RecordBatch()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

/**
 * number of records / rows. The arrays in the batch should all have this
 * length
 */
length():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.readInt64(this.bb_pos + offset) : BigInt('0');
}

/**
 * Nodes correspond to the pre-ordered flattened logical schema
 */
nodes(index: number, obj?:FieldNode):FieldNode|null {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? (obj || new FieldNode()).__init(this.bb!.__vector(this.bb_pos + offset) + index * 16, this.bb!) : null;
}

nodesLength():number {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? this.bb!.__vector_len(this.bb_pos + offset) : 0;
}

/**
 * Buffers correspond to the pre-ordered flattened buffer tree
 *
 * The number of buffers appended to this list depends on the schema. For
 * example, most primitive arrays will have 2 buffers, 1 for the validity
 * bitmap and 1 for the values. For struct arrays, there will only be a
 * single buffer for the validity (nulls) bitmap
 */
buffers(index: number, obj?:Buffer):Buffer|null {
  const offset = this.bb!.__offset(this.bb_pos, 8);
  return offset ? (obj || new Buffer()).__init(this.bb!.__vector(this.bb_pos + offset) + index * 16, this.bb!) : null;
}

buffersLength():number {
  const offset = this.bb!.__offset(this.bb_pos, 8);
  return offset ? this.bb!.__vector_len(this.bb_pos + offset) : 0;
}

/**
 * Optional compression of the message body
 */
compression(obj?:BodyCompression):BodyCompression|null {
  const offset = this.bb!.__offset(this.bb_pos, 10);
  return offset ? (obj || new BodyCompression()).__init(this.bb!.__indirect(this.bb_pos + offset), this.bb!) : null;
}

static startRecordBatch(builder:flatbuffers.Builder) {
  builder.startObject(4);
}

static addLength(builder:flatbuffers.Builder, length:bigint) {
  builder.addFieldInt64(0, length, BigInt('0'));
}

static addNodes(builder:flatbuffers.Builder, nodesOffset:flatbuffers.Offset) {
  builder.addFieldOffset(1, nodesOffset, 0);
}

static startNodesVector(builder:flatbuffers.Builder, numElems:number) {
  builder.startVector(16, numElems, 8);
}

static addBuffers(builder:flatbuffers.Builder, buffersOffset:flatbuffers.Offset) {
  builder.addFieldOffset(2, buffersOffset, 0);
}

static startBuffersVector(builder:flatbuffers.Builder, numElems:number) {
  builder.startVector(16, numElems, 8);
}

static addCompression(builder:flatbuffers.Builder, compressionOffset:flatbuffers.Offset) {
  builder.addFieldOffset(3, compressionOffset, 0);
}

static endRecordBatch(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

}
