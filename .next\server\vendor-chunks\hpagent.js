"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hpagent";
exports.ids = ["vendor-chunks/hpagent"];
exports.modules = {

/***/ "(rsc)/./node_modules/hpagent/index.js":
/*!***************************************!*\
  !*** ./node_modules/hpagent/index.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst https = __webpack_require__(/*! https */ \"https\")\nconst http = __webpack_require__(/*! http */ \"http\")\nconst { URL } = __webpack_require__(/*! url */ \"url\")\n\nclass HttpProxyAgent extends http.Agent {\n  constructor (options) {\n    const { proxy, proxyRequestOptions, ...opts } = options\n    super(opts)\n    this.proxy = typeof proxy === 'string'\n      ? new URL(proxy)\n      : proxy\n    this.proxyRequestOptions = proxyRequestOptions || {}\n  }\n\n  createConnection (options, callback) {\n    const requestOptions = {\n      ...this.proxyRequestOptions,\n      method: 'CONNECT',\n      host: this.proxy.hostname,\n      port: this.proxy.port,\n      path: `${options.host}:${options.port}`,\n      setHost: false,\n      headers: { ...this.proxyRequestOptions.headers, connection: this.keepAlive ? 'keep-alive' : 'close', host: `${options.host}:${options.port}` },\n      agent: false,\n      timeout: options.timeout || 0\n    }\n\n    if (this.proxy.username || this.proxy.password) {\n      const base64 = Buffer.from(`${decodeURIComponent(this.proxy.username || '')}:${decodeURIComponent(this.proxy.password || '')}`).toString('base64')\n      requestOptions.headers['proxy-authorization'] = `Basic ${base64}`\n    }\n\n    if (this.proxy.protocol === 'https:') {\n      requestOptions.servername = this.proxy.hostname\n    }\n\n    const request = (this.proxy.protocol === 'http:' ? http : https).request(requestOptions)\n    request.once('connect', (response, socket, head) => {\n      request.removeAllListeners()\n      socket.removeAllListeners()\n      if (response.statusCode === 200) {\n        callback(null, socket)\n      } else {\n        socket.destroy()\n        callback(new Error(`Bad response: ${response.statusCode}`), null)\n      }\n    })\n\n    request.once('timeout', () => {\n      request.destroy(new Error('Proxy timeout'))\n    })\n\n    request.once('error', err => {\n      request.removeAllListeners()\n      callback(err, null)\n    })\n\n    request.end()\n  }\n}\n\nclass HttpsProxyAgent extends https.Agent {\n  constructor (options) {\n    const { proxy, proxyRequestOptions, ...opts } = options\n    super(opts)\n    this.proxy = typeof proxy === 'string'\n      ? new URL(proxy)\n      : proxy\n    this.proxyRequestOptions = proxyRequestOptions || {}\n  }\n\n  createConnection (options, callback) {\n    const requestOptions = {\n      ...this.proxyRequestOptions,\n      method: 'CONNECT',\n      host: this.proxy.hostname,\n      port: this.proxy.port,\n      path: `${options.host}:${options.port}`,\n      setHost: false,\n      headers: { ...this.proxyRequestOptions.headers, connection: this.keepAlive ? 'keep-alive' : 'close', host: `${options.host}:${options.port}` },\n      agent: false,\n      timeout: options.timeout || 0\n    }\n\n    if (this.proxy.username || this.proxy.password) {\n      const base64 = Buffer.from(`${decodeURIComponent(this.proxy.username || '')}:${decodeURIComponent(this.proxy.password || '')}`).toString('base64')\n      requestOptions.headers['proxy-authorization'] = `Basic ${base64}`\n    }\n\n    // Necessary for the TLS check with the proxy to succeed.\n    if (this.proxy.protocol === 'https:') {\n      requestOptions.servername = this.proxy.hostname\n    }\n\n    const request = (this.proxy.protocol === 'http:' ? http : https).request(requestOptions)\n    request.once('connect', (response, socket, head) => {\n      request.removeAllListeners()\n      socket.removeAllListeners()\n      if (response.statusCode === 200) {\n        const secureSocket = super.createConnection({ ...options, socket })\n        callback(null, secureSocket)\n      } else {\n        socket.destroy()\n        callback(new Error(`Bad response: ${response.statusCode}`), null)\n      }\n    })\n\n    request.once('timeout', () => {\n      request.destroy(new Error('Proxy timeout'))\n    })\n\n    request.once('error', err => {\n      request.removeAllListeners()\n      callback(err, null)\n    })\n\n    request.end()\n  }\n}\n\nmodule.exports = {\n  HttpProxyAgent,\n  HttpsProxyAgent\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hpagent/index.js\n");

/***/ })

};
;