"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const resolveDepSections_1 = __importDefault(require("./resolveDepSections"));
/**
 * @returns String safe for use in `new RegExp()`
 */
function escapeRegexp(s) {
    return s.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&'); // Thanks Stack Overflow!
}
/**
 * Upgrade the dependency declarations in the package data.
 *
 * @param pkgData The package.json data, as utf8 text
 * @param oldDependencies Old dependencies {package: range}
 * @param newDependencies New dependencies {package: range}
 * @returns The updated package data, as utf8 text
 * @description Side Effect: prompts
 */
async function upgradePackageData(pkgData, current, upgraded, options) {
    // Always include overrides since any upgraded dependencies needed to be upgraded in overrides as well.
    // https://github.com/raineorshine/npm-check-updates/issues/1332
    const depSections = [...(0, resolveDepSections_1.default)(options.dep), 'overrides'];
    // iterate through each dependency section
    const sectionRegExp = new RegExp(`"(${depSections.join(`|`)})"s*:[^}]*`, 'g');
    let newPkgData = pkgData.replace(sectionRegExp, section => {
        // replace each upgraded dependency in the section
        Object.keys(upgraded).forEach(dep => {
            // const expression = `"${dep}"\\s*:\\s*"(${escapeRegexp(current[dep])})"`
            const expression = `"${dep}"\\s*:\\s*("|{\\s*"."\\s*:\\s*")(${escapeRegexp(current[dep])})"`;
            const regExp = new RegExp(expression, 'g');
            section = section.replace(regExp, (match, child) => `"${dep}${child ? `": ${child}` : ': '}${upgraded[dep]}"`);
        });
        return section;
    });
    if (depSections.includes('packageManager')) {
        const pkg = JSON.parse(pkgData);
        if (pkg.packageManager) {
            const [name] = pkg.packageManager.split('@');
            if (upgraded[name]) {
                newPkgData = newPkgData.replace(/"packageManager"\s*:\s*".*?@[^"]*"/, `"packageManager": "${name}@${upgraded[name]}"`);
            }
        }
    }
    return newPkgData;
}
exports.default = upgradePackageData;
//# sourceMappingURL=upgradePackageData.js.map