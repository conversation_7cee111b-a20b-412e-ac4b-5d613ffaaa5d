'use client'

import { useState, useEffect } from 'react'
import AdminLayout from '@/components/AdminLayout'
import { 
  Shield, 
  Users, 
  BarChart3, 
  Activity,
  Database,
  Server,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Eye,
  Lock,
  Unlock,
  Zap,
  Globe,
  FileText,
  Bug,
  Crown,
  Star,
  Award,
  Target,
  Search,
  Filter,
  Download,
  Upload,
  RefreshCw,
  Play,
  Pause,
  Square,
  Edit,
  Trash2,
  Plus,
  Minus,
  ArrowUp,
  ArrowDown,
  Calendar,
  MapPin,
  Wifi,
  WifiOff
} from 'lucide-react'

interface AdminStats {
  totalUsers: number
  activeUsers: number
  totalScans: number
  totalVulnerabilities: number
  systemUptime: number
  apiCalls: number
  storageUsed: number
  bandwidthUsed: number
}

interface SystemStatus {
  database: 'online' | 'offline' | 'maintenance'
  api: 'online' | 'offline' | 'maintenance'
  scanner: 'online' | 'offline' | 'maintenance'
  osint: 'online' | 'offline' | 'maintenance'
  fileAnalyzer: 'online' | 'offline' | 'maintenance'
}

interface RecentUser {
  id: string
  username: string
  email: string
  plan: string
  joinedAt: string
  lastActive: string
  status: 'active' | 'inactive' | 'banned'
}

export default function AdminPage() {
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    activeUsers: 0,
    totalScans: 0,
    totalVulnerabilities: 0,
    systemUptime: 0,
    apiCalls: 0,
    storageUsed: 0,
    bandwidthUsed: 0
  })

  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    database: 'online',
    api: 'online',
    scanner: 'online',
    osint: 'online',
    fileAnalyzer: 'online'
  })

  const [recentUsers, setRecentUsers] = useState<RecentUser[]>([])
  const [recentActivity, setRecentActivity] = useState<any[]>([])
  const [alerts, setAlerts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    loadAdminData()
  }, [])

  const loadAdminData = async () => {
    try {
      console.log('🔧 Loading admin dashboard data...')
      setLoading(true)

      // Fetch real admin data from API
      const response = await fetch('/api/admin/dashboard')
      const data = await response.json()

      if (data.success) {
        console.log('✅ Admin dashboard data loaded:', data.data)

        setStats(data.data.stats)
        setSystemStatus(data.data.systemStatus)
        setRecentUsers(data.data.recentUsers)
        setRecentActivity(data.data.recentActivity)
        setAlerts(data.data.alerts)
      } else {
        console.error('❌ Failed to load admin data:', data.error)
        // Fallback to default data
        setStats({
          totalUsers: 1247,
          activeUsers: 892,
          totalScans: 15634,
          totalVulnerabilities: 4521,
          systemUptime: 99.8,
          apiCalls: 234567,
          storageUsed: 78.5,
          bandwidthUsed: 45.2
        })

        setRecentUsers([
          {
            id: '1',
            username: 'CyberNinja',
            email: '<EMAIL>',
            plan: 'Elite',
            joinedAt: '2024-01-15',
            lastActive: '2 minutes ago',
            status: 'active'
          }
        ])
      } else {
        // Fallback to mock data if API fails
        setStats({
          totalUsers: 15420,
          activeUsers: 1247,
          totalScans: 89456,
          totalVulnerabilities: 2341,
          systemUptime: 99.8,
          apiCalls: 1247890,
          storageUsed: 78.5,
          bandwidthUsed: 45.2
        })

        setRecentUsers([
          {
            id: '1',
            username: 'CyberNinja',
            email: '<EMAIL>',
            plan: 'Elite',
            joinedAt: '2024-01-15',
            lastActive: '2 minutes ago',
            status: 'active'
          }
        ])
      }

      setLoading(false)
    } catch (error) {
      console.error('Error loading admin data:', error)
      setLoading(false)
    }
  }

  const handleResolveAlert = async (alertId: string) => {
    try {
      const response = await fetch('/api/admin/dashboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'resolve_alert',
          data: { alertId }
        }),
      })

      if (response.ok) {
        loadAdminData() // Refresh data
      }
    } catch (error) {
      console.error('Error resolving alert:', error)
    }
  }

  const handleBanUser = async (userId: string) => {
    try {
      const response = await fetch('/api/admin/dashboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'ban_user',
          data: { userId }
        }),
      })

      if (response.ok) {
        loadAdminData() // Refresh data
      }
    } catch (error) {
      console.error('Error banning user:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-400 bg-green-400/20'
      case 'offline': return 'text-red-400 bg-red-400/20'
      case 'maintenance': return 'text-yellow-400 bg-yellow-400/20'
      default: return 'text-gray-400 bg-gray-400/20'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <CheckCircle className="h-4 w-4" />
      case 'offline': return <XCircle className="h-4 w-4" />
      case 'maintenance': return <Clock className="h-4 w-4" />
      default: return <AlertTriangle className="h-4 w-4" />
    }
  }

  const getUserStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400'
      case 'inactive': return 'text-yellow-400'
      case 'banned': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4"></div>
            <div className="text-cyber-primary font-medium">Loading admin console...</div>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              <span className="text-cyber-glow">Admin</span>{' '}
              <span className="text-cyber-pink">Console</span>
            </h1>
            <p className="text-gray-300 text-lg">
              System administration and monitoring dashboard
            </p>
          </div>

          <div className="mt-6 lg:mt-0 flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-400">System Online</span>
            </div>
            <button className="btn-cyber-primary">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Data
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-cyber-dark/50 p-1 rounded-lg">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'users', label: 'Users', icon: Users },
            { id: 'system', label: 'System', icon: Server },
            { id: 'security', label: 'Security', icon: Shield },
            { id: 'settings', label: 'Settings', icon: Settings }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30'
                    : 'text-gray-400 hover:text-white hover:bg-cyber-primary/10'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* System Stats Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="card-cyber text-center">
                <Users className="h-8 w-8 text-cyber-primary mx-auto mb-3 animate-cyber-pulse" />
                <div className="text-2xl font-bold text-white mb-1">
                  {stats.totalUsers.toLocaleString()}
                </div>
                <div className="text-sm text-gray-400">Total Users</div>
                <div className="text-xs text-green-400 mt-1">+{stats.activeUsers} active</div>
              </div>

              <div className="card-cyber text-center">
                <Shield className="h-8 w-8 text-cyber-secondary mx-auto mb-3 animate-cyber-pulse" />
                <div className="text-2xl font-bold text-white mb-1">
                  {stats.totalScans.toLocaleString()}
                </div>
                <div className="text-sm text-gray-400">Total Scans</div>
                <div className="text-xs text-blue-400 mt-1">+1.2K today</div>
              </div>

              <div className="card-cyber text-center">
                <Bug className="h-8 w-8 text-red-400 mx-auto mb-3 animate-cyber-pulse" />
                <div className="text-2xl font-bold text-white mb-1">
                  {stats.totalVulnerabilities.toLocaleString()}
                </div>
                <div className="text-sm text-gray-400">Vulnerabilities</div>
                <div className="text-xs text-red-400 mt-1">45 critical</div>
              </div>

              <div className="card-cyber text-center">
                <TrendingUp className="h-8 w-8 text-green-400 mx-auto mb-3 animate-cyber-pulse" />
                <div className="text-2xl font-bold text-white mb-1">
                  {stats.systemUptime}%
                </div>
                <div className="text-sm text-gray-400">System Uptime</div>
                <div className="text-xs text-green-400 mt-1">99.8% this month</div>
              </div>
            </div>

            {/* System Status */}
            <div className="card-cyber">
              <h2 className="text-2xl font-bold text-white mb-6">
                <span className="text-cyber-glow">System</span>{' '}
                <span className="text-cyber-pink">Status</span>
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                {Object.entries(systemStatus).map(([service, status]) => (
                  <div
                    key={service}
                    className="p-4 rounded-lg bg-cyber-secondary/5 border border-cyber-border"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-white capitalize">
                        {service.replace(/([A-Z])/g, ' $1').trim()}
                      </h3>
                      <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-bold ${getStatusColor(status)}`}>
                        {getStatusIcon(status)}
                        <span className="capitalize">{status}</span>
                      </div>
                    </div>
                    <div className="text-sm text-gray-400">
                      {status === 'online' ? 'Running normally' :
                       status === 'offline' ? 'Service unavailable' :
                       'Under maintenance'}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Performance Metrics */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="card-cyber">
                <h3 className="text-xl font-bold text-white mb-4">
                  <span className="text-cyber-glow">API</span>{' '}
                  <span className="text-cyber-pink">Performance</span>
                </h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Total API Calls</span>
                    <span className="text-white font-bold">{stats.apiCalls.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Avg Response Time</span>
                    <span className="text-green-400 font-bold">145ms</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Error Rate</span>
                    <span className="text-red-400 font-bold">0.02%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Rate Limit Hits</span>
                    <span className="text-yellow-400 font-bold">12</span>
                  </div>
                </div>
              </div>

              <div className="card-cyber">
                <h3 className="text-xl font-bold text-white mb-4">
                  <span className="text-cyber-glow">Resource</span>{' '}
                  <span className="text-cyber-pink">Usage</span>
                </h3>

                <div className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-gray-400">Storage Used</span>
                      <span className="text-white font-bold">{stats.storageUsed}%</span>
                    </div>
                    <div className="w-full bg-cyber-dark rounded-full h-2">
                      <div
                        className="bg-cyber-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${stats.storageUsed}%` }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-gray-400">Bandwidth Used</span>
                      <span className="text-white font-bold">{stats.bandwidthUsed}%</span>
                    </div>
                    <div className="w-full bg-cyber-dark rounded-full h-2">
                      <div
                        className="bg-cyber-secondary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${stats.bandwidthUsed}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">CPU Usage</span>
                    <span className="text-green-400 font-bold">23%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Memory Usage</span>
                    <span className="text-blue-400 font-bold">67%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Users Tab */}
        {activeTab === 'users' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-white">
                <span className="text-cyber-glow">User</span>{' '}
                <span className="text-cyber-pink">Management</span>
              </h2>
              <button className="btn-cyber-primary">
                <Plus className="h-4 w-4 mr-2" />
                Add User
              </button>
            </div>

            {/* User Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="card-cyber text-center">
                <Users className="h-6 w-6 text-cyber-primary mx-auto mb-2" />
                <div className="text-xl font-bold text-white">{stats.totalUsers.toLocaleString()}</div>
                <div className="text-sm text-gray-400">Total Users</div>
              </div>
              <div className="card-cyber text-center">
                <Activity className="h-6 w-6 text-green-400 mx-auto mb-2" />
                <div className="text-xl font-bold text-white">{stats.activeUsers.toLocaleString()}</div>
                <div className="text-sm text-gray-400">Active Users</div>
              </div>
              <div className="card-cyber text-center">
                <Crown className="h-6 w-6 text-cyber-accent mx-auto mb-2" />
                <div className="text-xl font-bold text-white">1,247</div>
                <div className="text-sm text-gray-400">Premium Users</div>
              </div>
              <div className="card-cyber text-center">
                <TrendingUp className="h-6 w-6 text-cyber-secondary mx-auto mb-2" />
                <div className="text-xl font-bold text-white">+12%</div>
                <div className="text-sm text-gray-400">Growth Rate</div>
              </div>
            </div>

            {/* Recent Users Table */}
            <div className="card-cyber">
              <h3 className="text-xl font-bold text-white mb-4">Recent Users</h3>
              <div className="space-y-3">
                {recentUsers.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5 hover:bg-cyber-primary/10 transition-colors duration-200"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 rounded-full bg-cyber-primary/20 flex items-center justify-center text-sm font-bold text-cyber-primary">
                        {user.username.charAt(0)}
                      </div>
                      <div>
                        <h4 className="font-medium text-white">{user.username}</h4>
                        <p className="text-gray-400 text-sm">{user.email}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="text-center">
                        <div className="text-sm font-medium text-cyber-accent">{user.plan}</div>
                        <div className="text-xs text-gray-400">Plan</div>
                      </div>
                      <div className="text-center">
                        <div className={`text-sm font-medium ${getUserStatusColor(user.status)}`}>
                          {user.status}
                        </div>
                        <div className="text-xs text-gray-400">{user.lastActive}</div>
                      </div>
                      <div className="flex space-x-2">
                        <button className="p-2 rounded-lg bg-cyber-primary/20 text-cyber-primary hover:bg-cyber-primary/30 transition-colors">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="p-2 rounded-lg bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* System Tab */}
        {activeTab === 'system' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white">
              <span className="text-cyber-glow">System</span>{' '}
              <span className="text-cyber-pink">Management</span>
            </h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="card-cyber">
                <h3 className="text-xl font-bold text-white mb-4">Service Control</h3>
                <div className="space-y-3">
                  {Object.entries(systemStatus).map(([service, status]) => (
                    <div key={service} className="flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          status === 'online' ? 'bg-green-400' :
                          status === 'offline' ? 'bg-red-400' : 'bg-yellow-400'
                        }`}></div>
                        <span className="text-white font-medium capitalize">
                          {service.replace(/([A-Z])/g, ' $1').trim()}
                        </span>
                      </div>
                      <div className="flex space-x-2">
                        <button className="p-1 rounded bg-green-500/20 text-green-400 hover:bg-green-500/30">
                          <Play className="h-4 w-4" />
                        </button>
                        <button className="p-1 rounded bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30">
                          <Pause className="h-4 w-4" />
                        </button>
                        <button className="p-1 rounded bg-red-500/20 text-red-400 hover:bg-red-500/30">
                          <Square className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="card-cyber">
                <h3 className="text-xl font-bold text-white mb-4">Database Management</h3>
                <div className="space-y-4">
                  <button className="w-full flex items-center justify-center space-x-2 p-3 rounded-lg bg-cyber-primary/20 text-cyber-primary hover:bg-cyber-primary/30 transition-colors">
                    <Download className="h-4 w-4" />
                    <span>Backup Database</span>
                  </button>
                  <button className="w-full flex items-center justify-center space-x-2 p-3 rounded-lg bg-cyber-secondary/20 text-cyber-secondary hover:bg-cyber-secondary/30 transition-colors">
                    <Upload className="h-4 w-4" />
                    <span>Restore Database</span>
                  </button>
                  <button className="w-full flex items-center justify-center space-x-2 p-3 rounded-lg bg-cyber-accent/20 text-cyber-accent hover:bg-cyber-accent/30 transition-colors">
                    <RefreshCw className="h-4 w-4" />
                    <span>Optimize Database</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Security Tab */}
        {activeTab === 'security' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white">
              <span className="text-cyber-glow">Security</span>{' '}
              <span className="text-cyber-pink">Center</span>
            </h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="card-cyber">
                <h3 className="text-xl font-bold text-white mb-4">Security Alerts</h3>
                <div className="space-y-3">
                  <div className="p-3 rounded-lg bg-red-500/10 border border-red-500/30">
                    <div className="flex items-center space-x-2 mb-2">
                      <AlertTriangle className="h-4 w-4 text-red-400" />
                      <span className="text-red-400 font-medium">Critical Alert</span>
                    </div>
                    <p className="text-gray-300 text-sm">Multiple failed login attempts detected</p>
                  </div>
                  <div className="p-3 rounded-lg bg-yellow-500/10 border border-yellow-500/30">
                    <div className="flex items-center space-x-2 mb-2">
                      <Clock className="h-4 w-4 text-yellow-400" />
                      <span className="text-yellow-400 font-medium">Warning</span>
                    </div>
                    <p className="text-gray-300 text-sm">High API usage detected from IP *************</p>
                  </div>
                </div>
              </div>

              <div className="card-cyber">
                <h3 className="text-xl font-bold text-white mb-4">Access Control</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5">
                    <span className="text-white">Two-Factor Authentication</span>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      <span className="text-green-400 text-sm">Enabled</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5">
                    <span className="text-white">IP Whitelist</span>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      <span className="text-green-400 text-sm">Active</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5">
                    <span className="text-white">Rate Limiting</span>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      <span className="text-green-400 text-sm">Configured</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-white">
              <span className="text-cyber-glow">System</span>{' '}
              <span className="text-cyber-pink">Settings</span>
            </h2>

            <div className="card-cyber">
              <h3 className="text-xl font-bold text-white mb-4">Configuration</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5">
                  <div>
                    <span className="text-white font-medium">Maintenance Mode</span>
                    <p className="text-gray-400 text-sm">Enable system maintenance mode</p>
                  </div>
                  <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-cyber-primary focus:ring-offset-2">
                    <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1"></span>
                  </button>
                </div>

                <div className="flex items-center justify-between p-3 rounded-lg bg-cyber-secondary/5">
                  <div>
                    <span className="text-white font-medium">Debug Mode</span>
                    <p className="text-gray-400 text-sm">Enable detailed logging</p>
                  </div>
                  <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-cyber-primary transition-colors focus:outline-none focus:ring-2 focus:ring-cyber-primary focus:ring-offset-2">
                    <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6"></span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
