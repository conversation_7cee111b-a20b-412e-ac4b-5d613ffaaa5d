{"version": 3, "file": "PreloadScriptStorage.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/domains/script/PreloadScriptStorage.ts"], "names": [], "mappings": ";;;AA2BA;;GAEG;AACH,MAAa,oBAAoB;IAC/B,wCAAwC;IAC/B,QAAQ,GAAG,IAAI,GAAG,EAAiB,CAAC;IAE7C,qDAAqD;IACrD,IAAI,CAAC,MAA4B;QAC/B,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;YAC1C,IAAI,MAAM,CAAC,EAAE,KAAK,SAAS,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,EAAE,CAAC;gBACvD,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IACE,MAAM,CAAC,QAAQ,KAAK,SAAS;gBAC7B,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EACtC,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IACE,MAAM,CAAC,MAAM,KAAK,SAAS;gBAC3B,kCAAkC;gBAClC,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,CAAC;oBAC/C,0CAA0C;oBAC1C,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,EACpD,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED,GAAG,CAAC,aAA4B;QAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IACnC,CAAC;IAED,2EAA2E;IAC3E,MAAM,CAAC,MAA4B;QACjC,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9C,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;CACF;AA3CD,oDA2CC"}