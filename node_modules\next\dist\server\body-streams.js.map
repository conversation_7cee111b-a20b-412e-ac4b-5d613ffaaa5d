{"version": 3, "sources": ["../../src/server/body-streams.ts"], "names": ["getCloneableBody", "requestToBodyStream", "context", "KUint8Array", "stream", "ReadableStream", "start", "controller", "chunk", "enqueue", "close", "replaceRequestBody", "base", "key", "v", "bind", "readable", "buffered", "endPromise", "Promise", "resolve", "reject", "on", "catch", "error", "finalize", "res", "cloneBodyStream", "input", "p1", "PassThrough", "p2", "push"], "mappings": ";;;;;;;;;;;;;;;IAuCgBA,gBAAgB;eAAhBA;;IAnCAC,mBAAmB;eAAnBA;;;wBAFY;AAErB,SAASA,oBACdC,OAAkD,EAClDC,WAA8B,EAC9BC,MAAgB;IAEhB,OAAO,IAAIF,QAAQG,cAAc,CAAC;QAChCC,OAAO,OAAOC;YACZ,WAAW,MAAMC,SAASJ,OAAQ;gBAChCG,WAAWE,OAAO,CAAC,IAAIN,YAAYK;YACrC;YACAD,WAAWG,KAAK;QAClB;IACF;AACF;AAEA,SAASC,mBACPC,IAAO,EACPR,MAAgB;IAEhB,IAAK,MAAMS,OAAOT,OAAQ;QACxB,IAAIU,IAAIV,MAAM,CAACS,IAAsB;QACrC,IAAI,OAAOC,MAAM,YAAY;YAC3BA,IAAIA,EAAEC,IAAI,CAACH;QACb;QACAA,IAAI,CAACC,IAAe,GAAGC;IACzB;IAEA,OAAOF;AACT;AAOO,SAASZ,iBACdgB,QAAW;IAEX,IAAIC,WAA4B;IAEhC,MAAMC,aAAa,IAAIC,QACrB,CAACC,SAASC;QACRL,SAASM,EAAE,CAAC,OAAOF;QACnBJ,SAASM,EAAE,CAAC,SAASD;IACvB,GACAE,KAAK,CAAC,CAACC;QACP,OAAO;YAAEA;QAAM;IACjB;IAEA,OAAO;QACL;;;;KAIC,GACD,MAAMC;YACJ,IAAIR,UAAU;gBACZ,MAAMS,MAAM,MAAMR;gBAElB,IAAIQ,OAAO,OAAOA,QAAQ,YAAYA,IAAIF,KAAK,EAAE;oBAC/C,MAAME,IAAIF,KAAK;gBACjB;gBACAb,mBAAmBK,UAAUC;gBAC7BA,WAAWD;YACb;QACF;QACA;;;KAGC,GACDW;YACE,MAAMC,QAAQX,YAAYD;YAC1B,MAAMa,KAAK,IAAIC,mBAAW;YAC1B,MAAMC,KAAK,IAAID,mBAAW;YAC1BF,MAAMN,EAAE,CAAC,QAAQ,CAACd;gBAChBqB,GAAGG,IAAI,CAACxB;gBACRuB,GAAGC,IAAI,CAACxB;YACV;YACAoB,MAAMN,EAAE,CAAC,OAAO;gBACdO,GAAGG,IAAI,CAAC;gBACRD,GAAGC,IAAI,CAAC;YACV;YACAf,WAAWc;YACX,OAAOF;QACT;IACF;AACF"}