#!/usr/bin/env ts-node

import { testDatabaseConnection, closeDatabaseConnections, db } from '../lib/database'

async function checkUserSessions() {
  console.log('🔍 Checking User Sessions Table Structure...\n')

  try {
    // Test database connection
    const connected = await testDatabaseConnection()
    if (!connected) {
      console.error('❌ Database connection failed')
      process.exit(1)
    }

    // Check user_sessions table structure
    console.log('📋 Current user_sessions table structure:')
    try {
      const columns = await db.query('DESCRIBE user_sessions') as any[]
      console.table(columns)
    } catch (error: any) {
      console.error('❌ Error describing user_sessions table:', error.message)
    }

    // Check if table exists
    console.log('\n📋 Checking if user_sessions table exists:')
    try {
      const tables = await db.query("SHOW TABLES LIKE 'user_sessions'") as any[]
      console.log(`Found ${tables.length} table(s) matching 'user_sessions'`)
      if (tables.length > 0) {
        console.log('✅ user_sessions table exists')
      } else {
        console.log('❌ user_sessions table does not exist')
      }
    } catch (error: any) {
      console.error('❌ Error checking table existence:', error.message)
    }

    // Check all tables
    console.log('\n📋 All tables in database:')
    try {
      const allTables = await db.query('SHOW TABLES') as any[]
      const tableNames = allTables.map(row => Object.values(row)[0])
      console.log('Tables:', tableNames.join(', '))
    } catch (error: any) {
      console.error('❌ Error listing tables:', error.message)
    }

    console.log('\n✅ Check completed!')
    
  } catch (error) {
    console.error('❌ Check failed:', error)
    process.exit(1)
  } finally {
    await closeDatabaseConnections()
  }
}

// Run check if this file is executed directly
if (require.main === module) {
  checkUserSessions()
}
