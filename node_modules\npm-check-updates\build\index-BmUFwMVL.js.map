{"version": 3, "file": "index-BmUFwMVL.js", "sources": ["../node_modules/chalk/source/vendor/ansi-styles/index.js", "../node_modules/chalk/source/vendor/supports-color/index.js", "../node_modules/chalk/source/utilities.js", "../node_modules/chalk/source/index.js"], "sourcesContent": ["const ANSI_BACKGROUND_OFFSET = 10;\n\nconst wrapAnsi16 = (offset = 0) => code => `\\u001B[${code + offset}m`;\n\nconst wrapAnsi256 = (offset = 0) => code => `\\u001B[${38 + offset};5;${code}m`;\n\nconst wrapAnsi16m = (offset = 0) => (red, green, blue) => `\\u001B[${38 + offset};2;${red};${green};${blue}m`;\n\nconst styles = {\n\tmodifier: {\n\t\treset: [0, 0],\n\t\t// 21 isn't widely supported and 22 does the same thing\n\t\tbold: [1, 22],\n\t\tdim: [2, 22],\n\t\titalic: [3, 23],\n\t\tunderline: [4, 24],\n\t\toverline: [53, 55],\n\t\tinverse: [7, 27],\n\t\thidden: [8, 28],\n\t\tstrikethrough: [9, 29],\n\t},\n\tcolor: {\n\t\tblack: [30, 39],\n\t\tred: [31, 39],\n\t\tgreen: [32, 39],\n\t\tyellow: [33, 39],\n\t\tblue: [34, 39],\n\t\tmagenta: [35, 39],\n\t\tcyan: [36, 39],\n\t\twhite: [37, 39],\n\n\t\t// Bright color\n\t\tblackBright: [90, 39],\n\t\tgray: [90, 39], // Alias of `blackBright`\n\t\tgrey: [90, 39], // Alias of `blackBright`\n\t\tredBright: [91, 39],\n\t\tgreenBright: [92, 39],\n\t\tyellowBright: [93, 39],\n\t\tblueBright: [94, 39],\n\t\tmagentaBright: [95, 39],\n\t\tcyanBright: [96, 39],\n\t\twhiteBright: [97, 39],\n\t},\n\tbgColor: {\n\t\tbgBlack: [40, 49],\n\t\tbgRed: [41, 49],\n\t\tbgGreen: [42, 49],\n\t\tbgYellow: [43, 49],\n\t\tbgBlue: [44, 49],\n\t\tbgMagenta: [45, 49],\n\t\tbgCyan: [46, 49],\n\t\tbgWhite: [47, 49],\n\n\t\t// Bright color\n\t\tbgBlackBright: [100, 49],\n\t\tbgGray: [100, 49], // Alias of `bgBlackBright`\n\t\tbgGrey: [100, 49], // Alias of `bgBlackBright`\n\t\tbgRedBright: [101, 49],\n\t\tbgGreenBright: [102, 49],\n\t\tbgYellowBright: [103, 49],\n\t\tbgBlueBright: [104, 49],\n\t\tbgMagentaBright: [105, 49],\n\t\tbgCyanBright: [106, 49],\n\t\tbgWhiteBright: [107, 49],\n\t},\n};\n\nexport const modifierNames = Object.keys(styles.modifier);\nexport const foregroundColorNames = Object.keys(styles.color);\nexport const backgroundColorNames = Object.keys(styles.bgColor);\nexport const colorNames = [...foregroundColorNames, ...backgroundColorNames];\n\nfunction assembleStyles() {\n\tconst codes = new Map();\n\n\tfor (const [groupName, group] of Object.entries(styles)) {\n\t\tfor (const [styleName, style] of Object.entries(group)) {\n\t\t\tstyles[styleName] = {\n\t\t\t\topen: `\\u001B[${style[0]}m`,\n\t\t\t\tclose: `\\u001B[${style[1]}m`,\n\t\t\t};\n\n\t\t\tgroup[styleName] = styles[styleName];\n\n\t\t\tcodes.set(style[0], style[1]);\n\t\t}\n\n\t\tObject.defineProperty(styles, groupName, {\n\t\t\tvalue: group,\n\t\t\tenumerable: false,\n\t\t});\n\t}\n\n\tObject.defineProperty(styles, 'codes', {\n\t\tvalue: codes,\n\t\tenumerable: false,\n\t});\n\n\tstyles.color.close = '\\u001B[39m';\n\tstyles.bgColor.close = '\\u001B[49m';\n\n\tstyles.color.ansi = wrapAnsi16();\n\tstyles.color.ansi256 = wrapAnsi256();\n\tstyles.color.ansi16m = wrapAnsi16m();\n\tstyles.bgColor.ansi = wrapAnsi16(ANSI_BACKGROUND_OFFSET);\n\tstyles.bgColor.ansi256 = wrapAnsi256(ANSI_BACKGROUND_OFFSET);\n\tstyles.bgColor.ansi16m = wrapAnsi16m(ANSI_BACKGROUND_OFFSET);\n\n\t// From https://github.com/Qix-/color-convert/blob/3f0e0d4e92e235796ccb17f6e85c72094a651f49/conversions.js\n\tObject.defineProperties(styles, {\n\t\trgbToAnsi256: {\n\t\t\tvalue(red, green, blue) {\n\t\t\t\t// We use the extended greyscale palette here, with the exception of\n\t\t\t\t// black and white. normal palette only has 4 greyscale shades.\n\t\t\t\tif (red === green && green === blue) {\n\t\t\t\t\tif (red < 8) {\n\t\t\t\t\t\treturn 16;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (red > 248) {\n\t\t\t\t\t\treturn 231;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn Math.round(((red - 8) / 247) * 24) + 232;\n\t\t\t\t}\n\n\t\t\t\treturn 16\n\t\t\t\t\t+ (36 * Math.round(red / 255 * 5))\n\t\t\t\t\t+ (6 * Math.round(green / 255 * 5))\n\t\t\t\t\t+ Math.round(blue / 255 * 5);\n\t\t\t},\n\t\t\tenumerable: false,\n\t\t},\n\t\thexToRgb: {\n\t\t\tvalue(hex) {\n\t\t\t\tconst matches = /[a-f\\d]{6}|[a-f\\d]{3}/i.exec(hex.toString(16));\n\t\t\t\tif (!matches) {\n\t\t\t\t\treturn [0, 0, 0];\n\t\t\t\t}\n\n\t\t\t\tlet [colorString] = matches;\n\n\t\t\t\tif (colorString.length === 3) {\n\t\t\t\t\tcolorString = [...colorString].map(character => character + character).join('');\n\t\t\t\t}\n\n\t\t\t\tconst integer = Number.parseInt(colorString, 16);\n\n\t\t\t\treturn [\n\t\t\t\t\t/* eslint-disable no-bitwise */\n\t\t\t\t\t(integer >> 16) & 0xFF,\n\t\t\t\t\t(integer >> 8) & 0xFF,\n\t\t\t\t\tinteger & 0xFF,\n\t\t\t\t\t/* eslint-enable no-bitwise */\n\t\t\t\t];\n\t\t\t},\n\t\t\tenumerable: false,\n\t\t},\n\t\thexToAnsi256: {\n\t\t\tvalue: hex => styles.rgbToAnsi256(...styles.hexToRgb(hex)),\n\t\t\tenumerable: false,\n\t\t},\n\t\tansi256ToAnsi: {\n\t\t\tvalue(code) {\n\t\t\t\tif (code < 8) {\n\t\t\t\t\treturn 30 + code;\n\t\t\t\t}\n\n\t\t\t\tif (code < 16) {\n\t\t\t\t\treturn 90 + (code - 8);\n\t\t\t\t}\n\n\t\t\t\tlet red;\n\t\t\t\tlet green;\n\t\t\t\tlet blue;\n\n\t\t\t\tif (code >= 232) {\n\t\t\t\t\tred = (((code - 232) * 10) + 8) / 255;\n\t\t\t\t\tgreen = red;\n\t\t\t\t\tblue = red;\n\t\t\t\t} else {\n\t\t\t\t\tcode -= 16;\n\n\t\t\t\t\tconst remainder = code % 36;\n\n\t\t\t\t\tred = Math.floor(code / 36) / 5;\n\t\t\t\t\tgreen = Math.floor(remainder / 6) / 5;\n\t\t\t\t\tblue = (remainder % 6) / 5;\n\t\t\t\t}\n\n\t\t\t\tconst value = Math.max(red, green, blue) * 2;\n\n\t\t\t\tif (value === 0) {\n\t\t\t\t\treturn 30;\n\t\t\t\t}\n\n\t\t\t\t// eslint-disable-next-line no-bitwise\n\t\t\t\tlet result = 30 + ((Math.round(blue) << 2) | (Math.round(green) << 1) | Math.round(red));\n\n\t\t\t\tif (value === 2) {\n\t\t\t\t\tresult += 60;\n\t\t\t\t}\n\n\t\t\t\treturn result;\n\t\t\t},\n\t\t\tenumerable: false,\n\t\t},\n\t\trgbToAnsi: {\n\t\t\tvalue: (red, green, blue) => styles.ansi256ToAnsi(styles.rgbToAnsi256(red, green, blue)),\n\t\t\tenumerable: false,\n\t\t},\n\t\thexToAnsi: {\n\t\t\tvalue: hex => styles.ansi256ToAnsi(styles.hexToAnsi256(hex)),\n\t\t\tenumerable: false,\n\t\t},\n\t});\n\n\treturn styles;\n}\n\nconst ansiStyles = assembleStyles();\n\nexport default ansiStyles;\n", "import process from 'node:process';\nimport os from 'node:os';\nimport tty from 'node:tty';\n\n// From: https://github.com/sindresorhus/has-flag/blob/main/index.js\n/// function hasFlag(flag, argv = globalThis.Deno?.args ?? process.argv) {\nfunction hasFlag(flag, argv = globalThis.Deno ? globalThis.Deno.args : process.argv) {\n\tconst prefix = flag.startsWith('-') ? '' : (flag.length === 1 ? '-' : '--');\n\tconst position = argv.indexOf(prefix + flag);\n\tconst terminatorPosition = argv.indexOf('--');\n\treturn position !== -1 && (terminatorPosition === -1 || position < terminatorPosition);\n}\n\nconst {env} = process;\n\nlet flagForceColor;\nif (\n\thasFlag('no-color')\n\t|| hasFlag('no-colors')\n\t|| hasFlag('color=false')\n\t|| hasFlag('color=never')\n) {\n\tflagForceColor = 0;\n} else if (\n\thasFlag('color')\n\t|| hasFlag('colors')\n\t|| hasFlag('color=true')\n\t|| hasFlag('color=always')\n) {\n\tflagForceColor = 1;\n}\n\nfunction envForceColor() {\n\tif ('FORCE_COLOR' in env) {\n\t\tif (env.FORCE_COLOR === 'true') {\n\t\t\treturn 1;\n\t\t}\n\n\t\tif (env.FORCE_COLOR === 'false') {\n\t\t\treturn 0;\n\t\t}\n\n\t\treturn env.FORCE_COLOR.length === 0 ? 1 : Math.min(Number.parseInt(env.FORCE_COLOR, 10), 3);\n\t}\n}\n\nfunction translateLevel(level) {\n\tif (level === 0) {\n\t\treturn false;\n\t}\n\n\treturn {\n\t\tlevel,\n\t\thasBasic: true,\n\t\thas256: level >= 2,\n\t\thas16m: level >= 3,\n\t};\n}\n\nfunction _supportsColor(haveStream, {streamIsTTY, sniffFlags = true} = {}) {\n\tconst noFlagForceColor = envForceColor();\n\tif (noFlagForceColor !== undefined) {\n\t\tflagForceColor = noFlagForceColor;\n\t}\n\n\tconst forceColor = sniffFlags ? flagForceColor : noFlagForceColor;\n\n\tif (forceColor === 0) {\n\t\treturn 0;\n\t}\n\n\tif (sniffFlags) {\n\t\tif (hasFlag('color=16m')\n\t\t\t|| hasFlag('color=full')\n\t\t\t|| hasFlag('color=truecolor')) {\n\t\t\treturn 3;\n\t\t}\n\n\t\tif (hasFlag('color=256')) {\n\t\t\treturn 2;\n\t\t}\n\t}\n\n\t// Check for Azure DevOps pipelines.\n\t// Has to be above the `!streamIsTTY` check.\n\tif ('TF_BUILD' in env && 'AGENT_NAME' in env) {\n\t\treturn 1;\n\t}\n\n\tif (haveStream && !streamIsTTY && forceColor === undefined) {\n\t\treturn 0;\n\t}\n\n\tconst min = forceColor || 0;\n\n\tif (env.TERM === 'dumb') {\n\t\treturn min;\n\t}\n\n\tif (process.platform === 'win32') {\n\t\t// Windows 10 build 10586 is the first Windows release that supports 256 colors.\n\t\t// Windows 10 build 14931 is the first release that supports 16m/TrueColor.\n\t\tconst osRelease = os.release().split('.');\n\t\tif (\n\t\t\tNumber(osRelease[0]) >= 10\n\t\t\t&& Number(osRelease[2]) >= 10_586\n\t\t) {\n\t\t\treturn Number(osRelease[2]) >= 14_931 ? 3 : 2;\n\t\t}\n\n\t\treturn 1;\n\t}\n\n\tif ('CI' in env) {\n\t\tif ('GITHUB_ACTIONS' in env || 'GITEA_ACTIONS' in env) {\n\t\t\treturn 3;\n\t\t}\n\n\t\tif (['TRAVIS', 'CIRCLECI', 'APPVEYOR', 'GITLAB_CI', 'BUILDKITE', 'DRONE'].some(sign => sign in env) || env.CI_NAME === 'codeship') {\n\t\t\treturn 1;\n\t\t}\n\n\t\treturn min;\n\t}\n\n\tif ('TEAMCITY_VERSION' in env) {\n\t\treturn /^(9\\.(0*[1-9]\\d*)\\.|\\d{2,}\\.)/.test(env.TEAMCITY_VERSION) ? 1 : 0;\n\t}\n\n\tif (env.COLORTERM === 'truecolor') {\n\t\treturn 3;\n\t}\n\n\tif (env.TERM === 'xterm-kitty') {\n\t\treturn 3;\n\t}\n\n\tif ('TERM_PROGRAM' in env) {\n\t\tconst version = Number.parseInt((env.TERM_PROGRAM_VERSION || '').split('.')[0], 10);\n\n\t\tswitch (env.TERM_PROGRAM) {\n\t\t\tcase 'iTerm.app': {\n\t\t\t\treturn version >= 3 ? 3 : 2;\n\t\t\t}\n\n\t\t\tcase 'Apple_Terminal': {\n\t\t\t\treturn 2;\n\t\t\t}\n\t\t\t// No default\n\t\t}\n\t}\n\n\tif (/-256(color)?$/i.test(env.TERM)) {\n\t\treturn 2;\n\t}\n\n\tif (/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(env.TERM)) {\n\t\treturn 1;\n\t}\n\n\tif ('COLORTERM' in env) {\n\t\treturn 1;\n\t}\n\n\treturn min;\n}\n\nexport function createSupportsColor(stream, options = {}) {\n\tconst level = _supportsColor(stream, {\n\t\tstreamIsTTY: stream && stream.isTTY,\n\t\t...options,\n\t});\n\n\treturn translateLevel(level);\n}\n\nconst supportsColor = {\n\tstdout: createSupportsColor({isTTY: tty.isatty(1)}),\n\tstderr: createSupportsColor({isTTY: tty.isatty(2)}),\n};\n\nexport default supportsColor;\n", "// TODO: When targeting Node.js 16, use `String.prototype.replaceAll`.\nexport function stringReplaceAll(string, substring, replacer) {\n\tlet index = string.indexOf(substring);\n\tif (index === -1) {\n\t\treturn string;\n\t}\n\n\tconst substringLength = substring.length;\n\tlet endIndex = 0;\n\tlet returnValue = '';\n\tdo {\n\t\treturnValue += string.slice(endIndex, index) + substring + replacer;\n\t\tendIndex = index + substringLength;\n\t\tindex = string.indexOf(substring, endIndex);\n\t} while (index !== -1);\n\n\treturnValue += string.slice(endIndex);\n\treturn returnValue;\n}\n\nexport function stringEncaseCRLFWithFirstIndex(string, prefix, postfix, index) {\n\tlet endIndex = 0;\n\tlet returnValue = '';\n\tdo {\n\t\tconst gotCR = string[index - 1] === '\\r';\n\t\treturnValue += string.slice(endIndex, (gotCR ? index - 1 : index)) + prefix + (gotCR ? '\\r\\n' : '\\n') + postfix;\n\t\tendIndex = index + 1;\n\t\tindex = string.indexOf('\\n', endIndex);\n\t} while (index !== -1);\n\n\treturnValue += string.slice(endIndex);\n\treturn returnValue;\n}\n", "import ansiStyles from '#ansi-styles';\nimport supportsColor from '#supports-color';\nimport { // eslint-disable-line import/order\n\tstringReplaceAll,\n\tstringEncaseCRLFWithFirstIndex,\n} from './utilities.js';\n\nconst {stdout: stdoutColor, stderr: stderrColor} = supportsColor;\n\nconst GENERATOR = Symbol('GENERATOR');\nconst STYLER = Symbol('STYLER');\nconst IS_EMPTY = Symbol('IS_EMPTY');\n\n// `supportsColor.level` → `ansiStyles.color[name]` mapping\nconst levelMapping = [\n\t'ansi',\n\t'ansi',\n\t'ansi256',\n\t'ansi16m',\n];\n\nconst styles = Object.create(null);\n\nconst applyOptions = (object, options = {}) => {\n\tif (options.level && !(Number.isInteger(options.level) && options.level >= 0 && options.level <= 3)) {\n\t\tthrow new Error('The `level` option should be an integer from 0 to 3');\n\t}\n\n\t// Detect level if not set manually\n\tconst colorLevel = stdoutColor ? stdoutColor.level : 0;\n\tobject.level = options.level === undefined ? colorLevel : options.level;\n};\n\nexport class Chalk {\n\tconstructor(options) {\n\t\t// eslint-disable-next-line no-constructor-return\n\t\treturn chalkFactory(options);\n\t}\n}\n\nconst chalkFactory = options => {\n\tconst chalk = (...strings) => strings.join(' ');\n\tapplyOptions(chalk, options);\n\n\tObject.setPrototypeOf(chalk, createChalk.prototype);\n\n\treturn chalk;\n};\n\nfunction createChalk(options) {\n\treturn chalkFactory(options);\n}\n\nObject.setPrototypeOf(createChalk.prototype, Function.prototype);\n\nfor (const [styleName, style] of Object.entries(ansiStyles)) {\n\tstyles[styleName] = {\n\t\tget() {\n\t\t\tconst builder = createBuilder(this, createStyler(style.open, style.close, this[STYLER]), this[IS_EMPTY]);\n\t\t\tObject.defineProperty(this, styleName, {value: builder});\n\t\t\treturn builder;\n\t\t},\n\t};\n}\n\nstyles.visible = {\n\tget() {\n\t\tconst builder = createBuilder(this, this[STYLER], true);\n\t\tObject.defineProperty(this, 'visible', {value: builder});\n\t\treturn builder;\n\t},\n};\n\nconst getModelAnsi = (model, level, type, ...arguments_) => {\n\tif (model === 'rgb') {\n\t\tif (level === 'ansi16m') {\n\t\t\treturn ansiStyles[type].ansi16m(...arguments_);\n\t\t}\n\n\t\tif (level === 'ansi256') {\n\t\t\treturn ansiStyles[type].ansi256(ansiStyles.rgbToAnsi256(...arguments_));\n\t\t}\n\n\t\treturn ansiStyles[type].ansi(ansiStyles.rgbToAnsi(...arguments_));\n\t}\n\n\tif (model === 'hex') {\n\t\treturn getModelAnsi('rgb', level, type, ...ansiStyles.hexToRgb(...arguments_));\n\t}\n\n\treturn ansiStyles[type][model](...arguments_);\n};\n\nconst usedModels = ['rgb', 'hex', 'ansi256'];\n\nfor (const model of usedModels) {\n\tstyles[model] = {\n\t\tget() {\n\t\t\tconst {level} = this;\n\t\t\treturn function (...arguments_) {\n\t\t\t\tconst styler = createStyler(getModelAnsi(model, levelMapping[level], 'color', ...arguments_), ansiStyles.color.close, this[STYLER]);\n\t\t\t\treturn createBuilder(this, styler, this[IS_EMPTY]);\n\t\t\t};\n\t\t},\n\t};\n\n\tconst bgModel = 'bg' + model[0].toUpperCase() + model.slice(1);\n\tstyles[bgModel] = {\n\t\tget() {\n\t\t\tconst {level} = this;\n\t\t\treturn function (...arguments_) {\n\t\t\t\tconst styler = createStyler(getModelAnsi(model, levelMapping[level], 'bgColor', ...arguments_), ansiStyles.bgColor.close, this[STYLER]);\n\t\t\t\treturn createBuilder(this, styler, this[IS_EMPTY]);\n\t\t\t};\n\t\t},\n\t};\n}\n\nconst proto = Object.defineProperties(() => {}, {\n\t...styles,\n\tlevel: {\n\t\tenumerable: true,\n\t\tget() {\n\t\t\treturn this[GENERATOR].level;\n\t\t},\n\t\tset(level) {\n\t\t\tthis[GENERATOR].level = level;\n\t\t},\n\t},\n});\n\nconst createStyler = (open, close, parent) => {\n\tlet openAll;\n\tlet closeAll;\n\tif (parent === undefined) {\n\t\topenAll = open;\n\t\tcloseAll = close;\n\t} else {\n\t\topenAll = parent.openAll + open;\n\t\tcloseAll = close + parent.closeAll;\n\t}\n\n\treturn {\n\t\topen,\n\t\tclose,\n\t\topenAll,\n\t\tcloseAll,\n\t\tparent,\n\t};\n};\n\nconst createBuilder = (self, _styler, _isEmpty) => {\n\t// Single argument is hot path, implicit coercion is faster than anything\n\t// eslint-disable-next-line no-implicit-coercion\n\tconst builder = (...arguments_) => applyStyle(builder, (arguments_.length === 1) ? ('' + arguments_[0]) : arguments_.join(' '));\n\n\t// We alter the prototype because we must return a function, but there is\n\t// no way to create a function with a different prototype\n\tObject.setPrototypeOf(builder, proto);\n\n\tbuilder[GENERATOR] = self;\n\tbuilder[STYLER] = _styler;\n\tbuilder[IS_EMPTY] = _isEmpty;\n\n\treturn builder;\n};\n\nconst applyStyle = (self, string) => {\n\tif (self.level <= 0 || !string) {\n\t\treturn self[IS_EMPTY] ? '' : string;\n\t}\n\n\tlet styler = self[STYLER];\n\n\tif (styler === undefined) {\n\t\treturn string;\n\t}\n\n\tconst {openAll, closeAll} = styler;\n\tif (string.includes('\\u001B')) {\n\t\twhile (styler !== undefined) {\n\t\t\t// Replace any instances already present with a re-opening code\n\t\t\t// otherwise only the part of the string until said closing code\n\t\t\t// will be colored, and the rest will simply be 'plain'.\n\t\t\tstring = stringReplaceAll(string, styler.close, styler.open);\n\n\t\t\tstyler = styler.parent;\n\t\t}\n\t}\n\n\t// We can move both next actions out of loop, because remaining actions in loop won't have\n\t// any/visible effect on parts we add here. Close the styling before a linebreak and reopen\n\t// after next line to fix a bleed issue on macOS: https://github.com/chalk/chalk/pull/92\n\tconst lfIndex = string.indexOf('\\n');\n\tif (lfIndex !== -1) {\n\t\tstring = stringEncaseCRLFWithFirstIndex(string, closeAll, openAll, lfIndex);\n\t}\n\n\treturn openAll + string + closeAll;\n};\n\nObject.defineProperties(createChalk.prototype, styles);\n\nconst chalk = createChalk();\nexport const chalkStderr = createChalk({level: stderrColor ? stderrColor.level : 0});\n\nexport {\n\tmodifierNames,\n\tforegroundColorNames,\n\tbackgroundColorNames,\n\tcolorNames,\n\n\t// TODO: Remove these aliases in the next major version\n\tmodifierNames as modifiers,\n\tforegroundColorNames as foregroundColors,\n\tbackgroundColorNames as backgroundColors,\n\tcolorNames as colors,\n} from './vendor/ansi-styles/index.js';\n\nexport {\n\tstdoutColor as supportsColor,\n\tstderrColor as supportsColorStderr,\n};\n\nexport default chalk;\n"], "names": ["ANSI_BACKGROUND_OFFSET", "wrapAnsi16", "offset", "code", "wrapAnsi256", "wrapAnsi16m", "red", "green", "blue", "styles", "modifierNames", "foregroundColorNames", "backgroundColorNames", "colorNames", "assembleStyles", "codes", "groupName", "group", "styleName", "style", "hex", "matches", "colorString", "character", "integer", "remainder", "value", "result", "ansiStyles", "ansiStyles$1", "hasFlag", "flag", "argv", "process", "prefix", "position", "terminatorPosition", "env", "flagForceColor", "envForceColor", "translateLevel", "level", "_supportsColor", "haveStream", "streamIsTTY", "sniffFlags", "noFlagForceColor", "forceColor", "min", "osRelease", "os", "sign", "version", "createSupportsColor", "stream", "options", "supportsColor", "tty", "supportsColor$1", "stringReplaceAll", "string", "substring", "replacer", "index", "substringLength", "endIndex", "returnValue", "stringEncaseCRLFWithFirstIndex", "postfix", "gotCR", "stdoutColor", "stderrColor", "GENERATOR", "STYLER", "IS_EMPTY", "levelMapping", "applyOptions", "object", "colorLevel", "Chalk", "chalkFactory", "chalk", "strings", "createChalk", "builder", "createBuilder", "createStyler", "getModelAnsi", "model", "type", "arguments_", "usedModels", "styler", "bgModel", "proto", "open", "close", "parent", "openAll", "closeAll", "self", "_styler", "_isEmpty", "applyStyle", "lfIndex", "chalkStderr"], "mappings": "2JAAMA,EAAyB,GAEzBC,EAAa,CAACC,EAAS,IAAMC,GAAQ,QAAUA,EAAOD,CAAM,IAE5DE,EAAc,CAACF,EAAS,IAAMC,GAAQ,QAAU,GAAKD,CAAM,MAAMC,CAAI,IAErEE,EAAc,CAACH,EAAS,IAAM,CAACI,EAAKC,EAAOC,IAAS,QAAU,GAAKN,CAAM,MAAMI,CAAG,IAAIC,CAAK,IAAIC,CAAI,IAEnGC,EAAS,CACd,SAAU,CACT,MAAO,CAAC,EAAG,CAAC,EAEZ,KAAM,CAAC,EAAG,EAAE,EACZ,IAAK,CAAC,EAAG,EAAE,EACX,OAAQ,CAAC,EAAG,EAAE,EACd,UAAW,CAAC,EAAG,EAAE,EACjB,SAAU,CAAC,GAAI,EAAE,EACjB,QAAS,CAAC,EAAG,EAAE,EACf,OAAQ,CAAC,EAAG,EAAE,EACd,cAAe,CAAC,EAAG,EAAE,CACrB,EACD,MAAO,CACN,MAAO,CAAC,GAAI,EAAE,EACd,IAAK,CAAC,GAAI,EAAE,EACZ,MAAO,CAAC,GAAI,EAAE,EACd,OAAQ,CAAC,GAAI,EAAE,EACf,KAAM,CAAC,GAAI,EAAE,EACb,QAAS,CAAC,GAAI,EAAE,EAChB,KAAM,CAAC,GAAI,EAAE,EACb,MAAO,CAAC,GAAI,EAAE,EAGd,YAAa,CAAC,GAAI,EAAE,EACpB,KAAM,CAAC,GAAI,EAAE,EACb,KAAM,CAAC,GAAI,EAAE,EACb,UAAW,CAAC,GAAI,EAAE,EAClB,YAAa,CAAC,GAAI,EAAE,EACpB,aAAc,CAAC,GAAI,EAAE,EACrB,WAAY,CAAC,GAAI,EAAE,EACnB,cAAe,CAAC,GAAI,EAAE,EACtB,WAAY,CAAC,GAAI,EAAE,EACnB,YAAa,CAAC,GAAI,EAAE,CACpB,EACD,QAAS,CACR,QAAS,CAAC,GAAI,EAAE,EAChB,MAAO,CAAC,GAAI,EAAE,EACd,QAAS,CAAC,GAAI,EAAE,EAChB,SAAU,CAAC,GAAI,EAAE,EACjB,OAAQ,CAAC,GAAI,EAAE,EACf,UAAW,CAAC,GAAI,EAAE,EAClB,OAAQ,CAAC,GAAI,EAAE,EACf,QAAS,CAAC,GAAI,EAAE,EAGhB,cAAe,CAAC,IAAK,EAAE,EACvB,OAAQ,CAAC,IAAK,EAAE,EAChB,OAAQ,CAAC,IAAK,EAAE,EAChB,YAAa,CAAC,IAAK,EAAE,EACrB,cAAe,CAAC,IAAK,EAAE,EACvB,eAAgB,CAAC,IAAK,EAAE,EACxB,aAAc,CAAC,IAAK,EAAE,EACtB,gBAAiB,CAAC,IAAK,EAAE,EACzB,aAAc,CAAC,IAAK,EAAE,EACtB,cAAe,CAAC,IAAK,EAAE,CACvB,CACF,EAEaC,EAAgB,OAAO,KAAKD,EAAO,QAAQ,EAC3CE,EAAuB,OAAO,KAAKF,EAAO,KAAK,EAC/CG,EAAuB,OAAO,KAAKH,EAAO,OAAO,EACjDI,EAAa,CAAC,GAAGF,EAAsB,GAAGC,CAAoB,EAE3E,SAASE,GAAiB,CACzB,MAAMC,EAAQ,IAAI,IAElB,SAAW,CAACC,EAAWC,CAAK,IAAK,OAAO,QAAQR,CAAM,EAAG,CACxD,SAAW,CAACS,EAAWC,CAAK,IAAK,OAAO,QAAQF,CAAK,EACpDR,EAAOS,CAAS,EAAI,CACnB,KAAM,QAAUC,EAAM,CAAC,CAAC,IACxB,MAAO,QAAUA,EAAM,CAAC,CAAC,GAC7B,EAEGF,EAAMC,CAAS,EAAIT,EAAOS,CAAS,EAEnCH,EAAM,IAAII,EAAM,CAAC,EAAGA,EAAM,CAAC,CAAC,EAG7B,OAAO,eAAeV,EAAQO,EAAW,CACxC,MAAOC,EACP,WAAY,EACf,CAAG,CACD,CAED,cAAO,eAAeR,EAAQ,QAAS,CACtC,MAAOM,EACP,WAAY,EACd,CAAE,EAEDN,EAAO,MAAM,MAAQ,WACrBA,EAAO,QAAQ,MAAQ,WAEvBA,EAAO,MAAM,KAAOR,IACpBQ,EAAO,MAAM,QAAUL,IACvBK,EAAO,MAAM,QAAUJ,IACvBI,EAAO,QAAQ,KAAOR,EAAWD,CAAsB,EACvDS,EAAO,QAAQ,QAAUL,EAAYJ,CAAsB,EAC3DS,EAAO,QAAQ,QAAUJ,EAAYL,CAAsB,EAG3D,OAAO,iBAAiBS,EAAQ,CAC/B,aAAc,CACb,MAAMH,EAAKC,EAAOC,EAAM,CAGvB,OAAIF,IAAQC,GAASA,IAAUC,EAC1BF,EAAM,EACF,GAGJA,EAAM,IACF,IAGD,KAAK,OAAQA,EAAM,GAAK,IAAO,EAAE,EAAI,IAGtC,GACH,GAAK,KAAK,MAAMA,EAAM,IAAM,CAAC,EAC7B,EAAI,KAAK,MAAMC,EAAQ,IAAM,CAAC,EAC/B,KAAK,MAAMC,EAAO,IAAM,CAAC,CAC5B,EACD,WAAY,EACZ,EACD,SAAU,CACT,MAAMY,EAAK,CACV,MAAMC,EAAU,yBAAyB,KAAKD,EAAI,SAAS,EAAE,CAAC,EAC9D,GAAI,CAACC,EACJ,MAAO,CAAC,EAAG,EAAG,CAAC,EAGhB,GAAI,CAACC,CAAW,EAAID,EAEhBC,EAAY,SAAW,IAC1BA,EAAc,CAAC,GAAGA,CAAW,EAAE,IAAIC,GAAaA,EAAYA,CAAS,EAAE,KAAK,EAAE,GAG/E,MAAMC,EAAU,OAAO,SAASF,EAAa,EAAE,EAE/C,MAAO,CAELE,GAAW,GAAM,IACjBA,GAAW,EAAK,IACjBA,EAAU,GAEf,CACI,EACD,WAAY,EACZ,EACD,aAAc,CACb,MAAOJ,GAAOX,EAAO,aAAa,GAAGA,EAAO,SAASW,CAAG,CAAC,EACzD,WAAY,EACZ,EACD,cAAe,CACd,MAAMjB,EAAM,CACX,GAAIA,EAAO,EACV,MAAO,IAAKA,EAGb,GAAIA,EAAO,GACV,MAAO,KAAMA,EAAO,GAGrB,IAAIG,EACAC,EACAC,EAEJ,GAAIL,GAAQ,IACXG,IAASH,EAAO,KAAO,GAAM,GAAK,IAClCI,EAAQD,EACRE,EAAOF,MACD,CACNH,GAAQ,GAER,MAAMsB,EAAYtB,EAAO,GAEzBG,EAAM,KAAK,MAAMH,EAAO,EAAE,EAAI,EAC9BI,EAAQ,KAAK,MAAMkB,EAAY,CAAC,EAAI,EACpCjB,EAAQiB,EAAY,EAAK,CACzB,CAED,MAAMC,EAAQ,KAAK,IAAIpB,EAAKC,EAAOC,CAAI,EAAI,EAE3C,GAAIkB,IAAU,EACb,MAAO,IAIR,IAAIC,EAAS,IAAO,KAAK,MAAMnB,CAAI,GAAK,EAAM,KAAK,MAAMD,CAAK,GAAK,EAAK,KAAK,MAAMD,CAAG,GAEtF,OAAIoB,IAAU,IACbC,GAAU,IAGJA,CACP,EACD,WAAY,EACZ,EACD,UAAW,CACV,MAAO,CAACrB,EAAKC,EAAOC,IAASC,EAAO,cAAcA,EAAO,aAAaH,EAAKC,EAAOC,CAAI,CAAC,EACvF,WAAY,EACZ,EACD,UAAW,CACV,MAAOY,GAAOX,EAAO,cAAcA,EAAO,aAAaW,CAAG,CAAC,EAC3D,WAAY,EACZ,CACH,CAAE,EAEMX,CACR,CAEA,MAAMmB,EAAad,EAAc,EAEjCe,EAAeD,ECxNf,SAASE,EAAQC,EAAMC,EAAO,WAAW,KAAO,WAAW,KAAK,KAAOC,EAAQ,KAAM,CACpF,MAAMC,EAASH,EAAK,WAAW,GAAG,EAAI,GAAMA,EAAK,SAAW,EAAI,IAAM,KAChEI,EAAWH,EAAK,QAAQE,EAASH,CAAI,EACrCK,EAAqBJ,EAAK,QAAQ,IAAI,EAC5C,OAAOG,IAAa,KAAOC,IAAuB,IAAMD,EAAWC,EACpE,CAEA,KAAM,CAAC,IAAAC,CAAG,EAAIJ,EAEd,IAAIK,EAEHR,EAAQ,UAAU,GACfA,EAAQ,WAAW,GACnBA,EAAQ,aAAa,GACrBA,EAAQ,aAAa,EAExBQ,EAAiB,GAEjBR,EAAQ,OAAO,GACZA,EAAQ,QAAQ,GAChBA,EAAQ,YAAY,GACpBA,EAAQ,cAAc,KAEzBQ,EAAiB,GAGlB,SAASC,GAAgB,CACxB,GAAI,gBAAiBF,EACpB,OAAIA,EAAI,cAAgB,OAChB,EAGJA,EAAI,cAAgB,QAChB,EAGDA,EAAI,YAAY,SAAW,EAAI,EAAI,KAAK,IAAI,OAAO,SAASA,EAAI,YAAa,EAAE,EAAG,CAAC,CAE5F,CAEA,SAASG,EAAeC,EAAO,CAC9B,OAAIA,IAAU,EACN,GAGD,CACN,MAAAA,EACA,SAAU,GACV,OAAQA,GAAS,EACjB,OAAQA,GAAS,CACnB,CACA,CAEA,SAASC,EAAeC,EAAY,CAAC,YAAAC,EAAa,WAAAC,EAAa,EAAI,EAAI,GAAI,CAC1E,MAAMC,EAAmBP,IACrBO,IAAqB,SACxBR,EAAiBQ,GAGlB,MAAMC,EAAaF,EAAaP,EAAiBQ,EAEjD,GAAIC,IAAe,EAClB,MAAO,GAGR,GAAIF,EAAY,CACf,GAAIf,EAAQ,WAAW,GACnBA,EAAQ,YAAY,GACpBA,EAAQ,iBAAiB,EAC5B,MAAO,GAGR,GAAIA,EAAQ,WAAW,EACtB,MAAO,EAER,CAID,GAAI,aAAcO,GAAO,eAAgBA,EACxC,MAAO,GAGR,GAAIM,GAAc,CAACC,GAAeG,IAAe,OAChD,MAAO,GAGR,MAAMC,EAAMD,GAAc,EAE1B,GAAIV,EAAI,OAAS,OAChB,OAAOW,EAGR,GAAIf,EAAQ,WAAa,QAAS,CAGjC,MAAMgB,EAAYC,EAAG,QAAS,EAAC,MAAM,GAAG,EACxC,OACC,OAAOD,EAAU,CAAC,CAAC,GAAK,IACrB,OAAOA,EAAU,CAAC,CAAC,GAAK,MAEpB,OAAOA,EAAU,CAAC,CAAC,GAAK,MAAS,EAAI,EAGtC,CACP,CAED,GAAI,OAAQZ,EACX,MAAI,mBAAoBA,GAAO,kBAAmBA,EAC1C,EAGJ,CAAC,SAAU,WAAY,WAAY,YAAa,YAAa,OAAO,EAAE,KAAKc,GAAQA,KAAQd,CAAG,GAAKA,EAAI,UAAY,WAC/G,EAGDW,EAGR,GAAI,qBAAsBX,EACzB,MAAO,gCAAgC,KAAKA,EAAI,gBAAgB,EAAI,EAAI,EAOzE,GAJIA,EAAI,YAAc,aAIlBA,EAAI,OAAS,cAChB,MAAO,GAGR,GAAI,iBAAkBA,EAAK,CAC1B,MAAMe,EAAU,OAAO,UAAUf,EAAI,sBAAwB,IAAI,MAAM,GAAG,EAAE,CAAC,EAAG,EAAE,EAElF,OAAQA,EAAI,aAAY,CACvB,IAAK,YACJ,OAAOe,GAAW,EAAI,EAAI,EAG3B,IAAK,iBACJ,MAAO,EAGR,CACD,CAED,MAAI,iBAAiB,KAAKf,EAAI,IAAI,EAC1B,EAGJ,8DAA8D,KAAKA,EAAI,IAAI,GAI3E,cAAeA,EACX,EAGDW,CACR,CAEO,SAASK,EAAoBC,EAAQC,EAAU,GAAI,CACzD,MAAMd,EAAQC,EAAeY,EAAQ,CACpC,YAAaA,GAAUA,EAAO,MAC9B,GAAGC,CACL,CAAE,EAED,OAAOf,EAAeC,CAAK,CAC5B,CAEA,MAAMe,EAAgB,CACrB,OAAQH,EAAoB,CAAC,MAAOI,EAAI,OAAO,CAAC,CAAC,CAAC,EAClD,OAAQJ,EAAoB,CAAC,MAAOI,EAAI,OAAO,CAAC,CAAC,CAAC,CACnD,EAEAC,EAAeF,ECpLR,SAASG,EAAiBC,EAAQC,EAAWC,EAAU,CAC7D,IAAIC,EAAQH,EAAO,QAAQC,CAAS,EACpC,GAAIE,IAAU,GACb,OAAOH,EAGR,MAAMI,EAAkBH,EAAU,OAClC,IAAII,EAAW,EACXC,EAAc,GAClB,GACCA,GAAeN,EAAO,MAAMK,EAAUF,CAAK,EAAIF,EAAYC,EAC3DG,EAAWF,EAAQC,EACnBD,EAAQH,EAAO,QAAQC,EAAWI,CAAQ,QAClCF,IAAU,IAEnB,OAAAG,GAAeN,EAAO,MAAMK,CAAQ,EAC7BC,CACR,CAEO,SAASC,EAA+BP,EAAQ1B,EAAQkC,EAASL,EAAO,CAC9E,IAAIE,EAAW,EACXC,EAAc,GAClB,EAAG,CACF,MAAMG,EAAQT,EAAOG,EAAQ,CAAC,IAAM,KACpCG,GAAeN,EAAO,MAAMK,EAAWI,EAAQN,EAAQ,EAAIA,CAAO,EAAG7B,GAAUmC,EAAQ;AAAA,EAAS;AAAA,GAAQD,EACxGH,EAAWF,EAAQ,EACnBA,EAAQH,EAAO,QAAQ;AAAA,EAAMK,CAAQ,CACvC,OAAUF,IAAU,IAEnB,OAAAG,GAAeN,EAAO,MAAMK,CAAQ,EAC7BC,CACR,CCzBK,KAAC,CAAC,OAAQI,EAAa,OAAQC,CAAW,EAAIf,EAE7CgB,EAAY,OAAO,WAAW,EAC9BC,EAAS,OAAO,QAAQ,EACxBC,EAAW,OAAO,UAAU,EAG5BC,EAAe,CACpB,OACA,OACA,UACA,SACD,EAEMlE,EAAS,OAAO,OAAO,IAAI,EAE3BmE,EAAe,CAACC,EAAQtB,EAAU,KAAO,CAC9C,GAAIA,EAAQ,OAAS,EAAE,OAAO,UAAUA,EAAQ,KAAK,GAAKA,EAAQ,OAAS,GAAKA,EAAQ,OAAS,GAChG,MAAM,IAAI,MAAM,qDAAqD,EAItE,MAAMuB,EAAaR,EAAcA,EAAY,MAAQ,EACrDO,EAAO,MAAQtB,EAAQ,QAAU,OAAYuB,EAAavB,EAAQ,KACnE,EAEO,MAAMwB,CAAM,CAClB,YAAYxB,EAAS,CAEpB,OAAOyB,EAAazB,CAAO,CAC3B,CACF,CAEA,MAAMyB,EAAezB,GAAW,CAC/B,MAAM0B,EAAQ,IAAIC,IAAYA,EAAQ,KAAK,GAAG,EAC9C,OAAAN,EAAaK,EAAO1B,CAAO,EAE3B,OAAO,eAAe0B,EAAOE,EAAY,SAAS,EAE3CF,CACR,EAEA,SAASE,EAAY5B,EAAS,CAC7B,OAAOyB,EAAazB,CAAO,CAC5B,CAEA,OAAO,eAAe4B,EAAY,UAAW,SAAS,SAAS,EAE/D,SAAW,CAACjE,EAAWC,CAAK,IAAK,OAAO,QAAQS,CAAU,EACzDnB,EAAOS,CAAS,EAAI,CACnB,KAAM,CACL,MAAMkE,EAAUC,EAAc,KAAMC,EAAanE,EAAM,KAAMA,EAAM,MAAO,KAAKsD,CAAM,CAAC,EAAG,KAAKC,CAAQ,CAAC,EACvG,cAAO,eAAe,KAAMxD,EAAW,CAAC,MAAOkE,CAAO,CAAC,EAChDA,CACP,CACH,EAGA3E,EAAO,QAAU,CAChB,KAAM,CACL,MAAM2E,EAAUC,EAAc,KAAM,KAAKZ,CAAM,EAAG,EAAI,EACtD,cAAO,eAAe,KAAM,UAAW,CAAC,MAAOW,CAAO,CAAC,EAChDA,CACP,CACF,EAEA,MAAMG,EAAe,CAACC,EAAO/C,EAAOgD,KAASC,IACxCF,IAAU,MACT/C,IAAU,UACNb,EAAW6D,CAAI,EAAE,QAAQ,GAAGC,CAAU,EAG1CjD,IAAU,UACNb,EAAW6D,CAAI,EAAE,QAAQ7D,EAAW,aAAa,GAAG8D,CAAU,CAAC,EAGhE9D,EAAW6D,CAAI,EAAE,KAAK7D,EAAW,UAAU,GAAG8D,CAAU,CAAC,EAG7DF,IAAU,MACND,EAAa,MAAO9C,EAAOgD,EAAM,GAAG7D,EAAW,SAAS,GAAG8D,CAAU,CAAC,EAGvE9D,EAAW6D,CAAI,EAAED,CAAK,EAAE,GAAGE,CAAU,EAGvCC,EAAa,CAAC,MAAO,MAAO,SAAS,EAE3C,UAAWH,KAASG,EAAY,CAC/BlF,EAAO+E,CAAK,EAAI,CACf,KAAM,CACL,KAAM,CAAC,MAAA/C,CAAK,EAAI,KAChB,OAAO,YAAaiD,EAAY,CAC/B,MAAME,EAASN,EAAaC,EAAaC,EAAOb,EAAalC,CAAK,EAAG,QAAS,GAAGiD,CAAU,EAAG9D,EAAW,MAAM,MAAO,KAAK6C,CAAM,CAAC,EAClI,OAAOY,EAAc,KAAMO,EAAQ,KAAKlB,CAAQ,CAAC,CACrD,CACG,CACH,EAEC,MAAMmB,EAAU,KAAOL,EAAM,CAAC,EAAE,YAAW,EAAKA,EAAM,MAAM,CAAC,EAC7D/E,EAAOoF,CAAO,EAAI,CACjB,KAAM,CACL,KAAM,CAAC,MAAApD,CAAK,EAAI,KAChB,OAAO,YAAaiD,EAAY,CAC/B,MAAME,EAASN,EAAaC,EAAaC,EAAOb,EAAalC,CAAK,EAAG,UAAW,GAAGiD,CAAU,EAAG9D,EAAW,QAAQ,MAAO,KAAK6C,CAAM,CAAC,EACtI,OAAOY,EAAc,KAAMO,EAAQ,KAAKlB,CAAQ,CAAC,CACrD,CACG,CACH,CACA,CAEA,MAAMoB,EAAQ,OAAO,iBAAiB,IAAM,GAAI,CAC/C,GAAGrF,EACH,MAAO,CACN,WAAY,GACZ,KAAM,CACL,OAAO,KAAK+D,CAAS,EAAE,KACvB,EACD,IAAI/B,EAAO,CACV,KAAK+B,CAAS,EAAE,MAAQ/B,CACxB,CACD,CACF,CAAC,EAEK6C,EAAe,CAACS,EAAMC,EAAOC,IAAW,CAC7C,IAAIC,EACAC,EACJ,OAAIF,IAAW,QACdC,EAAUH,EACVI,EAAWH,IAEXE,EAAUD,EAAO,QAAUF,EAC3BI,EAAWH,EAAQC,EAAO,UAGpB,CACN,KAAAF,EACA,MAAAC,EACA,QAAAE,EACA,SAAAC,EACA,OAAAF,CACF,CACA,EAEMZ,EAAgB,CAACe,EAAMC,EAASC,IAAa,CAGlD,MAAMlB,EAAU,IAAIM,IAAea,EAAWnB,EAAUM,EAAW,SAAW,EAAM,GAAKA,EAAW,CAAC,EAAKA,EAAW,KAAK,GAAG,CAAC,EAI9H,cAAO,eAAeN,EAASU,CAAK,EAEpCV,EAAQZ,CAAS,EAAI4B,EACrBhB,EAAQX,CAAM,EAAI4B,EAClBjB,EAAQV,CAAQ,EAAI4B,EAEblB,CACR,EAEMmB,EAAa,CAACH,EAAMxC,IAAW,CACpC,GAAIwC,EAAK,OAAS,GAAK,CAACxC,EACvB,OAAOwC,EAAK1B,CAAQ,EAAI,GAAKd,EAG9B,IAAIgC,EAASQ,EAAK3B,CAAM,EAExB,GAAImB,IAAW,OACd,OAAOhC,EAGR,KAAM,CAAC,QAAAsC,EAAS,SAAAC,CAAQ,EAAIP,EAC5B,GAAIhC,EAAO,SAAS,MAAQ,EAC3B,KAAOgC,IAAW,QAIjBhC,EAASD,EAAiBC,EAAQgC,EAAO,MAAOA,EAAO,IAAI,EAE3DA,EAASA,EAAO,OAOlB,MAAMY,EAAU5C,EAAO,QAAQ;AAAA,CAAI,EACnC,OAAI4C,IAAY,KACf5C,EAASO,EAA+BP,EAAQuC,EAAUD,EAASM,CAAO,GAGpEN,EAAUtC,EAASuC,CAC3B,EAEA,OAAO,iBAAiBhB,EAAY,UAAW1E,CAAM,EAEhD,MAACwE,EAAQE,EAAc,EACfsB,EAActB,EAAY,CAAC,MAAOZ,EAAcA,EAAY,MAAQ,CAAC,CAAC", "x_google_ignoreList": [0, 1, 2, 3]}