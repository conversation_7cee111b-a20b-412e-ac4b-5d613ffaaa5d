{"version": 3, "sources": ["visitor/typecomparator.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,cAAc,CAAC;AAC7C,OAAO,EACH,QAAQ,EAAE,OAAO,EAAE,UAAU,EAC7B,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAC7G,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAChC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAC7D,KAAK,EAAE,OAAO,EAAE,eAAe,EAC/B,QAAQ,EAAE,eAAe,EAAE,iBAAiB,EAC5C,IAAI,EAAE,UAAU,EAAE,eAAe,EAAE,eAAe,EAAE,cAAc,EAClE,SAAS,EAAE,eAAe,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,mBAAmB,EAC3F,QAAQ,EAAE,cAAc,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,kBAAkB,EACtF,KAAK,EAAE,UAAU,EAAE,WAAW,EACjC,MAAM,YAAY,CAAC;AAEpB,cAAc;AACd,MAAM,WAAW,cAAe,SAAQ,OAAO;IAC3C,KAAK,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACxE,SAAS,CAAC,CAAC,SAAS,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,GAAG,IAAI,GAAG,OAAO,EAAE,CAAC;IACjF,UAAU,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,KAAK,KAAK,IAAI,CAAC,CAAC;IACvG,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACxE,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACxE,QAAQ,CAAC,CAAC,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACtE,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACxE,UAAU,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC1E,UAAU,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC1E,UAAU,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC1E,UAAU,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC1E,WAAW,CAAC,CAAC,SAAS,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC5E,WAAW,CAAC,CAAC,SAAS,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC5E,WAAW,CAAC,CAAC,SAAS,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC5E,UAAU,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC1E,YAAY,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC9E,YAAY,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC9E,YAAY,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC9E,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACxE,cAAc,CAAC,CAAC,SAAS,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAClF,WAAW,CAAC,CAAC,SAAS,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC5E,gBAAgB,CAAC,CAAC,SAAS,WAAW,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACtF,oBAAoB,CAAC,CAAC,SAAS,eAAe,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC9F,SAAS,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACzE,YAAY,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC9E,oBAAoB,CAAC,CAAC,SAAS,eAAe,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC9F,cAAc,CAAC,CAAC,SAAS,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAClF,oBAAoB,CAAC,CAAC,SAAS,eAAe,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC9F,yBAAyB,CAAC,CAAC,SAAS,oBAAoB,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACxG,yBAAyB,CAAC,CAAC,SAAS,oBAAoB,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACxG,wBAAwB,CAAC,CAAC,SAAS,mBAAmB,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACtG,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACxE,eAAe,CAAC,CAAC,SAAS,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACpF,oBAAoB,CAAC,CAAC,SAAS,eAAe,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC9F,oBAAoB,CAAC,CAAC,SAAS,eAAe,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC9F,mBAAmB,CAAC,CAAC,SAAS,cAAc,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC5F,YAAY,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC9E,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACxE,WAAW,CAAC,CAAC,SAAS,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC5E,UAAU,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC1E,eAAe,CAAC,CAAC,SAAS,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACpF,gBAAgB,CAAC,CAAC,SAAS,WAAW,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACtF,eAAe,CAAC,CAAC,SAAS,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACpF,aAAa,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAChF,oBAAoB,CAAC,CAAC,SAAS,eAAe,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC9F,sBAAsB,CAAC,CAAC,SAAS,iBAAiB,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAClG,aAAa,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAChF,mBAAmB,CAAC,CAAC,SAAS,cAAc,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC5F,wBAAwB,CAAC,CAAC,SAAS,mBAAmB,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACtG,wBAAwB,CAAC,CAAC,SAAS,mBAAmB,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACtG,uBAAuB,CAAC,CAAC,SAAS,kBAAkB,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IACpG,kBAAkB,CAAC,CAAC,SAAS,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;IAC1F,QAAQ,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;CAC1E;AAED,cAAc;AACd,qBAAa,cAAe,SAAQ,OAAO;IACvC,cAAc,CAAC,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC;IAM/F,iBAAiB,CAAC,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;IAQzH,aAAa,CAAC,CAAC,SAAS,QAAQ,GAAG,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC;CAQpG;AA6KD,cAAc;AACd,eAAO,MAAM,QAAQ,gBAAuB,CAAC;AAE7C,wBAAgB,cAAc,CAAC,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC,CAE9G;AAED,wBAAgB,aAAa,CAAC,CAAC,SAAS,QAAQ,GAAG,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,CAEhH;AAED,wBAAgB,YAAY,CAAC,CAAC,SAAS,QAAQ,GAAG,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,KAAK,IAAI,CAAC,CAE5F", "file": "typecomparator.d.ts", "sourceRoot": "../src"}