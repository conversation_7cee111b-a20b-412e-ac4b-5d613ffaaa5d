{"version": 3, "sources": ["fb/sparse-tensor-index-csf.ts"], "names": [], "mappings": "AAAA,qEAAqE;AAErE,OAAO,KAAK,WAAW,MAAM,aAAa,CAAC;AAE3C,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;AAG/B;;GAEG;AACH,MAAM,OAAO,oBAAoB;IAAjC;QACE,OAAE,GAAgC,IAAI,CAAC;QACvC,WAAM,GAAG,CAAC,CAAC;IAiMb,CAAC;IAhMC,MAAM,CAAC,CAAQ,EAAE,EAAyB;QAC1C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,6BAA6B,CAAC,EAAyB,EAAE,GAAyB;QACvF,OAAO,CAAC,GAAG,IAAI,IAAI,oBAAoB,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACrG,CAAC;IAED,MAAM,CAAC,yCAAyC,CAAC,EAAyB,EAAE,GAAyB;QACnG,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,IAAI,IAAI,oBAAoB,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACrG,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IACH,UAAU,CAAC,GAAQ;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,EAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACxG,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACH,aAAa,CAAC,KAAa,EAAE,GAAW;QACtC,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE,IAAI,CAAC,EAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACtH,CAAC;IAED,mBAAmB;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,GAAQ;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,EAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACxG,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,cAAc,CAAC,KAAa,EAAE,GAAW;QACvC,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE,IAAI,CAAC,EAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACtH,CAAC;IAED,oBAAoB;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;;OAOG;IACH,SAAS,CAAC,KAAa;QACrB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9F,CAAC;IAED,eAAe;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;IAED,cAAc;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAG,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAG,CAAC,KAAK,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,EAAG,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrL,CAAC;IAED,MAAM,CAAC,yBAAyB,CAAC,OAA2B;QAC1D,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,OAA2B,EAAE,gBAAmC;QACnF,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAC;IACjD,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,OAA2B,EAAE,mBAAsC;QACzF,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,mBAAmB,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,MAAM,CAAC,wBAAwB,CAAC,OAA2B,EAAE,QAAe;QAC1E,OAAO,CAAC,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IACvC,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,OAA2B,EAAE,iBAAoC;QACrF,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,iBAAiB,EAAE,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,OAA2B,EAAE,oBAAuC;QAC3F,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,oBAAoB,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,CAAC,yBAAyB,CAAC,OAA2B,EAAE,QAAe;QAC3E,OAAO,CAAC,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IACvC,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAA2B,EAAE,eAAkC;QACjF,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IAChD,CAAC;IAOD,MAAM,CAAC,qBAAqB,CAAC,OAA2B,EAAE,IAAmC;QAC3F,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACvC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAE,CAAC,CAAC;QAC7B,CAAC;QACD,OAAO,OAAO,CAAC,SAAS,EAAE,CAAC;IAC7B,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,OAA2B,EAAE,QAAe;QACtE,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,OAA2B;QACxD,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA,CAAC,aAAa;QAC9C,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA,CAAC,gBAAgB;QACjD,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA,CAAC,cAAc;QAC/C,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA,CAAC,iBAAiB;QACnD,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA,CAAC,YAAY;QAC9C,OAAO,MAAM,CAAC;IAChB,CAAC;CAEA", "file": "sparse-tensor-index-csf.mjs", "sourceRoot": "../src"}