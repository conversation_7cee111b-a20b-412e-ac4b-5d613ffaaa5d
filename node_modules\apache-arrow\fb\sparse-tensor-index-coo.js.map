{"version": 3, "sources": ["fb/sparse-tensor-index-coo.ts"], "names": [], "mappings": ";AAAA,qEAAqE;;;AAErE,2CAA2C;AAE3C,2CAAqC;AACrC,qCAA+B;AAG/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,MAAa,oBAAoB;IAAjC;QACE,OAAE,GAAgC,IAAI,CAAC;QACvC,WAAM,GAAG,CAAC,CAAC;IAiGb,CAAC;IAhGC,MAAM,CAAC,CAAQ,EAAE,EAAyB;QAC1C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,6BAA6B,CAAC,EAAyB,EAAE,GAAyB;QACvF,OAAO,CAAC,GAAG,IAAI,IAAI,oBAAoB,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACrG,CAAC;IAED,MAAM,CAAC,yCAAyC,CAAC,EAAyB,EAAE,GAAyB;QACnG,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,IAAI,IAAI,oBAAoB,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACrG,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,GAAQ;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,YAAG,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,EAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACxG,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,KAAa;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACtG,CAAC;IAED,oBAAoB;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,GAAW;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,kBAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE,IAAI,CAAC,EAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACtF,CAAC;IAED;;;;;;OAMG;IACH,WAAW;QACT,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACpE,CAAC;IAED,MAAM,CAAC,yBAAyB,CAAC,OAA2B;QAC1D,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,OAA2B,EAAE,iBAAoC;QACrF,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,iBAAiB,EAAE,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,OAA2B,EAAE,oBAAuC;QAC3F,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,oBAAoB,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,CAAC,0BAA0B,CAAC,OAA2B,EAAE,IAAa;QAC1E,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACvC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAE,CAAC,CAAC;QAC7B,CAAC;QACD,OAAO,OAAO,CAAC,SAAS,EAAE,CAAC;IAC7B,CAAC;IAED,MAAM,CAAC,yBAAyB,CAAC,OAA2B,EAAE,QAAe;QAC3E,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,OAA2B,EAAE,mBAAsC;QACzF,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,mBAAmB,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,OAA2B,EAAE,WAAmB;QACpE,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,OAA2B;QACxD,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA,CAAC,cAAc;QAC/C,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA,CAAC,gBAAgB;QACjD,OAAO,MAAM,CAAC;IAChB,CAAC;CAEA;AAnGD,oDAmGC", "file": "sparse-tensor-index-coo.js", "sourceRoot": "../src"}