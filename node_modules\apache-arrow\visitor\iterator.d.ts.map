{"version": 3, "sources": ["visitor/iterator.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EAAE,IAAI,EAAa,MAAM,YAAY,CAAC;AAC7C,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EACH,QAAQ,EAAE,UAAU,EACpB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAC7G,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAChC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAC7D,KAAK,EAAE,OAAO,EAAE,eAAe,EAC/B,QAAQ,EAAE,eAAe,EAAE,iBAAiB,EAC5C,IAAI,EAAE,UAAU,EAAE,eAAe,EAAE,eAAe,EAAE,cAAc,EAClE,SAAS,EAAE,eAAe,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,mBAAmB,EAC3F,QAAQ,EAAE,cAAc,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,kBAAkB,EACtF,KAAK,EAAE,UAAU,EAAE,WAAW,EACjC,MAAM,YAAY,CAAC;AAGpB,cAAc;AACd,MAAM,WAAW,eAAgB,SAAQ,OAAO;IAC5C,KAAK,CAAC,CAAC,SAAS,MAAM,EAAE,IAAI,EAAE,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACvE,SAAS,CAAC,CAAC,SAAS,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IAChF,UAAU,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACjH,UAAU,CAAC,CAAC,SAAS,IAAI,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACjI,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACnF,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACnF,QAAQ,CAAC,CAAC,SAAS,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACjF,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACnF,UAAU,CAAC,CAAC,SAAS,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACrF,UAAU,CAAC,CAAC,SAAS,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACrF,UAAU,CAAC,CAAC,SAAS,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACrF,UAAU,CAAC,CAAC,SAAS,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACrF,WAAW,CAAC,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACvF,WAAW,CAAC,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACvF,WAAW,CAAC,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACvF,UAAU,CAAC,CAAC,SAAS,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACrF,YAAY,CAAC,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACzF,YAAY,CAAC,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACzF,YAAY,CAAC,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACzF,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACnF,cAAc,CAAC,CAAC,SAAS,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IAC7F,WAAW,CAAC,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACvF,gBAAgB,CAAC,CAAC,SAAS,WAAW,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACjG,oBAAoB,CAAC,CAAC,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACzG,SAAS,CAAC,CAAC,SAAS,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACpF,YAAY,CAAC,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACzF,oBAAoB,CAAC,CAAC,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACzG,cAAc,CAAC,CAAC,SAAS,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IAC7F,oBAAoB,CAAC,CAAC,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACzG,yBAAyB,CAAC,CAAC,SAAS,oBAAoB,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACnH,yBAAyB,CAAC,CAAC,SAAS,oBAAoB,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACnH,wBAAwB,CAAC,CAAC,SAAS,mBAAmB,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACjH,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACnF,eAAe,CAAC,CAAC,SAAS,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IAC/F,oBAAoB,CAAC,CAAC,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACzG,oBAAoB,CAAC,CAAC,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACzG,mBAAmB,CAAC,CAAC,SAAS,cAAc,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACvG,YAAY,CAAC,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACzF,SAAS,CAAC,CAAC,SAAS,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACnF,WAAW,CAAC,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACvF,UAAU,CAAC,CAAC,SAAS,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACrF,eAAe,CAAC,CAAC,SAAS,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IAC/F,gBAAgB,CAAC,CAAC,SAAS,WAAW,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACjG,eAAe,CAAC,CAAC,SAAS,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IAC/F,aAAa,CAAC,CAAC,SAAS,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IAC3F,oBAAoB,CAAC,CAAC,SAAS,eAAe,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACzG,sBAAsB,CAAC,CAAC,SAAS,iBAAiB,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IAC7G,aAAa,CAAC,CAAC,SAAS,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IAC3F,mBAAmB,CAAC,CAAC,SAAS,cAAc,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACvG,wBAAwB,CAAC,CAAC,SAAS,mBAAmB,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACjH,wBAAwB,CAAC,CAAC,SAAS,mBAAmB,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACjH,uBAAuB,CAAC,CAAC,SAAS,kBAAkB,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IAC/G,kBAAkB,CAAC,CAAC,SAAS,aAAa,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IACrG,QAAQ,CAAC,CAAC,SAAS,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;CACrF;AAED,cAAc;AACd,qBAAa,eAAgB,SAAQ,OAAO;CAAI;AAwGhD,cAAc;AACd,eAAO,MAAM,QAAQ,iBAAwB,CAAC", "file": "iterator.d.ts", "sourceRoot": "../src"}