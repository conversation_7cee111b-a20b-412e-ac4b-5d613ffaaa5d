"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const package_managers_1 = __importDefault(require("../package-managers"));
const programError_1 = __importDefault(require("./programError"));
/**
 * Resolves the package manager from a string or object. Throws an error if an invalid packageManager is provided.
 *
 * @param packageManagerNameOrObject
 * @param packageManagerNameOrObject.global
 * @param packageManagerNameOrObject.packageManager
 * @returns
 */
function getPackageManager(options, name) {
    // default to npm
    if (!name || name === 'deno') {
        return package_managers_1.default.npm;
    }
    else if (options.registryType === 'json') {
        return package_managers_1.default.staticRegistry;
    }
    if (!package_managers_1.default[name]) {
        (0, programError_1.default)(options, `Invalid package manager: ${name}`);
    }
    return package_managers_1.default[name];
}
exports.default = getPackageManager;
//# sourceMappingURL=getPackageManager.js.map