import { Options } from 'pacote';
import { Index } from '../types/IndexType';
import { NpmOptions } from '../types/NpmOptions';
/** Spawn bun. */
declare function spawnBun(args: string | string[], npmOptions?: NpmOptions, spawnOptions?: Index<any>): Promise<string>;
/** Returns the global directory of bun. */
export declare const defaultPrefix: (options: Options) => Promise<string | undefined>;
/**
 * (Bun) Fetches the list of all installed packages.
 */
export declare const list: (options?: Options) => Promise<Index<string | undefined>>;
export declare const greatest: import("../types/GetVersion").GetVersion;
export declare const latest: import("../types/GetVersion").GetVersion;
export declare const minor: import("../types/GetVersion").GetVersion;
export declare const newest: import("../types/GetVersion").GetVersion;
export declare const patch: import("../types/GetVersion").GetVersion;
export declare const semver: import("../types/GetVersion").GetVersion;
export default spawnBun;
