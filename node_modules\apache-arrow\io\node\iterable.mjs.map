{"version": 3, "sources": ["io/node/iterable.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;AAErB,OAAO,EAAE,QAAQ,EAAuC,MAAM,aAAa,CAAC;AAC5E,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AASnE,cAAc;AACd,MAAM,UAAU,YAAY,CAAI,MAAsC,EAAE,OAAyB;IAC7F,IAAI,eAAe,CAAI,MAAM,CAAC,EAAE,CAAC;QAAC,OAAO,IAAI,qBAAqB,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,EAA4B,EAAE,OAAO,CAAC,CAAC;IAAC,CAAC;IACxI,IAAI,UAAU,CAAI,MAAM,CAAC,EAAE,CAAC;QAAC,OAAO,IAAI,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAuB,EAAE,OAAO,CAAC,CAAC;IAAC,CAAC;IACpH,0BAA0B;IAC1B,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;AACvF,CAAC;AAED,cAAc;AACd,MAAM,gBAA6C,SAAQ,QAAQ;IAI/D,YAAY,EAAqB,EAAE,OAAyB;QACxD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;IACtD,CAAC;IACD,KAAK,CAAC,IAAY;QACd,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;QAC1B,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC;YACjD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;IACD,QAAQ,CAAC,CAAe,EAAE,EAA6B;QACnD,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;QAC1B,IAAI,EAAO,CAAC;QACZ,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC;QAChD,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAChB,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;IACnB,CAAC;IACO,KAAK,CAAC,IAAY,EAAE,EAAqB;QAC7C,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;QAC3B,IAAI,CAAC,GAA6B,IAAI,CAAC;QACvC,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC5D,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACf,IAAI,IAAI,CAAC,EAAE,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzE,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;gBAAC,MAAM;YAAC,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,IAAI,KAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC;YAC3D,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC;QAC7B,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;IAC1B,CAAC;CACJ;AAED,cAAc;AACd,MAAM,qBAAkD,SAAQ,QAAQ;IAIpE,YAAY,EAA0B,EAAE,OAAyB;QAC7D,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;IACtD,CAAC;IACD,KAAK,CAAC,IAAY;QACd,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;QAC1B,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC;YACjD,CAAC,GAAS,EAAE,gDAAC,OAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA,GAAA,CAAC,EAAE,CAAC;QAC/D,CAAC;IACL,CAAC;IACD,QAAQ,CAAC,CAAe,EAAE,EAA6B;QACnD,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;QAC1B,IAAI,EAAO,CAAC;QACZ,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC;QAChD,CAAA,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,KAAI,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IACnE,CAAC;IACa,KAAK,CAAC,IAAY,EAAE,EAA0B;;YACxD,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC;YAC3B,IAAI,CAAC,GAA6B,IAAI,CAAC;YACvC,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAClE,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;oBACf,IAAI,IAAI,CAAC,EAAE,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzE,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;oBAAC,MAAM;gBAAC,CAAC;YACpD,CAAC;YACD,IAAI,CAAC,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,IAAI,KAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC;gBAC3D,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC;YAC7B,CAAC;YACD,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC1B,CAAC;KAAA;CACJ", "file": "iterable.mjs", "sourceRoot": "../../src"}