{"version": 3, "file": "Navigation.js", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/Navigation.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAC1D,OAAO,EAAC,eAAe,EAAC,MAAM,0BAA0B,CAAC;AACzD,OAAO,EAAC,QAAQ,EAAC,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAC,eAAe,EAAE,aAAa,EAAC,MAAM,0BAA0B,CAAC;AAaxE;;GAEG;IACU,UAAU;sBAAS,YAAY;;;iBAA/B,UAAW,SAAQ,WAS9B;;;YAqGA,wKAAQ,OAAO,6DAEd;;;QAtGD,MAAM,CAAC,IAAI,CAAC,OAAwB;YAClC,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;YAC3C,UAAU,CAAC,WAAW,EAAE,CAAC;YACzB,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,oBAAoB;QACpB,QAAQ,iEAAsB;QACrB,gBAAgB,CAAkB;QAClC,YAAY,GAAG,IAAI,eAAe,EAAE,CAAC;QACrC,GAAG,GAAG,IAAI,QAAQ,EAAU,CAAC;QACtC,kBAAkB;QAElB,YAAoB,OAAwB;YAC1C,KAAK,EAAE,CAAC;YACR,oBAAoB;YACpB,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;YAChC,kBAAkB;QACpB,CAAC;QAED,WAAW;YACT,MAAM,sBAAsB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAClD,IAAI,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CACxC,CAAC;YACF,sBAAsB,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACzC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAClB,GAAG,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG;oBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBACH,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAC,EAAE,EAAE;gBAChD,IAAI,OAAO,CAAC,UAAU,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC;oBAC5C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;oBACxB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBAChC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAC1C,IAAI,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAChC,CAAC;YACF,mCAAmC;YACnC,KAAK,MAAM,SAAS,IAAI;gBACtB,kCAAkC;gBAClC,sBAAsB;aACd,EAAE,CAAC;gBACX,cAAc,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE;oBAClC,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,CAAC;wBAC9C,OAAO;oBACT,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;wBACrB,OAAO;oBACT,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC;wBACzB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACpC,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,KAAK,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI;gBAC/B,CAAC,mCAAmC,EAAE,UAAU,CAAC;gBACjD,CAAC,kCAAkC,EAAE,QAAQ,CAAC;gBAC9C,CAAC,mCAAmC,EAAE,SAAS,CAAC;aACxC,EAAE,CAAC;gBACX,cAAc,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE;oBAClC,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,CAAC;wBAC9C,OAAO;oBACT,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;wBACrB,OAAO;oBACT,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC;wBACzB,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACpC,CAAC;oBACD,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,UAAU,EAAE,CAAC;wBACzC,OAAO;oBACT,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;wBACf,GAAG,EAAE,IAAI,CAAC,GAAG;wBACb,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;qBACpC,CAAC,CAAC;oBACH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,8BAA8B;QAC9B,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC;QAC3D,CAAC;QACD,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;QACpC,CAAC;QACD,IAAI,OAAO;YACT,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC;QACD,kBAAkB;QAGV,OAAO;YACb,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACxB,CAAC;QAED,yBALC,eAAe,GAKf,aAAa,EAAC;YACb,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;QACzB,CAAC;;;SArHU,UAAU"}