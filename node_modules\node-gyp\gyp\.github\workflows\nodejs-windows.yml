name: Node.js Windows integration

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build-windows:
    runs-on: windows-2019
    steps:
      - name: Clone gyp-next
        uses: actions/checkout@v3
        with:
          path: gyp-next
      - name: Clone nodejs/node
        uses: actions/checkout@v3
        with:
          repository: nodejs/node
          path: node
      - name: Install deps
        run: choco install nasm
      - name: Replace gyp in Node.js
        run: |
          rm -Recurse node/tools/gyp
          cp -Recurse gyp-next node/tools/gyp
      - name: Build Node.js
        run: |
          cd node
          ./vcbuild.bat
