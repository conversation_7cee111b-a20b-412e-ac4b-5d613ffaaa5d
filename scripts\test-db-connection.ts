#!/usr/bin/env ts-node

import { testDatabaseConnection, closeDatabaseConnections, db } from '../lib/database'

async function testDbConnection() {
  console.log('🔍 Testing Database Connection...\n')

  try {
    console.log('📡 Testing MySQL connection...')
    const connected = await testDatabaseConnection()
    
    if (connected) {
      console.log('✅ Database connection successful!')
    } else {
      console.log('❌ Database connection failed!')
      return
    }

    console.log('\n🧪 Testing basic query...')
    const result = await db.query('SELECT 1 as test')
    console.log('✅ Basic query successful:', result)

    console.log('\n📋 Testing users table...')
    const users = await db.query('SELECT COUNT(*) as count FROM users')
    console.log('✅ Users table accessible:', users)

    console.log('\n📋 Testing user_sessions table...')
    const sessions = await db.query('SELECT COUNT(*) as count FROM user_sessions')
    console.log('✅ User sessions table accessible:', sessions)

    console.log('\n📋 Testing cve_database table...')
    const cves = await db.query('SELECT COUNT(*) as count FROM cve_database')
    console.log('✅ CVE database table accessible:', cves)

    console.log('\n🎉 All database tests passed!')
    console.log('✅ No MySQL2 configuration warnings detected')

  } catch (error) {
    console.error('\n❌ Database test failed:', error)
  } finally {
    await closeDatabaseConnections()
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testDbConnection()
}
