{"version": 3, "file": "findPackage.js", "sourceRoot": "", "sources": ["../../../src/lib/findPackage.ts"], "names": [], "mappings": ";;;;;AAAA,sDAA4B;AAC5B,2DAA4B;AAC5B,0DAAgC;AAChC,gDAAuB;AACvB,4CAAsC;AAEtC,oDAA2B;AAC3B,kEAAyC;AAEzC;;;;;;;;GAQG;AACH,KAAK,UAAU,WAAW,CAAC,OAAgB;IAKzC,IAAI,OAAO,CAAA;IACX,IAAI,OAAO,GAAG,IAAI,CAAA;IAClB,MAAM,OAAO,GAAG,OAAO,CAAC,WAAW,IAAI,cAAc,CAAA;IAErD,4CAA4C;IAC5C,SAAS,sBAAsB,CAAC,OAAkC,EAAE,WAAmB;QACrF,qCAAqC;QACrC,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,MAAM,gBAAgB,GAAG,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YAC9C,IAAA,eAAK,EAAC,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,IAAI,gBAAgB,EAAE,CAAC,CAAA;SACpF;aAAM;YACL,IAAA,sBAAY,EACV,OAAO,EACP,GAAG,eAAK,CAAC,GAAG,CACV,MAAM,WAAW,EAAE,CACpB,oBAAoB,WAAW,0CAA0C,eAAK,CAAC,IAAI,CAClF,eAAe,CAChB,OAAO,eAAK,CAAC,IAAI,CAAC,eAAe,CAAC,uBAAuB,WAAW,yBAAyB,eAAK,CAAC,IAAI,CACtG,SAAS,CACV,GAAG,EACJ,EAAE,KAAK,EAAE,KAAK,EAAE,CACjB,CAAA;SACF;QAED,OAAO,kBAAE,CAAC,QAAQ,CAAC,OAAQ,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YAC9C,IAAA,sBAAY,EAAC,OAAO,EAAE,CAAC,CAAC,CAAA;QAC1B,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,IAAA,eAAK,EAAC,OAAO,EAAE,uBAAuB,EAAE,SAAS,CAAC,CAAA;IAClD,IAAA,eAAK,EAAC,OAAO,EAAE,2BAA2B,EAAE,SAAS,CAAC,CAAA;IAEtD,4DAA4D;IAC5D,IAAI,OAAO,CAAC,WAAW,EAAE;QACvB,OAAO,GAAG,IAAI,CAAA;QACd,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;KAC/C;SAAM,IAAI,OAAO,CAAC,WAAW,EAAE;QAC9B,OAAO,GAAG,OAAO,CAAC,WAAW,CAAA;QAC7B,OAAO,GAAG,sBAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;KACnD;SAAM,IAAI,OAAO,CAAC,KAAK,EAAE;QACxB,IAAA,eAAK,EAAC,OAAO,EAAE,mCAAmC,EAAE,SAAS,CAAC,CAAA;QAE9D,sBAAsB;QACtB,iCAAiC;QACjC,MAAM,SAAS,GAAG,MAAM,IAAA,mBAAQ,GAAE,CAAA;QAClC,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAA;QAE3D,sFAAsF;QACtF,OAAO,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,IAAA,iBAAM,EAAC,OAAO,CAAC,CAAA;QACzD,OAAO,GAAG,IAAI,IAAI,sBAAsB,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,CAAA;KACjE;SAAM;QACL,gGAAgG;QAChG,OAAO,GAAG,OAAO;YACf,CAAC,CAAC,MAAM,IAAA,iBAAM,EACV,CAAC,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,cAAc,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,EACjG;gBACE,GAAG,EAAE,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE;aAClC,CACF;YACH,CAAC,CAAC,IAAI,CAAA;QACR,OAAO,GAAG,sBAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;KACnD;IAED,MAAM,eAAe,GAAG,MAAM,OAAO,CAAA;IAErC,OAAO;QACL,OAAO,EAAE,eAAe;QACxB,OAAO,EAAE,OAAO,IAAI,IAAI;QACxB,OAAO;KACR,CAAA;AACH,CAAC;AAED,kBAAe,WAAW,CAAA"}