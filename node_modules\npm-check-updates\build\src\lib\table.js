"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const cli_table3_1 = __importDefault(require("cli-table3"));
const wrap_1 = __importDefault(require("./wrap"));
/** Wraps the second column in a list of 2-column cli-table rows. */
const wrapRows = (rows) => rows.map(([col1, col2]) => [col1, (0, wrap_1.default)(col2)]);
/** Renders an HTML row. */
const row = (cells) => '\n  <tr>' + cells.map(cell => `<td>${cell}</td>`).join('') + '</tr>';
/** Renders a table for the CLI or markdown. */
const table = ({ colAligns, markdown, rows, }) => {
    // return HTML table for Github-flavored markdown
    if (markdown) {
        return `<table>${rows.map(row).join('')}\n</table>`;
    }
    // otherwise use cli-table3
    else {
        const t = new cli_table3_1.default({ ...(colAligns ? { colAligns } : null) });
        // eslint-disable-next-line fp/no-mutating-methods
        t.push(...(markdown ? rows : wrapRows(rows)));
        return t.toString();
    }
};
exports.default = table;
//# sourceMappingURL=table.js.map