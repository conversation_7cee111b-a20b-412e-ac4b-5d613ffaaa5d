"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const decoder_1 = require("./decoder");
// export type CommandWithPoliciesSignature<
//   COMMAND extends Command,
//   RESP extends RespVersions,
//   TYPE_MAPPING extends TypeMapping,
//   POLICIES extends CommandPolicies
// > = (...args: Parameters<COMMAND['transformArguments']>) => Promise<
//   ReplyWithPolicy<
//     ReplyWithTypeMapping<CommandReply<COMMAND, RESP>, TYPE_MAPPING>,
//     MergePolicies<COMMAND, POLICIES>
//   >
// >;
// export type MergePolicies<
//   COMMAND extends Command,
//   POLICIES extends CommandPolicies
// > = Omit<COMMAND['POLICIES'], keyof POLICIES> & POLICIES;
// type ReplyWithPolicy<
//   REPLY,
//   POLICIES extends CommandPolicies,
// > = (
//   POLICIES['request'] extends REQUEST_POLICIES['SPECIAL'] ? never :
//   POLICIES['request'] extends null | undefined ? REPLY :
//   unknown extends POLICIES['request'] ? REPLY :
//   POLICIES['response'] extends RESPONSE_POLICIES['SPECIAL'] ? never :
//   POLICIES['response'] extends RESPONSE_POLICIES['ALL_SUCCEEDED' | 'ONE_SUCCEEDED' | 'LOGICAL_AND'] ? REPLY :
//   // otherwise, return array of replies
//   Array<REPLY>
// );
//# sourceMappingURL=types.js.map