{"version": 3, "sources": ["recordbatch.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAE,IAAI,EAAY,MAAM,WAAW,CAAC;AAC3C,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,MAAM,EAAS,MAAM,aAAa,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAQ,OAAO,EAAE,MAAM,WAAW,CAAC;AAQ5D,cAAc;AACd,MAAM,WAAW,WAAW,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG;IAKhD,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IACrC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IAErC;;OAEG;IACH,CAAC,MAAM,CAAC,kBAAkB,CAAC,EAAE,IAAI,CAAC;CACrC;AAED,cAAc;AACd,qBAAa,WAAW,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG;gBAEhC,OAAO,EAAE;SAAG,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAAE;gBACvC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IA2CrD,SAAS,CAAC,aAAa,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAE9C,SAAgB,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAClC,SAAgB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAEtC,IAAW,YAAY,6BAEtB;IAED;;OAEG;IACH,IAAW,OAAO,WAAwC;IAE1D;;OAEG;IACH,IAAW,OAAO,WAA+B;IAEjD;;OAEG;IACH,IAAW,SAAS,WAEnB;IAED;;;OAGG;IACI,OAAO,CAAC,KAAK,EAAE,MAAM;IAI5B;;;OAGG;IACI,GAAG,CAAC,KAAK,EAAE,MAAM;IAIxB;;;QAGI;IACG,EAAE,CAAC,KAAK,EAAE,MAAM;IAIvB;;;;OAIG;IACI,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IAIpD;;;;OAIG;IACI,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,MAAM;IAIrE;;OAEG;IACI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAIxB;;;OAGG;IACI,OAAO;IAId;;;OAGG;IACI,MAAM,CAAC,GAAG,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE;IAIzC;;;;OAIG;IACI,KAAK,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC;IAK1D;;;OAGG;IACI,QAAQ,CAAC,CAAC,SAAS,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC;IAI1C;;;OAGG;IACI,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI;IAO9E;;;;OAIG;IACI,QAAQ,CAAC,CAAC,SAAS,MAAM,CAAC,EAAE,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;IAIhF;;;;OAIG;IACI,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,IAAI,GAAG,WAAW;IACpD,UAAU,CAAC,CAAC,SAAS,QAAQ,GAAG,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,WAAW;IAkBzF;;;;;OAKG;IACI,MAAM,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,GAAG,EAAE,WAAW,EAAE,CAAC,EAAE;IAavD;;;;;OAKG;IACI,QAAQ,CAAC,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,aAAa,EAAE,MAAM,EAAE;IAS1D,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,SAIX;CAC7B;AAwDD;;;;;;;GAOG;AACH,qBAAa,oCAAoC,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,WAAW,CAAC,CAAC,CAAC;gBACjF,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;CAKhC", "file": "recordbatch.d.ts", "sourceRoot": "src"}