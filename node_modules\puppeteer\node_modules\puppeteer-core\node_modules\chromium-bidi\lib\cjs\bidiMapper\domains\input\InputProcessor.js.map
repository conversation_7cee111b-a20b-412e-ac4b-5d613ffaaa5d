{"version": 3, "file": "InputProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/domains/input/InputProcessor.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;;GAeG;AACH,+DAQuC;AACvC,wDAAgD;AAEhD,sEAA8D;AAI9D,wEAAgE;AAGhE,MAAa,cAAc;IAChB,uBAAuB,CAAyB;IAChD,aAAa,CAAe;IAE5B,kBAAkB,GAAG,IAAI,wCAAiB,EAAE,CAAC;IAEtD,YACE,sBAA8C,EAC9C,YAA0B;QAE1B,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,MAAsC;QAEtC,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5D,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QACjE,MAAM,UAAU,GAAG,IAAI,sCAAgB,CACrC,UAAU,EACV,OAAO,EACP,MAAM,sCAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAC3D,CAAC;QACF,MAAM,UAAU,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QAChD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,MAAsC;QAEtC,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxE,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC;QAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC3D,MAAM,UAAU,GAAG,IAAI,sCAAgB,CACrC,UAAU,EACV,OAAO,EACP,MAAM,sCAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAC3D,CAAC;QACF,MAAM,UAAU,CAAC,mBAAmB,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QACtE,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC3C,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,MAAgC;QAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;YACzC,iBAAiB,EAAE,MAAM,CAAC,OAAO;SAClC,CAAC,CAAC;QACH,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACxB,MAAM,IAAI,kCAAoB,CAC5B,kCAAkC,MAAM,CAAC,OAAO,EAAE,CACnD,CAAC;QACJ,CAAC;QAED,IAAI,WAAW,CAAC;QAChB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,YAAY,CACrC,MAAM,CAAC,SAAS,QAAQ;gBACtB,OAAO,CACL,IAAI,YAAY,gBAAgB;oBAChC,IAAI,CAAC,IAAI,KAAK,MAAM;oBACpB,CAAC,IAAI,CAAC,QAAQ,CACf,CAAC;YACJ,CAAC,CAAC,EACF,MAAM,CAAC,OAAO,EACd,EAAE,EACF,KAAK,4CAEL,EAAE,EACF,KAAK,CACN,CAAC;YACF,IAAA,kBAAM,EAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;YAClC,IAAA,kBAAM,EAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;YACzC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;QACpC,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,IAAI,oCAAsB,CAC9B,0BAA0B,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CACpD,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,2CAA6B,CACrC,WAAW,MAAM,CAAC,OAAO,CAAC,QAAQ,+BAA+B,CAClE,CAAC;QACJ,CAAC;QAED,yEAAyE;QACzE,cAAc;QACd,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAC7C,MAAM,MAAM,GAA0B,MAAM,KAAK,CAAC,YAAY,CAC5D,MAAM,CAAC,SAAS,QAAQ,CAAyB,KAAa;gBAC5D,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBAChB,kDAAkD;oBAClD,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChC,CAAC,CAAC,EACF,MAAM,CAAC,OAAO,EACd,CAAC,EAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAC,CAAC,EAC5B,KAAK,4CAEL,EAAE,EACF,KAAK,CACN,CAAC;YACF,IAAA,kBAAM,EAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;YAClC,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACpC,MAAM;YACR,CAAC;YAED,MAAM,EAAC,MAAM,EAAC,GAAsB,MAAM,CAAC,MAAM,CAAC;YAClD,IAAA,kBAAM,EAAC,MAAM,KAAK,SAAS,CAAC,CAAC;YAC7B,MAAM,EAAC,IAAI,EAAC,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,iBAAiB,EAAE;gBAClE,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;YACH,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjB,sBAAsB;YACtB,KAAK,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC7C,CAAC;QAED,KAAK,CAAC,IAAI,EAAE,CAAC;QACb,wEAAwE;QACxE,MAAM,WAAW,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;QAC7C,IACE,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,CAAC,MAAM;YACpC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;gBAC/B,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC;YAC/B,CAAC,CAAC,EACF,CAAC;YACD,MAAM,EAAC,QAAQ,EAAC,GAAG,MAAM,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACjE,sEAAsE;YACtE,IAAA,kBAAM,EAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;YAC/B,MAAM,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC,uBAAuB,EAAE;gBACzD,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ;aACT,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,2CAA2C;YAC3C,MAAM,KAAK,CAAC,YAAY,CACtB,MAAM,CAAC,SAAS,aAAa;gBAC3B,IAAI,CAAC,aAAa,CAChB,IAAI,KAAK,CAAC,QAAQ,EAAE;oBAClB,OAAO,EAAE,IAAI;iBACd,CAAC,CACH,CAAC;YACJ,CAAC,CAAC,EACF,MAAM,CAAC,OAAO,EACd,EAAE,EACF,KAAK,4CAEL,EAAE,EACF,KAAK,CACN,CAAC;QACJ,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,iBAAiB,CACf,MAAsC,EACtC,UAAsB;QAEtB,MAAM,aAAa,GAAqB,EAAE,CAAC;QAC3C,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACpC,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,uCAAuB,CAAC,CAAC,CAAC;oBACxB,MAAM,CAAC,UAAU,KAAK,EAAC,WAAW,uCAAyB,EAAC,CAAC;oBAC7D,MAAM,CAAC,UAAU,CAAC,WAAW,0CAA4B,CAAC;oBAE1D,MAAM,MAAM,GAAG,UAAU,CAAC,WAAW,CACnC,MAAM,CAAC,EAAE,sCAET,MAAM,CAAC,UAAU,CAAC,WAAW,CAC9B,CAAC;oBACF,IAAI,MAAM,CAAC,OAAO,KAAK,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;wBACrD,MAAM,IAAI,sCAAwB,CAChC,yBAAyB,MAAM,CAAC,EAAE,UAAU,MAAM,CAAC,OAAO,SAAS,MAAM,CAAC,UAAU,CAAC,WAAW,GAAG,CACpG,CAAC;oBACJ,CAAC;oBACD,MAAM;gBACR,CAAC;gBACD;oBACE,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,IAAkB,CAAC,CAAC;YACjE,CAAC;YACD,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBAC5C,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,MAAM,EAAE,IAAI;aACb,CAAC,CAAC,CAAC;YACJ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC/B,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACzB,CAAC;gBACD,aAAa,CAAC,CAAC,CAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAE,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;CACF;AAtMD,wCAsMC"}