import { Maybe } from '../types/Maybe';
import { Options } from '../types/Options';
import { PackageManager } from '../types/PackageManager';
/**
 * Resolves the package manager from a string or object. Throws an error if an invalid packageManager is provided.
 *
 * @param packageManagerNameOrObject
 * @param packageManagerNameOrObject.global
 * @param packageManagerNameOrObject.packageManager
 * @returns
 */
declare function getPackageManager(options: Options, name: Maybe<string>): PackageManager;
export default getPackageManager;
