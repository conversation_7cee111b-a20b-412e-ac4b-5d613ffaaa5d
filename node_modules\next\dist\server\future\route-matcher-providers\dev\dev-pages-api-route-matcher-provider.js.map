{"version": 3, "sources": ["../../../../../src/server/future/route-matcher-providers/dev/dev-pages-api-route-matcher-provider.ts"], "names": ["DevPagesAPIRouteMatcherProvider", "FileCacheRouteMatcherProvider", "constructor", "pagesDir", "extensions", "reader", "localeNormalizer", "expression", "RegExp", "join", "normalizers", "DevPagesNormalizers", "test", "filename", "startsWith", "path", "extension", "transform", "files", "matchers", "pathname", "normalize", "page", "bundlePath", "push", "PagesAPILocaleRouteMatcher", "kind", "RouteKind", "PAGES_API", "i18n", "PagesAPIRouteMatcher"], "mappings": ";;;;+BAWaA;;;eAAAA;;;sCAPN;2BACmB;6DACT;+CAE6B;uBACV;;;;;;AAE7B,MAAMA,wCAAwCC,4DAA6B;IAIhFC,YACE,AAAiBC,QAAgB,EACjC,AAAiBC,UAAiC,EAClDC,MAAkB,EAClB,AAAiBC,gBAAwC,CACzD;QACA,KAAK,CAACH,UAAUE;aALCF,WAAAA;aACAC,aAAAA;aAEAE,mBAAAA;QAIjB,4EAA4E;QAC5E,mBAAmB;QACnB,IAAI,CAACC,UAAU,GAAG,IAAIC,OAAO,CAAC,MAAM,EAAEJ,WAAWK,IAAI,CAAC,KAAK,EAAE,CAAC;QAE9D,IAAI,CAACC,WAAW,GAAG,IAAIC,0BAAmB,CAACR,UAAUC;IACvD;IAEQQ,KAAKC,QAAgB,EAAW;QACtC,sEAAsE;QACtE,IAAI,CAAC,IAAI,CAACN,UAAU,CAACK,IAAI,CAACC,WAAW,OAAO;QAE5C,sEAAsE;QACtE,yEAAyE;QACzE,6CAA6C;QAE7C,2DAA2D;QAC3D,IAAIA,SAASC,UAAU,CAACC,aAAI,CAACN,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE,WAAW,OAAO;QAEnE,KAAK,MAAMa,aAAa,IAAI,CAACZ,UAAU,CAAE;YACvC,qEAAqE;YACrE,uBAAuB;YACvB,IAAIS,aAAaE,aAAI,CAACN,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE,CAAC,IAAI,EAAEa,UAAU,CAAC,GAAG;gBAC7D,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,MAAgBC,UACdC,KAA4B,EACkB;QAC9C,MAAMC,WAAwC,EAAE;QAChD,KAAK,MAAMN,YAAYK,MAAO;YAC5B,4DAA4D;YAC5D,IAAI,CAAC,IAAI,CAACN,IAAI,CAACC,WAAW;YAE1B,MAAMO,WAAW,IAAI,CAACV,WAAW,CAACU,QAAQ,CAACC,SAAS,CAACR;YACrD,MAAMS,OAAO,IAAI,CAACZ,WAAW,CAACY,IAAI,CAACD,SAAS,CAACR;YAC7C,MAAMU,aAAa,IAAI,CAACb,WAAW,CAACa,UAAU,CAACF,SAAS,CAACR;YAEzD,IAAI,IAAI,CAACP,gBAAgB,EAAE;gBACzBa,SAASK,IAAI,CACX,IAAIC,gDAA0B,CAAC;oBAC7BC,MAAMC,oBAAS,CAACC,SAAS;oBACzBR;oBACAE;oBACAC;oBACAV;oBACAgB,MAAM,CAAC;gBACT;YAEJ,OAAO;gBACLV,SAASK,IAAI,CACX,IAAIM,0CAAoB,CAAC;oBACvBJ,MAAMC,oBAAS,CAACC,SAAS;oBACzBR;oBACAE;oBACAC;oBACAV;gBACF;YAEJ;QACF;QAEA,OAAOM;IACT;AACF"}