{"version": 3, "sources": ["../../../src/trace/report/index.test.ts"], "names": ["TRACE_EVENT", "name", "duration", "timestamp", "Date", "now", "id", "startTime", "WEBPACK_INVALIDATED_EVENT", "describe", "it", "tmpDir", "mkdtemp", "join", "tmpdir", "setGlobal", "reporter", "report", "flushAll", "traceFilename", "traces", "JSON", "parse", "readFile", "expect", "length", "toEqual", "traceId", "toBeDefined", "recordMock", "jest", "fn", "telemetryMock", "record", "toHaveBeenCalledTimes", "toHaveBeenCalledWith", "eventName", "payload", "durationInMicroseconds"], "mappings": ";;;;0BAAkC;kBACT;wBACC;sBACL;oBACE;AAEvB,MAAMA,cAAc;IAClBC,MAAM;IACNC,UAAU;IACVC,WAAWC,KAAKC,GAAG;IACnBC,IAAI;IACJC,WAAWH,KAAKC,GAAG;AACrB;AACA,MAAMG,4BAA4B;IAChCP,MAAM;IACNC,UAAU;IACVC,WAAWC,KAAKC,GAAG;IACnBC,IAAI;IACJC,WAAWH,KAAKC,GAAG;AACrB;AAEAI,SAAS,kBAAkB;IACzBA,SAAS,iBAAiB;QACxBC,GAAG,8CAA8C;YAC/C,MAAMC,SAAS,MAAMC,IAAAA,iBAAO,EAACC,IAAAA,UAAI,EAACC,IAAAA,UAAM,KAAI;YAC5CC,IAAAA,iBAAS,EAAC,WAAWJ;YACrBI,IAAAA,iBAAS,EAAC,SAAS;YACnBC,UAAQ,CAACC,MAAM,CAACjB;YAChB,MAAMgB,UAAQ,CAACE,QAAQ;YACvB,MAAMC,gBAAgBN,IAAAA,UAAI,EAACF,QAAQ;YACnC,MAAMS,SAASC,KAAKC,KAAK,CAAC,MAAMC,IAAAA,kBAAQ,EAACJ,eAAe;YACxDK,OAAOJ,OAAOK,MAAM,EAAEC,OAAO,CAAC;YAC9BF,OAAOJ,MAAM,CAAC,EAAE,CAACnB,IAAI,EAAEyB,OAAO,CAAC;YAC/BF,OAAOJ,MAAM,CAAC,EAAE,CAACd,EAAE,EAAEoB,OAAO,CAAC;YAC7BF,OAAOJ,MAAM,CAAC,EAAE,CAAClB,QAAQ,EAAEwB,OAAO,CAAC;YACnCF,OAAOJ,MAAM,CAAC,EAAE,CAACO,OAAO,EAAEC,WAAW;QACvC;IACF;IAEAnB,SAAS,sBAAsB;QAC7BC,GAAG,iCAAiC;YAClC,MAAMmB,aAAaC,KAAKC,EAAE;YAC1B,MAAMC,gBAAgB;gBACpBC,QAAQJ;YACV;YACAd,IAAAA,iBAAS,EAAC,aAAaiB;YACvB,0BAA0B;YAC1BhB,UAAQ,CAACC,MAAM,CAACjB;YAChBwB,OAAOK,YAAYK,qBAAqB,CAAC;YACzClB,UAAQ,CAACC,MAAM,CAACT;YAChBgB,OAAOK,YAAYK,qBAAqB,CAAC;YACzCV,OAAOK,YAAYM,oBAAoB,CAAC;gBACtCC,WAAW;gBACXC,SAAS;oBACPC,wBAAwB;gBAC1B;YACF;QACF;IACF;AACF"}