"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./lib/auth-simple.ts":
/*!****************************!*\
  !*** ./lib/auth-simple.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleAuthService: () => (/* binding */ SimpleAuthService)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(middleware)/./node_modules/bcryptjs/dist/bcrypt.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(middleware)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key\";\nconst JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || \"your-super-secret-refresh-key\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"1h\";\nconst JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || \"7d\";\nconst BCRYPT_ROUNDS = 12;\n// Mock users for testing\nconst mockUsers = [\n    {\n        id: 1,\n        username: \"admin\",\n        email: \"<EMAIL>\",\n        full_name: \"Administrator\",\n        role: \"admin\",\n        plan: \"Elite\",\n        level: 100,\n        score: 10000,\n        streak_days: 365,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"admin123\"\n    },\n    {\n        id: 2,\n        username: \"testuser\",\n        email: \"<EMAIL>\",\n        full_name: \"Test User\",\n        role: \"user\",\n        plan: \"Free\",\n        level: 1,\n        score: 0,\n        streak_days: 0,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"test123\"\n    }\n];\nclass SimpleAuthService {\n    // Hash password\n    static async hashPassword(password) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, BCRYPT_ROUNDS);\n    }\n    // Verify password\n    static async verifyPassword(password, hash) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hash);\n    }\n    // Generate JWT tokens\n    static generateTokens(user) {\n        const payload = {\n            id: user.id,\n            username: user.username,\n            email: user.email,\n            role: user.role,\n            plan: user.plan\n        };\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n        const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_REFRESH_SECRET, {\n            expiresIn: JWT_REFRESH_EXPIRES_IN\n        });\n        return {\n            accessToken,\n            refreshToken,\n            expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds\n        };\n    }\n    // Verify JWT token\n    static verifyToken(token, isRefreshToken = false) {\n        try {\n            const secret = isRefreshToken ? JWT_REFRESH_SECRET : JWT_SECRET;\n            console.log(\"\\uD83D\\uDD0D Verifying token with secret length:\", secret.length);\n            console.log(\"\\uD83D\\uDD0D Token length:\", token.length);\n            const result = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n            console.log(\"✅ Token verification successful:\", result);\n            return result;\n        } catch (error) {\n            console.log(\"❌ Token verification error:\", error instanceof Error ? error.message : String(error));\n            return null;\n        }\n    }\n    // Register new user\n    static async register(data) {\n        try {\n            // Check if user already exists\n            const existingUser = mockUsers.find((u)=>u.email === data.email || u.username === data.username);\n            if (existingUser) {\n                return {\n                    success: false,\n                    message: \"User already exists with this email or username\"\n                };\n            }\n            // Create new user\n            const newUser = {\n                id: mockUsers.length + 1,\n                username: data.username,\n                email: data.email,\n                full_name: data.fullName || \"\",\n                role: \"user\",\n                plan: \"Free\",\n                level: 1,\n                score: 0,\n                streak_days: 0,\n                email_verified: false,\n                status: \"active\",\n                last_active: new Date(),\n                created_at: new Date(),\n                password: data.password\n            };\n            // Add to mock users\n            mockUsers.push(newUser);\n            // Remove password from user object\n            const { password, ...userWithoutPassword } = newUser;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                message: \"Registration failed\"\n            };\n        }\n    }\n    // Login user\n    static async login(email, password) {\n        try {\n            // Find user by email\n            const user = mockUsers.find((u)=>u.email === email && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Verify password (simple comparison for testing)\n            if (password !== user.password) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Remove password from user object\n            const { password: userPassword, ...userWithoutPassword } = user;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"Login failed\"\n            };\n        }\n    }\n    // Authenticate request\n    static async authenticateRequest(request) {\n        try {\n            // Get token from Authorization header or cookies\n            let token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n            if (!token) {\n                // Try to get from cookies\n                const cookies = request.headers.get(\"cookie\");\n                console.log(\"\\uD83C\\uDF6A Cookies in middleware:\", cookies);\n                if (cookies) {\n                    const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n                    token = tokenMatch ? tokenMatch[1] : undefined;\n                    console.log(\"\\uD83D\\uDD11 Token from cookies:\", token ? \"Found\" : \"Not found\");\n                }\n            }\n            if (!token) {\n                console.log(\"❌ No token found in headers or cookies\");\n                return {\n                    success: false,\n                    message: \"No token provided\"\n                };\n            }\n            // Verify token\n            console.log(\"\\uD83D\\uDD0D Verifying token in middleware...\");\n            const decoded = this.verifyToken(token);\n            if (!decoded) {\n                console.log(\"❌ Token verification failed in middleware\");\n                return {\n                    success: false,\n                    message: \"Invalid token\"\n                };\n            }\n            console.log(\"✅ Token decoded successfully:\", decoded.id, decoded.username);\n            // Get user from mock data\n            const user = mockUsers.find((u)=>u.id === decoded.id);\n            if (!user) {\n                console.log(\"❌ User not found in mock data:\", decoded.id);\n                return {\n                    success: false,\n                    message: \"User not found\"\n                };\n            }\n            console.log(\"✅ User found in middleware:\", user.username);\n            const { password, ...userWithoutPassword } = user;\n            return {\n                success: true,\n                user: userWithoutPassword\n            };\n        } catch (error) {\n            console.error(\"Authentication error:\", error);\n            return {\n                success: false,\n                message: \"Authentication failed\"\n            };\n        }\n    }\n    // Logout user (for mock, just return success)\n    static async logout(userId, _sessionToken) {\n        // In a real implementation, this would remove the session from database\n        console.log(`User ${userId} logged out`);\n    }\n    // Refresh token\n    static async refreshToken(refreshToken) {\n        try {\n            // Verify refresh token\n            const decoded = this.verifyToken(refreshToken, true);\n            if (!decoded) {\n                return {\n                    success: false,\n                    message: \"Invalid refresh token\"\n                };\n            }\n            // Get user data\n            const user = mockUsers.find((u)=>u.id === decoded.id && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"User not found or inactive\"\n                };\n            }\n            const { password, ...userWithoutPassword } = user;\n            // Generate new tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update user last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            return {\n                success: false,\n                message: \"Token refresh failed\"\n            };\n        }\n    }\n    // Get user by ID\n    static async getUserById(userId) {\n        try {\n            const user = mockUsers.find((u)=>u.id === userId);\n            if (!user) return null;\n            const { password, ...userWithoutPassword } = user;\n            return userWithoutPassword;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            return null;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(middleware)/./lib/auth-simple.ts\n");

/***/ })

});