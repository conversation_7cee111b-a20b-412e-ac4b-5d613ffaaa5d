{"version": 3, "sources": ["fb/sparse-tensor.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,WAAW,MAAM,aAAa,CAAC;AAE3C,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAGjC,qBAAa,YAAY;IACvB,EAAE,EAAE,WAAW,CAAC,UAAU,GAAC,IAAI,CAAQ;IACvC,MAAM,SAAK;IACX,MAAM,CAAC,CAAC,EAAC,MAAM,EAAE,EAAE,EAAC,WAAW,CAAC,UAAU,GAAE,YAAY;IAM1D,MAAM,CAAC,qBAAqB,CAAC,EAAE,EAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,EAAC,YAAY,GAAE,YAAY;IAIvF,MAAM,CAAC,iCAAiC,CAAC,EAAE,EAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,EAAC,YAAY,GAAE,YAAY;IAKnG,QAAQ,IAAG,IAAI;IAKf;;;;OAIG;IACH,IAAI,CAAC,GAAG,EAAC,GAAG,GAAE,GAAG,GAAC,IAAI;IAKtB;;OAEG;IACH,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,EAAC,SAAS,GAAE,SAAS,GAAC,IAAI;IAKnD,WAAW,IAAG,MAAM;IAKpB;;OAEG;IACH,aAAa,IAAG,MAAM;IAKtB,eAAe,IAAG,iBAAiB;IAKnC;;OAEG;IACH,WAAW,CAAC,GAAG,EAAC,GAAG,GAAE,GAAG,GAAC,IAAI;IAK7B;;OAEG;IACH,IAAI,CAAC,GAAG,CAAC,EAAC,MAAM,GAAE,MAAM,GAAC,IAAI;IAK7B,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO;IAIpD,MAAM,CAAC,WAAW,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAC,IAAI;IAI7D,MAAM,CAAC,OAAO,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,UAAU,EAAC,WAAW,CAAC,MAAM;IAIzE,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,WAAW,EAAC,WAAW,CAAC,MAAM;IAI3E,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,IAAI,EAAC,WAAW,CAAC,MAAM,EAAE,GAAE,WAAW,CAAC,MAAM;IAQnG,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAC,MAAM;IAIpE,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,aAAa,EAAC,MAAM;IAIzE,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,eAAe,EAAC,iBAAiB;IAIxF,MAAM,CAAC,cAAc,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,iBAAiB,EAAC,WAAW,CAAC,MAAM;IAIvF,MAAM,CAAC,OAAO,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,UAAU,EAAC,WAAW,CAAC,MAAM;IAIzE,MAAM,CAAC,eAAe,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,GAAE,WAAW,CAAC,MAAM;IAStE,MAAM,CAAC,wBAAwB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAC,WAAW,CAAC,MAAM;IAItF,MAAM,CAAC,oCAAoC,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAC,WAAW,CAAC,MAAM;CAIjG", "file": "sparse-tensor.d.ts", "sourceRoot": "../src"}