{"version": 3, "file": "EXPLAINCLI.js", "sourceRoot": "", "sources": ["../../../lib/commands/EXPLAINCLI.ts"], "names": [], "mappings": ";;AAEA,gDAAqD;AAMrD,kBAAe;IACb,iBAAiB,EAAE,IAAI;IACvB,YAAY,EAAE,IAAI;IAClB;;;;;;;OAOG;IACH,YAAY,CACV,MAAqB,EACrB,KAAoB,EACpB,KAAoB,EACpB,OAA6B;QAE7B,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAE3C,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,yBAAe,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IACD,cAAc,EAAE,SAAyD;CAC/C,CAAC"}