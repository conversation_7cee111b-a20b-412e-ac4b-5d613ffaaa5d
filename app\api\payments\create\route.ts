'use server'

import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { PaymentService } from '@/lib/services/payment'
import { z } from 'zod'

const paymentSchema = z.object({
  planId: z.string().min(1),
  gateway: z.enum(['tripay', 'midtrans', 'xendit', 'manual']),
  duration: z.enum(['daily', 'weekly', 'monthly', 'yearly']).default('monthly'),
  customerInfo: z.object({
    name: z.string().min(1),
    email: z.string().email(),
    phone: z.string().min(1)
  }).optional()
})

export async function POST(request: NextRequest) {
  try {
    // Authenticate request
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const validatedData = paymentSchema.parse(body)

    // Get payment service
    const paymentService = new PaymentService()

    // Get plan details
    const plan = await paymentService.getPlan(validatedData.planId)
    if (!plan) {
      return NextResponse.json(
        { success: false, message: 'Plan not found' },
        { status: 404 }
      )
    }

    // Use user info if customerInfo not provided
    const customerInfo = validatedData.customerInfo || {
      name: authResult.user.fullName || authResult.user.username,
      email: authResult.user.email,
      phone: authResult.user.phone || ''
    }

    // Create payment
    const paymentResponse = await paymentService.createPayment({
      userId: authResult.user.id,
      planId: validatedData.planId,
      amount: plan.price,
      currency: plan.currency,
      duration: validatedData.duration,
      gateway: validatedData.gateway,
      customerInfo: customerInfo
    })

    return NextResponse.json({
      success: true,
      message: 'Payment created successfully',
      data: paymentResponse
    })

  } catch (error) {
    console.error('Payment creation error:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to create payment',
        error: error instanceof z.ZodError ? error.errors : undefined
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    endpoint: '/api/payments/create',
    method: 'POST',
    description: 'Create a new payment for plan subscription',
    parameters: {
      planId: 'string (required) - ID of the plan to subscribe to',
      gateway: 'string (required) - Payment gateway: tripay, midtrans, xendit, or manual',
      duration: 'string (optional) - Subscription duration: daily, weekly, monthly, yearly (default: monthly)',
      customerInfo: 'object (optional) - Customer information for payment'
    },
    example: {
      planId: 'student',
      gateway: 'tripay',
      duration: 'monthly',
      customerInfo: {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+6281234567890'
      }
    }
  })
}
