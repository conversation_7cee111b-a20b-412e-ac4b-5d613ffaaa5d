// Simple register test using Node.js built-in fetch
console.log('🧪 Testing Register API with Node.js fetch...\n')

const testRegister = async () => {
  try {
    const registerData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'test123',
      confirmPassword: 'test123',
      fullName: 'Test User'
    }

    console.log('📤 Sending register request...')
    console.log('Username:', registerData.username)
    console.log('Email:', registerData.email)
    console.log('Full Name:', registerData.fullName)
    console.log('Password: [HIDDEN]')

    const response = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(registerData)
    })

    console.log('\n📥 Response received:')
    console.log('Status:', response.status)
    console.log('Status Text:', response.statusText)
    console.log('OK:', response.ok)

    const result = await response.json()
    console.log('\n📋 Response body:')
    console.log(JSON.stringify(result, null, 2))

    if (response.ok && result.success) {
      console.log('\n✅ REGISTER TEST PASSED!')
      console.log('🎉 User successfully registered')
      console.log('👤 User ID:', result.user?.id)
      console.log('📧 Email:', result.user?.email)
      console.log('👤 Username:', result.user?.username)
      console.log('🔑 Role:', result.user?.role)
    } else {
      console.log('\n❌ REGISTER TEST FAILED!')
      console.log('Error:', result.message || result.error || 'Unknown error')
    }

  } catch (error) {
    console.error('\n💥 REGISTER TEST ERROR!')
    console.error('Error type:', error.constructor.name)
    console.error('Error message:', error.message)
    console.error('Full error:', error)
  }
}

testRegister()
