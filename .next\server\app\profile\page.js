/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/profile/page";
exports.ids = ["app/profile/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'profile',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/profile/page.tsx */ \"(rsc)/./app/profile/page.tsx\")), \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/profile/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/profile/page\",\n        pathname: \"/profile\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/profile/page.tsx */ \"(ssr)/./app/profile/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNVc2VycyU1QyU1Q0Rvd25sb2FkcyU1QyU1Q0tvZGUtWEd1YXJkJTVDJTVDYXBwJTVDJTVDcHJvZmlsZSU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SkFBOEYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYl9rb2RleGd1YXJkLz82ZDQ5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcVXNlcnNcXFxcRG93bmxvYWRzXFxcXEtvZGUtWEd1YXJkXFxcXGFwcFxcXFxwcm9maWxlXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cprofile%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccomponents%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Chooks%5C%5Cauth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccomponents%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Chooks%5C%5Cauth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Toast.tsx */ \"(ssr)/./components/Toast.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./contexts/ThemeContext.tsx */ \"(ssr)/./contexts/ThemeContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./hooks/auth.tsx */ \"(ssr)/./hooks/auth.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccomponents%5C%5CToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Chooks%5C%5Cauth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CUsers%5C%5CDownloads%5C%5CKode-XGuard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/profile/page.tsx":
/*!******************************!*\
  !*** ./app/profile/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProfilePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(ssr)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bug,Camera,Edit,Flame,Lock,Save,Search,Shield,TrendingUp,Trophy,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bug,Camera,Edit,Flame,Lock,Save,Search,Shield,TrendingUp,Trophy,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bug,Camera,Edit,Flame,Lock,Save,Search,Shield,TrendingUp,Trophy,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bug,Camera,Edit,Flame,Lock,Save,Search,Shield,TrendingUp,Trophy,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bug,Camera,Edit,Flame,Lock,Save,Search,Shield,TrendingUp,Trophy,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bug,Camera,Edit,Flame,Lock,Save,Search,Shield,TrendingUp,Trophy,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bug,Camera,Edit,Flame,Lock,Save,Search,Shield,TrendingUp,Trophy,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bug,Camera,Edit,Flame,Lock,Save,Search,Shield,TrendingUp,Trophy,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bug,Camera,Edit,Flame,Lock,Save,Search,Shield,TrendingUp,Trophy,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bug,Camera,Edit,Flame,Lock,Save,Search,Shield,TrendingUp,Trophy,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bug,Camera,Edit,Flame,Lock,Save,Search,Shield,TrendingUp,Trophy,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bug,Camera,Edit,Flame,Lock,Save,Search,Shield,TrendingUp,Trophy,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bug,Camera,Edit,Flame,Lock,Save,Search,Shield,TrendingUp,Trophy,User,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ProfilePage() {\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [editing, setEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadProfile();\n    }, []);\n    const loadProfile = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD04 Loading profile data...\");\n            setLoading(true);\n            const response = await fetch(\"/api/profile\");\n            const data = await response.json();\n            if (data.success) {\n                console.log(\"✅ Profile data loaded:\", data.data);\n                // Transform API data to match component interface\n                const transformedProfile = {\n                    id: data.data.id,\n                    username: data.data.username,\n                    email: data.data.email,\n                    fullName: data.data.fullName,\n                    bio: data.data.bio || \"\",\n                    location: data.data.location || \"\",\n                    website: data.data.website || \"\",\n                    joinedAt: new Date(data.data.joinDate).toLocaleDateString(),\n                    lastActive: new Date(data.data.lastActive).toLocaleDateString(),\n                    plan: data.data.plan,\n                    level: data.data.level,\n                    score: data.data.score,\n                    rank: data.data.stats.communityRank,\n                    streak: data.data.streak,\n                    badges: data.data.achievements.map((achievement)=>({\n                            id: achievement.id,\n                            name: achievement.name,\n                            description: achievement.description,\n                            icon: achievement.icon,\n                            color: achievement.rarity === \"legendary\" ? \"yellow\" : achievement.rarity === \"epic\" ? \"purple\" : achievement.rarity === \"rare\" ? \"blue\" : \"gray\",\n                            earnedAt: new Date(achievement.unlockedAt).toLocaleDateString(),\n                            rarity: achievement.rarity\n                        })),\n                    stats: {\n                        totalScans: data.data.stats.totalScans,\n                        vulnerabilitiesFound: data.data.stats.vulnerabilitiesFound,\n                        osintQueries: data.data.stats.totalScans * 3,\n                        filesAnalyzed: data.data.stats.reportsGenerated,\n                        cveReported: Math.floor(data.data.stats.vulnerabilitiesFound / 100),\n                        toolsUsed: data.data.stats.toolsUsed,\n                        daysActive: Math.floor((new Date().getTime() - new Date(data.data.joinDate).getTime()) / (1000 * 60 * 60 * 24)),\n                        pointsThisWeek: Math.floor(data.data.score * 0.1),\n                        pointsThisMonth: Math.floor(data.data.score * 0.3)\n                    },\n                    preferences: {\n                        emailNotifications: true,\n                        pushNotifications: false,\n                        publicProfile: true,\n                        showStats: true,\n                        theme: \"dark\",\n                        language: \"en\"\n                    }\n                };\n                setProfile(transformedProfile);\n            } else {\n                console.error(\"❌ Failed to load profile:\", data.error);\n            }\n        } catch (error) {\n            console.error(\"❌ Error loading profile:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSaveProfile = async (updatedData)=>{\n        try {\n            console.log(\"\\uD83D\\uDCBE Saving profile data...\");\n            const response = await fetch(\"/api/profile\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(updatedData)\n            });\n            const data = await response.json();\n            if (data.success) {\n                console.log(\"✅ Profile saved successfully\");\n                await loadProfile() // Reload profile data\n                ;\n                setEditing(false);\n            } else {\n                console.error(\"❌ Failed to save profile:\", data.error);\n            }\n        } catch (error) {\n            console.error(\"❌ Error saving profile:\", error);\n        }\n    };\n    const getBadgeIcon = (icon)=>{\n        switch(icon){\n            case \"bug\":\n                return _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"search\":\n                return _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"shield\":\n                return _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"trophy\":\n                return _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            default:\n                return _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n        }\n    };\n    const getRarityColor = (rarity)=>{\n        switch(rarity){\n            case \"legendary\":\n                return \"text-yellow-400 bg-yellow-400/20 border-yellow-400\";\n            case \"epic\":\n                return \"text-purple-400 bg-purple-400/20 border-purple-400\";\n            case \"rare\":\n                return \"text-blue-400 bg-blue-400/20 border-blue-400\";\n            case \"common\":\n                return \"text-gray-400 bg-gray-400/20 border-gray-400\";\n            default:\n                return \"text-gray-400 bg-gray-400/20 border-gray-400\";\n        }\n    };\n    const getPlanColor = (plan)=>{\n        switch(plan){\n            case \"Elite\":\n                return \"text-yellow-400 bg-yellow-400/20\";\n            case \"Expert\":\n                return \"text-purple-400 bg-purple-400/20\";\n            case \"Pro\":\n                return \"text-blue-400 bg-blue-400/20\";\n            case \"Free\":\n                return \"text-gray-400 bg-gray-400/20\";\n            default:\n                return \"text-gray-400 bg-gray-400/20\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-cyber-primary font-medium\",\n                            children: \"Loading profile...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, this);\n    }\n    if (!profile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-12 w-12 text-red-400 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-400 font-medium\",\n                            children: \"Failed to load profile\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                lineNumber: 243,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-glow\",\n                                            children: \"User\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-pink\",\n                                            children: \"Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg\",\n                                    children: \"Manage your account and preferences\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 lg:mt-0 flex items-center space-x-4\",\n                            children: editing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setEditing(false),\n                                        className: \"btn-cyber-secondary\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSaveProfile,\n                                        className: \"btn-cyber-primary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Save Changes\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setEditing(true),\n                                className: \"btn-cyber-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Edit Profile\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row lg:items-center lg:space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-6 lg:mb-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 h-32 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-4xl font-bold text-black\",\n                                        children: profile.username.charAt(0)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"absolute bottom-0 right-0 p-2 rounded-full bg-cyber-primary text-black hover:bg-cyber-primary/80 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this),\n                                    profile.streak > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 bg-cyber-secondary text-black text-sm font-bold px-3 py-1 rounded-full flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: profile.streak\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-white\",\n                                                children: profile.username\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `px-3 py-1 rounded-full text-sm font-bold ${getPlanColor(profile.plan)}`,\n                                                children: profile.plan\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 text-lg mb-4\",\n                                        children: profile.fullName\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-cyber-primary\",\n                                                        children: profile.level\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Level\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-cyber-secondary\",\n                                                        children: profile.score.toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-cyber-accent\",\n                                                        children: [\n                                                            \"#\",\n                                                            profile.rank\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Rank\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-green-400\",\n                                                        children: profile.stats.daysActive\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Days Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: profile.bio\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1 bg-cyber-dark/50 p-1 rounded-lg\",\n                    children: [\n                        {\n                            id: \"overview\",\n                            label: \"Overview\",\n                            icon: _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                        },\n                        {\n                            id: \"stats\",\n                            label: \"Statistics\",\n                            icon: _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                        },\n                        {\n                            id: \"badges\",\n                            label: \"Badges\",\n                            icon: _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                        },\n                        {\n                            id: \"settings\",\n                            label: \"Settings\",\n                            icon: _barrel_optimize_names_Award_Bug_Camera_Edit_Flame_Lock_Save_Search_Shield_TrendingUp_Trophy_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n                        }\n                    ].map((tab)=>{\n                        const Icon = tab.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(tab.id),\n                            className: `flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${activeTab === tab.id ? \"bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30\" : \"text-gray-400 hover:text-white hover:bg-cyber-primary/10\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: tab.label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, tab.id, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n            lineNumber: 255,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\profile\\\\page.tsx\",\n        lineNumber: 254,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/profile/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/DashboardLayout.tsx":
/*!****************************************!*\
  !*** ./components/DashboardLayout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DashboardLayout({ children }) {\n    const [isSidebarOpen, setIsSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"online\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Update time every minute\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    // Load user and check auth\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            try {\n                // Get user data from cookies (set by middleware)\n                const userCookie = document.cookie.split(\"; \").find((row)=>row.startsWith(\"user=\"));\n                if (!userCookie) {\n                    router.push(\"/login\");\n                    return;\n                }\n                const userData = JSON.parse(decodeURIComponent(userCookie.split(\"=\")[1]));\n                // Transform to match our User interface\n                const transformedUser = {\n                    id: userData.id?.toString() || \"1\",\n                    username: userData.username || \"User\",\n                    email: userData.email || \"<EMAIL>\",\n                    fullName: userData.username || \"KodeX User\",\n                    role: userData.role || \"user\",\n                    plan: userData.plan || \"Free\",\n                    level: 28,\n                    score: 8950,\n                    streak: 12\n                };\n                setUser(transformedUser);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Auth check error:\", error);\n                router.push(\"/login\");\n            }\n        };\n        checkAuth();\n    }, [\n        router\n    ]);\n    const handleLogout = async ()=>{\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"refreshToken\");\n        localStorage.removeItem(\"user\");\n        router.push(\"/login\");\n    };\n    const toggleSidebar = ()=>{\n        setIsSidebarOpen(!isSidebarOpen);\n    };\n    const toggleExpanded = (itemName)=>{\n        setExpandedItems((prev)=>prev.includes(itemName) ? prev.filter((name)=>name !== itemName) : [\n                ...prev,\n                itemName\n            ]);\n    };\n    const isActive = (href)=>{\n        if (href === \"/dashboard\") {\n            return pathname === \"/dashboard\";\n        }\n        return pathname.startsWith(href);\n    };\n    const isAdmin = pathname.startsWith(\"/admin\");\n    const dashboardNavItems = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            name: \"Profile\",\n            href: \"/profile\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"Settings\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"Tools\",\n            href: \"#\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            children: [\n                {\n                    name: \"OSINT Lookup\",\n                    href: \"/osint\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    badge: \"Popular\"\n                },\n                {\n                    name: \"Vulnerability Scanner\",\n                    href: \"/scanner\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    badge: \"Pro\"\n                },\n                {\n                    name: \"File Analyzer\",\n                    href: \"/file-analyzer\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    badge: \"Pro\"\n                },\n                {\n                    name: \"CVE Database\",\n                    href: \"/cve\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                },\n                {\n                    name: \"Google Dorking\",\n                    href: \"/dorking\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                }\n            ]\n        },\n        {\n            name: \"Resources\",\n            href: \"#\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            children: [\n                {\n                    name: \"Documentation\",\n                    href: \"/docs\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                },\n                {\n                    name: \"Community\",\n                    href: \"/community\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                },\n                {\n                    name: \"Leaderboard\",\n                    href: \"/leaderboard\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                }\n            ]\n        }\n    ];\n    const adminNavItems = [\n        {\n            name: \"Dashboard\",\n            href: \"/admin/dashboard\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        },\n        {\n            name: \"Users\",\n            href: \"/admin/users\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            name: \"Bots\",\n            href: \"/admin/bots\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        },\n        {\n            name: \"Plans\",\n            href: \"/admin/plans\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n        },\n        {\n            name: \"Settings\",\n            href: \"/admin/settings\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"System\",\n            href: \"#\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            children: [\n                {\n                    name: \"Monitoring\",\n                    href: \"/admin/monitoring\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n                },\n                {\n                    name: \"Logs\",\n                    href: \"/admin/logs\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                },\n                {\n                    name: \"Security\",\n                    href: \"/admin/security\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n                }\n            ]\n        }\n    ];\n    const navItems = isAdmin ? adminNavItems : dashboardNavItems;\n    const getPlanColor = (plan)=>{\n        switch(plan){\n            case \"Elite\":\n                return \"text-yellow-400 bg-yellow-400/20\";\n            case \"Expert\":\n                return \"text-purple-400 bg-purple-400/20\";\n            case \"Pro\":\n                return \"text-blue-400 bg-blue-400/20\";\n            case \"Free\":\n                return \"text-gray-400 bg-gray-400/20\";\n            default:\n                return \"text-gray-400 bg-gray-400/20\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-cyber-dark flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-cyber-primary font-medium\",\n                        children: \"Initializing cyber interface...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 216,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-cyber-dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-cyber-dark/95 backdrop-blur-md border-b border-cyber-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleSidebar,\n                                    className: \"p-2 rounded-lg text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-colors lg:hidden\",\n                                    children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 32\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 60\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-8 w-8 text-cyber-primary animate-cyber-glow\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden sm:block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl font-bold text-cyber-glow\",\n                                                    children: \"KodeXGuard\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-cyber-secondary uppercase tracking-wider\",\n                                                    children: isAdmin ? \"Admin Console\" : \"Cyber Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex flex-1 max-w-md mx-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search...\",\n                                        className: \"w-full pl-10 pr-4 py-2 rounded-lg input-cyber text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `w-2 h-2 rounded-full ${systemStatus === \"online\" ? \"bg-green-400\" : \"bg-red-400\"} animate-pulse`\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: [\n                                                \"System \",\n                                                systemStatus\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:block text-xs text-gray-400 font-mono\",\n                                    children: currentTime.toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 rounded-lg text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-colors relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-1 right-1 w-2 h-2 bg-cyber-secondary rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsProfileOpen(!isProfileOpen),\n                                            className: \"flex items-center space-x-3 p-2 rounded-lg hover:bg-cyber-primary/10 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-sm font-bold text-black\",\n                                                    children: user.username.charAt(0)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"hidden sm:block text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-white\",\n                                                            children: user.username\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `text-xs px-2 py-0.5 rounded-full ${getPlanColor(user.plan)}`,\n                                                            children: user.plan\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        isProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 top-full mt-2 w-64 bg-cyber-card border border-cyber-border rounded-lg shadow-xl z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 border-b border-cyber-border\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-lg font-bold text-black\",\n                                                                    children: user.username.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-white\",\n                                                                            children: user.fullName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-400\",\n                                                                            children: user.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        user.streak && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-1 text-xs text-cyber-secondary\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                                    lineNumber: 318,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        user.streak,\n                                                                                        \" day streak\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                                    lineNumber: 319,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        user.level && user.score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-3 grid grid-cols-2 gap-3 text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-bold text-cyber-primary\",\n                                                                            children: user.level\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"Level\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-bold text-cyber-secondary\",\n                                                                            children: user.score.toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"Score\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>router.push(\"/profile\"),\n                                                            className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-white hover:bg-cyber-primary/10 rounded-lg transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-cyber-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Profile\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>router.push(\"/settings\"),\n                                                            className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-white hover:bg-cyber-primary/10 rounded-lg transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-cyber-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Settings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleLogout,\n                                                            className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-red-400 hover:bg-red-500/10 rounded-lg transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Logout\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: `fixed top-16 left-0 z-40 w-64 h-[calc(100vh-4rem)] bg-cyber-card border-r border-cyber-border transform transition-transform duration-300 ease-in-out ${isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"} lg:translate-x-0`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 p-4 space-y-2 overflow-y-auto\",\n                            children: navItems.map((item)=>{\n                                const Icon = item.icon;\n                                const hasChildren = item.children && item.children.length > 0;\n                                const isExpanded = expandedItems.includes(item.name);\n                                const itemIsActive = hasChildren ? item.children?.some((child)=>isActive(child.href)) : isActive(item.href);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleExpanded(item.name),\n                                            className: `w-full flex items-center justify-between px-3 py-2 rounded-lg font-medium transition-colors duration-200 ${itemIsActive ? \"bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30\" : \"text-gray-300 hover:text-white hover:bg-cyber-primary/10\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: `h-4 w-4 transition-transform duration-200 ${isExpanded ? \"rotate-90\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>router.push(item.href),\n                                            className: `w-full flex items-center space-x-3 px-3 py-2 rounded-lg font-medium transition-colors duration-200 ${itemIsActive ? \"bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30\" : \"text-gray-300 hover:text-white hover:bg-cyber-primary/10\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 23\n                                                }, this),\n                                                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-auto bg-cyber-secondary/20 text-cyber-secondary px-2 py-0.5 rounded-full text-xs font-bold\",\n                                                    children: item.badge\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 21\n                                        }, this),\n                                        hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 mt-2 space-y-1\",\n                                            children: item.children?.map((child)=>{\n                                                const ChildIcon = child.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(child.href),\n                                                    className: `w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors duration-200 ${isActive(child.href) ? \"bg-cyber-primary/20 text-cyber-primary\" : \"text-gray-400 hover:text-white hover:bg-cyber-primary/10\"}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChildIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: child.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        child.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-auto bg-cyber-secondary/20 text-cyber-secondary px-1.5 py-0.5 rounded-full text-xs font-bold\",\n                                                            children: child.badge\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    ]\n                                                }, child.name, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 27\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t border-cyber-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mb-2\",\n                                        children: \"System Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-2 h-2 rounded-full ${systemStatus === \"online\" ? \"bg-green-400\" : \"bg-red-400\"} animate-pulse`\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-300 capitalize\",\n                                                children: systemStatus\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            isSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-30 bg-black/50 lg:hidden\",\n                onClick: ()=>setIsSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 465,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: `transition-all duration-300 pt-16 ${isSidebarOpen ? \"lg:ml-64\" : \"lg:ml-64\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-[calc(100vh-4rem)]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-cyber-card border-t border-cyber-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-6 py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-1 md:col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-8 w-8 text-cyber-primary animate-cyber-glow\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-cyber-glow\",\n                                                                    children: \"KodeXGuard\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-cyber-secondary uppercase tracking-wider\",\n                                                                    children: \"Cyber Security Platform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm mb-4\",\n                                                    children: \"Advanced cybersecurity platform for vulnerability scanning, OSINT intelligence, and comprehensive security analysis.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"All systems operational\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-bold text-white mb-4 uppercase tracking-wider\",\n                                                    children: \"Quick Links\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/dashboard\"),\n                                                                className: \"text-gray-400 hover:text-cyber-primary transition-colors text-sm\",\n                                                                children: \"Dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/profile\"),\n                                                                className: \"text-gray-400 hover:text-cyber-primary transition-colors text-sm\",\n                                                                children: \"Profile\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/settings\"),\n                                                                className: \"text-gray-400 hover:text-cyber-primary transition-colors text-sm\",\n                                                                children: \"Settings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/docs\"),\n                                                                className: \"text-gray-400 hover:text-cyber-primary transition-colors text-sm\",\n                                                                children: \"Documentation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-bold text-white mb-4 uppercase tracking-wider\",\n                                                    children: \"System\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Version:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-cyber-primary\",\n                                                                    children: \"v2.1.0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Uptime:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400\",\n                                                                    children: \"99.8%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Users:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-cyber-secondary\",\n                                                                    children: \"15.4K\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Scans:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-cyber-accent\",\n                                                                    children: \"89.4K\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 pt-8 border-t border-cyber-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"\\xa9 2024 KodeXGuard. All rights reserved.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 mt-4 md:mt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Last updated: \",\n                                                            currentTime.toLocaleDateString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-400\",\n                                                                children: \"Connected\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 472,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Toast.tsx":
/*!******************************!*\
  !*** ./components/Toast.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,useToast,toast,default auto */ \n\n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ToastProvider({ children }) {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((toast)=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        const newToast = {\n            ...toast,\n            id\n        };\n        setToasts((prev)=>[\n                ...prev,\n                newToast\n            ]);\n        // Auto remove after duration\n        const duration = toast.duration || 5000;\n        setTimeout(()=>{\n            removeToast(id);\n        }, duration);\n    }, []);\n    const removeToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    }, []);\n    const success = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"success\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    const error = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"error\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    const warning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"warning\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    const info = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, title)=>{\n        addToast({\n            type: \"info\",\n            message,\n            title\n        });\n    }, [\n        addToast\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toasts,\n            addToast,\n            removeToast,\n            success,\n            error,\n            warning,\n            info\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\nfunction useToast() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (context === undefined) {\n        throw new Error(\"useToast must be used within a ToastProvider\");\n    }\n    return context;\n}\nfunction ToastContainer() {\n    const { toasts, removeToast } = useToast();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastItem, {\n                toast: toast,\n                onRemove: ()=>removeToast(toast.id)\n            }, toast.id, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\nfunction ToastItem({ toast, onRemove }) {\n    const getIcon = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"h-5 w-5 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-5 w-5 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 16\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 16\n                }, this);\n            case \"info\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getBackgroundColor = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return \"bg-green-50 border-green-200\";\n            case \"error\":\n                return \"bg-red-50 border-red-200\";\n            case \"warning\":\n                return \"bg-yellow-50 border-yellow-200\";\n            case \"info\":\n                return \"bg-blue-50 border-blue-200\";\n        }\n    };\n    const getTextColor = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return \"text-green-800\";\n            case \"error\":\n                return \"text-red-800\";\n            case \"warning\":\n                return \"text-yellow-800\";\n            case \"info\":\n                return \"text-blue-800\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n      max-w-sm w-full shadow-lg rounded-lg border pointer-events-auto\n      ${getBackgroundColor()}\n      animate-slide-in-right\n    `,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: getIcon()\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3 w-0 flex-1\",\n                        children: [\n                            toast.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-sm font-medium ${getTextColor()}`,\n                                children: toast.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-sm ${toast.title ? \"mt-1\" : \"\"} ${getTextColor()}`,\n                                children: toast.message\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            toast.action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toast.action.onClick,\n                                    className: `text-sm font-medium underline hover:no-underline ${getTextColor()}`,\n                                    children: toast.action.label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4 flex-shrink-0 flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onRemove,\n                            className: `rounded-md inline-flex ${getTextColor()} hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\Toast.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n// Utility function for standalone usage\nconst toast = {\n    success: (message, title)=>{\n        // This will only work if ToastProvider is available\n        console.log(\"Success:\", title || \"\", message);\n    },\n    error: (message, title)=>{\n        console.log(\"Error:\", title || \"\", message);\n    },\n    warning: (message, title)=>{\n        console.log(\"Warning:\", title || \"\", message);\n    },\n    info: (message, title)=>{\n        console.log(\"Info:\", title || \"\", message);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ToastProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Toast.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useThemeClasses: () => (/* binding */ useThemeClasses)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme,useThemeClasses auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dark\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load theme from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"theme\");\n        const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n        const initialTheme = savedTheme || systemTheme;\n        setThemeState(initialTheme);\n        setMounted(true);\n    }, []);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        const root = document.documentElement;\n        // Remove existing theme classes\n        root.classList.remove(\"dark\", \"light\");\n        // Add current theme class\n        root.classList.add(theme);\n        // Update CSS variables based on theme\n        if (theme === \"light\") {\n            // Light theme variables\n            root.style.setProperty(\"--cyber-bg-primary\", \"#ffffff\");\n            root.style.setProperty(\"--cyber-bg-secondary\", \"#f8fafc\");\n            root.style.setProperty(\"--cyber-bg-tertiary\", \"#f1f5f9\");\n            root.style.setProperty(\"--cyber-bg-card\", \"#ffffff\");\n            root.style.setProperty(\"--cyber-bg-hover\", \"#f1f5f9\");\n            root.style.setProperty(\"--cyber-text-primary\", \"#1e293b\");\n            root.style.setProperty(\"--cyber-text-secondary\", \"#475569\");\n            root.style.setProperty(\"--cyber-text-muted\", \"#64748b\");\n            root.style.setProperty(\"--cyber-border\", \"#e2e8f0\");\n            root.style.setProperty(\"--cyber-border-bright\", \"#0ea5e9\");\n            // Keep cyber colors but adjust opacity for light mode\n            root.style.setProperty(\"--cyber-primary\", \"#0ea5e9\");\n            root.style.setProperty(\"--cyber-secondary\", \"#ec4899\");\n            root.style.setProperty(\"--cyber-accent\", \"#f59e0b\");\n        } else {\n            // Dark theme variables (cyberpunk)\n            root.style.setProperty(\"--cyber-bg-primary\", \"#0a0a0f\");\n            root.style.setProperty(\"--cyber-bg-secondary\", \"#1a1a2e\");\n            root.style.setProperty(\"--cyber-bg-tertiary\", \"#16213e\");\n            root.style.setProperty(\"--cyber-bg-card\", \"#0f0f23\");\n            root.style.setProperty(\"--cyber-bg-hover\", \"#1e1e3f\");\n            root.style.setProperty(\"--cyber-text-primary\", \"#ffffff\");\n            root.style.setProperty(\"--cyber-text-secondary\", \"#b0b0b0\");\n            root.style.setProperty(\"--cyber-text-muted\", \"#808080\");\n            root.style.setProperty(\"--cyber-border\", \"#333366\");\n            root.style.setProperty(\"--cyber-border-bright\", \"#00ffff\");\n            root.style.setProperty(\"--cyber-primary\", \"#00ffff\");\n            root.style.setProperty(\"--cyber-secondary\", \"#ff0080\");\n            root.style.setProperty(\"--cyber-accent\", \"#ffff00\");\n        }\n        // Save to localStorage\n        localStorage.setItem(\"theme\", theme);\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setThemeState((prev)=>prev === \"dark\" ? \"light\" : \"dark\");\n    };\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-cyber-dark\",\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\contexts\\\\ThemeContext.tsx\",\n            lineNumber: 95,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme,\n            setTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\n// Hook for theme-aware styling\nfunction useThemeClasses() {\n    const { theme } = useTheme();\n    return {\n        // Background classes\n        bgPrimary: theme === \"dark\" ? \"bg-cyber-dark\" : \"bg-white\",\n        bgSecondary: theme === \"dark\" ? \"bg-cyber-secondary\" : \"bg-gray-50\",\n        bgCard: theme === \"dark\" ? \"bg-cyber-card\" : \"bg-white\",\n        // Text classes\n        textPrimary: theme === \"dark\" ? \"text-white\" : \"text-gray-900\",\n        textSecondary: theme === \"dark\" ? \"text-gray-300\" : \"text-gray-600\",\n        textMuted: theme === \"dark\" ? \"text-gray-400\" : \"text-gray-500\",\n        // Border classes\n        border: theme === \"dark\" ? \"border-gray-700\" : \"border-gray-200\",\n        borderBright: theme === \"dark\" ? \"border-cyber-primary\" : \"border-blue-500\",\n        // Button classes\n        btnPrimary: theme === \"dark\" ? \"btn-cyber-primary\" : \"bg-blue-600 hover:bg-blue-700 text-white\",\n        btnSecondary: theme === \"dark\" ? \"btn-cyber-secondary\" : \"bg-gray-200 hover:bg-gray-300 text-gray-900\",\n        // Input classes\n        input: theme === \"dark\" ? \"input-cyber\" : \"bg-white border-gray-300 text-gray-900 focus:border-blue-500\",\n        // Card classes\n        card: theme === \"dark\" ? \"card-cyber\" : \"bg-white border border-gray-200 rounded-lg p-6 shadow-sm\",\n        // Effects\n        glow: theme === \"dark\" ? \"animate-cyber-glow\" : \"\",\n        pulse: theme === \"dark\" ? \"animate-cyber-pulse\" : \"\",\n        // Theme identifier\n        isDark: theme === \"dark\",\n        isLight: theme === \"light\"\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/auth.tsx":
/*!************************!*\
  !*** ./hooks/auth.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tokens, setTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isAuthenticated = !!user && !!tokens;\n    // Verify token validity\n    const verifyToken = async (token)=>{\n        try {\n            const response = await fetch(\"/api/auth/verify\", {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            return response.ok;\n        } catch  {\n            return false;\n        }\n    };\n    // Initialize auth state from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                if (false) {}\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n                await logout();\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const login = async (email, password, rememberMe = false)=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password,\n                    rememberMe\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                const { user: userData, tokens: tokenData } = data.data;\n                setUser(userData);\n                setTokens(tokenData);\n                // Store in localStorage\n                if (false) {}\n                console.log(\"✅ User logged in:\", userData.username);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: data.error || \"Login failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: \"Network error. Please try again.\"\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch(\"/api/auth/register\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(userData)\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                const { user: userInfo, tokens: tokenData } = data.data;\n                setUser(userInfo);\n                setTokens(tokenData);\n                // Store in localStorage\n                if (false) {}\n                console.log(\"✅ User registered:\", userInfo.username);\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: data.error || \"Registration failed\",\n                    details: data.details\n                };\n            }\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                error: \"Network error. Please try again.\"\n            };\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            // Call logout API if we have a token\n            if (tokens?.accessToken) {\n                await fetch(\"/api/auth/logout\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Authorization\": `Bearer ${tokens.accessToken}`,\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Logout API error:\", error);\n        } finally{\n            // Clear state\n            setUser(null);\n            setTokens(null);\n            // Clear localStorage\n            if (false) {}\n            console.log(\"✅ User logged out\");\n            router.push(\"/login\");\n        }\n    };\n    const refreshTokenFunc = async ()=>{\n        try {\n            if (!tokens?.refreshToken) {\n                return false;\n            }\n            const response = await fetch(\"/api/auth/refresh\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refreshToken: tokens.refreshToken\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                const { tokens: newTokens } = data.data;\n                setTokens(newTokens);\n                if (false) {}\n                return true;\n            } else {\n                await logout();\n                return false;\n            }\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            await logout();\n            return false;\n        }\n    };\n    const updateUser = (userData)=>{\n        if (user) {\n            const updatedUser = {\n                ...user,\n                ...userData\n            };\n            setUser(updatedUser);\n            if (false) {}\n        }\n    };\n    const value = {\n        user,\n        tokens,\n        isLoading,\n        isAuthenticated,\n        login,\n        register,\n        logout,\n        refreshToken: refreshTokenFunc,\n        updateUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\hooks\\\\auth.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/auth.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6662484993ed\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kYl9rb2RleGd1YXJkLy4vYXBwL2dsb2JhbHMuY3NzPzY0YTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2NjYyNDg0OTkzZWRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Toast */ \"(rsc)/./components/Toast.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(rsc)/./contexts/ThemeContext.tsx\");\n/* harmony import */ var _hooks_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/auth */ \"(rsc)/./hooks/auth.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"KodeXGuard - Cybersecurity & Bug Hunting Platform\",\n    description: \"Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan komunitas Bug Hunter\",\n    keywords: \"cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing\",\n    authors: [\n        {\n            name: \"KodeXGuard Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"id\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} scrollbar-cyber`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_auth__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/profile/page.tsx":
/*!******************************!*\
  !*** ./app/profile/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\app\profile\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/Toast.tsx":
/*!******************************!*\
  !*** ./components/Toast.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   toast: () => (/* binding */ e2),
/* harmony export */   useToast: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#ToastProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#useToast`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#toast`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\components\Toast.tsx#default`));


/***/ }),

/***/ "(rsc)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1),
/* harmony export */   useThemeClasses: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\contexts\ThemeContext.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\contexts\ThemeContext.tsx#useTheme`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\contexts\ThemeContext.tsx#useThemeClasses`);


/***/ }),

/***/ "(rsc)/./hooks/auth.tsx":
/*!************************!*\
  !*** ./hooks/auth.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\hooks\auth.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Users\Downloads\Kode-XGuard\hooks\auth.tsx#useAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();