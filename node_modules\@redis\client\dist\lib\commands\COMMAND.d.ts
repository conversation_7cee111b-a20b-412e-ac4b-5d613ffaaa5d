import { CommandParser } from '../client/parser';
import { ArrayReply, UnwrapReply } from '../RESP/types';
import { CommandRawReply, CommandReply } from './generic-transformers';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Returns an array with details about all Redis commands
     * @param parser - The Redis command parser
     */
    readonly parseCommand: (this: void, parser: CommandParser) => void;
    readonly transformReply: (this: void, reply: UnwrapReply<ArrayReply<CommandRawReply>>) => Array<CommandReply>;
};
export default _default;
//# sourceMappingURL=COMMAND.d.ts.map