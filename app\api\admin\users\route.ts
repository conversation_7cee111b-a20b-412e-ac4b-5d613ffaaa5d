'use server'

import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    // Authenticate request - only admins can access
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      )
    }

    // Get query parameters
    const url = new URL(request.url)
    const role = url.searchParams.get('role')
    const status = url.searchParams.get('status')
    const plan = url.searchParams.get('plan')
    const search = url.searchParams.get('search')
    const limit = parseInt(url.searchParams.get('limit') || '50')
    const offset = parseInt(url.searchParams.get('offset') || '0')

    // Build query
    let whereClause = '1=1'
    const params: any[] = []

    if (role && role !== 'all') {
      whereClause += ' AND u.role = ?'
      params.push(role)
    }

    if (status && status !== 'all') {
      whereClause += ' AND u.status = ?'
      params.push(status)
    }

    if (plan && plan !== 'all') {
      whereClause += ' AND u.plan = ?'
      params.push(plan)
    }

    if (search) {
      whereClause += ' AND (u.username LIKE ? OR u.email LIKE ? OR u.full_name LIKE ?)'
      const searchPattern = `%${search}%`
      params.push(searchPattern, searchPattern, searchPattern)
    }

    // Get users with additional information
    const [usersRows] = await db.query(`
      SELECT 
        u.*,
        COUNT(ak.id) as apiKeys,
        s.end_date as subscriptionEnd
      FROM users u
      LEFT JOIN api_keys ak ON u.id = ak.user_id AND ak.status = 'active'
      LEFT JOIN subscriptions s ON u.id = s.user_id AND s.status = 'active'
      WHERE ${whereClause}
      GROUP BY u.id
      ORDER BY u.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, limit, offset])

    // Get user statistics
    const [statsRows] = await db.query(`
      SELECT 
        COUNT(*) as totalUsers,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as activeUsers,
        COUNT(CASE WHEN plan != 'free' THEN 1 END) as premiumUsers,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as newUsersToday
      FROM users
    `)

    const stats = (statsRows as any[])[0] || {
      totalUsers: 0,
      activeUsers: 0,
      premiumUsers: 0,
      newUsersToday: 0
    }

    return NextResponse.json({
      success: true,
      data: {
        users: usersRows,
        stats: stats
      }
    })

  } catch (error) {
    console.error('Error getting users:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get users'
      },
      { status: 500 }
    )
  }
}
