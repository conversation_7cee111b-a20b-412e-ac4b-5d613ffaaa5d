"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const cli_options_1 = require("../cli-options");
// dependency section aliases that will be resolved to the full name
const depAliases = {
    dev: 'devDependencies',
    peer: 'peerDependencies',
    prod: 'dependencies',
    optional: 'optionalDependencies',
};
/** Gets a list of dependency sections based on options.dep. */
const resolveDepSections = (dep) => {
    // parse dep string and set default
    const depOptions = dep ? (typeof dep === 'string' ? dep.split(',') : dep) : cli_options_1.cliOptionsMap.dep.default;
    // map the dependency section option to a full dependency section name
    const depSections = depOptions.map(name => depAliases[name] || name);
    return depSections;
};
exports.default = resolveDepSections;
//# sourceMappingURL=resolveDepSections.js.map