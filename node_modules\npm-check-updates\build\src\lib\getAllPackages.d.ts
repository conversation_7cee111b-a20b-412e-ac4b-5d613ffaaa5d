import { Options } from '../types/Options';
import { PackageInfo } from '../types/PackageInfo';
/**
 * Gets all local packages, including workspaces (depending on -w, -ws, and -root).
 *
 * @param options the application options, used to determine which packages to return.
 * @returns PackageInfo[] an array of all package infos to be considered for updating
 */
declare function getAllPackages(options: Options): Promise<[PackageInfo[], string[]]>;
export default getAllPackages;
