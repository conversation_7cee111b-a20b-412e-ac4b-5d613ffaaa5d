// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.
export { MessageHeader } from './fb/message-header.mjs';
export { Type, BufferType, DateUnit, TimeUnit, Precision, UnionMode, IntervalUnit, MetadataVersion, } from './enum.mjs';
export { Data, makeData } from './data.mjs';
export { DataType, Null, Bool, Int, Int8, Int16, Int32, Int64, Uint8, Uint16, Uint32, Uint64, Float, Float16, Float32, Float64, Utf8, LargeUtf8, Binary, LargeBinary, FixedSizeBinary, Date_, DateDay, DateMillisecond, Timestamp, TimestampSecond, TimestampMillisecond, TimestampMicrosecond, TimestampNanosecond, Time, TimeSecond, TimeMillisecond, TimeMicrosecond, TimeNanosecond, Decimal, List, Struct, Union, DenseUnion, SparseUnion, Dictionary, Interval, IntervalDayTime, IntervalYearMonth, Duration, DurationSecond, DurationMillisecond, DurationMicrosecond, DurationNanosecond, FixedSizeList, Map_ } from './type.mjs';
export { Table, makeTable, tableFromArrays } from './table.mjs';
export { Vector, makeVector } from './vector.mjs';
export { Visitor } from './visitor.mjs';
export { Schema, Field } from './schema.mjs';
export { MapRow } from './row/map.mjs';
export { StructRow } from './row/struct.mjs';
export { Builder } from './builder.mjs';
export { makeBuilder, vectorFromArray, tableFromJSON, builderThroughIterable, builderThroughAsyncIterable } from './factories.mjs';
export { BoolBuilder } from './builder/bool.mjs';
export { NullBuilder } from './builder/null.mjs';
export { DateBuilder, DateDayBuilder, DateMillisecondBuilder } from './builder/date.mjs';
export { DecimalBuilder } from './builder/decimal.mjs';
export { DictionaryBuilder } from './builder/dictionary.mjs';
export { FixedSizeBinaryBuilder } from './builder/fixedsizebinary.mjs';
export { FloatBuilder, Float16Builder, Float32Builder, Float64Builder } from './builder/float.mjs';
export { IntBuilder, Int8Builder, Int16Builder, Int32Builder, Int64Builder, Uint8Builder, Uint16Builder, Uint32Builder, Uint64Builder } from './builder/int.mjs';
export { TimeBuilder, TimeSecondBuilder, TimeMillisecondBuilder, TimeMicrosecondBuilder, TimeNanosecondBuilder } from './builder/time.mjs';
export { TimestampBuilder, TimestampSecondBuilder, TimestampMillisecondBuilder, TimestampMicrosecondBuilder, TimestampNanosecondBuilder } from './builder/timestamp.mjs';
export { IntervalBuilder, IntervalDayTimeBuilder, IntervalYearMonthBuilder } from './builder/interval.mjs';
export { DurationBuilder, DurationSecondBuilder, DurationMillisecondBuilder, DurationMicrosecondBuilder, DurationNanosecondBuilder } from './builder/duration.mjs';
export { Utf8Builder } from './builder/utf8.mjs';
export { LargeUtf8Builder } from './builder/largeutf8.mjs';
export { BinaryBuilder } from './builder/binary.mjs';
export { LargeBinaryBuilder } from './builder/largebinary.mjs';
export { ListBuilder } from './builder/list.mjs';
export { FixedSizeListBuilder } from './builder/fixedsizelist.mjs';
export { MapBuilder } from './builder/map.mjs';
export { StructBuilder } from './builder/struct.mjs';
export { UnionBuilder, SparseUnionBuilder, DenseUnionBuilder } from './builder/union.mjs';
export { ByteStream, AsyncByteStream, AsyncByteQueue } from './io/stream.mjs';
export { RecordBatchReader, RecordBatchFileReader, RecordBatchStreamReader, AsyncRecordBatchFileReader, AsyncRecordBatchStreamReader } from './ipc/reader.mjs';
export { RecordBatchWriter, RecordBatchFileWriter, RecordBatchStreamWriter, RecordBatchJSONWriter } from './ipc/writer.mjs';
export { tableToIPC, tableFromIPC } from './ipc/serialization.mjs';
export { MessageReader, AsyncMessageReader, JSONMessageReader } from './ipc/message.mjs';
export { Message } from './ipc/metadata/message.mjs';
export { RecordBatch } from './recordbatch.mjs';
import * as util_bn_ from './util/bn.mjs';
import * as util_int_ from './util/int.mjs';
import * as util_bit_ from './util/bit.mjs';
import * as util_math_ from './util/math.mjs';
import * as util_buffer_ from './util/buffer.mjs';
import * as util_vector_ from './util/vector.mjs';
import * as util_pretty_ from './util/pretty.mjs';
import { compareSchemas, compareFields, compareTypes } from './visitor/typecomparator.mjs';
/** @ignore */
export const util = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, util_bn_), util_int_), util_bit_), util_math_), util_buffer_), util_vector_), util_pretty_), { compareSchemas,
    compareFields,
    compareTypes });

//# sourceMappingURL=Arrow.mjs.map
