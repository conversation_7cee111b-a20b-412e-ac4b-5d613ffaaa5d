'use server'

import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { WhatsAppBotService } from '@/lib/services/whatsapp-bot'
import { z } from 'zod'

const connectSchema = z.object({
  sessionName: z.string().optional().default('kodexguard-wa')
})

// Global bot service instance
let botService: WhatsAppBotService | null = null

function getBotService(): WhatsAppBotService {
  if (!botService) {
    botService = new WhatsAppBotService()
  }
  return botService
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate request - only admins can connect bots
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const validatedData = connectSchema.parse(body)

    // Get bot service
    const service = getBotService()

    // Create new WhatsApp session
    const sessionId = await service.createSession(validatedData.sessionName)

    return NextResponse.json({
      success: true,
      message: 'WhatsApp bot connection initiated',
      data: {
        sessionId,
        sessionName: validatedData.sessionName,
        status: 'connecting'
      }
    })

  } catch (error) {
    console.error('WhatsApp bot connection error:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to connect WhatsApp bot',
        error: error instanceof z.ZodError ? error.errors : undefined
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Authenticate request
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      )
    }

    // Get bot service
    const service = getBotService()

    // Get all sessions
    const sessions = await service.getAllSessions()

    return NextResponse.json({
      success: true,
      data: {
        sessions: sessions.map(session => ({
          id: session.id,
          status: session.status,
          lastActivity: session.lastActivity,
          connectedAt: session.connectedAt,
          error: session.error,
          hasQrCode: !!session.qrCode
        }))
      }
    })

  } catch (error) {
    console.error('Error getting WhatsApp sessions:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get WhatsApp sessions'
      },
      { status: 500 }
    )
  }
}
