# 🗄️ Database Setup Guide - KodeXGuard

Panduan lengkap untuk setup database KodeXGuard dengan semua fitur baru yang telah diimplementasi.

## 📋 Prerequisites

1. **MySQL Server** (versi 8.0 atau lebih tinggi)
2. **Node.js** (versi 18.0 atau lebih tinggi)
3. **npm** atau **yarn**

## 🚀 Quick Setup

### 1. Install Dependencies

```bash
# Install semua dependencies termasuk ts-node dan telegraf
npm install

# Atau jika menggunakan yarn
yarn install
```

### 2. One-Command Setup (Recommended)

```bash
# Setup semua fitur baru sekaligus
npm run setup:new
```

**Script ini akan:**
- ✅ Test koneksi database
- ✅ Jalankan semua migrasi
- ✅ Seed data fitur baru
- ✅ Test setup database

### 3. Manual Step-by-Step Setup

Jika ingin menjalankan setup secara manual:

### 2. Environment Configuration

Pastikan file `.env` sudah dikonfigurasi dengan benar:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=kodexguard

# Bot Configuration (Optional)
WHATSAPP_SESSION_PATH=./sessions/whatsapp
TELEGRAM_BOT_TOKEN=your_telegram_bot_token

# Payment Gateway Configuration (Optional)
TRIPAY_MERCHANT_CODE=your_tripay_merchant_code
TRIPAY_API_KEY=your_tripay_api_key
TRIPAY_PRIVATE_KEY=your_tripay_private_key

MIDTRANS_SERVER_KEY=your_midtrans_server_key
MIDTRANS_CLIENT_KEY=your_midtrans_client_key

XENDIT_SECRET_KEY=your_xendit_secret_key
```

### 3. Run Database Migrations

```bash
# Test koneksi database terlebih dahulu
npm run db:test

# Jalankan semua migrasi yang belum diaplikasikan
npm run db:migrate

# Atau jika ingin menjalankan file migrasi tertentu
npm run db:migrate:file 001_add_new_features.sql

# Seed data fitur baru
npm run db:seed:new
```

## 🛠️ Available Commands

```bash
# Database Management
npm run db:test              # Test database connection
npm run db:migrate           # Run all pending migrations
npm run db:migrate:file      # Run specific migration file
npm run db:seed              # Seed basic data
npm run db:seed:new          # Seed new features data

# Complete Setup
npm run setup:new            # Complete setup for new features (recommended)

# Development
npm run dev                  # Start development server
npm run build                # Build for production
npm run start                # Start production server
```

## 📊 Tabel-Tabel Baru yang Akan Dibuat

### 1. **Enhanced Users Table**
- Kolom baru: `phone`, `telegram_id`, `whatsapp_number`, `status`, `avatar_url`, `timezone`, `language`
- Role baru: `super_admin`
- Plan baru: `Student`, `Hobby`, `Bughunter`, `Cybersecurity`

### 2. **Subscription & Payment System**
```sql
- plans                 # Master data plan subscription
- subscriptions         # User subscription records
- payments              # Payment transactions
- payment_logs          # Payment audit logs
```

### 3. **Bot Management System**
```sql
- bot_instances         # WhatsApp & Telegram bot instances
- bot_messages          # Bot message history
```

### 4. **Enhanced OSINT System**
```sql
- osint_queries         # Enhanced OSINT query records
```

### 5. **Scoring & Achievement System**
```sql
- score_history         # User scoring history
- user_achievements     # User achievement records
- user_streaks          # User activity streaks
```

### 6. **Admin & Monitoring**
```sql
- admin_logs            # Admin activity logs
- migrations            # Database migration tracking
```

## 🔧 Manual Migration Steps

Jika Anda ingin menjalankan migrasi secara manual:

### 1. Connect to MySQL

```bash
mysql -u your_username -p kodexguard
```

### 2. Run Migration SQL

```sql
-- Copy dan paste isi dari lib/database/migrations/001_add_new_features.sql
-- Atau jalankan file langsung:
source /path/to/kodexguard/lib/database/migrations/001_add_new_features.sql;
```

## 📝 Default Data yang Akan Diinsert

### Plans
- **Free**: 0 IDR/month - Basic features
- **Student**: 50,000 IDR/month - Enhanced features untuk pelajar
- **Hobby**: 150,000 IDR/month - Untuk hobbyist
- **Bughunter**: 300,000 IDR/month - Professional bug hunting
- **Cybersecurity**: 500,000 IDR/month - Enterprise features

### Bot Instances
- WhatsApp Bot placeholder
- Telegram Bot placeholder

## 🔍 Verifikasi Setup

### 1. Check Tables

```sql
-- Cek semua tabel yang telah dibuat
SHOW TABLES;

-- Cek struktur tabel users yang telah diupdate
DESCRIBE users;

-- Cek data plans yang telah diinsert
SELECT * FROM plans;
```

### 2. Test Database Connection

```bash
# Test koneksi database melalui aplikasi
npm run dev
```

### 3. Check Migration Status

```sql
-- Cek migrasi yang telah dijalankan
SELECT * FROM migrations ORDER BY applied_at DESC;
```

## 🚨 Troubleshooting

### Error: Table already exists
Jika mendapat error "table already exists", ini normal karena migration script menggunakan `IF NOT EXISTS`.

### Error: Column already exists
Migration script menggunakan `ADD COLUMN IF NOT EXISTS` untuk menghindari error ini.

### Error: Foreign key constraint fails
Pastikan tabel parent sudah ada sebelum membuat foreign key. Migration script sudah diurutkan dengan benar.

### Error: Access denied
Pastikan user database memiliki privileges yang cukup:

```sql
GRANT ALL PRIVILEGES ON kodexguard.* TO 'your_user'@'localhost';
FLUSH PRIVILEGES;
```

## 📈 Post-Migration Steps

### 1. Update Admin User

```sql
-- Update user admin menjadi super_admin
UPDATE users SET role = 'super_admin' WHERE username = 'admin';
```

### 2. Test New Features

1. **OSINT Features**: Test NIK, NPWP, IMEI investigation
2. **Bot Integration**: Setup WhatsApp dan Telegram bots
3. **Payment System**: Test payment gateway integration
4. **Scoring System**: Verify point calculation
5. **Admin Panel**: Check all admin features

### 3. Configure Bot Tokens

```bash
# Setup WhatsApp bot (akan generate QR code)
# Setup Telegram bot dengan token dari BotFather
```

## 🔄 Rollback (Jika Diperlukan)

Jika perlu rollback migrasi:

```sql
-- Backup database terlebih dahulu
mysqldump -u username -p kodexguard > backup_before_migration.sql

-- Drop tabel baru (hati-hati!)
DROP TABLE IF EXISTS user_streaks;
DROP TABLE IF EXISTS user_achievements;
DROP TABLE IF EXISTS score_history;
-- ... dst
```

## 📞 Support

Jika mengalami masalah dengan database setup:

1. Check log aplikasi: `npm run logs`
2. Check MySQL error log
3. Verify environment variables
4. Test database connection manually

---

**✅ Setelah menjalankan migrasi ini, platform KodeXGuard akan memiliki semua fitur baru:**
- ✅ Enhanced OSINT dengan data Indonesia
- ✅ WhatsApp & Telegram bot integration
- ✅ Payment system dengan multiple gateway
- ✅ Advanced scoring & achievement system
- ✅ Complete admin panel dengan monitoring

**🚀 Platform siap untuk production deployment!**
