// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.
// @ts-nocheck
/* eslint-disable */
/**
 * @fileoverview Closure Compiler externs for Arrow
 * @externs
 * @suppress {duplicate,checkTypes}
 */
/** @type {symbol} */
Symbol.iterator;
/** @type {symbol} */
Symbol.toPrimitive;
/** @type {symbol} */
Symbol.asyncIterator;

var Encoding = function() {};
/** @type {?} */
Encoding[1] = function() {};
/** @type {?} */
Encoding[2] = function() {};
/** @type {?} */
Encoding.UTF8_BYTES = function() {};
/** @type {?} */
Encoding.UTF16_STRING = function() {};

var AsyncByteQueue = function() {};
/** @type {?} */
AsyncByteQueue.prototype.write;
/** @type {?} */
AsyncByteQueue.prototype.toUint8Array;
var AsyncByteStream = function() {};
/** @type {?} */
AsyncByteStream.prototype.next;
/** @type {?} */
AsyncByteStream.prototype.throw;
/** @type {?} */
AsyncByteStream.prototype.return;
/** @type {?} */
AsyncByteStream.prototype.closed;
/** @type {?} */
AsyncByteStream.prototype.cancel;
/** @type {?} */
AsyncByteStream.prototype.peek;
/** @type {?} */
AsyncByteStream.prototype.read;
var AsyncMessageReader = function() {};
/** @type {?} */
AsyncMessageReader.prototype.next;
/** @type {?} */
AsyncMessageReader.prototype.throw;
/** @type {?} */
AsyncMessageReader.prototype.return;
/** @type {?} */
AsyncMessageReader.prototype.readMessage;
/** @type {?} */
AsyncMessageReader.prototype.readMessageBody;
/** @type {?} */
AsyncMessageReader.prototype.readSchema;
/** @type {?} */
AsyncMessageReader.prototype.readMetadataLength;
/** @type {?} */
AsyncMessageReader.prototype.readMetadata;
var AsyncRecordBatchFileReader = function() {};
var AsyncRecordBatchStreamReader = function() {};
/** @type {?} */
AsyncRecordBatchStreamReader.prototype.readAll;
var Binary = function() {};
/** @type {?} */
Binary.prototype.ArrayType;
var BinaryBuilder = function() {};
/** @type {?} */
BinaryBuilder.prototype.byteLength;
/** @type {?} */
BinaryBuilder.prototype.setValue;
var Bool = function() {};
/** @type {?} */
Bool.prototype.ArrayType;
var BoolBuilder = function() {};
/** @type {?} */
BoolBuilder.prototype.setValue;
var BufferType = function() {};
/** @type {?} */
BufferType[0] = function() {};
/** @type {?} */
BufferType[1] = function() {};
/** @type {?} */
BufferType[2] = function() {};
/** @type {?} */
BufferType[3] = function() {};
/** @type {?} */
BufferType.OFFSET = function() {};
/** @type {?} */
BufferType.DATA = function() {};
/** @type {?} */
BufferType.VALIDITY = function() {};
/** @type {?} */
BufferType.TYPE = function() {};
var Builder = function() {};
/** @type {?} */
Builder.throughNode = function() {};
/** @type {?} */
Builder.throughDOM = function() {};
/** @type {?} */
Builder.prototype.toVector;
/** @type {?} */
Builder.prototype.ArrayType;
/** @type {?} */
Builder.prototype.nullCount;
/** @type {?} */
Builder.prototype.numChildren;
/** @type {?} */
Builder.prototype.byteLength;
/** @type {?} */
Builder.prototype.reservedLength;
/** @type {?} */
Builder.prototype.reservedByteLength;
/** @type {?} */
Builder.prototype.valueOffsets;
/** @type {?} */
Builder.prototype.values;
/** @type {?} */
Builder.prototype.nullBitmap;
/** @type {?} */
Builder.prototype.typeIds;
/** @type {?} */
Builder.prototype.append;
/** @type {?} */
Builder.prototype.isValid;
/** @type {?} */
Builder.prototype.set;
/** @type {?} */
Builder.prototype.setValue;
/** @type {?} */
Builder.prototype.setValid;
/** @type {?} */
Builder.prototype.addChild;
/** @type {?} */
Builder.prototype.getChildAt;
/** @type {?} */
Builder.prototype.flush;
/** @type {?} */
Builder.prototype.finish;
/** @type {?} */
Builder.prototype.clear;
/** @type {?} */
Builder.prototype.stride;
/** @type {?} */
Builder.prototype.children;
/** @type {?} */
Builder.prototype.finished;
/** @type {?} */
Builder.prototype.nullValues;
var ByteStream = function() {};
/** @type {?} */
ByteStream.prototype.next;
/** @type {?} */
ByteStream.prototype.throw;
/** @type {?} */
ByteStream.prototype.return;
/** @type {?} */
ByteStream.prototype.peek;
/** @type {?} */
ByteStream.prototype.read;
var Data = function() {};
/** @type {?} */
Data.prototype.typeId;
/** @type {?} */
Data.prototype.ArrayType;
/** @type {?} */
Data.prototype.buffers;
/** @type {?} */
Data.prototype.nullable;
/** @type {?} */
Data.prototype.byteLength;
/** @type {?} */
Data.prototype.nullCount;
/** @type {?} */
Data.prototype.getValid;
/** @type {?} */
Data.prototype.setValid;
/** @type {?} */
Data.prototype.clone;
/** @type {?} */
Data.prototype.slice;
/** @type {?} */
Data.prototype.children;
var DataType = function() {};
/** @type {?} */
DataType.isNull = function() {};
/** @type {?} */
DataType.isInt = function() {};
/** @type {?} */
DataType.isFloat = function() {};
/** @type {?} */
DataType.isBinary = function() {};
/** @type {?} */
DataType.isLargeBinary = function() {};
/** @type {?} */
DataType.isUtf8 = function() {};
/** @type {?} */
DataType.isLargeUtf8 = function() {};
/** @type {?} */
DataType.isBool = function() {};
/** @type {?} */
DataType.isDecimal = function() {};
/** @type {?} */
DataType.isDate = function() {};
/** @type {?} */
DataType.isTime = function() {};
/** @type {?} */
DataType.isTimestamp = function() {};
/** @type {?} */
DataType.isInterval = function() {};
/** @type {?} */
DataType.isDuration = function() {};
/** @type {?} */
DataType.isList = function() {};
/** @type {?} */
DataType.isStruct = function() {};
/** @type {?} */
DataType.isUnion = function() {};
/** @type {?} */
DataType.isFixedSizeBinary = function() {};
/** @type {?} */
DataType.isFixedSizeList = function() {};
/** @type {?} */
DataType.isMap = function() {};
/** @type {?} */
DataType.isDictionary = function() {};
/** @type {?} */
DataType.isDenseUnion = function() {};
/** @type {?} */
DataType.isSparseUnion = function() {};
/** @type {?} */
DataType.prototype.children;
/** @type {?} */
DataType.prototype.ArrayType;
/** @type {?} */
DataType.prototype.OffsetArrayType;
var DateBuilder = function() {};
var DateDay = function() {};
var DateDayBuilder = function() {};
var DateMillisecond = function() {};
var DateMillisecondBuilder = function() {};
var DateUnit = function() {};
/** @type {?} */
DateUnit[0] = function() {};
/** @type {?} */
DateUnit[1] = function() {};
/** @type {?} */
DateUnit.DAY = function() {};
/** @type {?} */
DateUnit.MILLISECOND = function() {};
var Date_ = function() {};
/** @type {?} */
Date_.prototype.ArrayType;
/** @type {?} */
Date_.prototype.unit;
var Decimal = function() {};
/** @type {?} */
Decimal.prototype.scale;
/** @type {?} */
Decimal.prototype.precision;
/** @type {?} */
Decimal.prototype.ArrayType;
var DecimalBuilder = function() {};
var DenseUnion = function() {};
var DenseUnionBuilder = function() {};
/** @type {?} */
DenseUnionBuilder.prototype.setValue;
var Dictionary = function() {};
/** @type {?} */
Dictionary.prototype.children;
/** @type {?} */
Dictionary.prototype.valueType;
/** @type {?} */
Dictionary.prototype.ArrayType;
/** @type {?} */
Dictionary.prototype.id;
/** @type {?} */
Dictionary.prototype.indices;
/** @type {?} */
Dictionary.prototype.isOrdered;
/** @type {?} */
Dictionary.prototype.dictionary;
var DictionaryBuilder = function() {};
/** @type {?} */
DictionaryBuilder.prototype.values;
/** @type {?} */
DictionaryBuilder.prototype.nullCount;
/** @type {?} */
DictionaryBuilder.prototype.nullBitmap;
/** @type {?} */
DictionaryBuilder.prototype.byteLength;
/** @type {?} */
DictionaryBuilder.prototype.reservedLength;
/** @type {?} */
DictionaryBuilder.prototype.reservedByteLength;
/** @type {?} */
DictionaryBuilder.prototype.isValid;
/** @type {?} */
DictionaryBuilder.prototype.setValid;
/** @type {?} */
DictionaryBuilder.prototype.setValue;
/** @type {?} */
DictionaryBuilder.prototype.flush;
/** @type {?} */
DictionaryBuilder.prototype.finish;
/** @type {?} */
DictionaryBuilder.prototype.clear;
/** @type {?} */
DictionaryBuilder.prototype.valueToKey;
var Duration = function() {};
/** @type {?} */
Duration.prototype.unit;
/** @type {?} */
Duration.prototype.ArrayType;
var DurationBuilder = function() {};
var DurationMicrosecond = function() {};
var DurationMicrosecondBuilder = function() {};
var DurationMillisecond = function() {};
var DurationMillisecondBuilder = function() {};
var DurationNanosecond = function() {};
var DurationNanosecondBuilder = function() {};
var DurationSecond = function() {};
var DurationSecondBuilder = function() {};
var Field = function() {};
/** @type {?} */
Field.new = function() {};
/** @type {?} */
Field.encode = function() {};
/** @type {?} */
Field.decode = function() {};
/** @type {?} */
Field.fromJSON = function() {};
/** @type {?} */
Field.prototype.typeId;
/** @type {?} */
Field.prototype.clone;
/** @type {?} */
Field.prototype.type;
/** @type {?} */
Field.prototype.nullable;
/** @type {?} */
Field.prototype.metadata;
var FixedSizeBinary = function() {};
/** @type {?} */
FixedSizeBinary.prototype.byteWidth;
/** @type {?} */
FixedSizeBinary.prototype.ArrayType;
var FixedSizeBinaryBuilder = function() {};
var FixedSizeList = function() {};
/** @type {?} */
FixedSizeList.prototype.valueType;
/** @type {?} */
FixedSizeList.prototype.valueField;
/** @type {?} */
FixedSizeList.prototype.ArrayType;
/** @type {?} */
FixedSizeList.prototype.children;
/** @type {?} */
FixedSizeList.prototype.listSize;
var FixedSizeListBuilder = function() {};
/** @type {?} */
FixedSizeListBuilder.prototype.setValue;
/** @type {?} */
FixedSizeListBuilder.prototype.addChild;
var Float = function() {};
/** @type {?} */
Float.prototype.ArrayType;
/** @type {?} */
Float.prototype.precision;
var Float16 = function() {};
/** @type {?} */
Float16.prototype.ArrayType;
var Float16Builder = function() {};
/** @type {?} */
Float16Builder.prototype.setValue;
var Float32 = function() {};
/** @type {?} */
Float32.prototype.ArrayType;
var Float32Builder = function() {};
var Float64 = function() {};
/** @type {?} */
Float64.prototype.ArrayType;
var Float64Builder = function() {};
var FloatBuilder = function() {};
/** @type {?} */
FloatBuilder.prototype.setValue;
var Int = function() {};
/** @type {?} */
Int.prototype.ArrayType;
/** @type {?} */
Int.prototype.isSigned;
/** @type {?} */
Int.prototype.bitWidth;
var Int16 = function() {};
/** @type {?} */
Int16.prototype.ArrayType;
var Int16Builder = function() {};
var Int32 = function() {};
/** @type {?} */
Int32.prototype.ArrayType;
var Int32Builder = function() {};
var Int64 = function() {};
/** @type {?} */
Int64.prototype.ArrayType;
var Int64Builder = function() {};
var Int8 = function() {};
/** @type {?} */
Int8.prototype.ArrayType;
var Int8Builder = function() {};
var IntBuilder = function() {};
/** @type {?} */
IntBuilder.prototype.setValue;
var Interval = function() {};
/** @type {?} */
Interval.prototype.unit;
/** @type {?} */
Interval.prototype.ArrayType;
var IntervalBuilder = function() {};
var IntervalDayTime = function() {};
var IntervalDayTimeBuilder = function() {};
var IntervalUnit = function() {};
/** @type {?} */
IntervalUnit[0] = function() {};
/** @type {?} */
IntervalUnit[1] = function() {};
/** @type {?} */
IntervalUnit[2] = function() {};
/** @type {?} */
IntervalUnit.YEAR_MONTH = function() {};
/** @type {?} */
IntervalUnit.DAY_TIME = function() {};
/** @type {?} */
IntervalUnit.MONTH_DAY_NANO = function() {};
var IntervalYearMonth = function() {};
var IntervalYearMonthBuilder = function() {};
var JSONMessageReader = function() {};
/** @type {?} */
JSONMessageReader.prototype.next;
/** @type {?} */
JSONMessageReader.prototype.readMessageBody;
/** @type {?} */
JSONMessageReader.prototype.readMessage;
/** @type {?} */
JSONMessageReader.prototype.readSchema;
var LargeBinary = function() {};
/** @type {?} */
LargeBinary.prototype.ArrayType;
/** @type {?} */
LargeBinary.prototype.OffsetArrayType;
var LargeBinaryBuilder = function() {};
/** @type {?} */
LargeBinaryBuilder.prototype.byteLength;
/** @type {?} */
LargeBinaryBuilder.prototype.setValue;
var LargeUtf8 = function() {};
/** @type {?} */
LargeUtf8.prototype.ArrayType;
/** @type {?} */
LargeUtf8.prototype.OffsetArrayType;
var LargeUtf8Builder = function() {};
/** @type {?} */
LargeUtf8Builder.prototype.byteLength;
/** @type {?} */
LargeUtf8Builder.prototype.setValue;
var List = function() {};
/** @type {?} */
List.prototype.valueType;
/** @type {?} */
List.prototype.valueField;
/** @type {?} */
List.prototype.ArrayType;
/** @type {?} */
List.prototype.children;
var ListBuilder = function() {};
/** @type {?} */
ListBuilder.prototype.addChild;
var MapBuilder = function() {};
/** @type {?} */
MapBuilder.prototype.set;
/** @type {?} */
MapBuilder.prototype.setValue;
/** @type {?} */
MapBuilder.prototype.addChild;
var MapRow = function() {};
/** @type {?} */
MapRow.prototype.size;
/** @type {?} */
MapRow.prototype.toArray;
/** @type {?} */
MapRow.prototype.toJSON;
var Map_ = function() {};
/** @type {?} */
Map_.prototype.keyType;
/** @type {?} */
Map_.prototype.valueType;
/** @type {?} */
Map_.prototype.childType;
/** @type {?} */
Map_.prototype.children;
/** @type {?} */
Map_.prototype.keysSorted;
var Message = function() {};
/** @type {?} */
Message.fromJSON = function() {};
/** @type {?} */
Message.decode = function() {};
/** @type {?} */
Message.encode = function() {};
/** @type {?} */
Message.from = function() {};
/** @type {?} */
Message.prototype.type;
/** @type {?} */
Message.prototype.version;
/** @type {?} */
Message.prototype.headerType;
/** @type {?} */
Message.prototype.bodyLength;
/** @type {?} */
Message.prototype.header;
/** @type {?} */
Message.prototype.isSchema;
/** @type {?} */
Message.prototype.isRecordBatch;
/** @type {?} */
Message.prototype.isDictionaryBatch;
var MessageHeader = function() {};
/** @type {?} */
MessageHeader[0] = function() {};
/** @type {?} */
MessageHeader[1] = function() {};
/** @type {?} */
MessageHeader[2] = function() {};
/** @type {?} */
MessageHeader[3] = function() {};
/** @type {?} */
MessageHeader[4] = function() {};
/** @type {?} */
MessageHeader[5] = function() {};
/** @type {?} */
MessageHeader.NONE = function() {};
/** @type {?} */
MessageHeader.Schema = function() {};
/** @type {?} */
MessageHeader.DictionaryBatch = function() {};
/** @type {?} */
MessageHeader.RecordBatch = function() {};
/** @type {?} */
MessageHeader.Tensor = function() {};
/** @type {?} */
MessageHeader.SparseTensor = function() {};
var MessageReader = function() {};
/** @type {?} */
MessageReader.prototype.next;
/** @type {?} */
MessageReader.prototype.throw;
/** @type {?} */
MessageReader.prototype.return;
/** @type {?} */
MessageReader.prototype.readMessage;
/** @type {?} */
MessageReader.prototype.readMessageBody;
/** @type {?} */
MessageReader.prototype.readSchema;
/** @type {?} */
MessageReader.prototype.readMetadataLength;
/** @type {?} */
MessageReader.prototype.readMetadata;
var MetadataVersion = function() {};
/** @type {?} */
MetadataVersion[0] = function() {};
/** @type {?} */
MetadataVersion[1] = function() {};
/** @type {?} */
MetadataVersion[2] = function() {};
/** @type {?} */
MetadataVersion[3] = function() {};
/** @type {?} */
MetadataVersion[4] = function() {};
/** @type {?} */
MetadataVersion.V1 = function() {};
/** @type {?} */
MetadataVersion.V2 = function() {};
/** @type {?} */
MetadataVersion.V3 = function() {};
/** @type {?} */
MetadataVersion.V4 = function() {};
/** @type {?} */
MetadataVersion.V5 = function() {};
var Null = function() {};
var NullBuilder = function() {};
/** @type {?} */
NullBuilder.prototype.setValue;
/** @type {?} */
NullBuilder.prototype.setValid;
var Precision = function() {};
/** @type {?} */
Precision[0] = function() {};
/** @type {?} */
Precision[1] = function() {};
/** @type {?} */
Precision[2] = function() {};
/** @type {?} */
Precision.HALF = function() {};
/** @type {?} */
Precision.SINGLE = function() {};
/** @type {?} */
Precision.DOUBLE = function() {};
var RecordBatch = function() {};
/** @type {?} */
RecordBatch.prototype.dictionaries;
/** @type {?} */
RecordBatch.prototype.numCols;
/** @type {?} */
RecordBatch.prototype.numRows;
/** @type {?} */
RecordBatch.prototype.nullCount;
/** @type {?} */
RecordBatch.prototype.isValid;
/** @type {?} */
RecordBatch.prototype.get;
/** @type {?} */
RecordBatch.prototype.at;
/** @type {?} */
RecordBatch.prototype.set;
/** @type {?} */
RecordBatch.prototype.indexOf;
/** @type {?} */
RecordBatch.prototype.toArray;
/** @type {?} */
RecordBatch.prototype.concat;
/** @type {?} */
RecordBatch.prototype.slice;
/** @type {?} */
RecordBatch.prototype.getChild;
/** @type {?} */
RecordBatch.prototype.getChildAt;
/** @type {?} */
RecordBatch.prototype.setChild;
/** @type {?} */
RecordBatch.prototype.setChildAt;
/** @type {?} */
RecordBatch.prototype.select;
/** @type {?} */
RecordBatch.prototype.selectAt;
var RecordBatchFileReader = function() {};
/** @type {?} */
RecordBatchFileReader.throughDOM = function() {};
var RecordBatchFileWriter = function() {};
/** @type {?} */
RecordBatchFileWriter.writeAll = function() {};
/** @type {?} */
RecordBatchFileWriter.throughDOM = function() {};
var RecordBatchJSONWriter = function() {};
/** @type {?} */
RecordBatchJSONWriter.writeAll = function() {};
/** @type {?} */
RecordBatchJSONWriter.prototype.close;
var RecordBatchReader = function() {};
/** @type {?} */
RecordBatchReader.throughNode = function() {};
/** @type {?} */
RecordBatchReader.throughDOM = function() {};
/** @type {?} */
RecordBatchReader.from = function() {};
/** @type {?} */
RecordBatchReader.readAll = function() {};
/** @type {?} */
RecordBatchReader.prototype.closed;
/** @type {?} */
RecordBatchReader.prototype.schema;
/** @type {?} */
RecordBatchReader.prototype.autoDestroy;
/** @type {?} */
RecordBatchReader.prototype.dictionaries;
/** @type {?} */
RecordBatchReader.prototype.numDictionaries;
/** @type {?} */
RecordBatchReader.prototype.numRecordBatches;
/** @type {?} */
RecordBatchReader.prototype.footer;
/** @type {?} */
RecordBatchReader.prototype.isSync;
/** @type {?} */
RecordBatchReader.prototype.isAsync;
/** @type {?} */
RecordBatchReader.prototype.isFile;
/** @type {?} */
RecordBatchReader.prototype.isStream;
/** @type {?} */
RecordBatchReader.prototype.next;
/** @type {?} */
RecordBatchReader.prototype.throw;
/** @type {?} */
RecordBatchReader.prototype.return;
/** @type {?} */
RecordBatchReader.prototype.cancel;
/** @type {?} */
RecordBatchReader.prototype.reset;
/** @type {?} */
RecordBatchReader.prototype.open;
/** @type {?} */
RecordBatchReader.prototype.readRecordBatch;
/** @type {?} */
RecordBatchReader.prototype.toDOMStream;
/** @type {?} */
RecordBatchReader.prototype.toNodeStream;
var RecordBatchStreamReader = function() {};
/** @type {?} */
RecordBatchStreamReader.throughDOM = function() {};
/** @type {?} */
RecordBatchStreamReader.prototype.readAll;
var RecordBatchStreamWriter = function() {};
/** @type {?} */
RecordBatchStreamWriter.writeAll = function() {};
/** @type {?} */
RecordBatchStreamWriter.throughDOM = function() {};
var RecordBatchWriter = function() {};
/** @type {?} */
RecordBatchWriter.throughNode = function() {};
/** @type {?} */
RecordBatchWriter.throughDOM = function() {};
/** @type {?} */
RecordBatchWriter.prototype.toUint8Array;
/** @type {?} */
RecordBatchWriter.prototype.writeAll;
/** @type {?} */
RecordBatchWriter.prototype.closed;
/** @type {?} */
RecordBatchWriter.prototype.toDOMStream;
/** @type {?} */
RecordBatchWriter.prototype.toNodeStream;
/** @type {?} */
RecordBatchWriter.prototype.close;
/** @type {?} */
RecordBatchWriter.prototype.abort;
/** @type {?} */
RecordBatchWriter.prototype.finish;
/** @type {?} */
RecordBatchWriter.prototype.reset;
/** @type {?} */
RecordBatchWriter.prototype.write;
var Schema = function() {};
/** @type {?} */
Schema.encode = function() {};
/** @type {?} */
Schema.decode = function() {};
/** @type {?} */
Schema.fromJSON = function() {};
/** @type {?} */
Schema.prototype.names;
/** @type {?} */
Schema.prototype.select;
/** @type {?} */
Schema.prototype.selectAt;
/** @type {?} */
Schema.prototype.assign;
/** @type {?} */
Schema.prototype.fields;
/** @type {?} */
Schema.prototype.metadata;
/** @type {?} */
Schema.prototype.dictionaries;
var SparseUnion = function() {};
var SparseUnionBuilder = function() {};
var Struct = function() {};
/** @type {?} */
Struct.prototype.children;
var StructBuilder = function() {};
/** @type {?} */
StructBuilder.prototype.setValue;
/** @type {?} */
StructBuilder.prototype.setValid;
/** @type {?} */
StructBuilder.prototype.addChild;
var StructRow = function() {};
/** @type {?} */
StructRow.prototype.toArray;
/** @type {?} */
StructRow.prototype.toJSON;
var Table = function() {};
/** @type {?} */
Table.prototype.data;
/** @type {?} */
Table.prototype.numCols;
/** @type {?} */
Table.prototype.numRows;
/** @type {?} */
Table.prototype.nullCount;
/** @type {?} */
Table.prototype.isValid;
/** @type {?} */
Table.prototype.get;
/** @type {?} */
Table.prototype.at;
/** @type {?} */
Table.prototype.set;
/** @type {?} */
Table.prototype.indexOf;
/** @type {?} */
Table.prototype.toArray;
/** @type {?} */
Table.prototype.concat;
/** @type {?} */
Table.prototype.slice;
/** @type {?} */
Table.prototype.getChild;
/** @type {?} */
Table.prototype.getChildAt;
/** @type {?} */
Table.prototype.setChild;
/** @type {?} */
Table.prototype.setChildAt;
/** @type {?} */
Table.prototype.select;
/** @type {?} */
Table.prototype.selectAt;
/** @type {?} */
Table.prototype.assign;
/** @type {?} */
Table.prototype.schema;
/** @type {?} */
Table.prototype.batches;
var Time = function() {};
/** @type {?} */
Time.prototype.ArrayType;
/** @type {?} */
Time.prototype.unit;
/** @type {?} */
Time.prototype.bitWidth;
var TimeBuilder = function() {};
var TimeMicrosecond = function() {};
var TimeMicrosecondBuilder = function() {};
var TimeMillisecond = function() {};
var TimeMillisecondBuilder = function() {};
var TimeNanosecond = function() {};
var TimeNanosecondBuilder = function() {};
var TimeSecond = function() {};
var TimeSecondBuilder = function() {};
var TimeUnit = function() {};
/** @type {?} */
TimeUnit[0] = function() {};
/** @type {?} */
TimeUnit[1] = function() {};
/** @type {?} */
TimeUnit[2] = function() {};
/** @type {?} */
TimeUnit[3] = function() {};
/** @type {?} */
TimeUnit.SECOND = function() {};
/** @type {?} */
TimeUnit.MILLISECOND = function() {};
/** @type {?} */
TimeUnit.MICROSECOND = function() {};
/** @type {?} */
TimeUnit.NANOSECOND = function() {};
var Timestamp = function() {};
/** @type {?} */
Timestamp.prototype.unit;
/** @type {?} */
Timestamp.prototype.timezone;
/** @type {?} */
Timestamp.prototype.ArrayType;
var TimestampBuilder = function() {};
var TimestampMicrosecond = function() {};
var TimestampMicrosecondBuilder = function() {};
var TimestampMillisecond = function() {};
var TimestampMillisecondBuilder = function() {};
var TimestampNanosecond = function() {};
var TimestampNanosecondBuilder = function() {};
var TimestampSecond = function() {};
var TimestampSecondBuilder = function() {};
var Type = function() {};
/** @type {?} */
Type[0] = function() {};
/** @type {?} */
Type[1] = function() {};
/** @type {?} */
Type[2] = function() {};
/** @type {?} */
Type[3] = function() {};
/** @type {?} */
Type[4] = function() {};
/** @type {?} */
Type[5] = function() {};
/** @type {?} */
Type[6] = function() {};
/** @type {?} */
Type[7] = function() {};
/** @type {?} */
Type[8] = function() {};
/** @type {?} */
Type[9] = function() {};
/** @type {?} */
Type[10] = function() {};
/** @type {?} */
Type[11] = function() {};
/** @type {?} */
Type[12] = function() {};
/** @type {?} */
Type[13] = function() {};
/** @type {?} */
Type[14] = function() {};
/** @type {?} */
Type[15] = function() {};
/** @type {?} */
Type[16] = function() {};
/** @type {?} */
Type[17] = function() {};
/** @type {?} */
Type[18] = function() {};
/** @type {?} */
Type[19] = function() {};
/** @type {?} */
Type[20] = function() {};
/** @type {?} */
Type.NONE = function() {};
/** @type {?} */
Type.Null = function() {};
/** @type {?} */
Type.Int = function() {};
/** @type {?} */
Type.Float = function() {};
/** @type {?} */
Type.Binary = function() {};
/** @type {?} */
Type.Utf8 = function() {};
/** @type {?} */
Type.Bool = function() {};
/** @type {?} */
Type.Decimal = function() {};
/** @type {?} */
Type.Date = function() {};
/** @type {?} */
Type.Time = function() {};
/** @type {?} */
Type.Timestamp = function() {};
/** @type {?} */
Type.Interval = function() {};
/** @type {?} */
Type.List = function() {};
/** @type {?} */
Type.Struct = function() {};
/** @type {?} */
Type.Union = function() {};
/** @type {?} */
Type.FixedSizeBinary = function() {};
/** @type {?} */
Type.FixedSizeList = function() {};
/** @type {?} */
Type.Map = function() {};
/** @type {?} */
Type.Duration = function() {};
/** @type {?} */
Type.LargeBinary = function() {};
/** @type {?} */
Type.LargeUtf8 = function() {};
/** @type {?} */
Type.Dictionary = function() {};
/** @type {?} */
Type[-1] = function() {};
/** @type {?} */
Type.Int8 = function() {};
/** @type {?} */
Type[-2] = function() {};
/** @type {?} */
Type.Int16 = function() {};
/** @type {?} */
Type[-3] = function() {};
/** @type {?} */
Type.Int32 = function() {};
/** @type {?} */
Type[-4] = function() {};
/** @type {?} */
Type.Int64 = function() {};
/** @type {?} */
Type[-5] = function() {};
/** @type {?} */
Type.Uint8 = function() {};
/** @type {?} */
Type[-6] = function() {};
/** @type {?} */
Type.Uint16 = function() {};
/** @type {?} */
Type[-7] = function() {};
/** @type {?} */
Type.Uint32 = function() {};
/** @type {?} */
Type[-8] = function() {};
/** @type {?} */
Type.Uint64 = function() {};
/** @type {?} */
Type[-9] = function() {};
/** @type {?} */
Type.Float16 = function() {};
/** @type {?} */
Type[-10] = function() {};
/** @type {?} */
Type.Float32 = function() {};
/** @type {?} */
Type[-11] = function() {};
/** @type {?} */
Type.Float64 = function() {};
/** @type {?} */
Type[-12] = function() {};
/** @type {?} */
Type.DateDay = function() {};
/** @type {?} */
Type[-13] = function() {};
/** @type {?} */
Type.DateMillisecond = function() {};
/** @type {?} */
Type[-14] = function() {};
/** @type {?} */
Type.TimestampSecond = function() {};
/** @type {?} */
Type[-15] = function() {};
/** @type {?} */
Type.TimestampMillisecond = function() {};
/** @type {?} */
Type[-16] = function() {};
/** @type {?} */
Type.TimestampMicrosecond = function() {};
/** @type {?} */
Type[-17] = function() {};
/** @type {?} */
Type.TimestampNanosecond = function() {};
/** @type {?} */
Type[-18] = function() {};
/** @type {?} */
Type.TimeSecond = function() {};
/** @type {?} */
Type[-19] = function() {};
/** @type {?} */
Type.TimeMillisecond = function() {};
/** @type {?} */
Type[-20] = function() {};
/** @type {?} */
Type.TimeMicrosecond = function() {};
/** @type {?} */
Type[-21] = function() {};
/** @type {?} */
Type.TimeNanosecond = function() {};
/** @type {?} */
Type[-22] = function() {};
/** @type {?} */
Type.DenseUnion = function() {};
/** @type {?} */
Type[-23] = function() {};
/** @type {?} */
Type.SparseUnion = function() {};
/** @type {?} */
Type[-24] = function() {};
/** @type {?} */
Type.IntervalDayTime = function() {};
/** @type {?} */
Type[-25] = function() {};
/** @type {?} */
Type.IntervalYearMonth = function() {};
/** @type {?} */
Type[-26] = function() {};
/** @type {?} */
Type.DurationSecond = function() {};
/** @type {?} */
Type[-27] = function() {};
/** @type {?} */
Type.DurationMillisecond = function() {};
/** @type {?} */
Type[-28] = function() {};
/** @type {?} */
Type.DurationMicrosecond = function() {};
/** @type {?} */
Type[-29] = function() {};
/** @type {?} */
Type.DurationNanosecond = function() {};
/** @type {?} */
Type[-30] = function() {};
var Uint16 = function() {};
/** @type {?} */
Uint16.prototype.ArrayType;
var Uint16Builder = function() {};
var Uint32 = function() {};
/** @type {?} */
Uint32.prototype.ArrayType;
var Uint32Builder = function() {};
var Uint64 = function() {};
/** @type {?} */
Uint64.prototype.ArrayType;
var Uint64Builder = function() {};
var Uint8 = function() {};
/** @type {?} */
Uint8.prototype.ArrayType;
var Uint8Builder = function() {};
var Union = function() {};
/** @type {?} */
Union.prototype.mode;
/** @type {?} */
Union.prototype.typeIds;
/** @type {?} */
Union.prototype.children;
/** @type {?} */
Union.prototype.typeIdToChildIndex;
/** @type {?} */
Union.prototype.ArrayType;
var UnionBuilder = function() {};
/** @type {?} */
UnionBuilder.prototype.typeIdToChildIndex;
/** @type {?} */
UnionBuilder.prototype.append;
/** @type {?} */
UnionBuilder.prototype.set;
/** @type {?} */
UnionBuilder.prototype.setValue;
/** @type {?} */
UnionBuilder.prototype.addChild;
var UnionMode = function() {};
/** @type {?} */
UnionMode[0] = function() {};
/** @type {?} */
UnionMode[1] = function() {};
/** @type {?} */
UnionMode.Sparse = function() {};
/** @type {?} */
UnionMode.Dense = function() {};
var Utf8 = function() {};
/** @type {?} */
Utf8.prototype.ArrayType;
var Utf8Builder = function() {};
/** @type {?} */
Utf8Builder.prototype.byteLength;
/** @type {?} */
Utf8Builder.prototype.setValue;
var Vector = function() {};
/** @type {?} */
Vector.prototype.byteLength;
/** @type {?} */
Vector.prototype.nullable;
/** @type {?} */
Vector.prototype.nullCount;
/** @type {?} */
Vector.prototype.ArrayType;
/** @type {?} */
Vector.prototype.VectorName;
/** @type {?} */
Vector.prototype.isValid;
/** @type {?} */
Vector.prototype.get;
/** @type {?} */
Vector.prototype.at;
/** @type {?} */
Vector.prototype.set;
/** @type {?} */
Vector.prototype.indexOf;
/** @type {?} */
Vector.prototype.includes;
/** @type {?} */
Vector.prototype.concat;
/** @type {?} */
Vector.prototype.slice;
/** @type {?} */
Vector.prototype.toJSON;
/** @type {?} */
Vector.prototype.toArray;
/** @type {?} */
Vector.prototype.getChild;
/** @type {?} */
Vector.prototype.getChildAt;
/** @type {?} */
Vector.prototype.isMemoized;
/** @type {?} */
Vector.prototype.memoize;
/** @type {?} */
Vector.prototype.unmemoize;
/** @type {?} */
Vector.prototype.type;
/** @type {?} */
Vector.prototype.data;
/** @type {?} */
Vector.prototype.stride;
/** @type {?} */
Vector.prototype.numChildren;
var Visitor = function() {};
/** @type {?} */
Visitor.prototype.visitMany;
/** @type {?} */
Visitor.prototype.visit;
/** @type {?} */
Visitor.prototype.getVisitFn;
/** @type {?} */
Visitor.prototype.getVisitFnByTypeId;
/** @type {?} */
Visitor.prototype.visitNull;
/** @type {?} */
Visitor.prototype.visitBool;
/** @type {?} */
Visitor.prototype.visitInt;
/** @type {?} */
Visitor.prototype.visitFloat;
/** @type {?} */
Visitor.prototype.visitUtf8;
/** @type {?} */
Visitor.prototype.visitLargeUtf8;
/** @type {?} */
Visitor.prototype.visitBinary;
/** @type {?} */
Visitor.prototype.visitLargeBinary;
/** @type {?} */
Visitor.prototype.visitFixedSizeBinary;
/** @type {?} */
Visitor.prototype.visitDate;
/** @type {?} */
Visitor.prototype.visitTimestamp;
/** @type {?} */
Visitor.prototype.visitTime;
/** @type {?} */
Visitor.prototype.visitDecimal;
/** @type {?} */
Visitor.prototype.visitList;
/** @type {?} */
Visitor.prototype.visitStruct;
/** @type {?} */
Visitor.prototype.visitUnion;
/** @type {?} */
Visitor.prototype.visitDictionary;
/** @type {?} */
Visitor.prototype.visitInterval;
/** @type {?} */
Visitor.prototype.visitDuration;
/** @type {?} */
Visitor.prototype.visitFixedSizeList;
/** @type {?} */
Visitor.prototype.visitMap;
/** @type {?} */
Visitor.prototype.visitInt8;
/** @type {?} */
Visitor.prototype.visitInt16;
/** @type {?} */
Visitor.prototype.visitInt32;
/** @type {?} */
Visitor.prototype.visitInt64;
/** @type {?} */
Visitor.prototype.visitUint8;
/** @type {?} */
Visitor.prototype.visitUint16;
/** @type {?} */
Visitor.prototype.visitUint32;
/** @type {?} */
Visitor.prototype.visitUint64;
/** @type {?} */
Visitor.prototype.visitFloat16;
/** @type {?} */
Visitor.prototype.visitFloat32;
/** @type {?} */
Visitor.prototype.visitFloat64;
/** @type {?} */
Visitor.prototype.visitDateDay;
/** @type {?} */
Visitor.prototype.visitDateMillisecond;
/** @type {?} */
Visitor.prototype.visitTimestampSecond;
/** @type {?} */
Visitor.prototype.visitTimestampMillisecond;
/** @type {?} */
Visitor.prototype.visitTimestampMicrosecond;
/** @type {?} */
Visitor.prototype.visitTimestampNanosecond;
/** @type {?} */
Visitor.prototype.visitTimeSecond;
/** @type {?} */
Visitor.prototype.visitTimeMillisecond;
/** @type {?} */
Visitor.prototype.visitTimeMicrosecond;
/** @type {?} */
Visitor.prototype.visitTimeNanosecond;
/** @type {?} */
Visitor.prototype.visitDenseUnion;
/** @type {?} */
Visitor.prototype.visitSparseUnion;
/** @type {?} */
Visitor.prototype.visitIntervalDayTime;
/** @type {?} */
Visitor.prototype.visitIntervalYearMonth;
/** @type {?} */
Visitor.prototype.visitDurationSecond;
/** @type {?} */
Visitor.prototype.visitDurationMillisecond;
/** @type {?} */
Visitor.prototype.visitDurationMicrosecond;
/** @type {?} */
Visitor.prototype.visitDurationNanosecond;
var builderThroughAsyncIterable = function() {};
var builderThroughIterable = function() {};
var makeBuilder = function() {};
var makeData = function() {};
var makeTable = function() {};
var makeVector = function() {};
var tableFromArrays = function() {};
var tableFromIPC = function() {};
var tableFromJSON = function() {};
var tableToIPC = function() {};
var util = function() {};
/** @type {?} */
util.BN = function() {};
/** @type {?} */
util.bigNumToBigInt = function() {};
/** @type {?} */
util.bigNumToNumber = function() {};
/** @type {?} */
util.bigNumToString = function() {};
/** @type {?} */
util.isArrowBigNumSymbol = function() {};
/** @type {?} */
util.BaseInt64 = function() {};
/** @type {?} */
util.Int128 = function() {};
/** @type {?} */
util.Int64 = function() {};
/** @type {?} */
util.Uint64 = function() {};
/** @type {?} */
util.BitIterator = function() {};
/** @type {?} */
util.getBit = function() {};
/** @type {?} */
util.getBool = function() {};
/** @type {?} */
util.packBools = function() {};
/** @type {?} */
util.popcnt_array = function() {};
/** @type {?} */
util.popcnt_bit_range = function() {};
/** @type {?} */
util.popcnt_uint32 = function() {};
/** @type {?} */
util.setBool = function() {};
/** @type {?} */
util.truncateBitmap = function() {};
/** @type {?} */
util.float64ToUint16 = function() {};
/** @type {?} */
util.uint16ToFloat64 = function() {};
/** @type {?} */
util.compareArrayLike = function() {};
/** @type {?} */
util.joinUint8Arrays = function() {};
/** @type {?} */
util.memcpy = function() {};
/** @type {?} */
util.rebaseValueOffsets = function() {};
/** @type {?} */
util.toArrayBufferView = function() {};
/** @type {?} */
util.toArrayBufferViewAsyncIterator = function() {};
/** @type {?} */
util.toArrayBufferViewIterator = function() {};
/** @type {?} */
util.toBigInt64Array = function() {};
/** @type {?} */
util.toBigUint64Array = function() {};
/** @type {?} */
util.toFloat32Array = function() {};
/** @type {?} */
util.toFloat32ArrayAsyncIterator = function() {};
/** @type {?} */
util.toFloat32ArrayIterator = function() {};
/** @type {?} */
util.toFloat64Array = function() {};
/** @type {?} */
util.toFloat64ArrayAsyncIterator = function() {};
/** @type {?} */
util.toFloat64ArrayIterator = function() {};
/** @type {?} */
util.toInt16Array = function() {};
/** @type {?} */
util.toInt16ArrayAsyncIterator = function() {};
/** @type {?} */
util.toInt16ArrayIterator = function() {};
/** @type {?} */
util.toInt32Array = function() {};
/** @type {?} */
util.toInt32ArrayAsyncIterator = function() {};
/** @type {?} */
util.toInt32ArrayIterator = function() {};
/** @type {?} */
util.toInt8Array = function() {};
/** @type {?} */
util.toInt8ArrayAsyncIterator = function() {};
/** @type {?} */
util.toInt8ArrayIterator = function() {};
/** @type {?} */
util.toUint16Array = function() {};
/** @type {?} */
util.toUint16ArrayAsyncIterator = function() {};
/** @type {?} */
util.toUint16ArrayIterator = function() {};
/** @type {?} */
util.toUint32Array = function() {};
/** @type {?} */
util.toUint32ArrayAsyncIterator = function() {};
/** @type {?} */
util.toUint32ArrayIterator = function() {};
/** @type {?} */
util.toUint8Array = function() {};
/** @type {?} */
util.toUint8ArrayAsyncIterator = function() {};
/** @type {?} */
util.toUint8ArrayIterator = function() {};
/** @type {?} */
util.toUint8ClampedArray = function() {};
/** @type {?} */
util.toUint8ClampedArrayAsyncIterator = function() {};
/** @type {?} */
util.toUint8ClampedArrayIterator = function() {};
/** @type {?} */
util.clampRange = function() {};
/** @type {?} */
util.createElementComparator = function() {};
/** @type {?} */
util.wrapIndex = function() {};
/** @type {?} */
util.valueToString = function() {};
/** @type {?} */
util.compareSchemas = function() {};
/** @type {?} */
util.compareFields = function() {};
/** @type {?} */
util.compareTypes = function() {};
var vectorFromArray = function() {};
var BaseInt64 = function() {};
/** @type {?} */
BaseInt64.prototype.high;
/** @type {?} */
BaseInt64.prototype.low;
/** @type {?} */
BaseInt64.prototype.lessThan;
/** @type {?} */
BaseInt64.prototype.equals;
/** @type {?} */
BaseInt64.prototype.greaterThan;
/** @type {?} */
BaseInt64.prototype.hex;
var Int128 = function() {};
/** @type {?} */
Int128.multiply = function() {};
/** @type {?} */
Int128.add = function() {};
/** @type {?} */
Int128.from = function() {};
/** @type {?} */
Int128.fromNumber = function() {};
/** @type {?} */
Int128.fromString = function() {};
/** @type {?} */
Int128.convertArray = function() {};
/** @type {?} */
Int128.prototype.high;
/** @type {?} */
Int128.prototype.low;
/** @type {?} */
Int128.prototype.negate;
/** @type {?} */
Int128.prototype.times;
/** @type {?} */
Int128.prototype.plus;
/** @type {?} */
Int128.prototype.hex;
var Int64 = function() {};
/** @type {?} */
Int64.from = function() {};
/** @type {?} */
Int64.fromNumber = function() {};
/** @type {?} */
Int64.fromString = function() {};
/** @type {?} */
Int64.convertArray = function() {};
/** @type {?} */
Int64.multiply = function() {};
/** @type {?} */
Int64.add = function() {};
/** @type {?} */
Int64.prototype.negate;
/** @type {?} */
Int64.prototype.times;
/** @type {?} */
Int64.prototype.plus;
/** @type {?} */
Int64.prototype.lessThan;
var Uint64 = function() {};
/** @type {?} */
Uint64.from = function() {};
/** @type {?} */
Uint64.fromNumber = function() {};
/** @type {?} */
Uint64.fromString = function() {};
/** @type {?} */
Uint64.convertArray = function() {};
/** @type {?} */
Uint64.multiply = function() {};
/** @type {?} */
Uint64.add = function() {};
/** @type {?} */
Uint64.prototype.times;
/** @type {?} */
Uint64.prototype.plus;