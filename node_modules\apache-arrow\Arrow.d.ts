export { MessageHeader } from './fb/message-header.js';
export { Type, BufferType, DateUnit, TimeUnit, Precision, UnionMode, IntervalUnit, MetadataVersion, } from './enum.js';
export { Data, makeData } from './data.js';
export type { TypeMap } from './type.js';
export { DataType, Null, Bool, Int, Int8, Int16, Int32, Int64, Uint8, Uint16, Uint32, Uint64, Float, Float16, Float32, Float64, Utf8, LargeUtf8, Binary, LargeBinary, FixedSizeBinary, Date_, DateDay, DateMillisecond, Timestamp, TimestampSecond, TimestampMillisecond, TimestampMicrosecond, TimestampNanosecond, Time, TimeSecond, TimeMillisecond, TimeMicrosecond, TimeNanosecond, Decimal, List, Struct, Union, DenseUnion, SparseUnion, Dictionary, Interval, IntervalDayTime, IntervalYearMonth, Duration, DurationSecond, DurationMillisecond, DurationMicrosecond, DurationNanosecond, FixedSizeList, Map_ } from './type.js';
export { Table, makeTable, tableFromArrays } from './table.js';
export { Vector, makeVector } from './vector.js';
export { Visitor } from './visitor.js';
export { Schema, Field } from './schema.js';
export { MapRow } from './row/map.js';
export { StructRow } from './row/struct.js';
export type { StructRowProxy } from './row/struct.js';
export { Builder } from './builder.js';
export { makeBuilder, vectorFromArray, tableFromJSON, builderThroughIterable, builderThroughAsyncIterable } from './factories.js';
export type { BuilderOptions } from './builder.js';
export { BoolBuilder } from './builder/bool.js';
export { NullBuilder } from './builder/null.js';
export { DateBuilder, DateDayBuilder, DateMillisecondBuilder } from './builder/date.js';
export { DecimalBuilder } from './builder/decimal.js';
export { DictionaryBuilder } from './builder/dictionary.js';
export { FixedSizeBinaryBuilder } from './builder/fixedsizebinary.js';
export { FloatBuilder, Float16Builder, Float32Builder, Float64Builder } from './builder/float.js';
export { IntBuilder, Int8Builder, Int16Builder, Int32Builder, Int64Builder, Uint8Builder, Uint16Builder, Uint32Builder, Uint64Builder } from './builder/int.js';
export { TimeBuilder, TimeSecondBuilder, TimeMillisecondBuilder, TimeMicrosecondBuilder, TimeNanosecondBuilder } from './builder/time.js';
export { TimestampBuilder, TimestampSecondBuilder, TimestampMillisecondBuilder, TimestampMicrosecondBuilder, TimestampNanosecondBuilder } from './builder/timestamp.js';
export { IntervalBuilder, IntervalDayTimeBuilder, IntervalYearMonthBuilder } from './builder/interval.js';
export { DurationBuilder, DurationSecondBuilder, DurationMillisecondBuilder, DurationMicrosecondBuilder, DurationNanosecondBuilder } from './builder/duration.js';
export { Utf8Builder } from './builder/utf8.js';
export { LargeUtf8Builder } from './builder/largeutf8.js';
export { BinaryBuilder } from './builder/binary.js';
export { LargeBinaryBuilder } from './builder/largebinary.js';
export { ListBuilder } from './builder/list.js';
export { FixedSizeListBuilder } from './builder/fixedsizelist.js';
export { MapBuilder } from './builder/map.js';
export { StructBuilder } from './builder/struct.js';
export { UnionBuilder, SparseUnionBuilder, DenseUnionBuilder } from './builder/union.js';
export { ByteStream, AsyncByteStream, AsyncByteQueue } from './io/stream.js';
export type { ReadableSource, WritableSink } from './io/stream.js';
export { RecordBatchReader, RecordBatchFileReader, RecordBatchStreamReader, AsyncRecordBatchFileReader, AsyncRecordBatchStreamReader } from './ipc/reader.js';
export { RecordBatchWriter, RecordBatchFileWriter, RecordBatchStreamWriter, RecordBatchJSONWriter } from './ipc/writer.js';
export { tableToIPC, tableFromIPC } from './ipc/serialization.js';
export { MessageReader, AsyncMessageReader, JSONMessageReader } from './ipc/message.js';
export { Message } from './ipc/metadata/message.js';
export { RecordBatch } from './recordbatch.js';
export type { ArrowJSONLike, FileHandle, Readable, Writable, ReadableWritable, ReadableDOMStreamOptions } from './io/interfaces.js';
import * as util_bn_ from './util/bn.js';
import * as util_int_ from './util/int.js';
import * as util_bit_ from './util/bit.js';
import * as util_buffer_ from './util/buffer.js';
import { compareSchemas, compareFields, compareTypes } from './visitor/typecomparator.js';
/** @ignore */
export declare const util: {
    compareSchemas: typeof compareSchemas;
    compareFields: typeof compareFields;
    compareTypes: typeof compareTypes;
    valueToString(x: any): string;
    clampRange<T extends {
        length: number;
        stride?: number | undefined;
    }>(source: T, begin: number | undefined, end: number | undefined): [number, number];
    clampRange<T_1 extends {
        length: number;
        stride?: number | undefined;
    }, N extends (source: T_1, offset: number, length: number) => any = (source: T_1, offset: number, length: number) => any>(source: T_1, begin: number | undefined, end: number | undefined, then: N): ReturnType<N>;
    createElementComparator(search: any): (value: any) => boolean;
    wrapIndex: (index: number, len: number) => number;
    memcpy<TTarget extends ArrayBufferView, TSource extends ArrayBufferView>(target: TTarget, source: TSource, targetByteOffset?: number, sourceByteLength?: number): TTarget;
    joinUint8Arrays(chunks: Uint8Array[], size?: number | null | undefined): [Uint8Array, Uint8Array[], number];
    toArrayBufferView<T_2 extends import("./interfaces.js").TypedArrayConstructor<any> | import("./interfaces.js").BigIntArrayConstructor<any>>(ArrayBufferViewCtor: any, input: util_buffer_.ArrayBufferViewInput): InstanceType<T_2>;
    toArrayBufferViewIterator<T_3 extends import("./interfaces.js").TypedArray>(ArrayCtor: import("./interfaces.js").TypedArrayConstructor<T_3>, source: util_buffer_.ArrayBufferViewInput | Iterable<util_buffer_.ArrayBufferViewInput>): Generator<T_3, T_3, number | undefined>;
    toArrayBufferViewAsyncIterator<T_4 extends import("./interfaces.js").TypedArray>(ArrayCtor: import("./interfaces.js").TypedArrayConstructor<T_4>, source: util_buffer_.ArrayBufferViewInput | AsyncIterable<util_buffer_.ArrayBufferViewInput> | Iterable<util_buffer_.ArrayBufferViewInput> | PromiseLike<util_buffer_.ArrayBufferViewInput>): AsyncGenerator<T_4, T_4, number | undefined>;
    rebaseValueOffsets(offset: number, length: number, valueOffsets: Int32Array): Int32Array;
    rebaseValueOffsets(offset: number, length: number, valueOffsets: BigInt64Array): BigInt64Array;
    compareArrayLike<T_5 extends ArrayLike<any>>(a: T_5, b: T_5): boolean;
    toInt8Array: (input: util_buffer_.ArrayBufferViewInput) => any;
    toInt16Array: (input: util_buffer_.ArrayBufferViewInput) => any;
    toInt32Array: (input: util_buffer_.ArrayBufferViewInput) => any;
    toBigInt64Array: (input: util_buffer_.ArrayBufferViewInput) => any;
    toUint8Array: (input: util_buffer_.ArrayBufferViewInput) => any;
    toUint16Array: (input: util_buffer_.ArrayBufferViewInput) => any;
    toUint32Array: (input: util_buffer_.ArrayBufferViewInput) => any;
    toBigUint64Array: (input: util_buffer_.ArrayBufferViewInput) => any;
    toFloat32Array: (input: util_buffer_.ArrayBufferViewInput) => any;
    toFloat64Array: (input: util_buffer_.ArrayBufferViewInput) => any;
    toUint8ClampedArray: (input: util_buffer_.ArrayBufferViewInput) => any;
    toInt8ArrayIterator: (input: util_buffer_.ArrayBufferViewInput | Iterable<util_buffer_.ArrayBufferViewInput>) => Generator<Int8Array, Int8Array, number | undefined>;
    toInt16ArrayIterator: (input: util_buffer_.ArrayBufferViewInput | Iterable<util_buffer_.ArrayBufferViewInput>) => Generator<Int16Array, Int16Array, number | undefined>;
    toInt32ArrayIterator: (input: util_buffer_.ArrayBufferViewInput | Iterable<util_buffer_.ArrayBufferViewInput>) => Generator<Int32Array, Int32Array, number | undefined>;
    toUint8ArrayIterator: (input: util_buffer_.ArrayBufferViewInput | Iterable<util_buffer_.ArrayBufferViewInput>) => Generator<Uint8Array, Uint8Array, number | undefined>;
    toUint16ArrayIterator: (input: util_buffer_.ArrayBufferViewInput | Iterable<util_buffer_.ArrayBufferViewInput>) => Generator<Uint16Array, Uint16Array, number | undefined>;
    toUint32ArrayIterator: (input: util_buffer_.ArrayBufferViewInput | Iterable<util_buffer_.ArrayBufferViewInput>) => Generator<Uint32Array, Uint32Array, number | undefined>;
    toFloat32ArrayIterator: (input: util_buffer_.ArrayBufferViewInput | Iterable<util_buffer_.ArrayBufferViewInput>) => Generator<Float32Array, Float32Array, number | undefined>;
    toFloat64ArrayIterator: (input: util_buffer_.ArrayBufferViewInput | Iterable<util_buffer_.ArrayBufferViewInput>) => Generator<Float64Array, Float64Array, number | undefined>;
    toUint8ClampedArrayIterator: (input: util_buffer_.ArrayBufferViewInput | Iterable<util_buffer_.ArrayBufferViewInput>) => Generator<Uint8ClampedArray, Uint8ClampedArray, number | undefined>;
    toInt8ArrayAsyncIterator: (input: util_buffer_.ArrayBufferViewInput | AsyncIterable<util_buffer_.ArrayBufferViewInput> | Iterable<util_buffer_.ArrayBufferViewInput> | PromiseLike<util_buffer_.ArrayBufferViewInput>) => AsyncGenerator<Int8Array, Int8Array, number | undefined>;
    toInt16ArrayAsyncIterator: (input: util_buffer_.ArrayBufferViewInput | AsyncIterable<util_buffer_.ArrayBufferViewInput> | Iterable<util_buffer_.ArrayBufferViewInput> | PromiseLike<util_buffer_.ArrayBufferViewInput>) => AsyncGenerator<Int16Array, Int16Array, number | undefined>;
    toInt32ArrayAsyncIterator: (input: util_buffer_.ArrayBufferViewInput | AsyncIterable<util_buffer_.ArrayBufferViewInput> | Iterable<util_buffer_.ArrayBufferViewInput> | PromiseLike<util_buffer_.ArrayBufferViewInput>) => AsyncGenerator<Int32Array, Int32Array, number | undefined>;
    toUint8ArrayAsyncIterator: (input: util_buffer_.ArrayBufferViewInput | AsyncIterable<util_buffer_.ArrayBufferViewInput> | Iterable<util_buffer_.ArrayBufferViewInput> | PromiseLike<util_buffer_.ArrayBufferViewInput>) => AsyncGenerator<Uint8Array, Uint8Array, number | undefined>;
    toUint16ArrayAsyncIterator: (input: util_buffer_.ArrayBufferViewInput | AsyncIterable<util_buffer_.ArrayBufferViewInput> | Iterable<util_buffer_.ArrayBufferViewInput> | PromiseLike<util_buffer_.ArrayBufferViewInput>) => AsyncGenerator<Uint16Array, Uint16Array, number | undefined>;
    toUint32ArrayAsyncIterator: (input: util_buffer_.ArrayBufferViewInput | AsyncIterable<util_buffer_.ArrayBufferViewInput> | Iterable<util_buffer_.ArrayBufferViewInput> | PromiseLike<util_buffer_.ArrayBufferViewInput>) => AsyncGenerator<Uint32Array, Uint32Array, number | undefined>;
    toFloat32ArrayAsyncIterator: (input: util_buffer_.ArrayBufferViewInput | AsyncIterable<util_buffer_.ArrayBufferViewInput> | Iterable<util_buffer_.ArrayBufferViewInput> | PromiseLike<util_buffer_.ArrayBufferViewInput>) => AsyncGenerator<Float32Array, Float32Array, number | undefined>;
    toFloat64ArrayAsyncIterator: (input: util_buffer_.ArrayBufferViewInput | AsyncIterable<util_buffer_.ArrayBufferViewInput> | Iterable<util_buffer_.ArrayBufferViewInput> | PromiseLike<util_buffer_.ArrayBufferViewInput>) => AsyncGenerator<Float64Array, Float64Array, number | undefined>;
    toUint8ClampedArrayAsyncIterator: (input: util_buffer_.ArrayBufferViewInput | AsyncIterable<util_buffer_.ArrayBufferViewInput> | Iterable<util_buffer_.ArrayBufferViewInput> | PromiseLike<util_buffer_.ArrayBufferViewInput>) => AsyncGenerator<Uint8ClampedArray, Uint8ClampedArray, number | undefined>;
    uint16ToFloat64(h: number): number;
    float64ToUint16(d: number): number;
    getBool(_data: any, _index: number, byte: number, bit: number): boolean;
    getBit(_data: any, _index: number, byte: number, bit: number): 0 | 1;
    setBool(bytes: Uint8Array, index: number, value: any): boolean;
    truncateBitmap(offset: number, length: number, bitmap: Uint8Array): Uint8Array;
    packBools(values: Iterable<any>): Uint8Array;
    popcnt_bit_range(data: Uint8Array, lhs: number, rhs: number): number;
    popcnt_array(arr: ArrayBufferView, byteOffset?: number | undefined, byteLength?: number | undefined): number;
    popcnt_uint32(uint32: number): number;
    BitIterator: typeof util_bit_.BitIterator;
    BaseInt64: typeof util_int_.BaseInt64;
    Uint64: typeof util_int_.Uint64;
    Int64: typeof util_int_.Int64;
    Int128: typeof util_int_.Int128;
    bigNumToNumber<T_6 extends util_bn_.BN<(Int8Array | Int16Array | Int32Array) | (Uint8Array | Uint8ClampedArray | Uint16Array | Uint32Array)>>(bn: T_6, scale?: number | undefined): number;
    bigNumToString<T_7 extends util_bn_.BN<(Int8Array | Int16Array | Int32Array) | (Uint8Array | Uint8ClampedArray | Uint16Array | Uint32Array)>>(a: T_7): string;
    bigNumToBigInt<T_8 extends util_bn_.BN<(Int8Array | Int16Array | Int32Array) | (Uint8Array | Uint8ClampedArray | Uint16Array | Uint32Array)>>(a: T_8): bigint;
    isArrowBigNumSymbol: typeof util_bn_.isArrowBigNumSymbol;
    BN: typeof util_bn_.BN;
};
