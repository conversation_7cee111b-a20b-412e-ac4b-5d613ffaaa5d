{"version": 3, "sources": ["util/recordbatch.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;AAGrB,OAAO,EAAQ,QAAQ,EAAE,MAAM,YAAY,CAAC;AAC5C,OAAO,EAAE,MAAM,EAAW,MAAM,YAAY,CAAC;AAE7C,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAEhD,cAAc;AACd,MAAM,UAAU,kCAAkC,CAA0B,MAAiB,EAAE,IAA0B;IACrH,OAAO,4CAA4C,CAAI,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACrG,CAAC;AAED,cAAc;AACd,SAAS,4CAA4C,CAA0B,MAAiB,EAAE,IAA0B;IAExH,MAAM,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;IAClC,MAAM,OAAO,GAAG,EAAuB,CAAC;IACxC,MAAM,IAAI,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAE7E,IAAI,UAAU,GAAG,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC;IACpC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACX,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;IAC/B,IAAI,KAAuB,EAAE,QAAQ,GAAuB,EAAE,CAAC;IAE/D,OAAO,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,CAAC;QAE3B,KAAK,WAAW,GAAG,MAAM,CAAC,iBAAiB,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,UAAU,GAAG,CAAC;YACrE,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAG,CAAC;YACvC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAC/B,QAAQ,GAAG,kBAAkB,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACzE,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;gBAClB,OAAO,CAAC,UAAU,EAAE,CAAC,GAAG,QAAQ,CAAC;oBAC7B,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC;oBACxB,MAAM,EAAE,WAAW;oBACnB,SAAS,EAAE,CAAC;oBACZ,QAAQ,EAAE,QAAQ,CAAC,KAAK,EAAE;iBAC7B,CAAC,CAAC;YACP,CAAC;QACL,CAAC;IACL,CAAC;IAED,OAAO;QACH,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;KACvD,CAAC;AACN,CAAC;AAED,cAAc;AACd,SAAS,kBAAkB,CACvB,MAA2B,EAC3B,WAAmB,EACnB,QAA4B,EAC5B,OAA6B,EAC7B,IAA4B;;IAE5B,MAAM,cAAc,GAAG,CAAC,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IACvD,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;QAC5C,MAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,MAAM,GAAG,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,CAAC;QAC7B,IAAI,MAAM,IAAI,WAAW,EAAE,CAAC;YACxB,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;gBACzB,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACJ,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;gBAC1C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAC1D,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,MAAM,GAAG,WAAW,CAAC,CACjD,CAAC,CAAC;YACP,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAC5C,QAAQ,CAAC,CAAC,CAAC,GAAG,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,kCAAkC,CAAC,WAAW,CAAC,mCAAI,QAAQ,CAAC;gBAC7E,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,MAAM,EAAE,WAAW;gBACnB,SAAS,EAAE,WAAW;gBACtB,UAAU,EAAE,IAAI,UAAU,CAAC,cAAc,CAAC;aAC7C,CAAqB,CAAC;QAC3B,CAAC;IACL,CAAC;IACD,OAAO,QAAQ,CAAC;AACpB,CAAC", "file": "recordbatch.mjs", "sourceRoot": "../src"}