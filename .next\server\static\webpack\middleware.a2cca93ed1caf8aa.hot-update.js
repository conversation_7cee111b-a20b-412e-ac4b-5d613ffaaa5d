"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n// Define protected routes\nconst protectedRoutes = [\n    \"/dashboard\",\n    \"/osint\",\n    \"/scanner\",\n    \"/file-analyzer\",\n    \"/cve\",\n    \"/dorking\",\n    \"/playground\",\n    \"/tools\",\n    \"/bot\",\n    \"/leaderboard\",\n    \"/profile\",\n    \"/plan\",\n    \"/admin\"\n];\n// Define admin-only routes\nconst adminRoutes = [\n    \"/admin\"\n];\n// Define public routes that should redirect to dashboard if authenticated\nconst publicRoutes = [\n    \"/login\",\n    \"/register\"\n];\nasync function middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Skip middleware for API routes, static files, and other assets\n    if (pathname.startsWith(\"/api/\") || pathname.startsWith(\"/_next/\") || pathname.startsWith(\"/favicon.ico\") || pathname.includes(\".\")) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Get authentication status - simplified for Edge Runtime\n    const isAuthenticated = await checkAuthentication(request);\n    const user = isAuthenticated ? {\n        username: \"user\",\n        role: \"user\"\n    } : null;\n    console.log(`🔐 Middleware: ${pathname} - Auth: ${isAuthenticated} - User: ${user?.username || \"none\"}`);\n    // Handle protected routes\n    if (protectedRoutes.some((route)=>pathname.startsWith(route))) {\n        if (!isAuthenticated) {\n            console.log(`❌ Redirecting unauthenticated user from ${pathname} to /login`);\n            const loginUrl = new URL(\"/login\", request.url);\n            loginUrl.searchParams.set(\"redirect\", pathname);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(loginUrl);\n        }\n        // Check admin routes\n        if (adminRoutes.some((route)=>pathname.startsWith(route))) {\n            if (user?.role !== \"admin\" && user?.role !== \"super_admin\") {\n                console.log(`❌ Unauthorized access attempt to ${pathname} by ${user?.username}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n            }\n        }\n        // User is authenticated and authorized, continue\n        console.log(`✅ Authorized access to ${pathname} by ${user?.username}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Handle public routes (login, register)\n    if (publicRoutes.some((route)=>pathname === route)) {\n        if (isAuthenticated) {\n            console.log(`🔄 Redirecting authenticated user from ${pathname} to /dashboard`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n        }\n        // User is not authenticated, allow access to public routes\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Handle root route\n    if (pathname === \"/\") {\n        if (isAuthenticated) {\n            console.log(`🔄 Redirecting authenticated user from / to /dashboard`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n        } else {\n            console.log(`🔄 Redirecting unauthenticated user from / to /login`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/login\", request.url));\n        }\n    }\n    // For all other routes, continue normally\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder files\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|.*\\\\..*$).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});