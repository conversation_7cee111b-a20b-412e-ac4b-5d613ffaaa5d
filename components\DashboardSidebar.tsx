'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { 
  Home,
  Search,
  Shield,
  FileText,
  Database,
  Globe,
  Zap,
  Trophy,
  Users,
  Settings,
  CreditCard,
  Bot,
  Monitor,
  BarChart3,
  MessageSquare,
  Phone,
  Crown,
  Lock,
  Wifi,
  Server,
  Activity,
  AlertTriangle,
  DollarSign,
  UserCheck,
  Cog,
  Eye,
  Target,
  Bug,
  Code,
  Terminal,
  Layers,
  Network,
  HardDrive,
  Cpu,
  MemoryStick,
  Gauge,
  Bell,
  Mail,
  Calendar,
  FileSearch,
  Scan,
  Radar,
  Fingerprint,
  Key,
  ShieldCheck,
  UserCog,
  Building,
  Briefcase,
  TrendingUp,
  PieChart,
  LineChart,
  BarChart,
  Smartphone,
  Headphones,
  Webhook,
  GitBranch,
  Package,
  Layers3,
  CloudCog,
  Database as DatabaseIcon,
  HelpCircle,
  BookOpen,
  GraduationCap,
  Award,
  Star,
  Flame,
  Gamepad2,
  Map,
  Compass,
  Navigation,
  Route,
  MapPin,
  Locate,
  Crosshair,
  Focus,
  Scope,
  Binoculars,
  Telescope,
  Microscope,
  ScanLine,
  ScanText,
  ScanFace,
  ScanEye,
  SearchCheck,
  SearchCode,
  SearchX,
  Sparkles,
  Rocket,
  Lightning,
  Bolt,
  Power,
  Battery,
  Plug,
  Cable,
  Link,
  Unlink,
  Chain,
  Anchor,
  Magnet,
  Atom,
  Orbit,
  Planet,
  Satellite,
  Radio,
  Signal,
  Antenna,
  Tower,
  Broadcast,
  Cast,
  Share,
  Send,
  Upload,
  Download,
  Import,
  Export,
  Archive,
  Package2,
  Box,
  Container,
  Folder,
  FolderOpen,
  File,
  Files,
  FilePlus,
  FileMinus,
  FileEdit,
  FileCheck,
  FileX,
  FileWarning,
  FileQuestion,
  FileImage,
  FileVideo,
  FileAudio,
  FileCode,
  FileSpreadsheet,
  FileText as FileTextIcon,
  Paperclip,
  Bookmark,
  Tag,
  Tags,
  Hash,
  AtSign,
  Percent,
  Plus,
  Minus,
  X,
  Check,
  CheckCircle,
  XCircle,
  AlertCircle,
  Info,
  HelpCircle as Help,
  Question,
  Exclamation,
  ExclamationTriangle,
  Warning,
  Error,
  Success,
  Pending,
  Loading,
  Refresh,
  RotateCcw,
  RotateCw,
  Repeat,
  Shuffle,
  SkipBack,
  SkipForward,
  Rewind,
  FastForward,
  Play,
  Pause,
  Stop,
  Record,
  Square,
  Circle,
  Triangle,
  Diamond,
  Heart,
  ThumbsUp,
  ThumbsDown,
  Like,
  Dislike,
  Love,
  Hate,
  Smile,
  Frown,
  Meh,
  Angry,
  Surprised,
  Confused,
  Sleepy,
  Tired,
  Sick,
  Dead,
  Ghost,
  Skull,
  Crossbones,
  Poison,
  Radioactive,
  Biohazard,
  Warning as WarningIcon,
  Danger,
  Caution,
  Notice,
  Important,
  Urgent,
  Priority,
  High,
  Medium,
  Low,
  Critical,
  Major,
  Minor,
  Trivial,
  Blocker,
  Enhancement,
  Feature,
  Improvement,
  Optimization,
  Performance,
  Security,
  Privacy,
  Compliance,
  Audit,
  Review,
  Approval,
  Rejection,
  Acceptance,
  Confirmation,
  Verification,
  Validation,
  Authentication,
  Authorization,
  Permission,
  Access,
  Deny,
  Allow,
  Block,
  Unblock,
  Ban,
  Unban,
  Mute,
  Unmute,
  Hide,
  Show,
  Visible,
  Invisible,
  Public,
  Private,
  Secret,
  Confidential,
  Classified,
  Restricted,
  Limited,
  Unlimited,
  Free,
  Paid,
  Premium,
  Pro,
  Basic,
  Standard,
  Advanced,
  Expert,
  Master,
  Elite,
  VIP,
  Special,
  Exclusive,
  Limited as LimitedIcon,
  Rare,
  Epic,
  Legendary,
  Mythic,
  Divine,
  Godlike,
  Ultimate,
  Supreme,
  Perfect,
  Flawless,
  Excellent,
  Great,
  Good,
  Average,
  Poor,
  Bad,
  Terrible,
  Awful,
  Horrible,
  Disgusting,
  Revolting,
  Appalling,
  Shocking,
  Stunning,
  Amazing,
  Incredible,
  Unbelievable,
  Fantastic,
  Wonderful,
  Marvelous,
  Spectacular,
  Magnificent,
  Brilliant,
  Outstanding,
  Exceptional,
  Extraordinary,
  Remarkable,
  Notable,
  Noteworthy,
  Significant,
  Important as ImportantIcon,
  Relevant,
  Useful,
  Helpful,
  Beneficial,
  Valuable,
  Precious,
  Priceless,
  Worthless,
  Useless,
  Pointless,
  Meaningless,
  Senseless,
  Nonsensical,
  Absurd,
  Ridiculous,
  Silly,
  Funny,
  Hilarious,
  Amusing,
  Entertaining,
  Enjoyable,
  Pleasant,
  Nice,
  Sweet,
  Cute,
  Adorable,
  Lovely,
  Beautiful,
  Pretty,
  Attractive,
  Gorgeous,
  Stunning as StunningIcon,
  Breathtaking,
  Mesmerizing,
  Captivating,
  Enchanting,
  Charming,
  Delightful,
  Wonderful as WonderfulIcon,
  Amazing as AmazingIcon,
  Incredible as IncredibleIcon,
  Unbelievable as UnbelievableIcon,
  Fantastic as FantasticIcon,
  Marvelous as MarvelousIcon,
  Spectacular as SpectacularIcon,
  Magnificent as MagnificentIcon,
  Brilliant as BrilliantIcon,
  Outstanding as OutstandingIcon,
  Exceptional as ExceptionalIcon,
  Extraordinary as ExtraordinaryIcon,
  Remarkable as RemarkableIcon
} from 'lucide-react'

interface User {
  id: string
  username: string
  email: string
  role: 'user' | 'admin' | 'super_admin' | 'moderator'
  plan: 'Free' | 'Student' | 'Hobby' | 'Bughunter' | 'Cybersecurity'
  level: number
  score: number
}

interface SidebarItem {
  id: string
  title: string
  icon: any
  href?: string
  badge?: string
  children?: SidebarItem[]
  roles?: string[]
  plans?: string[]
  premium?: boolean
  comingSoon?: boolean
}

interface DashboardSidebarProps {
  user: User | null
  isCollapsed: boolean
  onToggle: () => void
}

export default function DashboardSidebar({ user, isCollapsed, onToggle }: DashboardSidebarProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  // Define sidebar items based on user role and plan
  const sidebarItems: SidebarItem[] = [
    // Main Dashboard
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: Home,
      href: '/dashboard'
    },

    // Core Tools (Available to all users)
    {
      id: 'tools',
      title: 'Security Tools',
      icon: Shield,
      children: [
        {
          id: 'osint',
          title: 'OSINT Lookup',
          icon: Search,
          href: '/osint',
          badge: 'Popular'
        },
        {
          id: 'scanner',
          title: 'Vulnerability Scanner',
          icon: Scan,
          href: '/scanner',
          premium: true
        },
        {
          id: 'file-analyzer',
          title: 'File Analyzer',
          icon: FileText,
          href: '/file-analyzer',
          premium: true
        },
        {
          id: 'cve',
          title: 'CVE Database',
          icon: Database,
          href: '/cve'
        },
        {
          id: 'dorking',
          title: 'Google Dorking',
          icon: Globe,
          href: '/dorking'
        }
      ]
    },

    // Advanced Tools (Premium plans)
    {
      id: 'advanced',
      title: 'Advanced Tools',
      icon: Zap,
      plans: ['Hobby', 'Bughunter', 'Cybersecurity'],
      children: [
        {
          id: 'playground',
          title: 'API Playground',
          icon: Code,
          href: '/playground'
        },
        {
          id: 'tools-advanced',
          title: 'Advanced Tools',
          icon: Terminal,
          href: '/tools'
        }
      ]
    },

    // Community & Learning
    {
      id: 'community',
      title: 'Community',
      icon: Users,
      children: [
        {
          id: 'leaderboard',
          title: 'Leaderboard',
          icon: Trophy,
          href: '/leaderboard'
        },
        {
          id: 'community-hub',
          title: 'Community Hub',
          icon: MessageSquare,
          href: '/community'
        }
      ]
    },

    // Admin Tools (Admin and Super Admin only)
    ...(user?.role === 'admin' || user?.role === 'super_admin' ? [{
      id: 'admin',
      title: 'Administration',
      icon: UserCog,
      roles: ['admin', 'super_admin'],
      children: [
        {
          id: 'user-management',
          title: 'User Management',
          icon: Users,
          href: '/dashboard?section=users',
          roles: ['admin', 'super_admin']
        },
        {
          id: 'analytics',
          title: 'Analytics',
          icon: BarChart3,
          href: '/dashboard?section=analytics',
          roles: ['admin', 'super_admin']
        },
        {
          id: 'monitoring',
          title: 'System Monitor',
          icon: Monitor,
          href: '/dashboard?section=monitoring',
          roles: ['admin', 'super_admin']
        }
      ]
    }] : []),

    // Super Admin Tools (Super Admin only)
    ...(user?.role === 'super_admin' ? [{
      id: 'superadmin',
      title: 'Super Admin',
      icon: Crown,
      roles: ['super_admin'],
      children: [
        {
          id: 'website-settings',
          title: 'Website Settings',
          icon: Settings,
          href: '/dashboard?section=website-settings',
          roles: ['super_admin']
        },
        {
          id: 'payment-management',
          title: 'Payment Management',
          icon: CreditCard,
          href: '/dashboard?section=payments',
          roles: ['super_admin']
        },
        {
          id: 'bot-management',
          title: 'Bot Management',
          icon: Bot,
          href: '/dashboard?section=bots',
          roles: ['super_admin']
        },
        {
          id: 'whatsapp-bot',
          title: 'WhatsApp Bot',
          icon: MessageSquare,
          href: '/dashboard?section=whatsapp',
          roles: ['super_admin']
        },
        {
          id: 'telegram-bot',
          title: 'Telegram Bot',
          icon: Send,
          href: '/dashboard?section=telegram',
          roles: ['super_admin']
        },
        {
          id: 'system-config',
          title: 'System Config',
          icon: Cog,
          href: '/dashboard?section=system-config',
          roles: ['super_admin']
        },
        {
          id: 'security-center',
          title: 'Security Center',
          icon: ShieldCheck,
          href: '/dashboard?section=security',
          roles: ['super_admin']
        },
        {
          id: 'database-admin',
          title: 'Database Admin',
          icon: DatabaseIcon,
          href: '/dashboard?section=database',
          roles: ['super_admin']
        },
        {
          id: 'server-management',
          title: 'Server Management',
          icon: Server,
          href: '/dashboard?section=server',
          roles: ['super_admin']
        },
        {
          id: 'api-management',
          title: 'API Management',
          icon: Webhook,
          href: '/dashboard?section=api',
          roles: ['super_admin']
        }
      ]
    }] : []),

    // Personal Section
    {
      id: 'personal',
      title: 'Personal',
      icon: UserCheck,
      children: [
        {
          id: 'profile',
          title: 'Profile',
          icon: UserCheck,
          href: '/profile'
        },
        {
          id: 'plan',
          title: 'Subscription',
          icon: CreditCard,
          href: '/plan'
        },
        {
          id: 'settings',
          title: 'Settings',
          icon: Settings,
          href: '/settings'
        }
      ]
    }
  ]

  // Filter items based on user role and plan
  const filterItems = (items: SidebarItem[]): SidebarItem[] => {
    return items.filter(item => {
      // Check role permissions
      if (item.roles && !item.roles.includes(user?.role || 'user')) {
        return false
      }

      // Check plan permissions
      if (item.plans && !item.plans.includes(user?.plan || 'Free')) {
        return false
      }

      // Check premium access
      if (item.premium && user?.plan === 'Free') {
        return false
      }

      // Filter children recursively
      if (item.children) {
        item.children = filterItems(item.children)
      }

      return true
    })
  }

  const filteredItems = filterItems(sidebarItems)

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard' && !window.location.search
    }
    return pathname === href || pathname.startsWith(href + '/')
  }

  const renderSidebarItem = (item: SidebarItem, level: number = 0) => {
    const Icon = item.icon
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.id)
    const active = item.href ? isActive(item.href) : false

    return (
      <div key={item.id} className={`${level > 0 ? 'ml-4' : ''}`}>
        <div
          onClick={() => {
            if (hasChildren) {
              toggleExpanded(item.id)
            } else if (item.href) {
              router.push(item.href)
            }
          }}
          className={`
            flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200
            ${active
              ? 'bg-cyber-primary/20 text-cyber-primary border-l-4 border-cyber-primary'
              : 'text-gray-300 hover:bg-gray-800 hover:text-white'
            }
            ${item.comingSoon ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        >
          <div className="flex items-center space-x-3">
            <Icon className={`h-5 w-5 ${active ? 'text-cyber-primary' : ''}`} />
            {!isCollapsed && (
              <>
                <span className="font-medium">{item.title}</span>
                {item.badge && (
                  <span className="px-2 py-1 text-xs bg-cyber-primary/20 text-cyber-primary rounded-full">
                    {item.badge}
                  </span>
                )}
                {item.premium && user?.plan === 'Free' && (
                  <span className="px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded-full">
                    PRO
                  </span>
                )}
                {item.comingSoon && (
                  <span className="px-2 py-1 text-xs bg-gray-500/20 text-gray-400 rounded-full">
                    Soon
                  </span>
                )}
              </>
            )}
          </div>

          {!isCollapsed && hasChildren && (
            <div className={`transform transition-transform ${isExpanded ? 'rotate-90' : ''}`}>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          )}
        </div>

        {!isCollapsed && hasChildren && isExpanded && (
          <div className="mt-2 space-y-1">
            {item.children?.map(child => renderSidebarItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`
      bg-gray-900 border-r border-gray-800 transition-all duration-300 flex flex-col
      ${isCollapsed ? 'w-16' : 'w-64'}
    `}>
      {/* Header */}
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div>
              <h2 className="text-lg font-bold text-white">KodeXGuard</h2>
              <p className="text-xs text-gray-400">
                {user?.role === 'super_admin' ? '👑 Super Admin' :
                 user?.role === 'admin' ? '🛡️ Admin' :
                 '🔒 User'} • {user?.plan}
              </p>
            </div>
          )}
          <button
            onClick={onToggle}
            className="p-2 rounded-lg hover:bg-gray-800 text-gray-400 hover:text-white transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {filteredItems.map(item => renderSidebarItem(item))}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-800">
        {!isCollapsed && (
          <div className="text-xs text-gray-500 text-center">
            <p>© 2024 KodeXGuard</p>
            <p>Cybersecurity Platform</p>
          </div>
        )}
      </div>
    </div>
  )
}
