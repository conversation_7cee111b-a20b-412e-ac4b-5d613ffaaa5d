{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/is-local-url.ts"], "names": ["isAbsoluteUrl", "getLocationOrigin", "has<PERSON>ase<PERSON><PERSON>", "isLocalURL", "url", "locationOrigin", "resolved", "URL", "origin", "pathname", "_"], "mappings": "AAAA,SAASA,aAAa,EAAEC,iBAAiB,QAAQ,cAAa;AAC9D,SAASC,WAAW,QAAQ,mCAAkC;AAE9D;;CAEC,GACD,OAAO,SAASC,WAAWC,GAAW;IACpC,gEAAgE;IAChE,IAAI,CAACJ,cAAcI,MAAM,OAAO;IAChC,IAAI;QACF,4DAA4D;QAC5D,MAAMC,iBAAiBJ;QACvB,MAAMK,WAAW,IAAIC,IAAIH,KAAKC;QAC9B,OAAOC,SAASE,MAAM,KAAKH,kBAAkBH,YAAYI,SAASG,QAAQ;IAC5E,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF"}