import { db } from '../database'

export interface ScoreAction {
  type: 'osint_query' | 'vulnerability_scan' | 'file_analysis' | 'cve_report' | 'community_contribution' | 'bug_report' | 'achievement_unlock'
  points: number
  multiplier?: number
  description: string
  metadata?: any
}

export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  category: 'osint' | 'scanning' | 'analysis' | 'community' | 'special'
  requirements: {
    type: string
    count: number
    timeframe?: string
  }
  points: number
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary'
  unlockedBy?: number
}

export interface UserStats {
  userId: number
  totalScore: number
  level: number
  rank: number
  achievements: Achievement[]
  streaks: {
    current: number
    longest: number
    type: string
  }[]
  activities: {
    osintQueries: number
    vulnerabilityScans: number
    fileAnalyses: number
    cveReports: number
    communityContributions: number
  }
}

export class ScoringService {
  private scoreMultipliers = {
    'Free': 1.0,
    'Student': 1.2,
    'Hobby': 1.5,
    'Bughunter': 2.0,
    'Cybersecurity': 2.5
  }

  private baseScores: Record<string, number> = {
    'osint_query': 10,
    'vulnerability_scan': 25,
    'file_analysis': 15,
    'cve_report': 50,
    'community_contribution': 30,
    'bug_report': 100,
    'achievement_unlock': 0 // Variable based on achievement
  }

  async awardPoints(userId: number, action: ScoreAction): Promise<boolean> {
    try {
      // Get user's current plan for multiplier
      const [userRows] = await db.query(
        'SELECT plan FROM users WHERE id = ?',
        [userId]
      )

      if (!userRows || (userRows as any[]).length === 0) {
        throw new Error('User not found')
      }

      const user = (userRows as any[])[0]
      const planMultiplier = this.scoreMultipliers[user.plan as keyof typeof this.scoreMultipliers] || 1.0
      
      // Calculate final points
      const basePoints = this.baseScores[action.type] || action.points
      const finalPoints = Math.round(basePoints * planMultiplier * (action.multiplier || 1))

      // Start transaction
      await db.beginTransaction()

      try {
        // Update user's total score
        await db.query(
          'UPDATE users SET score = score + ?, updated_at = NOW() WHERE id = ?',
          [finalPoints, userId]
        )

        // Record score history
        await db.query(`
          INSERT INTO score_history (
            user_id, action_type, points_awarded, multiplier, description, metadata, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, NOW())
        `, [
          userId,
          action.type,
          finalPoints,
          planMultiplier * (action.multiplier || 1),
          action.description,
          JSON.stringify(action.metadata || {})
        ])

        // Update user level if needed
        await this.updateUserLevel(userId)

        // Check for new achievements
        await this.checkAchievements(userId, action)

        // Update activity streaks
        await this.updateStreaks(userId, action.type)

        await db.commit()
        return true
      } catch (error) {
        await db.rollback()
        throw error
      }
    } catch (error) {
      console.error('Error awarding points:', error)
      return false
    }
  }

  private async updateUserLevel(userId: number): Promise<void> {
    // Get user's current score
    const [userRows] = await db.query(
      'SELECT score, level FROM users WHERE id = ?',
      [userId]
    )

    if (!userRows || (userRows as any[]).length === 0) return

    const user = (userRows as any[])[0]
    const currentScore = user.score
    const currentLevel = user.level

    // Calculate new level based on score
    const newLevel = this.calculateLevel(currentScore)

    if (newLevel > currentLevel) {
      await db.query(
        'UPDATE users SET level = ? WHERE id = ?',
        [newLevel, userId]
      )

      // Award level up achievement
      await this.awardPoints(userId, {
        type: 'achievement_unlock',
        points: newLevel * 50,
        description: `Reached level ${newLevel}`,
        metadata: { level: newLevel, previousLevel: currentLevel }
      })
    }
  }

  private calculateLevel(score: number): number {
    // Level formula: level = floor(sqrt(score / 100))
    return Math.floor(Math.sqrt(score / 100)) + 1
  }

  private async checkAchievements(userId: number, action: ScoreAction): Promise<void> {
    const achievements = await this.getAvailableAchievements()
    
    for (const achievement of achievements) {
      const hasAchievement = await this.userHasAchievement(userId, achievement.id)
      if (hasAchievement) continue

      const meetsRequirements = await this.checkAchievementRequirements(userId, achievement, action)
      if (meetsRequirements) {
        await this.unlockAchievement(userId, achievement)
      }
    }
  }

  private async getAvailableAchievements(): Promise<Achievement[]> {
    // Predefined achievements
    return [
      {
        id: 'first_osint',
        name: 'First Investigation',
        description: 'Complete your first OSINT query',
        icon: '🔍',
        category: 'osint',
        requirements: { type: 'osint_query', count: 1 },
        points: 50,
        rarity: 'common'
      },
      {
        id: 'osint_master',
        name: 'OSINT Master',
        description: 'Complete 100 OSINT queries',
        icon: '🕵️',
        category: 'osint',
        requirements: { type: 'osint_query', count: 100 },
        points: 500,
        rarity: 'rare'
      },
      {
        id: 'vulnerability_hunter',
        name: 'Vulnerability Hunter',
        description: 'Find 10 vulnerabilities',
        icon: '🎯',
        category: 'scanning',
        requirements: { type: 'vulnerability_scan', count: 10 },
        points: 250,
        rarity: 'uncommon'
      },
      {
        id: 'daily_streak_7',
        name: 'Week Warrior',
        description: 'Maintain a 7-day activity streak',
        icon: '🔥',
        category: 'special',
        requirements: { type: 'daily_streak', count: 7 },
        points: 200,
        rarity: 'uncommon'
      },
      {
        id: 'community_helper',
        name: 'Community Helper',
        description: 'Make 25 community contributions',
        icon: '🤝',
        category: 'community',
        requirements: { type: 'community_contribution', count: 25 },
        points: 300,
        rarity: 'rare'
      },
      {
        id: 'bug_bounty_legend',
        name: 'Bug Bounty Legend',
        description: 'Report 50 valid bugs',
        icon: '👑',
        category: 'special',
        requirements: { type: 'bug_report', count: 50 },
        points: 2500,
        rarity: 'legendary'
      }
    ]
  }

  private async userHasAchievement(userId: number, achievementId: string): Promise<boolean> {
    const [rows] = await db.query(
      'SELECT id FROM user_achievements WHERE user_id = ? AND achievement_id = ?',
      [userId, achievementId]
    )
    return rows && (rows as any[]).length > 0
  }

  private async checkAchievementRequirements(
    userId: number, 
    achievement: Achievement, 
    action: ScoreAction
  ): Promise<boolean> {
    const req = achievement.requirements

    switch (req.type) {
      case 'osint_query':
      case 'vulnerability_scan':
      case 'file_analysis':
      case 'cve_report':
      case 'community_contribution':
      case 'bug_report':
        const [countRows] = await db.query(
          'SELECT COUNT(*) as count FROM score_history WHERE user_id = ? AND action_type = ?',
          [userId, req.type]
        )
        const count = (countRows as any[])[0].count
        return count >= req.count

      case 'daily_streak':
        const [streakRows] = await db.query(
          'SELECT current_streak FROM user_streaks WHERE user_id = ? AND streak_type = ?',
          [userId, 'daily_activity']
        )
        if (!streakRows || (streakRows as any[]).length === 0) return false
        const currentStreak = (streakRows as any[])[0].current_streak
        return currentStreak >= req.count

      default:
        return false
    }
  }

  private async unlockAchievement(userId: number, achievement: Achievement): Promise<void> {
    // Record achievement unlock
    await db.query(`
      INSERT INTO user_achievements (user_id, achievement_id, unlocked_at)
      VALUES (?, ?, NOW())
    `, [userId, achievement.id])

    // Award achievement points
    await this.awardPoints(userId, {
      type: 'achievement_unlock',
      points: achievement.points,
      description: `Unlocked achievement: ${achievement.name}`,
      metadata: { achievement: achievement }
    })
  }

  private async updateStreaks(userId: number, actionType: string): Promise<void> {
    const today = new Date().toISOString().split('T')[0]
    
    // Check if user has activity today
    const [activityRows] = await db.query(`
      SELECT COUNT(*) as count FROM score_history 
      WHERE user_id = ? AND DATE(created_at) = ?
    `, [userId, today])

    const hasActivityToday = (activityRows as any[])[0].count > 0

    if (hasActivityToday) {
      // Update or create daily activity streak
      await db.query(`
        INSERT INTO user_streaks (user_id, streak_type, current_streak, longest_streak, last_activity_date)
        VALUES (?, 'daily_activity', 1, 1, ?)
        ON DUPLICATE KEY UPDATE
          current_streak = CASE 
            WHEN DATE(last_activity_date) = DATE_SUB(?, INTERVAL 1 DAY) THEN current_streak + 1
            WHEN DATE(last_activity_date) = ? THEN current_streak
            ELSE 1
          END,
          longest_streak = GREATEST(longest_streak, current_streak),
          last_activity_date = ?
      `, [userId, today, today, today, today])
    }
  }

  async getUserStats(userId: number): Promise<UserStats | null> {
    try {
      // Get user basic info
      const [userRows] = await db.query(
        'SELECT score, level FROM users WHERE id = ?',
        [userId]
      )

      if (!userRows || (userRows as any[]).length === 0) return null

      const user = (userRows as any[])[0]

      // Get user rank
      const [rankRows] = await db.query(`
        SELECT COUNT(*) + 1 as rank FROM users 
        WHERE score > (SELECT score FROM users WHERE id = ?)
      `, [userId])

      const rank = (rankRows as any[])[0].rank

      // Get user achievements
      const [achievementRows] = await db.query(`
        SELECT ua.achievement_id, ua.unlocked_at
        FROM user_achievements ua
        WHERE ua.user_id = ?
        ORDER BY ua.unlocked_at DESC
      `, [userId])

      const achievements = await Promise.all(
        (achievementRows as any[]).map(async (row: any) => {
          const allAchievements = await this.getAvailableAchievements()
          return allAchievements.find(a => a.id === row.achievement_id)
        })
      ).then(results => results.filter(Boolean) as Achievement[])

      // Get user streaks
      const [streakRows] = await db.query(
        'SELECT streak_type, current_streak, longest_streak FROM user_streaks WHERE user_id = ?',
        [userId]
      )

      const streaks = (streakRows as any[]).map((row: any) => ({
        current: row.current_streak,
        longest: row.longest_streak,
        type: row.streak_type
      }))

      // Get activity counts
      const [activityRows] = await db.query(`
        SELECT 
          action_type,
          COUNT(*) as count
        FROM score_history 
        WHERE user_id = ?
        GROUP BY action_type
      `, [userId])

      const activities = {
        osintQueries: 0,
        vulnerabilityScans: 0,
        fileAnalyses: 0,
        cveReports: 0,
        communityContributions: 0
      }

      ;(activityRows as any[]).forEach((row: any) => {
        switch (row.action_type) {
          case 'osint_query':
            activities.osintQueries = row.count
            break
          case 'vulnerability_scan':
            activities.vulnerabilityScans = row.count
            break
          case 'file_analysis':
            activities.fileAnalyses = row.count
            break
          case 'cve_report':
            activities.cveReports = row.count
            break
          case 'community_contribution':
            activities.communityContributions = row.count
            break
        }
      })

      return {
        userId,
        totalScore: user.score,
        level: user.level,
        rank,
        achievements,
        streaks,
        activities
      }
    } catch (error) {
      console.error('Error getting user stats:', error)
      return null
    }
  }

  async getLeaderboard(limit: number = 50, offset: number = 0): Promise<any[]> {
    try {
      const [rows] = await db.query(`
        SELECT 
          u.id,
          u.username,
          u.email,
          u.score,
          u.level,
          u.plan,
          u.created_at,
          COUNT(ua.id) as achievement_count,
          ROW_NUMBER() OVER (ORDER BY u.score DESC) as rank
        FROM users u
        LEFT JOIN user_achievements ua ON u.id = ua.user_id
        WHERE u.score > 0
        GROUP BY u.id
        ORDER BY u.score DESC
        LIMIT ? OFFSET ?
      `, [limit, offset])

      return rows as any[]
    } catch (error) {
      console.error('Error getting leaderboard:', error)
      return []
    }
  }
}
