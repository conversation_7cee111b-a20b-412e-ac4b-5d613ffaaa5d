{"version": 3, "sources": ["io/whatwg/writer.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;;AAIrB,kDAAqD;AAGrD,cAAc;AACd,SAAgB,iCAAiC,CAE7C,gBAA6E,EAC7E,gBAAyD;IAGzD,MAAM,MAAM,GAAG,IAAI,IAAI,CAAI,gBAAgB,CAAC,CAAC;IAC7C,MAAM,MAAM,GAAG,IAAI,2BAAe,CAAC,MAAM,CAAC,CAAC;IAC3C,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAC;QAChC,iBAAiB;QACX,MAAM;0EAAK,MAAM,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SAAA;QACnC,IAAI,CAAC,UAAU;0EAAI,MAAM,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SAAA;QAC5C,KAAK,CAAC,UAAU;0EAAI,MAAM,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SAAA;KACtD,kBAAI,eAAe,EAAE,SAAA,CAAC,EAAI,EAAE,CAAA,IAAK,gBAAgB,EAAG,CAAC;IAEtD,OAAO,EAAE,QAAQ,EAAE,IAAI,cAAc,CAAC,MAAM,EAAE,gBAAgB,CAAC,EAAE,QAAQ,EAAE,CAAC;IAE5E,SAAe,IAAI,CAAC,UAAuD;;YACvE,IAAI,GAAG,GAAsB,IAAI,CAAC;YAClC,IAAI,IAAI,GAAG,UAAU,CAAC,WAAW,CAAC;YAClC,OAAO,GAAG,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;gBAC3C,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACxB,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;oBAAC,OAAO;gBAAC,CAAC;YAClE,CAAC;YACD,UAAU,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;KAAA;AACL,CAAC;AA1BD,8EA0BC", "file": "writer.js", "sourceRoot": "../../src"}