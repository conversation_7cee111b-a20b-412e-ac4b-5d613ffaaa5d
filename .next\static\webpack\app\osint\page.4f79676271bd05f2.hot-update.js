"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/osint/page",{

/***/ "(app-pages-browser)/./components/DashboardLayout.tsx":
/*!****************************************!*\
  !*** ./components/DashboardLayout.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,Bell,Bot,ChevronDown,ChevronRight,Crown,Database,FileText,Flame,Globe,Home,Lock,LogOut,Menu,Search,Server,Settings,Shield,TrendingUp,User,Users,Wifi,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DashboardLayout(param) {\n    let { children } = param;\n    _s();\n    const [isSidebarOpen, setIsSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"online\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Update time every minute\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    // Load user and check auth\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            try {\n                var _userData_id;\n                // Get user data from cookies (set by middleware)\n                const userCookie = document.cookie.split(\"; \").find((row)=>row.startsWith(\"user=\"));\n                if (!userCookie) {\n                    router.push(\"/login\");\n                    return;\n                }\n                const userData = JSON.parse(decodeURIComponent(userCookie.split(\"=\")[1]));\n                // Transform to match our User interface\n                const transformedUser = {\n                    id: ((_userData_id = userData.id) === null || _userData_id === void 0 ? void 0 : _userData_id.toString()) || \"1\",\n                    username: userData.username || \"User\",\n                    email: userData.email || \"<EMAIL>\",\n                    fullName: userData.username || \"KodeX User\",\n                    role: userData.role || \"user\",\n                    plan: userData.plan || \"Free\",\n                    level: 28,\n                    score: 8950,\n                    streak: 12\n                };\n                setUser(transformedUser);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Auth check error:\", error);\n                router.push(\"/login\");\n            }\n        };\n        checkAuth();\n    }, [\n        router\n    ]);\n    const handleLogout = async ()=>{\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"refreshToken\");\n        localStorage.removeItem(\"user\");\n        router.push(\"/login\");\n    };\n    const toggleSidebar = ()=>{\n        setIsSidebarOpen(!isSidebarOpen);\n    };\n    const toggleExpanded = (itemName)=>{\n        setExpandedItems((prev)=>prev.includes(itemName) ? prev.filter((name)=>name !== itemName) : [\n                ...prev,\n                itemName\n            ]);\n    };\n    const isActive = (href)=>{\n        if (href === \"/dashboard\") {\n            return pathname === \"/dashboard\";\n        }\n        return pathname.startsWith(href);\n    };\n    const isAdmin = pathname.startsWith(\"/admin\");\n    const dashboardNavItems = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            name: \"Profile\",\n            href: \"/profile\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"Settings\",\n            href: \"/settings\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"Tools\",\n            href: \"#\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            children: [\n                {\n                    name: \"OSINT Lookup\",\n                    href: \"/osint\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    badge: \"Popular\"\n                },\n                {\n                    name: \"Vulnerability Scanner\",\n                    href: \"/scanner\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    badge: \"Pro\"\n                },\n                {\n                    name: \"File Analyzer\",\n                    href: \"/file-analyzer\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    badge: \"Pro\"\n                },\n                {\n                    name: \"CVE Database\",\n                    href: \"/cve\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                },\n                {\n                    name: \"Google Dorking\",\n                    href: \"/dorking\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                }\n            ]\n        },\n        {\n            name: \"Resources\",\n            href: \"#\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            children: [\n                {\n                    name: \"Documentation\",\n                    href: \"/docs\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                },\n                {\n                    name: \"Community\",\n                    href: \"/community\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                },\n                {\n                    name: \"Leaderboard\",\n                    href: \"/leaderboard\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                }\n            ]\n        }\n    ];\n    const adminNavItems = [\n        {\n            name: \"Dashboard\",\n            href: \"/admin/dashboard\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        },\n        {\n            name: \"Users\",\n            href: \"/admin/users\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            name: \"Bots\",\n            href: \"/admin/bots\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n        },\n        {\n            name: \"Plans\",\n            href: \"/admin/plans\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n        },\n        {\n            name: \"Settings\",\n            href: \"/admin/settings\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"System\",\n            href: \"#\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            children: [\n                {\n                    name: \"Monitoring\",\n                    href: \"/admin/monitoring\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n                },\n                {\n                    name: \"Logs\",\n                    href: \"/admin/logs\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                },\n                {\n                    name: \"Security\",\n                    href: \"/admin/security\",\n                    icon: _barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n                }\n            ]\n        }\n    ];\n    const navItems = isAdmin ? adminNavItems : dashboardNavItems;\n    const getPlanColor = (plan)=>{\n        switch(plan){\n            case \"Elite\":\n                return \"text-yellow-400 bg-yellow-400/20\";\n            case \"Expert\":\n                return \"text-purple-400 bg-purple-400/20\";\n            case \"Pro\":\n                return \"text-blue-400 bg-blue-400/20\";\n            case \"Free\":\n                return \"text-gray-400 bg-gray-400/20\";\n            default:\n                return \"text-gray-400 bg-gray-400/20\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-cyber-dark flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-cyber-primary font-medium\",\n                        children: \"Initializing cyber interface...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 216,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-cyber-dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-cyber-dark/95 backdrop-blur-md border-b border-cyber-border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleSidebar,\n                                    className: \"p-2 rounded-lg text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-colors lg:hidden\",\n                                    children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 32\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 60\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-8 w-8 text-cyber-primary animate-cyber-glow\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden sm:block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl font-bold text-cyber-glow\",\n                                                    children: \"KodeXGuard\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-cyber-secondary uppercase tracking-wider\",\n                                                    children: isAdmin ? \"Admin Console\" : \"Cyber Platform\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex flex-1 max-w-md mx-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search...\",\n                                        className: \"w-full pl-10 pr-4 py-2 rounded-lg input-cyber text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full \".concat(systemStatus === \"online\" ? \"bg-green-400\" : \"bg-red-400\", \" animate-pulse\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: [\n                                                \"System \",\n                                                systemStatus\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:block text-xs text-gray-400 font-mono\",\n                                    children: currentTime.toLocaleTimeString()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 rounded-lg text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-colors relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-1 right-1 w-2 h-2 bg-cyber-secondary rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsProfileOpen(!isProfileOpen),\n                                            className: \"flex items-center space-x-3 p-2 rounded-lg hover:bg-cyber-primary/10 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-sm font-bold text-black\",\n                                                    children: user.username.charAt(0)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"hidden sm:block text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-white\",\n                                                            children: user.username\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs px-2 py-0.5 rounded-full \".concat(getPlanColor(user.plan)),\n                                                            children: user.plan\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        isProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 top-full mt-2 w-64 bg-cyber-card border border-cyber-border rounded-lg shadow-xl z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 border-b border-cyber-border\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-lg font-bold text-black\",\n                                                                    children: user.username.charAt(0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-white\",\n                                                                            children: user.fullName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 314,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-400\",\n                                                                            children: user.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        user.streak && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-1 text-xs text-cyber-secondary\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                                    lineNumber: 318,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: [\n                                                                                        user.streak,\n                                                                                        \" day streak\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                                    lineNumber: 319,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        user.level && user.score && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-3 grid grid-cols-2 gap-3 text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-bold text-cyber-primary\",\n                                                                            children: user.level\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"Level\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-bold text-cyber-secondary\",\n                                                                            children: user.score.toLocaleString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"Score\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>router.push(\"/profile\"),\n                                                            className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-white hover:bg-cyber-primary/10 rounded-lg transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-cyber-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Profile\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>router.push(\"/settings\"),\n                                                            className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-white hover:bg-cyber-primary/10 rounded-lg transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-cyber-primary\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Settings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleLogout,\n                                                            className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-red-400 hover:bg-red-500/10 rounded-lg transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Logout\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"fixed top-16 left-0 z-40 w-64 h-[calc(100vh-4rem)] bg-cyber-card border-r border-cyber-border transform transition-transform duration-300 ease-in-out \".concat(isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\", \" lg:translate-x-0\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 p-4 space-y-2 overflow-y-auto\",\n                            children: navItems.map((item)=>{\n                                var _item_children, _item_children1;\n                                const Icon = item.icon;\n                                const hasChildren = item.children && item.children.length > 0;\n                                const isExpanded = expandedItems.includes(item.name);\n                                const itemIsActive = hasChildren ? (_item_children = item.children) === null || _item_children === void 0 ? void 0 : _item_children.some((child)=>isActive(child.href)) : isActive(item.href);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleExpanded(item.name),\n                                            className: \"w-full flex items-center justify-between px-3 py-2 rounded-lg font-medium transition-colors duration-200 \".concat(itemIsActive ? \"bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30\" : \"text-gray-300 hover:text-white hover:bg-cyber-primary/10\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"h-4 w-4 transition-transform duration-200 \".concat(isExpanded ? \"rotate-90\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>router.push(item.href),\n                                            className: \"w-full flex items-center space-x-3 px-3 py-2 rounded-lg font-medium transition-colors duration-200 \".concat(itemIsActive ? \"bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30\" : \"text-gray-300 hover:text-white hover:bg-cyber-primary/10\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 23\n                                                }, this),\n                                                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-auto bg-cyber-secondary/20 text-cyber-secondary px-2 py-0.5 rounded-full text-xs font-bold\",\n                                                    children: item.badge\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 21\n                                        }, this),\n                                        hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 mt-2 space-y-1\",\n                                            children: (_item_children1 = item.children) === null || _item_children1 === void 0 ? void 0 : _item_children1.map((child)=>{\n                                                const ChildIcon = child.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(child.href),\n                                                    className: \"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors duration-200 \".concat(isActive(child.href) ? \"bg-cyber-primary/20 text-cyber-primary\" : \"text-gray-400 hover:text-white hover:bg-cyber-primary/10\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChildIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: child.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        child.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-auto bg-cyber-secondary/20 text-cyber-secondary px-1.5 py-0.5 rounded-full text-xs font-bold\",\n                                                            children: child.badge\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    ]\n                                                }, child.name, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 27\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t border-cyber-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mb-2\",\n                                        children: \"System Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full \".concat(systemStatus === \"online\" ? \"bg-green-400\" : \"bg-red-400\", \" animate-pulse\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-300 capitalize\",\n                                                children: systemStatus\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this),\n            isSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-30 bg-black/50 lg:hidden\",\n                onClick: ()=>setIsSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 465,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"transition-all duration-300 pt-16 \".concat(isSidebarOpen ? \"lg:ml-64\" : \"lg:ml-64\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-[calc(100vh-4rem)]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-cyber-card border-t border-cyber-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-6 py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-1 md:col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-8 w-8 text-cyber-primary animate-cyber-glow\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-cyber-glow\",\n                                                                    children: \"KodeXGuard\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-cyber-secondary uppercase tracking-wider\",\n                                                                    children: \"Cyber Security Platform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm mb-4\",\n                                                    children: \"Advanced cybersecurity platform for vulnerability scanning, OSINT intelligence, and comprehensive security analysis.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"All systems operational\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-bold text-white mb-4 uppercase tracking-wider\",\n                                                    children: \"Quick Links\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/dashboard\"),\n                                                                className: \"text-gray-400 hover:text-cyber-primary transition-colors text-sm\",\n                                                                children: \"Dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/profile\"),\n                                                                className: \"text-gray-400 hover:text-cyber-primary transition-colors text-sm\",\n                                                                children: \"Profile\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/settings\"),\n                                                                className: \"text-gray-400 hover:text-cyber-primary transition-colors text-sm\",\n                                                                children: \"Settings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/docs\"),\n                                                                className: \"text-gray-400 hover:text-cyber-primary transition-colors text-sm\",\n                                                                children: \"Documentation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-bold text-white mb-4 uppercase tracking-wider\",\n                                                    children: \"System\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Version:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-cyber-primary\",\n                                                                    children: \"v2.1.0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Uptime:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400\",\n                                                                    children: \"99.8%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Users:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-cyber-secondary\",\n                                                                    children: \"15.4K\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Scans:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-cyber-accent\",\n                                                                    children: \"89.4K\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 pt-8 border-t border-cyber-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"\\xa9 2024 KodeXGuard. All rights reserved.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 mt-4 md:mt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Last updated: \",\n                                                            currentTime.toLocaleDateString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_Bell_Bot_ChevronDown_ChevronRight_Crown_Database_FileText_Flame_Globe_Home_Lock_LogOut_Menu_Search_Server_Settings_Shield_TrendingUp_User_Users_Wifi_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-400\",\n                                                                children: \"Connected\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 472,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardLayout, \"heUARQDEz6nOjgtj3NY6TSiXxgU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = DashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"DashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/DashboardLayout.tsx\n"));

/***/ })

});