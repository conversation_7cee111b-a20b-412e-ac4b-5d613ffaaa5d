"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const V=require("node:process"),fi=require("node:child_process"),K=require("node:url"),J=require("node:path"),te=require("node:util"),mt=require("node:os"),R=require("./index.js"),he=require("node:fs"),Uu=require("node:constants"),ae=require("node:stream"),Zt=require("node:assert"),Re=require("node:events"),er=require("node:crypto"),oe=require("./index-BmUFwMVL.js"),fe=require("node:http"),yt=require("node:https"),ht=require("node:buffer"),hi=require("node:zlib"),qu=require("node:net"),Ft=require("node:tls"),We=require("node:dns"),Mr=require("node:http2"),Hu=require("./index-BnIU43YD.js");var cn=typeof document<"u"?document.currentScript:null,de=Uu,di=process.cwd,Ut=null,pi=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return Ut||(Ut=di.call(process)),Ut};try{process.cwd()}catch{}if(typeof process.chdir=="function"){var Dn=process.chdir;process.chdir=function(t){Ut=null,Dn.call(process,t)},Object.setPrototypeOf&&Object.setPrototypeOf(process.chdir,Dn)}var mi=yi;function yi(t){de.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&e(t),t.lutimes||r(t),t.chown=i(t.chown),t.fchown=i(t.fchown),t.lchown=i(t.lchown),t.chmod=n(t.chmod),t.fchmod=n(t.fchmod),t.lchmod=n(t.lchmod),t.chownSync=u(t.chownSync),t.fchownSync=u(t.fchownSync),t.lchownSync=u(t.lchownSync),t.chmodSync=s(t.chmodSync),t.fchmodSync=s(t.fchmodSync),t.lchmodSync=s(t.lchmodSync),t.stat=o(t.stat),t.fstat=o(t.fstat),t.lstat=o(t.lstat),t.statSync=c(t.statSync),t.fstatSync=c(t.fstatSync),t.lstatSync=c(t.lstatSync),t.chmod&&!t.lchmod&&(t.lchmod=function(D,d,h){h&&process.nextTick(h)},t.lchmodSync=function(){}),t.chown&&!t.lchown&&(t.lchown=function(D,d,h,p){p&&process.nextTick(p)},t.lchownSync=function(){}),pi==="win32"&&(t.rename=typeof t.rename!="function"?t.rename:function(D){function d(h,p,m){var f=Date.now(),F=0;D(h,p,function y(x){if(x&&(x.code==="EACCES"||x.code==="EPERM"||x.code==="EBUSY")&&Date.now()-f<6e4){setTimeout(function(){t.stat(p,function(C,A){C&&C.code==="ENOENT"?D(h,p,y):m(x)})},F),F<100&&(F+=10);return}m&&m(x)})}return Object.setPrototypeOf&&Object.setPrototypeOf(d,D),d}(t.rename)),t.read=typeof t.read!="function"?t.read:function(D){function d(h,p,m,f,F,y){var x;if(y&&typeof y=="function"){var C=0;x=function(A,N,k){if(A&&A.code==="EAGAIN"&&C<10)return C++,D.call(t,h,p,m,f,F,x);y.apply(this,arguments)}}return D.call(t,h,p,m,f,F,x)}return Object.setPrototypeOf&&Object.setPrototypeOf(d,D),d}(t.read),t.readSync=typeof t.readSync!="function"?t.readSync:function(D){return function(d,h,p,m,f){for(var F=0;;)try{return D.call(t,d,h,p,m,f)}catch(y){if(y.code==="EAGAIN"&&F<10){F++;continue}throw y}}}(t.readSync);function e(D){D.lchmod=function(d,h,p){D.open(d,de.O_WRONLY|de.O_SYMLINK,h,function(m,f){if(m){p&&p(m);return}D.fchmod(f,h,function(F){D.close(f,function(y){p&&p(F||y)})})})},D.lchmodSync=function(d,h){var p=D.openSync(d,de.O_WRONLY|de.O_SYMLINK,h),m=!0,f;try{f=D.fchmodSync(p,h),m=!1}finally{if(m)try{D.closeSync(p)}catch{}else D.closeSync(p)}return f}}function r(D){de.hasOwnProperty("O_SYMLINK")&&D.futimes?(D.lutimes=function(d,h,p,m){D.open(d,de.O_SYMLINK,function(f,F){if(f){m&&m(f);return}D.futimes(F,h,p,function(y){D.close(F,function(x){m&&m(y||x)})})})},D.lutimesSync=function(d,h,p){var m=D.openSync(d,de.O_SYMLINK),f,F=!0;try{f=D.futimesSync(m,h,p),F=!1}finally{if(F)try{D.closeSync(m)}catch{}else D.closeSync(m)}return f}):D.futimes&&(D.lutimes=function(d,h,p,m){m&&process.nextTick(m)},D.lutimesSync=function(){})}function n(D){return D&&function(d,h,p){return D.call(t,d,h,function(m){l(m)&&(m=null),p&&p.apply(this,arguments)})}}function s(D){return D&&function(d,h){try{return D.call(t,d,h)}catch(p){if(!l(p))throw p}}}function i(D){return D&&function(d,h,p,m){return D.call(t,d,h,p,function(f){l(f)&&(f=null),m&&m.apply(this,arguments)})}}function u(D){return D&&function(d,h,p){try{return D.call(t,d,h,p)}catch(m){if(!l(m))throw m}}}function o(D){return D&&function(d,h,p){typeof h=="function"&&(p=h,h=null);function m(f,F){F&&(F.uid<0&&(F.uid+=4294967296),F.gid<0&&(F.gid+=4294967296)),p&&p.apply(this,arguments)}return h?D.call(t,d,h,m):D.call(t,d,m)}}function c(D){return D&&function(d,h){var p=h?D.call(t,d,h):D.call(t,d);return p&&(p.uid<0&&(p.uid+=4294967296),p.gid<0&&(p.gid+=4294967296)),p}}function l(D){if(!D||D.code==="ENOSYS")return!0;var d=!process.getuid||process.getuid()!==0;return!!(d&&(D.code==="EINVAL"||D.code==="EPERM"))}}var ln=ae.Stream,Fi=gi;function gi(t){return{ReadStream:e,WriteStream:r};function e(n,s){if(!(this instanceof e))return new e(n,s);ln.call(this);var i=this;this.path=n,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=64*1024,s=s||{};for(var u=Object.keys(s),o=0,c=u.length;o<c;o++){var l=u[o];this[l]=s[l]}if(this.encoding&&this.setEncoding(this.encoding),this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.end===void 0)this.end=1/0;else if(typeof this.end!="number")throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}if(this.fd!==null){process.nextTick(function(){i._read()});return}t.open(this.path,this.flags,this.mode,function(D,d){if(D){i.emit("error",D),i.readable=!1;return}i.fd=d,i.emit("open",d),i._read()})}function r(n,s){if(!(this instanceof r))return new r(n,s);ln.call(this),this.path=n,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,s=s||{};for(var i=Object.keys(s),u=0,o=i.length;u<o;u++){var c=i[u];this[c]=s[c]}if(this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1,this._queue=[],this.fd===null&&(this._open=t.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}var Ei=bi,Ci=Object.getPrototypeOf||function(t){return t.__proto__};function bi(t){if(t===null||typeof t!="object")return t;if(t instanceof Object)var e={__proto__:Ci(t)};else var e=Object.create(null);return Object.getOwnPropertyNames(t).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}var G=he,wi=mi,_i=Fi,xi=Ei,wt=te,Z,Gt;typeof Symbol=="function"&&typeof Symbol.for=="function"?(Z=Symbol.for("graceful-fs.queue"),Gt=Symbol.for("graceful-fs.previous")):(Z="___graceful-fs.queue",Gt="___graceful-fs.previous");function Ai(){}function Mu(t,e){Object.defineProperty(t,Z,{get:function(){return e}})}var Be=Ai;wt.debuglog?Be=wt.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&(Be=function(){var t=wt.format.apply(wt,arguments);t="GFS4: "+t.split(/\n/).join(`
GFS4: `),console.error(t)});if(!G[Z]){var Bi=R.commonjsGlobal[Z]||[];Mu(G,Bi),G.close=function(t){function e(r,n){return t.call(G,r,function(s){s||fn(),typeof n=="function"&&n.apply(this,arguments)})}return Object.defineProperty(e,Gt,{value:t}),e}(G.close),G.closeSync=function(t){function e(r){t.apply(G,arguments),fn()}return Object.defineProperty(e,Gt,{value:t}),e}(G.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",function(){Be(G[Z]),Zt.equal(G[Z].length,0)})}R.commonjsGlobal[Z]||Mu(R.commonjsGlobal,G[Z]);var zu=zr(xi(G));process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!G.__patched&&(zu=zr(G),G.__patched=!0);function zr(t){wi(t),t.gracefulify=zr,t.createReadStream=N,t.createWriteStream=k;var e=t.readFile;t.readFile=r;function r(g,b,_){return typeof b=="function"&&(_=b,b=null),P(g,b,_);function P(T,j,O,$){return e(T,j,function(B){B&&(B.code==="EMFILE"||B.code==="ENFILE")?je([P,[T,j,O],B,$||Date.now(),Date.now()]):typeof O=="function"&&O.apply(this,arguments)})}}var n=t.writeFile;t.writeFile=s;function s(g,b,_,P){return typeof _=="function"&&(P=_,_=null),T(g,b,_,P);function T(j,O,$,B,L){return n(j,O,$,function(v){v&&(v.code==="EMFILE"||v.code==="ENFILE")?je([T,[j,O,$,B],v,L||Date.now(),Date.now()]):typeof B=="function"&&B.apply(this,arguments)})}}var i=t.appendFile;i&&(t.appendFile=u);function u(g,b,_,P){return typeof _=="function"&&(P=_,_=null),T(g,b,_,P);function T(j,O,$,B,L){return i(j,O,$,function(v){v&&(v.code==="EMFILE"||v.code==="ENFILE")?je([T,[j,O,$,B],v,L||Date.now(),Date.now()]):typeof B=="function"&&B.apply(this,arguments)})}}var o=t.copyFile;o&&(t.copyFile=c);function c(g,b,_,P){return typeof _=="function"&&(P=_,_=0),T(g,b,_,P);function T(j,O,$,B,L){return o(j,O,$,function(v){v&&(v.code==="EMFILE"||v.code==="ENFILE")?je([T,[j,O,$,B],v,L||Date.now(),Date.now()]):typeof B=="function"&&B.apply(this,arguments)})}}var l=t.readdir;t.readdir=d;var D=/^v[0-5]\./;function d(g,b,_){typeof b=="function"&&(_=b,b=null);var P=D.test(process.version)?function(O,$,B,L){return l(O,T(O,$,B,L))}:function(O,$,B,L){return l(O,$,T(O,$,B,L))};return P(g,b,_);function T(j,O,$,B){return function(L,v){L&&(L.code==="EMFILE"||L.code==="ENFILE")?je([P,[j,O,$],L,B||Date.now(),Date.now()]):(v&&v.sort&&v.sort(),typeof $=="function"&&$.call(this,L,v))}}}if(process.version.substr(0,4)==="v0.8"){var h=_i(t);y=h.ReadStream,C=h.WriteStream}var p=t.ReadStream;p&&(y.prototype=Object.create(p.prototype),y.prototype.open=x);var m=t.WriteStream;m&&(C.prototype=Object.create(m.prototype),C.prototype.open=A),Object.defineProperty(t,"ReadStream",{get:function(){return y},set:function(g){y=g},enumerable:!0,configurable:!0}),Object.defineProperty(t,"WriteStream",{get:function(){return C},set:function(g){C=g},enumerable:!0,configurable:!0});var f=y;Object.defineProperty(t,"FileReadStream",{get:function(){return f},set:function(g){f=g},enumerable:!0,configurable:!0});var F=C;Object.defineProperty(t,"FileWriteStream",{get:function(){return F},set:function(g){F=g},enumerable:!0,configurable:!0});function y(g,b){return this instanceof y?(p.apply(this,arguments),this):y.apply(Object.create(y.prototype),arguments)}function x(){var g=this;I(g.path,g.flags,g.mode,function(b,_){b?(g.autoClose&&g.destroy(),g.emit("error",b)):(g.fd=_,g.emit("open",_),g.read())})}function C(g,b){return this instanceof C?(m.apply(this,arguments),this):C.apply(Object.create(C.prototype),arguments)}function A(){var g=this;I(g.path,g.flags,g.mode,function(b,_){b?(g.destroy(),g.emit("error",b)):(g.fd=_,g.emit("open",_))})}function N(g,b){return new t.ReadStream(g,b)}function k(g,b){return new t.WriteStream(g,b)}var S=t.open;t.open=I;function I(g,b,_,P){return typeof _=="function"&&(P=_,_=null),T(g,b,_,P);function T(j,O,$,B,L){return S(j,O,$,function(v,li){v&&(v.code==="EMFILE"||v.code==="ENFILE")?je([T,[j,O,$,B],v,L||Date.now(),Date.now()]):typeof B=="function"&&B.apply(this,arguments)})}}return t}function je(t){Be("ENQUEUE",t[0].name,t[1]),G[Z].push(t),Gr()}var _t;function fn(){for(var t=Date.now(),e=0;e<G[Z].length;++e)G[Z][e].length>2&&(G[Z][e][3]=t,G[Z][e][4]=t);Gr()}function Gr(){if(clearTimeout(_t),_t=void 0,G[Z].length!==0){var t=G[Z].shift(),e=t[0],r=t[1],n=t[2],s=t[3],i=t[4];if(s===void 0)Be("RETRY",e.name,r),e.apply(null,r);else if(Date.now()-s>=6e4){Be("TIMEOUT",e.name,r);var u=r.pop();typeof u=="function"&&u.call(null,n)}else{var o=Date.now()-i,c=Math.max(i-s,1),l=Math.min(c*1.2,100);o>=l?(Be("RETRY",e.name,r),e.apply(null,r.concat([s]))):G[Z].push(t)}_t===void 0&&(_t=setTimeout(Gr,0))}}const hn=R.getDefaultExportFromCjs(zu),Ee=mt.homedir(),{env:Te}=process,dn=Te.XDG_DATA_HOME||(Ee?J.join(Ee,".local","share"):void 0),Wt=Te.XDG_CONFIG_HOME||(Ee?J.join(Ee,".config"):void 0);Te.XDG_STATE_HOME||Ee&&J.join(Ee,".local","state");Te.XDG_CACHE_HOME||Ee&&J.join(Ee,".cache");Te.XDG_RUNTIME_DIR;const Si=(Te.XDG_DATA_DIRS||"/usr/local/share/:/usr/share/").split(":");dn&&Si.unshift(dn);const vi=(Te.XDG_CONFIG_DIRS||"/etc/xdg").split(":");Wt&&vi.unshift(Wt);var gt={exports:{}},He={exports:{}},Dr={exports:{}},pn;function Oi(){return pn||(pn=1,function(t){t.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"],process.platform!=="win32"&&t.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT"),process.platform==="linux"&&t.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")}(Dr)),Dr.exports}var H=R.commonjsGlobal.process;const Ce=function(t){return t&&typeof t=="object"&&typeof t.removeListener=="function"&&typeof t.emit=="function"&&typeof t.reallyExit=="function"&&typeof t.listeners=="function"&&typeof t.kill=="function"&&typeof t.pid=="number"&&typeof t.on=="function"};if(!Ce(H))He.exports=function(){return function(){}};else{var Pi=Zt,Ye=Oi(),Ri=/^win/i.test(H.platform),xt=Re;typeof xt!="function"&&(xt=xt.EventEmitter);var Q;H.__signal_exit_emitter__?Q=H.__signal_exit_emitter__:(Q=H.__signal_exit_emitter__=new xt,Q.count=0,Q.emitted={}),Q.infinite||(Q.setMaxListeners(1/0),Q.infinite=!0),He.exports=function(t,e){if(!Ce(R.commonjsGlobal.process))return function(){};Pi.equal(typeof t,"function","a callback must be provided for exit handler"),Qe===!1&&mn();var r="exit";e&&e.alwaysLast&&(r="afterexit");var n=function(){Q.removeListener(r,t),Q.listeners("exit").length===0&&Q.listeners("afterexit").length===0&&lr()};return Q.on(r,t),n};var lr=function(){!Qe||!Ce(R.commonjsGlobal.process)||(Qe=!1,Ye.forEach(function(e){try{H.removeListener(e,fr[e])}catch{}}),H.emit=hr,H.reallyExit=yn,Q.count-=1)};He.exports.unload=lr;var ke=function(e,r,n){Q.emitted[e]||(Q.emitted[e]=!0,Q.emit(e,r,n))},fr={};Ye.forEach(function(t){fr[t]=function(){if(Ce(R.commonjsGlobal.process)){var r=H.listeners(t);r.length===Q.count&&(lr(),ke("exit",null,t),ke("afterexit",null,t),Ri&&t==="SIGHUP"&&(t="SIGINT"),H.kill(H.pid,t))}}}),He.exports.signals=function(){return Ye};var Qe=!1,mn=function(){Qe||!Ce(R.commonjsGlobal.process)||(Qe=!0,Q.count+=1,Ye=Ye.filter(function(e){try{return H.on(e,fr[e]),!0}catch{return!1}}),H.emit=$i,H.reallyExit=Ti)};He.exports.load=mn;var yn=H.reallyExit,Ti=function(e){Ce(R.commonjsGlobal.process)&&(H.exitCode=e||0,ke("exit",H.exitCode,null),ke("afterexit",H.exitCode,null),yn.call(H,H.exitCode))},hr=H.emit,$i=function(e,r){if(e==="exit"&&Ce(R.commonjsGlobal.process)){r!==void 0&&(H.exitCode=r);var n=hr.apply(this,arguments);return ke("exit",H.exitCode,null),ke("afterexit",H.exitCode,null),n}else return hr.apply(this,arguments)}}var ji=He.exports,Gu=Wr;Wr.strict=Wu;Wr.loose=Vu;var ki=Object.prototype.toString,Li={"[object Int8Array]":!0,"[object Int16Array]":!0,"[object Int32Array]":!0,"[object Uint8Array]":!0,"[object Uint8ClampedArray]":!0,"[object Uint16Array]":!0,"[object Uint32Array]":!0,"[object Float32Array]":!0,"[object Float64Array]":!0};function Wr(t){return Wu(t)||Vu(t)}function Wu(t){return t instanceof Int8Array||t instanceof Int16Array||t instanceof Int32Array||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Uint16Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array}function Vu(t){return Li[ki.call(t)]}var Ni=Gu.strict,Ii=function(e){if(Ni(e)){var r=Buffer.from(e.buffer);return e.byteLength!==e.buffer.byteLength&&(r=r.slice(e.byteOffset,e.byteOffset+e.byteLength)),r}else return Buffer.from(e)};gt.exports=Wi;gt.exports.sync=Vi;gt.exports._getTmpname=Vr;gt.exports._cleanupOnExit=Jr;const z=he,Ui=R.imurmurhashExports,Ju=ji,qi=J,Ku=Gu,Yu=Ii,{promisify:ce}=te,ge={},Hi=function(){try{return require("worker_threads").threadId}catch{return 0}}();let Mi=0;function Vr(t){return t+"."+Ui(__filename).hash(String(process.pid)).hash(String(Hi)).hash(String(++Mi)).result()}function Jr(t){return()=>{try{z.unlinkSync(typeof t=="function"?t():t)}catch{}}}function zi(t){return new Promise(e=>{ge[t]||(ge[t]=[]),ge[t].push(e),ge[t].length===1&&e()})}function Vt(t){return t.code==="ENOSYS"||(!process.getuid||process.getuid()!==0)&&(t.code==="EINVAL"||t.code==="EPERM")}async function Gi(t,e,r={}){typeof r=="string"&&(r={encoding:r});let n,s;const i=Ju(Jr(()=>s)),u=qi.resolve(t);try{await zi(u);const o=await ce(z.realpath)(t).catch(()=>t);if(s=Vr(o),!r.mode||!r.chown){const c=await ce(z.stat)(o).catch(()=>{});c&&(r.mode==null&&(r.mode=c.mode),r.chown==null&&process.getuid&&(r.chown={uid:c.uid,gid:c.gid}))}n=await ce(z.open)(s,"w",r.mode),r.tmpfileCreated&&await r.tmpfileCreated(s),Ku(e)&&(e=Yu(e)),Buffer.isBuffer(e)?await ce(z.write)(n,e,0,e.length,0):e!=null&&await ce(z.write)(n,String(e),0,String(r.encoding||"utf8")),r.fsync!==!1&&await ce(z.fsync)(n),await ce(z.close)(n),n=null,r.chown&&await ce(z.chown)(s,r.chown.uid,r.chown.gid).catch(c=>{if(!Vt(c))throw c}),r.mode&&await ce(z.chmod)(s,r.mode).catch(c=>{if(!Vt(c))throw c}),await ce(z.rename)(s,o)}finally{n&&await ce(z.close)(n).catch(()=>{}),i(),await ce(z.unlink)(s).catch(()=>{}),ge[u].shift(),ge[u].length>0?ge[u][0]():delete ge[u]}}function Wi(t,e,r,n){r instanceof Function&&(n=r,r={});const s=Gi(t,e,r);return n&&s.then(n,n),s}function Vi(t,e,r){typeof r=="string"?r={encoding:r}:r||(r={});try{t=z.realpathSync(t)}catch{}const n=Vr(t);if(!r.mode||!r.chown)try{const c=z.statSync(t);r=Object.assign({},r),r.mode||(r.mode=c.mode),!r.chown&&process.getuid&&(r.chown={uid:c.uid,gid:c.gid})}catch{}let s;const i=Jr(n),u=Ju(i);let o=!0;try{if(s=z.openSync(n,"w",r.mode||438),r.tmpfileCreated&&r.tmpfileCreated(n),Ku(e)&&(e=Yu(e)),Buffer.isBuffer(e)?z.writeSync(s,e,0,e.length,0):e!=null&&z.writeSync(s,String(e),0,String(r.encoding||"utf8")),r.fsync!==!1&&z.fsyncSync(s),z.closeSync(s),s=null,r.chown)try{z.chownSync(n,r.chown.uid,r.chown.gid)}catch(c){if(!Vt(c))throw c}if(r.mode)try{z.chmodSync(n,r.mode)}catch(c){if(!Vt(c))throw c}z.renameSync(n,t),o=!1}finally{if(s)try{z.closeSync(s)}catch{}u(),o&&i()}}var Ji=gt.exports;const Fn=R.getDefaultExportFromCjs(Ji);var Ki=t=>{const e=typeof t;return t!==null&&(e==="object"||e==="function")};const be=Ki,Yi=new Set(["__proto__","prototype","constructor"]),Qi=t=>!t.some(e=>Yi.has(e));function At(t){const e=t.split("."),r=[];for(let n=0;n<e.length;n++){let s=e[n];for(;s[s.length-1]==="\\"&&e[n+1]!==void 0;)s=s.slice(0,-1)+".",s+=e[++n];r.push(s)}return Qi(r)?r:[]}var Xi={get(t,e,r){if(!be(t)||typeof e!="string")return r===void 0?t:r;const n=At(e);if(n.length!==0){for(let s=0;s<n.length;s++)if(t=t[n[s]],t==null){if(s!==n.length-1)return r;break}return t===void 0?r:t}},set(t,e,r){if(!be(t)||typeof e!="string")return t;const n=t,s=At(e);for(let i=0;i<s.length;i++){const u=s[i];be(t[u])||(t[u]={}),i===s.length-1&&(t[u]=r),t=t[u]}return n},delete(t,e){if(!be(t)||typeof e!="string")return!1;const r=At(e);for(let n=0;n<r.length;n++){const s=r[n];if(n===r.length-1)return delete t[s],!0;if(t=t[s],!be(t))return!1}},has(t,e){if(!be(t)||typeof e!="string")return!1;const r=At(e);if(r.length===0)return!1;for(let n=0;n<r.length;n++)if(be(t)){if(!(r[n]in t))return!1;t=t[r[n]]}else return!1;return!0}};const Xe=R.getDefaultExportFromCjs(Xi),Qu=te.promisify(er.randomBytes),Zi="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._~".split(""),eo="0123456789".split(""),to="CDEHKMPRTUWXY012458".split(""),ro="!\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~".split(""),no="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),uo=(t,e)=>{const r=e.length,n=Math.floor(65536/r)*r-1,s=2*Math.ceil(1.1*t);let i="",u=0;for(;u<t;){const o=er.randomBytes(s);let c=0;for(;c<s&&u<t;){const l=o.readUInt16LE(c);c+=2,!(l>n)&&(i+=e[l%r],u++)}}return i},so=async(t,e)=>{const r=e.length,n=Math.floor(65536/r)*r-1,s=2*Math.ceil(1.1*t);let i="",u=0;for(;u<t;){const o=await Qu(s);let c=0;for(;c<s&&u<t;){const l=o.readUInt16LE(c);c+=2,!(l>n)&&(i+=e[l%r],u++)}}return i},io=(t,e,r)=>er.randomBytes(t).toString(e).slice(0,r),oo=async(t,e,r)=>(await Qu(t)).toString(e).slice(0,r),ao=new Set([void 0,"hex","base64","url-safe","numeric","distinguishable","ascii-printable","alphanumeric"]),Xu=(t,e)=>({length:r,type:n,characters:s})=>{if(!(r>=0&&Number.isFinite(r)))throw new TypeError("Expected a `length` to be a non-negative finite number");if(n!==void 0&&s!==void 0)throw new TypeError("Expected either `type` or `characters`");if(s!==void 0&&typeof s!="string")throw new TypeError("Expected `characters` to be string");if(!ao.has(n))throw new TypeError(`Unknown type: ${n}`);if(n===void 0&&s===void 0&&(n="hex"),n==="hex"||n===void 0&&s===void 0)return e(Math.ceil(r*.5),"hex",r);if(n==="base64")return e(Math.ceil(r*.75),"base64",r);if(n==="url-safe")return t(r,Zi);if(n==="numeric")return t(r,eo);if(n==="distinguishable")return t(r,to);if(n==="ascii-printable")return t(r,ro);if(n==="alphanumeric")return t(r,no);if(s.length===0)throw new TypeError("Expected `characters` string length to be greater than or equal to 1");if(s.length>65536)throw new TypeError("Expected `characters` string length to be less or equal to 65536");return t(r,s.split(""))},Zu=Xu(uo,io);Zu.async=Xu(so,oo);function co(){return Zu({length:32})}const Do=Wt||J.join(mt.tmpdir(),co()),gn="You don't have access to this file.",lo={mode:448,recursive:!0},En={mode:384};class fo{constructor(e,r,n={}){const s=n.globalConfigPath?J.join(e,"config.json"):J.join("configstore",`${e}.json`);this._path=n.configPath||J.join(Do,s),r&&(this.all={...r,...this.all})}get all(){try{return JSON.parse(hn.readFileSync(this._path,"utf8"))}catch(e){if(e.code==="ENOENT")return{};if(e.code==="EACCES"&&(e.message=`${e.message}
${gn}
`),e.name==="SyntaxError")return Fn.sync(this._path,"",En),{};throw e}}set all(e){try{hn.mkdirSync(J.dirname(this._path),lo),Fn.sync(this._path,JSON.stringify(e,void 0,"	"),En)}catch(r){throw r.code==="EACCES"&&(r.message=`${r.message}
${gn}
`),r}}get size(){return Object.keys(this.all||{}).length}get(e){return Xe.get(this.all,e)}set(e,r){const n=this.all;if(arguments.length===1)for(const s of Object.keys(e))Xe.set(n,s,e[s]);else Xe.set(n,e,r);this.all=n}has(e){return Xe.has(this.all,e)}delete(e){const r=this.all;Xe.delete(r,e),this.all=r}clear(){this.all={}}get path(){return this._path}}function ho(t,e){if(t=R.semver.parse(t),e=R.semver.parse(e),!(R.semver.compareBuild(t,e)>=0))return R.semver.diff(t,e)||"build"}const es=["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array","BigInt64Array","BigUint64Array"];function po(t){return es.includes(t)}const mo=["Function","Generator","AsyncGenerator","GeneratorFunction","AsyncGeneratorFunction","AsyncFunction","Observable","Array","Buffer","Blob","Object","RegExp","Date","Error","Map","Set","WeakMap","WeakSet","WeakRef","ArrayBuffer","SharedArrayBuffer","DataView","Promise","URL","FormData","URLSearchParams","HTMLElement","NaN",...es];function yo(t){return mo.includes(t)}const Fo=["null","undefined","string","number","bigint","boolean","symbol"];function go(t){return Fo.includes(t)}function Ve(t){return e=>typeof e===t}const{toString:Eo}=Object.prototype,Et=t=>{const e=Eo.call(t).slice(8,-1);if(/HTML\w+Element/.test(e)&&a.domElement(t))return"HTMLElement";if(yo(e))return e},U=t=>e=>Et(e)===t;function a(t){if(t===null)return"null";switch(typeof t){case"undefined":return"undefined";case"string":return"string";case"number":return Number.isNaN(t)?"NaN":"number";case"boolean":return"boolean";case"function":return"Function";case"bigint":return"bigint";case"symbol":return"symbol"}if(a.observable(t))return"Observable";if(a.array(t))return"Array";if(a.buffer(t))return"Buffer";const e=Et(t);if(e)return e;if(t instanceof String||t instanceof Boolean||t instanceof Number)throw new TypeError("Please don't use object wrappers for primitive types");return"Object"}a.undefined=Ve("undefined");a.string=Ve("string");const Co=Ve("number");a.number=t=>Co(t)&&!a.nan(t);a.positiveNumber=t=>a.number(t)&&t>0;a.negativeNumber=t=>a.number(t)&&t<0;a.bigint=Ve("bigint");a.function_=Ve("function");a.null_=t=>t===null;a.class_=t=>a.function_(t)&&t.toString().startsWith("class ");a.boolean=t=>t===!0||t===!1;a.symbol=Ve("symbol");a.numericString=t=>a.string(t)&&!a.emptyStringOrWhitespace(t)&&!Number.isNaN(Number(t));a.array=(t,e)=>Array.isArray(t)?a.function_(e)?t.every(r=>e(r)):!0:!1;a.buffer=t=>t?.constructor?.isBuffer?.(t)??!1;a.blob=t=>U("Blob")(t);a.nullOrUndefined=t=>a.null_(t)||a.undefined(t);a.object=t=>!a.null_(t)&&(typeof t=="object"||a.function_(t));a.iterable=t=>a.function_(t?.[Symbol.iterator]);a.asyncIterable=t=>a.function_(t?.[Symbol.asyncIterator]);a.generator=t=>a.iterable(t)&&a.function_(t?.next)&&a.function_(t?.throw);a.asyncGenerator=t=>a.asyncIterable(t)&&a.function_(t.next)&&a.function_(t.throw);a.nativePromise=t=>U("Promise")(t);const bo=t=>a.function_(t?.then)&&a.function_(t?.catch);a.promise=t=>a.nativePromise(t)||bo(t);a.generatorFunction=U("GeneratorFunction");a.asyncGeneratorFunction=t=>Et(t)==="AsyncGeneratorFunction";a.asyncFunction=t=>Et(t)==="AsyncFunction";a.boundFunction=t=>a.function_(t)&&!t.hasOwnProperty("prototype");a.regExp=U("RegExp");a.date=U("Date");a.error=U("Error");a.map=t=>U("Map")(t);a.set=t=>U("Set")(t);a.weakMap=t=>U("WeakMap")(t);a.weakSet=t=>U("WeakSet")(t);a.weakRef=t=>U("WeakRef")(t);a.int8Array=U("Int8Array");a.uint8Array=U("Uint8Array");a.uint8ClampedArray=U("Uint8ClampedArray");a.int16Array=U("Int16Array");a.uint16Array=U("Uint16Array");a.int32Array=U("Int32Array");a.uint32Array=U("Uint32Array");a.float32Array=U("Float32Array");a.float64Array=U("Float64Array");a.bigInt64Array=U("BigInt64Array");a.bigUint64Array=U("BigUint64Array");a.arrayBuffer=U("ArrayBuffer");a.sharedArrayBuffer=U("SharedArrayBuffer");a.dataView=U("DataView");a.enumCase=(t,e)=>Object.values(e).includes(t);a.directInstanceOf=(t,e)=>Object.getPrototypeOf(t)===e.prototype;a.urlInstance=t=>U("URL")(t);a.urlString=t=>{if(!a.string(t))return!1;try{return new URL(t),!0}catch{return!1}};a.truthy=t=>!!t;a.falsy=t=>!t;a.nan=t=>Number.isNaN(t);a.primitive=t=>a.null_(t)||go(typeof t);a.integer=t=>Number.isInteger(t);a.safeInteger=t=>Number.isSafeInteger(t);a.plainObject=t=>{if(typeof t!="object"||t===null)return!1;const e=Object.getPrototypeOf(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Symbol.toStringTag in t)&&!(Symbol.iterator in t)};a.typedArray=t=>po(Et(t));const wo=t=>a.safeInteger(t)&&t>=0;a.arrayLike=t=>!a.nullOrUndefined(t)&&!a.function_(t)&&wo(t.length);a.tupleLike=(t,e)=>a.array(e)&&a.array(t)&&e.length===t.length?e.every((r,n)=>r(t[n])):!1;a.inRange=(t,e)=>{if(a.number(e))return t>=Math.min(0,e)&&t<=Math.max(e,0);if(a.array(e)&&e.length===2)return t>=Math.min(...e)&&t<=Math.max(...e);throw new TypeError(`Invalid range: ${JSON.stringify(e)}`)};const _o=1,xo=["innerHTML","ownerDocument","style","attributes","nodeValue"];a.domElement=t=>a.object(t)&&t.nodeType===_o&&a.string(t.nodeName)&&!a.plainObject(t)&&xo.every(e=>e in t);a.observable=t=>t?t===t[Symbol.observable]?.()||t===t["@@observable"]?.():!1;a.nodeStream=t=>a.object(t)&&a.function_(t.pipe)&&!a.observable(t);a.infinite=t=>t===Number.POSITIVE_INFINITY||t===Number.NEGATIVE_INFINITY;const ts=t=>e=>a.integer(e)&&Math.abs(e%2)===t;a.evenInteger=ts(0);a.oddInteger=ts(1);a.emptyArray=t=>a.array(t)&&t.length===0;a.nonEmptyArray=t=>a.array(t)&&t.length>0;a.emptyString=t=>a.string(t)&&t.length===0;const Ao=t=>a.string(t)&&!/\S/.test(t);a.emptyStringOrWhitespace=t=>a.emptyString(t)||Ao(t);a.nonEmptyString=t=>a.string(t)&&t.length>0;a.nonEmptyStringAndNotWhitespace=t=>a.string(t)&&!a.emptyStringOrWhitespace(t);a.emptyObject=t=>a.object(t)&&!a.map(t)&&!a.set(t)&&Object.keys(t).length===0;a.nonEmptyObject=t=>a.object(t)&&!a.map(t)&&!a.set(t)&&Object.keys(t).length>0;a.emptySet=t=>a.set(t)&&t.size===0;a.nonEmptySet=t=>a.set(t)&&t.size>0;a.emptyMap=t=>a.map(t)&&t.size===0;a.nonEmptyMap=t=>a.map(t)&&t.size>0;a.propertyKey=t=>a.any([a.string,a.number,a.symbol],t);a.formData=t=>U("FormData")(t);a.urlSearchParams=t=>U("URLSearchParams")(t);const rs=(t,e,r)=>{if(!a.function_(e))throw new TypeError(`Invalid predicate: ${JSON.stringify(e)}`);if(r.length===0)throw new TypeError("Invalid number of values");return t.call(r,e)};a.any=(t,...e)=>(a.array(t)?t:[t]).some(n=>rs(Array.prototype.some,n,e));a.all=(t,...e)=>rs(Array.prototype.every,t,e);const w=(t,e,r,n={})=>{if(!t){const{multipleValues:s}=n,i=s?`received values of types ${[...new Set(r.map(u=>`\`${a(u)}\``))].join(", ")}`:`received value of type \`${a(r)}\``;throw new TypeError(`Expected value which is \`${e}\`, ${i}.`)}},E={undefined:t=>w(a.undefined(t),"undefined",t),string:t=>w(a.string(t),"string",t),number:t=>w(a.number(t),"number",t),positiveNumber:t=>w(a.positiveNumber(t),"positive number",t),negativeNumber:t=>w(a.negativeNumber(t),"negative number",t),bigint:t=>w(a.bigint(t),"bigint",t),function_:t=>w(a.function_(t),"Function",t),null_:t=>w(a.null_(t),"null",t),class_:t=>w(a.class_(t),"Class",t),boolean:t=>w(a.boolean(t),"boolean",t),symbol:t=>w(a.symbol(t),"symbol",t),numericString:t=>w(a.numericString(t),"string with a number",t),array:(t,e)=>{w(a.array(t),"Array",t),e&&t.forEach(e)},buffer:t=>w(a.buffer(t),"Buffer",t),blob:t=>w(a.blob(t),"Blob",t),nullOrUndefined:t=>w(a.nullOrUndefined(t),"null or undefined",t),object:t=>w(a.object(t),"Object",t),iterable:t=>w(a.iterable(t),"Iterable",t),asyncIterable:t=>w(a.asyncIterable(t),"AsyncIterable",t),generator:t=>w(a.generator(t),"Generator",t),asyncGenerator:t=>w(a.asyncGenerator(t),"AsyncGenerator",t),nativePromise:t=>w(a.nativePromise(t),"native Promise",t),promise:t=>w(a.promise(t),"Promise",t),generatorFunction:t=>w(a.generatorFunction(t),"GeneratorFunction",t),asyncGeneratorFunction:t=>w(a.asyncGeneratorFunction(t),"AsyncGeneratorFunction",t),asyncFunction:t=>w(a.asyncFunction(t),"AsyncFunction",t),boundFunction:t=>w(a.boundFunction(t),"Function",t),regExp:t=>w(a.regExp(t),"RegExp",t),date:t=>w(a.date(t),"Date",t),error:t=>w(a.error(t),"Error",t),map:t=>w(a.map(t),"Map",t),set:t=>w(a.set(t),"Set",t),weakMap:t=>w(a.weakMap(t),"WeakMap",t),weakSet:t=>w(a.weakSet(t),"WeakSet",t),weakRef:t=>w(a.weakRef(t),"WeakRef",t),int8Array:t=>w(a.int8Array(t),"Int8Array",t),uint8Array:t=>w(a.uint8Array(t),"Uint8Array",t),uint8ClampedArray:t=>w(a.uint8ClampedArray(t),"Uint8ClampedArray",t),int16Array:t=>w(a.int16Array(t),"Int16Array",t),uint16Array:t=>w(a.uint16Array(t),"Uint16Array",t),int32Array:t=>w(a.int32Array(t),"Int32Array",t),uint32Array:t=>w(a.uint32Array(t),"Uint32Array",t),float32Array:t=>w(a.float32Array(t),"Float32Array",t),float64Array:t=>w(a.float64Array(t),"Float64Array",t),bigInt64Array:t=>w(a.bigInt64Array(t),"BigInt64Array",t),bigUint64Array:t=>w(a.bigUint64Array(t),"BigUint64Array",t),arrayBuffer:t=>w(a.arrayBuffer(t),"ArrayBuffer",t),sharedArrayBuffer:t=>w(a.sharedArrayBuffer(t),"SharedArrayBuffer",t),dataView:t=>w(a.dataView(t),"DataView",t),enumCase:(t,e)=>w(a.enumCase(t,e),"EnumCase",t),urlInstance:t=>w(a.urlInstance(t),"URL",t),urlString:t=>w(a.urlString(t),"string with a URL",t),truthy:t=>w(a.truthy(t),"truthy",t),falsy:t=>w(a.falsy(t),"falsy",t),nan:t=>w(a.nan(t),"NaN",t),primitive:t=>w(a.primitive(t),"primitive",t),integer:t=>w(a.integer(t),"integer",t),safeInteger:t=>w(a.safeInteger(t),"integer",t),plainObject:t=>w(a.plainObject(t),"plain object",t),typedArray:t=>w(a.typedArray(t),"TypedArray",t),arrayLike:t=>w(a.arrayLike(t),"array-like",t),tupleLike:(t,e)=>w(a.tupleLike(t,e),"tuple-like",t),domElement:t=>w(a.domElement(t),"HTMLElement",t),observable:t=>w(a.observable(t),"Observable",t),nodeStream:t=>w(a.nodeStream(t),"Node.js Stream",t),infinite:t=>w(a.infinite(t),"infinite number",t),emptyArray:t=>w(a.emptyArray(t),"empty array",t),nonEmptyArray:t=>w(a.nonEmptyArray(t),"non-empty array",t),emptyString:t=>w(a.emptyString(t),"empty string",t),emptyStringOrWhitespace:t=>w(a.emptyStringOrWhitespace(t),"empty string or whitespace",t),nonEmptyString:t=>w(a.nonEmptyString(t),"non-empty string",t),nonEmptyStringAndNotWhitespace:t=>w(a.nonEmptyStringAndNotWhitespace(t),"non-empty string and not whitespace",t),emptyObject:t=>w(a.emptyObject(t),"empty object",t),nonEmptyObject:t=>w(a.nonEmptyObject(t),"non-empty object",t),emptySet:t=>w(a.emptySet(t),"empty set",t),nonEmptySet:t=>w(a.nonEmptySet(t),"non-empty set",t),emptyMap:t=>w(a.emptyMap(t),"empty map",t),nonEmptyMap:t=>w(a.nonEmptyMap(t),"non-empty map",t),propertyKey:t=>w(a.propertyKey(t),"PropertyKey",t),formData:t=>w(a.formData(t),"FormData",t),urlSearchParams:t=>w(a.urlSearchParams(t),"URLSearchParams",t),evenInteger:t=>w(a.evenInteger(t),"even integer",t),oddInteger:t=>w(a.oddInteger(t),"odd integer",t),directInstanceOf:(t,e)=>w(a.directInstanceOf(t,e),"T",t),inRange:(t,e)=>w(a.inRange(t,e),"in range",t),any:(t,...e)=>w(a.any(t,...e),"predicate returns truthy for any value",e,{multipleValues:!0}),all:(t,...e)=>w(a.all(t,...e),"predicate returns truthy for all values",e,{multipleValues:!0})};Object.defineProperties(a,{class:{value:a.class_},function:{value:a.function_},null:{value:a.null_}});Object.defineProperties(E,{class:{value:E.class_},function:{value:E.function_},null:{value:E.null_}});let Bo=class extends Error{constructor(e){super(e||"Promise was canceled"),this.name="CancelError"}get isCanceled(){return!0}};class tr{static fn(e){return(...r)=>new tr((n,s,i)=>{r.push(i),e(...r).then(n,s)})}constructor(e){this._cancelHandlers=[],this._isPending=!0,this._isCanceled=!1,this._rejectOnCancel=!0,this._promise=new Promise((r,n)=>{this._reject=n;const s=o=>{(!this._isCanceled||!u.shouldReject)&&(this._isPending=!1,r(o))},i=o=>{this._isPending=!1,n(o)},u=o=>{if(!this._isPending)throw new Error("The `onCancel` handler was attached after the promise settled.");this._cancelHandlers.push(o)};Object.defineProperties(u,{shouldReject:{get:()=>this._rejectOnCancel,set:o=>{this._rejectOnCancel=o}}}),e(s,i,u)})}then(e,r){return this._promise.then(e,r)}catch(e){return this._promise.catch(e)}finally(e){return this._promise.finally(e)}cancel(e){if(!(!this._isPending||this._isCanceled)){if(this._isCanceled=!0,this._cancelHandlers.length>0)try{for(const r of this._cancelHandlers)r()}catch(r){this._reject(r);return}this._rejectOnCancel&&this._reject(new Bo(e))}}get isCanceled(){return this._isCanceled}}Object.setPrototypeOf(tr.prototype,Promise.prototype);function So(t){return a.object(t)&&"_onResponse"in t}let Y=class extends Error{constructor(e,r,n){if(super(e),Object.defineProperty(this,"input",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"code",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"stack",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"response",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"request",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"timings",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Error.captureStackTrace(this,this.constructor),this.name="RequestError",this.code=r.code??"ERR_GOT_REQUEST_ERROR",this.input=r.input,So(n)?(Object.defineProperty(this,"request",{enumerable:!1,value:n}),Object.defineProperty(this,"response",{enumerable:!1,value:n.response}),this.options=n.options):this.options=n,this.timings=this.request?.timings,a.string(r.stack)&&a.string(this.stack)){const s=this.stack.indexOf(this.message)+this.message.length,i=this.stack.slice(s).split(`
`).reverse(),u=r.stack.slice(r.stack.indexOf(r.message)+r.message.length).split(`
`).reverse();for(;u.length>0&&u[0]===i[0];)i.shift();this.stack=`${this.stack.slice(0,s)}${i.reverse().join(`
`)}${u.reverse().join(`
`)}`}}};class vo extends Y{constructor(e){super(`Redirected ${e.options.maxRedirects} times. Aborting.`,{},e),this.name="MaxRedirectsError",this.code="ERR_TOO_MANY_REDIRECTS"}}class Jt extends Y{constructor(e){super(`Response code ${e.statusCode} (${e.statusMessage})`,{},e.request),this.name="HTTPError",this.code="ERR_NON_2XX_3XX_RESPONSE"}}let Oo=class extends Y{constructor(e,r){super(e.message,e,r),this.name="CacheError",this.code=this.code==="ERR_GOT_REQUEST_ERROR"?"ERR_CACHE_ACCESS":this.code}};class Cn extends Y{constructor(e,r){super(e.message,e,r),this.name="UploadError",this.code=this.code==="ERR_GOT_REQUEST_ERROR"?"ERR_UPLOAD":this.code}}let Po=class extends Y{constructor(e,r,n){super(e.message,e,n),Object.defineProperty(this,"timings",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"event",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name="TimeoutError",this.event=e.event,this.timings=r}};class bn extends Y{constructor(e,r){super(e.message,e,r),this.name="ReadError",this.code=this.code==="ERR_GOT_REQUEST_ERROR"?"ERR_READING_RESPONSE_STREAM":this.code}}class Ro extends Y{constructor(e){super("Retrying",{},e),this.name="RetryError",this.code="ERR_RETRYING"}}class To extends Y{constructor(e){super("This operation was aborted.",{},e),this.code="ERR_ABORTED",this.name="AbortError"}}var Lr={exports:{}};(function(t,e){Object.defineProperty(e,"__esModule",{value:!0});function r(s){return s.encrypted}const n=(s,i)=>{let u;typeof i=="function"?u={connect:i}:u=i;const o=typeof u.connect=="function",c=typeof u.secureConnect=="function",l=typeof u.close=="function",D=()=>{o&&u.connect(),r(s)&&c&&(s.authorized?u.secureConnect():s.authorizationError||s.once("secureConnect",u.secureConnect)),l&&s.once("close",u.close)};s.writable&&!s.connecting?D():s.connecting?s.once("connect",D):s.destroyed&&l&&u.close(s._hadError)};e.default=n,t.exports=n,t.exports.default=n})(Lr,Lr.exports);var $o=Lr.exports;const jo=R.getDefaultExportFromCjs($o),ko=t=>{if(t.timings)return t.timings;const e={start:Date.now(),socket:void 0,lookup:void 0,connect:void 0,secureConnect:void 0,upload:void 0,response:void 0,end:void 0,error:void 0,abort:void 0,phases:{wait:void 0,dns:void 0,tcp:void 0,tls:void 0,request:void 0,firstByte:void 0,download:void 0,total:void 0}};t.timings=e;const r=u=>{u.once(Re.errorMonitor,()=>{e.error=Date.now(),e.phases.total=e.error-e.start})};r(t);const n=()=>{e.abort=Date.now(),e.phases.total=e.abort-e.start};t.prependOnceListener("abort",n);const s=u=>{if(e.socket=Date.now(),e.phases.wait=e.socket-e.start,te.types.isProxy(u))return;const o=()=>{e.lookup=Date.now(),e.phases.dns=e.lookup-e.socket};u.prependOnceListener("lookup",o),jo(u,{connect:()=>{e.connect=Date.now(),e.lookup===void 0&&(u.removeListener("lookup",o),e.lookup=e.connect,e.phases.dns=e.lookup-e.socket),e.phases.tcp=e.connect-e.lookup},secureConnect:()=>{e.secureConnect=Date.now(),e.phases.tls=e.secureConnect-e.connect}})};t.socket?s(t.socket):t.prependOnceListener("socket",s);const i=()=>{e.upload=Date.now(),e.phases.request=e.upload-(e.secureConnect??e.connect)};return t.writableFinished?i():t.prependOnceListener("finish",i),t.prependOnceListener("response",u=>{e.response=Date.now(),e.phases.firstByte=e.response-e.upload,u.timings=e,r(u),u.prependOnceListener("end",()=>{t.off("abort",n),u.off("aborted",n),!e.phases.total&&(e.end=Date.now(),e.phases.download=e.end-e.response,e.phases.total=e.end-e.start)}),u.prependOnceListener("aborted",n)}),e},Lo="text/plain",No="us-ascii",dr=(t,e)=>e.some(r=>r instanceof RegExp?r.test(t):r===t),Io=new Set(["https:","http:","file:"]),Uo=t=>{try{const{protocol:e}=new URL(t);return e.endsWith(":")&&!e.includes(".")&&!Io.has(e)}catch{return!1}},qo=(t,{stripHash:e})=>{const r=/^data:(?<type>[^,]*?),(?<data>[^#]*?)(?:#(?<hash>.*))?$/.exec(t);if(!r)throw new Error(`Invalid URL: ${t}`);let{type:n,data:s,hash:i}=r.groups;const u=n.split(";");i=e?"":i;let o=!1;u[u.length-1]==="base64"&&(u.pop(),o=!0);const c=u.shift()?.toLowerCase()??"",D=[...u.map(d=>{let[h,p=""]=d.split("=").map(m=>m.trim());return h==="charset"&&(p=p.toLowerCase(),p===No)?"":`${h}${p?`=${p}`:""}`}).filter(Boolean)];return o&&D.push("base64"),(D.length>0||c&&c!==Lo)&&D.unshift(c),`data:${D.join(";")},${o?s.trim():s}${i?`#${i}`:""}`};function Ho(t,e){if(e={defaultProtocol:"http",normalizeProtocol:!0,forceHttp:!1,forceHttps:!1,stripAuthentication:!0,stripHash:!1,stripTextFragment:!0,stripWWW:!0,removeQueryParameters:[/^utm_\w+/i],removeTrailingSlash:!0,removeSingleSlash:!0,removeDirectoryIndex:!1,removeExplicitPort:!1,sortQueryParameters:!0,...e},typeof e.defaultProtocol=="string"&&!e.defaultProtocol.endsWith(":")&&(e.defaultProtocol=`${e.defaultProtocol}:`),t=t.trim(),/^data:/i.test(t))return qo(t,e);if(Uo(t))return t;const r=t.startsWith("//");!r&&/^\.*\//.test(t)||(t=t.replace(/^(?!(?:\w+:)?\/\/)|^\/\//,e.defaultProtocol));const s=new URL(t);if(e.forceHttp&&e.forceHttps)throw new Error("The `forceHttp` and `forceHttps` options cannot be used together");if(e.forceHttp&&s.protocol==="https:"&&(s.protocol="http:"),e.forceHttps&&s.protocol==="http:"&&(s.protocol="https:"),e.stripAuthentication&&(s.username="",s.password=""),e.stripHash?s.hash="":e.stripTextFragment&&(s.hash=s.hash.replace(/#?:~:text.*?$/i,"")),s.pathname){const u=/\b[a-z][a-z\d+\-.]{1,50}:\/\//g;let o=0,c="";for(;;){const D=u.exec(s.pathname);if(!D)break;const d=D[0],h=D.index,p=s.pathname.slice(o,h);c+=p.replace(/\/{2,}/g,"/"),c+=d,o=h+d.length}const l=s.pathname.slice(o,s.pathname.length);c+=l.replace(/\/{2,}/g,"/"),s.pathname=c}if(s.pathname)try{s.pathname=decodeURI(s.pathname)}catch{}if(e.removeDirectoryIndex===!0&&(e.removeDirectoryIndex=[/^index\.[a-z]+$/]),Array.isArray(e.removeDirectoryIndex)&&e.removeDirectoryIndex.length>0){let u=s.pathname.split("/");const o=u[u.length-1];dr(o,e.removeDirectoryIndex)&&(u=u.slice(0,-1),s.pathname=u.slice(1).join("/")+"/")}if(s.hostname&&(s.hostname=s.hostname.replace(/\.$/,""),e.stripWWW&&/^www\.(?!www\.)[a-z\-\d]{1,63}\.[a-z.\-\d]{2,63}$/.test(s.hostname)&&(s.hostname=s.hostname.replace(/^www\./,""))),Array.isArray(e.removeQueryParameters))for(const u of[...s.searchParams.keys()])dr(u,e.removeQueryParameters)&&s.searchParams.delete(u);if(!Array.isArray(e.keepQueryParameters)&&e.removeQueryParameters===!0&&(s.search=""),Array.isArray(e.keepQueryParameters)&&e.keepQueryParameters.length>0)for(const u of[...s.searchParams.keys()])dr(u,e.keepQueryParameters)||s.searchParams.delete(u);if(e.sortQueryParameters){s.searchParams.sort();try{s.search=decodeURIComponent(s.search)}catch{}}e.removeTrailingSlash&&(s.pathname=s.pathname.replace(/\/$/,"")),e.removeExplicitPort&&s.port&&(s.port="");const i=t;return t=s.toString(),!e.removeSingleSlash&&s.pathname==="/"&&!i.endsWith("/")&&s.hash===""&&(t=t.replace(/\/$/,"")),(e.removeTrailingSlash||s.pathname==="/")&&s.hash===""&&e.removeSingleSlash&&(t=t.replace(/\/$/,"")),r&&!e.normalizeProtocol&&(t=t.replace(/^http:\/\//,"//")),e.stripProtocol&&(t=t.replace(/^(?:https?:)?\/\//,"")),t}var Ct={exports:{}};const{PassThrough:Mo}=ae;var zo=t=>{t={...t};const{array:e}=t;let{encoding:r}=t;const n=r==="buffer";let s=!1;e?s=!(r||n):r=r||"utf8",n&&(r=null);const i=new Mo({objectMode:s});r&&i.setEncoding(r);let u=0;const o=[];return i.on("data",c=>{o.push(c),s?u=o.length:u+=c.length}),i.getBufferedValue=()=>e?o:n?Buffer.concat(o,u):o.join(""),i.getBufferedLength=()=>u,i};const{constants:Go}=ht,Wo=ae,{promisify:Vo}=te,Jo=zo,Ko=Vo(Wo.pipeline);class ns extends Error{constructor(){super("maxBuffer exceeded"),this.name="MaxBufferError"}}async function Kr(t,e){if(!t)throw new Error("Expected a stream");e={maxBuffer:1/0,...e};const{maxBuffer:r}=e,n=Jo(e);return await new Promise((s,i)=>{const u=o=>{o&&n.getBufferedLength()<=Go.MAX_LENGTH&&(o.bufferedData=n.getBufferedValue()),i(o)};(async()=>{try{await Ko(t,n),s()}catch(o){u(o)}})(),n.on("data",()=>{n.getBufferedLength()>r&&u(new ns)})}),n.getBufferedValue()}Ct.exports=Kr;Ct.exports.buffer=(t,e)=>Kr(t,{...e,encoding:"buffer"});Ct.exports.array=(t,e)=>Kr(t,{...e,array:!0});Ct.exports.MaxBufferError=ns;var Yo=Ct.exports;const us=R.getDefaultExportFromCjs(Yo);function Nr(t){return Object.fromEntries(Object.entries(t).map(([e,r])=>[e.toLowerCase(),r]))}class wn extends ae.Readable{statusCode;headers;body;url;constructor({statusCode:e,headers:r,body:n,url:s}){if(typeof e!="number")throw new TypeError("Argument `statusCode` should be a number");if(typeof r!="object")throw new TypeError("Argument `headers` should be an object");if(!(n instanceof Uint8Array))throw new TypeError("Argument `body` should be a buffer");if(typeof s!="string")throw new TypeError("Argument `url` should be a string");super({read(){this.push(n),this.push(null)}}),this.statusCode=e,this.headers=Nr(r),this.body=n,this.url=s}}var Yr={};Yr.stringify=function t(e){if(typeof e>"u")return e;if(e&&Buffer.isBuffer(e))return JSON.stringify(":base64:"+e.toString("base64"));if(e&&e.toJSON&&(e=e.toJSON()),e&&typeof e=="object"){var r="",n=Array.isArray(e);r=n?"[":"{";var s=!0;for(var i in e){var u=typeof e[i]=="function"||!n&&typeof e[i]>"u";Object.hasOwnProperty.call(e,i)&&!u&&(s||(r+=","),s=!1,n?e[i]==null?r+="null":r+=t(e[i]):e[i]!==void 0&&(r+=t(i)+":"+t(e[i])))}return r+=n?"]":"}",r}else return typeof e=="string"?JSON.stringify(/^:/.test(e)?":"+e:e):typeof e>"u"?"null":JSON.stringify(e)};Yr.parse=function(t){return JSON.parse(t,function(e,r){return typeof r=="string"?/^:base64:/.test(r)?Buffer.from(r.substring(8),"base64"):/^:/.test(r)?r.substring(1):r:r})};const Qo=Re,_n=Yr,Xo=t=>{const e={redis:"@keyv/redis",rediss:"@keyv/redis",mongodb:"@keyv/mongo",mongo:"@keyv/mongo",sqlite:"@keyv/sqlite",postgresql:"@keyv/postgres",postgres:"@keyv/postgres",mysql:"@keyv/mysql",etcd:"@keyv/etcd",offline:"@keyv/offline",tiered:"@keyv/tiered"};if(t.adapter||t.uri){const r=t.adapter||/^[^:+]*/.exec(t.uri)[0];return new(R.commonjsRequire(e[r]))(t)}return new Map},xn=["sqlite","postgres","mysql","mongo","redis","tiered"];class Zo extends Qo{constructor(e,{emitErrors:r=!0,...n}={}){if(super(),this.opts={namespace:"keyv",serialize:_n.stringify,deserialize:_n.parse,...typeof e=="string"?{uri:e}:e,...n},!this.opts.store){const i={...this.opts};this.opts.store=Xo(i)}if(this.opts.compression){const i=this.opts.compression;this.opts.serialize=i.serialize.bind(i),this.opts.deserialize=i.deserialize.bind(i)}typeof this.opts.store.on=="function"&&r&&this.opts.store.on("error",i=>this.emit("error",i)),this.opts.store.namespace=this.opts.namespace;const s=i=>async function*(){for await(const[u,o]of typeof i=="function"?i(this.opts.store.namespace):i){const c=await this.opts.deserialize(o);if(!(this.opts.store.namespace&&!u.includes(this.opts.store.namespace))){if(typeof c.expires=="number"&&Date.now()>c.expires){this.delete(u);continue}yield[this._getKeyUnprefix(u),c.value]}}};typeof this.opts.store[Symbol.iterator]=="function"&&this.opts.store instanceof Map?this.iterator=s(this.opts.store):typeof this.opts.store.iterator=="function"&&this.opts.store.opts&&this._checkIterableAdaptar()&&(this.iterator=s(this.opts.store.iterator.bind(this.opts.store)))}_checkIterableAdaptar(){return xn.includes(this.opts.store.opts.dialect)||xn.findIndex(e=>this.opts.store.opts.url.includes(e))>=0}_getKeyPrefix(e){return`${this.opts.namespace}:${e}`}_getKeyPrefixArray(e){return e.map(r=>`${this.opts.namespace}:${r}`)}_getKeyUnprefix(e){return e.split(":").splice(1).join(":")}get(e,r){const{store:n}=this.opts,s=Array.isArray(e),i=s?this._getKeyPrefixArray(e):this._getKeyPrefix(e);if(s&&n.getMany===void 0){const u=[];for(const o of i)u.push(Promise.resolve().then(()=>n.get(o)).then(c=>typeof c=="string"?this.opts.deserialize(c):this.opts.compression?this.opts.deserialize(c):c).then(c=>{if(c!=null)return typeof c.expires=="number"&&Date.now()>c.expires?this.delete(o).then(()=>{}):r&&r.raw?c:c.value}));return Promise.allSettled(u).then(o=>{const c=[];for(const l of o)c.push(l.value);return c})}return Promise.resolve().then(()=>s?n.getMany(i):n.get(i)).then(u=>typeof u=="string"?this.opts.deserialize(u):this.opts.compression?this.opts.deserialize(u):u).then(u=>{if(u!=null)return s?u.map((o,c)=>{if(typeof o=="string"&&(o=this.opts.deserialize(o)),o!=null){if(typeof o.expires=="number"&&Date.now()>o.expires){this.delete(e[c]).then(()=>{});return}return r&&r.raw?o:o.value}}):typeof u.expires=="number"&&Date.now()>u.expires?this.delete(e).then(()=>{}):r&&r.raw?u:u.value})}set(e,r,n){const s=this._getKeyPrefix(e);typeof n>"u"&&(n=this.opts.ttl),n===0&&(n=void 0);const{store:i}=this.opts;return Promise.resolve().then(()=>{const u=typeof n=="number"?Date.now()+n:null;return typeof r=="symbol"&&this.emit("error","symbol cannot be serialized"),r={value:r,expires:u},this.opts.serialize(r)}).then(u=>i.set(s,u,n)).then(()=>!0)}delete(e){const{store:r}=this.opts;if(Array.isArray(e)){const s=this._getKeyPrefixArray(e);if(r.deleteMany===void 0){const i=[];for(const u of s)i.push(r.delete(u));return Promise.allSettled(i).then(u=>u.every(o=>o.value===!0))}return Promise.resolve().then(()=>r.deleteMany(s))}const n=this._getKeyPrefix(e);return Promise.resolve().then(()=>r.delete(n))}clear(){const{store:e}=this.opts;return Promise.resolve().then(()=>e.clear())}has(e){const r=this._getKeyPrefix(e),{store:n}=this.opts;return Promise.resolve().then(async()=>typeof n.has=="function"?n.has(r):await n.get(r)!==void 0)}disconnect(){const{store:e}=this.opts;if(typeof e.disconnect=="function")return e.disconnect()}}var ea=Zo;const Bt=R.getDefaultExportFromCjs(ea),ta=["aborted","complete","headers","httpVersion","httpVersionMinor","httpVersionMajor","method","rawHeaders","rawTrailers","setTimeout","socket","statusCode","statusMessage","trailers","url"];function ra(t,e){if(e._readableState.autoDestroy)throw new Error("The second stream must have the `autoDestroy` option set to `false`");const r=new Set([...Object.keys(t),...ta]),n={};for(const s of r)s in e||(n[s]={get(){const i=t[s];return typeof i=="function"?i.bind(t):i},set(i){t[s]=i},enumerable:!0,configurable:!1});return Object.defineProperties(e,n),t.once("aborted",()=>{e.destroy(),e.emit("aborted")}),t.once("close",()=>{t.complete&&e.readable?e.once("end",()=>{e.emit("close")}):e.emit("close")}),e}class na extends Error{constructor(e){super(e.message),Object.assign(this,e)}}class Dt extends Error{constructor(e){super(e.message),Object.assign(this,e)}}class ua{constructor(e,r){this.hooks=new Map,this.request=()=>(n,s)=>{let i;if(typeof n=="string")i=pr(K.parse(n)),n={};else if(n instanceof K.URL)i=pr(K.parse(n.toString())),n={};else{const[h,...p]=(n.path??"").split("?"),m=p.length>0?`?${p.join("?")}`:"";i=pr({...n,pathname:h,search:m})}n={headers:{},method:"GET",cache:!0,strictTtl:!1,automaticFailover:!1,...n,...oa(i)},n.headers=Object.fromEntries(sa(n.headers).map(([h,p])=>[h.toLowerCase(),p]));const u=new Re,o=Ho(K.format(i),{stripWWW:!1,removeTrailingSlash:!1,stripAuthentication:!1});let c=`${n.method}:${o}`;n.body&&n.method!==void 0&&["POST","PATCH","PUT"].includes(n.method)&&(n.body instanceof ae.Readable?n.cache=!1:c+=`:${er.createHash("md5").update(n.body).digest("hex")}`);let l=!1,D=!1;const d=h=>{D=!0;let p=!1,m=()=>{};const f=new Promise(y=>{m=()=>{p||(p=!0,y())}}),F=async y=>{if(l){y.status=y.statusCode;const C=R.CachePolicy.fromObject(l.cachePolicy).revalidatedPolicy(h,y);if(!C.modified){y.resume(),await new Promise(N=>{y.once("end",N)});const A=An(C.policy.responseHeaders());y=new wn({statusCode:l.statusCode,headers:A,body:l.body,url:l.url}),y.cachePolicy=C.policy,y.fromCache=!0}}y.fromCache||(y.cachePolicy=new R.CachePolicy(h,y,h),y.fromCache=!1);let x;h.cache&&y.cachePolicy.storable()?(x=ia(y),(async()=>{try{const C=us.buffer(y);await Promise.race([f,new Promise(S=>y.once("end",S)),new Promise(S=>y.once("close",S))]);const A=await C;let N={url:y.url,statusCode:y.fromCache?l.statusCode:y.statusCode,body:A,cachePolicy:y.cachePolicy.toObject()},k=h.strictTtl?y.cachePolicy.timeToLive():void 0;if(h.maxTtl&&(k=k?Math.min(k,h.maxTtl):h.maxTtl),this.hooks.size>0)for(const S of this.hooks.keys())N=await this.runHook(S,N,y);await this.cache.set(c,N,k)}catch(C){u.emit("error",new Dt(C))}})()):h.cache&&l&&(async()=>{try{await this.cache.delete(c)}catch(C){u.emit("error",new Dt(C))}})(),u.emit("response",x??y),typeof s=="function"&&s(x??y)};try{const y=this.cacheRequest(h,F);y.once("error",m),y.once("abort",m),y.once("destroy",m),u.emit("request",y)}catch(y){u.emit("error",new na(y))}};return(async()=>{const h=async m=>{await Promise.resolve();const f=m.cache?await this.cache.get(c):void 0;if(f===void 0&&!m.forceRefresh){d(m);return}const F=R.CachePolicy.fromObject(f.cachePolicy);if(F.satisfiesWithoutRevalidation(m)&&!m.forceRefresh){const y=An(F.responseHeaders()),x=new wn({statusCode:f.statusCode,headers:y,body:f.body,url:f.url});x.cachePolicy=F,x.fromCache=!0,u.emit("response",x),typeof s=="function"&&s(x)}else F.satisfiesWithoutRevalidation(m)&&Date.now()>=F.timeToLive()&&m.forceRefresh?(await this.cache.delete(c),m.headers=F.revalidationHeaders(m),d(m)):(l=f,m.headers=F.revalidationHeaders(m),d(m))},p=m=>u.emit("error",new Dt(m));if(this.cache instanceof Bt){const m=this.cache;m.once("error",p),u.on("error",()=>m.removeListener("error",p)),u.on("response",()=>m.removeListener("error",p))}try{await h(n)}catch(m){n.automaticFailover&&!D&&d(n),u.emit("error",new Dt(m))}})(),u},this.addHook=(n,s)=>{this.hooks.has(n)||this.hooks.set(n,s)},this.removeHook=n=>this.hooks.delete(n),this.getHook=n=>this.hooks.get(n),this.runHook=async(n,...s)=>this.hooks.get(n)?.(...s),r instanceof Bt?this.cache=r:typeof r=="string"?this.cache=new Bt({uri:r,namespace:"cacheable-request"}):this.cache=new Bt({store:r,namespace:"cacheable-request"}),this.request=this.request.bind(this),this.cacheRequest=e}}const sa=Object.entries,ia=t=>{const e=new ae.PassThrough({autoDestroy:!1});return ra(t,e),t.pipe(e)},oa=t=>{const e={...t};return e.path=`${t.pathname||"/"}${t.search||""}`,delete e.pathname,delete e.search,e},pr=t=>({protocol:t.protocol,auth:t.auth,hostname:t.hostname||t.host||"localhost",port:t.port,pathname:t.pathname,search:t.search}),An=t=>{const e=[];for(const r of Object.keys(t))e[r.toLowerCase()]=t[r];return e},aa=["aborted","complete","headers","httpVersion","httpVersionMinor","httpVersionMajor","method","rawHeaders","rawTrailers","setTimeout","socket","statusCode","statusMessage","trailers","url"];var ca=(t,e)=>{if(e._readableState.autoDestroy)throw new Error("The second stream must have the `autoDestroy` option set to `false`");const r=new Set(Object.keys(t).concat(aa)),n={};for(const s of r)s in e||(n[s]={get(){const i=t[s];return typeof i=="function"?i.bind(t):i},set(i){t[s]=i},enumerable:!0,configurable:!1});return Object.defineProperties(e,n),t.once("aborted",()=>{e.destroy(),e.emit("aborted")}),t.once("close",()=>{t.complete&&e.readable?e.once("end",()=>{e.emit("close")}):e.emit("close")}),e};const{Transform:Da,PassThrough:la}=ae,mr=hi,fa=ca;var ha=t=>{const e=(t.headers["content-encoding"]||"").toLowerCase();if(!["gzip","deflate","br"].includes(e))return t;const r=e==="br";if(r&&typeof mr.createBrotliDecompress!="function")return t.destroy(new Error("Brotli is not supported on Node.js < 12")),t;let n=!0;const s=new Da({transform(o,c,l){n=!1,l(null,o)},flush(o){o()}}),i=new la({autoDestroy:!1,destroy(o,c){t.destroy(),c(o)}}),u=r?mr.createBrotliDecompress():mr.createUnzip();return u.once("error",o=>{if(n&&!t.readable){i.end();return}i.destroy(o)}),fa(t,i),t.pipe(s).pipe(u).pipe(i),i};const da=R.getDefaultExportFromCjs(ha),le=t=>typeof t=="function",pa=t=>le(t[Symbol.asyncIterator]);async function*ma(t){const e=t.getReader();for(;;){const{done:r,value:n}=await e.read();if(r)break;yield n}}const ya=t=>{if(pa(t))return t;if(le(t.getReader))return ma(t);throw new TypeError("Unsupported data source: Expected either ReadableStream or async iterable.")},Bn="abcdefghijklmnopqrstuvwxyz0123456789";function Fa(){let t=16,e="";for(;t--;)e+=Bn[Math.random()*Bn.length<<0];return e}const Sn=t=>String(t).replace(/\r|\n/g,(e,r,n)=>e==="\r"&&n[r+1]!==`
`||e===`
`&&n[r-1]!=="\r"?`\r
`:e),ga=t=>Object.prototype.toString.call(t).slice(8,-1).toLowerCase();function vn(t){if(ga(t)!=="object")return!1;const e=Object.getPrototypeOf(t);return e==null?!0:(e.constructor&&e.constructor.toString())===Object.toString()}function On(t,e){if(typeof e=="string"){for(const[r,n]of Object.entries(t))if(e.toLowerCase()===r.toLowerCase())return n}}const Ea=t=>new Proxy(t,{get:(e,r)=>On(e,r),has:(e,r)=>On(e,r)!==void 0}),Qr=t=>!!(t&&le(t.constructor)&&t[Symbol.toStringTag]==="FormData"&&le(t.append)&&le(t.getAll)&&le(t.entries)&&le(t[Symbol.iterator])),Pn=t=>String(t).replace(/\r/g,"%0D").replace(/\n/g,"%0A").replace(/"/g,"%22"),we=t=>!!(t&&typeof t=="object"&&le(t.constructor)&&t[Symbol.toStringTag]==="File"&&le(t.stream)&&t.name!=null);var Ze=function(t,e,r,n,s){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?t!==e||!s:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?s.call(t,r):s?s.value=r:e.set(t,r),r},q=function(t,e,r,n){if(r==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?t!==e||!n:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return r==="m"?n:r==="a"?n.call(t):n?n.value:e.get(t)},et,pe,tt,St,rt,_e,nt,ut,vt,yr,Rn;const Ca={enableAdditionalHeaders:!1},Ot={writable:!1,configurable:!1};class ba{constructor(e,r,n){if(et.add(this),pe.set(this,`\r
`),tt.set(this,void 0),St.set(this,void 0),rt.set(this,"-".repeat(2)),_e.set(this,new TextEncoder),nt.set(this,void 0),ut.set(this,void 0),vt.set(this,void 0),!Qr(e))throw new TypeError("Expected first argument to be a FormData instance.");let s;if(vn(r)?n=r:s=r,s||(s=Fa()),typeof s!="string")throw new TypeError("Expected boundary argument to be a string.");if(n&&!vn(n))throw new TypeError("Expected options argument to be an object.");Ze(this,ut,Array.from(e.entries()),"f"),Ze(this,vt,{...Ca,...n},"f"),Ze(this,tt,q(this,_e,"f").encode(q(this,pe,"f")),"f"),Ze(this,St,q(this,tt,"f").byteLength,"f"),this.boundary=`form-data-boundary-${s}`,this.contentType=`multipart/form-data; boundary=${this.boundary}`,Ze(this,nt,q(this,_e,"f").encode(`${q(this,rt,"f")}${this.boundary}${q(this,rt,"f")}${q(this,pe,"f").repeat(2)}`),"f");const i={"Content-Type":this.contentType},u=q(this,et,"m",Rn).call(this);u&&(this.contentLength=u,i["Content-Length"]=u),this.headers=Ea(Object.freeze(i)),Object.defineProperties(this,{boundary:Ot,contentType:Ot,contentLength:Ot,headers:Ot})}getContentLength(){return this.contentLength==null?void 0:Number(this.contentLength)}*values(){for(const[e,r]of q(this,ut,"f")){const n=we(r)?r:q(this,_e,"f").encode(Sn(r));yield q(this,et,"m",yr).call(this,e,n),yield n,yield q(this,tt,"f")}yield q(this,nt,"f")}async*encode(){for(const e of this.values())we(e)?yield*ya(e.stream()):yield e}[(pe=new WeakMap,tt=new WeakMap,St=new WeakMap,rt=new WeakMap,_e=new WeakMap,nt=new WeakMap,ut=new WeakMap,vt=new WeakMap,et=new WeakSet,yr=function(r,n){let s="";s+=`${q(this,rt,"f")}${this.boundary}${q(this,pe,"f")}`,s+=`Content-Disposition: form-data; name="${Pn(r)}"`,we(n)&&(s+=`; filename="${Pn(n.name)}"${q(this,pe,"f")}`,s+=`Content-Type: ${n.type||"application/octet-stream"}`);const i=we(n)?n.size:n.byteLength;return q(this,vt,"f").enableAdditionalHeaders===!0&&i!=null&&!isNaN(i)&&(s+=`${q(this,pe,"f")}Content-Length: ${we(n)?n.size:n.byteLength}`),q(this,_e,"f").encode(`${s}${q(this,pe,"f").repeat(2)}`)},Rn=function(){let r=0;for(const[n,s]of q(this,ut,"f")){const i=we(s)?s:q(this,_e,"f").encode(Sn(s)),u=we(i)?i.size:i.byteLength;if(u==null||isNaN(u))return;r+=q(this,et,"m",yr).call(this,n,i).byteLength,r+=u,r+=q(this,St,"f")}return String(r+q(this,nt,"f").byteLength)},Symbol.iterator)](){return this.values()}[Symbol.asyncIterator](){return this.encode()}}function ss(t){return a.nodeStream(t)&&a.function_(t.getBoundary)}async function wa(t,e){if(e&&"content-length"in e)return Number(e["content-length"]);if(!t)return 0;if(a.string(t))return ht.Buffer.byteLength(t);if(a.buffer(t))return t.length;if(ss(t))return te.promisify(t.getLength.bind(t))()}function is(t,e,r){const n={};for(const s of r){const i=(...u)=>{e.emit(s,...u)};n[s]=i,t.on(s,i)}return()=>{for(const[s,i]of Object.entries(n))t.off(s,i)}}function _a(){const t=[];return{once(e,r,n){e.once(r,n),t.push({origin:e,event:r,fn:n})},unhandleAll(){for(const e of t){const{origin:r,event:n,fn:s}=e;r.removeListener(n,s)}t.length=0}}}const Tn=Symbol("reentry"),xa=()=>{};class os extends Error{constructor(e,r){super(`Timeout awaiting '${r}' for ${e}ms`),Object.defineProperty(this,"event",{enumerable:!0,configurable:!0,writable:!0,value:r}),Object.defineProperty(this,"code",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name="TimeoutError",this.code="ETIMEDOUT"}}function Aa(t,e,r){if(Tn in t)return xa;t[Tn]=!0;const n=[],{once:s,unhandleAll:i}=_a(),u=(f,F,y)=>{const x=setTimeout(F,f,f,y);x.unref?.();const C=()=>{clearTimeout(x)};return n.push(C),C},{host:o,hostname:c}=r,l=(f,F)=>{t.destroy(new os(f,F))},D=()=>{for(const f of n)f();i()};if(t.once("error",f=>{if(D(),t.listenerCount("error")===0)throw f}),typeof e.request<"u"){const f=u(e.request,l,"request");s(t,"response",F=>{s(F,"end",f)})}if(typeof e.socket<"u"){const{socket:f}=e,F=()=>{l(f,"socket")};t.setTimeout(f,F),n.push(()=>{t.removeListener("timeout",F)})}const d=typeof e.lookup<"u",h=typeof e.connect<"u",p=typeof e.secureConnect<"u",m=typeof e.send<"u";return(d||h||p||m)&&s(t,"socket",f=>{const{socketPath:F}=t;if(f.connecting){const y=!!(F??qu.isIP(c??o??"")!==0);if(d&&!y&&typeof f.address().address>"u"){const x=u(e.lookup,l,"lookup");s(f,"lookup",x)}if(h){const x=()=>u(e.connect,l,"connect");y?s(f,"connect",x()):s(f,"lookup",C=>{C===null&&s(f,"connect",x())})}p&&r.protocol==="https:"&&s(f,"connect",()=>{const x=u(e.secureConnect,l,"secureConnect");s(f,"secureConnect",x)})}if(m){const y=()=>u(e.send,l,"send");f.connecting?s(f,"connect",()=>{s(t,"upload-complete",y())}):s(t,"upload-complete",y())}}),typeof e.response<"u"&&s(t,"upload-complete",()=>{const f=u(e.response,l,"response");s(t,"response",f)}),typeof e.read<"u"&&s(t,"response",f=>{const F=u(e.read,l,"read");s(f,"end",F)}),D}function Ba(t){t=t;const e={protocol:t.protocol,hostname:a.string(t.hostname)&&t.hostname.startsWith("[")?t.hostname.slice(1,-1):t.hostname,host:t.host,hash:t.hash,search:t.search,pathname:t.pathname,href:t.href,path:`${t.pathname||""}${t.search||""}`};return a.string(t.port)&&t.port.length>0&&(e.port=Number(t.port)),(t.username||t.password)&&(e.auth=`${t.username||""}:${t.password||""}`),e}class Sa{constructor(){Object.defineProperty(this,"weakMap",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"map",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.weakMap=new WeakMap,this.map=new Map}set(e,r){typeof e=="object"?this.weakMap.set(e,r):this.map.set(e,r)}get(e){return typeof e=="object"?this.weakMap.get(e):this.map.get(e)}has(e){return typeof e=="object"?this.weakMap.has(e):this.map.has(e)}}const va=({attemptCount:t,retryOptions:e,error:r,retryAfter:n,computedValue:s})=>{if(r.name==="RetryError")return 1;if(t>e.limit)return 0;const i=e.methods.includes(r.options.method),u=e.errorCodes.includes(r.code),o=r.response&&e.statusCodes.includes(r.response.statusCode);if(!i||!u&&!o)return 0;if(r.response){if(n)return n>s?0:n;if(r.response.statusCode===413)return 0}const c=Math.random()*e.noise;return Math.min(2**(t-1)*1e3,e.backoffLimit)+c},{Resolver:$n}=We.promises,Le=Symbol("cacheableLookupCreateConnection"),Fr=Symbol("cacheableLookupInstance"),jn=Symbol("expires"),Oa=typeof We.ALL=="number",kn=t=>{if(!(t&&typeof t.createConnection=="function"))throw new Error("Expected an Agent instance as the first argument")},Pa=t=>{for(const e of t)e.family!==6&&(e.address=`::ffff:${e.address}`,e.family=6)},Ln=()=>{let t=!1,e=!1;for(const r of Object.values(mt.networkInterfaces()))for(const n of r)if(!n.internal&&(n.family==="IPv6"?e=!0:t=!0,t&&e))return{has4:t,has6:e};return{has4:t,has6:e}},Ra=t=>Symbol.iterator in t,Pt=t=>t.catch(e=>{if(e.code==="ENODATA"||e.code==="ENOTFOUND"||e.code==="ENOENT")return[];throw e}),Nn={ttl:!0},Ta={all:!0},$a={all:!0,family:4},ja={all:!0,family:6};class ka{constructor({cache:e=new Map,maxTtl:r=1/0,fallbackDuration:n=3600,errorTtl:s=.15,resolver:i=new $n,lookup:u=We.lookup}={}){if(this.maxTtl=r,this.errorTtl=s,this._cache=e,this._resolver=i,this._dnsLookup=u&&te.promisify(u),this.stats={cache:0,query:0},this._resolver instanceof $n?(this._resolve4=this._resolver.resolve4.bind(this._resolver),this._resolve6=this._resolver.resolve6.bind(this._resolver)):(this._resolve4=te.promisify(this._resolver.resolve4.bind(this._resolver)),this._resolve6=te.promisify(this._resolver.resolve6.bind(this._resolver))),this._iface=Ln(),this._pending={},this._nextRemovalTime=!1,this._hostnamesToFallback=new Set,this.fallbackDuration=n,n>0){const o=setInterval(()=>{this._hostnamesToFallback.clear()},n*1e3);o.unref&&o.unref(),this._fallbackInterval=o}this.lookup=this.lookup.bind(this),this.lookupAsync=this.lookupAsync.bind(this)}set servers(e){this.clear(),this._resolver.setServers(e)}get servers(){return this._resolver.getServers()}lookup(e,r,n){if(typeof r=="function"?(n=r,r={}):typeof r=="number"&&(r={family:r}),!n)throw new Error("Callback must be a function.");this.lookupAsync(e,r).then(s=>{r.all?n(null,s):n(null,s.address,s.family,s.expires,s.ttl,s.source)},n)}async lookupAsync(e,r={}){typeof r=="number"&&(r={family:r});let n=await this.query(e);if(r.family===6){const s=n.filter(i=>i.family===6);r.hints&We.V4MAPPED&&(Oa&&r.hints&We.ALL||s.length===0)?Pa(n):n=s}else r.family===4&&(n=n.filter(s=>s.family===4));if(r.hints&We.ADDRCONFIG){const{_iface:s}=this;n=n.filter(i=>i.family===6?s.has6:s.has4)}if(n.length===0){const s=new Error(`cacheableLookup ENOTFOUND ${e}`);throw s.code="ENOTFOUND",s.hostname=e,s}return r.all?n:n[0]}async query(e){let r="cache",n=await this._cache.get(e);if(n&&this.stats.cache++,!n){const s=this._pending[e];if(s)this.stats.cache++,n=await s;else{r="query";const i=this.queryAndCache(e);this._pending[e]=i,this.stats.query++;try{n=await i}finally{delete this._pending[e]}}}return n=n.map(s=>({...s,source:r})),n}async _resolve(e){const[r,n]=await Promise.all([Pt(this._resolve4(e,Nn)),Pt(this._resolve6(e,Nn))]);let s=0,i=0,u=0;const o=Date.now();for(const c of r)c.family=4,c.expires=o+c.ttl*1e3,s=Math.max(s,c.ttl);for(const c of n)c.family=6,c.expires=o+c.ttl*1e3,i=Math.max(i,c.ttl);return r.length>0?n.length>0?u=Math.min(s,i):u=s:u=i,{entries:[...r,...n],cacheTtl:u}}async _lookup(e){try{const[r,n]=await Promise.all([Pt(this._dnsLookup(e,$a)),Pt(this._dnsLookup(e,ja))]);return{entries:[...r,...n],cacheTtl:0}}catch{return{entries:[],cacheTtl:0}}}async _set(e,r,n){if(this.maxTtl>0&&n>0){n=Math.min(n,this.maxTtl)*1e3,r[jn]=Date.now()+n;try{await this._cache.set(e,r,n)}catch(s){this.lookupAsync=async()=>{const i=new Error("Cache Error. Please recreate the CacheableLookup instance.");throw i.cause=s,i}}Ra(this._cache)&&this._tick(n)}}async queryAndCache(e){if(this._hostnamesToFallback.has(e))return this._dnsLookup(e,Ta);let r=await this._resolve(e);r.entries.length===0&&this._dnsLookup&&(r=await this._lookup(e),r.entries.length!==0&&this.fallbackDuration>0&&this._hostnamesToFallback.add(e));const n=r.entries.length===0?this.errorTtl:r.cacheTtl;return await this._set(e,r.entries,n),r.entries}_tick(e){const r=this._nextRemovalTime;(!r||e<r)&&(clearTimeout(this._removalTimeout),this._nextRemovalTime=e,this._removalTimeout=setTimeout(()=>{this._nextRemovalTime=!1;let n=1/0;const s=Date.now();for(const[i,u]of this._cache){const o=u[jn];s>=o?this._cache.delete(i):o<n&&(n=o)}n!==1/0&&this._tick(n-s)},e),this._removalTimeout.unref&&this._removalTimeout.unref())}install(e){if(kn(e),Le in e)throw new Error("CacheableLookup has been already installed");e[Le]=e.createConnection,e[Fr]=this,e.createConnection=(r,n)=>("lookup"in r||(r.lookup=this.lookup),e[Le](r,n))}uninstall(e){if(kn(e),e[Le]){if(e[Fr]!==this)throw new Error("The agent is not owned by this CacheableLookup instance");e.createConnection=e[Le],delete e[Le],delete e[Fr]}}updateInterfaceInfo(){const{_iface:e}=this;this._iface=Ln(),(e.has4&&!this._iface.has4||e.has6&&!this._iface.has6)&&this._cache.clear()}clear(e){if(e){this._cache.delete(e);return}this._cache.clear()}}let La=class{constructor(e={}){if(!(e.maxSize&&e.maxSize>0))throw new TypeError("`maxSize` must be a number greater than 0");this.maxSize=e.maxSize,this.onEviction=e.onEviction,this.cache=new Map,this.oldCache=new Map,this._size=0}_set(e,r){if(this.cache.set(e,r),this._size++,this._size>=this.maxSize){if(this._size=0,typeof this.onEviction=="function")for(const[n,s]of this.oldCache.entries())this.onEviction(n,s);this.oldCache=this.cache,this.cache=new Map}}get(e){if(this.cache.has(e))return this.cache.get(e);if(this.oldCache.has(e)){const r=this.oldCache.get(e);return this.oldCache.delete(e),this._set(e,r),r}}set(e,r){return this.cache.has(e)?this.cache.set(e,r):this._set(e,r),this}has(e){return this.cache.has(e)||this.oldCache.has(e)}peek(e){if(this.cache.has(e))return this.cache.get(e);if(this.oldCache.has(e))return this.oldCache.get(e)}delete(e){const r=this.cache.delete(e);return r&&this._size--,this.oldCache.delete(e)||r}clear(){this.cache.clear(),this.oldCache.clear(),this._size=0}*keys(){for(const[e]of this)yield e}*values(){for(const[,e]of this)yield e}*[Symbol.iterator](){for(const e of this.cache)yield e;for(const e of this.oldCache){const[r]=e;this.cache.has(r)||(yield e)}}get size(){let e=0;for(const r of this.oldCache.keys())this.cache.has(r)||e++;return Math.min(this._size+e,this.maxSize)}};var as=La,cs=t=>{if(t.listenerCount("error")!==0)return t;t.__destroy=t._destroy,t._destroy=(...r)=>{const n=r.pop();t.__destroy(...r,async s=>{await Promise.resolve(),n(s)})};const e=r=>{Promise.resolve().then(()=>{t.emit("error",r)})};return t.once("error",e),Promise.resolve().then(()=>{t.off("error",e)}),t};const{URL:In}=K,Na=Re,Ia=Ft,Ua=Mr,qa=as,Ha=cs,ue=Symbol("currentStreamCount"),Un=Symbol("request"),se=Symbol("cachedOriginSet"),ye=Symbol("gracefullyClosing"),Rt=Symbol("length"),qn=["createConnection","maxDeflateDynamicTableSize","maxSettings","maxSessionMemory","maxHeaderListPairs","maxOutstandingPings","maxReservedRemoteStreams","maxSendHeaderBlockLength","paddingStrategy","peerMaxConcurrentStreams","settings","family","localAddress","rejectUnauthorized","pskCallback","minDHSize","path","socket","ca","cert","sigalgs","ciphers","clientCertEngine","crl","dhparam","ecdhCurve","honorCipherOrder","key","privateKeyEngine","privateKeyIdentifier","maxVersion","minVersion","pfx","secureOptions","secureProtocol","sessionIdContext","ticketKeys"],Ma=(t,e,r)=>{let n=0,s=t.length;for(;n<s;){const i=n+s>>>1;r(t[i],e)?n=i+1:s=i}return n},za=(t,e)=>t.remoteSettings.maxConcurrentStreams>e.remoteSettings.maxConcurrentStreams,gr=(t,e)=>{for(let r=0;r<t.length;r++){const n=t[r];n[se].length>0&&n[se].length<e[se].length&&n[se].every(s=>e[se].includes(s))&&n[ue]+e[ue]<=e.remoteSettings.maxConcurrentStreams&&Ds(n)}},Hn=(t,e)=>{for(let r=0;r<t.length;r++){const n=t[r];if(e[se].length>0&&e[se].length<n[se].length&&e[se].every(s=>n[se].includes(s))&&e[ue]+n[ue]<=n.remoteSettings.maxConcurrentStreams)return Ds(e),!0}return!1},Ds=t=>{t[ye]=!0,t[ue]===0&&t.close()};let Kt=class ls extends Na{constructor({timeout:e=0,maxSessions:r=Number.POSITIVE_INFINITY,maxEmptySessions:n=10,maxCachedTlsSessions:s=100}={}){super(),this.sessions={},this.queue={},this.timeout=e,this.maxSessions=r,this.maxEmptySessions=n,this._emptySessionCount=0,this._sessionCount=0,this.settings={enablePush:!1,initialWindowSize:1024*1024*32},this.tlsSessionCache=new qa({maxSize:s})}get protocol(){return"https:"}normalizeOptions(e){let r="";for(let n=0;n<qn.length;n++){const s=qn[n];r+=":",e&&e[s]!==void 0&&(r+=e[s])}return r}_processQueue(){if(this._sessionCount>=this.maxSessions){this.closeEmptySessions(this.maxSessions-this._sessionCount+1);return}for(const e in this.queue)for(const r in this.queue[e]){const n=this.queue[e][r];n.completed||(n.completed=!0,n())}}_isBetterSession(e,r){return e>r}_accept(e,r,n,s){let i=0;for(;i<r.length&&e[ue]<e.remoteSettings.maxConcurrentStreams;)r[i].resolve(e),i++;r.splice(0,i),r.length>0&&(this.getSession(n,s,r),r.length=0)}getSession(e,r,n){return new Promise((s,i)=>{Array.isArray(n)&&n.length>0?(n=[...n],s()):n=[{resolve:s,reject:i}];try{if(typeof e=="string")e=new In(e);else if(!(e instanceof In))throw new TypeError("The `origin` argument needs to be a string or an URL object");if(r){const{servername:D}=r,{hostname:d}=e;if(D&&d!==D)throw new Error(`Origin ${d} differs from servername ${D}`)}}catch(D){for(let d=0;d<n.length;d++)n[d].reject(D);return}const u=this.normalizeOptions(r),o=e.origin;if(u in this.sessions){const D=this.sessions[u];let d=-1,h=-1,p;for(let m=0;m<D.length;m++){const f=D[m],F=f.remoteSettings.maxConcurrentStreams;if(F<d)break;if(!f[se].includes(o))continue;const y=f[ue];y>=F||f[ye]||f.destroyed||(p||(d=F),this._isBetterSession(y,h)&&(p=f,h=y))}if(p){this._accept(p,n,o,r);return}}if(u in this.queue){if(o in this.queue[u]){this.queue[u][o].listeners.push(...n);return}}else this.queue[u]={[Rt]:0};const c=()=>{u in this.queue&&this.queue[u][o]===l&&(delete this.queue[u][o],--this.queue[u][Rt]===0&&delete this.queue[u])},l=async()=>{this._sessionCount++;const D=`${o}:${u}`;let d=!1,h;try{const p={...r};p.settings===void 0&&(p.settings=this.settings),p.session===void 0&&(p.session=this.tlsSessionCache.get(D)),h=await(p.createConnection||this.createConnection).call(this,e,p),p.createConnection=()=>h;const f=Ua.connect(e,p);f[ue]=0,f[ye]=!1;const F=()=>{const{socket:C}=f;let A;return C.servername===!1?(C.servername=C.remoteAddress,A=f.originSet,C.servername=!1):A=f.originSet,A},y=()=>f[ue]<f.remoteSettings.maxConcurrentStreams;f.socket.once("session",C=>{this.tlsSessionCache.set(D,C)}),f.once("error",C=>{for(let A=0;A<n.length;A++)n[A].reject(C);this.tlsSessionCache.delete(D)}),f.setTimeout(this.timeout,()=>{f.destroy()}),f.once("close",()=>{if(this._sessionCount--,d){this._emptySessionCount--;const C=this.sessions[u];C.length===1?delete this.sessions[u]:C.splice(C.indexOf(f),1)}else{c();const C=new Error("Session closed without receiving a SETTINGS frame");C.code="HTTP2WRAPPER_NOSETTINGS";for(let A=0;A<n.length;A++)n[A].reject(C)}this._processQueue()});const x=()=>{const C=this.queue[u];if(!C)return;const A=f[se];for(let N=0;N<A.length;N++){const k=A[N];if(k in C){const{listeners:S,completed:I}=C[k];let g=0;for(;g<S.length&&y();)S[g].resolve(f),g++;if(C[k].listeners.splice(0,g),C[k].listeners.length===0&&!I&&(delete C[k],--C[Rt]===0)){delete this.queue[u];break}if(!y())break}}};f.on("origin",()=>{f[se]=F()||[],f[ye]=!1,Hn(this.sessions[u],f),!(f[ye]||!y())&&(x(),y()&&gr(this.sessions[u],f))}),f.once("remoteSettings",()=>{if(l.destroyed){const C=new Error("Agent has been destroyed");for(let A=0;A<n.length;A++)n[A].reject(C);f.destroy();return}if(f.setLocalWindowSize&&f.setLocalWindowSize(1024*1024*4),f[se]=F()||[],f.socket.encrypted){const C=f[se][0];if(C!==o){const A=new Error(`Requested origin ${o} does not match server ${C}`);for(let N=0;N<n.length;N++)n[N].reject(A);f.destroy();return}}c();{const C=this.sessions;if(u in C){const A=C[u];A.splice(Ma(A,f,za),0,f)}else C[u]=[f]}d=!0,this._emptySessionCount++,this.emit("session",f),this._accept(f,n,o,r),f[ue]===0&&this._emptySessionCount>this.maxEmptySessions&&this.closeEmptySessions(this._emptySessionCount-this.maxEmptySessions),f.on("remoteSettings",()=>{y()&&(x(),y()&&gr(this.sessions[u],f))})}),f[Un]=f.request,f.request=(C,A)=>{if(f[ye])throw new Error("The session is gracefully closing. No new streams are allowed.");const N=f[Un](C,A);return f.ref(),f[ue]++===0&&this._emptySessionCount--,N.once("close",()=>{if(--f[ue]===0&&(this._emptySessionCount++,f.unref(),this._emptySessionCount>this.maxEmptySessions||f[ye])){f.close();return}f.destroyed||f.closed||y()&&!Hn(this.sessions[u],f)&&(gr(this.sessions[u],f),x(),f[ue]===0&&this._processQueue())}),N}}catch(p){c(),this._sessionCount--;for(let m=0;m<n.length;m++)n[m].reject(p)}};l.listeners=n,l.completed=!1,l.destroyed=!1,this.queue[u][o]=l,this.queue[u][Rt]++,this._processQueue()})}request(e,r,n,s){return new Promise((i,u)=>{this.getSession(e,r,[{reject:u,resolve:o=>{try{const c=o.request(n,s);Ha(c),i(c)}catch(c){u(c)}}}])})}async createConnection(e,r){return ls.connect(e,r)}static connect(e,r){r.ALPNProtocols=["h2"];const n=e.port||443,s=e.hostname;typeof r.servername>"u"&&(r.servername=s);const i=Ia.connect(n,s,r);return r.socket&&(i._peername={family:void 0,address:void 0,port:n}),i}closeEmptySessions(e=Number.POSITIVE_INFINITY){let r=0;const{sessions:n}=this;for(const s in n){const i=n[s];for(let u=0;u<i.length;u++){const o=i[u];if(o[ue]===0&&(r++,o.close(),r>=e))return r}}return r}destroy(e){const{sessions:r,queue:n}=this;for(const s in r){const i=r[s];for(let u=0;u<i.length;u++)i[u].destroy(e)}for(const s in n){const i=n[s];for(const u in i)i[u].destroyed=!0}this.queue={},this.tlsSessionCache.clear()}get emptySessionCount(){return this._emptySessionCount}get pendingSessionCount(){return this._sessionCount-this._emptySessionCount}get sessionCount(){return this._sessionCount}};Kt.kCurrentStreamCount=ue;Kt.kGracefullyClosing=ye;var Je={Agent:Kt,globalAgent:new Kt};const{Readable:Ga}=ae;let Wa=class extends Ga{constructor(e,r){super({emitClose:!1,autoDestroy:!0,highWaterMark:r}),this.statusCode=null,this.statusMessage="",this.httpVersion="2.0",this.httpVersionMajor=2,this.httpVersionMinor=0,this.headers={},this.trailers={},this.req=null,this.aborted=!1,this.complete=!1,this.upgrade=null,this.rawHeaders=[],this.rawTrailers=[],this.socket=e,this._dumped=!1}get connection(){return this.socket}set connection(e){this.socket=e}_destroy(e,r){this.readableEnded||(this.aborted=!0),r(),this.req._request.destroy(e)}setTimeout(e,r){return this.req.setTimeout(e,r),this}_dump(){this._dumped||(this._dumped=!0,this.removeAllListeners("data"),this.resume())}_read(){this.req&&this.req._request.resume()}};var fs=Wa,Va=(t,e,r)=>{for(const n of r)t.on(n,(...s)=>e.emit(n,...s))},hs={exports:{}};(function(t){const e=(r,n,s)=>{t.exports[n]=class extends r{constructor(...u){super(typeof s=="string"?s:s(u)),this.name=`${super.name} [${n}]`,this.code=n}}};e(TypeError,"ERR_INVALID_ARG_TYPE",r=>{const n=r[0].includes(".")?"property":"argument";let s=r[1];const i=Array.isArray(s);return i&&(s=`${s.slice(0,-1).join(", ")} or ${s.slice(-1)}`),`The "${r[0]}" ${n} must be ${i?"one of":"of"} type ${s}. Received ${typeof r[2]}`}),e(TypeError,"ERR_INVALID_PROTOCOL",r=>`Protocol "${r[0]}" not supported. Expected "${r[1]}"`),e(Error,"ERR_HTTP_HEADERS_SENT",r=>`Cannot ${r[0]} headers after they are sent to the client`),e(TypeError,"ERR_INVALID_HTTP_TOKEN",r=>`${r[0]} must be a valid HTTP token [${r[1]}]`),e(TypeError,"ERR_HTTP_INVALID_HEADER_VALUE",r=>`Invalid value "${r[0]} for header "${r[1]}"`),e(TypeError,"ERR_INVALID_CHAR",r=>`Invalid character in ${r[0]} [${r[1]}]`),e(Error,"ERR_HTTP2_NO_SOCKET_MANIPULATION","HTTP/2 sockets should not be directly manipulated (e.g. read and written)")})(hs);var rr=hs.exports,Ja=t=>{switch(t){case":method":case":scheme":case":authority":case":path":return!0;default:return!1}};const{ERR_INVALID_HTTP_TOKEN:Ka}=rr,Ya=Ja,Qa=/^[\^`\-\w!#$%&*+.|~]+$/;var ds=t=>{if(typeof t!="string"||!Qa.test(t)&&!Ya(t))throw new Ka("Header name",t)};const{ERR_HTTP_INVALID_HEADER_VALUE:Xa,ERR_INVALID_CHAR:Za}=rr,ec=/[^\t\u0020-\u007E\u0080-\u00FF]/;var ps=(t,e)=>{if(typeof e>"u")throw new Xa(e,t);if(ec.test(e))throw new Za("header content",t)};const{ERR_HTTP2_NO_SOCKET_MANIPULATION:Mn}=rr,tc={has(t,e){const r=t.session===void 0?t:t.session.socket;return e in t||e in r},get(t,e){switch(e){case"on":case"once":case"end":case"emit":case"destroy":return t[e].bind(t);case"writable":case"destroyed":return t[e];case"readable":return t.destroyed?!1:t.readable;case"setTimeout":{const{session:r}=t;return r!==void 0?r.setTimeout.bind(r):t.setTimeout.bind(t)}case"write":case"read":case"pause":case"resume":throw new Mn;default:{const r=t.session===void 0?t:t.session.socket,n=r[e];return typeof n=="function"?n.bind(r):n}}},getPrototypeOf(t){return t.session!==void 0?Reflect.getPrototypeOf(t.session.socket):Reflect.getPrototypeOf(t)},set(t,e,r){switch(e){case"writable":case"readable":case"destroyed":case"on":case"once":case"end":case"emit":case"destroy":return t[e]=r,!0;case"setTimeout":{const{session:n}=t;return n===void 0?t.setTimeout=r:n.setTimeout=r,!0}case"write":case"read":case"pause":case"resume":throw new Mn;default:{const n=t.session===void 0?t:t.session.socket;return n[e]=r,!0}}}};var rc=tc;const{URL:Er,urlToHttpOptions:zn}=K,nc=Mr,{Writable:uc}=ae,{Agent:sc,globalAgent:ic}=Je,oc=fs,ac=Va,{ERR_INVALID_ARG_TYPE:Tt,ERR_INVALID_PROTOCOL:cc,ERR_HTTP_HEADERS_SENT:Gn}=rr,Dc=ds,lc=ps,fc=rc,{HTTP2_HEADER_STATUS:Wn,HTTP2_HEADER_METHOD:Vn,HTTP2_HEADER_PATH:Jn,HTTP2_HEADER_AUTHORITY:st,HTTP2_METHOD_CONNECT:hc}=nc.constants,X=Symbol("headers"),Ne=Symbol("origin"),it=Symbol("session"),Cr=Symbol("options"),$t=Symbol("flushedHeaders"),Ie=Symbol("jobs"),ot=Symbol("pendingAgentPromise");let dc=class extends uc{constructor(e,r,n){if(super({autoDestroy:!1,emitClose:!1}),typeof e=="string"?e=zn(new Er(e)):e instanceof Er?e=zn(e):e={...e},typeof r=="function"||r===void 0?(n=r,r=e):r=Object.assign(e,r),r.h2session){if(this[it]=r.h2session,this[it].destroyed)throw new Error("The session has been closed already");this.protocol=this[it].socket.encrypted?"https:":"http:"}else if(r.agent===!1)this.agent=new sc({maxEmptySessions:0});else if(typeof r.agent>"u"||r.agent===null)this.agent=ic;else if(typeof r.agent.request=="function")this.agent=r.agent;else throw new Tt("options.agent",["http2wrapper.Agent-like Object","undefined","false"],r.agent);if(this.agent&&(this.protocol=this.agent.protocol),r.protocol&&r.protocol!==this.protocol)throw new cc(r.protocol,this.protocol);r.port||(r.port=r.defaultPort||this.agent&&this.agent.defaultPort||443),r.host=r.hostname||r.host||"localhost",delete r.hostname;const{timeout:s}=r;r.timeout=void 0,this[X]=Object.create(null),this[Ie]=[],this[ot]=void 0,this.socket=null,this.connection=null,this.method=r.method||"GET",this.method==="CONNECT"&&(r.path==="/"||r.path===void 0)||(this.path=r.path),this.res=null,this.aborted=!1,this.reusedSocket=!1;const{headers:i}=r;if(i)for(const o in i)this.setHeader(o,i[o]);r.auth&&!("authorization"in this[X])&&(this[X].authorization="Basic "+Buffer.from(r.auth).toString("base64")),r.session=r.tlsSession,r.path=r.socketPath,this[Cr]=r,this[Ne]=new Er(`${this.protocol}//${r.servername||r.host}:${r.port}`);const u=r._reuseSocket;u&&(r.createConnection=(...o)=>u.destroyed?this.agent.createConnection(...o):u,this.agent.getSession(this[Ne],this[Cr]).catch(()=>{})),s&&this.setTimeout(s),n&&this.once("response",n),this[$t]=!1}get method(){return this[X][Vn]}set method(e){e&&(this[X][Vn]=e.toUpperCase())}get path(){const e=this.method==="CONNECT"?st:Jn;return this[X][e]}set path(e){if(e){const r=this.method==="CONNECT"?st:Jn;this[X][r]=e}}get host(){return this[Ne].hostname}set host(e){}get _mustNotHaveABody(){return this.method==="GET"||this.method==="HEAD"||this.method==="DELETE"}_write(e,r,n){if(this._mustNotHaveABody){n(new Error("The GET, HEAD and DELETE methods must NOT have a body"));return}this.flushHeaders();const s=()=>this._request.write(e,r,n);this._request?s():this[Ie].push(s)}_final(e){this.flushHeaders();const r=()=>{if(this._mustNotHaveABody||this.method==="CONNECT"){e();return}this._request.end(e)};this._request?r():this[Ie].push(r)}abort(){this.res&&this.res.complete||(this.aborted||process.nextTick(()=>this.emit("abort")),this.aborted=!0,this.destroy())}async _destroy(e,r){this.res&&this.res._dump(),this._request?this._request.destroy():process.nextTick(()=>{this.emit("close")});try{await this[ot]}catch(n){this.aborted&&(e=n)}r(e)}async flushHeaders(){if(this[$t]||this.destroyed)return;this[$t]=!0;const e=this.method===hc,r=n=>{if(this._request=n,this.destroyed){n.destroy();return}e||ac(n,this,["timeout","continue"]),n.once("error",i=>{this.destroy(i)}),n.once("aborted",()=>{const{res:i}=this;i?(i.aborted=!0,i.emit("aborted"),i.destroy()):this.destroy(new Error("The server aborted the HTTP/2 stream"))});const s=(i,u,o)=>{const c=new oc(this.socket,n.readableHighWaterMark);this.res=c,c.url=`${this[Ne].origin}${this.path}`,c.req=this,c.statusCode=i[Wn],c.headers=i,c.rawHeaders=o,c.once("end",()=>{c.complete=!0,c.socket=null,c.connection=null}),e?(c.upgrade=!0,this.emit("connect",c,n,Buffer.alloc(0))?this.emit("close"):n.destroy()):(n.on("data",l=>{!c._dumped&&!c.push(l)&&n.pause()}),n.once("end",()=>{this.aborted||c.push(null)}),this.emit("response",c)||c._dump())};n.once("response",s),n.once("headers",i=>this.emit("information",{statusCode:i[Wn]})),n.once("trailers",(i,u,o)=>{const{res:c}=this;if(c===null){s(i,u,o);return}c.trailers=i,c.rawTrailers=o}),n.once("close",()=>{const{aborted:i,res:u}=this;if(u){i&&(u.aborted=!0,u.emit("aborted"),u.destroy());const o=()=>{u.emit("close"),this.destroy(),this.emit("close")};u.readable?u.once("end",o):o();return}if(!this.destroyed){this.destroy(new Error("The HTTP/2 stream has been early terminated")),this.emit("close");return}this.destroy(),this.emit("close")}),this.socket=new Proxy(n,fc);for(const i of this[Ie])i();this[Ie].length=0,this.emit("socket",this.socket)};if(!(st in this[X])&&!e&&(this[X][st]=this[Ne].host),this[it])try{r(this[it].request(this[X]))}catch(n){this.destroy(n)}else{this.reusedSocket=!0;try{const n=this.agent.request(this[Ne],this[Cr],this[X]);this[ot]=n,r(await n),this[ot]=!1}catch(n){this[ot]=!1,this.destroy(n)}}}get connection(){return this.socket}set connection(e){this.socket=e}getHeaderNames(){return Object.keys(this[X])}hasHeader(e){if(typeof e!="string")throw new Tt("name","string",e);return!!this[X][e.toLowerCase()]}getHeader(e){if(typeof e!="string")throw new Tt("name","string",e);return this[X][e.toLowerCase()]}get headersSent(){return this[$t]}removeHeader(e){if(typeof e!="string")throw new Tt("name","string",e);if(this.headersSent)throw new Gn("remove");delete this[X][e.toLowerCase()]}setHeader(e,r){if(this.headersSent)throw new Gn("set");Dc(e),lc(e,r);const n=e.toLowerCase();if(n==="connection"){if(r.toLowerCase()==="keep-alive")return;throw new Error(`Invalid 'connection' header: ${r}`)}n==="host"&&this.method==="CONNECT"?this[X][st]=r:this[X][n]=r}setNoDelay(){}setSocketKeepAlive(){}setTimeout(e,r){const n=()=>this._request.setTimeout(e,r);return this._request?n():this[Ie].push(n),this}get maxHeadersCount(){if(!this.destroyed&&this._request)return this._request.session.localSettings.maxHeaderListSize}set maxHeadersCount(e){}};var ms=dc,bt={exports:{}};const pc=Ft;var mc=(t={},e=pc.connect)=>new Promise((r,n)=>{let s=!1,i;const u=async()=>{await c,i.off("timeout",o),i.off("error",n),t.resolveSocket?(r({alpnProtocol:i.alpnProtocol,socket:i,timeout:s}),s&&(await Promise.resolve(),i.emit("timeout"))):(i.destroy(),r({alpnProtocol:i.alpnProtocol,timeout:s}))},o=async()=>{s=!0,u()},c=(async()=>{try{i=await e(t,u),i.on("error",n),i.once("timeout",o)}catch(l){n(l)}})()});const{isIP:yc}=qu,Fc=Zt,gc=t=>{if(t[0]==="["){const r=t.indexOf("]");return Fc(r!==-1),t.slice(1,r)}const e=t.indexOf(":");return e===-1?t:t.slice(0,e)};var Ec=t=>{const e=gc(t);return yc(e)?"":e};const{URL:Kn,urlToHttpOptions:Yn}=K,Qn=fe,br=yt,Cc=mc,bc=as,{Agent:wc,globalAgent:_c}=Je,xc=ms,Ac=Ec,Xn=cs,ys=new bc({maxSize:100}),Bc=new Map,Sc=(t,e,r)=>{e._httpMessage={shouldKeepAlive:!0};const n=()=>{t.emit("free",e,r)};e.on("free",n);const s=()=>{t.removeSocket(e,r)};e.on("close",s);const i=()=>{const{freeSockets:o}=t;for(const c of Object.values(o))if(c.includes(e)){e.destroy();return}};e.on("timeout",i);const u=()=>{t.removeSocket(e,r),e.off("close",s),e.off("free",n),e.off("timeout",i),e.off("agentRemove",u)};e.on("agentRemove",u),t.emit("free",e,r)},Fs=(t,e=new Map,r=void 0)=>async n=>{const s=`${n.host}:${n.port}:${n.ALPNProtocols.sort()}`;if(!t.has(s)){if(e.has(s))return{alpnProtocol:(await e.get(s)).alpnProtocol};const{path:i}=n;n.path=n.socketPath;const u=Cc(n,r);e.set(s,u);try{const o=await u;return t.set(s,o.alpnProtocol),e.delete(s),n.path=i,o}catch(o){throw e.delete(s),n.path=i,o}}return{alpnProtocol:t.get(s)}},gs=Fs(ys,Bc);bt.exports=async(t,e,r)=>{if(typeof t=="string"?t=Yn(new Kn(t)):t instanceof Kn?t=Yn(t):t={...t},typeof e=="function"||e===void 0?(r=e,e=t):e=Object.assign(t,e),e.ALPNProtocols=e.ALPNProtocols||["h2","http/1.1"],!Array.isArray(e.ALPNProtocols)||e.ALPNProtocols.length===0)throw new Error("The `ALPNProtocols` option must be an Array with at least one entry");e.protocol=e.protocol||"https:";const n=e.protocol==="https:";e.host=e.hostname||e.host||"localhost",e.session=e.tlsSession,e.servername=e.servername||Ac(e.headers&&e.headers.host||e.host),e.port=e.port||(n?443:80),e._defaultAgent=n?br.globalAgent:Qn.globalAgent;const s=e.resolveProtocol||gs;let{agent:i}=e;if(i!==void 0&&i!==!1&&i.constructor.name!=="Object")throw new Error("The `options.agent` can be only an object `http`, `https` or `http2` properties");if(n){e.resolveSocket=!0;let{socket:u,alpnProtocol:o,timeout:c}=await s(e);if(c){u&&u.destroy();const D=new Error(`Timed out resolving ALPN: ${e.timeout} ms`);throw D.code="ETIMEDOUT",D.ms=e.timeout,D}u&&e.createConnection&&(u.destroy(),u=void 0),delete e.resolveSocket;const l=o==="h2";if(i&&(i=l?i.http2:i.https,e.agent=i),i===void 0&&(i=l?_c:br.globalAgent),u)if(i===!1)u.destroy();else{const D=(l?wc:br.Agent).prototype.createConnection;i.createConnection===D?l?e._reuseSocket=u:Sc(i,u,e):u.destroy()}if(l)return Xn(new xc(e,r))}else i&&(e.agent=i.http);return e.headers&&(e.headers={...e.headers},e.headers[":authority"]&&(e.headers.host||(e.headers.host=e.headers[":authority"]),delete e.headers[":authority"]),delete e.headers[":method"],delete e.headers[":scheme"],delete e.headers[":path"]),Xn(Qn.request(e,r))};bt.exports.protocolCache=ys;bt.exports.resolveProtocol=gs;bt.exports.createResolveProtocol=Fs;var vc=bt.exports;const Oc=ae,Pc=Ft,Rc=new Pc.TLSSocket(new Oc.PassThrough)._handle._parentWrap.constructor;var Es=Rc;let Tc=class extends Error{constructor(e,r=""){super(`The proxy server rejected the request with status code ${e} (${r||"empty status message"})`),this.statusCode=e,this.statusMessage=r}};var Cs=Tc;const $c=(t,e,r)=>{if(!r.some(s=>typeof s==="string"?typeof e===s:e instanceof s)){const s=r.map(i=>typeof i=="string"?i:i.name);throw new TypeError(`Expected '${t}' to be a type of ${s.join(" or ")}, got ${typeof e}`)}};var jc=$c;const{URL:Zn}=K,jt=jc;var bs=(t,e)=>{jt("proxyOptions",e,["object"]),jt("proxyOptions.headers",e.headers,["object","undefined"]),jt("proxyOptions.raw",e.raw,["boolean","undefined"]),jt("proxyOptions.url",e.url,[Zn,"string"]);const r=new Zn(e.url);t.proxyOptions={raw:!0,...e,headers:{...e.headers},url:r}},Xr=t=>{const{username:e,password:r}=t.proxyOptions.url;if(e||r){const n=`${e}:${r}`,s=`Basic ${Buffer.from(n).toString("base64")}`;return{"proxy-authorization":s,authorization:s}}return{}};const kc=Ft,Lc=fe,ws=yt,Nc=Es,{globalAgent:Ic}=Je,Uc=Cs,_s=bs,qc=Xr,xs=(t,e,r)=>{(async()=>{try{const{proxyOptions:n}=t,{url:s,headers:i,raw:u}=n,o=await Ic.request(s,n,{...qc(t),...i,":method":"CONNECT",":authority":`${e.host}:${e.port}`});o.once("error",r),o.once("response",c=>{const l=c[":status"];if(l!==200){r(new Uc(l,""));return}const D=t instanceof ws.Agent;if(u&&D){e.socket=o;const h=kc.connect(e);h.once("close",()=>{o.destroy()}),r(null,h);return}const d=new Nc(o);d.encrypted=!1,d._handle.getpeername=h=>{h.family=void 0,h.address=void 0,h.port=void 0},r(null,d)})}catch(n){r(n)}})()};let Hc=class extends Lc.Agent{constructor(e){super(e),_s(this,e.proxyOptions)}createConnection(e,r){xs(this,e,r)}},Mc=class extends ws.Agent{constructor(e){super(e),_s(this,e.proxyOptions)}createConnection(e,r){xs(this,e,r)}};var zc={HttpOverHttp2:Hc,HttpsOverHttp2:Mc};const{Agent:Gc}=Je,Wc=Es,Vc=Cs,Jc=bs;let Kc=class extends Gc{constructor(e){super(e),Jc(this,e.proxyOptions)}async createConnection(e,r){const n=`${e.hostname}:${e.port||443}`,[s,i,u]=await this._getProxyStream(n);if(i!==200)throw new Vc(i,u);if(this.proxyOptions.raw)r.socket=s;else{const o=new Wc(s);return o.encrypted=!1,o._handle.getpeername=c=>{c.family=void 0,c.address=void 0,c.port=void 0},o}return super.createConnection(e,r)}};var As=Kc;const{globalAgent:Yc}=Je,Qc=As,Xc=Xr,Zc=t=>new Promise((e,r)=>{t.once("error",r),t.once("response",n=>{t.off("error",r),e(n[":status"])})});let eD=class extends Qc{async _getProxyStream(e){const{proxyOptions:r}=this,n={...Xc(this),...r.headers,":method":"CONNECT",":authority":e},s=await Yc.request(r.url,r,n),i=await Zc(s);return[s,i,""]}};var tD=eD;const rD=fe,nD=yt,uD=As,sD=Xr,iD=t=>new Promise((e,r)=>{const n=(s,i,u)=>{i.unshift(u),t.off("error",r),e([i,s.statusCode,s.statusMessage])};t.once("error",r),t.once("connect",n)});let eu=class extends uD{async _getProxyStream(e){const{proxyOptions:r}=this,{url:n,headers:s}=this.proxyOptions,u=(n.protocol==="https:"?nD:rD).request({...r,hostname:n.hostname,port:n.port,path:e,headers:{...sD(this),...s,host:e},method:"CONNECT"}).end();return iD(u)}};var oD={Http2OverHttp:eu,Http2OverHttps:eu};const aD=Mr,{Agent:cD,globalAgent:DD}=Je,Zr=ms,lD=fs,fD=vc,{HttpOverHttp2:hD,HttpsOverHttp2:dD}=zc,pD=tD,{Http2OverHttp:mD,Http2OverHttps:yD}=oD,FD=ds,gD=ps,ED=(t,e,r)=>new Zr(t,e,r),CD=(t,e,r)=>{const n=new Zr(t,e,r);return n.end(),n};var bD={...aD,ClientRequest:Zr,IncomingMessage:lD,Agent:cD,globalAgent:DD,request:ED,get:CD,auto:fD,proxies:{HttpOverHttp2:hD,HttpsOverHttp2:dD,Http2OverHttp2:pD,Http2OverHttp:mD,Http2OverHttps:yD},validateHeaderName:FD,validateHeaderValue:gD};const wD=R.getDefaultExportFromCjs(bD);function _D(t){const e=[],r=t.split(",");for(const n of r){const[s,...i]=n.split(";"),u=s.trim();if(u[0]!=="<"||u[u.length-1]!==">")throw new Error(`Invalid format of the Link header reference: ${u}`);const o=u.slice(1,-1),c={};if(i.length===0)throw new Error(`Unexpected end of Link header parameters: ${i.join(";")}`);for(const l of i){const D=l.trim(),d=D.indexOf("=");if(d===-1)throw new Error(`Failed to parse Link header: ${t}`);const h=D.slice(0,d).trim(),p=D.slice(d+1).trim();c[h]=p}e.push({reference:o,parameters:c})}return e}const[tu,xD]=V.versions.node.split(".").map(Number);function AD(t){for(const e in t){const r=t[e];E.any([a.string,a.number,a.boolean,a.null_,a.undefined],r)}}const BD=new Map;let kt;const SD=()=>kt||(kt=new ka,kt),vD={request:void 0,agent:{http:void 0,https:void 0,http2:void 0},h2session:void 0,decompress:!0,timeout:{connect:void 0,lookup:void 0,read:void 0,request:void 0,response:void 0,secureConnect:void 0,send:void 0,socket:void 0},prefixUrl:"",body:void 0,form:void 0,json:void 0,cookieJar:void 0,ignoreInvalidCookies:!1,searchParams:void 0,dnsLookup:void 0,dnsCache:void 0,context:{},hooks:{init:[],beforeRequest:[],beforeError:[],beforeRedirect:[],beforeRetry:[],afterResponse:[]},followRedirect:!0,maxRedirects:10,cache:void 0,throwHttpErrors:!0,username:"",password:"",http2:!1,allowGetBody:!1,headers:{"user-agent":"got (https://github.com/sindresorhus/got)"},methodRewriting:!1,dnsLookupIpVersion:void 0,parseJson:JSON.parse,stringifyJson:JSON.stringify,retry:{limit:2,methods:["GET","PUT","HEAD","DELETE","OPTIONS","TRACE"],statusCodes:[408,413,429,500,502,503,504,521,522,524],errorCodes:["ETIMEDOUT","ECONNRESET","EADDRINUSE","ECONNREFUSED","EPIPE","ENOTFOUND","ENETUNREACH","EAI_AGAIN"],maxRetryAfter:void 0,calculateDelay:({computedValue:t})=>t,backoffLimit:Number.POSITIVE_INFINITY,noise:100},localAddress:void 0,method:"GET",createConnection:void 0,cacheOptions:{shared:void 0,cacheHeuristic:void 0,immutableMinTimeToLive:void 0,ignoreCargoCult:void 0},https:{alpnProtocols:void 0,rejectUnauthorized:void 0,checkServerIdentity:void 0,certificateAuthority:void 0,key:void 0,certificate:void 0,passphrase:void 0,pfx:void 0,ciphers:void 0,honorCipherOrder:void 0,minVersion:void 0,maxVersion:void 0,signatureAlgorithms:void 0,tlsSessionLifetime:void 0,dhparam:void 0,ecdhCurve:void 0,certificateRevocationLists:void 0},encoding:void 0,resolveBodyOnly:!1,isStream:!1,responseType:"text",url:void 0,pagination:{transform(t){return t.request.options.responseType==="json"?t.body:JSON.parse(t.body)},paginate({response:t}){const e=t.headers.link;if(typeof e!="string"||e.trim()==="")return!1;const n=_D(e).find(s=>s.parameters.rel==="next"||s.parameters.rel==='"next"');return n?{url:new K.URL(n.reference,t.url)}:!1},filter:()=>!0,shouldContinue:()=>!0,countLimit:Number.POSITIVE_INFINITY,backoff:0,requestLimit:1e4,stackAllItems:!1},setHost:!0,maxHeaderSize:void 0,signal:void 0,enableUnixSockets:!0},OD=t=>{const{hooks:e,retry:r}=t,n={...t,context:{...t.context},cacheOptions:{...t.cacheOptions},https:{...t.https},agent:{...t.agent},headers:{...t.headers},retry:{...r,errorCodes:[...r.errorCodes],methods:[...r.methods],statusCodes:[...r.statusCodes]},timeout:{...t.timeout},hooks:{init:[...e.init],beforeRequest:[...e.beforeRequest],beforeError:[...e.beforeError],beforeRedirect:[...e.beforeRedirect],beforeRetry:[...e.beforeRetry],afterResponse:[...e.afterResponse]},searchParams:t.searchParams?new K.URLSearchParams(t.searchParams):void 0,pagination:{...t.pagination}};return n.url!==void 0&&(n.prefixUrl=""),n},PD=t=>{const{hooks:e,retry:r}=t,n={...t};return a.object(t.context)&&(n.context={...t.context}),a.object(t.cacheOptions)&&(n.cacheOptions={...t.cacheOptions}),a.object(t.https)&&(n.https={...t.https}),a.object(t.cacheOptions)&&(n.cacheOptions={...n.cacheOptions}),a.object(t.agent)&&(n.agent={...t.agent}),a.object(t.headers)&&(n.headers={...t.headers}),a.object(r)&&(n.retry={...r},a.array(r.errorCodes)&&(n.retry.errorCodes=[...r.errorCodes]),a.array(r.methods)&&(n.retry.methods=[...r.methods]),a.array(r.statusCodes)&&(n.retry.statusCodes=[...r.statusCodes])),a.object(t.timeout)&&(n.timeout={...t.timeout}),a.object(e)&&(n.hooks={...e},a.array(e.init)&&(n.hooks.init=[...e.init]),a.array(e.beforeRequest)&&(n.hooks.beforeRequest=[...e.beforeRequest]),a.array(e.beforeError)&&(n.hooks.beforeError=[...e.beforeError]),a.array(e.beforeRedirect)&&(n.hooks.beforeRedirect=[...e.beforeRedirect]),a.array(e.beforeRetry)&&(n.hooks.beforeRetry=[...e.beforeRetry]),a.array(e.afterResponse)&&(n.hooks.afterResponse=[...e.afterResponse])),a.object(t.pagination)&&(n.pagination={...t.pagination}),n},RD=t=>{const e=[t.timeout.socket,t.timeout.connect,t.timeout.lookup,t.timeout.request,t.timeout.secureConnect].filter(r=>typeof r=="number");if(e.length>0)return Math.min(...e)},ru=(t,e,r)=>{const n=t.hooks?.init;if(n)for(const s of n)s(e,r)};class De{constructor(e,r,n){if(Object.defineProperty(this,"_unixOptions",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_internals",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_merging",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_init",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),E.any([a.string,a.urlInstance,a.object,a.undefined],e),E.any([a.object,a.undefined],r),E.any([a.object,a.undefined],n),e instanceof De||r instanceof De)throw new TypeError("The defaults must be passed as the third argument");this._internals=OD(n?._internals??n??vD),this._init=[...n?._init??[]],this._merging=!1,this._unixOptions=void 0;try{if(a.plainObject(e))try{this.merge(e),this.merge(r)}finally{this.url=e.url}else try{this.merge(r)}finally{if(r?.url!==void 0)if(e===void 0)this.url=r.url;else throw new TypeError("The `url` option is mutually exclusive with the `input` argument");else e!==void 0&&(this.url=e)}}catch(s){throw s.options=this,s}}merge(e){if(e){if(e instanceof De){for(const r of e._init)this.merge(r);return}e=PD(e),ru(this,e,this),ru(e,e,this),this._merging=!0,"isStream"in e&&(this.isStream=e.isStream);try{let r=!1;for(const n in e)if(!(n==="mutableDefaults"||n==="handlers")&&n!=="url"){if(!(n in this))throw new Error(`Unexpected option: ${n}`);this[n]=e[n],r=!0}r&&this._init.push(e)}finally{this._merging=!1}}}get request(){return this._internals.request}set request(e){E.any([a.function_,a.undefined],e),this._internals.request=e}get agent(){return this._internals.agent}set agent(e){E.plainObject(e);for(const r in e){if(!(r in this._internals.agent))throw new TypeError(`Unexpected agent option: ${r}`);E.any([a.object,a.undefined],e[r])}this._merging?Object.assign(this._internals.agent,e):this._internals.agent={...e}}get h2session(){return this._internals.h2session}set h2session(e){this._internals.h2session=e}get decompress(){return this._internals.decompress}set decompress(e){E.boolean(e),this._internals.decompress=e}get timeout(){return this._internals.timeout}set timeout(e){E.plainObject(e);for(const r in e){if(!(r in this._internals.timeout))throw new Error(`Unexpected timeout option: ${r}`);E.any([a.number,a.undefined],e[r])}this._merging?Object.assign(this._internals.timeout,e):this._internals.timeout={...e}}get prefixUrl(){return this._internals.prefixUrl}set prefixUrl(e){if(E.any([a.string,a.urlInstance],e),e===""){this._internals.prefixUrl="";return}if(e=e.toString(),e.endsWith("/")||(e+="/"),this._internals.prefixUrl&&this._internals.url){const{href:r}=this._internals.url;this._internals.url.href=e+r.slice(this._internals.prefixUrl.length)}this._internals.prefixUrl=e}get body(){return this._internals.body}set body(e){E.any([a.string,a.buffer,a.nodeStream,a.generator,a.asyncGenerator,Qr,a.undefined],e),a.nodeStream(e)&&E.truthy(e.readable),e!==void 0&&(E.undefined(this._internals.form),E.undefined(this._internals.json)),this._internals.body=e}get form(){return this._internals.form}set form(e){E.any([a.plainObject,a.undefined],e),e!==void 0&&(E.undefined(this._internals.body),E.undefined(this._internals.json)),this._internals.form=e}get json(){return this._internals.json}set json(e){e!==void 0&&(E.undefined(this._internals.body),E.undefined(this._internals.form)),this._internals.json=e}get url(){return this._internals.url}set url(e){if(E.any([a.string,a.urlInstance,a.undefined],e),e===void 0){this._internals.url=void 0;return}if(a.string(e)&&e.startsWith("/"))throw new Error("`url` must not start with a slash");const r=`${this.prefixUrl}${e.toString()}`,n=new K.URL(r);if(this._internals.url=n,n.protocol==="unix:"&&(n.href=`http://unix${n.pathname}${n.search}`),n.protocol!=="http:"&&n.protocol!=="https:"){const s=new Error(`Unsupported protocol: ${n.protocol}`);throw s.code="ERR_UNSUPPORTED_PROTOCOL",s}if(this._internals.username&&(n.username=this._internals.username,this._internals.username=""),this._internals.password&&(n.password=this._internals.password,this._internals.password=""),this._internals.searchParams&&(n.search=this._internals.searchParams.toString(),this._internals.searchParams=void 0),n.hostname==="unix"){if(!this._internals.enableUnixSockets)throw new Error("Using UNIX domain sockets but option `enableUnixSockets` is not enabled");const s=/(?<socketPath>.+?):(?<path>.+)/.exec(`${n.pathname}${n.search}`);if(s?.groups){const{socketPath:i,path:u}=s.groups;this._unixOptions={socketPath:i,path:u,host:""}}else this._unixOptions=void 0;return}this._unixOptions=void 0}get cookieJar(){return this._internals.cookieJar}set cookieJar(e){if(E.any([a.object,a.undefined],e),e===void 0){this._internals.cookieJar=void 0;return}let{setCookie:r,getCookieString:n}=e;E.function_(r),E.function_(n),r.length===4&&n.length===0?(r=te.promisify(r.bind(e)),n=te.promisify(n.bind(e)),this._internals.cookieJar={setCookie:r,getCookieString:n}):this._internals.cookieJar=e}get signal(){return this._internals.signal}set signal(e){E.object(e),this._internals.signal=e}get ignoreInvalidCookies(){return this._internals.ignoreInvalidCookies}set ignoreInvalidCookies(e){E.boolean(e),this._internals.ignoreInvalidCookies=e}get searchParams(){return this._internals.url?this._internals.url.searchParams:(this._internals.searchParams===void 0&&(this._internals.searchParams=new K.URLSearchParams),this._internals.searchParams)}set searchParams(e){E.any([a.string,a.object,a.undefined],e);const r=this._internals.url;if(e===void 0){this._internals.searchParams=void 0,r&&(r.search="");return}const n=this.searchParams;let s;if(a.string(e))s=new K.URLSearchParams(e);else if(e instanceof K.URLSearchParams)s=e;else{AD(e),s=new K.URLSearchParams;for(const i in e){const u=e[i];u===null?s.append(i,""):u===void 0?n.delete(i):s.append(i,u)}}if(this._merging){for(const i of s.keys())n.delete(i);for(const[i,u]of s)n.append(i,u)}else r?r.search=n.toString():this._internals.searchParams=n}get searchParameters(){throw new Error("The `searchParameters` option does not exist. Use `searchParams` instead.")}set searchParameters(e){throw new Error("The `searchParameters` option does not exist. Use `searchParams` instead.")}get dnsLookup(){return this._internals.dnsLookup}set dnsLookup(e){E.any([a.function_,a.undefined],e),this._internals.dnsLookup=e}get dnsCache(){return this._internals.dnsCache}set dnsCache(e){E.any([a.object,a.boolean,a.undefined],e),e===!0?this._internals.dnsCache=SD():e===!1?this._internals.dnsCache=void 0:this._internals.dnsCache=e}get context(){return this._internals.context}set context(e){E.object(e),this._merging?Object.assign(this._internals.context,e):this._internals.context={...e}}get hooks(){return this._internals.hooks}set hooks(e){E.object(e);for(const r in e){if(!(r in this._internals.hooks))throw new Error(`Unexpected hook event: ${r}`);const n=r,s=e[n];if(E.any([a.array,a.undefined],s),s)for(const i of s)E.function_(i);if(this._merging)s&&this._internals.hooks[n].push(...s);else{if(!s)throw new Error(`Missing hook event: ${r}`);this._internals.hooks[r]=[...s]}}}get followRedirect(){return this._internals.followRedirect}set followRedirect(e){E.boolean(e),this._internals.followRedirect=e}get followRedirects(){throw new TypeError("The `followRedirects` option does not exist. Use `followRedirect` instead.")}set followRedirects(e){throw new TypeError("The `followRedirects` option does not exist. Use `followRedirect` instead.")}get maxRedirects(){return this._internals.maxRedirects}set maxRedirects(e){E.number(e),this._internals.maxRedirects=e}get cache(){return this._internals.cache}set cache(e){E.any([a.object,a.string,a.boolean,a.undefined],e),e===!0?this._internals.cache=BD:e===!1?this._internals.cache=void 0:this._internals.cache=e}get throwHttpErrors(){return this._internals.throwHttpErrors}set throwHttpErrors(e){E.boolean(e),this._internals.throwHttpErrors=e}get username(){const e=this._internals.url,r=e?e.username:this._internals.username;return decodeURIComponent(r)}set username(e){E.string(e);const r=this._internals.url,n=encodeURIComponent(e);r?r.username=n:this._internals.username=n}get password(){const e=this._internals.url,r=e?e.password:this._internals.password;return decodeURIComponent(r)}set password(e){E.string(e);const r=this._internals.url,n=encodeURIComponent(e);r?r.password=n:this._internals.password=n}get http2(){return this._internals.http2}set http2(e){E.boolean(e),this._internals.http2=e}get allowGetBody(){return this._internals.allowGetBody}set allowGetBody(e){E.boolean(e),this._internals.allowGetBody=e}get headers(){return this._internals.headers}set headers(e){E.plainObject(e),this._merging?Object.assign(this._internals.headers,Nr(e)):this._internals.headers=Nr(e)}get methodRewriting(){return this._internals.methodRewriting}set methodRewriting(e){E.boolean(e),this._internals.methodRewriting=e}get dnsLookupIpVersion(){return this._internals.dnsLookupIpVersion}set dnsLookupIpVersion(e){if(e!==void 0&&e!==4&&e!==6)throw new TypeError(`Invalid DNS lookup IP version: ${e}`);this._internals.dnsLookupIpVersion=e}get parseJson(){return this._internals.parseJson}set parseJson(e){E.function_(e),this._internals.parseJson=e}get stringifyJson(){return this._internals.stringifyJson}set stringifyJson(e){E.function_(e),this._internals.stringifyJson=e}get retry(){return this._internals.retry}set retry(e){if(E.plainObject(e),E.any([a.function_,a.undefined],e.calculateDelay),E.any([a.number,a.undefined],e.maxRetryAfter),E.any([a.number,a.undefined],e.limit),E.any([a.array,a.undefined],e.methods),E.any([a.array,a.undefined],e.statusCodes),E.any([a.array,a.undefined],e.errorCodes),E.any([a.number,a.undefined],e.noise),e.noise&&Math.abs(e.noise)>100)throw new Error(`The maximum acceptable retry noise is +/- 100ms, got ${e.noise}`);for(const n in e)if(!(n in this._internals.retry))throw new Error(`Unexpected retry option: ${n}`);this._merging?Object.assign(this._internals.retry,e):this._internals.retry={...e};const{retry:r}=this._internals;r.methods=[...new Set(r.methods.map(n=>n.toUpperCase()))],r.statusCodes=[...new Set(r.statusCodes)],r.errorCodes=[...new Set(r.errorCodes)]}get localAddress(){return this._internals.localAddress}set localAddress(e){E.any([a.string,a.undefined],e),this._internals.localAddress=e}get method(){return this._internals.method}set method(e){E.string(e),this._internals.method=e.toUpperCase()}get createConnection(){return this._internals.createConnection}set createConnection(e){E.any([a.function_,a.undefined],e),this._internals.createConnection=e}get cacheOptions(){return this._internals.cacheOptions}set cacheOptions(e){E.plainObject(e),E.any([a.boolean,a.undefined],e.shared),E.any([a.number,a.undefined],e.cacheHeuristic),E.any([a.number,a.undefined],e.immutableMinTimeToLive),E.any([a.boolean,a.undefined],e.ignoreCargoCult);for(const r in e)if(!(r in this._internals.cacheOptions))throw new Error(`Cache option \`${r}\` does not exist`);this._merging?Object.assign(this._internals.cacheOptions,e):this._internals.cacheOptions={...e}}get https(){return this._internals.https}set https(e){E.plainObject(e),E.any([a.boolean,a.undefined],e.rejectUnauthorized),E.any([a.function_,a.undefined],e.checkServerIdentity),E.any([a.string,a.object,a.array,a.undefined],e.certificateAuthority),E.any([a.string,a.object,a.array,a.undefined],e.key),E.any([a.string,a.object,a.array,a.undefined],e.certificate),E.any([a.string,a.undefined],e.passphrase),E.any([a.string,a.buffer,a.array,a.undefined],e.pfx),E.any([a.array,a.undefined],e.alpnProtocols),E.any([a.string,a.undefined],e.ciphers),E.any([a.string,a.buffer,a.undefined],e.dhparam),E.any([a.string,a.undefined],e.signatureAlgorithms),E.any([a.string,a.undefined],e.minVersion),E.any([a.string,a.undefined],e.maxVersion),E.any([a.boolean,a.undefined],e.honorCipherOrder),E.any([a.number,a.undefined],e.tlsSessionLifetime),E.any([a.string,a.undefined],e.ecdhCurve),E.any([a.string,a.buffer,a.array,a.undefined],e.certificateRevocationLists);for(const r in e)if(!(r in this._internals.https))throw new Error(`HTTPS option \`${r}\` does not exist`);this._merging?Object.assign(this._internals.https,e):this._internals.https={...e}}get encoding(){return this._internals.encoding}set encoding(e){if(e===null)throw new TypeError("To get a Buffer, set `options.responseType` to `buffer` instead");E.any([a.string,a.undefined],e),this._internals.encoding=e}get resolveBodyOnly(){return this._internals.resolveBodyOnly}set resolveBodyOnly(e){E.boolean(e),this._internals.resolveBodyOnly=e}get isStream(){return this._internals.isStream}set isStream(e){E.boolean(e),this._internals.isStream=e}get responseType(){return this._internals.responseType}set responseType(e){if(e===void 0){this._internals.responseType="text";return}if(e!=="text"&&e!=="buffer"&&e!=="json")throw new Error(`Invalid \`responseType\` option: ${e}`);this._internals.responseType=e}get pagination(){return this._internals.pagination}set pagination(e){E.object(e),this._merging?Object.assign(this._internals.pagination,e):this._internals.pagination=e}get auth(){throw new Error("Parameter `auth` is deprecated. Use `username` / `password` instead.")}set auth(e){throw new Error("Parameter `auth` is deprecated. Use `username` / `password` instead.")}get setHost(){return this._internals.setHost}set setHost(e){E.boolean(e),this._internals.setHost=e}get maxHeaderSize(){return this._internals.maxHeaderSize}set maxHeaderSize(e){E.any([a.number,a.undefined],e),this._internals.maxHeaderSize=e}get enableUnixSockets(){return this._internals.enableUnixSockets}set enableUnixSockets(e){E.boolean(e),this._internals.enableUnixSockets=e}toJSON(){return{...this._internals}}[Symbol.for("nodejs.util.inspect.custom")](e,r){return te.inspect(this._internals,r)}createNativeRequestOptions(){const e=this._internals,r=e.url;let n;r.protocol==="https:"?n=e.http2?e.agent:e.agent.https:n=e.agent.http;const{https:s}=e;let{pfx:i}=s;return a.array(i)&&a.plainObject(i[0])&&(i=i.map(u=>({buf:u.buffer,passphrase:u.passphrase}))),{...e.cacheOptions,...this._unixOptions,ALPNProtocols:s.alpnProtocols,ca:s.certificateAuthority,cert:s.certificate,key:s.key,passphrase:s.passphrase,pfx:s.pfx,rejectUnauthorized:s.rejectUnauthorized,checkServerIdentity:s.checkServerIdentity??Ft.checkServerIdentity,ciphers:s.ciphers,honorCipherOrder:s.honorCipherOrder,minVersion:s.minVersion,maxVersion:s.maxVersion,sigalgs:s.signatureAlgorithms,sessionTimeout:s.tlsSessionLifetime,dhparam:s.dhparam,ecdhCurve:s.ecdhCurve,crl:s.certificateRevocationLists,lookup:e.dnsLookup??e.dnsCache?.lookup,family:e.dnsLookupIpVersion,agent:n,setHost:e.setHost,method:e.method,maxHeaderSize:e.maxHeaderSize,localAddress:e.localAddress,headers:e.headers,createConnection:e.createConnection,timeout:e.http2?RD(e):void 0,h2session:e.h2session}}getRequestFunction(){const e=this._internals.url,{request:r}=this._internals;return!r&&e?this.getFallbackRequestFunction():r}getFallbackRequestFunction(){const e=this._internals.url;if(e){if(e.protocol==="https:"){if(this._internals.http2){if(tu<15||tu===15&&xD<10){const r=new Error("To use the `http2` option, install Node.js 15.10.0 or above");throw r.code="EUNSUPPORTED",r}return wD.auto}return yt.request}return fe.request}}freeze(){const e=this._internals;Object.freeze(e),Object.freeze(e.hooks),Object.freeze(e.hooks.afterResponse),Object.freeze(e.hooks.beforeError),Object.freeze(e.hooks.beforeRedirect),Object.freeze(e.hooks.beforeRequest),Object.freeze(e.hooks.beforeRetry),Object.freeze(e.hooks.init),Object.freeze(e.https),Object.freeze(e.cacheOptions),Object.freeze(e.agent),Object.freeze(e.headers),Object.freeze(e.timeout),Object.freeze(e.retry),Object.freeze(e.retry.errorCodes),Object.freeze(e.retry.methods),Object.freeze(e.retry.statusCodes)}}const Yt=t=>{const{statusCode:e}=t,r=t.request.options.followRedirect?299:399;return e>=200&&e<=r||e===304};class nu extends Y{constructor(e,r){const{options:n}=r.request;super(`${e.message} in "${n.url.toString()}"`,e,r.request),this.name="ParseError",this.code="ERR_BODY_PARSE_FAILURE"}}const uu=(t,e,r,n)=>{const{rawBody:s}=t;try{if(e==="text")return s.toString(n);if(e==="json")return s.length===0?"":r(s.toString(n));if(e==="buffer")return s}catch(i){throw new nu(i,t)}throw new nu({message:`Unknown body type '${e}'`,name:"Error"},t)};function TD(t){return t.writable&&!t.writableEnded}function su(t){return t.protocol==="unix:"||t.hostname==="unix"}const{buffer:$D}=us,jD=a.string(V.versions.brotli),kD=new Set(["GET","HEAD"]),wr=new Sa,LD=new Set([300,301,302,303,304,307,308]),ND=["socket","connect","continue","information","upgrade"],Lt=()=>{};class nr extends ae.Duplex{constructor(e,r,n){super({autoDestroy:!1,highWaterMark:0}),Object.defineProperty(this,"constructor",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_noPipe",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"options",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"response",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"requestUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"redirectUrls",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"retryCount",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_stopRetry",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_downloadedSize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_uploadedSize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_stopReading",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_pipedServerResponses",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_request",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_responseSize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_bodySize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_unproxyEvents",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_isFromCache",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_cannotHaveBody",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_triggerRead",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_cancelTimeouts",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_removeListeners",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_nativeResponse",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_flushed",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_aborted",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_requestInitialized",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this._downloadedSize=0,this._uploadedSize=0,this._stopReading=!1,this._pipedServerResponses=new Set,this._cannotHaveBody=!1,this._unproxyEvents=Lt,this._triggerRead=!1,this._cancelTimeouts=Lt,this._removeListeners=Lt,this._jobs=[],this._flushed=!1,this._requestInitialized=!1,this._aborted=!1,this.redirectUrls=[],this.retryCount=0,this._stopRetry=Lt,this.on("pipe",i=>{i?.headers&&Object.assign(this.options.headers,i.headers)}),this.on("newListener",i=>{if(i==="retry"&&this.listenerCount("retry")>0)throw new Error("A retry listener has been attached already.")});try{if(this.options=new De(e,r,n),!this.options.url){if(this.options.prefixUrl==="")throw new TypeError("Missing `url` property");this.options.url=""}this.requestUrl=this.options.url}catch(i){const{options:u}=i;u&&(this.options=u),this.flush=async()=>{this.flush=async()=>{},this.destroy(i)};return}const{body:s}=this.options;if(a.nodeStream(s)&&s.once("error",i=>{this._flushed?this._beforeError(new Cn(i,this)):this.flush=async()=>{this.flush=async()=>{},this._beforeError(new Cn(i,this))}}),this.options.signal){const i=()=>{this.destroy(new To(this))};this.options.signal.aborted?i():(this.options.signal.addEventListener("abort",i),this._removeListeners=()=>{this.options.signal.removeEventListener("abort",i)})}}async flush(){if(!this._flushed){this._flushed=!0;try{if(await this._finalizeBody(),this.destroyed)return;if(await this._makeRequest(),this.destroyed){this._request?.destroy();return}for(const e of this._jobs)e();this._jobs.length=0,this._requestInitialized=!0}catch(e){this._beforeError(e)}}}_beforeError(e){if(this._stopReading)return;const{response:r,options:n}=this,s=this.retryCount+(e.name==="RetryError"?0:1);this._stopReading=!0,e instanceof Y||(e=new Y(e.message,e,this));const i=e;(async()=>{if(r?.readable&&!r.rawBody&&!this._request?.socket?.destroyed&&(r.setEncoding(this.readableEncoding),await this._setRawBody(r)&&(r.body=r.rawBody.toString())),this.listenerCount("retry")!==0){let u;try{let o;r&&"retry-after"in r.headers&&(o=Number(r.headers["retry-after"]),Number.isNaN(o)?(o=Date.parse(r.headers["retry-after"])-Date.now(),o<=0&&(o=1)):o*=1e3);const c=n.retry;u=await c.calculateDelay({attemptCount:s,retryOptions:c,error:i,retryAfter:o,computedValue:va({attemptCount:s,retryOptions:c,error:i,retryAfter:o,computedValue:c.maxRetryAfter??n.timeout.request??Number.POSITIVE_INFINITY})})}catch(o){this._error(new Y(o.message,o,this));return}if(u){if(await new Promise(o=>{const c=setTimeout(o,u);this._stopRetry=()=>{clearTimeout(c),o()}}),this.destroyed)return;try{for(const o of this.options.hooks.beforeRetry)await o(i,this.retryCount+1)}catch(o){this._error(new Y(o.message,e,this));return}if(this.destroyed)return;this.destroy(),this.emit("retry",this.retryCount+1,e,o=>{const c=new nr(n.url,o,n);return c.retryCount=this.retryCount+1,V.nextTick(()=>{c.flush()}),c});return}}this._error(i)})()}_read(){this._triggerRead=!0;const{response:e}=this;if(e&&!this._stopReading){e.readableLength&&(this._triggerRead=!1);let r;for(;(r=e.read())!==null;){this._downloadedSize+=r.length;const n=this.downloadProgress;n.percent<1&&this.emit("downloadProgress",n),this.push(r)}}}_write(e,r,n){const s=()=>{this._writeRequest(e,r,n)};this._requestInitialized?s():this._jobs.push(s)}_final(e){const r=()=>{if(!this._request||this._request.destroyed){e();return}this._request.end(n=>{this._request._writableState?.errored||(n||(this._bodySize=this._uploadedSize,this.emit("uploadProgress",this.uploadProgress),this._request.emit("upload-complete")),e(n))})};this._requestInitialized?r():this._jobs.push(r)}_destroy(e,r){if(this._stopReading=!0,this.flush=async()=>{},this._stopRetry(),this._cancelTimeouts(),this._removeListeners(),this.options){const{body:n}=this.options;a.nodeStream(n)&&n.destroy()}this._request&&this._request.destroy(),e!==null&&!a.undefined(e)&&!(e instanceof Y)&&(e=new Y(e.message,e,this)),r(e)}pipe(e,r){return e instanceof fe.ServerResponse&&this._pipedServerResponses.add(e),super.pipe(e,r)}unpipe(e){return e instanceof fe.ServerResponse&&this._pipedServerResponses.delete(e),super.unpipe(e),this}async _finalizeBody(){const{options:e}=this,{headers:r}=e,n=!a.undefined(e.form),s=!a.undefined(e.json),i=!a.undefined(e.body),u=kD.has(e.method)&&!(e.method==="GET"&&e.allowGetBody);if(this._cannotHaveBody=u,n||s||i){if(u)throw new TypeError(`The \`${e.method}\` method cannot be used with a body`);const o=!a.string(r["content-type"]);if(i){if(Qr(e.body)){const l=new ba(e.body);o&&(r["content-type"]=l.headers["Content-Type"]),"Content-Length"in l.headers&&(r["content-length"]=l.headers["Content-Length"]),e.body=l.encode()}ss(e.body)&&o&&(r["content-type"]=`multipart/form-data; boundary=${e.body.getBoundary()}`)}else if(n){o&&(r["content-type"]="application/x-www-form-urlencoded");const{form:l}=e;e.form=void 0,e.body=new K.URLSearchParams(l).toString()}else{o&&(r["content-type"]="application/json");const{json:l}=e;e.json=void 0,e.body=e.stringifyJson(l)}const c=await wa(e.body,e.headers);a.undefined(r["content-length"])&&a.undefined(r["transfer-encoding"])&&!u&&!a.undefined(c)&&(r["content-length"]=String(c))}e.responseType==="json"&&!("accept"in e.headers)&&(e.headers.accept="application/json"),this._bodySize=Number(r["content-length"])||void 0}async _onResponseBase(e){if(this.isAborted)return;const{options:r}=this,{url:n}=r;this._nativeResponse=e,r.decompress&&(e=da(e));const s=e.statusCode,i=e;i.statusMessage=i.statusMessage??fe.STATUS_CODES[s],i.url=r.url.toString(),i.requestUrl=this.requestUrl,i.redirectUrls=this.redirectUrls,i.request=this,i.isFromCache=this._nativeResponse.fromCache??!1,i.ip=this.ip,i.retryCount=this.retryCount,i.ok=Yt(i),this._isFromCache=i.isFromCache,this._responseSize=Number(e.headers["content-length"])||void 0,this.response=i,e.once("end",()=>{this._responseSize=this._downloadedSize,this.emit("downloadProgress",this.downloadProgress)}),e.once("error",o=>{this._aborted=!0,e.destroy(),this._beforeError(new bn(o,this))}),e.once("aborted",()=>{this._aborted=!0,this._beforeError(new bn({name:"Error",message:"The server aborted pending request",code:"ECONNRESET"},this))}),this.emit("downloadProgress",this.downloadProgress);const u=e.headers["set-cookie"];if(a.object(r.cookieJar)&&u){let o=u.map(async c=>r.cookieJar.setCookie(c,n.toString()));r.ignoreInvalidCookies&&(o=o.map(async c=>{try{await c}catch{}}));try{await Promise.all(o)}catch(c){this._beforeError(c);return}}if(!this.isAborted){if(r.followRedirect&&e.headers.location&&LD.has(s)){if(e.resume(),this._cancelTimeouts(),this._unproxyEvents(),this.redirectUrls.length>=r.maxRedirects){this._beforeError(new vo(this));return}this._request=void 0;const o=new De(void 0,void 0,this.options),c=s===303&&o.method!=="GET"&&o.method!=="HEAD",l=s!==307&&s!==308,D=o.methodRewriting&&l;(c||D)&&(o.method="GET",o.body=void 0,o.json=void 0,o.form=void 0,delete o.headers["content-length"]);try{const d=ht.Buffer.from(e.headers.location,"binary").toString(),h=new K.URL(d,n);if(!su(n)&&su(h)){this._beforeError(new Y("Cannot redirect to UNIX socket",{},this));return}h.hostname!==n.hostname||h.port!==n.port?("host"in o.headers&&delete o.headers.host,"cookie"in o.headers&&delete o.headers.cookie,"authorization"in o.headers&&delete o.headers.authorization,(o.username||o.password)&&(o.username="",o.password="")):(h.username=o.username,h.password=o.password),this.redirectUrls.push(h),o.prefixUrl="",o.url=h;for(const p of o.hooks.beforeRedirect)await p(o,i);this.emit("redirect",o,i),this.options=o,await this._makeRequest()}catch(d){this._beforeError(d);return}return}if(r.isStream&&r.throwHttpErrors&&!Yt(i)){this._beforeError(new Jt(i));return}if(e.on("readable",()=>{this._triggerRead&&this._read()}),this.on("resume",()=>{e.resume()}),this.on("pause",()=>{e.pause()}),e.once("end",()=>{this.push(null)}),this._noPipe){await this._setRawBody()&&this.emit("response",e);return}this.emit("response",e);for(const o of this._pipedServerResponses)if(!o.headersSent){for(const c in e.headers){const l=r.decompress?c!=="content-encoding":!0,D=e.headers[c];l&&o.setHeader(c,D)}o.statusCode=s}}}async _setRawBody(e=this){if(e.readableEnded)return!1;try{const r=await $D(e);if(!this.isAborted)return this.response.rawBody=r,!0}catch{}return!1}async _onResponse(e){try{await this._onResponseBase(e)}catch(r){this._beforeError(r)}}_onRequest(e){const{options:r}=this,{timeout:n,url:s}=r;ko(e),this.options.http2&&e.setTimeout(0),this._cancelTimeouts=Aa(e,n,s);const i=r.cache?"cacheableResponse":"response";e.once(i,u=>{this._onResponse(u)}),e.once("error",u=>{this._aborted=!0,e.destroy(),u=u instanceof os?new Po(u,this.timings,this):new Y(u.message,u,this),this._beforeError(u)}),this._unproxyEvents=is(e,this,ND),this._request=e,this.emit("uploadProgress",this.uploadProgress),this._sendBody(),this.emit("request",e)}async _asyncWrite(e){return new Promise((r,n)=>{super.write(e,s=>{if(s){n(s);return}r()})})}_sendBody(){const{body:e}=this.options,r=this.redirectUrls.length===0?this:this._request??this;a.nodeStream(e)?e.pipe(r):a.generator(e)||a.asyncGenerator(e)?(async()=>{try{for await(const n of e)await this._asyncWrite(n);super.end()}catch(n){this._beforeError(n)}})():a.undefined(e)?(this._cannotHaveBody||this._noPipe)&&r.end():(this._writeRequest(e,void 0,()=>{}),r.end())}_prepareCache(e){if(!wr.has(e)){const r=new ua((n,s)=>{const i=n._request(n,s);return a.promise(i)&&(i.once=(u,o)=>{if(u==="error")(async()=>{try{await i}catch(c){o(c)}})();else if(u==="abort")(async()=>{try{(await i).once("abort",o)}catch{}})();else throw new Error(`Unknown HTTP2 promise event: ${u}`);return i}),i},e);wr.set(e,r.request())}}async _createCacheableRequest(e,r){return new Promise((n,s)=>{Object.assign(r,Ba(e));let i;const u=wr.get(r.cache)(r,async o=>{if(o._readableState.autoDestroy=!1,i){const c=()=>{o.req&&(o.complete=o.req.res.complete)};o.prependOnceListener("end",c),c(),(await i).emit("cacheableResponse",o)}n(o)});u.once("error",s),u.once("request",async o=>{i=o,n(i)})})}async _makeRequest(){const{options:e}=this,{headers:r,username:n,password:s}=e,i=e.cookieJar;for(const l in r)if(a.undefined(r[l]))delete r[l];else if(a.null_(r[l]))throw new TypeError(`Use \`undefined\` instead of \`null\` to delete the \`${l}\` header`);if(e.decompress&&a.undefined(r["accept-encoding"])&&(r["accept-encoding"]=jD?"gzip, deflate, br":"gzip, deflate"),n||s){const l=ht.Buffer.from(`${n}:${s}`).toString("base64");r.authorization=`Basic ${l}`}if(i){const l=await i.getCookieString(e.url.toString());a.nonEmptyString(l)&&(r.cookie=l)}e.prefixUrl="";let u;for(const l of e.hooks.beforeRequest){const D=await l(e);if(!a.undefined(D)){u=()=>D;break}}u||(u=e.getRequestFunction());const o=e.url;this._requestOptions=e.createNativeRequestOptions(),e.cache&&(this._requestOptions._request=u,this._requestOptions.cache=e.cache,this._requestOptions.body=e.body,this._prepareCache(e.cache));const c=e.cache?this._createCacheableRequest:u;try{let l=c(o,this._requestOptions);a.promise(l)&&(l=await l),a.undefined(l)&&(l=e.getFallbackRequestFunction()(o,this._requestOptions),a.promise(l)&&(l=await l)),TD(l)?this._onRequest(l):this.writable?(this.once("finish",()=>{this._onResponse(l)}),this._sendBody()):this._onResponse(l)}catch(l){throw l instanceof Dt?new Oo(l,this):l}}async _error(e){try{if(!(e instanceof Jt&&!this.options.throwHttpErrors))for(const r of this.options.hooks.beforeError)e=await r(e)}catch(r){e=new Y(r.message,r,this)}this.destroy(e)}_writeRequest(e,r,n){!this._request||this._request.destroyed||this._request.write(e,r,s=>{if(!s&&!this._request.destroyed){this._uploadedSize+=ht.Buffer.byteLength(e,r);const i=this.uploadProgress;i.percent<1&&this.emit("uploadProgress",i)}n(s)})}get ip(){return this.socket?.remoteAddress}get isAborted(){return this._aborted}get socket(){return this._request?.socket??void 0}get downloadProgress(){let e;return this._responseSize?e=this._downloadedSize/this._responseSize:this._responseSize===this._downloadedSize?e=1:e=0,{percent:e,transferred:this._downloadedSize,total:this._responseSize}}get uploadProgress(){let e;return this._bodySize?e=this._uploadedSize/this._bodySize:this._bodySize===this._uploadedSize?e=1:e=0,{percent:e,transferred:this._uploadedSize,total:this._bodySize}}get timings(){return this._request?.timings}get isFromCache(){return this._isFromCache}get reusedSocket(){return this._request?.reusedSocket}}class ID extends Y{constructor(e){super("Promise was canceled",{},e),this.name="CancelError",this.code="ERR_CANCELED"}get isCanceled(){return!0}}const UD=["request","response","redirect","uploadProgress","downloadProgress"];function iu(t){let e,r,n;const s=new Re.EventEmitter,i=new tr((o,c,l)=>{l(()=>{e.destroy()}),l.shouldReject=!1,l(()=>{c(new ID(e))});const D=d=>{l(()=>{});const h=t??new nr(void 0,void 0,n);h.retryCount=d,h._noPipe=!0,e=h,h.once("response",async f=>{const F=(f.headers["content-encoding"]??"").toLowerCase(),y=F==="gzip"||F==="deflate"||F==="br",{options:x}=h;if(y&&!x.decompress)f.body=f.rawBody;else try{f.body=uu(f,x.responseType,x.parseJson,x.encoding)}catch(C){if(f.body=f.rawBody.toString(),Yt(f)){h._beforeError(C);return}}try{const C=x.hooks.afterResponse;for(const[A,N]of C.entries())if(f=await N(f,async k=>{throw x.merge(k),x.prefixUrl="",k.url&&(x.url=k.url),x.hooks.afterResponse=x.hooks.afterResponse.slice(0,A),new Ro(h)}),!(a.object(f)&&a.number(f.statusCode)&&!a.nullOrUndefined(f.body)))throw new TypeError("The `afterResponse` hook returned an invalid value")}catch(C){h._beforeError(C);return}if(r=f,!Yt(f)){h._beforeError(new Jt(f));return}h.destroy(),o(h.options.resolveBodyOnly?f.body:f)});const p=f=>{if(i.isCanceled)return;const{options:F}=h;if(f instanceof Jt&&!F.throwHttpErrors){const{response:y}=f;h.destroy(),o(h.options.resolveBodyOnly?y.body:y);return}c(f)};h.once("error",p);const m=h.options?.body;h.once("retry",(f,F)=>{t=void 0;const y=h.options.body;if(m===y&&a.nodeStream(y)){F.message="Cannot retry with consumed body stream",p(F);return}n=h.options,D(f)}),is(h,s,UD),a.undefined(t)&&h.flush()};D(0)});i.on=(o,c)=>(s.on(o,c),i),i.off=(o,c)=>(s.off(o,c),i);const u=o=>{const c=(async()=>{await i;const{options:l}=r.request;return uu(r,o,l.parseJson,l.encoding)})();return Object.defineProperties(c,Object.getOwnPropertyDescriptors(i)),c};return i.json=()=>{if(e.options){const{headers:o}=e.options;!e.writableFinished&&!("accept"in o)&&(o.accept="application/json")}return u("json")},i.buffer=()=>u("buffer"),i.text=()=>u("text"),i}const qD=async t=>new Promise(e=>{setTimeout(e,t)}),HD=t=>a.function_(t),MD=["get","post","put","patch","head","delete"],Bs=t=>{t={options:new De(void 0,void 0,t.options),handlers:[...t.handlers],mutableDefaults:t.mutableDefaults},Object.defineProperty(t,"mutableDefaults",{enumerable:!0,configurable:!1,writable:!1});const e=(n,s,i=t.options)=>{const u=new nr(n,s,i);let o;const c=d=>(u.options=d,u._noPipe=!d.isStream,u.flush(),d.isStream?u:(o||(o=iu(u)),o));let l=0;const D=d=>{const p=(t.handlers[l++]??c)(d,D);if(a.promise(p)&&!u.options.isStream&&(o||(o=iu(u)),p!==o)){const m=Object.getOwnPropertyDescriptors(o);for(const f in m)f in p&&delete m[f];Object.defineProperties(p,m),p.cancel=o.cancel}return p};return D(u.options)};e.extend=(...n)=>{const s=new De(void 0,void 0,t.options),i=[...t.handlers];let u;for(const o of n)HD(o)?(s.merge(o.defaults.options),i.push(...o.defaults.handlers),u=o.defaults.mutableDefaults):(s.merge(o),o.handlers&&i.push(...o.handlers),u=o.mutableDefaults);return Bs({options:s,handlers:i,mutableDefaults:!!u})};const r=async function*(n,s){let i=new De(n,s,t.options);i.resolveBodyOnly=!1;const{pagination:u}=i;E.function_(u.transform),E.function_(u.shouldContinue),E.function_(u.filter),E.function_(u.paginate),E.number(u.countLimit),E.number(u.requestLimit),E.number(u.backoff);const o=[];let{countLimit:c}=u,l=0;for(;l<u.requestLimit;){l!==0&&await qD(u.backoff);const D=await e(void 0,void 0,i),d=await u.transform(D),h=[];E.array(d);for(const m of d)if(u.filter({item:m,currentItems:h,allItems:o})&&(!u.shouldContinue({item:m,currentItems:h,allItems:o})||(yield m,u.stackAllItems&&o.push(m),h.push(m),--c<=0)))return;const p=u.paginate({response:D,currentItems:h,allItems:o});if(p===!1)return;p===D.request.options?i=D.request.options:(i.merge(p),E.any([a.urlInstance,a.undefined],p.url),p.url!==void 0&&(i.prefixUrl="",i.url=p.url)),l++}};e.paginate=r,e.paginate.all=async(n,s)=>{const i=[];for await(const u of r(n,s))i.push(u);return i},e.paginate.each=r,e.stream=(n,s)=>e(n,{...s,isStream:!0});for(const n of MD)e[n]=(s,i)=>e(s,{...i,method:n}),e.stream[n]=(s,i)=>e(s,{...i,method:n,isStream:!0});return t.mutableDefaults||(Object.freeze(t.handlers),t.options.freeze()),Object.defineProperty(e,"defaults",{value:t,writable:!1,configurable:!1,enumerable:!0}),e},zD={options:new De,handlers:[],mutableDefaults:!1},GD=Bs(zD);var Ke={},ve={};ve.parse=ve.decode=WD;ve.stringify=ve.encode=Ss;ve.safe=Me;ve.unsafe=qt;var _r=typeof process<"u"&&process.platform==="win32"?`\r
`:`
`;function Ss(t,e){var r=[],n="";typeof e=="string"?e={section:e,whitespace:!1}:(e=e||{},e.whitespace=e.whitespace===!0);var s=e.whitespace?" = ":"=";return Object.keys(t).forEach(function(i,u,o){var c=t[i];c&&Array.isArray(c)?c.forEach(function(l){n+=Me(i+"[]")+s+Me(l)+`
`}):c&&typeof c=="object"?r.push(i):n+=Me(i)+s+Me(c)+_r}),e.section&&n.length&&(n="["+Me(e.section)+"]"+_r+n),r.forEach(function(i,u,o){var c=vs(i).join("\\."),l=(e.section?e.section+".":"")+c,D=Ss(t[i],{section:l,whitespace:e.whitespace});n.length&&D.length&&(n+=_r),n+=D}),n}function vs(t){return t.replace(/\1/g,"LITERAL\\1LITERAL").replace(/\\\./g,"").split(/\./).map(function(e){return e.replace(/\1/g,"\\.").replace(/\2LITERAL\\1LITERAL\2/g,"")})}function WD(t){var e={},r=e,n=null,s=/^\[([^\]]*)\]$|^([^=]+)(=(.*))?$/i,i=t.split(/[\r\n]+/g);return i.forEach(function(u,o,c){if(!(!u||u.match(/^\s*[;#]/))){var l=u.match(s);if(l){if(l[1]!==void 0){if(n=qt(l[1]),n==="__proto__"){r={};return}r=e[n]=e[n]||{};return}var D=qt(l[2]);if(D!=="__proto__"){var d=l[3]?qt(l[4]):!0;switch(d){case"true":case"false":case"null":d=JSON.parse(d)}if(D.length>2&&D.slice(-2)==="[]"){if(D=D.substring(0,D.length-2),D==="__proto__")return;r[D]?Array.isArray(r[D])||(r[D]=[r[D]]):r[D]=[]}Array.isArray(r[D])?r[D].push(d):r[D]=d}}}}),Object.keys(e).filter(function(u,o,c){if(!e[u]||typeof e[u]!="object"||Array.isArray(e[u]))return!1;var l=vs(u),D=e,d=l.pop(),h=d.replace(/\\\./g,".");return l.forEach(function(p,m,f){p!=="__proto__"&&((!D[p]||typeof D[p]!="object")&&(D[p]={}),D=D[p])}),D===e&&h===d?!1:(D[h]=e[u],!0)}).forEach(function(u,o,c){delete e[u]}),e}function Os(t){return t.charAt(0)==='"'&&t.slice(-1)==='"'||t.charAt(0)==="'"&&t.slice(-1)==="'"}function Me(t){return typeof t!="string"||t.match(/[=\r\n]/)||t.match(/^\[/)||t.length>1&&Os(t)||t!==t.trim()?JSON.stringify(t):t.replace(/;/g,"\\;").replace(/#/g,"\\#")}function qt(t,e){if(t=(t||"").trim(),Os(t)){t.charAt(0)==="'"&&(t=t.substr(1,t.length-2));try{t=JSON.parse(t)}catch{}}else{for(var r=!1,n="",s=0,i=t.length;s<i;s++){var u=t.charAt(s);if(r)"\\;#".indexOf(u)!==-1?n+=u:n+="\\"+u,r=!1;else{if(";#".indexOf(u)!==-1)break;u==="\\"?r=!0:n+=u}}return r&&(n+="\\"),n.trim()}return t}var xr=1,ou=2;function VD(){return""}function JD(t,e,r){return t.slice(e,r).replace(/\S/g," ")}var KD=function(t,e){e=e||{};for(var r,n,s=!1,i=!1,u=0,o="",c=e.whitespace===!1?VD:JD,l=0;l<t.length;l++){if(r=t[l],n=t[l+1],!i&&r==='"'){var D=t[l-1]==="\\"&&t[l-2]!=="\\";D||(s=!s)}if(!s){if(!i&&r+n==="//")o+=t.slice(u,l),u=l,i=xr,l++;else if(i===xr&&r+n===`\r
`){l++,i=!1,o+=c(t,u,l),u=l;continue}else if(i===xr&&r===`
`)i=!1,o+=c(t,u,l),u=l;else if(!i&&r+n==="/*"){o+=t.slice(u,l),u=l,i=ou,l++;continue}else if(i===ou&&r+n==="*/"){l++,i=!1,o+=c(t,u,l+1),u=l+1;continue}}}return o+(i?c(t.substr(u)):t.substr(u))},Ps=he,YD=ve,lt=J,QD=KD,XD=Ke.parse=function(t){return/^\s*{/.test(t)?JSON.parse(QD(t)):YD.parse(t)},ZD=Ke.file=function(){var t=[].slice.call(arguments).filter(function(n){return n!=null});for(var e in t)if(typeof t[e]!="string")return;var r=lt.join.apply(null,t);try{return Ps.readFileSync(r,"utf-8")}catch{return}};Ke.json=function(){var t=ZD.apply(null,arguments);return t?XD(t):null};Ke.env=function(t,e){e=e||process.env;var r={},n=t.length;for(var s in e)if(s.toLowerCase().indexOf(t.toLowerCase())===0){for(var i=s.substring(n).split("__"),u;(u=i.indexOf(""))>-1;)i.splice(u,1);var o=r;i.forEach(function(l,D){!l||typeof o!="object"||(D===i.length-1&&(o[l]=e[s]),o[l]===void 0&&(o[l]={}),o=o[l])})}return r};Ke.find=function(){var t=lt.join.apply(null,[].slice.call(arguments));function e(r,n){var s=lt.join(r,n);try{return Ps.statSync(s),s}catch{if(lt.dirname(r)!==r)return e(lt.dirname(r),n)}}return e(process.cwd(),t)};var Rs={exports:{}};/*!
 * @description Recursive object extending
 * <AUTHOR> Lotsmanov <<EMAIL>>
 * @license MIT
 *
 * The MIT License (MIT)
 *
 * Copyright (c) 2013-2018 Viacheslav Lotsmanov
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */function Ts(t){return t instanceof Buffer||t instanceof Date||t instanceof RegExp}function $s(t){if(t instanceof Buffer){var e=Buffer.alloc?Buffer.alloc(t.length):new Buffer(t.length);return t.copy(e),e}else{if(t instanceof Date)return new Date(t.getTime());if(t instanceof RegExp)return new RegExp(t);throw new Error("Unexpected situation")}}function js(t){var e=[];return t.forEach(function(r,n){typeof r=="object"&&r!==null?Array.isArray(r)?e[n]=js(r):Ts(r)?e[n]=$s(r):e[n]=Ir({},r):e[n]=r}),e}function au(t,e){return e==="__proto__"?void 0:t[e]}var Ir=Rs.exports=function(){if(arguments.length<1||typeof arguments[0]!="object")return!1;if(arguments.length<2)return arguments[0];var t=arguments[0],e=Array.prototype.slice.call(arguments,1),r,n;return e.forEach(function(s){typeof s!="object"||s===null||Array.isArray(s)||Object.keys(s).forEach(function(i){if(n=au(t,i),r=au(s,i),r!==t)if(typeof r!="object"||r===null){t[i]=r;return}else if(Array.isArray(r)){t[i]=js(r);return}else if(Ts(r)){t[i]=$s(r);return}else if(typeof n!="object"||n===null||Array.isArray(n)){t[i]=Ir({},r);return}else{t[i]=Ir(n,r);return}})}),t},el=Rs.exports,Ar,cu;function tl(){if(cu)return Ar;cu=1;function t(n,s){var i=n;s.slice(0,-1).forEach(function(o){i=i[o]||{}});var u=s[s.length-1];return u in i}function e(n){return typeof n=="number"||/^0x[0-9a-f]+$/i.test(n)?!0:/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)(e[-+]?\d+)?$/.test(n)}function r(n,s){return s==="constructor"&&typeof n[s]=="function"||s==="__proto__"}return Ar=function(n,s){s||(s={});var i={bools:{},strings:{},unknownFn:null};typeof s.unknown=="function"&&(i.unknownFn=s.unknown),typeof s.boolean=="boolean"&&s.boolean?i.allBools=!0:[].concat(s.boolean).filter(Boolean).forEach(function(S){i.bools[S]=!0});var u={};function o(S){return u[S].some(function(I){return i.bools[I]})}Object.keys(s.alias||{}).forEach(function(S){u[S]=[].concat(s.alias[S]),u[S].forEach(function(I){u[I]=[S].concat(u[S].filter(function(g){return I!==g}))})}),[].concat(s.string).filter(Boolean).forEach(function(S){i.strings[S]=!0,u[S]&&[].concat(u[S]).forEach(function(I){i.strings[I]=!0})});var c=s.default||{},l={_:[]};function D(S,I){return i.allBools&&/^--[^=]+$/.test(I)||i.strings[S]||i.bools[S]||u[S]}function d(S,I,g){for(var b=S,_=0;_<I.length-1;_++){var P=I[_];if(r(b,P))return;b[P]===void 0&&(b[P]={}),(b[P]===Object.prototype||b[P]===Number.prototype||b[P]===String.prototype)&&(b[P]={}),b[P]===Array.prototype&&(b[P]=[]),b=b[P]}var T=I[I.length-1];r(b,T)||((b===Object.prototype||b===Number.prototype||b===String.prototype)&&(b={}),b===Array.prototype&&(b=[]),b[T]===void 0||i.bools[T]||typeof b[T]=="boolean"?b[T]=g:Array.isArray(b[T])?b[T].push(g):b[T]=[b[T],g])}function h(S,I,g){if(!(g&&i.unknownFn&&!D(S,g)&&i.unknownFn(g)===!1)){var b=!i.strings[S]&&e(I)?Number(I):I;d(l,S.split("."),b),(u[S]||[]).forEach(function(_){d(l,_.split("."),b)})}}Object.keys(i.bools).forEach(function(S){h(S,c[S]===void 0?!1:c[S])});var p=[];n.indexOf("--")!==-1&&(p=n.slice(n.indexOf("--")+1),n=n.slice(0,n.indexOf("--")));for(var m=0;m<n.length;m++){var f=n[m],F,y;if(/^--.+=/.test(f)){var x=f.match(/^--([^=]+)=([\s\S]*)$/);F=x[1];var C=x[2];i.bools[F]&&(C=C!=="false"),h(F,C,f)}else if(/^--no-.+/.test(f))F=f.match(/^--no-(.+)/)[1],h(F,!1,f);else if(/^--.+/.test(f))F=f.match(/^--(.+)/)[1],y=n[m+1],y!==void 0&&!/^(-|--)[^-]/.test(y)&&!i.bools[F]&&!i.allBools&&(!u[F]||!o(F))?(h(F,y,f),m+=1):/^(true|false)$/.test(y)?(h(F,y==="true",f),m+=1):h(F,i.strings[F]?"":!0,f);else if(/^-[^-]+/.test(f)){for(var A=f.slice(1,-1).split(""),N=!1,k=0;k<A.length;k++){if(y=f.slice(k+2),y==="-"){h(A[k],y,f);continue}if(/[A-Za-z]/.test(A[k])&&y[0]==="="){h(A[k],y.slice(1),f),N=!0;break}if(/[A-Za-z]/.test(A[k])&&/-?\d+(\.\d*)?(e-?\d+)?$/.test(y)){h(A[k],y,f),N=!0;break}if(A[k+1]&&A[k+1].match(/\W/)){h(A[k],f.slice(k+2),f),N=!0;break}else h(A[k],i.strings[A[k]]?"":!0,f)}F=f.slice(-1)[0],!N&&F!=="-"&&(n[m+1]&&!/^(-|--)[^-]/.test(n[m+1])&&!i.bools[F]&&(!u[F]||!o(F))?(h(F,n[m+1],f),m+=1):n[m+1]&&/^(true|false)$/.test(n[m+1])?(h(F,n[m+1]==="true",f),m+=1):h(F,i.strings[F]?"":!0,f))}else if((!i.unknownFn||i.unknownFn(f)!==!1)&&l._.push(i.strings._||!e(f)?f:Number(f)),s.stopEarly){l._.push.apply(l._,n.slice(m+1));break}}return Object.keys(c).forEach(function(S){t(l,S.split("."))||(d(l,S.split("."),c[S]),(u[S]||[]).forEach(function(I){d(l,I.split("."),c[S])}))}),s["--"]?l["--"]=p.slice():p.forEach(function(S){l._.push(S)}),l},Ar}var at=Ke,Ue=J.join,rl=el,Du="/etc",ks=process.platform==="win32",ct=ks?process.env.USERPROFILE:process.env.HOME,nl=function(t,e,r,n){if(typeof t!="string")throw new Error("rc(name): name *must* be string");r||(r=tl()(process.argv.slice(2))),e=(typeof e=="string"?at.json(e):e)||{},n=n||at.parse;var s=at.env(t+"_"),i=[e],u=[];function o(c){if(!(u.indexOf(c)>=0)){var l=at.file(c);l&&(i.push(n(l)),u.push(c))}}return ks||[Ue(Du,t,"config"),Ue(Du,t+"rc")].forEach(o),ct&&[Ue(ct,".config",t,"config"),Ue(ct,".config",t),Ue(ct,"."+t,"config"),Ue(ct,"."+t+"rc")].forEach(o),o(at.find("."+t+"rc")),s.config&&o(s.config),r.config&&o(r.config),rl.apply(null,i.concat([s,r,u.length?{configs:u,config:u[u.length-1]}:void 0]))};const ul=R.getDefaultExportFromCjs(nl);function sl(t){const e=ul("npm",{registry:"https://registry.npmjs.org/"}),r=e[`${t}:registry`]||e.config_registry||e.registry;return r.slice(-1)==="/"?r:`${r}/`}var Ls={exports:{}},Ns={},ur={},me=Uu,il=process.cwd,Ht=null,ol=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return Ht||(Ht=il.call(process)),Ht};try{process.cwd()}catch{}if(typeof process.chdir=="function"){var lu=process.chdir;process.chdir=function(t){Ht=null,lu.call(process,t)},Object.setPrototypeOf&&Object.setPrototypeOf(process.chdir,lu)}var al=cl;function cl(t){me.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&e(t),t.lutimes||r(t),t.chown=i(t.chown),t.fchown=i(t.fchown),t.lchown=i(t.lchown),t.chmod=n(t.chmod),t.fchmod=n(t.fchmod),t.lchmod=n(t.lchmod),t.chownSync=u(t.chownSync),t.fchownSync=u(t.fchownSync),t.lchownSync=u(t.lchownSync),t.chmodSync=s(t.chmodSync),t.fchmodSync=s(t.fchmodSync),t.lchmodSync=s(t.lchmodSync),t.stat=o(t.stat),t.fstat=o(t.fstat),t.lstat=o(t.lstat),t.statSync=c(t.statSync),t.fstatSync=c(t.fstatSync),t.lstatSync=c(t.lstatSync),t.chmod&&!t.lchmod&&(t.lchmod=function(D,d,h){h&&process.nextTick(h)},t.lchmodSync=function(){}),t.chown&&!t.lchown&&(t.lchown=function(D,d,h,p){p&&process.nextTick(p)},t.lchownSync=function(){}),ol==="win32"&&(t.rename=typeof t.rename!="function"?t.rename:function(D){function d(h,p,m){var f=Date.now(),F=0;D(h,p,function y(x){if(x&&(x.code==="EACCES"||x.code==="EPERM")&&Date.now()-f<6e4){setTimeout(function(){t.stat(p,function(C,A){C&&C.code==="ENOENT"?D(h,p,y):m(x)})},F),F<100&&(F+=10);return}m&&m(x)})}return Object.setPrototypeOf&&Object.setPrototypeOf(d,D),d}(t.rename)),t.read=typeof t.read!="function"?t.read:function(D){function d(h,p,m,f,F,y){var x;if(y&&typeof y=="function"){var C=0;x=function(A,N,k){if(A&&A.code==="EAGAIN"&&C<10)return C++,D.call(t,h,p,m,f,F,x);y.apply(this,arguments)}}return D.call(t,h,p,m,f,F,x)}return Object.setPrototypeOf&&Object.setPrototypeOf(d,D),d}(t.read),t.readSync=typeof t.readSync!="function"?t.readSync:function(D){return function(d,h,p,m,f){for(var F=0;;)try{return D.call(t,d,h,p,m,f)}catch(y){if(y.code==="EAGAIN"&&F<10){F++;continue}throw y}}}(t.readSync);function e(D){D.lchmod=function(d,h,p){D.open(d,me.O_WRONLY|me.O_SYMLINK,h,function(m,f){if(m){p&&p(m);return}D.fchmod(f,h,function(F){D.close(f,function(y){p&&p(F||y)})})})},D.lchmodSync=function(d,h){var p=D.openSync(d,me.O_WRONLY|me.O_SYMLINK,h),m=!0,f;try{f=D.fchmodSync(p,h),m=!1}finally{if(m)try{D.closeSync(p)}catch{}else D.closeSync(p)}return f}}function r(D){me.hasOwnProperty("O_SYMLINK")&&D.futimes?(D.lutimes=function(d,h,p,m){D.open(d,me.O_SYMLINK,function(f,F){if(f){m&&m(f);return}D.futimes(F,h,p,function(y){D.close(F,function(x){m&&m(y||x)})})})},D.lutimesSync=function(d,h,p){var m=D.openSync(d,me.O_SYMLINK),f,F=!0;try{f=D.futimesSync(m,h,p),F=!1}finally{if(F)try{D.closeSync(m)}catch{}else D.closeSync(m)}return f}):D.futimes&&(D.lutimes=function(d,h,p,m){m&&process.nextTick(m)},D.lutimesSync=function(){})}function n(D){return D&&function(d,h,p){return D.call(t,d,h,function(m){l(m)&&(m=null),p&&p.apply(this,arguments)})}}function s(D){return D&&function(d,h){try{return D.call(t,d,h)}catch(p){if(!l(p))throw p}}}function i(D){return D&&function(d,h,p,m){return D.call(t,d,h,p,function(f){l(f)&&(f=null),m&&m.apply(this,arguments)})}}function u(D){return D&&function(d,h,p){try{return D.call(t,d,h,p)}catch(m){if(!l(m))throw m}}}function o(D){return D&&function(d,h,p){typeof h=="function"&&(p=h,h=null);function m(f,F){F&&(F.uid<0&&(F.uid+=4294967296),F.gid<0&&(F.gid+=4294967296)),p&&p.apply(this,arguments)}return h?D.call(t,d,h,m):D.call(t,d,m)}}function c(D){return D&&function(d,h){var p=h?D.call(t,d,h):D.call(t,d);return p&&(p.uid<0&&(p.uid+=4294967296),p.gid<0&&(p.gid+=4294967296)),p}}function l(D){if(!D||D.code==="ENOSYS")return!0;var d=!process.getuid||process.getuid()!==0;return!!(d&&(D.code==="EINVAL"||D.code==="EPERM"))}}var fu=ae.Stream,Dl=ll;function ll(t){return{ReadStream:e,WriteStream:r};function e(n,s){if(!(this instanceof e))return new e(n,s);fu.call(this);var i=this;this.path=n,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=64*1024,s=s||{};for(var u=Object.keys(s),o=0,c=u.length;o<c;o++){var l=u[o];this[l]=s[l]}if(this.encoding&&this.setEncoding(this.encoding),this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.end===void 0)this.end=1/0;else if(typeof this.end!="number")throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}if(this.fd!==null){process.nextTick(function(){i._read()});return}t.open(this.path,this.flags,this.mode,function(D,d){if(D){i.emit("error",D),i.readable=!1;return}i.fd=d,i.emit("open",d),i._read()})}function r(n,s){if(!(this instanceof r))return new r(n,s);fu.call(this),this.path=n,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,s=s||{};for(var i=Object.keys(s),u=0,o=i.length;u<o;u++){var c=i[u];this[c]=s[c]}if(this.start!==void 0){if(typeof this.start!="number")throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1,this._queue=[],this.fd===null&&(this._open=t.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}var fl=dl,hl=Object.getPrototypeOf||function(t){return t.__proto__};function dl(t){if(t===null||typeof t!="object")return t;if(t instanceof Object)var e={__proto__:hl(t)};else var e=Object.create(null);return Object.getOwnPropertyNames(t).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}var W=he,pl=al,ml=Dl,yl=fl,Nt=te,ee,Qt;typeof Symbol=="function"&&typeof Symbol.for=="function"?(ee=Symbol.for("graceful-fs.queue"),Qt=Symbol.for("graceful-fs.previous")):(ee="___graceful-fs.queue",Qt="___graceful-fs.previous");function Fl(){}function Is(t,e){Object.defineProperty(t,ee,{get:function(){return e}})}var Se=Fl;Nt.debuglog?Se=Nt.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&(Se=function(){var t=Nt.format.apply(Nt,arguments);t="GFS4: "+t.split(/\n/).join(`
GFS4: `),console.error(t)});if(!W[ee]){var gl=R.commonjsGlobal[ee]||[];Is(W,gl),W.close=function(t){function e(r,n){return t.call(W,r,function(s){s||hu(),typeof n=="function"&&n.apply(this,arguments)})}return Object.defineProperty(e,Qt,{value:t}),e}(W.close),W.closeSync=function(t){function e(r){t.apply(W,arguments),hu()}return Object.defineProperty(e,Qt,{value:t}),e}(W.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",function(){Se(W[ee]),Zt.equal(W[ee].length,0)})}R.commonjsGlobal[ee]||Is(R.commonjsGlobal,W[ee]);var Us=en(yl(W));process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!W.__patched&&(Us=en(W),W.__patched=!0);function en(t){pl(t),t.gracefulify=en,t.createReadStream=N,t.createWriteStream=k;var e=t.readFile;t.readFile=r;function r(g,b,_){return typeof b=="function"&&(_=b,b=null),P(g,b,_);function P(T,j,O,$){return e(T,j,function(B){B&&(B.code==="EMFILE"||B.code==="ENFILE")?qe([P,[T,j,O],B,$||Date.now(),Date.now()]):typeof O=="function"&&O.apply(this,arguments)})}}var n=t.writeFile;t.writeFile=s;function s(g,b,_,P){return typeof _=="function"&&(P=_,_=null),T(g,b,_,P);function T(j,O,$,B,L){return n(j,O,$,function(v){v&&(v.code==="EMFILE"||v.code==="ENFILE")?qe([T,[j,O,$,B],v,L||Date.now(),Date.now()]):typeof B=="function"&&B.apply(this,arguments)})}}var i=t.appendFile;i&&(t.appendFile=u);function u(g,b,_,P){return typeof _=="function"&&(P=_,_=null),T(g,b,_,P);function T(j,O,$,B,L){return i(j,O,$,function(v){v&&(v.code==="EMFILE"||v.code==="ENFILE")?qe([T,[j,O,$,B],v,L||Date.now(),Date.now()]):typeof B=="function"&&B.apply(this,arguments)})}}var o=t.copyFile;o&&(t.copyFile=c);function c(g,b,_,P){return typeof _=="function"&&(P=_,_=0),T(g,b,_,P);function T(j,O,$,B,L){return o(j,O,$,function(v){v&&(v.code==="EMFILE"||v.code==="ENFILE")?qe([T,[j,O,$,B],v,L||Date.now(),Date.now()]):typeof B=="function"&&B.apply(this,arguments)})}}var l=t.readdir;t.readdir=d;var D=/^v[0-5]\./;function d(g,b,_){typeof b=="function"&&(_=b,b=null);var P=D.test(process.version)?function(O,$,B,L){return l(O,T(O,$,B,L))}:function(O,$,B,L){return l(O,$,T(O,$,B,L))};return P(g,b,_);function T(j,O,$,B){return function(L,v){L&&(L.code==="EMFILE"||L.code==="ENFILE")?qe([P,[j,O,$],L,B||Date.now(),Date.now()]):(v&&v.sort&&v.sort(),typeof $=="function"&&$.call(this,L,v))}}}if(process.version.substr(0,4)==="v0.8"){var h=ml(t);y=h.ReadStream,C=h.WriteStream}var p=t.ReadStream;p&&(y.prototype=Object.create(p.prototype),y.prototype.open=x);var m=t.WriteStream;m&&(C.prototype=Object.create(m.prototype),C.prototype.open=A),Object.defineProperty(t,"ReadStream",{get:function(){return y},set:function(g){y=g},enumerable:!0,configurable:!0}),Object.defineProperty(t,"WriteStream",{get:function(){return C},set:function(g){C=g},enumerable:!0,configurable:!0});var f=y;Object.defineProperty(t,"FileReadStream",{get:function(){return f},set:function(g){f=g},enumerable:!0,configurable:!0});var F=C;Object.defineProperty(t,"FileWriteStream",{get:function(){return F},set:function(g){F=g},enumerable:!0,configurable:!0});function y(g,b){return this instanceof y?(p.apply(this,arguments),this):y.apply(Object.create(y.prototype),arguments)}function x(){var g=this;I(g.path,g.flags,g.mode,function(b,_){b?(g.autoClose&&g.destroy(),g.emit("error",b)):(g.fd=_,g.emit("open",_),g.read())})}function C(g,b){return this instanceof C?(m.apply(this,arguments),this):C.apply(Object.create(C.prototype),arguments)}function A(){var g=this;I(g.path,g.flags,g.mode,function(b,_){b?(g.destroy(),g.emit("error",b)):(g.fd=_,g.emit("open",_))})}function N(g,b){return new t.ReadStream(g,b)}function k(g,b){return new t.WriteStream(g,b)}var S=t.open;t.open=I;function I(g,b,_,P){return typeof _=="function"&&(P=_,_=null),T(g,b,_,P);function T(j,O,$,B,L){return S(j,O,$,function(v,li){v&&(v.code==="EMFILE"||v.code==="ENFILE")?qe([T,[j,O,$,B],v,L||Date.now(),Date.now()]):typeof B=="function"&&B.apply(this,arguments)})}}return t}function qe(t){Se("ENQUEUE",t[0].name,t[1]),W[ee].push(t),tn()}var It;function hu(){for(var t=Date.now(),e=0;e<W[ee].length;++e)W[ee][e].length>2&&(W[ee][e][3]=t,W[ee][e][4]=t);tn()}function tn(){if(clearTimeout(It),It=void 0,W[ee].length!==0){var t=W[ee].shift(),e=t[0],r=t[1],n=t[2],s=t[3],i=t[4];if(s===void 0)Se("RETRY",e.name,r),e.apply(null,r);else if(Date.now()-s>=6e4){Se("TIMEOUT",e.name,r);var u=r.pop();typeof u=="function"&&u.call(null,n)}else{var o=Date.now()-i,c=Math.max(i-s,1),l=Math.min(c*1.2,100);o>=l?(Se("RETRY",e.name,r),e.apply(null,r.concat([s]))):W[ee].push(t)}It===void 0&&(It=setTimeout(tn,0))}}var El=R.commonjsGlobal&&R.commonjsGlobal.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(ur,"__esModule",{value:!0});ur.readCAFileSync=void 0;const Cl=El(Us);function bl(t){try{const e=Cl.default.readFileSync(t,"utf8"),r="-----END CERTIFICATE-----";return e.split(r).filter(s=>!!s.trim()).map(s=>`${s.trimLeft()}${r}`)}catch(e){if(e.code==="ENOENT")return;throw e}}ur.readCAFileSync=bl;(function(t){var e=R.commonjsGlobal&&R.commonjsGlobal.__createBinding||(Object.create?function(n,s,i,u){u===void 0&&(u=i);var o=Object.getOwnPropertyDescriptor(s,i);(!o||("get"in o?!s.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return s[i]}}),Object.defineProperty(n,u,o)}:function(n,s,i,u){u===void 0&&(u=i),n[u]=s[i]}),r=R.commonjsGlobal&&R.commonjsGlobal.__exportStar||function(n,s){for(var i in n)i!=="default"&&!Object.prototype.hasOwnProperty.call(s,i)&&e(s,n,i)};Object.defineProperty(t,"__esModule",{value:!0}),r(ur,t)})(Ns);var qs={exports:{}},wl=Hs;function xe(t,e){if(typeof Object.setPrototypeOf=="function")return Object.setPrototypeOf(t,e);t.__proto__=e}function Hs(){this.list=[];var t=null;Object.defineProperty(this,"root",{get:function(){return t},set:function(e){t=e,this.list.length&&xe(this.list[this.list.length-1],e)},enumerable:!0,configurable:!0})}Hs.prototype={get length(){return this.list.length},get keys(){var t=[];for(var e in this.list[0])t.push(e);return t},get snapshot(){var t={};return this.keys.forEach(function(e){t[e]=this.get(e)},this),t},get store(){return this.list[0]},push:function(t){return typeof t!="object"&&(t={valueOf:t}),this.list.length>=1&&xe(this.list[this.list.length-1],t),xe(t,this.root),this.list.push(t)},pop:function(){return this.list.length>=2&&xe(this.list[this.list.length-2],this.root),this.list.pop()},unshift:function(t){return xe(t,this.list[0]||this.root),this.list.unshift(t)},shift:function(){return this.list.length===1&&xe(this.list[0],this.root),this.list.shift()},get:function(t){return this.list[0][t]},set:function(t,e,r){return this.length||this.push({}),r&&this.list[0].hasOwnProperty(t)&&this.push({}),this.list[0][t]=e},forEach:function(t,e){for(var r in this.list[0])t.call(e,r,this.list[0][r])},slice:function(){return this.list.slice.apply(this.list,arguments)},splice:function(){for(var t=this.list.splice.apply(this.list,arguments),e=0,r=this.list.length;e<r;e++)xe(this.list[e],this.list[e+1]||this.root);return t}};var Oe={};Oe.parse=Oe.decode=_l;Oe.stringify=Oe.encode=Ms;Oe.safe=ze;Oe.unsafe=Mt;var Br=typeof process<"u"&&process.platform==="win32"?`\r
`:`
`;function Ms(t,e){var r=[],n="";typeof e=="string"?e={section:e,whitespace:!1}:(e=e||{},e.whitespace=e.whitespace===!0);var s=e.whitespace?" = ":"=";return Object.keys(t).forEach(function(i,u,o){var c=t[i];c&&Array.isArray(c)?c.forEach(function(l){n+=ze(i+"[]")+s+ze(l)+`
`}):c&&typeof c=="object"?r.push(i):n+=ze(i)+s+ze(c)+Br}),e.section&&n.length&&(n="["+ze(e.section)+"]"+Br+n),r.forEach(function(i,u,o){var c=zs(i).join("\\."),l=(e.section?e.section+".":"")+c,D=Ms(t[i],{section:l,whitespace:e.whitespace});n.length&&D.length&&(n+=Br),n+=D}),n}function zs(t){return t.replace(/\1/g,"LITERAL\\1LITERAL").replace(/\\\./g,"").split(/\./).map(function(e){return e.replace(/\1/g,"\\.").replace(/\2LITERAL\\1LITERAL\2/g,"")})}function _l(t){var e={},r=e,n=null,s=/^\[([^\]]*)\]$|^([^=]+)(=(.*))?$/i,i=t.split(/[\r\n]+/g);return i.forEach(function(u,o,c){if(!(!u||u.match(/^\s*[;#]/))){var l=u.match(s);if(l){if(l[1]!==void 0){if(n=Mt(l[1]),n==="__proto__"){r={};return}r=e[n]=e[n]||{};return}var D=Mt(l[2]);if(D!=="__proto__"){var d=l[3]?Mt(l[4]):!0;switch(d){case"true":case"false":case"null":d=JSON.parse(d)}if(D.length>2&&D.slice(-2)==="[]"){if(D=D.substring(0,D.length-2),D==="__proto__")return;r[D]?Array.isArray(r[D])||(r[D]=[r[D]]):r[D]=[]}Array.isArray(r[D])?r[D].push(d):r[D]=d}}}}),Object.keys(e).filter(function(u,o,c){if(!e[u]||typeof e[u]!="object"||Array.isArray(e[u]))return!1;var l=zs(u),D=e,d=l.pop(),h=d.replace(/\\\./g,".");return l.forEach(function(p,m,f){p!=="__proto__"&&((!D[p]||typeof D[p]!="object")&&(D[p]={}),D=D[p])}),D===e&&h===d?!1:(D[h]=e[u],!0)}).forEach(function(u,o,c){delete e[u]}),e}function Gs(t){return t.charAt(0)==='"'&&t.slice(-1)==='"'||t.charAt(0)==="'"&&t.slice(-1)==="'"}function ze(t){return typeof t!="string"||t.match(/[=\r\n]/)||t.match(/^\[/)||t.length>1&&Gs(t)||t!==t.trim()?JSON.stringify(t):t.replace(/;/g,"\\;").replace(/#/g,"\\#")}function Mt(t,e){if(t=(t||"").trim(),Gs(t)){t.charAt(0)==="'"&&(t=t.substr(1,t.length-2));try{t=JSON.parse(t)}catch{}}else{for(var r=!1,n="",s=0,i=t.length;s<i;s++){var u=t.charAt(s);if(r)"\\;#".indexOf(u)!==-1?n+=u:n+="\\"+u,r=!1;else{if(";#".indexOf(u)!==-1)break;u==="\\"?r=!0:n+=u}}return r&&(n+="\\"),n.trim()}return t}var Ws=wl,ft=J,sr=he,Ur=Oe,qr=Re.EventEmitter,xl=K,Al=fe,$e=qs.exports=function(){for(var t=[].slice.call(arguments),e=new re;t.length;){var r=t.shift();r&&e.push(typeof r=="string"?Sl(r):r)}return e};$e.find=function(){var t=ft.join.apply(null,[].slice.call(arguments));function e(r,n){var s=ft.join(r,n);try{return sr.statSync(s),s}catch{if(ft.dirname(r)!==r)return e(ft.dirname(r),n)}}return e(__dirname,t)};var Bl=$e.parse=function(t,e,r){if(t=""+t,r)if(r==="json")if(this.emit)try{return JSON.parse(t)}catch(n){this.emit("error",n)}else return JSON.parse(t);else return Ur.parse(t);else try{return JSON.parse(t)}catch{return Ur.parse(t)}},Sl=$e.json=function(){var t=[].slice.call(arguments).filter(function(n){return n!=null}),e=ft.join.apply(null,t),r;try{r=sr.readFileSync(e,"utf-8")}catch{return}return Bl(r,e,"json")};$e.env=function(t,e){e=e||process.env;var r={},n=t.length;for(var s in e)s.indexOf(t)===0&&(r[s.substring(n)]=e[s]);return r};$e.ConfigChain=re;function re(){qr.apply(this),Ws.apply(this,arguments),this._awaiting=0,this._saving=0,this.sources={}}var Vs={constructor:{value:re}};Object.keys(qr.prototype).forEach(function(t){Vs[t]=Object.getOwnPropertyDescriptor(qr.prototype,t)});re.prototype=Object.create(Ws.prototype,Vs);re.prototype.del=function(t,e){if(e){var r=this.sources[e];if(r=r&&r.data,!r)return this.emit("error",new Error("not found "+e));delete r[t]}else for(var n=0,s=this.list.length;n<s;n++)delete this.list[n][t];return this};re.prototype.set=function(t,e,r){var n;if(r){if(n=this.sources[r],n=n&&n.data,!n)return this.emit("error",new Error("not found "+r))}else if(n=this.list[0],!n)return this.emit("error",new Error("cannot set, no confs!"));return n[t]=e,this};re.prototype.get=function(t,e){return e?(e=this.sources[e],e&&(e=e.data),e&&Object.hasOwnProperty.call(e,t)?e[t]:void 0):this.list[0][t]};re.prototype.save=function(t,i,r){typeof i=="function"&&(r=i,i=null);var n=this.sources[t];if(!n||!(n.path||n.source)||!n.data)return this.emit("error",new Error("bad save target: "+t));if(n.source){var s=n.prefix||"";return Object.keys(n.data).forEach(function(o){n.source[s+o]=n.data[o]}),this}var i=i||n.type,u=n.data;return n.type==="json"?u=JSON.stringify(u):u=Ur.stringify(u),this._saving++,sr.writeFile(n.path,u,"utf8",function(o){if(this._saving--,o)return r?r(o):this.emit("error",o);this._saving===0&&(r&&r(),this.emit("save"))}.bind(this)),this};re.prototype.addFile=function(t,e,r){r=r||t;var n={__source__:r};return this.sources[r]={path:t,type:e},this.push(n),this._await(),sr.readFile(t,"utf8",function(s,i){s&&this.emit("error",s),this.addString(i,t,e,n)}.bind(this)),this};re.prototype.addEnv=function(t,e,r){r=r||"env";var n=$e.env(t,e);return this.sources[r]={data:n,source:e,prefix:t},this.add(n,r)};re.prototype.addUrl=function(t,e,r){this._await();var n=xl.format(t);r=r||n;var s={__source__:r};return this.sources[r]={href:n,type:e},this.push(s),Al.request(t,function(i){var u=[],o=i.headers["content-type"];e||(e=o.indexOf("json")!==-1?"json":o.indexOf("ini")!==-1?"ini":n.match(/\.json$/)?"json":n.match(/\.ini$/)?"ini":null,s.type=e),i.on("data",u.push.bind(u)).on("end",function(){this.addString(Buffer.concat(u),n,e,s)}.bind(this)).on("error",this.emit.bind(this,"error"))}.bind(this)).on("error",this.emit.bind(this,"error")).end(),this};re.prototype.addString=function(t,e,r,n){return t=this.parse(t,e,r),this.add(t,n),this};re.prototype.add=function(t,e){if(e&&typeof e=="object"){var r=this.list.indexOf(e);if(r===-1)return this.emit("error",new Error("bad marker"));this.splice(r,1,t),e=e.__source__,this.sources[e]=this.sources[e]||{},this.sources[e].data=t,this._resolve()}else typeof e=="string"&&(this.sources[e]=this.sources[e]||{},this.sources[e].data=t),this._await(),this.push(t),process.nextTick(this._resolve.bind(this));return this};re.prototype.parse=$e.parse;re.prototype._await=function(){this._awaiting++};re.prototype._resolve=function(){this._awaiting--,this._awaiting===0&&this.emit("load",this)};var vl=qs.exports,Ol=function(t){const e=t.indexOf(":");if(e===-1)return Sr(t);const r=t.substr(0,e),n=t.substr(e+1);return`${Sr(r)}:${Sr(n)}`};function Sr(t){if(t=t.toLowerCase(),t==="_authtoken")return"_authToken";let e=t[0];for(let r=1;r<t.length;r++)e+=t[r]==="_"?"-":t[r];return e}var ir={},Js={},or={};Object.defineProperty(or,"__esModule",{value:!0});or.envReplace=void 0;const Pl=/(?<!\\)(\\*)\$\{([^${}]+)\}/g;function Rl(t,e){return t.replace(Pl,Tl.bind(null,e))}or.envReplace=Rl;function Tl(t,e,r,n){if(r.length%2)return e.slice((r.length+1)/2);const s=jl(t,n);if(s===void 0)throw new Error(`Failed to replace env in config: ${e}`);return`${r.slice(r.length/2)}${s}`}const $l=/([^:-]+)(:?)-(.+)/;function jl(t,e){const r=e.match($l);if(!r)return t[e];const[,n,s,i]=r;return Object.prototype.hasOwnProperty.call(t,n)?!t[n]&&s?i:t[n]:i}(function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.envReplace=void 0;var e=or;Object.defineProperty(t,"envReplace",{enumerable:!0,get:function(){return e.envReplace}})})(Js);const kl=he,Ae=J,{envReplace:Ks}=Js,Ll=(t,e,r)=>{if(typeof e!="string")return e;const n=[].concat(t[r]),s=n.indexOf(Ae)!==-1,i=n.indexOf(Boolean)!==-1,u=n.indexOf(String)!==-1,o=n.indexOf(Number)!==-1;if(e=`${e}`.trim(),/^".*"$/.test(e))try{e=JSON.parse(e)}catch{throw new Error(`Failed parsing JSON config key ${r}: ${e}`)}if(i&&!u&&e==="")return!0;switch(e){case"true":return!0;case"false":return!1;case"null":return null;case"undefined":return}return e=Ks(e,process.env),s&&((process.platform==="win32"?/^~(\/|\\)/:/^~\//).test(e)&&process.env.HOME&&(e=Ae.resolve(process.env.HOME,e.substr(2))),e=Ae.resolve(e)),o&&!isNaN(e)&&(e=Number(e)),e},Nl=t=>{t=Ae.resolve(t);let e=!1;for(;Ae.basename(t)==="node_modules";)t=Ae.dirname(t),e=!0;if(e)return t;const r=(n,s)=>{const i=/^[a-zA-Z]:(\\|\/)?$/;if(n==="/"||process.platform==="win32"&&i.test(n))return s;try{const u=kl.readdirSync(n);if(u.includes("node_modules")||u.includes("package.json")||u.includes("package.json5")||u.includes("package.yaml")||u.includes("pnpm-workspace.yaml"))return n;const o=Ae.dirname(n);return o===n?s:r(o,s)}catch(u){if(n===s){if(u.code==="ENOENT")return s;throw u}return s}};return r(t,t)};ir.envReplace=Ks;ir.findPrefix=Nl;ir.parseField=Ll;var vr={},du;function Il(){if(du)return vr;du=1;const t=J,e=ae.Stream,r=K,n=()=>{},s=()=>[],i=()=>{};return vr.types={access:[null,"restricted","public"],"allow-same-version":Boolean,"always-auth":Boolean,also:[null,"dev","development"],audit:Boolean,"auth-type":["legacy","sso","saml","oauth"],"bin-links":Boolean,browser:[null,String],ca:[null,String,Array],cafile:t,cache:t,"cache-lock-stale":Number,"cache-lock-retries":Number,"cache-lock-wait":Number,"cache-max":Number,"cache-min":Number,cert:[null,String],cidr:[null,String,Array],color:["always",Boolean],depth:Number,description:Boolean,dev:Boolean,"dry-run":Boolean,editor:String,"engine-strict":Boolean,force:Boolean,"fetch-retries":Number,"fetch-retry-factor":Number,"fetch-retry-mintimeout":Number,"fetch-retry-maxtimeout":Number,git:String,"git-tag-version":Boolean,"commit-hooks":Boolean,global:Boolean,globalconfig:t,"global-style":Boolean,group:[Number,String],"https-proxy":[null,r],"user-agent":String,"ham-it-up":Boolean,heading:String,"if-present":Boolean,"ignore-prepublish":Boolean,"ignore-scripts":Boolean,"init-module":t,"init-author-name":String,"init-author-email":String,"init-author-url":["",r],"init-license":String,"init-version":i,json:Boolean,key:[null,String],"legacy-bundling":Boolean,link:Boolean,"local-address":s(),loglevel:["silent","error","warn","notice","http","timing","info","verbose","silly"],logstream:e,"logs-max":Number,long:Boolean,maxsockets:Number,message:String,"metrics-registry":[null,String],"node-options":[null,String],"node-version":[null,i],"no-proxy":[null,String,Array],offline:Boolean,"onload-script":[null,String],only:[null,"dev","development","prod","production"],optional:Boolean,"package-lock":Boolean,otp:[null,String],"package-lock-only":Boolean,parseable:Boolean,"prefer-offline":Boolean,"prefer-online":Boolean,prefix:t,production:Boolean,progress:Boolean,proxy:[null,!1,r],provenance:Boolean,"read-only":Boolean,"rebuild-bundle":Boolean,registry:[null,r],rollback:Boolean,save:Boolean,"save-bundle":Boolean,"save-dev":Boolean,"save-exact":Boolean,"save-optional":Boolean,"save-prefix":String,"save-prod":Boolean,scope:String,"script-shell":[null,String],"scripts-prepend-node-path":[!1,!0,"auto","warn-only"],searchopts:String,searchexclude:[null,String],searchlimit:Number,searchstaleness:Number,"send-metrics":Boolean,shell:String,shrinkwrap:Boolean,"sign-git-tag":Boolean,"sso-poll-frequency":Number,"sso-type":[null,"oauth","saml"],"strict-ssl":Boolean,tag:String,timing:Boolean,tmp:t,unicode:Boolean,"unsafe-perm":Boolean,usage:Boolean,user:[Number,String],userconfig:t,umask:n,version:Boolean,"tag-version-prefix":String,versions:Boolean,viewer:String,_exit:Boolean},vr}const{readCAFileSync:Ul}=Ns,pu=he,Or=J,{ConfigChain:ql}=vl,Hl=Ol,mu=ir;class Ml extends ql{constructor(e,r){super(e),this.root=e,this._parseField=mu.parseField.bind(null,r||Il())}add(e,r){try{for(const n of Object.keys(e))e[n]=this._parseField(e[n],n)}catch(n){throw n}return super.add(e,r)}addFile(e,r){r=r||e;const n={__source__:r};this.sources[r]={path:e,type:"ini"},this.push(n),this._await();try{const s=pu.readFileSync(e,"utf8");this.addString(s,e,"ini",n)}catch(s){if(s.code==="ENOENT")this.add({},n);else return`Issue while reading "${e}". ${s.message}`}}addEnv(e){e=e||process.env;const r={};return Object.keys(e).filter(n=>/^npm_config_/i.test(n)).forEach(n=>{e[n]&&(r[Hl(n.substr(11))]=e[n])}),super.addEnv("",r,"env")}loadPrefix(){const e=this.list[0];Object.defineProperty(this,"prefix",{enumerable:!0,set:n=>{const s=this.get("global");this[s?"globalPrefix":"localPrefix"]=n},get:()=>this.get("global")?this.globalPrefix:this.localPrefix}),Object.defineProperty(this,"globalPrefix",{enumerable:!0,set:n=>{this.set("prefix",n)},get:()=>Or.resolve(this.get("prefix"))});let r;if(Object.defineProperty(this,"localPrefix",{enumerable:!0,set:n=>{r=n},get:()=>r}),Object.prototype.hasOwnProperty.call(e,"prefix"))r=Or.resolve(e.prefix);else try{r=mu.findPrefix(process.cwd())}catch(n){throw n}return r}loadCAFile(e){if(!e)return;const r=Ul(e);r&&this.set("ca",r)}loadUser(){const e=this.root;if(this.get("global"))return;if(process.env.SUDO_UID){e.user=Number(process.env.SUDO_UID);return}const r=Or.resolve(this.get("prefix"));try{const n=pu.statSync(r);e.user=n.uid}catch(n){if(n.code==="ENOENT")return;throw n}}}var zl=Ml,Ys={};(function(t){const e=mt,r=J,n=e.tmpdir(),s=process.getuid?process.getuid():process.pid,i=()=>!0,u=process.platform==="win32",o={editor:()=>process.env.EDITOR||process.env.VISUAL||(u?"notepad.exe":"vi"),shell:()=>u?process.env.COMSPEC||"cmd.exe":process.env.SHELL||"/bin/bash"},c={fromString:()=>process.umask()};let l=e.homedir();l?process.env.HOME=l:l=r.resolve(n,"npm-"+s);const D=process.platform==="win32"?"npm-cache":".npm",d=process.platform==="win32"&&process.env.APPDATA||l,h=r.resolve(d,D);let p,m;Object.defineProperty(t,"defaults",{get:function(){return p||(process.env.PREFIX?m=process.env.PREFIX:process.platform==="win32"?m=r.dirname(process.execPath):(m=r.dirname(r.dirname(process.execPath)),process.env.DESTDIR&&(m=r.join(process.env.DESTDIR,m))),p={access:null,"allow-same-version":!1,"always-auth":!1,also:null,audit:!0,"auth-type":"legacy","bin-links":!0,browser:null,ca:null,cafile:null,cache:h,"cache-lock-stale":6e4,"cache-lock-retries":10,"cache-lock-wait":1e4,"cache-max":1/0,"cache-min":10,cert:null,cidr:null,color:process.env.NO_COLOR==null,depth:1/0,description:!0,dev:!1,"dry-run":!1,editor:o.editor(),"engine-strict":!1,force:!1,"fetch-retries":2,"fetch-retry-factor":10,"fetch-retry-mintimeout":1e4,"fetch-retry-maxtimeout":6e4,git:"git","git-tag-version":!0,"commit-hooks":!0,global:!1,globalconfig:r.resolve(m,"etc","npmrc"),"global-style":!1,group:process.platform==="win32"?0:process.env.SUDO_GID||process.getgid&&process.getgid(),"ham-it-up":!1,heading:"npm","if-present":!1,"ignore-prepublish":!1,"ignore-scripts":!1,"init-module":r.resolve(l,".npm-init.js"),"init-author-name":"","init-author-email":"","init-author-url":"","init-version":"1.0.0","init-license":"ISC",json:!1,key:null,"legacy-bundling":!1,link:!1,"local-address":void 0,loglevel:"notice",logstream:process.stderr,"logs-max":10,long:!1,maxsockets:50,message:"%s","metrics-registry":null,"node-options":null,offline:!1,"onload-script":!1,only:null,optional:!0,otp:null,"package-lock":!0,"package-lock-only":!1,parseable:!1,"prefer-offline":!1,"prefer-online":!1,prefix:m,production:process.env.NODE_ENV==="production",progress:!process.env.TRAVIS&&!process.env.CI,provenance:!1,proxy:null,"https-proxy":null,"no-proxy":null,"user-agent":"npm/{npm-version} node/{node-version} {platform} {arch}","read-only":!1,"rebuild-bundle":!0,registry:"https://registry.npmjs.org/",rollback:!0,save:!0,"save-bundle":!1,"save-dev":!1,"save-exact":!1,"save-optional":!1,"save-prefix":"^","save-prod":!1,scope:"","script-shell":null,"scripts-prepend-node-path":"warn-only",searchopts:"",searchexclude:null,searchlimit:20,searchstaleness:15*60,"send-metrics":!1,shell:o.shell(),shrinkwrap:!0,"sign-git-tag":!1,"sso-poll-frequency":500,"sso-type":"oauth","strict-ssl":!0,tag:"latest","tag-version-prefix":"v",timing:!1,tmp:n,unicode:i(),"unsafe-perm":process.platform==="win32"||process.platform==="cygwin"||!(process.getuid&&process.setuid&&process.getgid&&process.setgid)||process.getuid()!==0,usage:!1,user:process.platform==="win32"?0:"nobody",userconfig:r.resolve(l,".npmrc"),umask:process.umask?process.umask():c.fromString("022"),version:!1,versions:!1,viewer:process.platform==="win32"?"browser":"man",_exit:!0},p)}})})(Ys);(function(t){const e=J,r=zl,n=Ys;t.exports=(s,i,u)=>{const o=new r(Object.assign({},n.defaults,u),i);o.add(Object.assign({},s),"cli");const c=[];let l=!1;if(require.resolve.paths){const p=require.resolve.paths("npm");let m;try{m=require.resolve("npm",{paths:p.slice(-1)})}catch{l=!0}m&&c.push(o.addFile(e.resolve(e.dirname(m),"..","npmrc"),"builtin"))}o.addEnv(),o.loadPrefix();const D=e.resolve(o.localPrefix,".npmrc"),d=o.get("userconfig");if(!o.get("global")&&D!==d?c.push(o.addFile(D,"project")):o.add({},"project"),o.get("workspace-prefix")&&o.get("workspace-prefix")!==D){const p=e.resolve(o.get("workspace-prefix"),".npmrc");c.push(o.addFile(p,"workspace"))}if(c.push(o.addFile(o.get("userconfig"),"user")),o.get("prefix")){const p=e.resolve(o.get("prefix"),"etc");o.root.globalconfig=e.resolve(p,"npmrc"),o.root.globalignorefile=e.resolve(p,"npmignore")}c.push(o.addFile(o.get("globalconfig"),"global")),o.loadUser();const h=o.get("cafile");return h&&o.loadCAFile(h),{config:o,warnings:c.filter(Boolean),failedToLoadBuiltInConfig:l}},Object.defineProperty(t.exports,"defaults",{get(){return n.defaults},enumerable:!0})})(Ls);var Gl=Ls.exports;const Pr=K,yu=Gl,Fu=":_authToken",gu=":_auth",Eu=":username",Cu=":_password";var Wl=function(){let e,r;arguments.length>=2?(e=arguments[0],r=Object.assign({},arguments[1])):typeof arguments[0]=="string"?e=arguments[0]:r=Object.assign({},arguments[0]),r=r||{};const n=r.npmrc;return r.npmrc=(r.npmrc?{config:{get:s=>n[s]}}:yu()).config,e=e||r.npmrc.get("registry")||yu.defaults.registry,Qs(e,r)||Vl(r.npmrc)};function Qs(t,e){const r=Pr.parse(t,!1,!0);let n;for(;n!=="/"&&r.pathname!==n;){n=r.pathname||"/";const s="//"+r.host+n.replace(/\/$/,""),i=Kl(s,e.npmrc);if(i)return i;if(!e.recursive)return/\/$/.test(t)?void 0:Qs(Pr.resolve(t,"."),e);r.pathname=Pr.resolve(Jl(n),"..")||"/"}}function Vl(t){return t.get("_auth")?{token:ar(t.get("_auth")),type:"Basic"}:void 0}function Jl(t){return t[t.length-1]==="/"?t:t+"/"}function Kl(t,e){const r=Yl(e.get(t+Fu)||e.get(t+"/"+Fu));if(r)return r;const n=e.get(t+Eu)||e.get(t+"/"+Eu),s=e.get(t+Cu)||e.get(t+"/"+Cu),i=Ql(n,s);if(i)return i;const u=Xl(e.get(t+gu)||e.get(t+"/"+gu));if(u)return u}function ar(t){return t.replace(/^\$\{?([^}]*)\}?$/,function(e,r){return process.env[r]})}function Yl(t){return t?{token:ar(t),type:"Bearer"}:void 0}function Ql(t,e){if(!t||!e)return;const r=Buffer.from(ar(e),"base64").toString("utf8");return{token:Buffer.from(t+":"+r,"utf8").toString("base64"),type:"Basic",password:r,username:t}}function Xl(t){return t?{token:ar(t),type:"Basic"}:void 0}const Zl=R.getDefaultExportFromCjs(Wl),Xs={keepAlive:!0,maxSockets:50},ef=new fe.Agent(Xs),tf=new yt.Agent(Xs);class rf extends Error{constructor(e){super(`Package \`${e}\` could not be found`),this.name="PackageNotFoundError"}}class nf extends Error{constructor(e,r){super(`Version \`${r}\` for package \`${e}\` could not be found`),this.name="VersionNotFoundError"}}async function uf(t,e){e={version:"latest",...e};const r=t.split("/")[0],n=e.registryUrl||sl(r),s=new URL(encodeURIComponent(t).replace(/^%40/,"@"),n),i=Zl(n.toString(),{recursive:!0}),u={accept:"application/vnd.npm.install-v1+json; q=1.0, application/json; q=0.8, */*"};e.fullMetadata&&delete u.accept,i&&(u.authorization=`${i.type} ${i.token}`);const o={headers:u,agent:{http:ef,https:tf}};e.agent&&(o.agent=e.agent);let c;try{c=await GD(s,o).json()}catch(d){throw d?.response?.statusCode===404?new rf(t):d}if(e.allVersions)return c;let{version:l}=e;const D=new nf(t,l);if(c["dist-tags"][l]){const d=c.time;c=c.versions[c["dist-tags"][l]],c.time=d}else if(l){if(!c.versions[l]){const h=Object.keys(c.versions);if(l=R.semver.maxSatisfying(h,l),!l)throw D}const d=c.time;if(c=c.versions[l],c.time=d,!c)throw D}return c}async function sf(t,e){const{version:r}=await uf(t.toLowerCase(),e);return r}const bu=V.env.npm_package_json,Xt=V.env.npm_config_user_agent,of=!!(Xt&&Xt.startsWith("npm")),af=!!(bu&&bu.endsWith("package.json")),cf=of||af,Df=!!(Xt&&Xt.startsWith("yarn")),lf=cf||Df;var Zs={};const{hasOwnProperty:Rr}=Object.prototype,Tr=typeof process<"u"&&process.platform==="win32"?`\r
`:`
`,Hr=(t,e)=>{const r=[];let n="";typeof e=="string"?e={section:e,whitespace:!1}:(e=e||Object.create(null),e.whitespace=e.whitespace===!0);const s=e.whitespace?" = ":"=";for(const i of Object.keys(t)){const u=t[i];if(u&&Array.isArray(u))for(const o of u)n+=Ge(i+"[]")+s+Ge(o)+`
`;else u&&typeof u=="object"?r.push(i):n+=Ge(i)+s+Ge(u)+Tr}e.section&&n.length&&(n="["+Ge(e.section)+"]"+Tr+n);for(const i of r){const u=ei(i).join("\\."),o=(e.section?e.section+".":"")+u,{whitespace:c}=e,l=Hr(t[i],{section:o,whitespace:c});n.length&&l.length&&(n+=Tr),n+=l}return n},ei=t=>t.replace(/\1/g,"LITERAL\\1LITERAL").replace(/\\\./g,"").split(/\./).map(e=>e.replace(/\1/g,"\\.").replace(/\2LITERAL\\1LITERAL\2/g,"")),wu=t=>{const e=Object.create(null);let r=e,n=null;const s=/^\[([^\]]*)\]$|^([^=]+)(=(.*))?$/i,i=t.split(/[\r\n]+/g);for(const o of i){if(!o||o.match(/^\s*[;#]/))continue;const c=o.match(s);if(!c)continue;if(c[1]!==void 0){if(n=zt(c[1]),n==="__proto__"){r=Object.create(null);continue}r=e[n]=e[n]||Object.create(null);continue}const l=zt(c[2]),D=l.length>2&&l.slice(-2)==="[]",d=D?l.slice(0,-2):l;if(d==="__proto__")continue;const h=c[3]?zt(c[4]):!0,p=h==="true"||h==="false"||h==="null"?JSON.parse(h):h;D&&(Rr.call(r,d)?Array.isArray(r[d])||(r[d]=[r[d]]):r[d]=[]),Array.isArray(r[d])?r[d].push(p):r[d]=p}const u=[];for(const o of Object.keys(e)){if(!Rr.call(e,o)||typeof e[o]!="object"||Array.isArray(e[o]))continue;const c=ei(o);let l=e;const D=c.pop(),d=D.replace(/\\\./g,".");for(const h of c)h!=="__proto__"&&((!Rr.call(l,h)||typeof l[h]!="object")&&(l[h]=Object.create(null)),l=l[h]);l===e&&d===D||(l[d]=e[o],u.push(o))}for(const o of u)delete e[o];return e},ti=t=>t.charAt(0)==='"'&&t.slice(-1)==='"'||t.charAt(0)==="'"&&t.slice(-1)==="'",Ge=t=>typeof t!="string"||t.match(/[=\r\n]/)||t.match(/^\[/)||t.length>1&&ti(t)||t!==t.trim()?JSON.stringify(t):t.replace(/;/g,"\\;").replace(/#/g,"\\#"),zt=(t,e)=>{if(t=(t||"").trim(),ti(t)){t.charAt(0)==="'"&&(t=t.substr(1,t.length-2));try{t=JSON.parse(t)}catch{}}else{let r=!1,n="";for(let s=0,i=t.length;s<i;s++){const u=t.charAt(s);if(r)"\\;#".indexOf(u)!==-1?n+=u:n+="\\"+u,r=!1;else{if(";#".indexOf(u)!==-1)break;u==="\\"?r=!0:n+=u}}return r&&(n+="\\"),n.trim()}return t};var ff={parse:wu,decode:wu,stringify:Hr,encode:Hr,safe:Ge,unsafe:zt};(function(t){const e=J,r=mt,n=he,s=ff,i=process.platform==="win32",u=f=>{try{return s.parse(n.readFileSync(f,"utf8")).prefix}catch{}},o=()=>Object.keys(process.env).reduce((f,F)=>/^npm_config_prefix$/i.test(F)?process.env[F]:f,void 0),c=()=>{if(i&&process.env.APPDATA)return e.join(process.env.APPDATA,"/npm/etc/npmrc");if(process.execPath.includes("/Cellar/node")){const f=process.execPath.slice(0,process.execPath.indexOf("/Cellar/node"));return e.join(f,"/lib/node_modules/npm/npmrc")}if(process.execPath.endsWith("/bin/node")){const f=e.dirname(e.dirname(process.execPath));return e.join(f,"/etc/npmrc")}},l=()=>{if(i){const{APPDATA:f}=process.env;return f?e.join(f,"npm"):e.dirname(process.execPath)}return e.dirname(e.dirname(process.execPath))},D=()=>{const f=o();if(f)return f;const F=u(e.join(r.homedir(),".npmrc"));if(F)return F;if(process.env.PREFIX)return process.env.PREFIX;const y=u(c());return y||l()},d=e.resolve(D()),h=()=>{if(i&&process.env.LOCALAPPDATA){const f=e.join(process.env.LOCALAPPDATA,"Yarn");if(n.existsSync(f))return f}return!1},p=()=>{if(process.env.PREFIX)return process.env.PREFIX;const f=h();if(f)return f;const F=e.join(r.homedir(),".config/yarn");if(n.existsSync(F))return F;const y=e.join(r.homedir(),".yarn-config");return n.existsSync(y)?y:d};t.npm={},t.npm.prefix=d,t.npm.packages=e.join(d,i?"node_modules":"lib/node_modules"),t.npm.binaries=i?d:e.join(d,"bin");const m=e.resolve(p());t.yarn={},t.yarn.prefix=m,t.yarn.packages=e.join(m,h()?"Data/global/node_modules":"global/node_modules"),t.yarn.binaries=e.join(t.yarn.packages,".bin")})(Zs);const $r=J;var hf=(t,e)=>{const r=$r.relative(e,t);return!!(r&&r!==".."&&!r.startsWith(`..${$r.sep}`)&&r!==$r.resolve(t))};const df=he,_u=Zs,xu=hf;var pf=(()=>{try{return xu(__dirname,_u.yarn.packages)||xu(__dirname,df.realpathSync(_u.npm.packages))}catch{return!1}})();const mf=R.getDefaultExportFromCjs(pf);var ri={exports:{}};(function(t){var e={};t.exports=e,e.eastAsianWidth=function(n){var s=n.charCodeAt(0),i=n.length==2?n.charCodeAt(1):0,u=s;return 55296<=s&&s<=56319&&56320<=i&&i<=57343&&(s&=1023,i&=1023,u=s<<10|i,u+=65536),u==12288||65281<=u&&u<=65376||65504<=u&&u<=65510?"F":u==8361||65377<=u&&u<=65470||65474<=u&&u<=65479||65482<=u&&u<=65487||65490<=u&&u<=65495||65498<=u&&u<=65500||65512<=u&&u<=65518?"H":4352<=u&&u<=4447||4515<=u&&u<=4519||4602<=u&&u<=4607||9001<=u&&u<=9002||11904<=u&&u<=11929||11931<=u&&u<=12019||12032<=u&&u<=12245||12272<=u&&u<=12283||12289<=u&&u<=12350||12353<=u&&u<=12438||12441<=u&&u<=12543||12549<=u&&u<=12589||12593<=u&&u<=12686||12688<=u&&u<=12730||12736<=u&&u<=12771||12784<=u&&u<=12830||12832<=u&&u<=12871||12880<=u&&u<=13054||13056<=u&&u<=19903||19968<=u&&u<=42124||42128<=u&&u<=42182||43360<=u&&u<=43388||44032<=u&&u<=55203||55216<=u&&u<=55238||55243<=u&&u<=55291||63744<=u&&u<=64255||65040<=u&&u<=65049||65072<=u&&u<=65106||65108<=u&&u<=65126||65128<=u&&u<=65131||110592<=u&&u<=110593||127488<=u&&u<=127490||127504<=u&&u<=127546||127552<=u&&u<=127560||127568<=u&&u<=127569||131072<=u&&u<=194367||177984<=u&&u<=196605||196608<=u&&u<=262141?"W":32<=u&&u<=126||162<=u&&u<=163||165<=u&&u<=166||u==172||u==175||10214<=u&&u<=10221||10629<=u&&u<=10630?"Na":u==161||u==164||167<=u&&u<=168||u==170||173<=u&&u<=174||176<=u&&u<=180||182<=u&&u<=186||188<=u&&u<=191||u==198||u==208||215<=u&&u<=216||222<=u&&u<=225||u==230||232<=u&&u<=234||236<=u&&u<=237||u==240||242<=u&&u<=243||247<=u&&u<=250||u==252||u==254||u==257||u==273||u==275||u==283||294<=u&&u<=295||u==299||305<=u&&u<=307||u==312||319<=u&&u<=322||u==324||328<=u&&u<=331||u==333||338<=u&&u<=339||358<=u&&u<=359||u==363||u==462||u==464||u==466||u==468||u==470||u==472||u==474||u==476||u==593||u==609||u==708||u==711||713<=u&&u<=715||u==717||u==720||728<=u&&u<=731||u==733||u==735||768<=u&&u<=879||913<=u&&u<=929||931<=u&&u<=937||945<=u&&u<=961||963<=u&&u<=969||u==1025||1040<=u&&u<=1103||u==1105||u==8208||8211<=u&&u<=8214||8216<=u&&u<=8217||8220<=u&&u<=8221||8224<=u&&u<=8226||8228<=u&&u<=8231||u==8240||8242<=u&&u<=8243||u==8245||u==8251||u==8254||u==8308||u==8319||8321<=u&&u<=8324||u==8364||u==8451||u==8453||u==8457||u==8467||u==8470||8481<=u&&u<=8482||u==8486||u==8491||8531<=u&&u<=8532||8539<=u&&u<=8542||8544<=u&&u<=8555||8560<=u&&u<=8569||u==8585||8592<=u&&u<=8601||8632<=u&&u<=8633||u==8658||u==8660||u==8679||u==8704||8706<=u&&u<=8707||8711<=u&&u<=8712||u==8715||u==8719||u==8721||u==8725||u==8730||8733<=u&&u<=8736||u==8739||u==8741||8743<=u&&u<=8748||u==8750||8756<=u&&u<=8759||8764<=u&&u<=8765||u==8776||u==8780||u==8786||8800<=u&&u<=8801||8804<=u&&u<=8807||8810<=u&&u<=8811||8814<=u&&u<=8815||8834<=u&&u<=8835||8838<=u&&u<=8839||u==8853||u==8857||u==8869||u==8895||u==8978||9312<=u&&u<=9449||9451<=u&&u<=9547||9552<=u&&u<=9587||9600<=u&&u<=9615||9618<=u&&u<=9621||9632<=u&&u<=9633||9635<=u&&u<=9641||9650<=u&&u<=9651||9654<=u&&u<=9655||9660<=u&&u<=9661||9664<=u&&u<=9665||9670<=u&&u<=9672||u==9675||9678<=u&&u<=9681||9698<=u&&u<=9701||u==9711||9733<=u&&u<=9734||u==9737||9742<=u&&u<=9743||9748<=u&&u<=9749||u==9756||u==9758||u==9792||u==9794||9824<=u&&u<=9825||9827<=u&&u<=9829||9831<=u&&u<=9834||9836<=u&&u<=9837||u==9839||9886<=u&&u<=9887||9918<=u&&u<=9919||9924<=u&&u<=9933||9935<=u&&u<=9953||u==9955||9960<=u&&u<=9983||u==10045||u==10071||10102<=u&&u<=10111||11093<=u&&u<=11097||12872<=u&&u<=12879||57344<=u&&u<=63743||65024<=u&&u<=65039||u==65533||127232<=u&&u<=127242||127248<=u&&u<=127277||127280<=u&&u<=127337||127344<=u&&u<=127386||917760<=u&&u<=917999||983040<=u&&u<=1048573||1048576<=u&&u<=1114109?"A":"N"},e.characterLength=function(n){var s=this.eastAsianWidth(n);return s=="F"||s=="W"||s=="A"?2:1};function r(n){return n.match(/[\uD800-\uDBFF][\uDC00-\uDFFF]|[^\uD800-\uDFFF]/g)||[]}e.length=function(n){for(var s=r(n),i=0,u=0;u<s.length;u++)i=i+this.characterLength(s[u]);return i},e.slice=function(n,s,i){textLen=e.length(n),s=s||0,i=i||1,s<0&&(s=textLen+s),i<0&&(i=textLen+i);for(var u="",o=0,c=r(n),l=0;l<c.length;l++){var D=c[l],d=e.length(D);if(o>=s-(d==2?1:0))if(o+d<=i)u+=D;else break;o+=d}return u}})(ri);var yf=ri.exports;const Ff=R.getDefaultExportFromCjs(yf);var gf=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67)\uDB40\uDC7F|(?:\uD83E\uDDD1\uD83C\uDFFF\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFC-\uDFFF])|\uD83D\uDC68(?:\uD83C\uDFFB(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|[\u2695\u2696\u2708]\uFE0F|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))?|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])\uFE0F|\u200D(?:(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D[\uDC66\uDC67])|\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC)?|(?:\uD83D\uDC69(?:\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC69(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83E\uDDD1(?:\u200D(?:\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F\u200D\u26A7|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\uD83C\uDFF4\u200D\u2620|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u2600-\u2604\u260E\u2611\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26B0\u26B1\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0\u26F1\u26F4\u26F7\u26F8\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u3030\u303D\u3297\u3299]|\uD83C[\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3])\uFE0F|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDE35\u200D\uD83D\uDCAB|\uD83D\uDE2E\u200D\uD83D\uDCA8|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83E\uDDD1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC69(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF6\uD83C\uDDE6|\uD83C\uDDF4\uD83C\uDDF2|\uD83D\uDC08\u200D\u2B1B|\u2764\uFE0F\u200D(?:\uD83D\uDD25|\uD83E\uDE79)|\uD83D\uDC41\uFE0F|\uD83C\uDFF3\uFE0F|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|[#\*0-9]\uFE0F\u20E3|\u2764\uFE0F|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|\uD83C\uDFF4|(?:[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270C\u270D]|\uD83D[\uDD74\uDD90])(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC08\uDC15\uDC3B\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE2E\uDE35\uDE36\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5]|\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD]|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF]|[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0D\uDD0E\uDD10-\uDD17\uDD1D\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78\uDD7A-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCB\uDDD0\uDDE0-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6]|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26A7\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5-\uDED7\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDD77\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g};const Ef=R.getDefaultExportFromCjs(gf);function ie(t,e={}){if(typeof t!="string"||t.length===0||(e={ambiguousIsNarrow:!0,...e},t=Hu.default(t),t.length===0))return 0;t=t.replace(Ef(),"  ");const r=e.ambiguousIsNarrow?1:2;let n=0;for(const s of t){const i=s.codePointAt(0);if(i<=31||i>=127&&i<=159||i>=768&&i<=879)continue;switch(Ff.eastAsianWidth(s)){case"F":case"W":n+=2;break;case"A":n+=r;break;default:n+=1}}return n}function ni(t){let e=0;for(const r of t.split(`
`))e=Math.max(e,ie(r));return e}var rn={exports:{}};const Cf={topLeft:"┌",top:"─",topRight:"┐",right:"│",bottomRight:"┘",bottom:"─",bottomLeft:"└",left:"│"},bf={topLeft:"╔",top:"═",topRight:"╗",right:"║",bottomRight:"╝",bottom:"═",bottomLeft:"╚",left:"║"},wf={topLeft:"╭",top:"─",topRight:"╮",right:"│",bottomRight:"╯",bottom:"─",bottomLeft:"╰",left:"│"},_f={topLeft:"┏",top:"━",topRight:"┓",right:"┃",bottomRight:"┛",bottom:"━",bottomLeft:"┗",left:"┃"},xf={topLeft:"╓",top:"─",topRight:"╖",right:"║",bottomRight:"╜",bottom:"─",bottomLeft:"╙",left:"║"},Af={topLeft:"╒",top:"═",topRight:"╕",right:"│",bottomRight:"╛",bottom:"═",bottomLeft:"╘",left:"│"},Bf={topLeft:"+",top:"-",topRight:"+",right:"|",bottomRight:"+",bottom:"-",bottomLeft:"+",left:"|"},Sf={topLeft:"↘",top:"↓",topRight:"↙",right:"←",bottomRight:"↖",bottom:"↑",bottomLeft:"↗",left:"→"},vf={single:Cf,double:bf,round:wf,bold:_f,singleDouble:xf,doubleSingle:Af,classic:Bf,arrow:Sf},ui=vf;rn.exports=ui;rn.exports.default=ui;var Of=rn.exports;const Pf=R.getDefaultExportFromCjs(Of),Rf=/[\p{Lu}]/u,Tf=/[\p{Ll}]/u,Au=/^[\p{Lu}](?![\p{Lu}])/gu,si=/([\p{Alpha}\p{N}_]|$)/u,nn=/[_.\- ]+/,$f=new RegExp("^"+nn.source),Bu=new RegExp(nn.source+si.source,"gu"),Su=new RegExp("\\d+"+si.source,"gu"),jf=(t,e,r,n)=>{let s=!1,i=!1,u=!1,o=!1;for(let c=0;c<t.length;c++){const l=t[c];o=c>2?t[c-3]==="-":!0,s&&Rf.test(l)?(t=t.slice(0,c)+"-"+t.slice(c),s=!1,u=i,i=!0,c++):i&&u&&Tf.test(l)&&(!o||n)?(t=t.slice(0,c-1)+"-"+t.slice(c-1),u=i,i=!1,s=!0):(s=e(l)===l&&r(l)!==l,u=i,i=r(l)===l&&e(l)!==l)}return t},kf=(t,e)=>(Au.lastIndex=0,t.replace(Au,r=>e(r))),Lf=(t,e)=>(Bu.lastIndex=0,Su.lastIndex=0,t.replace(Bu,(r,n)=>e(n)).replace(Su,r=>e(r)));function Nf(t,e){if(!(typeof t=="string"||Array.isArray(t)))throw new TypeError("Expected the input to be `string | string[]`");if(e={pascalCase:!1,preserveConsecutiveUppercase:!1,...e},Array.isArray(t)?t=t.map(i=>i.trim()).filter(i=>i.length).join("-"):t=t.trim(),t.length===0)return"";const r=e.locale===!1?i=>i.toLowerCase():i=>i.toLocaleLowerCase(e.locale),n=e.locale===!1?i=>i.toUpperCase():i=>i.toLocaleUpperCase(e.locale);return t.length===1?nn.test(t)?"":e.pascalCase?n(t):r(t):(t!==r(t)&&(t=jf(t,r,n,e.preserveConsecutiveUppercase)),t=t.replace($f,""),t=e.preserveConsecutiveUppercase?kf(t,r):r(t),e.pascalCase&&(t=n(t.charAt(0))+t.slice(1)),Lf(t,n))}var un={exports:{}},If=({onlyFirst:t=!1}={})=>{const e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(e,t?void 0:"g")};const Uf=If;var qf=t=>typeof t=="string"?t.replace(Uf(),""):t,Hf=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F|\uD83D\uDC68(?:\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68\uD83C\uDFFB|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|[\u2695\u2696\u2708]\uFE0F|\uD83D[\uDC66\uDC67]|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708])\uFE0F|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C[\uDFFB-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)\uD83C\uDFFB|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB\uDFFC])|\uD83D\uDC69(?:\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D\uD83D\uDC69)(?:\uD83C[\uDFFB-\uDFFD])|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|(?:(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)\uFE0F|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\u200D[\u2640\u2642])|\uD83C\uDFF4\u200D\u2620)\uFE0F|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF4\uD83C\uDDF2|\uD83C\uDDF6\uD83C\uDDE6|[#\*0-9]\uFE0F\u20E3|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83D\uDC69(?:\uD83C[\uDFFB-\uDFFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270A-\u270D]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC70\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDCAA\uDD74\uDD7A\uDD90\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD36\uDDB5\uDDB6\uDDBB\uDDD2-\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5\uDEEB\uDEEC\uDEF4-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFA\uDFE0-\uDFEB]|\uD83E[\uDD0D-\uDD3A\uDD3C-\uDD45\uDD47-\uDD71\uDD73-\uDD76\uDD7A-\uDDA2\uDDA5-\uDDAA\uDDAE-\uDDCA\uDDCD-\uDDFF\uDE70-\uDE73\uDE78-\uDE7A\uDE80-\uDE82\uDE90-\uDE95])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g};const Mf=qf,zf=R.isFullwidthCodePointExports,Gf=Hf,ii=t=>{if(typeof t!="string"||t.length===0||(t=Mf(t),t.length===0))return 0;t=t.replace(Gf(),"  ");let e=0;for(let r=0;r<t.length;r++){const n=t.codePointAt(r);n<=31||n>=127&&n<=159||n>=768&&n<=879||(n>65535&&r++,e+=zf(n)?2:1)}return e};un.exports=ii;un.exports.default=ii;var Wf=un.exports;const Vf=Wf;function Pe(t,e){if(!t)return t;e=e||{};const r=e.align||"center";if(r==="left")return t;const n=e.split||`
`,s=e.pad||" ",i=r!=="right"?Kf:Yf;let u=!1;Array.isArray(t)||(u=!0,t=String(t).split(n));let o,c=0;return t=t.map(function(l){return l=String(l),o=Vf(l),c=Math.max(o,c),{str:l,width:o}}).map(function(l){return new Array(i(c,l.width)+1).join(s)+l.str}),u?t.join(n):t}Pe.left=function(e){return Pe(e,{align:"left"})};Pe.center=function(e){return Pe(e,{align:"center"})};Pe.right=function(e){return Pe(e,{align:"right"})};var Jf=Pe;function Kf(t,e){return Math.floor((t-e)/2)}function Yf(t,e){return t-e}const vu=R.getDefaultExportFromCjs(Jf),jr=10,Ou=(t=0)=>e=>`\x1B[${e+t}m`,Pu=(t=0)=>e=>`\x1B[${38+t};5;${e}m`,Ru=(t=0)=>(e,r,n)=>`\x1B[${38+t};2;${e};${r};${n}m`,M={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};Object.keys(M.modifier);const Qf=Object.keys(M.color),Xf=Object.keys(M.bgColor);[...Qf,...Xf];function Zf(){const t=new Map;for(const[e,r]of Object.entries(M)){for(const[n,s]of Object.entries(r))M[n]={open:`\x1B[${s[0]}m`,close:`\x1B[${s[1]}m`},r[n]=M[n],t.set(s[0],s[1]);Object.defineProperty(M,e,{value:r,enumerable:!1})}return Object.defineProperty(M,"codes",{value:t,enumerable:!1}),M.color.close="\x1B[39m",M.bgColor.close="\x1B[49m",M.color.ansi=Ou(),M.color.ansi256=Pu(),M.color.ansi16m=Ru(),M.bgColor.ansi=Ou(jr),M.bgColor.ansi256=Pu(jr),M.bgColor.ansi16m=Ru(jr),Object.defineProperties(M,{rgbToAnsi256:{value:(e,r,n)=>e===r&&r===n?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(r/255*5)+Math.round(n/255*5),enumerable:!1},hexToRgb:{value:e=>{const r=/[a-f\d]{6}|[a-f\d]{3}/i.exec(e.toString(16));if(!r)return[0,0,0];let[n]=r;n.length===3&&(n=[...n].map(i=>i+i).join(""));const s=Number.parseInt(n,16);return[s>>16&255,s>>8&255,s&255]},enumerable:!1},hexToAnsi256:{value:e=>M.rgbToAnsi256(...M.hexToRgb(e)),enumerable:!1},ansi256ToAnsi:{value:e=>{if(e<8)return 30+e;if(e<16)return 90+(e-8);let r,n,s;if(e>=232)r=((e-232)*10+8)/255,n=r,s=r;else{e-=16;const o=e%36;r=Math.floor(e/36)/5,n=Math.floor(o/6)/5,s=o%6/5}const i=Math.max(r,n,s)*2;if(i===0)return 30;let u=30+(Math.round(s)<<2|Math.round(n)<<1|Math.round(r));return i===2&&(u+=60),u},enumerable:!1},rgbToAnsi:{value:(e,r,n)=>M.ansi256ToAnsi(M.rgbToAnsi256(e,r,n)),enumerable:!1},hexToAnsi:{value:e=>M.ansi256ToAnsi(M.hexToAnsi256(e)),enumerable:!1}}),M}const eh=Zf(),cr=new Set(["\x1B",""]),th=39,sn="\x07",oi="[",rh="]",ai="m",on=`${rh}8;;`,Tu=t=>`${cr.values().next().value}${oi}${t}${ai}`,$u=t=>`${cr.values().next().value}${on}${t}${sn}`,nh=t=>t.split(" ").map(e=>ie(e)),kr=(t,e,r)=>{const n=[...e];let s=!1,i=!1,u=ie(Hu.default(t[t.length-1]));for(const[o,c]of n.entries()){const l=ie(c);if(u+l<=r?t[t.length-1]+=c:(t.push(c),u=0),cr.has(c)&&(s=!0,i=n.slice(o+1).join("").startsWith(on)),s){i?c===sn&&(s=!1,i=!1):c===ai&&(s=!1);continue}u+=l,u===r&&o<n.length-1&&(t.push(""),u=0)}!u&&t[t.length-1].length>0&&t.length>1&&(t[t.length-2]+=t.pop())},uh=t=>{const e=t.split(" ");let r=e.length;for(;r>0&&!(ie(e[r-1])>0);)r--;return r===e.length?t:e.slice(0,r).join(" ")+e.slice(r).join("")},sh=(t,e,r={})=>{if(r.trim!==!1&&t.trim()==="")return"";let n="",s,i;const u=nh(t);let o=[""];for(const[l,D]of t.split(" ").entries()){r.trim!==!1&&(o[o.length-1]=o[o.length-1].trimStart());let d=ie(o[o.length-1]);if(l!==0&&(d>=e&&(r.wordWrap===!1||r.trim===!1)&&(o.push(""),d=0),(d>0||r.trim===!1)&&(o[o.length-1]+=" ",d++)),r.hard&&u[l]>e){const h=e-d,p=1+Math.floor((u[l]-h-1)/e);Math.floor((u[l]-1)/e)<p&&o.push(""),kr(o,D,e);continue}if(d+u[l]>e&&d>0&&u[l]>0){if(r.wordWrap===!1&&d<e){kr(o,D,e);continue}o.push("")}if(d+u[l]>e&&r.wordWrap===!1){kr(o,D,e);continue}o[o.length-1]+=D}r.trim!==!1&&(o=o.map(l=>uh(l)));const c=[...o.join(`
`)];for(const[l,D]of c.entries()){if(n+=D,cr.has(D)){const{groups:h}=new RegExp(`(?:\\${oi}(?<code>\\d+)m|\\${on}(?<uri>.*)${sn})`).exec(c.slice(l).join(""))||{groups:{}};if(h.code!==void 0){const p=Number.parseFloat(h.code);s=p===th?void 0:p}else h.uri!==void 0&&(i=h.uri.length===0?void 0:h.uri)}const d=eh.codes.get(Number(s));c[l+1]===`
`?(i&&(n+=$u("")),s&&d&&(n+=Tu(d))):D===`
`&&(s&&d&&(n+=Tu(s)),i&&(n+=$u(i)))}return n};function ci(t,e,r){return String(t).normalize().replace(/\r\n/g,`
`).split(`
`).map(n=>sh(n,e,r)).join(`
`)}const Fe=`
`,ne=" ",dt="none",Di=()=>{const{env:t,stdout:e,stderr:r}=V;return e?.columns?e.columns:r?.columns?r.columns:t.COLUMNS?Number.parseInt(t.COLUMNS,10):80},ju=t=>typeof t=="number"?{top:t,right:t*3,bottom:t,left:t*3}:{top:0,right:0,bottom:0,left:0,...t},pt=t=>t===dt?0:2,ih=t=>{const e=["topLeft","topRight","bottomRight","bottomLeft","left","right","top","bottom"];let r;if(t===dt){t={};for(const n of e)t[n]=""}if(typeof t=="string"){if(r=Pf[t],!r)throw new TypeError(`Invalid border style: ${t}`)}else{typeof t?.vertical=="string"&&(t.left=t.vertical,t.right=t.vertical),typeof t?.horizontal=="string"&&(t.top=t.horizontal,t.bottom=t.horizontal);for(const n of e)if(t[n]===null||typeof t[n]!="string")throw new TypeError(`Invalid border style: ${n}`);r=t}return r},oh=(t,e,r)=>{let n="";const s=ie(t);switch(r){case"left":{n=t+e.slice(s);break}case"right":{n=e.slice(s)+t;break}default:{e=e.slice(s),e.length%2===1?(e=e.slice(Math.floor(e.length/2)),n=e.slice(1)+t+e):(e=e.slice(e.length/2),n=e+t+e);break}}return n},ah=(t,{padding:e,width:r,textAlignment:n,height:s})=>{t=vu(t,{align:n});let i=t.split(Fe);const u=ni(t),o=r-e.left-e.right;if(u>o){const D=[];for(const d of i){const h=ci(d,o,{hard:!0}),m=vu(h,{align:n}).split(`
`),f=Math.max(...m.map(F=>ie(F)));for(const F of m){let y;switch(n){case"center":{y=ne.repeat((o-f)/2)+F;break}case"right":{y=ne.repeat(o-f)+F;break}default:{y=F;break}}D.push(y)}}i=D}n==="center"&&u<o?i=i.map(D=>ne.repeat((o-u)/2)+D):n==="right"&&u<o&&(i=i.map(D=>ne.repeat(o-u)+D));const c=ne.repeat(e.left),l=ne.repeat(e.right);return i=i.map(D=>c+D+l),i=i.map(D=>{if(r-ie(D)>0)switch(n){case"center":return D+ne.repeat(r-ie(D));case"right":return D+ne.repeat(r-ie(D));default:return D+ne.repeat(r-ie(D))}return D}),e.top>0&&(i=[...Array.from({length:e.top}).fill(ne.repeat(r)),...i]),e.bottom>0&&(i=[...i,...Array.from({length:e.bottom}).fill(ne.repeat(r))]),s&&i.length>s?i=i.slice(0,s):s&&i.length<s&&(i=[...i,...Array.from({length:s-i.length}).fill(ne.repeat(r))]),i.join(Fe)},ch=(t,e,r)=>{const n=D=>{const d=r.borderColor?fh(r.borderColor)(D):D;return r.dimBorder?oe.default.dim(d):d},s=D=>r.backgroundColor?hh(r.backgroundColor)(D):D,i=ih(r.borderStyle),u=Di();let o=ne.repeat(r.margin.left);if(r.float==="center"){const D=Math.max((u-e-pt(r.borderStyle))/2,0);o=ne.repeat(D)}else if(r.float==="right"){const D=Math.max(u-e-r.margin.right-pt(r.borderStyle),0);o=ne.repeat(D)}let c="";r.margin.top&&(c+=Fe.repeat(r.margin.top)),(r.borderStyle!==dt||r.title)&&(c+=n(o+i.topLeft+(r.title?oh(r.title,i.top.repeat(e),r.titleAlignment):i.top.repeat(e))+i.topRight)+Fe);const l=t.split(Fe);return c+=l.map(D=>o+n(i.left)+s(D)+n(i.right)).join(Fe),r.borderStyle!==dt&&(c+=Fe+n(o+i.bottomLeft+i.bottom.repeat(e)+i.bottomRight)),r.margin.bottom&&(c+=Fe.repeat(r.margin.bottom)),c},Dh=t=>{if(t.fullscreen&&V?.stdout){let e=[V.stdout.columns,V.stdout.rows];typeof t.fullscreen=="function"&&(e=t.fullscreen(...e)),t.width||(t.width=e[0]),t.height||(t.height=e[1])}return t.width&&(t.width=Math.max(1,t.width-pt(t.borderStyle))),t.height&&(t.height=Math.max(1,t.height-pt(t.borderStyle))),t},ku=(t,e)=>e===dt?t:` ${t} `,lh=(t,e)=>{e=Dh(e);const r=e.width!==void 0,n=Di(),s=pt(e.borderStyle),i=n-e.margin.left-e.margin.right-s,u=ni(ci(t,n-s,{hard:!0,trim:!1}))+e.padding.left+e.padding.right;if(e.title&&r?(e.title=e.title.slice(0,Math.max(0,e.width-2)),e.title&&(e.title=ku(e.title,e.borderStyle))):e.title&&(e.title=e.title.slice(0,Math.max(0,i-2)),e.title&&(e.title=ku(e.title,e.borderStyle),ie(e.title)>u&&(e.width=ie(e.title)))),e.width=e.width?e.width:u,!r){if(e.margin.left&&e.margin.right&&e.width>i){const c=(n-e.width-s)/(e.margin.left+e.margin.right);e.margin.left=Math.max(0,Math.floor(e.margin.left*c)),e.margin.right=Math.max(0,Math.floor(e.margin.right*c))}e.width=Math.min(e.width,n-s-e.margin.left-e.margin.right)}return e.width-(e.padding.left+e.padding.right)<=0&&(e.padding.left=0,e.padding.right=0),e.height&&e.height-(e.padding.top+e.padding.bottom)<=0&&(e.padding.top=0,e.padding.bottom=0),e},an=t=>t.match(/^#(?:[0-f]{3}){1,2}$/i),Lu=t=>typeof t=="string"&&(oe.default[t]??an(t)),fh=t=>an(t)?oe.default.hex(t):oe.default[t],hh=t=>an(t)?oe.default.bgHex(t):oe.default[Nf(["bg",t])];function Nu(t,e){if(e={padding:0,borderStyle:"single",dimBorder:!1,textAlignment:"left",float:"left",titleAlignment:"left",...e},e.align&&(e.textAlignment=e.align),e.borderColor&&!Lu(e.borderColor))throw new Error(`${e.borderColor} is not a valid borderColor`);if(e.backgroundColor&&!Lu(e.backgroundColor))throw new Error(`${e.backgroundColor} is not a valid backgroundColor`);return e.padding=ju(e.padding),e.margin=ju(e.margin),e=lh(t,e),t=ah(t,e),ch(t,e.width,e)}const dh=V.env.CI!=="0"&&V.env.CI!=="false"&&("CI"in V.env||"CONTINUOUS_INTEGRATION"in V.env||Object.keys(V.env).some(t=>t.startsWith("CI_"))),ph=dh,Iu=t=>t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;");function mh(t,...e){if(typeof t=="string")return Iu(t);let r=t[0];for(const[n,s]of e.entries())r=r+Iu(String(s))+t[n+1];return r}class yh extends Error{constructor(e){super(`Missing a value for ${e?`the placeholder: ${e}`:"a placeholder"}`,e),this.name="MissingValueError",this.key=e}}function Fh(t,e,{ignoreMissing:r=!1,transform:n=({value:s})=>s}={}){if(typeof t!="string")throw new TypeError(`Expected a \`string\` in the first argument, got \`${typeof t}\``);if(typeof e!="object")throw new TypeError(`Expected an \`object\` or \`Array\` in the second argument, got \`${typeof e}\``);const s=(c,l)=>{let D=e;for(const h of l.split("."))D=D?D[h]:void 0;const d=n({value:D,key:l});if(d===void 0){if(r)return c;throw new yh(l)}return String(d)},i=c=>(...l)=>mh(c(...l)),u=/{{(\d+|[a-z$_][\w\-$]*?(?:\.[\w\-$]*?)*?)}}/gi;u.test(t)&&(t=t.replace(u,i(s)));const o=/{(\d+|[a-z$_][\w\-$]*?(?:\.[\w\-$]*?)*?)}/gi;return t.replace(o,s)}const gh=J.dirname(K.fileURLToPath(typeof document>"u"?require("url").pathToFileURL(__filename).href:cn&&cn.src||new URL("index-5sFb3Tvv.js",document.baseURI).href)),Eh=1e3*60*60*24;class Ch{config;update;_packageName;_shouldNotifyInNpmScript;#t;#e;#n;#r;constructor(e={}){if(this.#t=e,e.pkg=e.pkg??{},e.distTag=e.distTag??"latest",e.pkg={name:e.pkg.name??e.packageName,version:e.pkg.version??e.packageVersion},!e.pkg.name||!e.pkg.version)throw new Error("pkg.name and pkg.version required");if(this._packageName=e.pkg.name,this.#e=e.pkg.version,this.#n=typeof e.updateCheckInterval=="number"?e.updateCheckInterval:Eh,this.#r="NO_UPDATE_NOTIFIER"in V.env||V.env.NODE_ENV==="test"||V.argv.includes("--no-update-notifier")||ph,this._shouldNotifyInNpmScript=e.shouldNotifyInNpmScript,!this.#r)try{this.config=new fo(`update-notifier-${this._packageName}`,{optOut:!1,lastUpdateCheck:Date.now()})}catch{const r=oe.default.yellow(te.format(" %s update check failed ",e.pkg.name))+te.format(`
 Try running with %s or get access `,oe.default.cyan("sudo"))+`
 to the local update config store via 
`+oe.default.cyan(te.format(" sudo chown -R $USER:$(id -gn $USER) %s ",Wt));V.on("exit",()=>{console.error(Nu(r,{textAlignment:"center"}))})}}check(){!this.config||this.config.get("optOut")||this.#r||(this.update=this.config.get("update"),this.update&&(this.update.current=this.#e,this.config.delete("update")),!(Date.now()-this.config.get("lastUpdateCheck")<this.#n)&&fi.spawn(V.execPath,[J.join(gh,"check.js"),JSON.stringify(this.#t)],{detached:!0,stdio:"ignore"}).unref())}async fetchInfo(){const{distTag:e}=this.#t,r=await sf(this._packageName,{version:e});return{latest:r,current:this.#e,type:ho(this.#e,r)??e,name:this._packageName}}notify(e){const r=!this._shouldNotifyInNpmScript&&lf;if(!V.stdout.isTTY||r||!this.update||!R.semver.gt(this.update.latest,this.update.current))return this;e={isGlobal:mf,...e};const n=e.isGlobal?`npm i -g ${this._packageName}`:`npm i ${this._packageName}`,s="Update available "+oe.default.dim("{currentVersion}")+oe.default.reset(" → ")+oe.default.green("{latestVersion}")+` 
Run `+oe.default.cyan("{updateCommand}")+" to update",i=e.message||s;e.boxenOptions??={padding:1,margin:1,textAlignment:"center",borderColor:"yellow",borderStyle:"round"};const u=Nu(Fh(i,{packageName:this._packageName,currentVersion:this.update.current,latestVersion:this.update.latest,updateCommand:n}),e.boxenOptions);return e.defer===!1?console.error(u):(V.on("exit",()=>{console.error(u)}),V.on("SIGINT",()=>{console.error(""),V.exit()})),this}}function bh(t){const e=new Ch(t);return e.check(),e}exports.default=bh;
//# sourceMappingURL=index-5sFb3Tvv.js.map
