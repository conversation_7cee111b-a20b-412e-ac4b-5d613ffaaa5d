"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const ADD_1 = __importDefault(require("./ADD"));
const ALTER_1 = __importDefault(require("./ALTER"));
const CREATE_1 = __importDefault(require("./CREATE"));
const CREATERULE_1 = __importDefault(require("./CREATERULE"));
const DECRBY_1 = __importDefault(require("./DECRBY"));
const DEL_1 = __importDefault(require("./DEL"));
const DELETERULE_1 = __importDefault(require("./DELETERULE"));
const GET_1 = __importDefault(require("./GET"));
const INCRBY_1 = __importDefault(require("./INCRBY"));
const INFO_DEBUG_1 = __importDefault(require("./INFO_DEBUG"));
const INFO_1 = __importDefault(require("./INFO"));
const MADD_1 = __importDefault(require("./MADD"));
const MGET_SELECTED_LABELS_1 = __importDefault(require("./MGET_SELECTED_LABELS"));
const MGET_WITHLABELS_1 = __importDefault(require("./MGET_WITHLABELS"));
const MGET_1 = __importDefault(require("./MGET"));
const MRANGE_GROUPBY_1 = __importDefault(require("./MRANGE_GROUPBY"));
const MRANGE_SELECTED_LABELS_GROUPBY_1 = __importDefault(require("./MRANGE_SELECTED_LABELS_GROUPBY"));
const MRANGE_SELECTED_LABELS_1 = __importDefault(require("./MRANGE_SELECTED_LABELS"));
const MRANGE_WITHLABELS_GROUPBY_1 = __importDefault(require("./MRANGE_WITHLABELS_GROUPBY"));
const MRANGE_WITHLABELS_1 = __importDefault(require("./MRANGE_WITHLABELS"));
const MRANGE_1 = __importDefault(require("./MRANGE"));
const MREVRANGE_GROUPBY_1 = __importDefault(require("./MREVRANGE_GROUPBY"));
const MREVRANGE_SELECTED_LABELS_GROUPBY_1 = __importDefault(require("./MREVRANGE_SELECTED_LABELS_GROUPBY"));
const MREVRANGE_SELECTED_LABELS_1 = __importDefault(require("./MREVRANGE_SELECTED_LABELS"));
const MREVRANGE_WITHLABELS_GROUPBY_1 = __importDefault(require("./MREVRANGE_WITHLABELS_GROUPBY"));
const MREVRANGE_WITHLABELS_1 = __importDefault(require("./MREVRANGE_WITHLABELS"));
const MREVRANGE_1 = __importDefault(require("./MREVRANGE"));
const QUERYINDEX_1 = __importDefault(require("./QUERYINDEX"));
const RANGE_1 = __importDefault(require("./RANGE"));
const REVRANGE_1 = __importDefault(require("./REVRANGE"));
__exportStar(require("./helpers"), exports);
exports.default = {
    ADD: ADD_1.default,
    add: ADD_1.default,
    ALTER: ALTER_1.default,
    alter: ALTER_1.default,
    CREATE: CREATE_1.default,
    create: CREATE_1.default,
    CREATERULE: CREATERULE_1.default,
    createRule: CREATERULE_1.default,
    DECRBY: DECRBY_1.default,
    decrBy: DECRBY_1.default,
    DEL: DEL_1.default,
    del: DEL_1.default,
    DELETERULE: DELETERULE_1.default,
    deleteRule: DELETERULE_1.default,
    GET: GET_1.default,
    get: GET_1.default,
    INCRBY: INCRBY_1.default,
    incrBy: INCRBY_1.default,
    INFO_DEBUG: INFO_DEBUG_1.default,
    infoDebug: INFO_DEBUG_1.default,
    INFO: INFO_1.default,
    info: INFO_1.default,
    MADD: MADD_1.default,
    mAdd: MADD_1.default,
    MGET_SELECTED_LABELS: MGET_SELECTED_LABELS_1.default,
    mGetSelectedLabels: MGET_SELECTED_LABELS_1.default,
    MGET_WITHLABELS: MGET_WITHLABELS_1.default,
    mGetWithLabels: MGET_WITHLABELS_1.default,
    MGET: MGET_1.default,
    mGet: MGET_1.default,
    MRANGE_GROUPBY: MRANGE_GROUPBY_1.default,
    mRangeGroupBy: MRANGE_GROUPBY_1.default,
    MRANGE_SELECTED_LABELS_GROUPBY: MRANGE_SELECTED_LABELS_GROUPBY_1.default,
    mRangeSelectedLabelsGroupBy: MRANGE_SELECTED_LABELS_GROUPBY_1.default,
    MRANGE_SELECTED_LABELS: MRANGE_SELECTED_LABELS_1.default,
    mRangeSelectedLabels: MRANGE_SELECTED_LABELS_1.default,
    MRANGE_WITHLABELS_GROUPBY: MRANGE_WITHLABELS_GROUPBY_1.default,
    mRangeWithLabelsGroupBy: MRANGE_WITHLABELS_GROUPBY_1.default,
    MRANGE_WITHLABELS: MRANGE_WITHLABELS_1.default,
    mRangeWithLabels: MRANGE_WITHLABELS_1.default,
    MRANGE: MRANGE_1.default,
    mRange: MRANGE_1.default,
    MREVRANGE_GROUPBY: MREVRANGE_GROUPBY_1.default,
    mRevRangeGroupBy: MREVRANGE_GROUPBY_1.default,
    MREVRANGE_SELECTED_LABELS_GROUPBY: MREVRANGE_SELECTED_LABELS_GROUPBY_1.default,
    mRevRangeSelectedLabelsGroupBy: MREVRANGE_SELECTED_LABELS_GROUPBY_1.default,
    MREVRANGE_SELECTED_LABELS: MREVRANGE_SELECTED_LABELS_1.default,
    mRevRangeSelectedLabels: MREVRANGE_SELECTED_LABELS_1.default,
    MREVRANGE_WITHLABELS_GROUPBY: MREVRANGE_WITHLABELS_GROUPBY_1.default,
    mRevRangeWithLabelsGroupBy: MREVRANGE_WITHLABELS_GROUPBY_1.default,
    MREVRANGE_WITHLABELS: MREVRANGE_WITHLABELS_1.default,
    mRevRangeWithLabels: MREVRANGE_WITHLABELS_1.default,
    MREVRANGE: MREVRANGE_1.default,
    mRevRange: MREVRANGE_1.default,
    QUERYINDEX: QUERYINDEX_1.default,
    queryIndex: QUERYINDEX_1.default,
    RANGE: RANGE_1.default,
    range: RANGE_1.default,
    REVRANGE: REVRANGE_1.default,
    revRange: REVRANGE_1.default
};
//# sourceMappingURL=index.js.map