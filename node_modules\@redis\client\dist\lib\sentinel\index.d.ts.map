{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../lib/sentinel/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAC3C,OAAO,EAAE,gBAAgB,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AACpI,OAAO,WAAW,EAAE,EAAsB,eAAe,EAAE,MAAM,WAAW,CAAC;AAC7E,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAG1D,OAAO,EAA8G,SAAS,EAAE,uBAAuB,EAAsB,oBAAoB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,SAAS,CAAC;AAEzP,OAAO,EAAE,uBAAuB,EAAE,MAAM,kBAAkB,CAAC;AAC3D,OAAkC,EAAE,6BAA6B,EAAE,MAAM,kBAAkB,CAAC;AAC5F,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAInD,OAAO,EAAE,qBAAqB,EAAE,MAAM,kCAAkC,CAAC;AAIzE,OAAO,EAA8B,6BAA6B,EAAE,MAAM,iBAAiB,CAAC;AAE5F,UAAU,UAAU;IAClB,EAAE,EAAE,MAAM,CAAC;CACZ;AAED,qBAAa,mBAAmB,CAC9B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW;;IAIhC,QAAQ,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;IAEjE;;;;OAIG;IAEH,IAAI,MAAM,YAET;IAED;;;;OAIG;IACH,IAAI,OAAO,YAEV;IAED;;;;OAIG;IACH,IAAI,cAAc,6CAEjB;gBAKC,QAAQ,EAAE,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,EAC5D,UAAU,EAAE,UAAU,EACtB,cAAc,CAAC,EAAE,cAAc,CAAC,YAAY,CAAC;IAQ/C,MAAM,CAAC,OAAO,CACZ,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,YAAY,SAAS,WAAW,GAAG,EAAE,EACrC,MAAM,CAAC,EAAE,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,cAc3C,sBAAsB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,cAChD,UAAU,mBACL,eAAe,YAAY,CAAC;IAOjD,MAAM,CAAC,MAAM,CACX,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,YAAY,SAAS,WAAW,GAAG,EAAE,EAErC,OAAO,EAAE,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,EAC1D,QAAQ,EAAE,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,EAC5D,UAAU,EAAE,UAAU,EACtB,cAAc,CAAC,EAAE,cAAc,CAAC,YAAY,CAAC;IAK/C,kBAAkB,CAChB,OAAO,SAAS,cAAc,CAAC,YAAY,CAAC,EAC5C,YAAY,SAAS,WAAW,EAChC,OAAO,EAAE,OAAO;IAYlB,OAAO,CAAC,oBAAoB;IAmB5B;;OAEG;IACH,eAAe,CAAC,YAAY,SAAS,WAAW,EAAE,WAAW,EAAE,YAAY;IAIrE,QAAQ,CAAC,CAAC,EACd,UAAU,EAAE,OAAO,GAAG,SAAS,EAC/B,EAAE,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,GAC7G,OAAO,CAAC,CAAC,CAAC;IAQP,WAAW,CAAC,CAAC,GAAG,UAAU,EAC9B,UAAU,EAAE,OAAO,GAAG,SAAS,EAC/B,IAAI,EAAE,gBAAgB,EACtB,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,CAAC,CAAC;IAOb;;OAEG;IACG,gBAAgB,CACpB,UAAU,EAAE,OAAO,GAAG,SAAS,EAC/B,QAAQ,EAAE,KAAK,CAAC,uBAAuB,CAAC;IAQ1C;;QAEI;IACE,aAAa,CACjB,UAAU,EAAE,OAAO,GAAG,SAAS,EAC/B,QAAQ,EAAE,KAAK,CAAC,uBAAuB,CAAC;IAQ1C,KAAK,IAAI,6BAA6B,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;IAIvE,KAAK,QAJI,8BAA8B,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAIpD;IAEnB,KAAK,CAAC,GAAG,EAAE,qBAAqB;IAWhC,KAAK,QAXM,qBAAqB,mBAWb;IAEnB,OAAO;IAWP,OAAO,sBAAgB;IAEvB;;;;;;;;;OASG;IACH,OAAO;CASR;AAED,MAAM,CAAC,OAAO,OAAO,aAAa,CAChC,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,CAChC,SAAQ,YAAY;;IACpB,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;IAK3D;;;;OAIG;IACH,IAAI,MAAM,YAET;IAED;;;;OAIG;IACH,IAAI,OAAO,YAEV;IAED,IAAI,cAAc,6CAEjB;IAUD,IAAI,eAAe,8CAElB;gBAEW,OAAO,EAAE,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;IAuBtE,MAAM,CAAC,OAAO,CACZ,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,YAAY,SAAS,WAAW,GAAG,EAAE,EACrC,MAAM,CAAC,EAAE,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,aAatC,KAAK,oBAAoB,EAAE,MAAM,QAAQ,aAAa,EAAE,SAAS,CAAC,CAAC;IAMtF,MAAM,CAAC,MAAM,CACX,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,CAAC,SAAS,cAAc,GAAG,EAAE,EAC7B,CAAC,SAAS,YAAY,GAAG,EAAE,EAC3B,IAAI,SAAS,YAAY,GAAG,CAAC,EAC7B,YAAY,SAAS,WAAW,GAAG,EAAE,EACrC,OAAO,EAAE,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;IAI5D,kBAAkB,CAChB,OAAO,SAAS,cAAc,CAAC,YAAY,CAAC,EAC5C,YAAY,SAAS,WAAW,EAChC,OAAO,EAAE,OAAO;IAYlB,OAAO,CAAC,oBAAoB;IAsB5B;;OAEG;IACH,eAAe,CAAC,YAAY,SAAS,WAAW,EAAE,WAAW,EAAE,YAAY;IAIrE,OAAO;IAUP,QAAQ,CAAC,CAAC,EACd,UAAU,EAAE,OAAO,GAAG,SAAS,EAC/B,EAAE,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,GAC7G,OAAO,CAAC,CAAC,CAAC;IA2BP,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,cAAc,EAAE,uBAAuB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC;IAa/F,WAAW,CAAC,CAAC,GAAG,UAAU,EAC9B,UAAU,EAAE,OAAO,GAAG,SAAS,EAC/B,IAAI,EAAE,gBAAgB,EACtB,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,CAAC,CAAC;IAOb;;OAEG;IACG,gBAAgB,CACpB,UAAU,EAAE,OAAO,GAAG,SAAS,EAC/B,QAAQ,EAAE,KAAK,CAAC,uBAAuB,CAAC;IAQ1C;;QAEI;IACE,aAAa,CACjB,UAAU,EAAE,OAAO,GAAG,SAAS,EAC/B,QAAQ,EAAE,KAAK,CAAC,uBAAuB,CAAC;IAQ1C,KAAK,IAAI,6BAA6B,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;IAIvE,KAAK,QAJI,8BAA8B,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAIpD;IAEb,KAAK;IAIX,OAAO;IAID,SAAS,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACvC,QAAQ,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EAChC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,EAC3B,UAAU,CAAC,EAAE,CAAC;IAKhB,SAAS,wCAPG,MAAM,GAAG,MAAM,MAAM,CAAC,wFAOP;IAErB,WAAW,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACzC,QAAQ,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EACjC,QAAQ,CAAC,EAAE,cAAc,CAAC,OAAO,CAAC,EAClC,UAAU,CAAC,EAAE,CAAC;IAKhB,WAAW,yCAPE,MAAM,GAAG,MAAM,MAAM,CAAC,aACtB,eAAe,OAAO,CAAC,2DAML;IAEzB,UAAU,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACxC,QAAQ,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EAChC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,EAC3B,UAAU,CAAC,EAAE,CAAC;IAKhB,UAAU,wCAPE,MAAM,GAAG,MAAM,MAAM,CAAC,wFAOL;IAEvB,YAAY,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EAC1C,QAAQ,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EACjC,QAAQ,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,EAC5B,UAAU,CAAC,EAAE,CAAC;IAKhB,YAAY,yCAPC,MAAM,GAAG,MAAM,MAAM,CAAC,qGAOF;IAEjC;;;;;;;;;;;;;;;;;;;;OAoBG;IACG,OAAO,IAAI,OAAO,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;IAK9E,eAAe,IAAI,SAAS,GAAG,SAAS;IAIxC,aAAa,IAAI,SAAS,GAAG,SAAS;IAItC,eAAe,IAAI,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC;IAIzC,SAAS,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC;CASjC;AAED,cAAM,qBAAqB,CACzB,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,YAAY,SAAS,WAAW,CAChC,SAAQ,YAAY;;IAGpB,IAAI,MAAM,YAET;IAID,IAAI,OAAO,YAEV;IAwBD,IAAI,WAAW,YAEd;IAaD,IAAI,eAAe,8CAElB;gBAQW,OAAO,EAAE,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;IAgEtE;;;;;OAKG;IACH,cAAc,IAAI,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;IASlD;;;;;;;;;OASG;IACH,kBAAkB,CAAC,UAAU,EAAE,UAAU;IAcnC,OAAO;IAuDP,OAAO,CAAC,CAAC,EACb,EAAE,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EAClH,UAAU,CAAC,EAAE,UAAU,GACtB,OAAO,CAAC,CAAC,CAAC;IAoGP,KAAK;IAkDL,OAAO;IA2CP,SAAS,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACvC,QAAQ,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EAChC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,EAC3B,UAAU,CAAC,EAAE,CAAC;IAKV,WAAW,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACzC,QAAQ,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EACjC,QAAQ,CAAC,EAAE,cAAc,CAAC,OAAO,CAAC,EAClC,UAAU,CAAC,EAAE,CAAC;IAKV,UAAU,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EACxC,QAAQ,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EAChC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,EAC3B,UAAU,CAAC,EAAE,CAAC;IAKV,YAAY,CAAC,CAAC,SAAS,OAAO,GAAG,KAAK,EAC1C,QAAQ,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,EACjC,QAAQ,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,EAC5B,UAAU,CAAC,EAAE,CAAC;IAMV,OAAO;;;;;;;;;;;;;;;;;IA8Cb,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;;;;;;;;IAqE9F,SAAS,CAAC,QAAQ,EAAE,UAAU,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,SAAS,CAAC,CAAC;IAkLnG,aAAa,IAAI,SAAS,GAAG,SAAS;IActC,eAAe,IAAI,SAAS,GAAG,SAAS;IAQxC,eAAe,IAAI,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC;IAwBzC,SAAS,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC;CAQjC;AAED,qBAAa,oBAAqB,SAAQ,YAAY;;IACpD,OAAO,EAAE,oBAAoB,CAAC;gBAIlB,OAAO,EAAE,oBAAoB;IAOnC,uBAAuB;IAiCvB,aAAa;IA+Cb,eAAe;IAYf,eAAe;IA+Cf,gBAAgB;CAoBvB"}