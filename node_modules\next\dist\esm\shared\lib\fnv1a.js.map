{"version": 3, "sources": ["../../../src/shared/lib/fnv1a.ts"], "names": ["FNV_PRIMES", "BigInt", "FNV_OFFSETS", "fnv1a", "inputString", "size", "seed", "Error", "hash", "fnvPrime", "isUnicoded", "index", "length", "characterCode", "charCodeAt", "unescape", "encodeURIComponent", "asUintN"], "mappings": "AAAA,gDAAgD;AAChD,kCAAkC;AAClC,iEAAiE;AACjE,0DAA0D,GAE1D,MAAMA,aAAa;IACjB,IAAIC,OAAO;IACX,IAAIA,OAAO;IACX,KAAKA,OAAO;IACZ,KAAKA,OACH;IAEF,KAAKA,OACH;IAEF,MAAMA,OACJ;AAEJ;AAEA,MAAMC,cAAc;IAClB,IAAID,OAAO;IACX,IAAIA,OAAO;IACX,KAAKA,OAAO;IACZ,KAAKA,OACH;IAEF,KAAKA,OACH;IAEF,MAAMA,OACJ;AAEJ;AAEA,eAAe,SAASE,MACtBC,WAAmB,EACnB;IAAA,IAAA,EACEC,OAAO,EAAE,EACTC,OAAO,CAAC,EAIT,GAND,mBAMI,CAAC,IANL;IAQA,IAAI,CAACN,UAAU,CAACK,KAAK,EAAE;QACrB,MAAM,IAAIE,MACR;IAEJ;IAEA,IAAIC,OAAeN,WAAW,CAACG,KAAK,GAAGJ,OAAOK;IAC9C,MAAMG,WAAWT,UAAU,CAACK,KAAK;IAEjC,oCAAoC;IACpC,IAAIK,aAAa;IAEjB,IAAK,IAAIC,QAAQ,GAAGA,QAAQP,YAAYQ,MAAM,EAAED,QAAS;QACvD,IAAIE,gBAAgBT,YAAYU,UAAU,CAACH;QAE3C,wDAAwD;QACxD,IAAIE,gBAAgB,QAAQ,CAACH,YAAY;YACvCN,cAAcW,SAASC,mBAAmBZ;YAC1CS,gBAAgBT,YAAYU,UAAU,CAACH;YACvCD,aAAa;QACf;QAEAF,QAAQP,OAAOY;QACfL,OAAOP,OAAOgB,OAAO,CAACZ,MAAMG,OAAOC;IACrC;IAEA,OAAOD;AACT"}