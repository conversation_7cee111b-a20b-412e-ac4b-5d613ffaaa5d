"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n// Define protected routes\nconst protectedRoutes = [\n    \"/dashboard\",\n    \"/osint\",\n    \"/scanner\",\n    \"/file-analyzer\",\n    \"/cve\",\n    \"/dorking\",\n    \"/playground\",\n    \"/tools\",\n    \"/bot\",\n    \"/leaderboard\",\n    \"/profile\",\n    \"/plan\",\n    \"/admin\"\n];\n// Define admin-only routes\nconst adminRoutes = [\n    \"/admin\"\n];\n// Define public routes that should redirect to dashboard if authenticated\nconst publicRoutes = [\n    \"/login\",\n    \"/register\"\n];\n// Simple authentication check for Edge Runtime\nasync function checkAuthentication(request) {\n    try {\n        // Get token from cookies\n        const cookies = request.headers.get(\"cookie\");\n        if (!cookies) return false;\n        const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n        const token = tokenMatch ? tokenMatch[1] : null;\n        if (!token) return false;\n        // Simple token validation - just check if it exists and looks like a JWT\n        const parts = token.split(\".\");\n        if (parts.length !== 3) return false;\n        try {\n            // Try to decode the payload (without verification for Edge Runtime)\n            const payload = JSON.parse(atob(parts[1]));\n            // Check if token is not expired\n            if (payload.exp && payload.exp < Date.now() / 1000) {\n                return false;\n            }\n            return true;\n        } catch  {\n            return false;\n        }\n    } catch  {\n        return false;\n    }\n}\nasync function middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Skip middleware for API routes, static files, and other assets\n    if (pathname.startsWith(\"/api/\") || pathname.startsWith(\"/_next/\") || pathname.startsWith(\"/favicon.ico\") || pathname.includes(\".\")) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Get authentication status - simplified for Edge Runtime\n    const authResult = await checkAuthentication(request);\n    const isAuthenticated = authResult.success;\n    const user = authResult.user;\n    console.log(`🔐 Middleware: ${pathname} - Auth: ${isAuthenticated} - User: ${user?.username || \"none\"}`);\n    // Handle protected routes\n    if (protectedRoutes.some((route)=>pathname.startsWith(route))) {\n        if (!isAuthenticated) {\n            console.log(`❌ Redirecting unauthenticated user from ${pathname} to /login`);\n            const loginUrl = new URL(\"/login\", request.url);\n            loginUrl.searchParams.set(\"redirect\", pathname);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(loginUrl);\n        }\n        // Check admin routes\n        if (adminRoutes.some((route)=>pathname.startsWith(route))) {\n            if (user?.role !== \"admin\" && user?.role !== \"super_admin\") {\n                console.log(`❌ Unauthorized access attempt to ${pathname} by ${user?.username}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n            }\n        }\n        // User is authenticated and authorized, continue\n        console.log(`✅ Authorized access to ${pathname} by ${user?.username}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Handle public routes (login, register)\n    if (publicRoutes.some((route)=>pathname === route)) {\n        if (isAuthenticated) {\n            console.log(`🔄 Redirecting authenticated user from ${pathname} to /dashboard`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n        }\n        // User is not authenticated, allow access to public routes\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Handle root route\n    if (pathname === \"/\") {\n        if (isAuthenticated) {\n            console.log(`🔄 Redirecting authenticated user from / to /dashboard`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n        } else {\n            console.log(`🔄 Redirecting unauthenticated user from / to /login`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/login\", request.url));\n        }\n    }\n    // For all other routes, continue normally\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder files\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|.*\\\\..*$).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});