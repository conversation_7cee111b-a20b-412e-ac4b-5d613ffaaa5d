{"version": 3, "sources": ["../../../src/shared/lib/bloom-filter.ts"], "names": ["murmurhash2", "str", "h", "i", "length", "c", "charCodeAt", "Math", "imul", "DEFAULT_ERROR_RATE", "<PERSON><PERSON><PERSON><PERSON>", "from", "items", "errorRate", "filter", "item", "add", "export", "data", "numItems", "numBits", "numHashes", "bitArray", "process", "env", "NEXT_RUNTIME", "filterData", "JSON", "stringify", "gzipSize", "require", "sync", "console", "warn", "import", "hashValues", "getHashValues", "for<PERSON>ach", "hash", "contains", "every", "push", "constructor", "ceil", "log", "Array", "fill"], "mappings": "AAAA,mDAAmD;AACnD,SAASA,YAAYC,GAAW;IAC9B,IAAIC,IAAI;IACR,IAAK,IAAIC,IAAI,GAAGA,IAAIF,IAAIG,MAAM,EAAED,IAAK;QACnC,MAAME,IAAIJ,IAAIK,UAAU,CAACH;QACzBD,IAAIK,KAAKC,IAAI,CAACN,IAAIG,GAAG;QACrBH,KAAKA,MAAM;QACXA,IAAIK,KAAKC,IAAI,CAACN,GAAG;IACnB;IACA,OAAOA,MAAM;AACf;AAEA,iEAAiE;AACjE,MAAMO,qBAAqB;AAE3B,OAAO,MAAMC;IAiBX,OAAOC,KAAKC,KAAe,EAAEC,SAA8B,EAAE;QAAhCA,IAAAA,sBAAAA,YAAYJ;QACvC,MAAMK,SAAS,IAAIJ,YAAYE,MAAMR,MAAM,EAAES;QAE7C,KAAK,MAAME,QAAQH,MAAO;YACxBE,OAAOE,GAAG,CAACD;QACb;QACA,OAAOD;IACT;IAEAG,SAAS;QACP,MAAMC,OAAO;YACXC,UAAU,IAAI,CAACA,QAAQ;YACvBN,WAAW,IAAI,CAACA,SAAS;YACzBO,SAAS,IAAI,CAACA,OAAO;YACrBC,WAAW,IAAI,CAACA,SAAS;YACzBC,UAAU,IAAI,CAACA,QAAQ;QACzB;QAEA,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAU;YACzC,IAAI,IAAI,CAACZ,SAAS,GAAGJ,oBAAoB;gBACvC,MAAMiB,aAAaC,KAAKC,SAAS,CAACV;gBAClC,MAAMW,WAAWC,QAAQ,gCAAgCC,IAAI,CAC3DL;gBAGF,IAAIG,WAAW,MAAM;oBACnBG,QAAQC,IAAI,CACV,AAAC,yIAAsI,IAAI,CAACpB,SAAS,GAAC,uBAAoBa,WAAWtB,MAAM,GAAC,aAAUyB,WAAS;gBAEnN;YACF;QACF;QAEA,OAAOX;IACT;IAEAgB,OAAOhB,IAAyC,EAAE;QAChD,IAAI,CAACC,QAAQ,GAAGD,KAAKC,QAAQ;QAC7B,IAAI,CAACN,SAAS,GAAGK,KAAKL,SAAS;QAC/B,IAAI,CAACO,OAAO,GAAGF,KAAKE,OAAO;QAC3B,IAAI,CAACC,SAAS,GAAGH,KAAKG,SAAS;QAC/B,IAAI,CAACC,QAAQ,GAAGJ,KAAKI,QAAQ;IAC/B;IAEAN,IAAID,IAAY,EAAE;QAChB,MAAMoB,aAAa,IAAI,CAACC,aAAa,CAACrB;QACtCoB,WAAWE,OAAO,CAAC,CAACC;YAClB,IAAI,CAAChB,QAAQ,CAACgB,KAAK,GAAG;QACxB;IACF;IAEAC,SAASxB,IAAY,EAAE;QACrB,MAAMoB,aAAa,IAAI,CAACC,aAAa,CAACrB;QACtC,OAAOoB,WAAWK,KAAK,CAAC,CAACF,OAAS,IAAI,CAAChB,QAAQ,CAACgB,KAAK;IACvD;IAEAF,cAAcrB,IAAY,EAAE;QAC1B,MAAMoB,aAAa,EAAE;QACrB,IAAK,IAAIhC,IAAI,GAAGA,KAAK,IAAI,CAACkB,SAAS,EAAElB,IAAK;YACxC,MAAMmC,OAAOtC,YAAY,AAAC,KAAEe,OAAOZ,KAAO,IAAI,CAACiB,OAAO;YACtDe,WAAWM,IAAI,CAACH;QAClB;QACA,OAAOH;IACT;IAzEAO,YAAYvB,QAAgB,EAAEN,YAAoBJ,kBAAkB,CAAE;QACpE,IAAI,CAACU,QAAQ,GAAGA;QAChB,IAAI,CAACN,SAAS,GAAGA;QACjB,IAAI,CAACO,OAAO,GAAGb,KAAKoC,IAAI,CACtB,CAAExB,CAAAA,WAAWZ,KAAKqC,GAAG,CAAC/B,UAAS,IAAMN,CAAAA,KAAKqC,GAAG,CAAC,KAAKrC,KAAKqC,GAAG,CAAC,EAAC;QAE/D,IAAI,CAACvB,SAAS,GAAGd,KAAKoC,IAAI,CAAC,AAAC,IAAI,CAACvB,OAAO,GAAGD,WAAYZ,KAAKqC,GAAG,CAAC;QAChE,IAAI,CAACtB,QAAQ,GAAG,IAAIuB,MAAM,IAAI,CAACzB,OAAO,EAAE0B,IAAI,CAAC;IAC/C;AAkEF"}