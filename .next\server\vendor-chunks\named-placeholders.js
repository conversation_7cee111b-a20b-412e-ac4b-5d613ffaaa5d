"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/named-placeholders";
exports.ids = ["vendor-chunks/named-placeholders"];
exports.modules = {

/***/ "(rsc)/./node_modules/named-placeholders/index.js":
/*!**************************************************!*\
  !*** ./node_modules/named-placeholders/index.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// based on code from Brian White @mscdex mariasql library - https://github.com/mscdex/node-mariasql/blob/master/lib/Client.js#L272-L332\n// License: https://github.com/mscdex/node-mariasql/blob/master/LICENSE\n\nconst RE_PARAM = /(?:\\?)|(?::(\\d+|(?:[a-zA-Z][a-zA-Z0-9_]*)))/g,\nDQUOTE = 34,\nSQUOTE = 39,\nBSLASH = 92;\n\nfunction parse(query) {\n  let ppos = RE_PARAM.exec(query);\n  let curpos = 0;\n  let start = 0;\n  let end;\n  const parts = [];\n  let inQuote = false;\n  let escape = false;\n  let qchr;\n  const tokens = [];\n  let qcnt = 0;\n  let lastTokenEndPos = 0;\n  let i;\n\n  if (ppos) {\n    do {\n      for (i=curpos,end=ppos.index; i<end; ++i) {\n        let chr = query.charCodeAt(i);\n        if (chr === BSLASH)\n        escape = !escape;\n        else {\n          if (escape) {\n            escape = false;\n            continue;\n          }\n          if (inQuote && chr === qchr) {\n            if (query.charCodeAt(i + 1) === qchr) {\n              // quote escaped via \"\" or ''\n              ++i;\n              continue;\n            }\n            inQuote = false;\n          } else if (chr === DQUOTE || chr === SQUOTE) {\n            inQuote = true;\n            qchr = chr;\n          }\n        }\n      }\n      if (!inQuote) {\n        parts.push(query.substring(start, end));\n        tokens.push(ppos[0].length === 1 ? qcnt++ : ppos[1]);\n        start = end + ppos[0].length;\n        lastTokenEndPos = start;\n      }\n      curpos = end + ppos[0].length;\n    } while (ppos = RE_PARAM.exec(query));\n\n    if (tokens.length) {\n      if (curpos < query.length) {\n        parts.push(query.substring(lastTokenEndPos));\n      }\n      return [parts, tokens];\n    }\n  }\n  return [query];\n};\n\nfunction createCompiler(config) {\n  if (!config)\n  config = {};\n  if (!config.placeholder) {\n    config.placeholder = '?';\n  }\n  let ncache = 100;\n  let cache;\n  if (typeof config.cache === 'number') {\n    ncache = config.cache;\n  }\n  if (typeof config.cache === 'object') {\n    cache = config.cache;\n  }\n  if (config.cache !== false && !cache) {\n    cache = new (__webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/lru-cache/index.js\"))({ max: ncache });\n  }\n\n  function toArrayParams(tree, params) {\n    const arr = [];\n    if (tree.length == 1) {\n      return [tree[0], []];\n    }\n\n    if (typeof params == 'undefined')\n      throw new Error('Named query contains placeholders, but parameters object is undefined');\n\n    const tokens = tree[1];\n    for (let i=0; i < tokens.length; ++i) {\n      arr.push(params[tokens[i]]);\n    }\n    return [tree[0], arr];\n  }\n\n  function noTailingSemicolon(s) {\n    if (s.slice(-1) == ':') {\n      return s.slice(0, -1);\n    }\n    return s;\n  }\n\n  function join(tree) {\n    if (tree.length == 1) {\n      return tree;\n    }\n\n    let unnamed = noTailingSemicolon(tree[0][0]);\n    for (let i=1; i < tree[0].length; ++i) {\n      if (tree[0][i-1].slice(-1) == ':') {\n        unnamed += config.placeholder;\n      }\n      unnamed += config.placeholder;\n      unnamed += noTailingSemicolon(tree[0][i]);\n    }\n\n    const last = tree[0][tree[0].length -1];\n    if (tree[0].length == tree[1].length) {\n      if (last.slice(-1) == ':') {\n        unnamed += config.placeholder;\n      }\n      unnamed += config.placeholder;\n    }\n    return [unnamed, tree[1]];\n  }\n\n  function compile(query, paramsObj) {\n    let tree;\n    if (cache && (tree = cache.get(query))) {\n      return toArrayParams(tree, paramsObj)\n    }\n    tree = join(parse(query));\n    if(cache) {\n      cache.set(query, tree);\n    }\n    return toArrayParams(tree, paramsObj);\n  }\n\n  compile.parse = parse;\n  return compile;\n}\n\n// named :one :two to postgres-style numbered $1 $2 $3\nfunction toNumbered(q, params) {\n  const tree = parse(q);\n  const paramsArr = [];\n  if (tree.length == 1) {\n    return [tree[0], paramsArr];\n  }\n\n  const pIndexes = {};\n  let pLastIndex = 0;\n  let qs = '';\n  let varIndex;\n  const varNames = [];\n  for (let i=0; i < tree[0].length; ++i) {\n    varIndex = pIndexes[tree[1][i]];\n    if (!varIndex) {\n      varIndex = ++pLastIndex;\n      pIndexes[tree[1][i]] = varIndex;\n    }\n    if (tree[1][i]) {\n      varNames[varIndex - 1] = tree[1][i];\n      qs += tree[0][i] + '$' + varIndex;\n    } else {\n      qs += tree[0][i];\n    }\n  }\n  return [qs, varNames.map(n => params[n])];\n}\n\nmodule.exports = createCompiler;\nmodule.exports.toNumbered = toNumbered;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmFtZWQtcGxhY2Vob2xkZXJzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLG9DQUFvQyxPQUFPO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNOztBQUVOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsbUJBQU8sQ0FBQywwREFBVyxLQUFLLGFBQWE7QUFDdEQ7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0Esa0JBQWtCLG1CQUFtQjtBQUNyQztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxrQkFBa0Isb0JBQW9CO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixvQkFBb0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGJfa29kZXhndWFyZC8uL25vZGVfbW9kdWxlcy9uYW1lZC1wbGFjZWhvbGRlcnMvaW5kZXguanM/YzRjNiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbi8vIGJhc2VkIG9uIGNvZGUgZnJvbSBCcmlhbiBXaGl0ZSBAbXNjZGV4IG1hcmlhc3FsIGxpYnJhcnkgLSBodHRwczovL2dpdGh1Yi5jb20vbXNjZGV4L25vZGUtbWFyaWFzcWwvYmxvYi9tYXN0ZXIvbGliL0NsaWVudC5qcyNMMjcyLUwzMzJcbi8vIExpY2Vuc2U6IGh0dHBzOi8vZ2l0aHViLmNvbS9tc2NkZXgvbm9kZS1tYXJpYXNxbC9ibG9iL21hc3Rlci9MSUNFTlNFXG5cbmNvbnN0IFJFX1BBUkFNID0gLyg/OlxcPyl8KD86OihcXGQrfCg/OlthLXpBLVpdW2EtekEtWjAtOV9dKikpKS9nLFxuRFFVT1RFID0gMzQsXG5TUVVPVEUgPSAzOSxcbkJTTEFTSCA9IDkyO1xuXG5mdW5jdGlvbiBwYXJzZShxdWVyeSkge1xuICBsZXQgcHBvcyA9IFJFX1BBUkFNLmV4ZWMocXVlcnkpO1xuICBsZXQgY3VycG9zID0gMDtcbiAgbGV0IHN0YXJ0ID0gMDtcbiAgbGV0IGVuZDtcbiAgY29uc3QgcGFydHMgPSBbXTtcbiAgbGV0IGluUXVvdGUgPSBmYWxzZTtcbiAgbGV0IGVzY2FwZSA9IGZhbHNlO1xuICBsZXQgcWNocjtcbiAgY29uc3QgdG9rZW5zID0gW107XG4gIGxldCBxY250ID0gMDtcbiAgbGV0IGxhc3RUb2tlbkVuZFBvcyA9IDA7XG4gIGxldCBpO1xuXG4gIGlmIChwcG9zKSB7XG4gICAgZG8ge1xuICAgICAgZm9yIChpPWN1cnBvcyxlbmQ9cHBvcy5pbmRleDsgaTxlbmQ7ICsraSkge1xuICAgICAgICBsZXQgY2hyID0gcXVlcnkuY2hhckNvZGVBdChpKTtcbiAgICAgICAgaWYgKGNociA9PT0gQlNMQVNIKVxuICAgICAgICBlc2NhcGUgPSAhZXNjYXBlO1xuICAgICAgICBlbHNlIHtcbiAgICAgICAgICBpZiAoZXNjYXBlKSB7XG4gICAgICAgICAgICBlc2NhcGUgPSBmYWxzZTtcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgIH1cbiAgICAgICAgICBpZiAoaW5RdW90ZSAmJiBjaHIgPT09IHFjaHIpIHtcbiAgICAgICAgICAgIGlmIChxdWVyeS5jaGFyQ29kZUF0KGkgKyAxKSA9PT0gcWNocikge1xuICAgICAgICAgICAgICAvLyBxdW90ZSBlc2NhcGVkIHZpYSBcIlwiIG9yICcnXG4gICAgICAgICAgICAgICsraTtcbiAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpblF1b3RlID0gZmFsc2U7XG4gICAgICAgICAgfSBlbHNlIGlmIChjaHIgPT09IERRVU9URSB8fCBjaHIgPT09IFNRVU9URSkge1xuICAgICAgICAgICAgaW5RdW90ZSA9IHRydWU7XG4gICAgICAgICAgICBxY2hyID0gY2hyO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgaWYgKCFpblF1b3RlKSB7XG4gICAgICAgIHBhcnRzLnB1c2gocXVlcnkuc3Vic3RyaW5nKHN0YXJ0LCBlbmQpKTtcbiAgICAgICAgdG9rZW5zLnB1c2gocHBvc1swXS5sZW5ndGggPT09IDEgPyBxY250KysgOiBwcG9zWzFdKTtcbiAgICAgICAgc3RhcnQgPSBlbmQgKyBwcG9zWzBdLmxlbmd0aDtcbiAgICAgICAgbGFzdFRva2VuRW5kUG9zID0gc3RhcnQ7XG4gICAgICB9XG4gICAgICBjdXJwb3MgPSBlbmQgKyBwcG9zWzBdLmxlbmd0aDtcbiAgICB9IHdoaWxlIChwcG9zID0gUkVfUEFSQU0uZXhlYyhxdWVyeSkpO1xuXG4gICAgaWYgKHRva2Vucy5sZW5ndGgpIHtcbiAgICAgIGlmIChjdXJwb3MgPCBxdWVyeS5sZW5ndGgpIHtcbiAgICAgICAgcGFydHMucHVzaChxdWVyeS5zdWJzdHJpbmcobGFzdFRva2VuRW5kUG9zKSk7XG4gICAgICB9XG4gICAgICByZXR1cm4gW3BhcnRzLCB0b2tlbnNdO1xuICAgIH1cbiAgfVxuICByZXR1cm4gW3F1ZXJ5XTtcbn07XG5cbmZ1bmN0aW9uIGNyZWF0ZUNvbXBpbGVyKGNvbmZpZykge1xuICBpZiAoIWNvbmZpZylcbiAgY29uZmlnID0ge307XG4gIGlmICghY29uZmlnLnBsYWNlaG9sZGVyKSB7XG4gICAgY29uZmlnLnBsYWNlaG9sZGVyID0gJz8nO1xuICB9XG4gIGxldCBuY2FjaGUgPSAxMDA7XG4gIGxldCBjYWNoZTtcbiAgaWYgKHR5cGVvZiBjb25maWcuY2FjaGUgPT09ICdudW1iZXInKSB7XG4gICAgbmNhY2hlID0gY29uZmlnLmNhY2hlO1xuICB9XG4gIGlmICh0eXBlb2YgY29uZmlnLmNhY2hlID09PSAnb2JqZWN0Jykge1xuICAgIGNhY2hlID0gY29uZmlnLmNhY2hlO1xuICB9XG4gIGlmIChjb25maWcuY2FjaGUgIT09IGZhbHNlICYmICFjYWNoZSkge1xuICAgIGNhY2hlID0gbmV3IChyZXF1aXJlKCdscnUtY2FjaGUnKSkoeyBtYXg6IG5jYWNoZSB9KTtcbiAgfVxuXG4gIGZ1bmN0aW9uIHRvQXJyYXlQYXJhbXModHJlZSwgcGFyYW1zKSB7XG4gICAgY29uc3QgYXJyID0gW107XG4gICAgaWYgKHRyZWUubGVuZ3RoID09IDEpIHtcbiAgICAgIHJldHVybiBbdHJlZVswXSwgW11dO1xuICAgIH1cblxuICAgIGlmICh0eXBlb2YgcGFyYW1zID09ICd1bmRlZmluZWQnKVxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdOYW1lZCBxdWVyeSBjb250YWlucyBwbGFjZWhvbGRlcnMsIGJ1dCBwYXJhbWV0ZXJzIG9iamVjdCBpcyB1bmRlZmluZWQnKTtcblxuICAgIGNvbnN0IHRva2VucyA9IHRyZWVbMV07XG4gICAgZm9yIChsZXQgaT0wOyBpIDwgdG9rZW5zLmxlbmd0aDsgKytpKSB7XG4gICAgICBhcnIucHVzaChwYXJhbXNbdG9rZW5zW2ldXSk7XG4gICAgfVxuICAgIHJldHVybiBbdHJlZVswXSwgYXJyXTtcbiAgfVxuXG4gIGZ1bmN0aW9uIG5vVGFpbGluZ1NlbWljb2xvbihzKSB7XG4gICAgaWYgKHMuc2xpY2UoLTEpID09ICc6Jykge1xuICAgICAgcmV0dXJuIHMuc2xpY2UoMCwgLTEpO1xuICAgIH1cbiAgICByZXR1cm4gcztcbiAgfVxuXG4gIGZ1bmN0aW9uIGpvaW4odHJlZSkge1xuICAgIGlmICh0cmVlLmxlbmd0aCA9PSAxKSB7XG4gICAgICByZXR1cm4gdHJlZTtcbiAgICB9XG5cbiAgICBsZXQgdW5uYW1lZCA9IG5vVGFpbGluZ1NlbWljb2xvbih0cmVlWzBdWzBdKTtcbiAgICBmb3IgKGxldCBpPTE7IGkgPCB0cmVlWzBdLmxlbmd0aDsgKytpKSB7XG4gICAgICBpZiAodHJlZVswXVtpLTFdLnNsaWNlKC0xKSA9PSAnOicpIHtcbiAgICAgICAgdW5uYW1lZCArPSBjb25maWcucGxhY2Vob2xkZXI7XG4gICAgICB9XG4gICAgICB1bm5hbWVkICs9IGNvbmZpZy5wbGFjZWhvbGRlcjtcbiAgICAgIHVubmFtZWQgKz0gbm9UYWlsaW5nU2VtaWNvbG9uKHRyZWVbMF1baV0pO1xuICAgIH1cblxuICAgIGNvbnN0IGxhc3QgPSB0cmVlWzBdW3RyZWVbMF0ubGVuZ3RoIC0xXTtcbiAgICBpZiAodHJlZVswXS5sZW5ndGggPT0gdHJlZVsxXS5sZW5ndGgpIHtcbiAgICAgIGlmIChsYXN0LnNsaWNlKC0xKSA9PSAnOicpIHtcbiAgICAgICAgdW5uYW1lZCArPSBjb25maWcucGxhY2Vob2xkZXI7XG4gICAgICB9XG4gICAgICB1bm5hbWVkICs9IGNvbmZpZy5wbGFjZWhvbGRlcjtcbiAgICB9XG4gICAgcmV0dXJuIFt1bm5hbWVkLCB0cmVlWzFdXTtcbiAgfVxuXG4gIGZ1bmN0aW9uIGNvbXBpbGUocXVlcnksIHBhcmFtc09iaikge1xuICAgIGxldCB0cmVlO1xuICAgIGlmIChjYWNoZSAmJiAodHJlZSA9IGNhY2hlLmdldChxdWVyeSkpKSB7XG4gICAgICByZXR1cm4gdG9BcnJheVBhcmFtcyh0cmVlLCBwYXJhbXNPYmopXG4gICAgfVxuICAgIHRyZWUgPSBqb2luKHBhcnNlKHF1ZXJ5KSk7XG4gICAgaWYoY2FjaGUpIHtcbiAgICAgIGNhY2hlLnNldChxdWVyeSwgdHJlZSk7XG4gICAgfVxuICAgIHJldHVybiB0b0FycmF5UGFyYW1zKHRyZWUsIHBhcmFtc09iaik7XG4gIH1cblxuICBjb21waWxlLnBhcnNlID0gcGFyc2U7XG4gIHJldHVybiBjb21waWxlO1xufVxuXG4vLyBuYW1lZCA6b25lIDp0d28gdG8gcG9zdGdyZXMtc3R5bGUgbnVtYmVyZWQgJDEgJDIgJDNcbmZ1bmN0aW9uIHRvTnVtYmVyZWQocSwgcGFyYW1zKSB7XG4gIGNvbnN0IHRyZWUgPSBwYXJzZShxKTtcbiAgY29uc3QgcGFyYW1zQXJyID0gW107XG4gIGlmICh0cmVlLmxlbmd0aCA9PSAxKSB7XG4gICAgcmV0dXJuIFt0cmVlWzBdLCBwYXJhbXNBcnJdO1xuICB9XG5cbiAgY29uc3QgcEluZGV4ZXMgPSB7fTtcbiAgbGV0IHBMYXN0SW5kZXggPSAwO1xuICBsZXQgcXMgPSAnJztcbiAgbGV0IHZhckluZGV4O1xuICBjb25zdCB2YXJOYW1lcyA9IFtdO1xuICBmb3IgKGxldCBpPTA7IGkgPCB0cmVlWzBdLmxlbmd0aDsgKytpKSB7XG4gICAgdmFySW5kZXggPSBwSW5kZXhlc1t0cmVlWzFdW2ldXTtcbiAgICBpZiAoIXZhckluZGV4KSB7XG4gICAgICB2YXJJbmRleCA9ICsrcExhc3RJbmRleDtcbiAgICAgIHBJbmRleGVzW3RyZWVbMV1baV1dID0gdmFySW5kZXg7XG4gICAgfVxuICAgIGlmICh0cmVlWzFdW2ldKSB7XG4gICAgICB2YXJOYW1lc1t2YXJJbmRleCAtIDFdID0gdHJlZVsxXVtpXTtcbiAgICAgIHFzICs9IHRyZWVbMF1baV0gKyAnJCcgKyB2YXJJbmRleDtcbiAgICB9IGVsc2Uge1xuICAgICAgcXMgKz0gdHJlZVswXVtpXTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIFtxcywgdmFyTmFtZXMubWFwKG4gPT4gcGFyYW1zW25dKV07XG59XG5cbm1vZHVsZS5leHBvcnRzID0gY3JlYXRlQ29tcGlsZXI7XG5tb2R1bGUuZXhwb3J0cy50b051bWJlcmVkID0gdG9OdW1iZXJlZDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/named-placeholders/index.js\n");

/***/ })

};
;