"use strict";
// automatically generated by the FlatBuffers compiler, do not modify
Object.defineProperty(exports, "__esModule", { value: true });
exports.Schema = exports.MetadataVersion = exports.KeyValue = exports.Footer = exports.Block = void 0;
var block_js_1 = require("./block.js");
Object.defineProperty(exports, "Block", { enumerable: true, get: function () { return block_js_1.Block; } });
var footer_js_1 = require("./footer.js");
Object.defineProperty(exports, "Footer", { enumerable: true, get: function () { return footer_js_1.Footer; } });
var key_value_js_1 = require("./key-value.js");
Object.defineProperty(exports, "KeyValue", { enumerable: true, get: function () { return key_value_js_1.KeyValue; } });
var metadata_version_js_1 = require("./metadata-version.js");
Object.defineProperty(exports, "MetadataVersion", { enumerable: true, get: function () { return metadata_version_js_1.MetadataVersion; } });
var schema_js_1 = require("./schema.js");
Object.defineProperty(exports, "Schema", { enumerable: true, get: function () { return schema_js_1.Schema; } });

//# sourceMappingURL=File_generated.js.map
