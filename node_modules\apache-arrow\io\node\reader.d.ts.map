{"version": 3, "sources": ["io/node/reader.ts"], "names": [], "mappings": ";AAiBA,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AACpD,OAAO,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAC;AACpD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAExD,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAExC,cAAc;AACd,wBAAgB,kCAAkC,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,OAAO,CAAC,EAAE,aAAa,GAAG;IAAE,WAAW,EAAE,OAAO,CAAA;CAAE,8BAE7H;AAED,cAAc;AACd,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG,IAAI,GAAG,SAAS,KAAK,IAAI,CAAC;AAErD,cAAc;AACd,cAAM,uBAAuB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,MAAM;IACjE,OAAO,CAAC,QAAQ,CAAS;IACzB,OAAO,CAAC,YAAY,CAAQ;IAC5B,OAAO,CAAC,OAAO,CAA2B;IAC1C,OAAO,CAAC,WAAW,CAAwB;gBAC/B,OAAO,CAAC,EAAE,aAAa,GAAG;QAAE,WAAW,EAAE,OAAO,CAAA;KAAE;IAO9D,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE;IAKd,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE;IAMhC,KAAK,CAAC,IAAI,EAAE,MAAM;IAWlB,QAAQ,CAAC,GAAG,EAAE,KAAK,GAAG,IAAI,EAAE,EAAE,EAAE,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,KAAK,IAAI;IAKvD,KAAK,CAAC,MAAM,EAAE,cAAc;IAG5B,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC,CAAC;CAWzD", "file": "reader.d.ts", "sourceRoot": "../../src"}