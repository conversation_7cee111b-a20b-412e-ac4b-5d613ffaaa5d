{"version": 3, "sources": ["../../../src/server/lib/incremental-cache-server.ts"], "names": ["initialize", "initializeResult", "constructorArgs", "incrementalCache", "IncrementalCache", "ipcPort", "ipcValidationKey", "createIpcServer", "revalidateTag", "args", "get", "set", "lock", "unlock"], "mappings": ";;;;+BAUsBA;;;eAAAA;;;2BAVU;kCACC;AAEjC,IAAIC;AAOG,eAAeD,WACpB,GAAGE,eAA+D;IAElE,MAAMC,mBAAmB,IAAIC,kCAAgB,IAAIF;IAEjD,MAAM,EAAEG,OAAO,EAAEC,gBAAgB,EAAE,GAAG,MAAMC,IAAAA,0BAAe,EAAC;QAC1D,MAAMC,eACJ,GAAGC,IAAmD;YAEtD,OAAON,iBAAiBK,aAAa,IAAIC;QAC3C;QAEA,MAAMC,KAAI,GAAGD,IAAyC;YACpD,OAAON,iBAAiBO,GAAG,IAAID;QACjC;QAEA,MAAME,KAAI,GAAGF,IAAyC;YACpD,OAAON,iBAAiBQ,GAAG,IAAIF;QACjC;QAEA,MAAMG,MAAK,GAAGH,IAA0C;YACtD,OAAON,iBAAiBS,IAAI,IAAIH;QAClC;QAEA,MAAMI,QAAO,GAAGJ,IAA4C;YAC1D,OAAON,iBAAiBU,MAAM,IAAIJ;QACpC;IACF;IAEA,OAAO;QACLJ;QACAC;IACF;AACF"}