{"version": 3, "sources": ["../../../../src/server/typescript/rules/client-boundary.ts"], "names": ["clientBoundary", "getSemanticDiagnosticsForExportVariableStatement", "source", "node", "ts", "getTs", "diagnostics", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "initializer", "isArrowFunction", "push", "getSemanticDiagnosticsForFunctionExport", "typeC<PERSON>cker", "getType<PERSON><PERSON>cker", "isErrorFile", "test", "fileName", "isGlobalErrorFile", "props", "parameters", "name", "isObjectBindingPattern", "prop", "elements", "type", "getTypeAtLocation", "typeDeclarationNode", "symbol", "getDeclarations", "propName", "propertyName", "getText", "isFunctionOrConstructorTypeNode", "isClassDeclaration", "file", "category", "DiagnosticCategory", "Warning", "code", "NEXT_TS_ERRORS", "INVALID_CLIENT_ENTRY_PROP", "messageText", "start", "getStart", "length", "getWidth"], "mappings": "AAAA,8FAA8F;;;;;+BAgF9F;;;eAAA;;;0BA9E+B;uBACO;AAGtC,MAAMA,iBAAiB;IACrBC,kDACEC,MAA2B,EAC3BC,IAAgC;QAEhC,MAAMC,KAAKC,IAAAA,YAAK;QAEhB,MAAMC,cAAqC,EAAE;QAE7C,IAAIF,GAAGG,yBAAyB,CAACJ,KAAKK,eAAe,GAAG;YACtD,KAAK,MAAMC,eAAeN,KAAKK,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAMC,cAAcF,YAAYE,WAAW;gBAC3C,IAAIA,eAAeP,GAAGQ,eAAe,CAACD,cAAc;oBAClDL,YAAYO,IAAI,IACXb,eAAec,uCAAuC,CACvDZ,QACAS;gBAGN;YACF;QACF;QAEA,OAAOL;IACT;IAEAQ,yCACEZ,MAA2B,EAC3BC,IAA2D;YAW7CA,mBAAAA;QATd,MAAMC,KAAKC,IAAAA,YAAK;QAChB,MAAMU,cAAcC,IAAAA,qBAAc;QAClC,IAAI,CAACD,aAAa,OAAO,EAAE;QAE3B,MAAMT,cAAqC,EAAE;QAE7C,MAAMW,cAAc,oBAAoBC,IAAI,CAAChB,OAAOiB,QAAQ;QAC5D,MAAMC,oBAAoB,2BAA2BF,IAAI,CAAChB,OAAOiB,QAAQ;QAEzE,MAAME,SAAQlB,mBAAAA,KAAKmB,UAAU,sBAAfnB,oBAAAA,gBAAiB,CAAC,EAAE,qBAApBA,kBAAsBoB,IAAI;QACxC,IAAIF,SAASjB,GAAGoB,sBAAsB,CAACH,QAAQ;YAC7C,KAAK,MAAMI,QAAQ,AAACJ,MAAwCK,QAAQ,CAAE;oBAExCC,8BAAAA;gBAD5B,MAAMA,OAAOZ,YAAYa,iBAAiB,CAACH;gBAC3C,MAAMI,uBAAsBF,eAAAA,KAAKG,MAAM,sBAAXH,+BAAAA,aAAaI,eAAe,uBAA5BJ,4BAAgC,CAAC,EAAE;gBAC/D,MAAMK,WAAW,AAACP,CAAAA,KAAKQ,YAAY,IAAIR,KAAKF,IAAI,AAAD,EAAGW,OAAO;gBAEzD,IAAIL,qBAAqB;oBACvB,IACE,2CAA2C;oBAC3CzB,GAAG+B,+BAA+B,CAACN,wBACnCzB,GAAGgC,kBAAkB,CAACP,sBACtB;wBACA,6EAA6E;wBAC7E,oBAAoB;wBACpB,iDAAiD;wBACjD,IAAI,CAAEZ,CAAAA,eAAeG,iBAAgB,KAAMY,aAAa,SAAS;4BAC/D1B,YAAYO,IAAI,CAAC;gCACfwB,MAAMnC;gCACNoC,UAAUlC,GAAGmC,kBAAkB,CAACC,OAAO;gCACvCC,MAAMC,wBAAc,CAACC,yBAAyB;gCAC9CC,aAAa,CAAC,2EAA2E,EAAEZ,SAAS,aAAa,CAAC;gCAClHa,OAAOpB,KAAKqB,QAAQ;gCACpBC,QAAQtB,KAAKuB,QAAQ;4BACvB;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO1C;IACT;AACF;MAEA,WAAeN"}