{"version": 3, "sources": ["ipc/message.ts"], "names": [], "mappings": ";AAiBA,OAAO,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AAE3C,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAGhD,OAAO,EAAgB,oBAAoB,EAAE,MAAM,mBAAmB,CAAC;AACvE,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AAC9E,OAAO,EAAE,SAAS,EAAE,aAAa,EAAiB,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAO1F,cAAc;AACd,qBAAa,aAAc,YAAW,gBAAgB,CAAC,OAAO,CAAC;IAC3D,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC;gBACjB,MAAM,EAAE,UAAU,GAAG,oBAAoB,GAAG,QAAQ,CAAC,oBAAoB,CAAC;IAG/E,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,gBAAgB,CAAC,OAAO,CAAC;IAC9C,IAAI,IAAI,cAAc,CAAC,OAAO,CAAC;IAW/B,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG;IACjB,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG;IAClB,WAAW,CAAC,CAAC,SAAS,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI;IAQpD,eAAe,CAAC,UAAU,EAAE,MAAM,GAAG,UAAU;IAW/C,UAAU,CAAC,WAAW,UAAQ;IASrC,SAAS,CAAC,kBAAkB,IAAI,cAAc,CAAC,MAAM,CAAC;IAMtD,SAAS,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,GAAG,cAAc,CAAC,OAAO,CAAC;CAQ1E;AAED,cAAc;AACd,qBAAa,kBAAmB,YAAW,qBAAqB,CAAC,OAAO,CAAC;IACrE,SAAS,CAAC,MAAM,EAAE,eAAe,CAAC;gBACtB,MAAM,EAAE,cAAc,CAAC,UAAU,CAAC;gBAClC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,MAAM;IAO5C,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,qBAAqB,CAAC,OAAO,CAAC;IAClD,IAAI,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAWxC,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG;IACjB,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG;IAClB,WAAW,CAAC,CAAC,SAAS,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI;IAQpD,eAAe,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC;IAWxD,UAAU,CAAC,WAAW,UAAQ;cAS3B,kBAAkB,IAAI,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;cAMrD,YAAY,CAAC,cAAc,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;CAQzF;AAED,cAAc;AACd,qBAAa,iBAAkB,SAAQ,aAAa;IAChD,OAAO,CAAC,OAAO,CAAS;IACxB,OAAO,CAAC,KAAK,CAAY;IACzB,OAAO,CAAC,KAAK,CAAa;IAC1B,OAAO,CAAC,WAAW,CAAK;IACxB,OAAO,CAAC,gBAAgB,CAAK;gBACjB,MAAM,EAAE,SAAS,GAAG,aAAa;IAItC,IAAI;IAsBJ,eAAe,CAAC,WAAW,CAAC,EAAE,MAAM;IAapC,WAAW,CAAC,CAAC,SAAS,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI;IAQpD,UAAU;CASpB;AAED,cAAc;AACd,eAAO,MAAM,OAAO,IAAI,CAAC;AACzB,cAAc;AACd,eAAO,MAAM,SAAS,WAAW,CAAC;AAClC,cAAc;AACd,eAAO,MAAM,KAAK,YAAmC,CAAC;AAMtD,cAAc;AACd,wBAAgB,wBAAwB,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,SAAI,WAOrE;AAED,cAAc;AACd,eAAO,MAAM,WAAW,QAAe,CAAC;AACxC,cAAc;AACd,eAAO,MAAM,eAAe,QAAwB,CAAC;AACrD,cAAc;AACd,eAAO,MAAM,iBAAiB,QAA4B,CAAC", "file": "message.d.ts", "sourceRoot": "../src"}