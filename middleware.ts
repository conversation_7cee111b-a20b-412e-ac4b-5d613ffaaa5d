import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Define protected routes
const protectedRoutes = [
  '/dashboard',
  '/osint',
  '/scanner',
  '/file-analyzer',
  '/cve',
  '/dorking',
  '/playground',
  '/tools',
  '/bot',
  '/leaderboard',
  '/profile',
  '/plan',
  '/admin'
]

// Define admin-only routes
const adminRoutes = [
  '/admin'
]

// Define public routes that should redirect to dashboard if authenticated
const publicRoutes = [
  '/login',
  '/register'
]

// Simple authentication check for Edge Runtime
async function checkAuthentication(request: NextRequest): Promise<boolean> {
  try {
    // Get token from cookies
    const cookies = request.headers.get('cookie')
    if (!cookies) return false

    const tokenMatch = cookies.match(/accessToken=([^;]+)/)
    const token = tokenMatch ? tokenMatch[1] : null

    if (!token) return false

    // Simple token validation - just check if it exists and looks like a JWT
    const parts = token.split('.')
    if (parts.length !== 3) return false

    try {
      // Try to decode the payload (without verification for Edge Runtime)
      const payload = JSON.parse(atob(parts[1]))

      // Check if token is not expired
      if (payload.exp && payload.exp < Date.now() / 1000) {
        return false
      }

      return true
    } catch {
      return false
    }
  } catch {
    return false
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Skip middleware for API routes, static files, and other assets
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.includes('.')
  ) {
    return NextResponse.next()
  }

  // Get authentication status - simplified for Edge Runtime
  const isAuthenticated = await checkAuthentication(request)
  const user = isAuthenticated ? { username: 'user', role: 'user' } : null

  console.log(`🔐 Middleware: ${pathname} - Auth: ${isAuthenticated} - User: ${user?.username || 'none'}`)

  // Handle protected routes
  if (protectedRoutes.some(route => pathname.startsWith(route))) {
    if (!isAuthenticated) {
      console.log(`❌ Redirecting unauthenticated user from ${pathname} to /login`)
      const loginUrl = new URL('/login', request.url)
      loginUrl.searchParams.set('redirect', pathname)
      return NextResponse.redirect(loginUrl)
    }

    // Check admin routes
    if (adminRoutes.some(route => pathname.startsWith(route))) {
      if (user?.role !== 'admin' && user?.role !== 'super_admin') {
        console.log(`❌ Unauthorized access attempt to ${pathname} by ${user?.username}`)
        return NextResponse.redirect(new URL('/dashboard', request.url))
      }
    }

    // User is authenticated and authorized, continue
    console.log(`✅ Authorized access to ${pathname} by ${user?.username}`)
    return NextResponse.next()
  }

  // Handle public routes (login, register)
  if (publicRoutes.some(route => pathname === route)) {
    if (isAuthenticated) {
      console.log(`🔄 Redirecting authenticated user from ${pathname} to /dashboard`)
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }
    
    // User is not authenticated, allow access to public routes
    return NextResponse.next()
  }

  // Handle root route
  if (pathname === '/') {
    if (isAuthenticated) {
      console.log(`🔄 Redirecting authenticated user from / to /dashboard`)
      return NextResponse.redirect(new URL('/dashboard', request.url))
    } else {
      console.log(`🔄 Redirecting unauthenticated user from / to /login`)
      return NextResponse.redirect(new URL('/login', request.url))
    }
  }

  // For all other routes, continue normally
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\..*$).*)',
  ],
}
