"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./lib/auth-simple.ts":
/*!****************************!*\
  !*** ./lib/auth-simple.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleAuthService: () => (/* binding */ SimpleAuthService)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(middleware)/./node_modules/bcryptjs/dist/bcrypt.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(middleware)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key\";\nconst JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || \"your-super-secret-refresh-key\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"1h\";\nconst JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || \"7d\";\nconst BCRYPT_ROUNDS = 12;\n// Mock users for testing\nconst mockUsers = [\n    {\n        id: 1,\n        username: \"admin\",\n        email: \"<EMAIL>\",\n        full_name: \"Administrator\",\n        role: \"admin\",\n        plan: \"Elite\",\n        level: 100,\n        score: 10000,\n        streak_days: 365,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"admin123\"\n    },\n    {\n        id: 2,\n        username: \"testuser\",\n        email: \"<EMAIL>\",\n        full_name: \"Test User\",\n        role: \"user\",\n        plan: \"Free\",\n        level: 1,\n        score: 0,\n        streak_days: 0,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"test123\"\n    }\n];\nclass SimpleAuthService {\n    // Hash password\n    static async hashPassword(password) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, BCRYPT_ROUNDS);\n    }\n    // Verify password\n    static async verifyPassword(password, hash) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hash);\n    }\n    // Generate JWT tokens\n    static generateTokens(user) {\n        const payload = {\n            id: user.id,\n            username: user.username,\n            email: user.email,\n            role: user.role,\n            plan: user.plan\n        };\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n        const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_REFRESH_SECRET, {\n            expiresIn: JWT_REFRESH_EXPIRES_IN\n        });\n        return {\n            accessToken,\n            refreshToken,\n            expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds\n        };\n    }\n    // Verify JWT token\n    static verifyToken(token, isRefreshToken = false) {\n        try {\n            const secret = isRefreshToken ? JWT_REFRESH_SECRET : JWT_SECRET;\n            return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        } catch (error) {\n            return null;\n        }\n    }\n    // Register new user\n    static async register(data) {\n        try {\n            // Check if user already exists\n            const existingUser = mockUsers.find((u)=>u.email === data.email || u.username === data.username);\n            if (existingUser) {\n                return {\n                    success: false,\n                    message: \"User already exists with this email or username\"\n                };\n            }\n            // Create new user\n            const newUser = {\n                id: mockUsers.length + 1,\n                username: data.username,\n                email: data.email,\n                full_name: data.fullName || \"\",\n                role: \"user\",\n                plan: \"Free\",\n                level: 1,\n                score: 0,\n                streak_days: 0,\n                email_verified: false,\n                status: \"active\",\n                last_active: new Date(),\n                created_at: new Date(),\n                password: data.password\n            };\n            // Add to mock users\n            mockUsers.push(newUser);\n            // Remove password from user object\n            const { password, ...userWithoutPassword } = newUser;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                message: \"Registration failed\"\n            };\n        }\n    }\n    // Login user\n    static async login(email, password) {\n        try {\n            // Find user by email\n            const user = mockUsers.find((u)=>u.email === email && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Verify password (simple comparison for testing)\n            if (password !== user.password) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Remove password from user object\n            const { password: userPassword, ...userWithoutPassword } = user;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"Login failed\"\n            };\n        }\n    }\n    // Authenticate request\n    static async authenticateRequest(request) {\n        try {\n            // Get token from Authorization header or cookies\n            let token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n            if (!token) {\n                // Try to get from cookies\n                const cookies = request.headers.get(\"cookie\");\n                console.log(\"\\uD83C\\uDF6A Cookies in middleware:\", cookies);\n                if (cookies) {\n                    const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n                    token = tokenMatch ? tokenMatch[1] : undefined;\n                    console.log(\"\\uD83D\\uDD11 Token from cookies:\", token ? \"Found\" : \"Not found\");\n                }\n            }\n            if (!token) {\n                console.log(\"❌ No token found in headers or cookies\");\n                return {\n                    success: false,\n                    message: \"No token provided\"\n                };\n            }\n            // Verify token\n            const decoded = this.verifyToken(token);\n            if (!decoded) {\n                return {\n                    success: false,\n                    message: \"Invalid token\"\n                };\n            }\n            // Get user from mock data\n            const user = mockUsers.find((u)=>u.id === decoded.id);\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"User not found\"\n                };\n            }\n            const { password, ...userWithoutPassword } = user;\n            return {\n                success: true,\n                user: userWithoutPassword\n            };\n        } catch (error) {\n            console.error(\"Authentication error:\", error);\n            return {\n                success: false,\n                message: \"Authentication failed\"\n            };\n        }\n    }\n    // Logout user (for mock, just return success)\n    static async logout(userId, _sessionToken) {\n        // In a real implementation, this would remove the session from database\n        console.log(`User ${userId} logged out`);\n    }\n    // Refresh token\n    static async refreshToken(refreshToken) {\n        try {\n            // Verify refresh token\n            const decoded = this.verifyToken(refreshToken, true);\n            if (!decoded) {\n                return {\n                    success: false,\n                    message: \"Invalid refresh token\"\n                };\n            }\n            // Get user data\n            const user = mockUsers.find((u)=>u.id === decoded.id && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"User not found or inactive\"\n                };\n            }\n            const { password, ...userWithoutPassword } = user;\n            // Generate new tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update user last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            return {\n                success: false,\n                message: \"Token refresh failed\"\n            };\n        }\n    }\n    // Get user by ID\n    static async getUserById(userId) {\n        try {\n            const user = mockUsers.find((u)=>u.id === userId);\n            if (!user) return null;\n            const { password, ...userWithoutPassword } = user;\n            return userWithoutPassword;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            return null;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./lib/auth-simple.ts\n");

/***/ })

});