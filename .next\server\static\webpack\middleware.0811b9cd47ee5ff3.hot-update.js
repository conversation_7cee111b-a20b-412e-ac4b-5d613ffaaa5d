"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n// Define protected routes\nconst protectedRoutes = [\n    \"/dashboard\",\n    \"/osint\",\n    \"/scanner\",\n    \"/file-analyzer\",\n    \"/cve\",\n    \"/dorking\",\n    \"/playground\",\n    \"/tools\",\n    \"/bot\",\n    \"/leaderboard\",\n    \"/profile\",\n    \"/plan\",\n    \"/admin\"\n];\n// Define admin-only routes\nconst adminRoutes = [\n    \"/admin\"\n];\n// Define public routes that should redirect to dashboard if authenticated\nconst publicRoutes = [\n    \"/login\",\n    \"/register\"\n];\n// Simple authentication check for Edge Runtime\nasync function checkAuthentication(request) {\n    try {\n        // Get token from cookies\n        const cookies = request.headers.get(\"cookie\");\n        if (!cookies) return false;\n        const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n        const token = tokenMatch ? tokenMatch[1] : null;\n        if (!token) return false;\n        // Simple token validation - just check if it exists and looks like a JWT\n        const parts = token.split(\".\");\n        if (parts.length !== 3) return false;\n        try {\n            // Try to decode the payload (without verification for Edge Runtime)\n            const payload = JSON.parse(atob(parts[1]));\n            // Check if token is not expired\n            if (payload.exp && payload.exp < Date.now() / 1000) {\n                return false;\n            }\n            return true;\n        } catch  {\n            return false;\n        }\n    } catch  {\n        return false;\n    }\n}\nasync function middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Skip middleware for API routes, static files, and other assets\n    if (pathname.startsWith(\"/api/\") || pathname.startsWith(\"/_next/\") || pathname.startsWith(\"/favicon.ico\") || pathname.includes(\".\")) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Get authentication status - simplified for Edge Runtime\n    const isAuthenticated = await checkAuthentication(request);\n    const user = isAuthenticated ? {\n        username: \"user\",\n        role: \"user\"\n    } : null;\n    console.log(`🔐 Middleware: ${pathname} - Auth: ${isAuthenticated} - User: ${user?.username || \"none\"}`);\n    // Handle protected routes\n    if (protectedRoutes.some((route)=>pathname.startsWith(route))) {\n        if (!isAuthenticated) {\n            console.log(`❌ Redirecting unauthenticated user from ${pathname} to /login`);\n            const loginUrl = new URL(\"/login\", request.url);\n            loginUrl.searchParams.set(\"redirect\", pathname);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(loginUrl);\n        }\n        // Check admin routes\n        if (adminRoutes.some((route)=>pathname.startsWith(route))) {\n            if (user?.role !== \"admin\" && user?.role !== \"super_admin\") {\n                console.log(`❌ Unauthorized access attempt to ${pathname} by ${user?.username}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n            }\n        }\n        // User is authenticated and authorized, continue\n        console.log(`✅ Authorized access to ${pathname} by ${user?.username}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Handle public routes (login, register)\n    if (publicRoutes.some((route)=>pathname === route)) {\n        if (isAuthenticated) {\n            console.log(`🔄 Redirecting authenticated user from ${pathname} to /dashboard`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n        }\n        // User is not authenticated, allow access to public routes\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Handle root route\n    if (pathname === \"/\") {\n        if (isAuthenticated) {\n            console.log(`🔄 Redirecting authenticated user from / to /dashboard`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n        } else {\n            console.log(`🔄 Redirecting unauthenticated user from / to /login`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/login\", request.url));\n        }\n    }\n    // For all other routes, continue normally\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder files\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|.*\\\\..*$).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});