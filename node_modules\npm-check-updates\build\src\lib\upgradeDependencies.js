"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const flow_1 = __importDefault(require("lodash/flow"));
const mapValues_1 = __importDefault(require("lodash/mapValues"));
const pickBy_1 = __importDefault(require("lodash/pickBy"));
const semver_utils_1 = require("semver-utils");
const filterObject_1 = __importDefault(require("./filterObject"));
const getPreferredWildcard_1 = __importDefault(require("./getPreferredWildcard"));
const isUpgradeable_1 = __importDefault(require("./isUpgradeable"));
const versionUtil = __importStar(require("./version-util"));
/**
 * Upgrade a dependencies collection based on latest available versions. Supports npm aliases.
 *
 * @param currentDependencies current dependencies collection object
 * @param latestVersions latest available versions collection object
 * @param [options={}]
 * @returns upgraded dependency collection object
 */
function upgradeDependencies(currentDependencies, latestVersions, options = {}) {
    const targetOption = options.target || 'latest';
    // filter out dependencies with empty values
    currentDependencies = (0, filterObject_1.default)(currentDependencies, (key, value) => !!value);
    // get the preferred wildcard and bind it to upgradeDependencyDeclaration
    const wildcard = (0, getPreferredWildcard_1.default)(currentDependencies) || versionUtil.DEFAULT_WILDCARD;
    /** Upgrades a single dependency. */
    const upgradeDep = (current, latest) => versionUtil.upgradeDependencyDeclaration(current, latest, {
        wildcard,
        removeRange: options.removeRange,
    });
    return (0, flow_1.default)([
        // only include packages for which a latest version was fetched
        (deps) => (0, pickBy_1.default)(deps, (current, packageName) => packageName in latestVersions),
        // unpack npm alias and git urls
        (deps) => (0, mapValues_1.default)(deps, (current, packageName) => {
            const latest = latestVersions[packageName];
            let currentParsed = null;
            let latestParsed = null;
            // parse npm alias
            if (versionUtil.isNpmAlias(current)) {
                currentParsed = versionUtil.parseNpmAlias(current)[1];
            }
            if (versionUtil.isNpmAlias(latest)) {
                latestParsed = versionUtil.parseNpmAlias(latest)[1];
            }
            // "branch" is also used for tags (refers to everything after the hash character)
            if (versionUtil.isGithubUrl(current)) {
                const currentTag = versionUtil.getGithubUrlTag(current);
                const [currentSemver] = (0, semver_utils_1.parseRange)(currentTag);
                currentParsed = versionUtil.stringify(currentSemver);
            }
            if (versionUtil.isGithubUrl(latest)) {
                const latestTag = versionUtil.getGithubUrlTag(latest);
                const [latestSemver] = (0, semver_utils_1.parseRange)(latestTag);
                latestParsed = versionUtil.stringify(latestSemver);
            }
            return { current, currentParsed, latest, latestParsed };
        }),
        // pick the packages that are upgradeable
        (deps) => (0, pickBy_1.default)(deps, ({ current, currentParsed, latest, latestParsed }, name) => {
            // allow downgrades from prereleases when explicit tag is given
            const downgrade = versionUtil.isPre(current) &&
                (typeof targetOption === 'string' ? targetOption : targetOption(name, (0, semver_utils_1.parseRange)(current))).startsWith('@');
            return (0, isUpgradeable_1.default)(currentParsed || current, latestParsed || latest, { downgrade });
        }),
        // pack embedded versions: npm aliases and git urls
        (deps) => (0, mapValues_1.default)(deps, ({ current, currentParsed, latest, latestParsed }) => {
            const upgraded = upgradeDep(currentParsed || current, latestParsed || latest);
            return versionUtil.isNpmAlias(current)
                ? versionUtil.upgradeNpmAlias(current, upgraded)
                : versionUtil.isGithubUrl(current)
                    ? versionUtil.upgradeGithubUrl(current, upgraded)
                    : upgraded;
        }),
    ])(currentDependencies);
}
exports.default = upgradeDependencies;
//# sourceMappingURL=upgradeDependencies.js.map