import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import ToastProvider from '@/components/Toast'
import { ThemeProvider } from '@/contexts/ThemeContext'
import { AuthProvider } from '@/hooks/useAuth'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'KodeXGuard - Cybersecurity & Bug Hunting Platform',
  description: 'Platform mandiri untuk OSINT, Vulnerability Scanner, File Analyzer, CVE Intelligence, dan komunitas Bug Hunter',
  keywords: 'cybersecurity, bug hunting, OSINT, vulnerability scanner, CVE, penetration testing',
  authors: [{ name: 'KodeXGuard Team' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="id">
      <body className={`${inter.className} scrollbar-cyber`}>
        <ThemeProvider>
          <AuthProvider>
            <ToastProvider>
              {children}
            </ToastProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
