"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    instantiateEmscriptenWasm: null,
    pathify: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    instantiateEmscriptenWasm: function() {
        return instantiateEmscriptenWasm;
    },
    pathify: function() {
        return pathify;
    }
});
const _url = require("url");
function pathify(path) {
    if (path.startsWith("file://")) {
        path = (0, _url.fileURLToPath)(path);
    }
    return path;
}
function instantiateEmscriptenWasm(factory, path, workerJS = "") {
    return factory({
        locateFile (requestPath) {
            // The glue code generated by emscripten uses the original
            // file names of the worker file and the wasm binary.
            // These will have changed in the bundling process and
            // we need to inject them here.
            if (requestPath.endsWith(".wasm")) return pathify(path);
            if (requestPath.endsWith(".worker.js")) return pathify(workerJS);
            return requestPath;
        }
    });
}

//# sourceMappingURL=emscripten-utils.js.map