#!/usr/bin/env ts-node

import { testDatabaseConnection, closeDatabaseConnections } from '../lib/database'
import { runMigrations } from '../lib/database/run-migrations'
import { spawn } from 'child_process'
import { join } from 'path'

async function setupNewFeatures() {
  console.log('🚀 Setting up KodeXGuard New Features...\n')

  try {
    // Step 1: Test database connection
    console.log('🔍 Step 1: Testing database connection...')
    const connected = await testDatabaseConnection()
    if (!connected) {
      console.error('❌ Database connection failed. Please check your .env configuration.')
      process.exit(1)
    }
    console.log('✅ Database connection successful!\n')

    // Step 2: Run migrations
    console.log('🔄 Step 2: Running database migrations...')
    const migrationsSuccess = await runMigrations()
    if (!migrationsSuccess) {
      console.error('❌ Migrations failed. Please check the error messages above.')
      process.exit(1)
    }
    console.log('✅ Migrations completed successfully!\n')

    // Step 3: Seed new features data
    console.log('🌱 Step 3: Seeding new features data...')
    try {
      await runTsScript('seed-new-features.ts')
      console.log('✅ New features data seeded successfully!\n')
    } catch (error) {
      console.error('❌ New features data seeding failed:', error)
      process.exit(1)
    }

    // Step 4: Test database setup
    console.log('🔍 Step 4: Testing database setup...')
    try {
      await runTsScript('test-db.ts')
      console.log('✅ Database setup test completed successfully!\n')
    } catch (error) {
      console.error('❌ Database setup test failed:', error)
      process.exit(1)
    }

    console.log(`
🎉 KodeXGuard New Features Setup Completed Successfully! 🎉

Your database is now ready with all the new features:
- ✅ Enhanced OSINT with data Indonesia
- ✅ WhatsApp & Telegram bot integration
- ✅ Payment system dengan multiple gateway
- ✅ Advanced scoring & achievement system
- ✅ Complete admin panel dengan monitoring

Next steps:
1. Start the application: npm run dev
2. Login as admin to access the admin panel
3. Configure payment gateways in .env file
4. Setup bot tokens for WhatsApp and Telegram

For more information, see DATABASE_SETUP.md
    `)
    
  } catch (error) {
    console.error('❌ New features setup failed:', error)
    process.exit(1)
  } finally {
    await closeDatabaseConnections()
  }
}

function runTsScript(scriptName: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const scriptPath = join(process.cwd(), 'scripts', scriptName)
    const child = spawn('ts-node', [scriptPath], { stdio: 'inherit' })
    
    child.on('close', code => {
      if (code === 0) {
        resolve()
      } else {
        reject(new Error(`Script ${scriptName} exited with code ${code}`))
      }
    })
    
    child.on('error', error => {
      reject(error)
    })
  })
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupNewFeatures()
}
