{"version": 3, "sources": ["visitor/typeassembler.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;AAMrB,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAExC,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AACrC,OAAO,EAAE,GAAG,EAAE,MAAM,cAAc,CAAC;AACnC,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACzC,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AACrC,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AACrC,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAC;AAC3C,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AACrC,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AACrC,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AACrC,OAAO,EAAE,OAAO,IAAI,MAAM,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,KAAK,EAAE,MAAM,gBAAgB,CAAC;AACvC,OAAO,EAAE,kBAAkB,EAAE,MAAM,8BAA8B,CAAC;AAClE,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAC;AAC7D,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACzD,OAAO,EAAE,GAAG,IAAI,IAAI,EAAE,MAAM,cAAc,CAAC;AAO3C,cAAc;AACd,MAAM,OAAO,aAAc,SAAQ,OAAO;IAC/B,KAAK,CAA0B,IAAO,EAAE,OAAgB;QAC3D,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACtF,CAAC;IACM,SAAS,CAAsB,KAAQ,EAAE,CAAU;QACtD,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACM,QAAQ,CAAqB,IAAO,EAAE,CAAU;QACnD,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChB,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClC,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IACM,UAAU,CAAuB,IAAO,EAAE,CAAU;QACvD,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACpC,aAAa,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9C,OAAO,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IACM,WAAW,CAAwB,KAAQ,EAAE,CAAU;QAC1D,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACtB,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IACM,gBAAgB,CAA6B,KAAQ,EAAE,CAAU;QACpE,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAChC,OAAO,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IACM,SAAS,CAAsB,KAAQ,EAAE,CAAU;QACtD,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACM,SAAS,CAAsB,KAAQ,EAAE,CAAU;QACtD,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACM,cAAc,CAA2B,KAAQ,EAAE,CAAU;QAChE,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAC5B,OAAO,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;IACM,YAAY,CAAyB,IAAO,EAAE,CAAU;QAC3D,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACxB,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAChC,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACxC,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtC,OAAO,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACM,SAAS,CAAuB,IAAO,EAAE,CAAU;QACtD,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACM,SAAS,CAAsB,IAAO,EAAE,CAAU;QACrD,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3B,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACM,cAAc,CAA2B,IAAO,EAAE,CAAU;QAC/D,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,SAAS,CAAC;QAC/E,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAC5B,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YACzB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;QACvC,CAAC;QACD,OAAO,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC;IACM,aAAa,CAA0B,IAAO,EAAE,CAAU;QAC7D,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC1B,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IACM,aAAa,CAA0B,IAAO,EAAE,CAAU;QAC7D,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC1B,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IACM,SAAS,CAAsB,KAAQ,EAAE,CAAU;QACtD,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACM,WAAW,CAAwB,KAAQ,EAAE,CAAU;QAC1D,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACvB,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC;IACM,UAAU,CAAuB,IAAO,EAAE,CAAU;QACvD,KAAK,CAAC,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,OAAO,GAAG,KAAK,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3D,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACpB,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAC7B,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IACM,eAAe,CAA4B,IAAO,EAAE,CAAU;QACjE,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC9C,kBAAkB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAC9C,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,kBAAkB,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACnD,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC1B,kBAAkB,CAAC,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QAClD,CAAC;QACD,OAAO,kBAAkB,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IACM,oBAAoB,CAAiC,IAAO,EAAE,CAAU;QAC3E,eAAe,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;QACxC,eAAe,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAChD,OAAO,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC;IACM,kBAAkB,CAA+B,IAAO,EAAE,CAAU;QACvE,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACpC,aAAa,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5C,OAAO,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IACM,QAAQ,CAAsB,IAAO,EAAE,CAAU;QACpD,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjB,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC;CACJ;AAED,cAAc;AACd,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAI,aAAa,EAAE,CAAC", "file": "typeassembler.mjs", "sourceRoot": "../src"}