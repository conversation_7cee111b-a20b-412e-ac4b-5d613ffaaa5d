{"version": 3, "sources": ["io/whatwg/builder.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AACzC,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACzC,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAG3D,cAAc;AACd,MAAM,WAAW,uBAAuB,CAAC,CAAC,SAAS,QAAQ,GAAG,GAAG,EAAE,KAAK,GAAG,GAAG,CAAE,SAAQ,cAAc,CAAC,CAAC,EAAE,KAAK,CAAC;IAC5G,gBAAgB,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC;IACrC,sBAAsB,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,MAAM,GAAG,MAAM,CAAC;IACzD,gBAAgB,CAAC,EAAE;QAAE,aAAa,CAAC,EAAE,MAAM,CAAC;QAAC,IAAI,CAAC,EAAE,GAAG,CAAC;QAAC,IAAI,CAAC,EAAE,OAAO,CAAA;KAAE,CAAC;IAC1E,gBAAgB,CAAC,EAAE;QAAE,aAAa,CAAC,EAAE,MAAM,CAAC;QAAC,IAAI,CAAC,EAAE,GAAG,CAAC;QAAC,IAAI,CAAC,EAAE,OAAO,CAAA;KAAE,CAAC;IAC1E,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,CAAC;CAC3F;AAED,cAAc;AACd,wBAAgB,uBAAuB,CAAC,CAAC,SAAS,QAAQ,GAAG,GAAG,EAAE,KAAK,GAAG,GAAG,EAAE,OAAO,EAAE,uBAAuB,CAAC,CAAC,EAAE,KAAK,CAAC,8BAExH;AAED,cAAc;AACd,qBAAa,gBAAgB,CAAC,CAAC,SAAS,QAAQ,GAAG,GAAG,EAAE,KAAK,GAAG,GAAG;IAExD,QAAQ,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,CAAC;IAC9C,WAAW,EAAE,+BAA+B,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAEtE,OAAO,CAAC,UAAU,CAAK;IACvB,OAAO,CAAC,SAAS,CAAS;IAC1B,OAAO,CAAC,aAAa,CAAK;IAC1B,OAAO,CAAC,QAAQ,CAAoB;IACpC,OAAO,CAAC,QAAQ,CAAyC;gBAE7C,OAAO,EAAE,uBAAuB,CAAC,CAAC,EAAE,KAAK,CAAC;IAqCtD,OAAO,CAAC,6BAA6B;IAMrC,OAAO,CAAC,WAAW;IAenB,OAAO,CAAC,QAAQ;CAKnB", "file": "builder.d.ts", "sourceRoot": "../../src"}