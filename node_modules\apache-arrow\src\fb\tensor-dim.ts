// automatically generated by the <PERSON>Buffers compiler, do not modify

import * as flatbuffers from 'flatbuffers';

/**
 * ----------------------------------------------------------------------
 * Data structures for dense tensors
 * Shape data for a single axis in a tensor
 */
export class TensorDim {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):TensorDim {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsTensorDim(bb:flatbuffers.ByteBuffer, obj?:TensorDim):TensorDim {
  return (obj || new TensorDim()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsTensorDim(bb:flatbuffers.ByteBuffer, obj?:TensorDim):TensorDim {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new TensorDim()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

/**
 * Length of dimension
 */
size():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.readInt64(this.bb_pos + offset) : BigInt('0');
}

/**
 * Name of the dimension, optional
 */
name():string|null
name(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
name(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

static startTensorDim(builder:flatbuffers.Builder) {
  builder.startObject(2);
}

static addSize(builder:flatbuffers.Builder, size:bigint) {
  builder.addFieldInt64(0, size, BigInt('0'));
}

static addName(builder:flatbuffers.Builder, nameOffset:flatbuffers.Offset) {
  builder.addFieldOffset(1, nameOffset, 0);
}

static endTensorDim(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

static createTensorDim(builder:flatbuffers.Builder, size:bigint, nameOffset:flatbuffers.Offset):flatbuffers.Offset {
  TensorDim.startTensorDim(builder);
  TensorDim.addSize(builder, size);
  TensorDim.addName(builder, nameOffset);
  return TensorDim.endTensorDim(builder);
}
}
