/**
 * Converts an integer as a number or bigint to a number, throwing an error if the input cannot safely be represented as a number.
 */
export declare function bigIntToNumber(number: bigint | number): number;
/**
 * Duivides the bigint number by the divisor and returns the result as a number.
 * Dividing bigints always results in bigints so we don't get the remainder.
 * This function gives us the remainder but assumes that the result fits into a number.
 *
 * @param number The number to divide.
 * @param divisor The divisor.
 * @returns The result of the division as a number.
 */
export declare function divideBigInts(number: bigint, divisor: bigint): number;
