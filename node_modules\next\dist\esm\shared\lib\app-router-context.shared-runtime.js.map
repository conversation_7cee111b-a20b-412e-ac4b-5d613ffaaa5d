{"version": 3, "sources": ["../../../src/shared/lib/app-router-context.shared-runtime.ts"], "names": ["React", "AppRouterContext", "createContext", "LayoutRouterContext", "GlobalLayoutRouterContext", "TemplateContext", "process", "env", "NODE_ENV", "displayName", "MissingSlotContext", "Set"], "mappings": "AAAA;AASA,OAAOA,WAAW,QAAO;AAiJzB,OAAO,MAAMC,mBAAmBD,MAAME,aAAa,CACjD,MACD;AACD,OAAO,MAAMC,sBAAsBH,MAAME,aAAa,CAK5C,MAAK;AAEf,OAAO,MAAME,4BAA4BJ,MAAME,aAAa,CAMzD,MAAY;AAEf,OAAO,MAAMG,kBAAkBL,MAAME,aAAa,CAAkB,MAAY;AAEhF,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCP,iBAAiBQ,WAAW,GAAG;IAC/BN,oBAAoBM,WAAW,GAAG;IAClCL,0BAA0BK,WAAW,GAAG;IACxCJ,gBAAgBI,WAAW,GAAG;AAChC;AAEA,OAAO,MAAMC,qBAAqBV,MAAME,aAAa,CAAc,IAAIS,OAAM"}