import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth-real'

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  console.log('🔐 Register attempt started')

  try {
    const body = await request.json()
    const { username, email, password, fullName, confirmPassword } = body

    console.log('Register attempt for email:', email)

    // Basic validation
    if (!username || !email || !password || !fullName) {
      return NextResponse.json({
        success: false,
        message: 'All fields are required'
      }, { status: 400 })
    }

    if (password !== confirmPassword) {
      return NextResponse.json({
        success: false,
        message: 'Passwords do not match'
      }, { status: 400 })
    }

    if (password.length < 6) {
      return NextResponse.json({
        success: false,
        message: 'Password must be at least 6 characters long'
      }, { status: 400 })
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        success: false,
        message: 'Please enter a valid email address'
      }, { status: 400 })
    }

    console.log('🔍 Attempting registration for:', email)

    // Attempt registration
    const result = await AuthService.register({
      username: username.trim(),
      email: email.trim().toLowerCase(),
      password,
      fullName: fullName.trim()
    })

    const duration = Date.now() - startTime

    if (result.success) {
      console.log('✅ Registration successful for user:', result.user?.id)
      console.log('✅ Registration completed in', duration + 'ms')

      return NextResponse.json({
        success: true,
        message: 'Registration successful! Please check your email to verify your account.',
        user: result.user
      }, { status: 201 })
    } else {
      console.log('❌ Registration failed:', result.message)
      console.log('❌ Registration failed in', duration + 'ms')

      return NextResponse.json({
        success: false,
        message: result.message || 'Registration failed'
      }, { status: 400 })
    }

  } catch (error) {
    const duration = Date.now() - startTime
    console.error('💥 Registration error:', error)
    console.log('💥 Registration failed in', duration + 'ms')

    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 })
  }
}
