"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./lib/auth-simple.ts":
/*!****************************!*\
  !*** ./lib/auth-simple.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleAuthService: () => (/* binding */ SimpleAuthService)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(middleware)/./node_modules/bcryptjs/dist/bcrypt.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(middleware)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key\";\nconst JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || \"your-super-secret-refresh-key\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"1h\";\nconst JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || \"7d\";\nconst BCRYPT_ROUNDS = 12;\n// Mock users for testing\nconst mockUsers = [\n    {\n        id: 1,\n        username: \"admin\",\n        email: \"<EMAIL>\",\n        full_name: \"Administrator\",\n        role: \"admin\",\n        plan: \"Elite\",\n        level: 100,\n        score: 10000,\n        streak_days: 365,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"admin123\"\n    },\n    {\n        id: 2,\n        username: \"testuser\",\n        email: \"<EMAIL>\",\n        full_name: \"Test User\",\n        role: \"user\",\n        plan: \"Free\",\n        level: 1,\n        score: 0,\n        streak_days: 0,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"test123\"\n    }\n];\nclass SimpleAuthService {\n    // Hash password\n    static async hashPassword(password) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, BCRYPT_ROUNDS);\n    }\n    // Verify password\n    static async verifyPassword(password, hash) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hash);\n    }\n    // Generate JWT tokens\n    static generateTokens(user) {\n        const payload = {\n            id: user.id,\n            username: user.username,\n            email: user.email,\n            role: user.role,\n            plan: user.plan\n        };\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n        const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_REFRESH_SECRET, {\n            expiresIn: JWT_REFRESH_EXPIRES_IN\n        });\n        return {\n            accessToken,\n            refreshToken,\n            expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds\n        };\n    }\n    // Verify JWT token\n    static verifyToken(token, isRefreshToken = false) {\n        try {\n            const secret = isRefreshToken ? JWT_REFRESH_SECRET : JWT_SECRET;\n            console.log(\"\\uD83D\\uDD0D Verifying token with secret length:\", secret.length);\n            console.log(\"\\uD83D\\uDD0D Token length:\", token.length);\n            const result = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n            console.log(\"✅ Token verification successful:\", result);\n            return result;\n        } catch (error) {\n            console.log(\"❌ Token verification error:\", error.message);\n            return null;\n        }\n    }\n    // Register new user\n    static async register(data) {\n        try {\n            // Check if user already exists\n            const existingUser = mockUsers.find((u)=>u.email === data.email || u.username === data.username);\n            if (existingUser) {\n                return {\n                    success: false,\n                    message: \"User already exists with this email or username\"\n                };\n            }\n            // Create new user\n            const newUser = {\n                id: mockUsers.length + 1,\n                username: data.username,\n                email: data.email,\n                full_name: data.fullName || \"\",\n                role: \"user\",\n                plan: \"Free\",\n                level: 1,\n                score: 0,\n                streak_days: 0,\n                email_verified: false,\n                status: \"active\",\n                last_active: new Date(),\n                created_at: new Date(),\n                password: data.password\n            };\n            // Add to mock users\n            mockUsers.push(newUser);\n            // Remove password from user object\n            const { password, ...userWithoutPassword } = newUser;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                message: \"Registration failed\"\n            };\n        }\n    }\n    // Login user\n    static async login(email, password) {\n        try {\n            // Find user by email\n            const user = mockUsers.find((u)=>u.email === email && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Verify password (simple comparison for testing)\n            if (password !== user.password) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Remove password from user object\n            const { password: userPassword, ...userWithoutPassword } = user;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"Login failed\"\n            };\n        }\n    }\n    // Authenticate request\n    static async authenticateRequest(request) {\n        try {\n            // Get token from Authorization header or cookies\n            let token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n            if (!token) {\n                // Try to get from cookies\n                const cookies = request.headers.get(\"cookie\");\n                console.log(\"\\uD83C\\uDF6A Cookies in middleware:\", cookies);\n                if (cookies) {\n                    const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n                    token = tokenMatch ? tokenMatch[1] : undefined;\n                    console.log(\"\\uD83D\\uDD11 Token from cookies:\", token ? \"Found\" : \"Not found\");\n                }\n            }\n            if (!token) {\n                console.log(\"❌ No token found in headers or cookies\");\n                return {\n                    success: false,\n                    message: \"No token provided\"\n                };\n            }\n            // Verify token\n            console.log(\"\\uD83D\\uDD0D Verifying token in middleware...\");\n            const decoded = this.verifyToken(token);\n            if (!decoded) {\n                console.log(\"❌ Token verification failed in middleware\");\n                return {\n                    success: false,\n                    message: \"Invalid token\"\n                };\n            }\n            console.log(\"✅ Token decoded successfully:\", decoded.id, decoded.username);\n            // Get user from mock data\n            const user = mockUsers.find((u)=>u.id === decoded.id);\n            if (!user) {\n                console.log(\"❌ User not found in mock data:\", decoded.id);\n                return {\n                    success: false,\n                    message: \"User not found\"\n                };\n            }\n            console.log(\"✅ User found in middleware:\", user.username);\n            const { password, ...userWithoutPassword } = user;\n            return {\n                success: true,\n                user: userWithoutPassword\n            };\n        } catch (error) {\n            console.error(\"Authentication error:\", error);\n            return {\n                success: false,\n                message: \"Authentication failed\"\n            };\n        }\n    }\n    // Logout user (for mock, just return success)\n    static async logout(userId, _sessionToken) {\n        // In a real implementation, this would remove the session from database\n        console.log(`User ${userId} logged out`);\n    }\n    // Refresh token\n    static async refreshToken(refreshToken) {\n        try {\n            // Verify refresh token\n            const decoded = this.verifyToken(refreshToken, true);\n            if (!decoded) {\n                return {\n                    success: false,\n                    message: \"Invalid refresh token\"\n                };\n            }\n            // Get user data\n            const user = mockUsers.find((u)=>u.id === decoded.id && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"User not found or inactive\"\n                };\n            }\n            const { password, ...userWithoutPassword } = user;\n            // Generate new tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update user last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            return {\n                success: false,\n                message: \"Token refresh failed\"\n            };\n        }\n    }\n    // Get user by ID\n    static async getUserById(userId) {\n        try {\n            const user = mockUsers.find((u)=>u.id === userId);\n            if (!user) return null;\n            const { password, ...userWithoutPassword } = user;\n            return userWithoutPassword;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            return null;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./lib/auth-simple.ts\n");

/***/ })

});