"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,Eye,FileText,Flame,Globe,Play,RefreshCw,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DashboardPage() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalScans: 0,\n        vulnerabilitiesFound: 0,\n        filesAnalyzed: 0,\n        osintQueries: 0,\n        apiCalls: 0,\n        score: 0,\n        level: 0,\n        rank: 0,\n        streak: 0,\n        pointsToday: 0,\n        pointsThisWeek: 0,\n        pointsThisMonth: 0\n    });\n    const [recentActivity, setRecentActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [achievements, setAchievements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [planUsage, setPlanUsage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [trendingVulns, setTrendingVulns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update time every minute\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD04 Loading dashboard data...\");\n            setLoading(true);\n            // Fetch real dashboard data from API\n            const response = await fetch(\"/api/dashboard/stats\");\n            console.log(\"\\uD83D\\uDCE1 API Response status:\", response.status);\n            const data = await response.json();\n            console.log(\"\\uD83D\\uDCCA API Response data:\", data);\n            if (data.success) {\n                setStats({\n                    totalScans: data.data.stats.totalScans,\n                    vulnerabilitiesFound: data.data.stats.vulnerabilitiesFound,\n                    filesAnalyzed: data.data.stats.fileAnalyses,\n                    osintQueries: data.data.stats.osintQueries,\n                    apiCalls: data.data.stats.cveSearches + data.data.stats.dorkingQueries,\n                    score: data.data.user.score,\n                    level: data.data.user.level,\n                    rank: data.data.user.rank,\n                    streak: data.data.user.streak,\n                    pointsToday: Math.floor(data.data.user.score * 0.1),\n                    pointsThisWeek: Math.floor(data.data.user.score * 0.3),\n                    pointsThisMonth: Math.floor(data.data.user.score * 0.6)\n                });\n                // Transform recent activities to match interface\n                const transformedActivities = data.data.recentActivities.map((activity)=>({\n                        id: activity.id.toString(),\n                        type: activity.type === \"vulnerability_scan\" ? \"scan\" : activity.type === \"osint_query\" ? \"osint\" : activity.type === \"file_analysis\" ? \"file\" : activity.type === \"cve_search\" ? \"cve\" : \"dorking\",\n                        target: activity.description.includes(\"target.\") ? \"target.example.com\" : activity.description.includes(\"domain\") ? \"example.com\" : activity.description.includes(\"file:\") ? \"malware.exe\" : activity.description.includes(\"Apache\") ? \"Apache HTTP Server\" : activity.description.includes(\"databases\") ? \"Google Search\" : \"Unknown\",\n                        time: new Date(activity.timestamp).toLocaleTimeString() + \" ago\",\n                        status: \"completed\",\n                        severity: activity.severity,\n                        result: activity.result\n                    }));\n                setRecentActivity(transformedActivities);\n                setUser(data.data.user);\n                setAchievements(data.data.achievements);\n                setPlanUsage(data.data.usage);\n                setTrendingVulns(data.data.trendingVulnerabilities);\n                console.log(\"✅ Dashboard data loaded successfully\");\n            } else {\n                console.log(\"❌ API returned success: false\");\n                // Fallback to mock data if API fails\n                setStats({\n                    totalScans: 142,\n                    vulnerabilitiesFound: 23,\n                    filesAnalyzed: 89,\n                    osintQueries: 456,\n                    apiCalls: 2847,\n                    score: 8950,\n                    level: 28,\n                    rank: 156,\n                    streak: 12,\n                    pointsToday: 250,\n                    pointsThisWeek: 1450,\n                    pointsThisMonth: 5890\n                });\n                setRecentActivity([\n                    {\n                        id: \"1\",\n                        type: \"scan\",\n                        target: \"example.com\",\n                        time: \"2 minutes ago\",\n                        status: \"completed\",\n                        severity: \"high\",\n                        result: \"3 vulnerabilities found\"\n                    }\n                ]);\n            }\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            setLoading(false);\n        }\n    };\n    const quickActions = [\n        {\n            title: \"OSINT Lookup\",\n            description: \"Investigate emails, phone numbers, and personal information\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"/osint\",\n            badge: \"Popular\",\n            color: \"cyber-primary\",\n            stats: \"456 queries\"\n        },\n        {\n            title: \"Vulnerability Scan\",\n            description: \"Scan websites and applications for security vulnerabilities\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"/scanner\",\n            premium: true,\n            color: \"cyber-secondary\",\n            stats: \"142 scans\"\n        },\n        {\n            title: \"File Analysis\",\n            description: \"Analyze files for malware, webshells, and suspicious content\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"/file-analyzer\",\n            premium: true,\n            color: \"cyber-accent\",\n            stats: \"89 files\"\n        },\n        {\n            title: \"CVE Database\",\n            description: \"Search and explore the latest CVE vulnerabilities\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: \"/cve\",\n            color: \"green-400\",\n            stats: \"50K+ CVEs\"\n        },\n        {\n            title: \"Google Dorking\",\n            description: \"Use advanced search queries to find sensitive information\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: \"/dorking\",\n            color: \"blue-400\",\n            stats: \"Advanced\"\n        },\n        {\n            title: \"API Playground\",\n            description: \"Test and explore our API endpoints\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            href: \"/playground\",\n            color: \"purple-400\",\n            stats: \"2.8K calls\"\n        }\n    ];\n    const getActivityIcon = (type)=>{\n        switch(type){\n            case \"scan\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"osint\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"file\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"cve\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"dorking\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            default:\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"text-green-400\";\n            case \"failed\":\n                return \"text-red-400\";\n            case \"malicious\":\n                return \"text-red-400\";\n            case \"clean\":\n                return \"text-green-400\";\n            case \"pending\":\n                return \"text-yellow-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"critical\":\n                return \"text-red-400 bg-red-400/20\";\n            case \"high\":\n                return \"text-orange-400 bg-orange-400/20\";\n            case \"medium\":\n                return \"text-yellow-400 bg-yellow-400/20\";\n            case \"low\":\n                return \"text-green-400 bg-green-400/20\";\n            default:\n                return \"text-gray-400 bg-gray-400/20\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-cyber-primary font-medium\",\n                            children: \"Loading cyber dashboard...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 293,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-glow\",\n                                            children: \"Cyber\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-pink\",\n                                            children: \"Command\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg\",\n                                    children: [\n                                        \"Welcome back, \",\n                                        (user === null || user === void 0 ? void 0 : user.username) || \"Cyber Warrior\",\n                                        \"! Your digital arsenal awaits.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 lg:mt-0 flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: loadDashboardData,\n                                    disabled: loading,\n                                    className: \"btn-cyber-secondary text-sm\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-cyber-primary mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Refreshing...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Refresh\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Current Time\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-mono text-cyber-primary\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-cyber-primary\",\n                                    onClick: ()=>window.location.href = \"/vulnerability-scanner\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Quick Scan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-2xl font-bold text-black\",\n                                        children: (user === null || user === void 0 ? void 0 : user.username) ? user.username.substring(0, 2).toUpperCase() : \"CW\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: (user === null || user === void 0 ? void 0 : user.fullName) || (user === null || user === void 0 ? void 0 : user.username) || \"Cyber Warrior\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Level \",\n                                                            stats.level\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded text-xs font-medium \".concat((user === null || user === void 0 ? void 0 : user.plan) === \"Elite\" ? \"bg-purple-500/20 text-purple-400\" : (user === null || user === void 0 ? void 0 : user.plan) === \"Pro\" ? \"bg-blue-500/20 text-blue-400\" : (user === null || user === void 0 ? void 0 : user.plan) === \"Cybersecurity\" ? \"bg-red-500/20 text-red-400\" : (user === null || user === void 0 ? void 0 : user.plan) === \"Bughunter\" ? \"bg-orange-500/20 text-orange-400\" : \"bg-gray-500/20 text-gray-400\"),\n                                                        children: (user === null || user === void 0 ? void 0 : user.plan) || \"Free\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Rank #\",\n                                                            stats.rank\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 text-cyber-secondary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    stats.streak,\n                                                                    \" day streak\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-cyber-primary\",\n                                        children: stats.score.toLocaleString()\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"Total Score\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-green-400\",\n                                                children: [\n                                                    \"+\",\n                                                    stats.pointsToday,\n                                                    \" today\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-3 w-3 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-8 w-8 text-cyber-primary mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.totalScans\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Total Scans\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-green-400 mt-1\",\n                                    children: \"+12 this week\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-8 w-8 text-red-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.vulnerabilitiesFound\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Vulnerabilities\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-red-400 mt-1\",\n                                    children: \"+3 critical\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-8 w-8 text-cyber-accent mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.filesAnalyzed\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Files Analyzed\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-yellow-400 mt-1\",\n                                    children: \"5 malicious\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-8 w-8 text-blue-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.osintQueries\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"OSINT Queries\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-blue-400 mt-1\",\n                                    children: \"+45 today\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.apiCalls.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"API Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-purple-400 mt-1\",\n                                    children: \"98% uptime\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.pointsThisWeek.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Weekly Points\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-green-400 mt-1\",\n                                    children: \"+15% vs last week\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 399,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber border-l-4 border-l-cyber-secondary\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-6 w-6 text-cyber-secondary mt-1\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold text-white mb-1\",\n                                        children: \"System Status Update\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-3\",\n                                        children: \"New CVE database update available. 15 critical vulnerabilities added to our intelligence feed.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-cyber-secondary text-sm\",\n                                                children: \"View Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"2 minutes ago\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Quick\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: quickActions.map((action, index)=>{\n                                const Icon = action.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber hover:scale-105 transition-all duration-300 cursor-pointer group\",\n                                    onClick: ()=>window.location.href = action.href,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg bg-\".concat(action.color, \"/20\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-6 w-6 text-\".concat(action.color, \" group-hover:animate-cyber-pulse\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-end space-y-1\",\n                                                    children: [\n                                                        action.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-cyber-secondary/20 text-cyber-secondary px-2 py-1 rounded-full text-xs font-bold\",\n                                                            children: action.badge\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        action.premium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-cyber-accent/20 text-cyber-accent px-2 py-1 rounded-full text-xs font-bold\",\n                                                            children: \"PRO\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-2 group-hover:text-cyber-primary transition-colors\",\n                                            children: action.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm mb-4\",\n                                            children: action.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium text-\".concat(action.color),\n                                                    children: action.stats\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400 group-hover:text-cyber-primary transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 9\n                }, this),\n                planUsage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Plan\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Usage\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: \"Vulnerability Scans\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5 text-cyber-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Used\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: [\n                                                                planUsage.usage.vulnerabilityScans,\n                                                                \" / \",\n                                                                planUsage.limits.scans === -1 ? \"∞\" : planUsage.limits.scans\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, this),\n                                                planUsage.limits.scans !== -1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-cyber-primary h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(Math.min(planUsage.percentage.vulnerabilityScans, 100), \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: \"OSINT Queries\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-cyber-secondary\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Used\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: [\n                                                                planUsage.usage.osintQueries,\n                                                                \" / \",\n                                                                planUsage.limits.osint === -1 ? \"∞\" : planUsage.limits.osint\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 19\n                                                }, this),\n                                                planUsage.limits.osint !== -1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-cyber-secondary h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(Math.min(planUsage.percentage.osintQueries, 100), \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: \"File Analyses\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5 text-cyber-accent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"Used\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white\",\n                                                            children: [\n                                                                planUsage.usage.fileAnalyses,\n                                                                \" / \",\n                                                                planUsage.limits.files === -1 ? \"∞\" : planUsage.limits.files\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 19\n                                                }, this),\n                                                planUsage.limits.files !== -1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-cyber-accent h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(Math.min(planUsage.percentage.fileAnalyses, 100), \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 529,\n                    columnNumber: 11\n                }, this),\n                achievements && achievements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Recent\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Achievements\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 605,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: achievements.slice(0, 4).map((achievement)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber text-center transition-all duration-300 \".concat(achievement.unlocked ? \"border-cyber-primary/50 bg-cyber-primary/5\" : \"border-gray-600/50 bg-gray-800/50 opacity-60\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center \".concat(achievement.unlocked ? \"bg-cyber-primary/20 text-cyber-primary\" : \"bg-gray-700 text-gray-500\"),\n                                            children: [\n                                                achievement.icon === \"shield\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 55\n                                                }, this),\n                                                achievement.icon === \"eye\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 52\n                                                }, this),\n                                                achievement.icon === \"bug\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 52\n                                                }, this),\n                                                achievement.icon === \"flame\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_Eye_FileText_Flame_Globe_Play_RefreshCw_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 54\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2 \".concat(achievement.unlocked ? \"text-white\" : \"text-gray-400\"),\n                                            children: achievement.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 mb-3\",\n                                            children: achievement.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(achievement.rarity === \"legendary\" ? \"bg-purple-500/20 text-purple-400\" : achievement.rarity === \"epic\" ? \"bg-orange-500/20 text-orange-400\" : achievement.rarity === \"rare\" ? \"bg-blue-500/20 text-blue-400\" : \"bg-gray-500/20 text-gray-400\"),\n                                            children: achievement.rarity\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 639,\n                                            columnNumber: 19\n                                        }, this),\n                                        achievement.unlocked && achievement.unlockedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-2\",\n                                            children: [\n                                                \"Unlocked \",\n                                                new Date(achievement.unlockedAt).toLocaleDateString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, achievement.id, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 604,\n                    columnNumber: 11\n                }, this),\n                trendingVulns && trendingVulns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Trending\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Vulnerabilities\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 664,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 662,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: trendingVulns.map((vuln)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-white mb-1\",\n                                                            children: vuln.id\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 672,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: vuln.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-2 py-1 rounded-full text-xs font-bold uppercase \".concat(vuln.severity === \"critical\" ? \"bg-red-500/20 text-red-400\" : vuln.severity === \"high\" ? \"bg-orange-500/20 text-orange-400\" : vuln.severity === \"medium\" ? \"bg-yellow-500/20 text-yellow-400\" : \"bg-green-500/20 text-green-400\"),\n                                                    children: vuln.severity\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 670,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-300 mb-4\",\n                                            children: vuln.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400\",\n                                                    children: [\n                                                        \"CVSS: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white font-medium\",\n                                                            children: vuln.score\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 689,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 688,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400\",\n                                                    children: [\n                                                        \"Affected: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-cyber-primary font-medium\",\n                                                            children: vuln.affectedSystems.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, vuln.id, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 661,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Recent\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Activity\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 705,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 703,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: recentActivity.map((activity)=>{\n                                        const Icon = getActivityIcon(activity.type);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5 hover:bg-cyber-primary/10 transition-colors duration-200 cursor-pointer\",\n                                            onClick: ()=>{\n                                                // Navigate to appropriate tool based on activity type\n                                                const routes = {\n                                                    \"scan\": \"/vulnerability-scanner\",\n                                                    \"osint\": \"/osint\",\n                                                    \"file\": \"/file-analyzer\",\n                                                    \"cve\": \"/cve-intelligence\",\n                                                    \"dorking\": \"/dorking\"\n                                                };\n                                                const route = routes[activity.type];\n                                                if (route) {\n                                                    window.location.href = route;\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-cyber-primary/20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"h-5 w-5 text-cyber-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 733,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 732,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium text-white\",\n                                                                            children: activity.target\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 738,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-bold uppercase \".concat(getStatusColor(activity.status)),\n                                                                            children: activity.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 739,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        activity.severity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-bold uppercase \".concat(getSeverityColor(activity.severity)),\n                                                                            children: activity.severity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 743,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-sm\",\n                                                                    children: activity.result\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: activity.time\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 capitalize\",\n                                                            children: activity.type\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, activity.id, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-cyber-primary hover:text-cyber-secondary transition-colors\",\n                                        children: \"View All Activity →\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 762,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 761,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 708,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 702,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 306,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 305,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"BFRTwvn+5IkN9CfaeTLp5dlKHsI=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});