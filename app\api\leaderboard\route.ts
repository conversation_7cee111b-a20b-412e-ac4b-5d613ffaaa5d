'use server'

import { NextRequest, NextResponse } from 'next/server'
import { ScoringService } from '@/lib/services/scoring'

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const timeframe = url.searchParams.get('timeframe') || 'all'
    const limit = parseInt(url.searchParams.get('limit') || '50')
    const offset = parseInt(url.searchParams.get('offset') || '0')

    const scoringService = new ScoringService()
    const leaderboard = await scoringService.getLeaderboard(limit, offset)

    return NextResponse.json({
      success: true,
      data: {
        leaderboard: leaderboard,
        timeframe: timeframe,
        total: leaderboard.length
      }
    })

  } catch (error) {
    console.error('Error getting leaderboard:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get leaderboard'
      },
      { status: 500 }
    )
  }
}
