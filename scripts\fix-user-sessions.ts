#!/usr/bin/env ts-node

import { testDatabaseConnection, closeDatabaseConnections, db } from '../lib/database'

async function fixUserSessions() {
  console.log('🔧 Fixing User Sessions Table...\n')

  try {
    // Test database connection
    const connected = await testDatabaseConnection()
    if (!connected) {
      console.error('❌ Database connection failed')
      process.exit(1)
    }

    // Drop and recreate user_sessions table with correct structure
    console.log('🔄 Dropping existing user_sessions table...')
    await db.query('DROP TABLE IF EXISTS user_sessions')

    console.log('🔄 Creating user_sessions table with correct structure...')
    await db.query(`
      CREATE TABLE user_sessions (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        session_token TEXT NOT NULL,
        refresh_token TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_session_token (session_token(255)),
        INDEX idx_expires_at (expires_at)
      )
    `)

    console.log('✅ user_sessions table recreated successfully!')

    // Also fix api_keys table if needed
    console.log('🔄 Checking api_keys table structure...')
    try {
      const columns = await db.query('DESCRIBE api_keys') as any[]
      const userIdColumn = columns.find((col: any) => col.Field === 'user_id')
      
      if (userIdColumn && userIdColumn.Type.includes('int')) {
        console.log('🔄 Updating api_keys table structure...')
        
        // Drop foreign key constraint first
        await db.query('ALTER TABLE api_keys DROP FOREIGN KEY IF EXISTS api_keys_ibfk_1')
        
        // Change column type
        await db.query('ALTER TABLE api_keys MODIFY COLUMN user_id VARCHAR(36) NOT NULL')
        
        console.log('✅ api_keys table updated successfully!')
      } else {
        console.log('ℹ️ api_keys table already has correct structure')
      }
    } catch (error: any) {
      console.log(`⚠️ Error checking api_keys table: ${error.message}`)
    }

    console.log('\n✅ User sessions fix completed successfully!')
    
  } catch (error) {
    console.error('❌ Fix user sessions failed:', error)
    process.exit(1)
  } finally {
    await closeDatabaseConnections()
  }
}

// Run fix if this file is executed directly
if (require.main === module) {
  fixUserSessions()
}
