{"version": 3, "file": "ChromeLauncher.js", "sourceRoot": "", "sources": ["../../../../src/node/ChromeLauncher.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;AAEH,0CAAoC;AACpC,4CAAoB;AACpB,gDAAwB;AAExB,kDAI6B;AAG7B,+CAA6C;AAC7C,iDAAyC;AAOzC,6DAA8E;AAE9E,wCAAgC;AAEhC;;GAEG;AACH,MAAa,cAAe,SAAQ,oCAAe;IACjD,YAAY,SAAwB;QAClC,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAEQ,MAAM,CAAC,UAAsC,EAAE;QACtD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC;QAC1C,IACE,QAAQ,KAAK,IAAI;YACjB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,KAAK,MAAM;YAChD,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC,EAC3D,CAAC;YACD,OAAO,CAAC,IAAI,CACV;gBACE,yBAAyB;gBACzB,4DAA4D;gBAC5D,6EAA6E;gBAC7E,mEAAmE;gBACnE,gFAAgF;gBAChF,iFAAiF;gBACjF,uHAAuH;aACxH,CAAC,IAAI,CAAC,MAAM,CAAC,CACf,CAAC;QACJ,CAAC;QAED,IACE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,KAAK,MAAM;YAChD,OAAO,CAAC,QAAQ,KAAK,QAAQ;YAC7B,OAAO,CAAC,IAAI,KAAK,KAAK,EACtB,CAAC;YACD,MAAM,IAAI,GAAG,YAAE,CAAC,IAAI,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACrC,OAAO,CAAC,IAAI,CACV;oBACE,yBAAyB;oBACzB,8CAA8C;oBAC9C,kFAAkF;oBAClF,oFAAoF;oBACpF,iFAAiF;oBACjF,oCAAoC;iBACrC,CAAC,IAAI,CAAC,MAAM,CAAC,CACf,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,sBAAsB,CACnC,UAAsC,EAAE;QAExC,MAAM,EACJ,iBAAiB,GAAG,KAAK,EACzB,IAAI,GAAG,EAAE,EACT,IAAI,GAAG,KAAK,EACZ,aAAa,EACb,OAAO,EACP,cAAc,GACf,GAAG,OAAO,CAAC;QAEZ,MAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;QACrD,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC5C,eAAe,CAAC,IAAI,CAClB,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;gBACxC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC1C,CAAC,CAAC,CACH,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAChC,CAAC;QAED,IACE,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC/B,OAAO,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;QACpD,CAAC,CAAC,EACF,CAAC;YACD,IAAI,IAAI,EAAE,CAAC;gBACT,IAAA,kBAAM,EACJ,CAAC,aAAa,EACd,2EAA2E,CAC5E,CAAC;gBACF,eAAe,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,eAAe,CAAC,IAAI,CAAC,2BAA2B,aAAa,IAAI,CAAC,EAAE,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAE9B,sEAAsE;QACtE,gEAAgE;QAChE,IAAI,gBAAgB,GAAG,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACrD,OAAO,GAAG,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,IAAI,gBAAgB,GAAG,CAAC,EAAE,CAAC;YACzB,iBAAiB,GAAG,IAAI,CAAC;YACzB,eAAe,CAAC,IAAI,CAClB,mBAAmB,MAAM,IAAA,kBAAO,EAAC,IAAI,CAAC,cAAc,EAAE,CAAC,EAAE,CAC1D,CAAC;YACF,gBAAgB,GAAG,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,WAAW,GAAG,eAAe,CAAC,gBAAgB,CAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,IAAA,kBAAM,EAAC,OAAO,WAAW,KAAK,QAAQ,EAAE,gCAAgC,CAAC,CAAC;QAE1E,IAAI,gBAAgB,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,IAAA,kBAAM,EACJ,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAC3C,+EAA+E,CAChF,CAAC;YACF,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC;QAC5E,CAAC;QAED,OAAO;YACL,cAAc,EAAE,gBAAgB;YAChC,IAAI,EAAE,eAAe;YACrB,iBAAiB;YACjB,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,gBAAgB,CAC7B,IAAY,EACZ,IAAuB;QAEvB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC;gBACH,MAAM,IAAA,UAAE,EAAC,IAAI,CAAC,CAAC;YACjB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;gBAClB,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAEQ,WAAW,CAAC,UAAwC,EAAE;QAC7D,+FAA+F;QAE/F,MAAM,oBAAoB,GAAG,WAAW,CACtC,oBAAoB,EACpB,OAAO,CAAC,IAAI,CACb,CAAC;QACF,IAAI,OAAO,CAAC,IAAI,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpD,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC;QAC1D,CAAC;QAED,mEAAmE;QACnE,MAAM,gBAAgB,GAAG;YACvB,WAAW;YACX,uDAAuD;YACvD,eAAe;YACf,aAAa;YACb,mBAAmB;YACnB,4BAA4B;YAC5B,sCAAsC;YACtC,GAAG,oBAAoB;SACxB,CAAC;QAEF,MAAM,mBAAmB,GAAG,WAAW,CAAC,mBAAmB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QAC3E,IAAI,OAAO,CAAC,IAAI,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnD,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;QACzD,CAAC;QAED,kEAAkE;QAClE,MAAM,eAAe,GAAG;YACtB,0BAA0B;YAC1B,GAAG,mBAAmB;SACvB,CAAC;QAEF,MAAM,eAAe,GAAG;YACtB,0BAA0B;YAC1B,iCAAiC;YACjC,uCAAuC;YACvC,0CAA0C;YAC1C,oBAAoB;YACpB,0CAA0C;YAC1C,sDAAsD;YACtD,4BAA4B;YAC5B,wBAAwB;YACxB,yBAAyB;YACzB,sBAAsB;YACtB,8BAA8B,EAAE,wFAAwF;YACxH,wBAAwB;YACxB,oBAAoB;YACpB,mCAAmC;YACnC,0BAA0B;YAC1B,4BAA4B;YAC5B,kCAAkC;YAClC,uCAAuC;YACvC,gBAAgB;YAChB,qBAAqB;YACrB,qBAAqB;YACrB,iCAAiC;YACjC,4BAA4B;YAC5B,0BAA0B;YAC1B,gBAAgB;YAChB,wBAAwB;YACxB,qBAAqB;YACrB,sBAAsB,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAClD,qBAAqB,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;SACjD,CAAC;QACF,MAAM,EACJ,QAAQ,GAAG,KAAK,EAChB,QAAQ,GAAG,CAAC,QAAQ,EACpB,IAAI,GAAG,EAAE,EACT,WAAW,GACZ,GAAG,OAAO,CAAC;QACZ,IAAI,WAAW,EAAE,CAAC;YAChB,eAAe,CAAC,IAAI,CAAC,mBAAmB,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QACvE,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACb,eAAe,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACb,eAAe,CAAC,IAAI,CAClB,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,YAAY,EACpD,mBAAmB,EACnB,cAAc,CACf,CAAC;QACJ,CAAC;QACD,IACE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACf,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC,EACF,CAAC;YACD,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACtC,CAAC;QACD,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAC9B,OAAO,eAAe,CAAC;IACzB,CAAC;IAEQ,cAAc,CACrB,OAA8B,EAC9B,QAA0B;QAE1B,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,IAAA,sCAA2B,EAAC;gBACjC,OAAO,EAAE,kBAAiB,CAAC,MAAM;gBACjC,OAAO,EAAE,wCAAwC,CAAC,OAAO,CAAC;aAC3D,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;CACF;AA7PD,wCA6PC;AAED,SAAS,wCAAwC,CAC/C,OAA6B;IAE7B,QAAQ,OAAO,EAAE,CAAC;QAChB,KAAK,QAAQ;YACX,OAAO,+BAA4B,CAAC,MAAM,CAAC;QAC7C,KAAK,YAAY;YACf,OAAO,+BAA4B,CAAC,GAAG,CAAC;QAC1C,KAAK,aAAa;YAChB,OAAO,+BAA4B,CAAC,IAAI,CAAC;QAC3C,KAAK,eAAe;YAClB,OAAO,+BAA4B,CAAC,MAAM,CAAC;IAC/C,CAAC;AACH,CAAC;AAED;;;;;;;;;;;GAWG;AACH,SAAgB,WAAW,CAAC,IAAY,EAAE,UAAoB,EAAE;IAC9D,OAAO,OAAO;SACX,MAAM,CAAC,CAAC,CAAC,EAAE;QACV,OAAO,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;IAC9D,CAAC,CAAC;SACD,GAAG,CAAC,CAAC,CAAC,EAAE;QACP,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;IACxD,CAAC,CAAC;SACD,MAAM,CAAC,CAAC,CAAC,EAAE;QACV,OAAO,CAAC,CAAC;IACX,CAAC,CAAa,CAAC;AACnB,CAAC;AAXD,kCAWC;AAED;;;;;GAKG;AACH,SAAgB,mBAAmB,CAAC,KAAe,EAAE,IAAY;IAC/D,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;IACxC,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QACxB,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAE,CAAC,EAAE,CAAC;YAC1B,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,CAAC,EAAE,CAAC;QACN,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAXD,kDAWC"}