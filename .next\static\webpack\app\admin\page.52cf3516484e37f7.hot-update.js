"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/alert-triangle.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlertTriangle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst AlertTriangle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"AlertTriangle\", [\n    [\n        \"path\",\n        {\n            d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\",\n            key: \"c3ski4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 9v4\",\n            key: \"juzpu7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 17h.01\",\n            key: \"p32p05\"\n        }\n    ]\n]);\n //# sourceMappingURL=alert-triangle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/arrow-up.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ArrowUp; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst ArrowUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ArrowUp\", [\n    [\n        \"path\",\n        {\n            d: \"m5 12 7-7 7 7\",\n            key: \"hav0vg\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 19V5\",\n            key: \"x0mq9r\"\n        }\n    ]\n]);\n //# sourceMappingURL=arrow-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvYXJyb3ctdXAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFhTSxNQUFBQSxVQUFVQyxnRUFBZ0JBLENBQUMsV0FBVztJQUMxQztRQUFDO1FBQVE7WUFBRUMsR0FBRztZQUFpQkMsS0FBSztRQUFBO0tBQVU7SUFDOUM7UUFBQztRQUFRO1lBQUVELEdBQUc7WUFBWUMsS0FBSztRQUFBO0tBQVU7Q0FDMUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9pY29ucy9hcnJvdy11cC50cz82M2Y2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgQXJyb3dVcFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TlNBeE1pQTNMVGNnTnlBM0lpQXZQZ29nSUR4d1lYUm9JR1E5SWsweE1pQXhPVlkxSWlBdlBnbzhMM04yWno0SykgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvYXJyb3ctdXBcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKlxuICovXG5jb25zdCBBcnJvd1VwID0gY3JlYXRlTHVjaWRlSWNvbignQXJyb3dVcCcsIFtcbiAgWydwYXRoJywgeyBkOiAnbTUgMTIgNy03IDcgNycsIGtleTogJ2hhdjB2ZycgfV0sXG4gIFsncGF0aCcsIHsgZDogJ00xMiAxOVY1Jywga2V5OiAneDBtcTlyJyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBBcnJvd1VwO1xuIl0sIm5hbWVzIjpbIkFycm93VXAiLCJjcmVhdGVMdWNpZGVJY29uIiwiZCIsImtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/check-circle.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CheckCircle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst CheckCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CheckCircle\", [\n    [\n        \"path\",\n        {\n            d: \"M22 11.08V12a10 10 0 1 1-5.93-9.14\",\n            key: \"g774vq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n]);\n //# sourceMappingURL=check-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/credit-card.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CreditCard; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst CreditCard = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CreditCard\", [\n    [\n        \"rect\",\n        {\n            width: \"20\",\n            height: \"14\",\n            x: \"2\",\n            y: \"5\",\n            rx: \"2\",\n            key: \"ynyp8z\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"2\",\n            x2: \"22\",\n            y1: \"10\",\n            y2: \"10\",\n            key: \"1b3vmo\"\n        }\n    ]\n]);\n //# sourceMappingURL=credit-card.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/eye.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Eye; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst Eye = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Eye\", [\n    [\n        \"path\",\n        {\n            d: \"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z\",\n            key: \"rwhkz3\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"3\",\n            key: \"1v7zrd\"\n        }\n    ]\n]);\n //# sourceMappingURL=eye.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZXllLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBYU0sTUFBQUEsTUFBTUMsZ0VBQWdCQSxDQUFDLE9BQU87SUFDbEM7UUFDRTtRQUNBO1lBQUVDLEdBQUc7WUFBZ0RDLEtBQUs7UUFBUztLQUNyRTtJQUNBO1FBQUM7UUFBVTtZQUFFQyxJQUFJO1lBQU1DLElBQUk7WUFBTUMsR0FBRztZQUFLSCxLQUFLO1FBQUE7S0FBVTtDQUN6RCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL2ljb25zL2V5ZS50cz84Y2I4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgRXllXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NaUF4TW5NekxUY2dNVEF0TnlBeE1DQTNJREV3SURjdE15QTNMVEV3SURjdE1UQXROeTB4TUMwM1dpSWdMejRLSUNBOFkybHlZMnhsSUdONFBTSXhNaUlnWTNrOUlqRXlJaUJ5UFNJeklpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2V5ZVxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IEV5ZSA9IGNyZWF0ZUx1Y2lkZUljb24oJ0V5ZScsIFtcbiAgW1xuICAgICdwYXRoJyxcbiAgICB7IGQ6ICdNMiAxMnMzLTcgMTAtNyAxMCA3IDEwIDctMyA3LTEwIDctMTAtNy0xMC03WicsIGtleTogJ3J3aGt6MycgfSxcbiAgXSxcbiAgWydjaXJjbGUnLCB7IGN4OiAnMTInLCBjeTogJzEyJywgcjogJzMnLCBrZXk6ICcxdjd6cmQnIH1dLFxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IEV5ZTtcbiJdLCJuYW1lcyI6WyJFeWUiLCJjcmVhdGVMdWNpZGVJY29uIiwiZCIsImtleSIsImN4IiwiY3kiLCJyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/monitor.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Monitor; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst Monitor = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Monitor\", [\n    [\n        \"rect\",\n        {\n            width: \"20\",\n            height: \"14\",\n            x: \"2\",\n            y: \"3\",\n            rx: \"2\",\n            key: \"48i651\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"8\",\n            x2: \"16\",\n            y1: \"21\",\n            y2: \"21\",\n            key: \"1svkeh\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"17\",\n            y2: \"21\",\n            key: \"vw1qmm\"\n        }\n    ]\n]);\n //# sourceMappingURL=monitor.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/refresh-cw.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RefreshCw; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst RefreshCw = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"RefreshCw\", [\n    [\n        \"path\",\n        {\n            d: \"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8\",\n            key: \"v9h5vc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 3v5h-5\",\n            key: \"1q7to0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16\",\n            key: \"3uifl3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 16H3v5\",\n            key: \"1cv678\"\n        }\n    ]\n]);\n //# sourceMappingURL=refresh-cw.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/x-circle.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ XCircle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst XCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"XCircle\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m15 9-6 6\",\n            key: \"1uzhvr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 9 6 6\",\n            key: \"z0biqf\"\n        }\n    ]\n]);\n //# sourceMappingURL=x-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AdminLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AdminLayout */ \"(app-pages-browser)/./components/AdminLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowUp,BarChart3,Bot,CreditCard,Eye,Monitor,RefreshCw,Server,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowUp,BarChart3,Bot,CreditCard,Eye,Monitor,RefreshCw,Server,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowUp,BarChart3,Bot,CreditCard,Eye,Monitor,RefreshCw,Server,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowUp,BarChart3,Bot,CreditCard,Eye,Monitor,RefreshCw,Server,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowUp,BarChart3,Bot,CreditCard,Eye,Monitor,RefreshCw,Server,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowUp,BarChart3,Bot,CreditCard,Eye,Monitor,RefreshCw,Server,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowUp,BarChart3,Bot,CreditCard,Eye,Monitor,RefreshCw,Server,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowUp,BarChart3,Bot,CreditCard,Eye,Monitor,RefreshCw,Server,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowUp,BarChart3,Bot,CreditCard,Eye,Monitor,RefreshCw,Server,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowUp,BarChart3,Bot,CreditCard,Eye,Monitor,RefreshCw,Server,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowUp,BarChart3,Bot,CreditCard,Eye,Monitor,RefreshCw,Server,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowUp,BarChart3,Bot,CreditCard,Eye,Monitor,RefreshCw,Server,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowUp,BarChart3,Bot,CreditCard,Eye,Monitor,RefreshCw,Server,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AdminPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [overview, setOverview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        systemHealth: {\n            status: \"healthy\",\n            uptime: 99.8,\n            lastCheck: new Date().toISOString()\n        },\n        quickStats: {\n            totalUsers: 1247,\n            activeToday: 156,\n            totalScans: 15634,\n            alertsCount: 3\n        },\n        recentAlerts: [\n            {\n                id: \"1\",\n                type: \"security\",\n                message: \"Multiple failed login attempts detected\",\n                severity: \"medium\",\n                timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString()\n            },\n            {\n                id: \"2\",\n                type: \"system\",\n                message: \"High API usage detected\",\n                severity: \"low\",\n                timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString()\n            },\n            {\n                id: \"3\",\n                type: \"user\",\n                message: \"New premium subscription activated\",\n                severity: \"low\",\n                timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString()\n            }\n        ],\n        systemServices: [\n            {\n                name: \"Database\",\n                status: \"online\",\n                uptime: 99.9,\n                lastCheck: new Date().toISOString()\n            },\n            {\n                name: \"API Gateway\",\n                status: \"online\",\n                uptime: 99.8,\n                lastCheck: new Date().toISOString()\n            },\n            {\n                name: \"Scanner Engine\",\n                status: \"online\",\n                uptime: 98.5,\n                lastCheck: new Date().toISOString()\n            },\n            {\n                name: \"OSINT Service\",\n                status: \"online\",\n                uptime: 99.2,\n                lastCheck: new Date().toISOString()\n            },\n            {\n                name: \"File Analyzer\",\n                status: \"maintenance\",\n                uptime: 95.1,\n                lastCheck: new Date().toISOString()\n            },\n            {\n                name: \"Bot Services\",\n                status: \"online\",\n                uptime: 97.8,\n                lastCheck: new Date().toISOString()\n            }\n        ]\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const adminModules = [\n        {\n            title: \"Dashboard\",\n            description: \"System monitoring and analytics\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"/admin/dashboard\",\n            color: \"blue\",\n            stats: \"\".concat(overview.quickStats.totalUsers, \" users\")\n        },\n        {\n            title: \"User Management\",\n            description: \"Manage users, roles, and permissions\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"/admin/users\",\n            color: \"green\",\n            stats: \"\".concat(overview.quickStats.activeToday, \" active today\")\n        },\n        {\n            title: \"System Settings\",\n            description: \"Configure system parameters\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: \"/admin/settings\",\n            color: \"purple\",\n            stats: \"Global config\"\n        },\n        {\n            title: \"Bot Management\",\n            description: \"WhatsApp & Telegram bot controls\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: \"/admin/bots\",\n            color: \"cyan\",\n            stats: \"2 bots active\"\n        },\n        {\n            title: \"Payment System\",\n            description: \"Subscription and billing management\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            href: \"/admin/payments\",\n            color: \"yellow\",\n            stats: \"Revenue tracking\"\n        },\n        {\n            title: \"System Monitor\",\n            description: \"Real-time system monitoring\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            href: \"/admin/monitoring\",\n            color: \"red\",\n            stats: \"\".concat(overview.systemHealth.uptime, \"% uptime\")\n        }\n    ];\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"online\":\n                return \"text-green-400\";\n            case \"offline\":\n                return \"text-red-400\";\n            case \"maintenance\":\n                return \"text-yellow-400\";\n            case \"healthy\":\n                return \"text-green-400\";\n            case \"warning\":\n                return \"text-yellow-400\";\n            case \"critical\":\n                return \"text-red-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"low\":\n                return \"text-blue-400\";\n            case \"medium\":\n                return \"text-yellow-400\";\n            case \"high\":\n                return \"text-orange-400\";\n            case \"critical\":\n                return \"text-red-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-400\",\n                                            children: \"\\uD83D\\uDD27 Admin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-pink\",\n                                            children: \"Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg\",\n                                    children: \"System overview and administrative controls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 mt-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 rounded-full \".concat(overview.systemHealth.status === \"healthy\" ? \"bg-green-400\" : overview.systemHealth.status === \"warning\" ? \"bg-yellow-400\" : \"bg-red-400\", \" animate-pulse\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium \".concat(getStatusColor(overview.systemHealth.status)),\n                                                    children: [\n                                                        \"System \",\n                                                        overview.systemHealth.status.charAt(0).toUpperCase() + overview.systemHealth.status.slice(1)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-blue-400 font-medium\",\n                                                    children: [\n                                                        overview.quickStats.totalUsers,\n                                                        \" Total Users\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-green-400 font-medium\",\n                                                    children: [\n                                                        overview.quickStats.activeToday,\n                                                        \" Active Today\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-5 h-5 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-yellow-400 font-medium\",\n                                                    children: [\n                                                        overview.quickStats.alertsCount,\n                                                        \" Alerts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 lg:mt-0 flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"System Uptime\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-mono text-green-400\",\n                                            children: [\n                                                overview.systemHealth.uptime,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setLoading(!loading),\n                                    disabled: loading,\n                                    className: \"btn-cyber-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        loading ? \"Refreshing...\" : \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Total Users\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: overview.quickStats.totalUsers.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Active Today\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: overview.quickStats.activeToday\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Total Scans\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: overview.quickStats.totalScans.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-cyber-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm\",\n                                                children: \"Active Alerts\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: overview.quickStats.alertsCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Administrative Modules\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: adminModules.map((module, index)=>{\n                                const Icon = module.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    onClick: ()=>router.push(module.href),\n                                    className: \"card-cyber hover:border-cyber-primary transition-all duration-300 cursor-pointer group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg bg-\".concat(module.color, \"-500/20\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-6 w-6 text-\".concat(module.color, \"-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 group-hover:text-cyber-primary transition-colors transform group-hover:-translate-y-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white mb-2\",\n                                            children: module.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm mb-3\",\n                                            children: module.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: module.stats\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white\",\n                                            children: \"System Services\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-6 w-6 text-cyber-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: overview.systemServices.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 bg-cyber-dark/50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 rounded-full \".concat(service.status === \"online\" ? \"bg-green-400\" : service.status === \"maintenance\" ? \"bg-yellow-400\" : \"bg-red-400\", \" animate-pulse\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: service.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: [\n                                                                        \"Uptime: \",\n                                                                        service.uptime,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium \".concat(getStatusColor(service.status)),\n                                                            children: service.status.charAt(0).toUpperCase() + service.status.slice(1)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-xs\",\n                                                            children: new Date(service.lastCheck).toLocaleTimeString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white\",\n                                            children: \"Recent Alerts\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6 text-yellow-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        overview.recentAlerts.map((alert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-cyber-dark/50 rounded-lg border-l-4 border-l-yellow-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded-full bg-\".concat(alert.type === \"security\" ? \"red\" : alert.type === \"system\" ? \"yellow\" : \"blue\", \"-500/20 text-\").concat(alert.type === \"security\" ? \"red\" : alert.type === \"system\" ? \"yellow\" : \"blue\", \"-400\"),\n                                                                            children: alert.type.toUpperCase()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 372,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs font-medium \".concat(getSeverityColor(alert.severity)),\n                                                                            children: alert.severity.toUpperCase()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 375,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-white text-sm\",\n                                                                    children: alert.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-xs mt-1\",\n                                                                    children: new Date(alert.timestamp).toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"text-gray-400 hover:text-white transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push(\"/admin/monitoring\"),\n                                                className: \"text-cyber-primary hover:text-cyber-secondary transition-colors text-sm\",\n                                                children: \"View All Alerts →\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-white mb-6\",\n                            children: \"Quick Actions\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/admin/users\"),\n                                    className: \"btn-cyber-secondary text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Manage Users\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/admin/settings\"),\n                                    className: \"btn-cyber-secondary text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"System Config\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/admin/monitoring\"),\n                                    className: \"btn-cyber-secondary text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Monitor System\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/admin/dashboard\"),\n                                    className: \"btn-cyber-primary text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowUp_BarChart3_Bot_CreditCard_Eye_Monitor_RefreshCw_Server_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Full Dashboard\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 201,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPage, \"3SOnn0SKfTmCfzRyJviwa4+DecM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/AdminLayout.tsx":
/*!************************************!*\
  !*** ./components/AdminLayout.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,Bell,Bot,CheckCircle,ChevronDown,ChevronRight,Crown,Database,FileText,Lock,LogOut,Menu,Server,Settings,Shield,User,Users,Wifi,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AdminLayout(param) {\n    let { children } = param;\n    _s();\n    const [isSidebarOpen, setIsSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"System\"\n    ]);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [systemStatus, setSystemStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"online\");\n    const [alerts, setAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Update time every minute\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    // Load user and check auth\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            try {\n                const token = localStorage.getItem(\"token\");\n                const storedUser = localStorage.getItem(\"user\");\n                if (!token || !storedUser) {\n                    router.push(\"/login\");\n                    return;\n                }\n                // Mock admin user data\n                const userData = {\n                    id: \"1\",\n                    username: \"AdminUser\",\n                    email: \"<EMAIL>\",\n                    fullName: \"System Administrator\",\n                    role: \"admin\",\n                    plan: \"Elite\"\n                };\n                setUser(userData);\n                // Mock system alerts\n                setAlerts([\n                    {\n                        id: \"1\",\n                        type: \"warning\",\n                        message: \"High CPU usage detected on server-02\",\n                        timestamp: \"2 minutes ago\"\n                    },\n                    {\n                        id: \"2\",\n                        type: \"info\",\n                        message: \"Database backup completed successfully\",\n                        timestamp: \"15 minutes ago\"\n                    }\n                ]);\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Auth check error:\", error);\n                router.push(\"/login\");\n            }\n        };\n        checkAuth();\n    }, [\n        router\n    ]);\n    const handleLogout = async ()=>{\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"refreshToken\");\n        localStorage.removeItem(\"user\");\n        router.push(\"/login\");\n    };\n    const toggleSidebar = ()=>{\n        setIsSidebarOpen(!isSidebarOpen);\n    };\n    const toggleExpanded = (itemName)=>{\n        setExpandedItems((prev)=>prev.includes(itemName) ? prev.filter((name)=>name !== itemName) : [\n                ...prev,\n                itemName\n            ]);\n    };\n    const isActive = (href)=>{\n        if (href === \"/admin/dashboard\") {\n            return pathname === \"/admin/dashboard\";\n        }\n        return pathname.startsWith(href);\n    };\n    const adminNavItems = [\n        {\n            name: \"Dashboard\",\n            href: \"/admin/dashboard\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            name: \"Users\",\n            href: \"/admin/users\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            badge: \"15.4K\"\n        },\n        {\n            name: \"Bots\",\n            href: \"/admin/bots\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            badge: \"4\"\n        },\n        {\n            name: \"Plans\",\n            href: \"/admin/plans\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            badge: \"New\"\n        },\n        {\n            name: \"System\",\n            href: \"#\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            children: [\n                {\n                    name: \"Monitoring\",\n                    href: \"/admin/monitoring\",\n                    icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                },\n                {\n                    name: \"Logs\",\n                    href: \"/admin/logs\",\n                    icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                },\n                {\n                    name: \"Security\",\n                    href: \"/admin/security\",\n                    icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                },\n                {\n                    name: \"Database\",\n                    href: \"/admin/database\",\n                    icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                }\n            ]\n        },\n        {\n            name: \"Settings\",\n            href: \"/admin/settings\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        }\n    ];\n    const getAlertIcon = (type)=>{\n        switch(type){\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-red-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 28\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 30\n                }, this);\n            case \"info\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 27\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getAlertColor = (type)=>{\n        switch(type){\n            case \"error\":\n                return \"bg-red-500/10 border-red-500/30\";\n            case \"warning\":\n                return \"bg-yellow-500/10 border-yellow-500/30\";\n            case \"info\":\n                return \"bg-blue-500/10 border-blue-500/30\";\n            default:\n                return \"bg-gray-500/10 border-gray-500/30\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-cyber-dark flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-cyber-primary font-medium\",\n                        children: \"Loading admin console...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-cyber-dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-cyber-dark/95 backdrop-blur-md border-b border-cyber-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16 px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleSidebar,\n                                        className: \"p-2 rounded-lg text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-colors lg:hidden\",\n                                        children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 32\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 60\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-8 w-8 text-cyber-primary animate-cyber-glow\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden sm:block\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold text-cyber-glow\",\n                                                        children: \"KodeXGuard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-cyber-secondary uppercase tracking-wider\",\n                                                        children: \"Admin Console\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 rounded-full \".concat(systemStatus === \"online\" ? \"bg-green-400\" : \"bg-red-400\", \" animate-pulse\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-300\",\n                                                children: \"System Online\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-cyber-primary\",\n                                                children: \"15,420\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" users online\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-cyber-secondary\",\n                                                children: \"99.8%\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" uptime\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block text-xs text-gray-400 font-mono\",\n                                        children: currentTime.toLocaleTimeString()\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 rounded-lg text-gray-300 hover:text-cyber-primary hover:bg-cyber-primary/10 transition-colors relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                alerts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-1 right-1 w-2 h-2 bg-cyber-secondary rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setIsProfileOpen(!isProfileOpen),\n                                                className: \"flex items-center space-x-3 p-2 rounded-lg hover:bg-cyber-primary/10 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 flex items-center justify-center text-sm font-bold text-black\",\n                                                        children: \"A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"hidden sm:block text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                children: user.username\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-yellow-400\",\n                                                                children: \"Administrator\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this),\n                                            isProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 top-full mt-2 w-64 bg-cyber-card border border-cyber-border rounded-lg shadow-xl z-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 border-b border-cyber-border\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 flex items-center justify-center text-lg font-bold text-black\",\n                                                                    children: \"A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-white\",\n                                                                            children: user.fullName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                            lineNumber: 302,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-400\",\n                                                                            children: user.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-yellow-400 font-medium\",\n                                                                            children: \"System Administrator\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/admin/settings\"),\n                                                                className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-white hover:bg-cyber-primary/10 rounded-lg transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-cyber-primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Admin Settings\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push(\"/dashboard\"),\n                                                                className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-white hover:bg-cyber-primary/10 rounded-lg transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-cyber-primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"User Dashboard\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleLogout,\n                                                                className: \"w-full flex items-center space-x-2 px-3 py-2 text-left text-red-400 hover:bg-red-500/10 rounded-lg transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Logout\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    alerts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-cyber-border bg-cyber-card/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 overflow-x-auto\",\n                                children: [\n                                    alerts.slice(0, 3).map((alert)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 px-3 py-1 rounded-lg border \".concat(getAlertColor(alert.type), \" whitespace-nowrap\"),\n                                            children: [\n                                                getAlertIcon(alert.type),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-white\",\n                                                    children: alert.message\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: alert.timestamp\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, alert.id, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 19\n                                        }, this)),\n                                    alerts.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-xs text-cyber-primary hover:text-cyber-secondary transition-colors\",\n                                        children: [\n                                            \"+\",\n                                            alerts.length - 3,\n                                            \" more alerts\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"fixed top-16 left-0 z-40 w-64 h-[calc(100vh-4rem)] bg-cyber-card border-r border-cyber-border transform transition-transform duration-300 ease-in-out \".concat(isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\", \" lg:translate-x-0 \").concat(alerts.length > 0 ? \"top-24\" : \"top-16\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 p-4 space-y-2 overflow-y-auto\",\n                            children: adminNavItems.map((item)=>{\n                                var _item_children, _item_children1;\n                                const Icon = item.icon;\n                                const hasChildren = item.children && item.children.length > 0;\n                                const isExpanded = expandedItems.includes(item.name);\n                                const itemIsActive = hasChildren ? (_item_children = item.children) === null || _item_children === void 0 ? void 0 : _item_children.some((child)=>isActive(child.href)) : isActive(item.href);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleExpanded(item.name),\n                                            className: \"w-full flex items-center justify-between px-3 py-2 rounded-lg font-medium transition-colors duration-200 \".concat(itemIsActive ? \"bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30\" : \"text-gray-300 hover:text-white hover:bg-cyber-primary/10\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-4 w-4 transition-transform duration-200 \".concat(isExpanded ? \"rotate-90\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>router.push(item.href),\n                                            className: \"w-full flex items-center space-x-3 px-3 py-2 rounded-lg font-medium transition-colors duration-200 \".concat(itemIsActive ? \"bg-cyber-primary/20 text-cyber-primary border border-cyber-primary/30\" : \"text-gray-300 hover:text-white hover:bg-cyber-primary/10\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 23\n                                                }, this),\n                                                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-auto bg-cyber-secondary/20 text-cyber-secondary px-2 py-0.5 rounded-full text-xs font-bold\",\n                                                    children: item.badge\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 21\n                                        }, this),\n                                        hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 mt-2 space-y-1\",\n                                            children: (_item_children1 = item.children) === null || _item_children1 === void 0 ? void 0 : _item_children1.map((child)=>{\n                                                const ChildIcon = child.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>router.push(child.href),\n                                                    className: \"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm transition-colors duration-200 \".concat(isActive(child.href) ? \"bg-cyber-primary/20 text-cyber-primary\" : \"text-gray-400 hover:text-white hover:bg-cyber-primary/10\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChildIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: child.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, child.name, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 27\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t border-cyber-border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400 mb-2\",\n                                                children: \"Admin Console\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full \".concat(systemStatus === \"online\" ? \"bg-green-400\" : \"bg-red-400\", \" animate-pulse\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-300 capitalize\",\n                                                        children: systemStatus\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-2 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-cyber-primary font-bold\",\n                                                        children: \"15.4K\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"Users\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-cyber-secondary font-bold\",\n                                                        children: \"99.8%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"Uptime\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this),\n            isSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-30 bg-black/50 lg:hidden\",\n                onClick: ()=>setIsSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                lineNumber: 468,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"transition-all duration-300 \".concat(alerts.length > 0 ? \"pt-24\" : \"pt-16\", \" \").concat(isSidebarOpen ? \"lg:ml-64\" : \"lg:ml-64\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-[calc(100vh-4rem)]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                            lineNumber: 477,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-cyber-card border-t border-cyber-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-6 py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"col-span-1 md:col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-cyber-primary animate-cyber-glow\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-cyber-primary opacity-20 blur-lg animate-cyber-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-bold text-cyber-glow\",\n                                                                    children: \"KodeXGuard Admin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-cyber-secondary uppercase tracking-wider\",\n                                                                    children: \"System Administration Console\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm mb-4\",\n                                                    children: \"Comprehensive admin console for managing users, monitoring system health, and configuring platform settings.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-bold text-white mb-4 uppercase tracking-wider\",\n                                                    children: \"System Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Database:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400\",\n                                                                    children: \"Online\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 512,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"API:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400\",\n                                                                    children: \"Healthy\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Storage:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 519,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-yellow-400\",\n                                                                    children: \"78%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Memory:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400\",\n                                                                    children: \"67%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-bold text-white mb-4 uppercase tracking-wider\",\n                                                    children: \"Quick Stats\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-2 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Active Users:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-cyber-primary\",\n                                                                    children: \"1,247\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Total Scans:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 538,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-cyber-secondary\",\n                                                                    children: \"89.4K\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Revenue:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-cyber-accent\",\n                                                                    children: \"$165K\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"Alerts:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-400\",\n                                                                    children: alerts.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 pt-6 border-t border-cyber-border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"\\xa9 2024 KodeXGuard Admin Console. All rights reserved.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 mt-4 md:mt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Admin Session: \",\n                                                            currentTime.toLocaleTimeString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_Bell_Bot_CheckCircle_ChevronDown_ChevronRight_Crown_Database_FileText_Lock_LogOut_Menu_Server_Settings_Shield_User_Users_Wifi_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-green-400\",\n                                                                children: \"Secure Connection\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n                lineNumber: 475,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\components\\\\AdminLayout.tsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminLayout, \"kIylqMPe4LGfPuV8NXnN2Vxucck=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/AdminLayout.tsx\n"));

/***/ })

});