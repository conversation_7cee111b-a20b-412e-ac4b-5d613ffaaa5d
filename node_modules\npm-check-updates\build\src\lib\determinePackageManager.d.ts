import { Options } from '../types/Options';
import { PackageManagerName } from '../types/PackageManagerName';
/**
 * If the packageManager option was not provided, look at the lockfiles to
 * determine which package manager is being used.
 */
declare const determinePackageManager: (options: Options, readdir?: (_path: string) => Promise<string[]>) => Promise<PackageManagerName>;
export default determinePackageManager;
