{"version": 3, "file": "yarn.js", "sourceRoot": "", "sources": ["../../../src/package-managers/yarn.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gEAAkC;AAClC,2DAA4B;AAC5B,sDAA0B;AAC1B,0DAAiC;AACjC,yDAAgC;AAChC,4CAAmB;AACnB,gDAAuB;AACvB,gEAAgC;AAChC,2DAAkC;AAClC,uEAA8C;AAC9C,kDAA8C;AAC9C,4CAAsC;AAQtC,2CAA4B;AAoB5B,yDAAyD;AACzD,MAAM,WAAW,GAAG,CAAC,CAAS,EAAE,IAA+B,EAAU,EAAE,CACzE,CAAC,CAAC,OAAO,CACP,kCAAkC,EAClC,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAChG,CAAA;AAEH,+GAA+G;AAClG,QAAA,oBAAoB,GAAG,IAAA,eAAK,EAAC,CAAC,SAAkC,EAAE,GAAW,EAAE,YAAsB,EAAE,EAAE;IACpH,IAAI,YAAY,CAAC,YAAY,EAAE;QAC7B,+HAA+H;QAC/H,MAAM,cAAc,GAAG,YAAY,CAAC,iBAAiB,IAAK,SAAS,CAAC,IAAI,GAAG,WAAW,CAAwB,CAAA;QAC9G,4CAA4C;QAC5C,2CAA2C;QAC3C,IAAI,cAAc,EAAE;YAClB,IAAI,qBAAqB,GAAG,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;YAElE,IAAI,qBAAqB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACvC,qBAAqB,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;aAC3D;YAED,OAAO;gBACL,CAAC,GAAG,qBAAqB,cAAc,CAAC,EAAE,WAAW,CAAC,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC;aAC9F,CAAA;SACF;KACF;IAED,OAAO,IAAI,CAAA;AACb,CAAC,CAAC,CAAA;AAEF,4GAA4G;AAC5G,MAAM,mBAAmB,GAAG,CAAC,GAAW,EAAE,YAAsB,EAA6B,EAAE,CAC7F,YAAY,CAAC,iBAAiB;IAC5B,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,WAAW,CAAC,EAAE,WAAW,CAAC,YAAY,CAAC,iBAAiB,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE;IACpF,CAAC,CAAC,IAAI,CAAA;AAEV;;;;;;;GAOG;AACI,KAAK,UAAU,sBAAsB,CAC1C,OAAgB,EAChB,UAAgD,kBAAE,CAAC,OAAO;;IAE1D,IAAI,OAAO,CAAC,MAAM;QAAE,OAAO,SAAS,CAAA;IAEpC,MAAM,aAAa,GAAG,MAAA,CAAC,MAAM,IAAA,sBAAY,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC,0CAAE,aAAa,CAAA;IAC3E,IAAI,CAAC,aAAa;QAAE,OAAO,SAAS,CAAA;IAEpC,OAAO,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,CAAA;AAChD,CAAC;AAVD,wDAUC;AAED,4HAA4H;AAC5H,2HAA2H;AAC3H,gEAAgE;AAChE,MAAM,iBAAiB,GAAG,IAAA,sBAAO,EAAC,KAAK,EAAE,OAAgB,EAAsB,EAAE;IAC/E,MAAM,eAAe,GAAG,MAAM,sBAAsB,CAAC,OAAO,CAAC,CAAA;IAC7D,MAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,YAAE,CAAC,OAAO,EAAE,EAAE,aAAa,CAAC,CAAA;IAC7D,MAAM,iBAAiB,GAAG,OAAO,eAAe,KAAK,QAAQ,IAAI,CAAC,MAAM,IAAA,gBAAM,EAAC,eAAe,CAAC,CAAC,CAAA;IAChG,MAAM,gBAAgB,GAAG,MAAM,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAA;IACrD,MAAM,WAAW,GAAG,iBAAiB,CAAC,CAAC,CAAC,MAAM,kBAAE,CAAC,QAAQ,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;IACxF,MAAM,UAAU,GAAG,gBAAgB,CAAC,CAAC,CAAC,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;IACrF,MAAM,eAAe,GAAe,iBAAI,CAAC,IAAI,CAAC,WAAW,CAAe,CAAA;IACxE,MAAM,cAAc,GAAe,iBAAI,CAAC,IAAI,CAAC,UAAU,CAAe,CAAA;IAEtE,IAAI,SAAS,GAA4B;QACvC,GAAG,IAAA,uBAAU,EAAC,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,SAAS,KAAI,EAAE,EAAE,mBAAmB,CAAC;QACnE,GAAG,IAAA,uBAAU,EAAC,CAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,SAAS,KAAI,EAAE,EAAE,mBAAmB,CAAC;KACrE,CAAA;IAED,6FAA6F;IAC7F,SAAS,GAAG;QACV,GAAG,SAAS;QACZ,GAAG,IAAA,uBAAU,EAAC,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,SAAS,KAAI,EAAE,EAAE,IAAA,4BAAoB,EAAC,SAAS,CAAC,CAAC;QAC/E,GAAG,IAAA,uBAAU,EAAC,CAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,SAAS,KAAI,EAAE,EAAE,IAAA,4BAAoB,EAAC,SAAS,CAAC,CAAC;KACjF,CAAA;IAED,0EAA0E;IAE1E,IAAI,iBAAiB,EAAE;QACrB,IAAA,eAAK,EAAC,OAAO,EAAE,gCAAgC,eAAe,GAAG,EAAE,SAAS,CAAC,CAAA;QAC7E,IAAA,eAAK,EAAC,OAAO,EAAE,eAAe,EAAE,SAAS,CAAC,CAAA;KAC3C;IACD,IAAI,gBAAgB,EAAE;QACpB,IAAA,eAAK,EAAC,OAAO,EAAE,+BAA+B,cAAc,GAAG,EAAE,SAAS,CAAC,CAAA;QAC3E,IAAA,eAAK,EAAC,OAAO,EAAE,eAAe,EAAE,SAAS,CAAC,CAAA;KAC3C;IAED,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QAC1B,IAAA,eAAK,EAAC,OAAO,EAAE,qCAAqC,EAAE,SAAS,CAAC,CAAA;QAChE,IAAA,eAAK,EAAC,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;KACrC;IAED,OAAO,SAAS,CAAA;AAClB,CAAC,CAAC,CAAA;AAEF;;;;;;;GAOG;AACH,SAAS,cAAc,CAAC,MAAc;IACpC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,YAAY,GAAqB,EAAE,CAAA;QAEzC,MAAM,MAAM,GAAG,mBAAS,CAAC,KAAK,EAAE,CAAA;QAEhC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE;YACpB,uBAAuB;YACvB,6GAA6G;YAC7G,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;gBAChD,0FAA0F;gBAC1F,MAAM,CAAC,EAAE,OAAO,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,CAAA;gBAEnE,YAAY,CAAC,OAAO,CAAC,GAAG;oBACtB,OAAO,EAAE,UAAU;oBACnB,IAAI,EAAE,OAAO;iBACd,CAAA;aACF;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE;gBAC7B,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;aAC1B;QACH,CAAC,CAAC,CAAA;QAEF,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACpB,OAAO,CAAC,EAAE,YAAY,EAAE,CAAC,CAAA;QAC3B,CAAC,CAAC,CAAA;QAEF,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;QAE1B,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QAEpB,MAAM,CAAC,GAAG,EAAE,CAAA;IACd,CAAC,CAAC,CAAA;AACJ,CAAC;AAED;;;;;;;GAOG;AACH,KAAK,UAAU,SAAS,CACtB,IAAuB,EACvB,cAA0B,EAAE,EAC5B,YAA2B;IAE3B,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAA;IAE9D,MAAM,QAAQ,GAAG;QACf,GAAG,CAAC,WAAW,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACxD,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACjE,WAAW;QACX,QAAQ;QACR,eAAe;QACf,oFAAoF;QACpF,gEAAgE;QAChE,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;KACzC,CAAA;IAED,OAAO,IAAA,sBAAK,EAAC,GAAG,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAA;AAC3C,CAAC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,aAAa,CAAC,OAAgB;IAClD,IAAI,OAAO,CAAC,MAAM,EAAE;QAClB,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;KACvC;IAED,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAA;IAE9D,MAAM,MAAM,GAAG,MAAM,IAAA,sBAAK,EAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAChD,wCAAwC;QACxC,+CAA+C;QAC/C,+DAA+D;SAC9D,KAAK,CAAC,GAAG,EAAE;QACV,WAAW;IACb,CAAC,CAAC,CAAA;IAEJ,2DAA2D;IAC3D,+DAA+D;IAE/D,OAAO,OAAO,CAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;QACvD,CAAC,CAAC,YAAY;QACd,CAAC,CAAC,wDAAwD;YAC1D,0CAA0C;YAC1C,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM;gBACrE,CAAC,CAAC,MAAM;oBACN,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE;oBACf,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,sBAAsB;gBACrD,CAAC,CAAC,IAAI,CAAA;AACV,CAAC;AA3BD,sCA2BC;AAED;;;;;;;;GAQG;AACI,MAAM,IAAI,GAAG,KAAK,EAAE,UAAmB,EAAE,EAAE,YAA2B,EAAsC,EAAE;IACnH,MAAM,SAAS,GAAW,MAAM,SAAS,CAAC,MAAM,EAAE,OAAwB,EAAE;QAC1E,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5C,GAAG,YAAY;KAChB,CAAC,CAAA;IACF,MAAM,IAAI,GAAuC,MAAM,cAAc,CAAC,SAAS,CAAC,CAAA;IAChF,MAAM,SAAS,GAA8B,IAAA,uBAAU,EACrD,IAAI,CAAC,YAAY,EACjB,CAAC,IAAI,EAAE,IAAI,EAAyC,EAAE;;QAAC,OAAA,CAAC;YACtD,qDAAqD;YACrD,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,OAAO,KAAI,MAAA,IAAI,CAAC,QAAQ,0CAAE,OAAO,CAAA;SAC/C,CAAC,CAAA;KAAA,CACH,CAAA;IACD,OAAO,SAAS,CAAA;AAClB,CAAC,CAAA;AAdY,QAAA,IAAI,QAchB;AAED,8DAA8D;AAC9D,MAAM,qBAAqB,GACzB,CAAC,UAAsB,EAAc,EAAE,CACvC,KAAK,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,GAAG,EAAE,EAAE,EAAE,CAClD,UAAU,CAAC,WAAW,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAA;AAEzE,QAAA,OAAO,GAAG,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;AAC5C,QAAA,QAAQ,GAAG,qBAAqB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;AAC9C,QAAA,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;AAC1C,QAAA,KAAK,GAAG,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AACxC,QAAA,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;AAC1C,QAAA,KAAK,GAAG,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AACxC,QAAA,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;AAEvD,kBAAe,SAAS,CAAA"}