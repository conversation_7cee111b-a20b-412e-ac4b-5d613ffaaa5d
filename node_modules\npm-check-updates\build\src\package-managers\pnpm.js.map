{"version": 3, "file": "pnpm.js", "sourceRoot": "", "sources": ["../../../src/package-managers/pnpm.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gEAAkC;AAClC,sDAA4B;AAC5B,2DAA4B;AAC5B,8CAAqB;AACrB,gDAAuB;AACvB,gEAAgC;AAChC,mEAA0C;AAC1C,4CAAsC;AAQtC,2CAA4B;AAa5B,0EAA0E;AAC1E,MAAM,0BAA0B,GAAG,IAAA,sBAAO,EAAC,KAAK,EAAE,OAAgB,EAAsB,EAAE;IACxF,MAAM,iBAAiB,GAAG,MAAM,IAAA,iBAAM,EAAC,qBAAqB,CAAC,CAAA;IAC7D,IAAI,CAAC,iBAAiB;QAAE,OAAO,EAAE,CAAA;IAEjC,MAAM,gBAAgB,GAAG,cAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;IACxD,MAAM,uBAAuB,GAAG,cAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAA;IAErE,IAAI,mBAAmB,CAAA;IACvB,IAAI;QACF,mBAAmB,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAA;KAC1E;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,EAAE,CAAA;KACV;IAED,IAAA,eAAK,EAAC,OAAO,EAAE,oCAAoC,uBAAuB,GAAG,EAAE,SAAS,CAAC,CAAA;IAEzF,MAAM,MAAM,GAAG,GAAG,CAAC,kBAAkB,CAAC,aAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE,gBAAgB,CAAC,CAAA;IAEvF,IAAA,eAAK,EAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAA;IAEjC,OAAO,MAAM,CAAA;AACf,CAAC,CAAC,CAAA;AAEF;;;;;;;GAOG;AACH,MAAM,SAAS,GAAG,KAAK,EACrB,IAAuB,EACvB,aAAyB,EAAE,EAC3B,YAA2B,EACV,EAAE;IACnB,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAA;IAE9D,MAAM,QAAQ,GAAG;QACf,GAAG,CAAC,UAAU,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QACrD,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACxC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;KAC9D,CAAA;IAED,OAAO,IAAA,sBAAK,EAAC,GAAG,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAA;AAC3C,CAAC,CAAA;AAED,kDAAkD;AAC3C,MAAM,IAAI,GAAG,KAAK,EAAE,UAAmB,EAAE,EAAsC,EAAE;IACtF,wCAAwC;IACxC,0FAA0F;IAC1F,IAAI,CAAC,OAAO,CAAC,MAAM;QAAE,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IAE7C,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAA;IAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,IAAA,sBAAK,EAAC,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAa,CAAA;IAC/E,MAAM,IAAI,GAAG,IAAA,oBAAU,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;QAC5E,CAAC,IAAI,CAAC,EAAE,OAAO;KAChB,CAAC,CAAC,CAAA;IACH,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAXY,QAAA,IAAI,QAWhB;AAED,6GAA6G;AAC7G,MAAM,sBAAsB,GAC1B,CAAC,UAAsB,EAAc,EAAE,CACvC,KAAK,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,GAAG,EAAE,EAAE,EAAE,CAClD,UAAU,CAAC,WAAW,EAAE,cAAc,EAAE,OAAO,EAAE,EAAE,EAAE,MAAM,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAA;AAEtF,QAAA,OAAO,GAAG,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;AAC7C,QAAA,QAAQ,GAAG,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;AAC/C,QAAA,MAAM,GAAG,sBAAsB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;AAC3C,QAAA,KAAK,GAAG,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AACzC,QAAA,MAAM,GAAG,sBAAsB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;AAC3C,QAAA,KAAK,GAAG,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AACzC,QAAA,MAAM,GAAG,sBAAsB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;AAExD,6BAAgF;AAAvE,oGAAA,aAAa,OAAA;AAAE,0GAAA,mBAAmB,OAAA;AAAE,2GAAA,oBAAoB,OAAA;AAEjE,kBAAe,SAAS,CAAA"}