{"version": 3, "sources": ["../../../../src/server/lib/incremental-cache/fetch-cache.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "rateLimitedUntil", "memoryCache", "CACHE_TAGS_HEADER", "CACHE_HEADERS_HEADER", "CACHE_STATE_HEADER", "CACHE_REVALIDATE_HEADER", "CACHE_FETCH_URL_HEADER", "CACHE_CONTROL_VALUE_HEADER", "DEBUG", "Boolean", "process", "env", "NEXT_PRIVATE_DEBUG_CACHE", "fetchRetryWithTimeout", "url", "init", "retryIndex", "controller", "AbortController", "timeout", "setTimeout", "abort", "fetch", "signal", "catch", "err", "console", "log", "finally", "clearTimeout", "hasMatchingTags", "arr1", "arr2", "length", "set1", "Set", "set2", "size", "tag", "has", "isAvailable", "ctx", "_requestHeaders", "SUSPENSE_CACHE_URL", "constructor", "headers", "newHeaders", "JSON", "parse", "k", "scHost", "sc<PERSON><PERSON><PERSON><PERSON>", "SUSPENSE_CACHE_BASEPATH", "SUSPENSE_CACHE_AUTH_TOKEN", "scProto", "SUSPENSE_CACHE_PROTO", "cacheEndpoint", "maxMemoryCacheSize", "L<PERSON><PERSON><PERSON>", "max", "value", "kind", "stringify", "props", "Error", "data", "body", "html", "pageData", "resetRequestCache", "reset", "revalidateTag", "args", "tags", "Date", "now", "i", "Math", "ceil", "currentTags", "slice", "res", "map", "encodeURIComponent", "join", "method", "next", "internal", "status", "retryAfter", "get", "parseInt", "ok", "warn", "key", "softTags", "kindHint", "fetchIdx", "fetchUrl", "hasFetchKindAndMatchingTags", "start", "fetchParams", "fetchType", "NEXT_CACHE_SOFT_TAGS_HEADER", "error", "text", "cached", "json", "includes", "push", "cacheState", "age", "lastModified", "CACHE_ONE_YEAR", "Object", "keys", "set", "fetchCache", "revalidate", "toString", "undefined"], "mappings": ";;;;+BAyDA;;;eAAqBA;;;iEAtDA;2BAId;;;;;;AAEP,IAAIC,mBAAmB;AACvB,IAAIC;AASJ,MAAMC,oBAAoB;AAC1B,MAAMC,uBAAuB;AAC7B,MAAMC,qBAAqB;AAC3B,MAAMC,0BAA0B;AAChC,MAAMC,yBAAyB;AAC/B,MAAMC,6BAA6B;AAEnC,MAAMC,QAAQC,QAAQC,QAAQC,GAAG,CAACC,wBAAwB;AAE1D,eAAeC,sBACbC,GAAgC,EAChCC,IAAiC,EACjCC,aAAa,CAAC;IAEd,MAAMC,aAAa,IAAIC;IACvB,MAAMC,UAAUC,WAAW;QACzBH,WAAWI,KAAK;IAClB,GAAG;IAEH,OAAOC,MAAMR,KAAK;QAChB,GAAIC,QAAQ,CAAC,CAAC;QACdQ,QAAQN,WAAWM,MAAM;IAC3B,GACGC,KAAK,CAAC,CAACC;QACN,IAAIT,eAAe,GAAG;YACpB,MAAMS;QACR,OAAO;YACL,IAAIjB,OAAO;gBACTkB,QAAQC,GAAG,CAAC,CAAC,iBAAiB,EAAEb,IAAI,OAAO,EAAEE,WAAW,CAAC;YAC3D;YACA,OAAOH,sBAAsBC,KAAKC,MAAMC,aAAa;QACvD;IACF,GACCY,OAAO,CAAC;QACPC,aAAaV;IACf;AACJ;AAEe,MAAMpB;IAIX+B,gBAAgBC,IAAc,EAAEC,IAAc,EAAE;QACtD,IAAID,KAAKE,MAAM,KAAKD,KAAKC,MAAM,EAAE,OAAO;QAExC,MAAMC,OAAO,IAAIC,IAAIJ;QACrB,MAAMK,OAAO,IAAID,IAAIH;QAErB,IAAIE,KAAKG,IAAI,KAAKD,KAAKC,IAAI,EAAE,OAAO;QAEpC,KAAK,IAAIC,OAAOJ,KAAM;YACpB,IAAI,CAACE,KAAKG,GAAG,CAACD,MAAM,OAAO;QAC7B;QAEA,OAAO;IACT;IAEA,OAAOE,YAAYC,GAElB,EAAE;QACD,OAAO,CAAC,CACNA,CAAAA,IAAIC,eAAe,CAAC,mBAAmB,IAAIhC,QAAQC,GAAG,CAACgC,kBAAkB,AAAD;IAE5E;IAEAC,YAAYH,GAAwB,CAAE;QACpC,IAAI,CAACI,OAAO,GAAG,CAAC;QAChB,IAAI,CAACA,OAAO,CAAC,eAAe,GAAG;QAE/B,IAAI1C,wBAAwBsC,IAAIC,eAAe,EAAE;YAC/C,MAAMI,aAAaC,KAAKC,KAAK,CAC3BP,IAAIC,eAAe,CAACvC,qBAAqB;YAE3C,IAAK,MAAM8C,KAAKH,WAAY;gBAC1B,IAAI,CAACD,OAAO,CAACI,EAAE,GAAGH,UAAU,CAACG,EAAE;YACjC;YACA,OAAOR,IAAIC,eAAe,CAACvC,qBAAqB;QAClD;QACA,MAAM+C,SACJT,IAAIC,eAAe,CAAC,mBAAmB,IAAIhC,QAAQC,GAAG,CAACgC,kBAAkB;QAE3E,MAAMQ,aACJV,IAAIC,eAAe,CAAC,uBAAuB,IAC3ChC,QAAQC,GAAG,CAACyC,uBAAuB;QAErC,IAAI1C,QAAQC,GAAG,CAAC0C,yBAAyB,EAAE;YACzC,IAAI,CAACR,OAAO,CACV,gBACD,GAAG,CAAC,OAAO,EAAEnC,QAAQC,GAAG,CAAC0C,yBAAyB,CAAC,CAAC;QACvD;QAEA,IAAIH,QAAQ;YACV,MAAMI,UAAU5C,QAAQC,GAAG,CAAC4C,oBAAoB,IAAI;YACpD,IAAI,CAACC,aAAa,GAAG,CAAC,EAAEF,QAAQ,GAAG,EAAEJ,OAAO,EAAEC,cAAc,GAAG,CAAC;YAChE,IAAI3C,OAAO;gBACTkB,QAAQC,GAAG,CAAC,wBAAwB,IAAI,CAAC6B,aAAa;YACxD;QACF,OAAO,IAAIhD,OAAO;YAChBkB,QAAQC,GAAG,CAAC;QACd;QAEA,IAAIc,IAAIgB,kBAAkB,EAAE;YAC1B,IAAI,CAACxD,aAAa;gBAChB,IAAIO,OAAO;oBACTkB,QAAQC,GAAG,CAAC;gBACd;gBAEA1B,cAAc,IAAIyD,iBAAQ,CAAC;oBACzBC,KAAKlB,IAAIgB,kBAAkB;oBAC3BxB,QAAO,EAAE2B,KAAK,EAAE;4BAeXb;wBAdH,IAAI,CAACa,OAAO;4BACV,OAAO;wBACT,OAAO,IAAIA,MAAMC,IAAI,KAAK,YAAY;4BACpC,OAAOd,KAAKe,SAAS,CAACF,MAAMG,KAAK,EAAE9B,MAAM;wBAC3C,OAAO,IAAI2B,MAAMC,IAAI,KAAK,SAAS;4BACjC,MAAM,IAAIG,MAAM;wBAClB,OAAO,IAAIJ,MAAMC,IAAI,KAAK,SAAS;4BACjC,OAAOd,KAAKe,SAAS,CAACF,MAAMK,IAAI,IAAI,IAAIhC,MAAM;wBAChD,OAAO,IAAI2B,MAAMC,IAAI,KAAK,SAAS;4BACjC,OAAOD,MAAMM,IAAI,CAACjC,MAAM;wBAC1B;wBACA,wCAAwC;wBACxC,OACE2B,MAAMO,IAAI,CAAClC,MAAM,GAChBc,CAAAA,EAAAA,kBAAAA,KAAKe,SAAS,CAACF,MAAMC,IAAI,KAAK,UAAUD,MAAMQ,QAAQ,sBAAtDrB,gBACGd,MAAM,KAAI,CAAA;oBAElB;gBACF;YACF;QACF,OAAO;YACL,IAAIzB,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;QACF;IACF;IAEO0C,oBAA0B;QAC/BpE,+BAAAA,YAAaqE,KAAK;IACpB;IAEA,MAAaC,cACX,GAAGC,IAA+C,EAClD;QACA,IAAI,CAACC,KAAK,GAAGD;QACbC,OAAO,OAAOA,SAAS,WAAW;YAACA;SAAK,GAAGA;QAC3C,IAAIjE,OAAO;YACTkB,QAAQC,GAAG,CAAC,iBAAiB8C;QAC/B;QAEA,IAAI,CAACA,KAAKxC,MAAM,EAAE;QAElB,IAAIyC,KAAKC,GAAG,KAAK3E,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC,iBAAiB3B;YAC/B;YACA;QACF;QAEA,IAAK,IAAI4E,IAAI,GAAGA,IAAIC,KAAKC,IAAI,CAACL,KAAKxC,MAAM,GAAG,KAAK2C,IAAK;YACpD,MAAMG,cAAcN,KAAKO,KAAK,CAACJ,IAAI,IAAIA,IAAI,KAAK;YAChD,IAAI;gBACF,MAAMK,MAAM,MAAMpE,sBAChB,CAAC,EAAE,IAAI,CAAC2C,aAAa,CAAC,mCAAmC,EAAEuB,YACxDG,GAAG,CAAC,CAAC5C,MAAQ6C,mBAAmB7C,MAChC8C,IAAI,CAAC,KAAK,CAAC,EACd;oBACEC,QAAQ;oBACRxC,SAAS,IAAI,CAACA,OAAO;oBACrB,sCAAsC;oBACtCyC,MAAM;wBAAEC,UAAU;oBAAK;gBACzB;gBAGF,IAAIN,IAAIO,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaR,IAAIpC,OAAO,CAAC6C,GAAG,CAAC,kBAAkB;oBACrD1F,mBAAmB0E,KAAKC,GAAG,KAAKgB,SAASF;gBAC3C;gBAEA,IAAI,CAACR,IAAIW,EAAE,EAAE;oBACX,MAAM,IAAI5B,MAAM,CAAC,2BAA2B,EAAEiB,IAAIO,MAAM,CAAC,CAAC,CAAC;gBAC7D;YACF,EAAE,OAAO/D,KAAK;gBACZC,QAAQmE,IAAI,CAAC,CAAC,wBAAwB,CAAC,EAAEd,aAAatD;YACxD;QACF;IACF;IAEA,MAAaiE,IAAI,GAAGlB,IAAqC,EAAE;YAqBvDP;QApBF,MAAM,CAAC6B,KAAKrD,MAAM,CAAC,CAAC,CAAC,GAAG+B;QACxB,MAAM,EAAEC,IAAI,EAAEsB,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE,GAAGzD;QAEzD,IAAIuD,aAAa,SAAS;YACxB,OAAO;QACT;QAEA,IAAItB,KAAKC,GAAG,KAAK3E,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;YACA,OAAO;QACT;QAEA,qDAAqD;QACrD,qDAAqD;QACrD,yBAAyB;QACzB,IAAIsC,OAAOhE,+BAAAA,YAAayF,GAAG,CAACI;QAE5B,MAAMK,8BACJlC,CAAAA,yBAAAA,cAAAA,KAAML,KAAK,qBAAXK,YAAaJ,IAAI,MAAK,WACtB,IAAI,CAAC/B,eAAe,CAAC2C,QAAQ,EAAE,EAAER,KAAKL,KAAK,CAACa,IAAI,IAAI,EAAE;QAExD,8DAA8D;QAC9D,gDAAgD;QAChD,IAAI,IAAI,CAACjB,aAAa,IAAK,CAAA,CAACS,QAAQ,CAACkC,2BAA0B,GAAI;YACjE,IAAI;gBACF,MAAMC,QAAQ1B,KAAKC,GAAG;gBACtB,MAAM0B,cAAoC;oBACxCd,UAAU;oBACVe,WAAW;oBACXJ,UAAUA;oBACVD;gBACF;gBACA,MAAMhB,MAAM,MAAM3D,MAChB,CAAC,EAAE,IAAI,CAACkC,aAAa,CAAC,mBAAmB,EAAEsC,IAAI,CAAC,EAChD;oBACET,QAAQ;oBACRxC,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACvC,uBAAuB,EAAE4F;wBAC1B,CAAChG,kBAAkB,EAAEuE,CAAAA,wBAAAA,KAAMW,IAAI,CAAC,SAAQ;wBACxC,CAACmB,sCAA2B,CAAC,EAAER,CAAAA,4BAAAA,SAAUX,IAAI,CAAC,SAAQ;oBACxD;oBACAE,MAAMe;gBACR;gBAGF,IAAIpB,IAAIO,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaR,IAAIpC,OAAO,CAAC6C,GAAG,CAAC,kBAAkB;oBACrD1F,mBAAmB0E,KAAKC,GAAG,KAAKgB,SAASF;gBAC3C;gBAEA,IAAIR,IAAIO,MAAM,KAAK,KAAK;oBACtB,IAAIhF,OAAO;wBACTkB,QAAQC,GAAG,CACT,CAAC,yBAAyB,EAAEmE,IAAI,YAAY,EAC1CpB,KAAKC,GAAG,KAAKyB,MACd,EAAE,CAAC;oBAER;oBACA,OAAO;gBACT;gBAEA,IAAI,CAACnB,IAAIW,EAAE,EAAE;oBACXlE,QAAQ8E,KAAK,CAAC,MAAMvB,IAAIwB,IAAI;oBAC5B,MAAM,IAAIzC,MAAM,CAAC,4BAA4B,EAAEiB,IAAIO,MAAM,CAAC,CAAC;gBAC7D;gBAEA,MAAMkB,SAAgC,MAAMzB,IAAI0B,IAAI;gBAEpD,IAAI,CAACD,UAAUA,OAAO7C,IAAI,KAAK,SAAS;oBACtCrD,SAASkB,QAAQC,GAAG,CAAC;wBAAE+E;oBAAO;oBAC9B,MAAM,IAAI1C,MAAM;gBAClB;gBAEA,oEAAoE;gBACpE,IAAI0C,OAAO7C,IAAI,KAAK,SAAS;oBAC3B6C,OAAOjC,IAAI,KAAK,EAAE;oBAClB,KAAK,MAAMnC,OAAOmC,QAAQ,EAAE,CAAE;wBAC5B,IAAI,CAACiC,OAAOjC,IAAI,CAACmC,QAAQ,CAACtE,MAAM;4BAC9BoE,OAAOjC,IAAI,CAACoC,IAAI,CAACvE;wBACnB;oBACF;gBACF;gBAEA,MAAMwE,aAAa7B,IAAIpC,OAAO,CAAC6C,GAAG,CAACtF;gBACnC,MAAM2G,MAAM9B,IAAIpC,OAAO,CAAC6C,GAAG,CAAC;gBAE5BzB,OAAO;oBACLL,OAAO8C;oBACP,qDAAqD;oBACrD,uCAAuC;oBACvCM,cACEF,eAAe,UACXpC,KAAKC,GAAG,KAAKsC,yBAAc,GAC3BvC,KAAKC,GAAG,KAAKgB,SAASoB,OAAO,KAAK,MAAM;gBAChD;gBAEA,IAAIvG,OAAO;oBACTkB,QAAQC,GAAG,CACT,CAAC,0BAA0B,EAAEmE,IAAI,YAAY,EAC3CpB,KAAKC,GAAG,KAAKyB,MACd,UAAU,EACTc,OAAOC,IAAI,CAACT,QAAQzE,MAAM,CAC3B,eAAe,EAAE6E,WAAW,OAAO,EAAErC,wBAAAA,KAAMW,IAAI,CAC9C,KACA,WAAW,EAAEW,4BAAAA,SAAUX,IAAI,CAAC,KAAK,CAAC;gBAExC;gBAEA,IAAInB,MAAM;oBACRhE,+BAAAA,YAAamH,GAAG,CAACtB,KAAK7B;gBACxB;YACF,EAAE,OAAOxC,KAAK;gBACZ,sCAAsC;gBACtC,IAAIjB,OAAO;oBACTkB,QAAQ8E,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAE/E;gBAClD;YACF;QACF;QAEA,OAAOwC,QAAQ;IACjB;IAEA,MAAamD,IAAI,GAAG5C,IAAqC,EAAE;QACzD,MAAM,CAACsB,KAAK7B,MAAMxB,IAAI,GAAG+B;QAEzB,MAAM,EAAE6C,UAAU,EAAEpB,QAAQ,EAAEC,QAAQ,EAAEzB,IAAI,EAAE,GAAGhC;QACjD,IAAI,CAAC4E,YAAY;QAEjB,IAAI3C,KAAKC,GAAG,KAAK3E,kBAAkB;YACjC,IAAIQ,OAAO;gBACTkB,QAAQC,GAAG,CAAC;YACd;YACA;QACF;QAEA1B,+BAAAA,YAAamH,GAAG,CAACtB,KAAK;YACpBlC,OAAOK;YACP+C,cAActC,KAAKC,GAAG;QACxB;QAEA,IAAI,IAAI,CAACnB,aAAa,EAAE;YACtB,IAAI;gBACF,MAAM4C,QAAQ1B,KAAKC,GAAG;gBACtB,IAAIV,SAAS,QAAQ,gBAAgBA,MAAM;oBACzC,IAAI,CAACpB,OAAO,CAACxC,wBAAwB,GAAG4D,KAAKqD,UAAU,CAACC,QAAQ;gBAClE;gBACA,IACE,CAAC,IAAI,CAAC1E,OAAO,CAACxC,wBAAwB,IACtC4D,SAAS,QACT,UAAUA,MACV;oBACA,IAAI,CAACpB,OAAO,CAACtC,2BAA2B,GACtC0D,KAAKA,IAAI,CAACpB,OAAO,CAAC,gBAAgB;gBACtC;gBACA,MAAMqB,OAAOnB,KAAKe,SAAS,CAAC;oBAC1B,GAAGG,IAAI;oBACP,yCAAyC;oBACzC,sBAAsB;oBACtBQ,MAAM+C;gBACR;gBAEA,IAAIhH,OAAO;oBACTkB,QAAQC,GAAG,CAAC,aAAamE;gBAC3B;gBACA,MAAMO,cAAoC;oBACxCd,UAAU;oBACVe,WAAW;oBACXJ;oBACAD;gBACF;gBACA,MAAMhB,MAAM,MAAM3D,MAChB,CAAC,EAAE,IAAI,CAACkC,aAAa,CAAC,mBAAmB,EAAEsC,IAAI,CAAC,EAChD;oBACET,QAAQ;oBACRxC,SAAS;wBACP,GAAG,IAAI,CAACA,OAAO;wBACf,CAACvC,uBAAuB,EAAE4F,YAAY;wBACtC,CAAChG,kBAAkB,EAAEuE,CAAAA,wBAAAA,KAAMW,IAAI,CAAC,SAAQ;oBAC1C;oBACAlB,MAAMA;oBACNoB,MAAMe;gBACR;gBAGF,IAAIpB,IAAIO,MAAM,KAAK,KAAK;oBACtB,MAAMC,aAAaR,IAAIpC,OAAO,CAAC6C,GAAG,CAAC,kBAAkB;oBACrD1F,mBAAmB0E,KAAKC,GAAG,KAAKgB,SAASF;gBAC3C;gBAEA,IAAI,CAACR,IAAIW,EAAE,EAAE;oBACXpF,SAASkB,QAAQC,GAAG,CAAC,MAAMsD,IAAIwB,IAAI;oBACnC,MAAM,IAAIzC,MAAM,CAAC,iBAAiB,EAAEiB,IAAIO,MAAM,CAAC,CAAC;gBAClD;gBAEA,IAAIhF,OAAO;oBACTkB,QAAQC,GAAG,CACT,CAAC,oCAAoC,EAAEmE,IAAI,YAAY,EACrDpB,KAAKC,GAAG,KAAKyB,MACd,UAAU,EAAElC,KAAKjC,MAAM,CAAC,CAAC;gBAE9B;YACF,EAAE,OAAOR,KAAK;gBACZ,+BAA+B;gBAC/B,IAAIjB,OAAO;oBACTkB,QAAQ8E,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE/E;gBAChD;YACF;QACF;QACA;IACF;AACF"}