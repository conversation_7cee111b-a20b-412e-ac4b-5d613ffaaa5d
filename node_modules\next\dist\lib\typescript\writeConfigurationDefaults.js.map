{"version": 3, "sources": ["../../../src/lib/typescript/writeConfigurationDefaults.ts"], "names": ["getRequiredConfiguration", "writeConfigurationDefaults", "getDesiredCompilerOptions", "ts", "tsOptions", "o", "lib", "suggested", "allowJs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strict", "semver", "lt", "version", "forceConsistentCasingInFileNames", "undefined", "noEmit", "gte", "incremental", "module", "parsedValue", "Module<PERSON>ind", "ESNext", "parsed<PERSON><PERSON>ues", "Preserve", "ES2020", "CommonJS", "AMD", "NodeNext", "Node16", "value", "reason", "esModuleInterop", "moduleResolution", "ModuleResolutionKind", "<PERSON><PERSON><PERSON>", "Node10", "NodeJs", "Node12", "filter", "val", "resolveJsonModule", "verbatimModuleSyntax", "isolatedModules", "jsx", "JsxEmit", "res", "desiredCompilerOptions", "optionKey", "Object", "keys", "ev", "tsConfigPath", "isFirstTimeSetup", "hasAppDir", "distDir", "hasPagesDir", "fs", "writeFile", "os", "EOL", "options", "raw", "rawConfig", "getTypeScriptConfiguration", "userTsConfigContent", "readFile", "encoding", "userTsConfig", "CommentJson", "parse", "compilerOptions", "suggestedActions", "requiredActions", "check", "push", "cyan", "bold", "includes", "_", "nextAppTypes", "include", "plugins", "Array", "isArray", "hasNextPlugin", "some", "name", "length", "Log", "info", "strict<PERSON>ull<PERSON>hecks", "exclude", "stringify", "for<PERSON>ach", "action", "white"], "mappings": ";;;;;;;;;;;;;;;IAoHgBA,wBAAwB;eAAxBA;;IAiBMC,0BAA0B;eAA1BA;;;oBArIS;4BACG;qEACL;+DACV;2DACJ;4CAE4B;6DACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarB,SAASC,0BACPC,EAA+B,EAC/BC,SAA2B;IAE3B,MAAMC,IAAiC;QACrC,qEAAqE;QACrE,gBAAgB;QAChBC,KAAK;YAAEC,WAAW;gBAAC;gBAAO;gBAAgB;aAAS;QAAC;QACpDC,SAAS;YAAED,WAAW;QAAK;QAC3BE,cAAc;YAAEF,WAAW;QAAK;QAChCG,QAAQ;YAAEH,WAAW;QAAM;QAC3B,GAAII,eAAM,CAACC,EAAE,CAACT,GAAGU,OAAO,EAAE,WACtB;YAAEC,kCAAkC;gBAAEP,WAAW;YAAK;QAAE,IACxDQ,SAAS;QACbC,QAAQ;YAAET,WAAW;QAAK;QAC1B,GAAII,eAAM,CAACM,GAAG,CAACd,GAAGU,OAAO,EAAE,WACvB;YAAEK,aAAa;gBAAEX,WAAW;YAAK;QAAE,IACnCQ,SAAS;QAEb,8DAA8D;QAC9D,4CAA4C;QAC5C,8EAA8E;QAC9EI,QAAQ;YACNC,aAAajB,GAAGkB,UAAU,CAACC,MAAM;YACjC,4BAA4B;YAC5BC,cAAc;gBACZZ,eAAM,CAACM,GAAG,CAACd,GAAGU,OAAO,EAAE,YAAY,AAACV,GAAGkB,UAAU,CAASG,QAAQ;gBAClErB,GAAGkB,UAAU,CAACI,MAAM;gBACpBtB,GAAGkB,UAAU,CAACC,MAAM;gBACpBnB,GAAGkB,UAAU,CAACK,QAAQ;gBACtBvB,GAAGkB,UAAU,CAACM,GAAG;gBACjBxB,GAAGkB,UAAU,CAACO,QAAQ;gBACtBzB,GAAGkB,UAAU,CAACQ,MAAM;aACrB;YACDC,OAAO;YACPC,QAAQ;QACV;QACA,4DAA4D;QAC5D,GAAIpB,eAAM,CAACM,GAAG,CAACd,GAAGU,OAAO,EAAE,YAC3BT,CAAAA,6BAAAA,UAAWe,MAAM,MAAK,AAAChB,GAAGkB,UAAU,CAASG,QAAQ,GACjD;QAMA,IACA;YACEQ,iBAAiB;gBACfF,OAAO;gBACPC,QAAQ;YACV;YACAE,kBAAkB;gBAChB,sDAAsD;gBACtDb,aACEjB,GAAG+B,oBAAoB,CAACC,OAAO,IAC/BhC,GAAG+B,oBAAoB,CAACN,QAAQ,IAChC,AAACzB,GAAG+B,oBAAoB,CAASE,MAAM,IACvCjC,GAAG+B,oBAAoB,CAACG,MAAM;gBAChC,4BAA4B;gBAC5Bd,cAAc;oBACXpB,GAAG+B,oBAAoB,CAASE,MAAM,IACrCjC,GAAG+B,oBAAoB,CAACG,MAAM;oBAChC,qDAAqD;oBACrD,kDAAkD;oBACjDlC,GAAG+B,oBAAoB,CAASI,MAAM;oBACvCnC,GAAG+B,oBAAoB,CAACL,MAAM;oBAC9B1B,GAAG+B,oBAAoB,CAACN,QAAQ;oBAChCzB,GAAG+B,oBAAoB,CAACC,OAAO;iBAChC,CAACI,MAAM,CAAC,CAACC,MAAQ,OAAOA,QAAQ;gBACjCV,OAAO;gBACPC,QAAQ;YACV;YACAU,mBAAmB;gBACjBX,OAAO;gBACPC,QAAQ;YACV;QACF,CAAC;QACL,GAAI3B,CAAAA,6BAAAA,UAAWsC,oBAAoB,MAAK,OACpC3B,YACA;YACE4B,iBAAiB;gBACfb,OAAO;gBACPC,QAAQ;YACV;QACF,CAAC;QACLa,KAAK;YACHxB,aAAajB,GAAG0C,OAAO,CAACrB,QAAQ;YAChCM,OAAO;YACPC,QAAQ;QACV;IACF;IAEA,OAAO1B;AACT;AAEO,SAASL,yBACdG,EAA+B;IAE/B,MAAM2C,MAAqD,CAAC;IAE5D,MAAMC,yBAAyB7C,0BAA0BC;IACzD,KAAK,MAAM6C,aAAaC,OAAOC,IAAI,CAACH,wBAAyB;QAC3D,MAAMI,KAAKJ,sBAAsB,CAACC,UAAU;QAC5C,IAAI,CAAE,CAAA,WAAWG,EAAC,GAAI;YACpB;QACF;QACAL,GAAG,CAACE,UAAU,GAAGG,GAAG/B,WAAW,IAAI+B,GAAGrB,KAAK;IAC7C;IAEA,OAAOgB;AACT;AAEO,eAAe7C,2BACpBE,EAA+B,EAC/BiD,YAAoB,EACpBC,gBAAyB,EACzBC,SAAkB,EAClBC,OAAe,EACfC,WAAoB;IAEpB,IAAIH,kBAAkB;QACpB,MAAMI,YAAE,CAACC,SAAS,CAACN,cAAc,OAAOO,WAAE,CAACC,GAAG;IAChD;IAEA,MAAM,EAAEC,SAASzD,SAAS,EAAE0D,KAAKC,SAAS,EAAE,GAC1C,MAAMC,IAAAA,sDAA0B,EAAC7D,IAAIiD,cAAc;IAErD,MAAMa,sBAAsB,MAAMR,YAAE,CAACS,QAAQ,CAACd,cAAc;QAC1De,UAAU;IACZ;IACA,MAAMC,eAAeC,aAAYC,KAAK,CAACL;IACvC,IAAIG,aAAaG,eAAe,IAAI,QAAQ,CAAE,CAAA,aAAaR,SAAQ,GAAI;QACrEK,aAAaG,eAAe,GAAG,CAAC;QAChClB,mBAAmB;IACrB;IAEA,MAAMN,yBAAyB7C,0BAA0BC,IAAIC;IAE7D,MAAMoE,mBAA6B,EAAE;IACrC,MAAMC,kBAA4B,EAAE;IACpC,KAAK,MAAMzB,aAAaC,OAAOC,IAAI,CAACH,wBAAyB;QAC3D,MAAM2B,QAAQ3B,sBAAsB,CAACC,UAAU;QAC/C,IAAI,eAAe0B,OAAO;YACxB,IAAI,CAAE1B,CAAAA,aAAa5C,SAAQ,GAAI;gBAC7B,IAAI,CAACgE,aAAaG,eAAe,EAAE;oBACjCH,aAAaG,eAAe,GAAG,CAAC;gBAClC;gBACAH,aAAaG,eAAe,CAACvB,UAAU,GAAG0B,MAAMnE,SAAS;gBACzDiE,iBAAiBG,IAAI,CACnBC,IAAAA,gBAAI,EAAC5B,aAAa,iBAAiB6B,IAAAA,gBAAI,EAACH,MAAMnE,SAAS;YAE3D;QACF,OAAO,IAAI,WAAWmE,OAAO;gBAIrBA;YAHN,MAAMvB,KAAK/C,SAAS,CAAC4C,UAAU;YAC/B,IACE,CAAE,CAAA,kBAAkB0B,SAChBA,sBAAAA,MAAMnD,YAAY,qBAAlBmD,oBAAoBI,QAAQ,CAAC3B,MAC7B,iBAAiBuB,QACjBA,MAAMtD,WAAW,KAAK+B,KACtBuB,MAAM5C,KAAK,KAAKqB,EAAC,GACrB;gBACA,IAAI,CAACiB,aAAaG,eAAe,EAAE;oBACjCH,aAAaG,eAAe,GAAG,CAAC;gBAClC;gBACAH,aAAaG,eAAe,CAACvB,UAAU,GAAG0B,MAAM5C,KAAK;gBACrD2C,gBAAgBE,IAAI,CAClBC,IAAAA,gBAAI,EAAC5B,aACH,iBACA6B,IAAAA,gBAAI,EAACH,MAAM5C,KAAK,IAChB,CAAC,EAAE,EAAE4C,MAAM3C,MAAM,CAAC,CAAC,CAAC;YAE1B;QACF,OAAO;YACL,6DAA6D;YAC7D,MAAMgD,IAAWL;QACnB;IACF;IAEA,MAAMM,eAAe,CAAC,EAAEzB,QAAQ,cAAc,CAAC;IAE/C,IAAI,CAAE,CAAA,aAAaQ,SAAQ,GAAI;QAC7BK,aAAaa,OAAO,GAAG3B,YACnB;YAAC;YAAiB0B;YAAc;YAAW;SAAW,GACtD;YAAC;YAAiB;YAAW;SAAW;QAC5CR,iBAAiBG,IAAI,CACnBC,IAAAA,gBAAI,EAAC,aACH,iBACAC,IAAAA,gBAAI,EACFvB,YACI,CAAC,mBAAmB,EAAE0B,aAAa,yBAAyB,CAAC,GAC7D,CAAC,wCAAwC,CAAC;IAGtD,OAAO,IAAI1B,aAAa,CAACS,UAAUkB,OAAO,CAACH,QAAQ,CAACE,eAAe;QACjEZ,aAAaa,OAAO,CAACN,IAAI,CAACK;QAC1BR,iBAAiBG,IAAI,CACnBC,IAAAA,gBAAI,EAAC,aAAa,yBAAyBC,IAAAA,gBAAI,EAAC,CAAC,CAAC,EAAEG,aAAa,CAAC,CAAC;IAEvE;IAEA,wCAAwC;IACxC,IAAI1B,WAAW;QACb,qEAAqE;QACrE,MAAM4B,UAAU;eACVC,MAAMC,OAAO,CAAChF,UAAU8E,OAAO,IAAI9E,UAAU8E,OAAO,GAAG,EAAE;eACzDd,aAAaG,eAAe,IAChCY,MAAMC,OAAO,CAAChB,aAAaG,eAAe,CAACW,OAAO,IAC9Cd,aAAaG,eAAe,CAACW,OAAO,GACpC,EAAE;SACP;QACD,MAAMG,gBAAgBH,QAAQI,IAAI,CAChC,CAAC,EAAEC,IAAI,EAAoB,GAAKA,SAAS;QAG3C,8EAA8E;QAC9E,0DAA0D;QAC1D,4EAA4E;QAC5E,IACE,CAACnB,aAAaG,eAAe,IAC5BW,QAAQM,MAAM,IACb,CAACH,iBACD,aAAatB,aACZ,CAAA,CAACA,UAAUQ,eAAe,IAAI,CAACR,UAAUQ,eAAe,CAACW,OAAO,AAAD,GAClE;YACAO,KAAIC,IAAI,CACN,CAAC,OAAO,EAAEb,IAAAA,gBAAI,EACZ,iBACA,yLAAyL,EAAED,IAAAA,gBAAI,EAC/L,mCACA,+JAA+J,CAAC;QAEtK,OAAO,IAAI,CAACS,eAAe;YACzB,IAAI,CAAE,CAAA,aAAajB,aAAaG,eAAe,AAAD,GAAI;gBAChDH,aAAaG,eAAe,CAACW,OAAO,GAAG,EAAE;YAC3C;YACAd,aAAaG,eAAe,CAACW,OAAO,CAACP,IAAI,CAAC;gBAAEY,MAAM;YAAO;YACzDf,iBAAiBG,IAAI,CACnBC,IAAAA,gBAAI,EAAC,aAAa,yBAAyBC,IAAAA,gBAAI,EAAC,CAAC,gBAAgB,CAAC;QAEtE;QAEA,yEAAyE;QACzE,yCAAyC;QACzC,IACErB,eACAF,aACAc,aAAaG,eAAe,IAC5B,CAACH,aAAaG,eAAe,CAAC7D,MAAM,IACpC,CAAE,CAAA,sBAAsB0D,aAAaG,eAAe,AAAD,GACnD;YACAH,aAAaG,eAAe,CAACoB,gBAAgB,GAAG;YAChDnB,iBAAiBG,IAAI,CACnBC,IAAAA,gBAAI,EAAC,sBAAsB,iBAAiBC,IAAAA,gBAAI,EAAC,CAAC,IAAI,CAAC;QAE3D;IACF;IAEA,IAAI,CAAE,CAAA,aAAad,SAAQ,GAAI;QAC7BK,aAAawB,OAAO,GAAG;YAAC;SAAe;QACvCpB,iBAAiBG,IAAI,CACnBC,IAAAA,gBAAI,EAAC,aAAa,iBAAiBC,IAAAA,gBAAI,EAAC,CAAC,gBAAgB,CAAC;IAE9D;IAEA,IAAIL,iBAAiBgB,MAAM,GAAG,KAAKf,gBAAgBe,MAAM,GAAG,GAAG;QAC7D;IACF;IAEA,MAAM/B,YAAE,CAACC,SAAS,CAChBN,cACAiB,aAAYwB,SAAS,CAACzB,cAAc,MAAM,KAAKT,WAAE,CAACC,GAAG;IAGvD6B,KAAIC,IAAI,CAAC;IACT,IAAIrC,kBAAkB;QACpBoC,KAAIC,IAAI,CACN,CAAC,qDAAqD,EAAEd,IAAAA,gBAAI,EAC1D,iBACA,cAAc,CAAC;QAEnB;IACF;IAEAa,KAAIC,IAAI,CACN,CAAC,6DAA6D,EAAEd,IAAAA,gBAAI,EAClE,iBACA,qCAAqC,EAAEA,IAAAA,gBAAI,EAAC,SAAS,YAAY,CAAC;IAEtE,IAAIJ,iBAAiBgB,MAAM,EAAE;QAC3BC,KAAIC,IAAI,CACN,CAAC,kDAAkD,EAAEd,IAAAA,gBAAI,EACvD,iBACA,eAAe,EAAEA,IAAAA,gBAAI,EAAC,kBAAkB,+BAA+B,CAAC;QAG5EJ,iBAAiBsB,OAAO,CAAC,CAACC,SAAWN,KAAIC,IAAI,CAAC,CAAC,IAAI,EAAEK,OAAO,CAAC;QAE7DN,KAAIC,IAAI,CAAC;IACX;IAEA,IAAIjB,gBAAgBe,MAAM,EAAE;QAC1BC,KAAIC,IAAI,CACN,CAAC,cAAc,EAAEM,IAAAA,iBAAK,EAAC,qBAAqB,mBAAmB,EAAEpB,IAAAA,gBAAI,EACnE,iBACA,GAAG,CAAC;QAGRH,gBAAgBqB,OAAO,CAAC,CAACC,SAAWN,KAAIC,IAAI,CAAC,CAAC,IAAI,EAAEK,OAAO,CAAC;QAE5DN,KAAIC,IAAI,CAAC;IACX;AACF"}