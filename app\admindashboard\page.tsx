'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import DashboardLayout from '@/components/DashboardLayout'
import { 
  Shield, 
  Users, 
  BarChart3, 
  Settings, 
  Database,
  Server,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Eye,
  Lock,
  Zap,
  Globe,
  FileText,
  Bug,
  Crown,
  Star,
  Award,
  Target,
  Search,
  Filter,
  Download,
  Upload,
  RefreshCw,
  Play,
  Pause,
  Square,
  Edit,
  Trash2,
  Plus,
  Minus,
  ArrowUp,
  ArrowDown,
  Calendar,
  MapPin,
  Wifi,
  WifiOff,
  Bot,
  CreditCard,
  Monitor,
  Terminal,
  Code,
  Cpu,
  HardDrive,
  Network,
  Flame
} from 'lucide-react'

interface AdminDashboard {
  systemHealth: {
    status: 'healthy' | 'warning' | 'critical'
    uptime: number
    lastCheck: string
    cpuUsage: number
    memoryUsage: number
    diskUsage: number
  }
  platformStats: {
    totalUsers: number
    activeToday: number
    totalScans: number
    alertsCount: number
    revenue: number
    newUsersToday: number
    scansToday: number
    vulnerabilitiesFound: number
  }
  recentAlerts: Array<{
    id: string
    type: 'security' | 'system' | 'user' | 'payment'
    message: string
    severity: 'low' | 'medium' | 'high' | 'critical'
    timestamp: string
    resolved: boolean
  }>
  systemServices: Array<{
    name: string
    status: 'online' | 'offline' | 'maintenance'
    uptime: number
    lastCheck: string
    responseTime: number
  }>
  recentActivity: Array<{
    id: string
    type: string
    user: string
    description: string
    timestamp: string
    severity: string
  }>
}

export default function AdminDashboardPage() {
  const router = useRouter()
  const [dashboard, setDashboard] = useState<AdminDashboard>({
    systemHealth: {
      status: 'healthy',
      uptime: 99.8,
      lastCheck: new Date().toISOString(),
      cpuUsage: 45.2,
      memoryUsage: 67.8,
      diskUsage: 34.5
    },
    platformStats: {
      totalUsers: 1247,
      activeToday: 156,
      totalScans: 15634,
      alertsCount: 3,
      revenue: 125000,
      newUsersToday: 23,
      scansToday: 456,
      vulnerabilitiesFound: 89
    },
    recentAlerts: [
      {
        id: '1',
        type: 'security',
        message: 'Multiple failed login attempts detected from IP *************',
        severity: 'high',
        timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
        resolved: false
      },
      {
        id: '2',
        type: 'system',
        message: 'High API usage detected - 85% of daily limit reached',
        severity: 'medium',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        resolved: false
      },
      {
        id: '3',
        type: 'payment',
        message: 'Payment gateway timeout for transaction #12345',
        severity: 'high',
        timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
        resolved: true
      }
    ],
    systemServices: [
      { name: 'Database', status: 'online', uptime: 99.9, lastCheck: new Date().toISOString(), responseTime: 12 },
      { name: 'API Gateway', status: 'online', uptime: 99.8, lastCheck: new Date().toISOString(), responseTime: 45 },
      { name: 'Scanner Engine', status: 'online', uptime: 98.5, lastCheck: new Date().toISOString(), responseTime: 234 },
      { name: 'OSINT Service', status: 'online', uptime: 99.2, lastCheck: new Date().toISOString(), responseTime: 67 },
      { name: 'File Analyzer', status: 'maintenance', uptime: 95.1, lastCheck: new Date().toISOString(), responseTime: 0 },
      { name: 'Bot Services', status: 'online', uptime: 97.8, lastCheck: new Date().toISOString(), responseTime: 89 }
    ],
    recentActivity: [
      {
        id: '1',
        type: 'scan_completed',
        user: 'cyberhunter',
        description: 'Completed vulnerability scan on target *************',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        severity: 'info'
      },
      {
        id: '2',
        type: 'vulnerability_found',
        user: 'securitypro',
        description: 'Found critical vulnerability CVE-2024-0001 in OpenSSL',
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        severity: 'critical'
      },
      {
        id: '3',
        type: 'user_registered',
        user: 'student123',
        description: 'New user registered with Student plan',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        severity: 'info'
      }
    ]
  })
  const [loading, setLoading] = useState(false)
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    // Update current time every second
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    // Auto-refresh dashboard data every 30 seconds
    const refreshTimer = setInterval(() => {
      loadDashboardData()
    }, 30000)

    return () => {
      clearInterval(timer)
      clearInterval(refreshTimer)
    }
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      console.log('🔧 Loading admin dashboard data...')
      
      // Simulate API call to fetch real admin data
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Update some dynamic values
      setDashboard(prev => ({
        ...prev,
        systemHealth: {
          ...prev.systemHealth,
          cpuUsage: 40 + Math.random() * 20,
          memoryUsage: 60 + Math.random() * 20,
          lastCheck: new Date().toISOString()
        },
        platformStats: {
          ...prev.platformStats,
          activeToday: prev.platformStats.activeToday + Math.floor(Math.random() * 5),
          scansToday: prev.platformStats.scansToday + Math.floor(Math.random() * 10)
        }
      }))
      
      console.log('✅ Admin dashboard data loaded successfully')
    } catch (error) {
      console.error('❌ Error loading admin dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const adminModules = [
    {
      title: 'User Management',
      description: 'Manage users, roles, and permissions',
      icon: Users,
      href: '/admin/users',
      color: 'blue',
      stats: `${dashboard.platformStats.totalUsers} users`,
      urgent: false
    },
    {
      title: 'System Analytics',
      description: 'Detailed system analytics and reports',
      icon: BarChart3,
      href: '/admin/dashboard',
      color: 'green',
      stats: `${dashboard.platformStats.scansToday} scans today`,
      urgent: false
    },
    {
      title: 'Security Center',
      description: 'Security monitoring and threat analysis',
      icon: Shield,
      href: '/admin/security',
      color: 'red',
      stats: `${dashboard.recentAlerts.filter(a => !a.resolved).length} active alerts`,
      urgent: dashboard.recentAlerts.filter(a => !a.resolved && a.severity === 'critical').length > 0
    },
    {
      title: 'System Settings',
      description: 'Configure system parameters and policies',
      icon: Settings,
      href: '/admin/settings',
      color: 'purple',
      stats: 'Global config',
      urgent: false
    },
    {
      title: 'Bot Management',
      description: 'WhatsApp & Telegram bot controls',
      icon: Bot,
      href: '/admin/bots',
      color: 'cyan',
      stats: '2 bots active',
      urgent: false
    },
    {
      title: 'Payment System',
      description: 'Subscription and billing management',
      icon: CreditCard,
      href: '/admin/payments',
      color: 'yellow',
      stats: `$${dashboard.platformStats.revenue.toLocaleString()}`,
      urgent: false
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-400'
      case 'offline': return 'text-red-400'
      case 'maintenance': return 'text-yellow-400'
      case 'healthy': return 'text-green-400'
      case 'warning': return 'text-yellow-400'
      case 'critical': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-blue-400'
      case 'medium': return 'text-yellow-400'
      case 'high': return 'text-orange-400'
      case 'critical': return 'text-red-400'
      case 'info': return 'text-blue-400'
      default: return 'text-gray-400'
    }
  }

  const getUsageColor = (usage: number) => {
    if (usage > 80) return 'text-red-400'
    if (usage > 60) return 'text-yellow-400'
    return 'text-green-400'
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Admin Dashboard Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              <span className="text-red-400">👑 Super Admin</span>{' '}
              <span className="text-cyber-pink">Dashboard</span>
            </h1>
            <p className="text-gray-300 text-lg">
              Ultimate system control and platform management - Super Admin Only
            </p>
            <div className="flex items-center space-x-6 mt-3">
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${dashboard.systemHealth.status === 'healthy' ? 'bg-green-400' : dashboard.systemHealth.status === 'warning' ? 'bg-yellow-400' : 'bg-red-400'} animate-pulse`}></div>
                <span className={`text-sm font-medium ${getStatusColor(dashboard.systemHealth.status)}`}>
                  System {dashboard.systemHealth.status.charAt(0).toUpperCase() + dashboard.systemHealth.status.slice(1)}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Cpu className="w-5 h-5 text-blue-400" />
                <span className={`text-sm font-medium ${getUsageColor(dashboard.systemHealth.cpuUsage)}`}>
                  CPU: {dashboard.systemHealth.cpuUsage.toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <HardDrive className="w-5 h-5 text-purple-400" />
                <span className={`text-sm font-medium ${getUsageColor(dashboard.systemHealth.memoryUsage)}`}>
                  RAM: {dashboard.systemHealth.memoryUsage.toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Server className="w-5 h-5 text-cyber-primary" />
                <span className="text-sm text-cyber-primary font-medium">
                  Uptime: {dashboard.systemHealth.uptime}%
                </span>
              </div>
            </div>
          </div>

          <div className="mt-6 lg:mt-0 flex items-center space-x-4">
            <div className="text-right">
              <div className="text-sm text-gray-400">System Time</div>
              <div className="text-lg font-mono text-green-400">
                {currentTime.toLocaleTimeString()}
              </div>
            </div>
            <button
              onClick={loadDashboardData}
              disabled={loading}
              className="btn-cyber-primary"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {loading ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        </div>

        {/* Platform Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="card-cyber">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Users</p>
                <p className="text-2xl font-bold text-white">{dashboard.platformStats.totalUsers.toLocaleString()}</p>
                <p className="text-green-400 text-xs">+{dashboard.platformStats.newUsersToday} today</p>
              </div>
              <Users className="h-8 w-8 text-blue-400" />
            </div>
          </div>

          <div className="card-cyber">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Active Today</p>
                <p className="text-2xl font-bold text-white">{dashboard.platformStats.activeToday}</p>
                <p className="text-cyan-400 text-xs">Online users</p>
              </div>
              <Activity className="h-8 w-8 text-green-400" />
            </div>
          </div>

          <div className="card-cyber">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Scans Today</p>
                <p className="text-2xl font-bold text-white">{dashboard.platformStats.scansToday}</p>
                <p className="text-purple-400 text-xs">{dashboard.platformStats.vulnerabilitiesFound} vulns found</p>
              </div>
              <Shield className="h-8 w-8 text-cyber-primary" />
            </div>
          </div>

          <div className="card-cyber">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Revenue</p>
                <p className="text-2xl font-bold text-white">${dashboard.platformStats.revenue.toLocaleString()}</p>
                <p className="text-yellow-400 text-xs">This month</p>
              </div>
              <TrendingUp className="h-8 w-8 text-yellow-400" />
            </div>
          </div>
        </div>

        {/* Admin Modules Grid */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-6">Administrative Modules</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {adminModules.map((module, index) => {
              const Icon = module.icon
              return (
                <div
                  key={index}
                  onClick={() => router.push(module.href)}
                  className={`card-cyber hover:border-cyber-primary transition-all duration-300 cursor-pointer group relative ${module.urgent ? 'border-red-400 bg-red-500/10' : ''}`}
                >
                  {module.urgent && (
                    <div className="absolute -top-2 -right-2">
                      <div className="w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
                    </div>
                  )}

                  <div className="flex items-start justify-between mb-4">
                    <div className={`p-3 rounded-lg bg-${module.color}-500/20`}>
                      <Icon className={`h-6 w-6 text-${module.color}-400`} />
                    </div>
                    <ArrowUp className="h-5 w-5 text-gray-400 group-hover:text-cyber-primary transition-colors transform group-hover:-translate-y-1" />
                  </div>

                  <h3 className="text-lg font-semibold text-white mb-2">{module.title}</h3>
                  <p className="text-gray-400 text-sm mb-3">{module.description}</p>

                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">{module.stats}</span>
                    <div className={`w-2 h-2 rounded-full animate-pulse ${module.urgent ? 'bg-red-400' : 'bg-green-400'}`}></div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* System Services & Recent Alerts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* System Services */}
          <div className="card-cyber">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-white">System Services</h3>
              <div className="flex items-center space-x-2">
                <Server className="h-6 w-6 text-cyber-primary" />
                <span className="text-sm text-gray-400">
                  {dashboard.systemServices.filter(s => s.status === 'online').length}/{dashboard.systemServices.length} Online
                </span>
              </div>
            </div>

            <div className="space-y-4">
              {dashboard.systemServices.map((service, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-cyber-dark/50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${service.status === 'online' ? 'bg-green-400' : service.status === 'maintenance' ? 'bg-yellow-400' : 'bg-red-400'} animate-pulse`}></div>
                    <div>
                      <p className="text-white font-medium">{service.name}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-400">
                        <span>Uptime: {service.uptime}%</span>
                        {service.responseTime > 0 && (
                          <span>Response: {service.responseTime}ms</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`text-sm font-medium ${getStatusColor(service.status)}`}>
                      {service.status.charAt(0).toUpperCase() + service.status.slice(1)}
                    </span>
                    <p className="text-gray-500 text-xs">
                      {new Date(service.lastCheck).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Alerts */}
          <div className="card-cyber">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-white">Security Alerts</h3>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-6 w-6 text-yellow-400" />
                <span className="text-sm text-gray-400">
                  {dashboard.recentAlerts.filter(a => !a.resolved).length} Active
                </span>
              </div>
            </div>

            <div className="space-y-4">
              {dashboard.recentAlerts.map((alert, index) => (
                <div key={index} className={`p-3 bg-cyber-dark/50 rounded-lg border-l-4 ${alert.resolved ? 'border-l-green-400' : alert.severity === 'critical' ? 'border-l-red-400' : alert.severity === 'high' ? 'border-l-orange-400' : 'border-l-yellow-400'}`}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className={`text-xs px-2 py-1 rounded-full bg-${alert.type === 'security' ? 'red' : alert.type === 'system' ? 'yellow' : alert.type === 'payment' ? 'purple' : 'blue'}-500/20 text-${alert.type === 'security' ? 'red' : alert.type === 'system' ? 'yellow' : alert.type === 'payment' ? 'purple' : 'blue'}-400`}>
                          {alert.type.toUpperCase()}
                        </span>
                        <span className={`text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                          {alert.severity.toUpperCase()}
                        </span>
                        {alert.resolved && (
                          <span className="text-xs px-2 py-1 rounded-full bg-green-500/20 text-green-400">
                            RESOLVED
                          </span>
                        )}
                      </div>
                      <p className="text-white text-sm">{alert.message}</p>
                      <p className="text-gray-500 text-xs mt-1">
                        {new Date(alert.timestamp).toLocaleString()}
                      </p>
                    </div>
                    <button className="text-gray-400 hover:text-white transition-colors">
                      <Eye className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}

              <div className="text-center pt-4">
                <button
                  onClick={() => router.push('/admin/security')}
                  className="text-cyber-primary hover:text-cyber-secondary transition-colors text-sm"
                >
                  View All Alerts →
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity & System Performance */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Activity */}
          <div className="card-cyber">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-white">Recent Activity</h3>
              <Activity className="h-6 w-6 text-green-400" />
            </div>

            <div className="space-y-4">
              {dashboard.recentActivity.map((activity, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-cyber-dark/50 rounded-lg">
                  <div className={`w-2 h-2 rounded-full mt-2 ${activity.severity === 'critical' ? 'bg-red-400' : activity.severity === 'info' ? 'bg-blue-400' : 'bg-green-400'} animate-pulse`}></div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-white font-medium text-sm">{activity.user}</span>
                      <span className="text-gray-500 text-xs">•</span>
                      <span className="text-gray-400 text-xs">{activity.type.replace('_', ' ')}</span>
                    </div>
                    <p className="text-gray-300 text-sm">{activity.description}</p>
                    <p className="text-gray-500 text-xs mt-1">
                      {new Date(activity.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}

              <div className="text-center pt-4">
                <button
                  onClick={() => router.push('/admin/activity')}
                  className="text-cyber-primary hover:text-cyber-secondary transition-colors text-sm"
                >
                  View All Activity →
                </button>
              </div>
            </div>
          </div>

          {/* System Performance */}
          <div className="card-cyber">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-white">System Performance</h3>
              <Monitor className="h-6 w-6 text-purple-400" />
            </div>

            <div className="space-y-6">
              {/* CPU Usage */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-300 text-sm">CPU Usage</span>
                  <span className={`text-sm font-medium ${getUsageColor(dashboard.systemHealth.cpuUsage)}`}>
                    {dashboard.systemHealth.cpuUsage.toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${dashboard.systemHealth.cpuUsage > 80 ? 'bg-red-400' : dashboard.systemHealth.cpuUsage > 60 ? 'bg-yellow-400' : 'bg-green-400'}`}
                    style={{ width: `${dashboard.systemHealth.cpuUsage}%` }}
                  ></div>
                </div>
              </div>

              {/* Memory Usage */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-300 text-sm">Memory Usage</span>
                  <span className={`text-sm font-medium ${getUsageColor(dashboard.systemHealth.memoryUsage)}`}>
                    {dashboard.systemHealth.memoryUsage.toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${dashboard.systemHealth.memoryUsage > 80 ? 'bg-red-400' : dashboard.systemHealth.memoryUsage > 60 ? 'bg-yellow-400' : 'bg-green-400'}`}
                    style={{ width: `${dashboard.systemHealth.memoryUsage}%` }}
                  ></div>
                </div>
              </div>

              {/* Disk Usage */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-300 text-sm">Disk Usage</span>
                  <span className={`text-sm font-medium ${getUsageColor(dashboard.systemHealth.diskUsage)}`}>
                    {dashboard.systemHealth.diskUsage.toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${dashboard.systemHealth.diskUsage > 80 ? 'bg-red-400' : dashboard.systemHealth.diskUsage > 60 ? 'bg-yellow-400' : 'bg-green-400'}`}
                    style={{ width: `${dashboard.systemHealth.diskUsage}%` }}
                  ></div>
                </div>
              </div>

              {/* System Uptime */}
              <div className="pt-4 border-t border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">System Uptime</span>
                  <span className="text-green-400 text-sm font-medium">{dashboard.systemHealth.uptime}%</span>
                </div>
                <p className="text-gray-500 text-xs mt-1">
                  Last check: {new Date(dashboard.systemHealth.lastCheck).toLocaleTimeString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card-cyber">
          <h3 className="text-xl font-semibold text-white mb-6">Quick Actions</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <button
              onClick={() => router.push('/admin/users')}
              className="btn-cyber-secondary text-sm"
            >
              <Users className="h-4 w-4 mr-2" />
              Users
            </button>
            <button
              onClick={() => router.push('/admin/settings')}
              className="btn-cyber-secondary text-sm"
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </button>
            <button
              onClick={() => router.push('/admin/security')}
              className="btn-cyber-secondary text-sm"
            >
              <Shield className="h-4 w-4 mr-2" />
              Security
            </button>
            <button
              onClick={() => router.push('/admin/monitoring')}
              className="btn-cyber-secondary text-sm"
            >
              <Monitor className="h-4 w-4 mr-2" />
              Monitor
            </button>
            <button
              onClick={() => router.push('/admin/bots')}
              className="btn-cyber-secondary text-sm"
            >
              <Bot className="h-4 w-4 mr-2" />
              Bots
            </button>
            <button
              onClick={() => router.push('/admin/dashboard')}
              className="btn-cyber-primary text-sm"
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
