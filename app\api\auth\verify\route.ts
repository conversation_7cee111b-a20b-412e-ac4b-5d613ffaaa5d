import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Token verification request started')
    
    // Get current user from token
    const user = await AuthService.getCurrentUser(request)
    
    if (!user) {
      console.log('❌ Token verification failed: No user found')
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid or expired token',
          authenticated: false
        },
        { status: 401 }
      )
    }

    console.log(`✅ Token verified for user: ${user.username} (${user.id})`)
    
    return NextResponse.json({
      success: true,
      authenticated: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.full_name,
        role: user.role,
        plan: user.plan,
        level: user.level,
        score: user.score,
        streak: user.streak_days,
        emailVerified: user.email_verified,
        lastActive: user.last_active,
        createdAt: user.created_at
      }
    })

  } catch (error) {
    console.error('❌ Token verification error:', error)
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Token verification failed',
        authenticated: false
      },
      { status: 401 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
