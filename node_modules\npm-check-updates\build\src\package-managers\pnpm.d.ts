import { GetVersion } from '../types/GetVersion';
import { Index } from '../types/IndexType';
import { NpmOptions } from '../types/NpmOptions';
import { Options } from '../types/Options';
import { SpawnOptions } from '../types/SpawnOptions';
/**
 * Spawn pnpm.
 *
 * @param args
 * @param [npmOptions={}]
 * @param [spawnOptions={}]
 * @returns
 */
declare const spawnPnpm: (args: string | string[], npmOptions?: NpmOptions, spawnOptions?: SpawnOptions) => Promise<string>;
/** Fetches the list of all installed packages. */
export declare const list: (options?: Options) => Promise<Index<string | undefined>>;
export declare const distTag: GetVersion;
export declare const greatest: GetVersion;
export declare const latest: GetVersion;
export declare const minor: GetVersion;
export declare const newest: GetVersion;
export declare const patch: GetVersion;
export declare const semver: GetVersion;
export { defaultPrefix, getPeerDependencies, packageAuthorChanged } from './npm';
export default spawnPnpm;
