"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.packageAuthorChanged = exports.getPeerDependencies = exports.defaultPrefix = exports.semver = exports.patch = exports.newest = exports.minor = exports.latest = exports.greatest = exports.distTag = exports.list = void 0;
const fast_memoize_1 = __importDefault(require("fast-memoize"));
const find_up_1 = __importDefault(require("find-up"));
const promises_1 = __importDefault(require("fs/promises"));
const ini_1 = __importDefault(require("ini"));
const path_1 = __importDefault(require("path"));
const spawn_please_1 = __importDefault(require("spawn-please"));
const keyValueBy_1 = __importDefault(require("../lib/keyValueBy"));
const logging_1 = require("../lib/logging");
const npm = __importStar(require("./npm"));
/** Reads the npmrc config file from the pnpm-workspace.yaml directory. */
const npmConfigFromPnpmWorkspace = (0, fast_memoize_1.default)(async (options) => {
    const pnpmWorkspacePath = await (0, find_up_1.default)('pnpm-workspace.yaml');
    if (!pnpmWorkspacePath)
        return {};
    const pnpmWorkspaceDir = path_1.default.dirname(pnpmWorkspacePath);
    const pnpmWorkspaceConfigPath = path_1.default.join(pnpmWorkspaceDir, '.npmrc');
    let pnpmWorkspaceConfig;
    try {
        pnpmWorkspaceConfig = await promises_1.default.readFile(pnpmWorkspaceConfigPath, 'utf-8');
    }
    catch (e) {
        return {};
    }
    (0, logging_1.print)(options, `\nUsing pnpm workspace config at ${pnpmWorkspaceConfigPath}:`, 'verbose');
    const config = npm.normalizeNpmConfig(ini_1.default.parse(pnpmWorkspaceConfig), pnpmWorkspaceDir);
    (0, logging_1.print)(options, config, 'verbose');
    return config;
});
/**
 * Spawn pnpm.
 *
 * @param args
 * @param [npmOptions={}]
 * @param [spawnOptions={}]
 * @returns
 */
const spawnPnpm = async (args, npmOptions = {}, spawnOptions) => {
    const cmd = process.platform === 'win32' ? 'pnpm.cmd' : 'pnpm';
    const fullArgs = [
        ...(npmOptions.location === 'global' ? 'global' : []),
        ...(Array.isArray(args) ? args : [args]),
        ...(npmOptions.prefix ? `--prefix=${npmOptions.prefix}` : []),
    ];
    return (0, spawn_please_1.default)(cmd, fullArgs, spawnOptions);
};
/** Fetches the list of all installed packages. */
const list = async (options = {}) => {
    // use npm for local ls for completeness
    // this should never happen since list is only called in runGlobal -> getInstalledPackages
    if (!options.global)
        return npm.list(options);
    const cmd = process.platform === 'win32' ? 'pnpm.cmd' : 'pnpm';
    const result = JSON.parse(await (0, spawn_please_1.default)(cmd, ['ls', '-g', '--json']));
    const list = (0, keyValueBy_1.default)(result[0].dependencies || {}, (name, { version }) => ({
        [name]: version,
    }));
    return list;
};
exports.list = list;
/** Wraps a GetVersion function and passes the npmrc located next to the pnpm-workspace.yaml if it exists. */
const withNpmWorkspaceConfig = (getVersion) => async (packageName, currentVersion, options = {}) => getVersion(packageName, currentVersion, options, {}, await npmConfigFromPnpmWorkspace(options));
exports.distTag = withNpmWorkspaceConfig(npm.distTag);
exports.greatest = withNpmWorkspaceConfig(npm.greatest);
exports.latest = withNpmWorkspaceConfig(npm.latest);
exports.minor = withNpmWorkspaceConfig(npm.minor);
exports.newest = withNpmWorkspaceConfig(npm.newest);
exports.patch = withNpmWorkspaceConfig(npm.patch);
exports.semver = withNpmWorkspaceConfig(npm.semver);
var npm_1 = require("./npm");
Object.defineProperty(exports, "defaultPrefix", { enumerable: true, get: function () { return npm_1.defaultPrefix; } });
Object.defineProperty(exports, "getPeerDependencies", { enumerable: true, get: function () { return npm_1.getPeerDependencies; } });
Object.defineProperty(exports, "packageAuthorChanged", { enumerable: true, get: function () { return npm_1.packageAuthorChanged; } });
exports.default = spawnPnpm;
//# sourceMappingURL=pnpm.js.map