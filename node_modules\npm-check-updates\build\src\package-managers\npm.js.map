{"version": 3, "file": "npm.js", "sourceRoot": "", "sources": ["../../../src/package-managers/npm.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gEAAkC;AAClC,4CAAmB;AACnB,8CAAqB;AACrB,iEAAwC;AACxC,2DAAkC;AAClC,qDAA4B;AAC5B,6DAAoC;AACpC,uDAA8B;AAC9B,uDAA8B;AAC9B,2DAAkC;AAClC,oDAA2B;AAC3B,gDAAuB;AACvB,oDAA+B;AAC/B,+CAAyC;AACzC,gEAAgC;AAChC,0DAAiC;AACjC,uEAA8C;AAC9C,kDAA8C;AAC9C,uEAA8C;AAC9C,4CAAmD;AACnD,iEAAkD;AAWlD,uCAAgE;AAEhE,MAAM,kBAAkB,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAA;AAE3E,0EAA0E;AAC1E,MAAM,eAAe,GAAG,CAAC,IAAiB,EAAE,EAAE;IAC5C,MAAM,KAAK,GAAG,IAAA,yBAAU,EAAC,IAAI,CAAC,CAAA;IAC9B,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAA;AAC5E,CAAC,CAAA;AAED,8DAA8D;AAC9D,MAAM,cAAc,GAAG,CAAC,OAAgB,EAAE,EAAE,CAC1C,OAAO,IAAI,CAAC,CAAC,gBAAU,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAA;AAEjF,uDAAuD;AAChD,MAAM,kBAAkB,GAAG,CAChC,SAAoB;AACpB,sDAAsD;AACtD,UAAmB,EACR,EAAE;IACb,MAAM,oBAAoB,GAAG;QAC3B,MAAM,EAAE,CAAC,MAAc,EAAgC,EAAE;YACvD,kFAAkF;YAClF,IAAI,CAAC,MAAM;gBAAE,OAAM;YACnB,+FAA+F;YAC/F,+IAA+I;YAC/I,MAAM,MAAM,GAAG,YAAE,CAAC,YAAY,CAAC,cAAI,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,IAAA,mBAAS,EAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;YACzF,MAAM,KAAK,GAAG,2BAA2B,CAAA;YACzC,MAAM,MAAM,GAAa,MAAM;iBAC5B,KAAK,CAAC,KAAK,CAAC;iBACZ,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;iBACzB,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,SAAS,EAAE,GAAG,KAAK,EAAE,CAAC,CAAA;YACzC,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,CAAA;QACvB,CAAC;QACD,UAAU,EAAE,YAAY;QACxB,YAAY,EAAE,WAAW;KAC1B,CAAA;IAED,qFAAqF;IACrF,yDAAyD;IACzD,MAAM,QAAQ,GAAgC;QAC5C,GAAG,EAAE,SAAS;QACd,gBAAgB,EAAE,SAAS;QAC3B,KAAK,EAAE,SAAS;QAChB,QAAQ,EAAE,SAAS;QACnB,KAAK,EAAE,SAAS;QAChB,WAAW,EAAE,SAAS;QACtB,WAAW,EAAE,SAAS;QACtB,GAAG,EAAE,SAAS;QACd,kBAAkB,EAAE,SAAS;QAC7B,YAAY,EAAE,SAAS;QACvB,YAAY,EAAE,SAAS;QACvB,QAAQ,EAAE,SAAS;QACnB,MAAM,EAAE,SAAS;QACjB,YAAY,EAAE,SAAS;QACvB,KAAK,EAAE,SAAS;QAChB,iBAAiB,EAAE,SAAS;QAC5B,iBAAiB,EAAE,SAAS;QAC5B,IAAI,EAAE,SAAS;QACf,aAAa,EAAE,SAAS;QACxB,MAAM,EAAE,SAAS;QACjB,WAAW,EAAE,SAAS;QACtB,SAAS,EAAE,SAAS;QACpB,aAAa,EAAE,SAAS;QACxB,aAAa,EAAE,SAAS;QACxB,oBAAoB,EAAE,SAAS;QAC/B,YAAY,EAAE,SAAS;QACvB,IAAI,EAAE,SAAS;QACf,cAAc,EAAE,SAAS;QACzB,cAAc,EAAE,SAAS;QACzB,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,SAAS;QAClB,4BAA4B,EAAE,SAAS;QACvC,WAAW,EAAE,SAAS;QACtB,eAAe,EAAE,SAAS;QAC1B,SAAS,EAAE,SAAS;QACpB,aAAa,EAAE,SAAS;QACxB,YAAY,EAAE,SAAS;QACvB,QAAQ,EAAE,SAAS;QACnB,QAAQ,EAAE,SAAS;QACnB,aAAa,EAAE,SAAS;QACxB,IAAI,EAAE,SAAS;QACf,UAAU,EAAE,SAAS;QACrB,OAAO,EAAE,SAAS;QAClB,SAAS,EAAE,SAAS;QACpB,YAAY,EAAE,SAAS;QACvB,QAAQ,EAAE,SAAS;QACnB,QAAQ,EAAE,SAAS;QACnB,UAAU,EAAE,SAAS;QACrB,aAAa,EAAE,SAAS;QACxB,UAAU,EAAE,SAAS;QACrB,cAAc,EAAE,SAAS;QACzB,SAAS,EAAE,SAAS;QACpB,MAAM,EAAE,SAAS;QACjB,OAAO,EAAE,SAAS;QAClB,cAAc,EAAE,SAAS;QACzB,KAAK,EAAE,SAAS;QAChB,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,SAAS;QACnB,gBAAgB,EAAE,SAAS;QAC3B,WAAW,EAAE,QAAQ;QACrB,YAAY,EAAE,QAAQ;QACtB,gBAAgB,EAAE,QAAQ;QAC1B,oBAAoB,EAAE,QAAQ;QAC9B,oBAAoB,EAAE,QAAQ;QAC9B,YAAY,EAAE,QAAQ;QACtB,OAAO,EAAE,QAAQ;QACjB,UAAU,EAAE,QAAQ;QACpB,WAAW,EAAE,QAAQ;QACrB,eAAe,EAAE,QAAQ;QACzB,gBAAgB,EAAE,QAAQ;QAC1B,OAAO,EAAE,QAAQ;KAClB,CAAA;IAED,oCAAoC;IACpC,MAAM,eAAe,GAAG,CAAC,CAAS,EAAW,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,GAAG,CAAA;IAEjF,mCAAmC;IACnC,MAAM,cAAc,GAAG,CAAC,CAAS,EAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IAE9D,6DAA6D;IAC7D,iDAAiD;IACjD,MAAM,MAAM,GAAc,IAAA,uBAAU,EAAC,SAAS,EAAE,CAAC,GAAW,EAAE,KAAiC,EAAE,EAAE;QACjG,4DAA4D;QAC5D,MAAM,eAAe,GACnB,OAAO,KAAK,KAAK,QAAQ;YACvB,CAAC,CAAC,KAAK;YACP,CAAC,CAAC,6BAA6B;gBAC/B,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,KAAK,SAAS;oBAC3D,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC;oBACxB,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,KAAK,QAAQ;wBAC5D,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC;wBACvB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAW,CAAC,CAAA;QAEhF,+BAA+B;QAC/B,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,GAAsC,oBAAoB,CAAA;QAEpF,OAAO,OAAO,SAAS,KAAK,QAAQ;YAClC,CAAC,CAAC,4BAA4B;gBAC5B,EAAE,CAAC,SAAS,CAAC,EAAE,eAAe,EAAE;YAClC,CAAC,CAAC,8BAA8B;gBAChC,OAAO,SAAS,KAAK,UAAU;oBAC/B,CAAC,CAAC,EAAE,GAAI,SAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAS,EAAE;oBACvD,CAAC,CAAC,uCAAuC;wBACvC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAA,mBAAS,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,eAAe,EAAE,CAAA;IACxE,CAAC,CAAC,CAAA;IAEF,OAAO,MAAM,CAAA;AACf,CAAC,CAAA;AAtIY,QAAA,kBAAkB,sBAsI9B;AAED,sNAAsN;AACtN,MAAM,aAAa,GAAG,IAAA,sBAAO,EAAC,CAAC,UAAmB,EAAoB,EAAE;IACtE,IAAI,MAAM,CAAA;IAEV,IAAI,UAAU,EAAE;QACd,IAAI;YACF,MAAM,GAAG,aAAG,CAAC,KAAK,CAAC,YAAE,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAA;SACzD;QAAC,OAAO,GAAQ,EAAE;YACjB,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACzB,OAAO,IAAI,CAAA;aACZ;iBAAM;gBACL,MAAM,GAAG,CAAA;aACV;SACF;KACF;SAAM;QACL,+HAA+H;QAC/H,oDAAoD;QACpD,MAAM,IAAI,GAAG,sBAAY,CAAC,IAAI,CAAC,IAAI,EAAE;YACnC,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB;SACnF,CAAC,CAAA;QACF,MAAM,GAAG;YACP,GAAG,IAAI,CAAC,MAAM,EAAE;YAChB,KAAK,EAAE,KAAK;SACb,CAAA;KACF;IAED,OAAO,IAAA,0BAAkB,EAAC,MAAM,EAAE,UAAU,CAAC,CAAA;AAC/C,CAAC,CAAC,CAAA;AAEF,uDAAuD;AACvD,6EAA6E;AAC7E,MAAM,SAAS,GAAG,aAAa,EAAE,CAAA;AAEjC,uGAAuG;AACvG,MAAM,kBAAkB,GAAG,IAAA,sBAAO,EAAC,KAAK,IAAI,EAAE;IAC5C,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAA;IAC5D,MAAM,MAAM,GAAG,MAAM,IAAA,sBAAK,EAAC,GAAG,EAAE,CAAC,WAAW,CAAC,CAAC,CAAA;IAC9C,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,EAAE,CAAA;IAChC,0CAA0C;IAC1C,OAAO,gBAAU,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,gBAAU,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;AAC7E,CAAC,CAAC,CAAA;AAEF;;;;GAIG;AAEH;;;;;;GAMG;AACH,SAAS,SAAS,CAAI,MAAc,EAAE,IAAgD;IACpF,IAAI,IAAI,CAAA;IACR,IAAI;QACF,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;KAC1B;IAAC,OAAO,GAAG,EAAE;QACZ,MAAM,IAAI,KAAK,CACb,uBAAuB,IAAI,CAAC,OAAO,KACjC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,qCAAqC,IAAI,CAAC,WAAW,WAAW,CAAC,CAAC,CAAC,EACxF,IAAI,MAAM,CAAC,CAAC,CAAC,oBAAoB,GAAG,MAAM,CAAC,CAAC,CAAC,0BAA0B,EAAE,CAC1E,CAAA;KACF;IACD,OAAO,IAAS,CAAA;AAClB,CAAC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,oBAAoB,CACxC,WAAmB,EACnB,cAA2B,EAC3B,eAA4B,EAC5B,UAAmB,EAAE,EACrB,cAA0B;;IAE1B,MAAM,MAAM,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAC,WAAW,EAAE;QACjD,GAAG,cAAc;QACjB,GAAG,SAAS;QACZ,YAAY,EAAE,IAAI;QAClB,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;KAC5E,CAAC,CAAA;IACF,IAAI,MAAM,CAAC,QAAQ,EAAE;QACnB,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;QAChD,MAAM,OAAO,GAAG,gBAAU,CAAC,aAAa,CAAC,WAAW,EAAE,cAAc,CAAC,CAAA;QACrE,MAAM,QAAQ,GAAG,gBAAU,CAAC,aAAa,CAAC,WAAW,EAAE,eAAe,CAAC,CAAA;QACvE,IAAI,OAAO,IAAI,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE;YAClG,MAAM,aAAa,GAAG,MAAA,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,0CAAE,IAAI,CAAA;YAC7D,MAAM,YAAY,GAAG,MAAA,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,QAAQ,0CAAE,IAAI,CAAA;YAC7D,OAAO,CAAC,IAAA,iBAAO,EAAC,aAAa,EAAE,YAAY,CAAC,CAAA;SAC7C;KACF;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AAzBD,oDAyBC;AAED,gDAAgD;AAChD,MAAM,WAAW,GAAG,CAAC,CAAM,EAA2B,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAA;AAE9G,qGAAqG;AAC9F,MAAM,YAAY,GACvB,CAAC,oBAAoC,EAAmB,EAAE,CAC1D,CAAC,IAAY,EAAE,MAAgB,EAAE,cAAuB,EAAE,OAAgB,EAAE,EAAE;;IAC5E,sBAAsB;IACtB,MAAM,gBAAgB,GACpB,OAAO,oBAAoB,KAAK,UAAU;QACxC,CAAC,CAAC,MAAA,oBAAoB,CAAC,OAAO,CAAC,0CAAG,IAAI,CAAC;QACvC,CAAC,CAAC,OAAO,oBAAoB,KAAK,QAAQ,IAAI,WAAW,CAAC,oBAAoB,CAAC;YAC/E,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA;IAEhC,MAAM,OAAO,GAAG,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAA;IAE3F,oCAAoC;IACpC,uDAAuD;IACvD,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,CAAC,KAAK,CAAC,gCAAgC,IAAI,EAAE,CAAC,CAAA;QACrD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;KAChB;IAED,MAAM,IAAI,GAAG,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAI,MAAA,gBAAgB,CAAC,IAAI,0CAAG,OAAO,CAAC,CAAA,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;IAC5G,MAAM,SAAS,GAAc;QAC3B,IAAI;QACJ,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;QACrB,IAAI,EAAE;YACJ,CAAC,OAAO,CAAC,EAAE,IAAI;SAChB;QACD,OAAO;QACP,oBAAoB;QACpB,QAAQ,EAAE,EAAE;QACZ,GAAG,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC;KAC7D,CAAA;IAED,OAAO,OAAO,CAAC,OAAO,CACpB,IAAA,uBAAU,EAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;QAC3B,CAAC,KAAK,CAAC,EACL,KAAK,KAAK,UAAU;YAClB,CAAC,CAAE;gBACC,CAAC,OAAO,CAAC,EAAE,SAAS;aACA;YACxB,CAAC,CAAC,KAAK,KAAK,MAAM;gBAClB,CAAC,CAAE;oBACC,CAAC,OAAO,CAAC,EAAE,IAAI;iBACE;gBACrB,CAAC,CAAE;oBACC,GAAG,SAAS;oBACZ,QAAQ,EAAE,CAAC,SAAS,CAAC;iBACR;KACtB,CAAC,CAAC,CACJ,CAAA;AACH,CAAC,CAAA;AAlDU,QAAA,YAAY,gBAkDtB;AAEH,+FAA+F;AAC/F,iOAAiO;AACjO,MAAM,eAAe,GAAG,IAAA,sBAAO,EAC7B,CACE,EACE,cAAc,EACd,aAAa,EACb,yBAAyB,GAK1B,EACD,OAAgB,EAChB,EAAE;IACF,4CAA4C;IAC5C,MAAM,oBAAoB,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IACrG,MAAM,gBAAgB,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,oBAAoB,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IACtG,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAC9E,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,gBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAE1E,IAAI,yBAAyB,IAAI,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;QAClF,IAAA,eAAK,EAAC,OAAO,EAAE,mCAAmC,EAAE,SAAS,CAAC,CAAA;QAC9D,IAAA,qBAAW,EAAC,OAAO,EAAE,IAAA,cAAI,EAAC,yBAAyB,EAAE,OAAO,CAAC,EAAE,SAAS,CAAC,CAAA;KAC1E;IAED,IAAI,aAAa,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;QAC1D,IAAA,eAAK,EAAC,OAAO,EAAE,sBAAsB,EAAE,SAAS,CAAC,CAAA;QACjD,IAAA,qBAAW,EAAC,OAAO,EAAE,IAAA,cAAI,EAAC,aAAa,EAAE,OAAO,CAAC,EAAE,SAAS,CAAC,CAAA;KAC9D;IAED,IAAI,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;QAC5D,IAAA,eAAK,EAAC,OAAO,EAAE,gCAAgC,EAAE,SAAS,CAAC,CAAA;QAC3D,IAAA,qBAAW,EAAC,OAAO,EAAE,IAAA,cAAI,EAAC,cAAc,EAAE,OAAO,CAAC,EAAE,SAAS,CAAC,CAAA;KAC/D;IAED,IAAI,gBAAgB,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;QAChE,IAAA,eAAK,EAAC,OAAO,EAAE,0BAA0B,oBAAoB,IAAI,EAAE,SAAS,CAAC,CAAA;QAC7E,IAAA,qBAAW,EAAC,OAAO,EAAE,IAAA,cAAI,EAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,SAAS,CAAC,CAAA;KACjE;IAED,IAAI,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;QACxD,IAAA,eAAK,EAAC,OAAO,EAAE,sBAAsB,gBAAgB,IAAI,EAAE,SAAS,CAAC,CAAA;QACrE,+CAA+C;QAC/C,IAAA,qBAAW,EAAC,OAAO,EAAE,IAAA,cAAI,EAAC,YAAY,EAAE,OAAO,CAAC,EAAE,SAAS,CAAC,CAAA;KAC7D;IAED,MAAM,eAAe,GAAG;QACtB,GAAG,yBAAyB;QAC5B,GAAG,aAAa;QAChB,GAAG,cAAc;QACjB,GAAG,gBAAgB;QACnB,GAAG,YAAY;QACf,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAC3E,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;KAC3D,CAAA;IAED,MAAM,QAAQ,GAAG,yBAAyB,IAAI,cAAc,IAAI,gBAAgB,IAAI,YAAY,CAAA;IAChG,IAAI,QAAQ,EAAE;QACZ,IAAA,eAAK,EAAC,OAAO,EAAE,sBAAsB,EAAE,SAAS,CAAC,CAAA;QACjD,+CAA+C;QAC/C,IAAA,qBAAW,EAAC,OAAO,EAAE,IAAA,cAAI,EAAC,eAAe,EAAE,OAAO,CAAC,EAAE,SAAS,CAAC,CAAA;KAChE;IAED,OAAO,eAAe,CAAA;AACxB,CAAC,CACF,CAAA;AAED;;;;;;;GAOG;AACH,KAAK,UAAU,QAAQ,CACrB,WAAmB,EACnB,MAAgB,EAChB,cAAuB,EACvB,OAAgB,EAChB,OAAO,GAAG,CAAC,EACX,cAA0B,EAC1B,yBAAqC;;IAErC,iCAAiC;IACjC,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE;QAC7B,MAAM,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;QAClE,OAAO,IAAA,oBAAY,EAAC,oBAAoB,CAAC,CAAC,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC,CAAA;KACxF;IAED,IAAI,cAAc,CAAC,cAAc,CAAC,EAAE;QAClC,OAAO,OAAO,CAAC,OAAO,CAAC,EAAsB,CAAC,CAAA;KAC/C;IAED,kCAAkC;IAClC,MAAM,cAAc,GAAG,CAAA,MAAA,OAAO,CAAC,MAAM,0CAAE,QAAQ,CAAC,MAAM,CAAC,EAAC,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;IACtF,MAAM,YAAY,GAAG,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;IAEpD,MAAM,eAAe,GAAG,eAAe,CACrC;QACE,aAAa,EAAE,EAAE,GAAG,SAAS,EAAE,YAAY,EAAE;QAC7C,cAAc;QACd,yBAAyB;KAC1B,EACD,OAAO,CACR,CAAA;IAED,IAAI,MAAW,CAAA;IACf,IAAI;QACF,MAAM,GAAG,MAAM,gBAAM,CAAC,SAAS,CAAC,WAAW,EAAE,eAAe,CAAC,CAAA;KAC9D;IAAC,OAAO,GAAQ,EAAE;QACjB,IAAI,OAAO,CAAC,KAAK,IAAI,EAAE,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE;YAC/C,OAAO,QAAQ,CAAC,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,CAAC,CAAA;SAC/F;QAED,MAAM,GAAG,CAAA;KACV;IAED,2CAA2C;IAC3C,MAAM,gBAAgB,GAAG,IAAA,uBAAU,EAAC,MAAM,EAAE,KAAK,CAAC,EAAE;QAClD,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;QAEzB,mDAAmD;QACnD,IAAI,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,MAAM,CAAC,QAAQ,EAAE;YACrD,MAAM,SAAS,GAAc,MAAM,CAAC,QAAQ,CAAC,IAAA,aAAG,EAAC,MAAM,EAAE,KAAK,CAAsB,CAAC,CAAA;YACrF,uFAAuF;YACvF,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,CAAA;SACvE;QAED,OAAO;YACL,CAAC,KAAK,CAAC,EAAE,KAAK;SACf,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,OAAO,gBAAgB,CAAA;AACzB,CAAC;AAED,gEAAgE;AACnD,QAAA,gBAAgB,GAAG,IAAA,sBAAO,EAAC,QAAQ,EAAE;IAChD,iGAAiG;IACjG,UAAU,EAAE,CAAC,CAAC,CACZ,WAAW,EACX,MAAM,EACN,cAAc,EACd,OAAO,EACP,OAAO,EACP,cAAc,EACd,yBAAyB,EACG,EAAE,EAAE,CAChC,IAAI,CAAC,SAAS,CAAC;QACb,WAAW;QACX,MAAM;QACN,mIAAmI;QACnI,cAAc,CAAC,cAAc,CAAC;QAC9B,0FAA0F;QAC1F,IAAA,cAAI,EAAC,OAAO,EAAE,aAAa,CAAC;QAC5B,wCAAwC;QACxC,OAAO;QACP,cAAc;QACd,yBAAyB;KAC1B,CAAC,CAA4B;CACjC,CAAC,CAAA;AAEF;;;;;;;GAOG;AACI,KAAK,UAAU,OAAO,CAC3B,WAAmB,EACnB,KAAa,EACb,cAAuB,EACvB,OAAgB,EAChB,cAA0B,EAC1B,gBAA4B;IAE5B,MAAM,MAAM,GAAG,MAAM,IAAA,wBAAgB,EACnC,WAAW,EACX,CAAC,KAAK,CAAC,EACP,cAAc,EACd,OAAO,EACP,CAAC,EACD,cAAc,EACd,gBAAgB,CACjB,CAAA;IACD,OAAO,MAAM,CAAC,KAAK,CAAC,CAAA;AACtB,CAAC;AAlBD,0BAkBC;AAED;;;;;;;GAOG;AACH,KAAK,UAAU,QAAQ,CACrB,IAAuB,EACvB,aAAyB,EAAE,EAC3B,eAA2B,EAAE;IAE7B,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAA;IAE5D,MAAM,QAAQ,GAAG;QACf,GAAG,CAAC,UAAU,CAAC,QAAQ;YACrB,CAAC,CAAC,CAAC,MAAM,kBAAkB,EAAE,CAAC;gBAC5B,CAAC,CAAC,CAAC,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACvC,CAAC,CAAC,UAAU,CAAC,QAAQ,KAAK,QAAQ;oBAClC,CAAC,CAAC,CAAC,UAAU,CAAC;oBACd,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,EAAE,CAAC;QACP,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/D,QAAQ;QACR,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;KACzC,CAAA;IAED,OAAO,IAAA,sBAAK,EAAC,GAAG,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAA;AAC3C,CAAC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,aAAa,CAAC,OAAgB;IAClD,IAAI,OAAO,CAAC,MAAM,EAAE;QAClB,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;KACvC;IAED,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAA;IAE5D,IAAI,MAA0B,CAAA;IAE9B,+CAA+C;IAC/C,+DAA+D;IAC/D,IAAI;QACF,MAAM,GAAG,MAAM,IAAA,sBAAK,EAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAA;KACvD;IAAC,OAAO,CAAM,EAAE;QACf,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAA;QACjD,IAAA,eAAK,EACH,OAAO,EACP,8IAA8I;YAC5I,OAAO,EACT,SAAS,EACT,OAAO,CACR,CAAA;KACF;IAED,2DAA2D;IAC3D,+DAA+D;IAC/D,OAAO,OAAO,CAAC,MAAM,KAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,CAAC,QAAQ,CAAC,CAAA;QAC9C,CAAC,CAAC,YAAY;QACd,CAAC,CAAC,wDAAwD;YAC1D,0CAA0C;YAC1C,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM;gBACrE,CAAC,CAAC,MAAM;oBACN,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE;oBACf,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,OAAO;gBACjC,CAAC,CAAC,SAAS,CAAA;AACf,CAAC;AAnCD,sCAmCC;AAED;;;;;;;GAOG;AACI,MAAM,QAAQ,GAAe,KAAK,EACvC,WAAW,EACX,cAAc,EACd,OAAO,GAAG,EAAE,EACZ,SAAqB,EACrB,gBAA4B,EACJ,EAAE;IAC1B,iCAAiC;IACjC,MAAM,QAAQ,GAAG,CAAC,MAAM,OAAO,CAC7B,WAAW,EACX,UAAU,EACV,cAAc,EACd,OAAO,EACP,SAAS,EACT,gBAAgB,CACjB,CAAqB,CAAA;IAEtB,OAAO;QACL,OAAO,EACL,IAAA,cAAI;QACF,kDAAkD;QAClD,IAAA,gBAAM,EAAC,QAAQ,EAAE,IAAA,yBAAe,EAAC,OAAO,CAAC,CAAC;aACvC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;aACnB,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CACrC,IAAI,IAAI;KACZ,CAAA;AACH,CAAC,CAAA;AA1BY,QAAA,QAAQ,YA0BpB;AAED;;;;;;GAMG;AACI,MAAM,mBAAmB,GAAG,KAAK,EAAE,WAAmB,EAAE,OAAgB,EAA2B,EAAE;IAC1G,MAAM,IAAI,GAAG,CAAC,MAAM,EAAE,GAAG,WAAW,IAAI,OAAO,EAAE,EAAE,kBAAkB,CAAC,CAAA;IACtE,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAA;IACjE,OAAO,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;AACpF,CAAC,CAAA;AAJY,QAAA,mBAAmB,uBAI/B;AAED;;;;;;;;GAQG;AACI,MAAM,IAAI,GAAG,KAAK,EAAE,UAAmB,EAAE,EAAsC,EAAE;IACtF,MAAM,MAAM,GAAG,MAAM,QAAQ,CAC3B,CAAC,IAAI,EAAE,WAAW,CAAC,EACnB;QACE,mGAAmG;QACnG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QACnD,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;KACxD,EACD;QACE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAC9C,aAAa,EAAE,KAAK;KACrB,CACF,CAAA;IACD,MAAM,YAAY,GAAG,SAAS,CAE3B,MAAM,EAAE;QACT,OAAO,EAAE,MAAM,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,aAAa,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,GAC9G,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EACnD,EAAE;KACH,CAAC,CAAC,YAAY,CAAA;IAEf,OAAO,IAAA,uBAAU,EAAC,YAAY,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;;QAAC,OAAA,CAAC;YAC/C,qDAAqD;YACrD,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,OAAO,KAAI,MAAA,IAAI,CAAC,QAAQ,0CAAE,OAAO,CAAA;SAC/C,CAAC,CAAA;KAAA,CAAC,CAAA;AACL,CAAC,CAAA;AAzBY,QAAA,IAAI,QAyBhB;AAED;;;;;;;GAOG;AACI,MAAM,OAAO,GAAe,KAAK,EACtC,WAAW,EACX,cAAc,EACd,UAAmB,EAAE,EACrB,SAAqB,EACrB,gBAA4B,EAC5B,EAAE;;IACF,MAAM,SAAS,GAAG,CAAC,MAAM,OAAO,CAC9B,WAAW,EACX,aAAa,OAAO,CAAC,OAAO,EAAE,EAC9B,cAAc,EACd,OAAO,EACP,SAAS,EACT,gBAAgB,CACjB,CAAc,CAAA,CAAC,uCAAuC;IAEvD,kCAAkC;IAClC,qEAAqE;IACrE,wFAAwF;IACxF,wEAAwE;IACxE,IAAI,SAAS,IAAI,IAAA,yBAAe,EAAC,OAAO,CAAC,CAAC,SAAS,CAAC,EAAE;QACpD,OAAO;YACL,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,GAAG,CAAC,CAAA,MAAA,SAAS,CAAC,IAAI,0CAAG,SAAS,CAAC,OAAO,CAAC,EAAC,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;SAC9F,CAAA;KACF;IAED,4GAA4G;IAC5G,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,KAAK,QAAQ;QAAE,OAAO,EAAE,CAAA;IAE9D,+DAA+D;IAC/D,0BAA0B;IAC1B,8BAA8B;IAC9B,uCAAuC;IACvC,OAAO,IAAA,gBAAQ,EAAC,WAAW,EAAE,cAAc,EAAE,OAAO,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAA;AACpF,CAAC,CAAA;AAnCY,QAAA,OAAO,WAmCnB;AAED;;;;;;;GAOG;AACI,MAAM,MAAM,GAAe,KAAK,EACrC,WAAmB,EACnB,cAAuB,EACvB,UAAmB,EAAE,EACrB,SAAqB,EACrB,gBAA4B,EAC5B,EAAE,CAAC,IAAA,eAAO,EAAC,WAAW,EAAE,cAAc,EAAE,EAAE,GAAG,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAA;AAN5F,QAAA,MAAM,UAMsF;AAEzG;;;;;;;GAOG;AACI,MAAM,MAAM,GAAe,KAAK,EACrC,WAAW,EACX,cAAc,EACd,OAAO,GAAG,EAAE,EACZ,SAAqB,EACrB,gBAA4B,EACJ,EAAE;IAC1B,MAAM,MAAM,GAAG,MAAM,IAAA,wBAAgB,EACnC,WAAW,EACX,CAAC,MAAM,EAAE,UAAU,CAAC,EACpB,cAAc,EACd,OAAO,EACP,CAAC,EACD,SAAS,EACT,gBAAgB,CACjB,CAAA;IAED,2DAA2D;IAC3D,+FAA+F;IAC/F,6EAA6E;IAC7E,MAAM,4BAA4B,GAAG,IAAA,uBAAU,EAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC,EAAE,CAAC,SAAoB,EAAE,EAAE,CAC7G,IAAA,6BAAmB,EAAC,SAAS,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CACjG,CAAA;IAED,uDAAuD;IACvD,mEAAmE;IACnE,MAAM,yBAAyB,GAAG,IAAA,sBAAY,EAC5C,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAA6B,EAC/C,OAAO,CAAC,EAAE,CAAC,4BAA4B,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAC3G,CAAA;IAED,gDAAgD;IAChD,MAAM,oBAAoB,GAAG,IAAA,gBAAM,EAAC,MAAM,CAAC,OAAO,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAA;IAE7G,OAAO,EAAE,OAAO,EAAE,IAAA,cAAI,EAAC,oBAAoB,CAAC,EAAE,CAAA;AAChD,CAAC,CAAA;AAnCY,QAAA,MAAM,UAmClB;AAED;;;;;;;GAOG;AACI,MAAM,KAAK,GAAe,KAAK,EACpC,WAAW,EACX,cAAc,EACd,OAAO,GAAG,EAAE,EACZ,SAAqB,EACrB,gBAA4B,EACJ,EAAE;IAC1B,MAAM,QAAQ,GAAG,CAAC,MAAM,OAAO,CAC7B,WAAW,EACX,UAAU,EACV,cAAc,EACd,OAAO,EACP,SAAS,EACT,gBAAgB,CACjB,CAAqB,CAAA;IACtB,MAAM,OAAO,GAAG,WAAW,CAAC,mBAAmB,CAC7C,IAAA,gBAAM,EAAC,QAAQ,EAAE,IAAA,yBAAe,EAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAC9D,cAAc,EACd,OAAO,CACR,CAAA;IACD,OAAO,EAAE,OAAO,EAAE,CAAA;AACpB,CAAC,CAAA;AArBY,QAAA,KAAK,SAqBjB;AAED;;;;;;;GAOG;AACI,MAAM,KAAK,GAAe,KAAK,EACpC,WAAW,EACX,cAAc,EACd,OAAO,GAAG,EAAE,EACZ,SAAqB,EACrB,gBAA4B,EACJ,EAAE;IAC1B,MAAM,QAAQ,GAAG,CAAC,MAAM,OAAO,CAC7B,WAAW,EACX,UAAU,EACV,cAAc,EACd,OAAO,EACP,SAAS,EACT,gBAAgB,CACjB,CAAqB,CAAA;IACtB,MAAM,OAAO,GAAG,WAAW,CAAC,mBAAmB,CAC7C,IAAA,gBAAM,EAAC,QAAQ,EAAE,IAAA,yBAAe,EAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAC9D,cAAc,EACd,OAAO,CACR,CAAA;IACD,OAAO,EAAE,OAAO,EAAE,CAAA;AACpB,CAAC,CAAA;AArBY,QAAA,KAAK,SAqBjB;AAED;;;;;;;GAOG;AACI,MAAM,MAAM,GAAe,KAAK,EACrC,WAAW,EACX,cAAc,EACd,OAAO,GAAG,EAAE,EACZ,SAAqB,EACrB,gBAA4B,EACJ,EAAE;IAC1B,MAAM,QAAQ,GAAG,CAAC,MAAM,OAAO,CAC7B,WAAW,EACX,UAAU,EACV,cAAc,EACd,OAAO,EACP,SAAS,EACT,gBAAgB,CACjB,CAAqB,CAAA;IACtB,iCAAiC;IACjC,IAAI,eAAe,CAAC,cAAc,CAAC;QAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAA;IAE7D,MAAM,gBAAgB,GAAG,IAAA,gBAAM,EAAC,QAAQ,EAAE,IAAA,yBAAe,EAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;IACvF,6DAA6D;IAC7D,6CAA6C;IAC7C,MAAM,OAAO,GAAG,gBAAU,CAAC,aAAa,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAA;IAC1E,OAAO,EAAE,OAAO,EAAE,CAAA;AACpB,CAAC,CAAA;AAvBY,QAAA,MAAM,UAuBlB;AAED,kBAAe,QAAQ,CAAA"}