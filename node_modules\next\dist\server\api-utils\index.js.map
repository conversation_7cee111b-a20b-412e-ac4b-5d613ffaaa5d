{"version": 3, "sources": ["../../../src/server/api-utils/index.ts"], "names": ["ApiError", "COOKIE_NAME_PRERENDER_BYPASS", "COOKIE_NAME_PRERENDER_DATA", "RESPONSE_LIMIT_DEFAULT", "SYMBOL_CLEARED_COOKIES", "SYMBOL_PREVIEW_DATA", "checkIsOnDemandRevalidate", "clearPreviewData", "redirect", "sendError", "sendStatusCode", "setLazyProp", "wrapApiHandler", "page", "handler", "args", "getTracer", "getRootSpanAttributes", "set", "trace", "NodeSpan", "<PERSON><PERSON><PERSON><PERSON>", "spanName", "res", "statusCode", "statusOrUrl", "url", "Error", "writeHead", "Location", "write", "end", "req", "previewProps", "headers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from", "previewModeId", "get", "PRERENDER_REVALIDATE_HEADER", "isOnDemandRevalidate", "revalidateOnlyGenerated", "has", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "Symbol", "options", "serialize", "require", "previous", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "expires", "Date", "httpOnly", "sameSite", "process", "env", "NODE_ENV", "secure", "path", "undefined", "Object", "defineProperty", "value", "enumerable", "constructor", "message", "statusMessage", "prop", "getter", "opts", "configurable", "optsReset", "writable"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;IAkKaA,QAAQ;eAARA;;IAjEAC,4BAA4B;eAA5BA;;IACAC,0BAA0B;eAA1BA;;IAEAC,sBAAsB;eAAtBA;;IAGAC,sBAAsB;eAAtBA;;IADAC,mBAAmB;eAAnBA;;IAxBGC,yBAAyB;eAAzBA;;IA2BAC,gBAAgB;eAAhBA;;IA/CAC,QAAQ;eAARA;;IAuHAC,SAAS;eAATA;;IArIAC,cAAc;eAAdA;;IAyJAC,WAAW;eAAXA;;IA/KAC,cAAc;eAAdA;;;yBAjBe;2BAIxB;wBACmB;4BACD;AAWlB,SAASA,eACdC,IAAY,EACZC,OAAU;IAEV,OAAQ,CAAC,GAAGC;YACVC;SAAAA,mCAAAA,IAAAA,iBAAS,IAAGC,qBAAqB,uBAAjCD,iCAAqCE,GAAG,CAAC,cAAcL;QACvD,wBAAwB;QACxB,OAAOG,IAAAA,iBAAS,IAAGG,KAAK,CACtBC,oBAAQ,CAACC,UAAU,EACnB;YACEC,UAAU,CAAC,4BAA4B,EAAET,KAAK,CAAC;QACjD,GACA,IAAMC,WAAWC;IAErB;AACF;AAOO,SAASL,eACda,GAAoB,EACpBC,UAAkB;IAElBD,IAAIC,UAAU,GAAGA;IACjB,OAAOD;AACT;AAQO,SAASf,SACde,GAAoB,EACpBE,WAA4B,EAC5BC,GAAY;IAEZ,IAAI,OAAOD,gBAAgB,UAAU;QACnCC,MAAMD;QACNA,cAAc;IAChB;IACA,IAAI,OAAOA,gBAAgB,YAAY,OAAOC,QAAQ,UAAU;QAC9D,MAAM,IAAIC,MACR,CAAC,qKAAqK,CAAC;IAE3K;IACAJ,IAAIK,SAAS,CAACH,aAAa;QAAEI,UAAUH;IAAI;IAC3CH,IAAIO,KAAK,CAACJ;IACVH,IAAIQ,GAAG;IACP,OAAOR;AACT;AAEO,SAASjB,0BACd0B,GAAgD,EAChDC,YAA+B;IAK/B,MAAMC,UAAUC,uBAAc,CAACC,IAAI,CAACJ,IAAIE,OAAO;IAE/C,MAAMG,gBAAgBH,QAAQI,GAAG,CAACC,sCAA2B;IAC7D,MAAMC,uBAAuBH,kBAAkBJ,aAAaI,aAAa;IAEzE,MAAMI,0BAA0BP,QAAQQ,GAAG,CACzCC,qDAA0C;IAG5C,OAAO;QAAEH;QAAsBC;IAAwB;AACzD;AAEO,MAAMxC,+BAA+B,CAAC,kBAAkB,CAAC;AACzD,MAAMC,6BAA6B,CAAC,mBAAmB,CAAC;AAExD,MAAMC,yBAAyB,IAAI,OAAO;AAE1C,MAAME,sBAAsBuC,OAAO1C;AACnC,MAAME,yBAAyBwC,OAAO3C;AAEtC,SAASM,iBACdgB,GAAuB,EACvBsB,UAEI,CAAC,CAAC;IAEN,IAAIzC,0BAA0BmB,KAAK;QACjC,OAAOA;IACT;IAEA,MAAM,EAAEuB,SAAS,EAAE,GACjBC,QAAQ;IACV,MAAMC,WAAWzB,IAAI0B,SAAS,CAAC;IAC/B1B,IAAI2B,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAOF,aAAa,WACpB;YAACA;SAAS,GACVG,MAAMC,OAAO,CAACJ,YACdA,WACA,EAAE;QACNF,UAAU7C,8BAA8B,IAAI;YAC1C,2DAA2D;YAC3D,oDAAoD;YACpD,wEAAwE;YACxEoD,SAAS,IAAIC,KAAK;YAClBC,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCE,MAAM;YACN,GAAIhB,QAAQgB,IAAI,KAAKC,YAChB;gBAAED,MAAMhB,QAAQgB,IAAI;YAAC,IACtBC,SAAS;QACf;QACAhB,UAAU5C,4BAA4B,IAAI;YACxC,2DAA2D;YAC3D,oDAAoD;YACpD,wEAAwE;YACxEmD,SAAS,IAAIC,KAAK;YAClBC,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCE,MAAM;YACN,GAAIhB,QAAQgB,IAAI,KAAKC,YAChB;gBAAED,MAAMhB,QAAQgB,IAAI;YAAC,IACtBC,SAAS;QACf;KACD;IAEDC,OAAOC,cAAc,CAACzC,KAAKnB,wBAAwB;QACjD6D,OAAO;QACPC,YAAY;IACd;IACA,OAAO3C;AACT;AAKO,MAAMvB,iBAAiB2B;IAG5BwC,YAAY3C,UAAkB,EAAE4C,OAAe,CAAE;QAC/C,KAAK,CAACA;QACN,IAAI,CAAC5C,UAAU,GAAGA;IACpB;AACF;AAQO,SAASf,UACdc,GAAoB,EACpBC,UAAkB,EAClB4C,OAAe;IAEf7C,IAAIC,UAAU,GAAGA;IACjBD,IAAI8C,aAAa,GAAGD;IACpB7C,IAAIQ,GAAG,CAACqC;AACV;AAYO,SAASzD,YACd,EAAEqB,GAAG,EAAa,EAClBsC,IAAY,EACZC,MAAe;IAEf,MAAMC,OAAO;QAAEC,cAAc;QAAMP,YAAY;IAAK;IACpD,MAAMQ,YAAY;QAAE,GAAGF,IAAI;QAAEG,UAAU;IAAK;IAE5CZ,OAAOC,cAAc,CAAChC,KAAKsC,MAAM;QAC/B,GAAGE,IAAI;QACPlC,KAAK;YACH,MAAM2B,QAAQM;YACd,8DAA8D;YAC9DR,OAAOC,cAAc,CAAChC,KAAKsC,MAAM;gBAAE,GAAGI,SAAS;gBAAET;YAAM;YACvD,OAAOA;QACT;QACA/C,KAAK,CAAC+C;YACJF,OAAOC,cAAc,CAAChC,KAAKsC,MAAM;gBAAE,GAAGI,SAAS;gBAAET;YAAM;QACzD;IACF;AACF"}