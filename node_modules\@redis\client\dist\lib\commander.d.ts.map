{"version": 3, "file": "commander.d.ts", "sourceRoot": "", "sources": ["../../lib/commander.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAE5L,UAAU,mBAAmB,CAC3B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY;IAEzB,SAAS,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC;IACrC,QAAQ,EAAE,aAAa,CAAC;IACxB,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC;IAC3E,mBAAmB,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC;IACjF,qBAAqB,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,YAAY,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC;IAClG,mBAAmB,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,YAAY,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC;IACpF,MAAM,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;CACzC;AAOD,wBAAgB,YAAY,CAC1B,CAAC,SAAS,YAAY,EACtB,CAAC,SAAS,cAAc,EACxB,CAAC,SAAS,YAAY,EACtB,IAAI,SAAS,YAAY,EACzB,EACA,SAAS,EACT,QAAQ,EACR,aAAa,EACb,mBAAmB,EACnB,qBAAqB,EACrB,mBAAmB,EACnB,MAAM,EACP,EAAE,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OA6CpC;AAaD,wBAAgB,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,GAAG,cAAc,GAAG,SAAS,CAQlG;AAED,wBAAgB,uBAAuB,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,aAAa,mBAWtE;AAED,wBAAgB,qBAAqB,CAAC,MAAM,EAAE,WAAW,uBAWxD"}