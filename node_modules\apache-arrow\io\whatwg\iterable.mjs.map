{"version": 3, "sources": ["io/whatwg/iterable.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;AAErB,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAEpD,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AAOnE,cAAc;AACd,MAAM,UAAU,WAAW,CAAI,MAAsC,EAAE,OAAkC;IACrG,IAAI,eAAe,CAAI,MAAM,CAAC,EAAE,CAAC;QAAC,OAAO,gCAAgC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAAC,CAAC;IAC7F,IAAI,UAAU,CAAI,MAAM,CAAC,EAAE,CAAC;QAAC,OAAO,2BAA2B,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAAC,CAAC;IACnF,0BAA0B;IAC1B,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;AACtF,CAAC;AAED,cAAc;AACd,SAAS,2BAA2B,CAAI,MAAmB,EAAE,OAAkC;IAE3F,IAAI,EAAE,GAA6B,IAAI,CAAC;IACxC,MAAM,EAAE,GAAG,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,MAAK,OAAO,CAAC,IAAI,KAAK,CAAC;IAChD,MAAM,GAAG,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,KAAI,CAAC,SAAA,CAAC,EAAI,EAAE,CAAA,CAAC,CAAC;IAEhD,OAAO,IAAI,cAAc,iCAClB,OAAc,KACjB,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;QACpG,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACtE,MAAM,KAAK,CAAC,CAAA,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,MAAM,KAAI,EAAE,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,qBAC/D,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,IAAK,OAAO,EAAG,CAAC;IAExD,SAAS,IAAI,CAAC,UAA8C,EAAE,EAAqB;QAC/E,IAAI,GAAe,CAAC;QACpB,IAAI,CAAC,GAA6B,IAAI,CAAC;QACvC,IAAI,IAAI,GAAG,UAAU,CAAC,WAAW,IAAI,IAAI,CAAC;QAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC3C,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC/D,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;gBACzD,CAAC,CAAC,KAAK,GAAQ,GAAG,CAAC;YACvB,CAAC;YACD,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC;gBAAC,OAAO;YAAC,CAAC;QAChD,CAAC;QACD,UAAU,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;AACL,CAAC;AAED,cAAc;AACd,SAAS,gCAAgC,CAAI,MAAwB,EAAE,OAAkC;IAErG,IAAI,EAAE,GAAkC,IAAI,CAAC;IAC7C,MAAM,EAAE,GAAG,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,MAAK,OAAO,CAAC,IAAI,KAAK,CAAC;IAChD,MAAM,GAAG,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,KAAI,CAAC,SAAA,CAAC,EAAI,EAAE,CAAA,CAAC,CAAC;IAEhD,OAAO,IAAI,cAAc,iCAClB,OAAc,KACX,KAAK,CAAC,UAAU;kEAAI,MAAM,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,EAA4B,CAAC,CAAC,CAAC,CAAC,CAAC;SAAA;QACpH,IAAI,CAAC,UAAU;kEAAI,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAAA;QAC5E,MAAM;kEAAK,CAAC,CAAA,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,MAAM,MAAI,MAAM,EAAE,CAAC,MAAM,EAAE,CAAA,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;SAAA,qBAC3E,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,IAAK,OAAO,EAAG,CAAC;IAExD,SAAe,IAAI,CAAC,UAA8C,EAAE,EAA0B;;YAC1F,IAAI,GAAe,CAAC;YACpB,IAAI,CAAC,GAA6B,IAAI,CAAC;YACvC,IAAI,IAAI,GAAG,UAAU,CAAC,WAAW,IAAI,IAAI,CAAC;YAC1C,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBACjD,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC/D,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;oBACzD,CAAC,CAAC,KAAK,GAAQ,GAAG,CAAC;gBACvB,CAAC;gBACD,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBAC5B,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC;oBAAC,OAAO;gBAAC,CAAC;YAChD,CAAC;YACD,UAAU,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;KAAA;AACL,CAAC", "file": "iterable.mjs", "sourceRoot": "../../src"}