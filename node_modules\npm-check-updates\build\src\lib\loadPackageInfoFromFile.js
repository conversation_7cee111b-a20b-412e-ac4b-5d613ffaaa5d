"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const promises_1 = __importDefault(require("fs/promises"));
const programError_1 = __importDefault(require("./programError"));
/** Load and parse a package file. */
const loadPackageInfoFromFile = async (options, filepath) => {
    let pkg, pkgFile;
    // assert package.json
    try {
        pkgFile = await promises_1.default.readFile(filepath, 'utf-8');
        pkg = JSON.parse(pkgFile);
    }
    catch (e) {
        (0, programError_1.default)(options, `Missing or invalid ${filepath}`);
    }
    return {
        name: undefined,
        pkg,
        pkgFile,
        filepath,
    };
};
exports.default = loadPackageInfoFromFile;
//# sourceMappingURL=loadPackageInfoFromFile.js.map