import { Options } from '../types/Options';
/**
 * Shallow merge (specific or all) properties.
 * If some properties both are arrays, then merge them also.
 */
declare function mergeOptions(rawOptions1: Options | null, rawOptions2: Options | null): {
    cache?: boolean | undefined;
    cacheClear?: boolean | undefined;
    cacheExpiration?: number | undefined;
    cacheFile?: string | undefined;
    color?: boolean | undefined;
    concurrency?: number | undefined;
    configFileName?: string | undefined;
    configFilePath?: string | undefined;
    cwd?: string | undefined;
    deep?: boolean | undefined;
    dep?: string | string[] | undefined;
    deprecated?: boolean | undefined;
    doctor?: boolean | undefined;
    doctorInstall?: string | undefined;
    doctorTest?: string | undefined;
    enginesNode?: boolean | undefined;
    errorLevel?: number | undefined;
    filter?: string | RegExp | import("../types/FilterFunction").FilterFunction | (string | RegExp)[] | undefined;
    filterResults?: import("../types/FilterResultsFunction").FilterResultsFunction | undefined;
    filterVersion?: string | RegExp | import("../types/FilterFunction").FilterFunction | (string | RegExp)[] | undefined;
    format?: string[] | undefined;
    global?: boolean | undefined;
    groupFunction?: import("../types/GroupFunction").GroupFunction | undefined;
    install?: "always" | "never" | "prompt" | undefined;
    interactive?: boolean | undefined;
    jsonAll?: boolean | undefined;
    jsonDeps?: boolean | undefined;
    jsonUpgraded?: boolean | undefined;
    loglevel?: string | undefined;
    mergeConfig?: boolean | undefined;
    minimal?: boolean | undefined;
    packageData?: string | (import("../types/PackageFile").PackageFile & string) | undefined;
    packageFile?: string | undefined;
    packageManager?: "npm" | "yarn" | "pnpm" | "deno" | "bun" | "staticRegistry" | undefined;
    peer?: boolean | undefined;
    pre?: boolean | undefined;
    prefix?: string | undefined;
    registry?: string | undefined;
    registryType?: "npm" | "json" | undefined;
    reject?: string | RegExp | import("../types/FilterFunction").FilterFunction | (string | RegExp)[] | undefined;
    rejectVersion?: string | RegExp | import("../types/FilterFunction").FilterFunction | (string | RegExp)[] | undefined;
    removeRange?: boolean | undefined;
    retry?: number | undefined;
    root?: boolean | undefined;
    silent?: boolean | undefined;
    stdin?: string | undefined;
    target?: "minor" | "patch" | `@${string}` | import("../types/TargetFunction").TargetFunction | "latest" | "newest" | "greatest" | "semver" | undefined;
    timeout?: number | undefined;
    upgrade?: boolean | undefined;
    verbose?: boolean | undefined;
    workspace?: string[] | undefined;
    workspaces?: boolean | undefined;
    args?: any[] | undefined;
    cacher?: import("../types/Cacher").Cacher | undefined;
    cli?: boolean | undefined;
    distTag?: string | undefined;
    json?: boolean | undefined;
    nodeEngineVersion?: string | undefined;
    peerDependencies?: import("../types/IndexType").Index<any> | undefined;
    rcConfigPath?: string | undefined;
    workspacePackages?: string[] | undefined;
};
export default mergeOptions;
