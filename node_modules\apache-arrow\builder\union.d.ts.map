{"version": 3, "sources": ["builder/union.ts"], "names": [], "mappings": "AAkBA,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,MAAM,eAAe,CAAC;AACxD,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AAE5D,MAAM,WAAW,mBAAmB,CAAC,CAAC,SAAS,KAAK,GAAG,GAAG,EAAE,KAAK,GAAG,GAAG,CAAE,SAAQ,cAAc,CAAC,CAAC,EAAE,KAAK,CAAC;IACrG,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,CAAC;CAChG;AAED,cAAc;AACd,8BAAsB,YAAY,CAAC,CAAC,SAAS,KAAK,EAAE,KAAK,GAAG,GAAG,CAAE,SAAQ,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC;IAEtF,SAAS,CAAC,QAAQ,EAAE,iBAAiB,CAAC,SAAS,CAAC,CAAC;gBAErC,OAAO,EAAE,mBAAmB,CAAC,CAAC,EAAE,KAAK,CAAC;IAQlD,IAAW,kBAAkB;;MAA2C;IAEjE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,KAAK,EAAE,WAAW,CAAC,EAAE,MAAM;IAIvD,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,KAAK,EAAE,WAAW,CAAC,EAAE,MAAM;IAQnE,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,EAAE,MAAM;IAOhE,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,SAA4B;IAQhE,cAAc;IAEd,SAAS,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM;CAKrG;AAED,cAAc;AACd,qBAAa,kBAAkB,CAAC,CAAC,SAAS,WAAW,EAAE,KAAK,GAAG,GAAG,CAAE,SAAQ,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC;CAAI;AACtG,cAAc;AACd,qBAAa,iBAAiB,CAAC,CAAC,SAAS,UAAU,EAAE,KAAK,GAAG,GAAG,CAAE,SAAQ,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC;IAE5F,SAAS,CAAC,QAAQ,EAAE,iBAAiB,CAAC,UAAU,CAAC,CAAC;gBAEtC,OAAO,EAAE,mBAAmB,CAAC,CAAC,EAAE,KAAK,CAAC;IAKlD,cAAc;IACP,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,EAAE,MAAM;CAM1E", "file": "union.d.ts", "sourceRoot": "../src"}