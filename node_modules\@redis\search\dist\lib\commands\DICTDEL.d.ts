import { CommandParser } from '@redis/client/dist/lib/client/parser';
import { RedisArgument, NumberReply } from '@redis/client/dist/lib/RESP/types';
import { RedisVariadicArgument } from '@redis/client/dist/lib/commands/generic-transformers';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Deletes terms from a dictionary.
     * @param parser - The command parser
     * @param dictionary - Name of the dictionary to remove terms from
     * @param term - One or more terms to delete from the dictionary
     */
    readonly parseCommand: (this: void, parser: CommandParser, dictionary: RedisArgument, term: RedisVariadicArgument) => void;
    readonly transformReply: () => NumberReply;
};
export default _default;
//# sourceMappingURL=DICTDEL.d.ts.map