"use strict";
// automatically generated by the FlatBuffers compiler, do not modify
Object.defineProperty(exports, "__esModule", { value: true });
exports.RunEndEncoded = void 0;
const flatbuffers = require("flatbuffers");
/**
 * Contains two child arrays, run_ends and values.
 * The run_ends child array must be a 16/32/64-bit integer array
 * which encodes the indices at which the run with the value in
 * each corresponding index in the values child array ends.
 * Like list/struct types, the value array can be of any type.
 */
class RunEndEncoded {
    constructor() {
        this.bb = null;
        this.bb_pos = 0;
    }
    __init(i, bb) {
        this.bb_pos = i;
        this.bb = bb;
        return this;
    }
    static getRootAsRunEndEncoded(bb, obj) {
        return (obj || new RunEndEncoded()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
    }
    static getSizePrefixedRootAsRunEndEncoded(bb, obj) {
        bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
        return (obj || new RunEndEncoded()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
    }
    static startRunEndEncoded(builder) {
        builder.startObject(0);
    }
    static endRunEndEncoded(builder) {
        const offset = builder.endObject();
        return offset;
    }
    static createRunEndEncoded(builder) {
        RunEndEncoded.startRunEndEncoded(builder);
        return RunEndEncoded.endRunEndEncoded(builder);
    }
}
exports.RunEndEncoded = RunEndEncoded;

//# sourceMappingURL=run-end-encoded.js.map
