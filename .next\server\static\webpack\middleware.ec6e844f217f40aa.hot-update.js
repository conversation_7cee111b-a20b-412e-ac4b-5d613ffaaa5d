"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n// Define protected routes\nconst protectedRoutes = [\n    \"/dashboard\",\n    \"/osint\",\n    \"/scanner\",\n    \"/file-analyzer\",\n    \"/cve\",\n    \"/dorking\",\n    \"/playground\",\n    \"/tools\",\n    \"/bot\",\n    \"/leaderboard\",\n    \"/profile\",\n    \"/plan\",\n    \"/admin\"\n];\n// Define admin-only routes\nconst adminRoutes = [\n    \"/admin\"\n];\n// Define public routes that should redirect to dashboard if authenticated\nconst publicRoutes = [\n    \"/login\",\n    \"/register\"\n];\nasync function middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Skip middleware for API routes, static files, and other assets\n    if (pathname.startsWith(\"/api/\") || pathname.startsWith(\"/_next/\") || pathname.startsWith(\"/favicon.ico\") || pathname.includes(\".\")) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Get authentication status\n    const authResult = await SimpleAuthService.authenticateRequest(request);\n    const isAuthenticated = authResult.success;\n    const user = authResult.user;\n    console.log(`🔐 Middleware: ${pathname} - Auth: ${isAuthenticated} - User: ${user?.username || \"none\"}`);\n    // Handle protected routes\n    if (protectedRoutes.some((route)=>pathname.startsWith(route))) {\n        if (!isAuthenticated) {\n            console.log(`❌ Redirecting unauthenticated user from ${pathname} to /login`);\n            const loginUrl = new URL(\"/login\", request.url);\n            loginUrl.searchParams.set(\"redirect\", pathname);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(loginUrl);\n        }\n        // Check admin routes\n        if (adminRoutes.some((route)=>pathname.startsWith(route))) {\n            if (user?.role !== \"admin\" && user?.role !== \"super_admin\") {\n                console.log(`❌ Unauthorized access attempt to ${pathname} by ${user?.username}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n            }\n        }\n        // User is authenticated and authorized, continue\n        console.log(`✅ Authorized access to ${pathname} by ${user?.username}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Handle public routes (login, register)\n    if (publicRoutes.some((route)=>pathname === route)) {\n        if (isAuthenticated) {\n            console.log(`🔄 Redirecting authenticated user from ${pathname} to /dashboard`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n        }\n        // User is not authenticated, allow access to public routes\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Handle root route\n    if (pathname === \"/\") {\n        if (isAuthenticated) {\n            console.log(`🔄 Redirecting authenticated user from / to /dashboard`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n        } else {\n            console.log(`🔄 Redirecting unauthenticated user from / to /login`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/login\", request.url));\n        }\n    }\n    // For all other routes, continue normally\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder files\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|.*\\\\..*$).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});