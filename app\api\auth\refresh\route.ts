import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Token refresh attempt started')

    const body = await request.json()
    const { refreshToken } = body

    if (!refreshToken) {
      // Try to get from cookies
      const cookies = request.headers.get('cookie')
      let cookieRefreshToken = null

      if (cookies) {
        const tokenMatch = cookies.match(/refreshToken=([^;]+)/)
        cookieRefreshToken = tokenMatch ? tokenMatch[1] : null
      }

      if (!cookieRefreshToken) {
        return NextResponse.json({
          success: false,
          error: 'No refresh token provided'
        }, { status: 401 })
      }
    }

    const tokenToUse = refreshToken || cookieRefreshToken

    // Refresh the token
    const result = await RealAuthService.refreshToken(tokenToUse)

    if (!result.success) {
      console.log('❌ Token refresh failed:', result.message)
      return NextResponse.json({
        success: false,
        error: result.message || 'Token refresh failed'
      }, { status: 401 })
    }

    console.log('✅ Token refresh successful for user:', result.user?.id)

    const response = NextResponse.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        user: {
          id: result.user!.id,
          username: result.user!.username,
          email: result.user!.email,
          fullName: result.user!.full_name,
          role: result.user!.role,
          plan: result.user!.plan,
          level: result.user!.level,
          score: result.user!.score,
          streak: result.user!.streak_days,
          emailVerified: result.user!.email_verified,
          lastActive: result.user!.last_active,
          createdAt: result.user!.created_at
        },
        tokens: {
          accessToken: result.tokens!.accessToken,
          refreshToken: result.tokens!.refreshToken,
          expiresIn: result.tokens!.expiresIn
        }
      }
    })

    // Update cookies
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      path: '/'
    }

    response.cookies.set('accessToken', result.tokens!.accessToken, {
      ...cookieOptions,
      maxAge: 24 * 60 * 60 // 24 hours
    })

    response.cookies.set('refreshToken', result.tokens!.refreshToken, {
      ...cookieOptions,
      maxAge: 7 * 24 * 60 * 60 // 7 days
    })

    response.cookies.set('user', JSON.stringify({
      id: result.user!.id,
      username: result.user!.username,
      email: result.user!.email,
      role: result.user!.role,
      plan: result.user!.plan
    }), {
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      path: '/',
      maxAge: 24 * 60 * 60 // 24 hours
    })

    return response

  } catch (error) {
    console.error('❌ Token refresh error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'An unexpected error occurred during token refresh'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
