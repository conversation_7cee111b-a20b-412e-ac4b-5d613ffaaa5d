/// <reference types="node" />
/// <reference types="node" />
import { Binary<PERSON>ike, KeyLike } from 'crypto';
export declare function createPublicKey(key: string | Buffer): KeyLike;
export declare function verifyBlob(data: Buffer, key: <PERSON><PERSON><PERSON>, signature: <PERSON>uffer, algorithm?: string): boolean;
export declare function hash(data: BinaryLike): Buffer;
export declare function randomBytes(count: number): Buffer;
export declare function bufferEqual(a: <PERSON><PERSON><PERSON>, b: Buffer): boolean;
