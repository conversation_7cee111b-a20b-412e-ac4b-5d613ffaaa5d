'use server'

import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { db } from '@/lib/database'
import { OSINTService } from '@/lib/services/osint'
import { z } from 'zod'

const npwpSchema = z.object({
  npwp: z.string().min(15).max(20).regex(/^[\d.-]+$/, 'NPWP must contain only digits, dots, or dashes'),
  sources: z.array(z.string()).optional()
})

export async function POST(request: NextRequest) {
  try {
    // Authenticate request
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    
    // Validate input
    const validatedData = npwpSchema.parse(body)
    
    // Check user's plan and rate limits
    const canPerformOsint = await checkUserLimits(authResult.user.id)
    if (!canPerformOsint.allowed) {
      return NextResponse.json(
        { success: false, message: canPerformOsint.message },
        { status: 429 }
      )
    }

    // Create OSINT query record
    const result = await db.query(
      `INSERT INTO osint_queries (user_id, type, value, sources, status, created_at)
       VALUES (?, ?, ?, ?, 'pending', NOW())`,
      [authResult.user.id, 'npwp', validatedData.npwp, JSON.stringify(validatedData.sources || ['all'])]
    )

    const queryId = (result as any).insertId

    // Start OSINT investigation
    const osintService = new OSINTService()

    // Run investigation in background
    osintService.investigate({
      id: queryId,
      type: 'npwp',
      value: validatedData.npwp,
      sources: validatedData.sources || ['all']
    }).then(async (results) => {
      // Update query record with results
      await db.query(
        `UPDATE osint_queries SET status = 'completed', results = ?, completed_at = NOW() WHERE id = ?`,
        [JSON.stringify(results), queryId]
      )

      // Award points to user
      await db.query(
        `UPDATE users SET score = score + 10 WHERE id = ?`,
        [authResult.user.id]
      )
    }).catch(async (error) => {
      // Update query record with error
      await db.query(
        `UPDATE osint_queries SET status = 'failed', error = ? WHERE id = ?`,
        [error.message, queryId]
      )
    })

    return NextResponse.json({
      success: true,
      message: 'NPWP investigation started',
      data: {
        queryId,
        status: 'pending'
      }
    })
  } catch (error) {
    console.error('NPWP investigation error:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'An error occurred',
        error: error instanceof z.ZodError ? error.errors : undefined
      },
      { status: 400 }
    )
  }
}

async function checkUserLimits(userId: number): Promise<{ allowed: boolean; message?: string }> {
  try {
    // Get user's plan
    const [userRows] = await db.query(
      'SELECT plan FROM users WHERE id = ?',
      [userId]
    )
    
    if (!userRows || (userRows as any[]).length === 0) {
      return { allowed: false, message: 'User not found' }
    }
    
    const user = (userRows as any[])[0]
    
    // Get today's OSINT query count
    const [countRows] = await db.query(
      `SELECT COUNT(*) as count FROM osint_queries 
       WHERE user_id = ? AND created_at >= CURDATE()`,
      [userId]
    )
    
    const count = (countRows as any[])[0].count
    
    // Check limits based on plan
    const limits: Record<string, number> = {
      'Free': 10,
      'Pro': 50,
      'Expert': 200,
      'Elite': 1000
    }
    
    const limit = limits[user.plan] || 10
    
    if (count >= limit) {
      return { 
        allowed: false, 
        message: `Daily OSINT query limit reached (${count}/${limit}). Upgrade your plan for more queries.`
      }
    }
    
    return { allowed: true }
  } catch (error) {
    console.error('Error checking user limits:', error)
    return { allowed: true } // Allow in case of error
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    endpoint: '/api/osint/npwp',
    method: 'POST',
    description: 'Investigate Indonesian NPWP (Tax ID)',
    parameters: {
      npwp: 'string (required) - 15-digit NPWP number (can include dots and dashes)',
      sources: 'array (optional) - List of sources to check'
    },
    example: {
      npwp: '12.345.678.9-012.345',
      sources: ['tax', 'leaked']
    }
  })
}
