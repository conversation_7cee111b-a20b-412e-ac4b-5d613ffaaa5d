"use strict";
// automatically generated by the FlatBuffers compiler, do not modify
Object.defineProperty(exports, "__esModule", { value: true });
exports.List = void 0;
const flatbuffers = require("flatbuffers");
class List {
    constructor() {
        this.bb = null;
        this.bb_pos = 0;
    }
    __init(i, bb) {
        this.bb_pos = i;
        this.bb = bb;
        return this;
    }
    static getRootAsList(bb, obj) {
        return (obj || new List()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
    }
    static getSizePrefixedRootAsList(bb, obj) {
        bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
        return (obj || new List()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
    }
    static startList(builder) {
        builder.startObject(0);
    }
    static endList(builder) {
        const offset = builder.endObject();
        return offset;
    }
    static createList(builder) {
        List.startList(builder);
        return List.endList(builder);
    }
}
exports.List = List;

//# sourceMappingURL=list.js.map
