'use client'

import { useState, useEffect } from 'react'
import AdminLayout from '@/components/AdminLayout'
import { Card, StatsCard } from '@/components/Card'
import { DataTable } from '@/components/Table'
import { Modal } from '@/components/Modal'
import { Loading } from '@/components/Loading'
import { useToast } from '@/components/Toast'
import { 
  MessageSquare, 
  Smartphone, 
  Wifi, 
  WifiOff,
  QrCode,
  Plus,
  Trash2,
  RefreshCw,
  Settings,
  Users,
  Activity,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react'

interface WhatsAppSession {
  id: string
  status: 'connected' | 'connecting' | 'disconnected' | 'error'
  lastActivity: string
  connectedAt?: string
  error?: string
  hasQrCode?: boolean
}

interface WhatsAppStats {
  totalSessions: number
  activeSessions: number
  totalMessages: number
  todayMessages: number
}

export default function AdminWhatsAppPage() {
  const [sessions, setSessions] = useState<WhatsAppSession[]>([])
  const [stats, setStats] = useState<WhatsAppStats>({
    totalSessions: 0,
    activeSessions: 0,
    totalMessages: 0,
    todayMessages: 0
  })
  const [loading, setLoading] = useState(true)
  const [showConnectModal, setShowConnectModal] = useState(false)
  const [showQrModal, setShowQrModal] = useState(false)
  const [selectedSession, setSelectedSession] = useState<string>('')
  const [qrCode, setQrCode] = useState<string>('')
  const [connectingBot, setConnectingBot] = useState(false)
  const { showToast } = useToast()

  useEffect(() => {
    loadWhatsAppData()
    // Refresh data every 30 seconds
    const interval = setInterval(loadWhatsAppData, 30000)
    return () => clearInterval(interval)
  }, [])

  const loadWhatsAppData = async () => {
    try {
      // Load WhatsApp sessions
      const response = await fetch('/api/bots/whatsapp/connect')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setSessions(data.data.sessions || [])
          setStats({
            totalSessions: data.data.sessions?.length || 0,
            activeSessions: data.data.sessions?.filter((s: any) => s.status === 'connected').length || 0,
            totalMessages: 1250, // Mock data
            todayMessages: 89 // Mock data
          })
        }
      }
    } catch (error) {
      console.error('Failed to load WhatsApp data:', error)
      showToast('Failed to load WhatsApp data', 'error')
    } finally {
      setLoading(false)
    }
  }

  const handleConnectWhatsApp = async () => {
    setConnectingBot(true)
    try {
      const response = await fetch('/api/bots/whatsapp/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionName: `kodexguard-wa-${Date.now()}`
        })
      })

      const data = await response.json()
      
      if (data.success) {
        showToast('WhatsApp bot connection initiated', 'success')
        setShowConnectModal(false)
        await loadWhatsAppData()
        
        // Check for QR code after a short delay
        setTimeout(() => {
          checkForQrCode(data.data.sessionId)
        }, 2000)
      } else {
        showToast(data.message || 'Failed to connect WhatsApp bot', 'error')
      }
    } catch (error) {
      console.error('WhatsApp connection error:', error)
      showToast('Failed to connect WhatsApp bot', 'error')
    } finally {
      setConnectingBot(false)
    }
  }

  const checkForQrCode = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/bots/whatsapp/qr?sessionId=${sessionId}`)
      const data = await response.json()
      
      if (data.success && data.data.qrCode) {
        setQrCode(data.data.qrCode)
        setSelectedSession(sessionId)
        setShowQrModal(true)
      }
    } catch (error) {
      console.error('Failed to get QR code:', error)
    }
  }

  const handleDisconnectBot = async (sessionId: string) => {
    try {
      const response = await fetch('/api/bots/whatsapp/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ sessionId })
      })

      const data = await response.json()
      
      if (data.success) {
        showToast('Bot disconnected successfully', 'success')
        await loadWhatsAppData()
      } else {
        showToast(data.message || 'Failed to disconnect bot', 'error')
      }
    } catch (error) {
      console.error('Disconnect error:', error)
      showToast('Failed to disconnect bot', 'error')
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'connecting':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'disconnected':
        return <WifiOff className="h-4 w-4 text-gray-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <WifiOff className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'text-green-500'
      case 'connecting':
        return 'text-yellow-500'
      case 'disconnected':
        return 'text-gray-500'
      case 'error':
        return 'text-red-500'
      default:
        return 'text-gray-500'
    }
  }

  const columns = [
    {
      key: 'id',
      label: 'Session ID',
      render: (value: string) => (
        <span className="font-mono text-sm">{value.substring(0, 12)}...</span>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value: string) => (
        <div className="flex items-center space-x-2">
          {getStatusIcon(value)}
          <span className={`capitalize ${getStatusColor(value)}`}>{value}</span>
        </div>
      )
    },
    {
      key: 'lastActivity',
      label: 'Last Activity',
      render: (value: string) => new Date(value).toLocaleString()
    },
    {
      key: 'connectedAt',
      label: 'Connected At',
      render: (value: string) => value ? new Date(value).toLocaleString() : '-'
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, row: WhatsAppSession) => (
        <div className="flex items-center space-x-2">
          {row.hasQrCode && row.status === 'connecting' && (
            <button
              onClick={() => checkForQrCode(row.id)}
              className="p-1 text-blue-600 hover:text-blue-800"
              title="Show QR Code"
            >
              <QrCode className="h-4 w-4" />
            </button>
          )}
          {row.status === 'connected' && (
            <button
              onClick={() => handleDisconnectBot(row.id)}
              className="p-1 text-red-600 hover:text-red-800"
              title="Disconnect"
            >
              <WifiOff className="h-4 w-4" />
            </button>
          )}
        </div>
      )
    }
  ]

  if (loading) {
    return (
      <AdminLayout>
        <Loading />
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">WhatsApp Bot Management</h1>
            <p className="text-gray-600">Manage WhatsApp bot connections and sessions</p>
          </div>
          <button
            onClick={() => setShowConnectModal(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Connect WhatsApp</span>
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <StatsCard
            title="Total Sessions"
            value={stats.totalSessions.toString()}
            icon={Activity}
            color="blue"
          />
          <StatsCard
            title="Active Sessions"
            value={stats.activeSessions.toString()}
            icon={Wifi}
            color="green"
          />
          <StatsCard
            title="Total Messages"
            value={stats.totalMessages.toLocaleString()}
            icon={MessageSquare}
            color="purple"
          />
          <StatsCard
            title="Today's Messages"
            value={stats.todayMessages.toString()}
            icon={Users}
            color="orange"
          />
        </div>

        {/* WhatsApp Sessions Table */}
        <Card>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">WhatsApp Sessions</h2>
            <button
              onClick={loadWhatsAppData}
              className="btn-secondary flex items-center space-x-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </button>
          </div>
          
          <DataTable
            columns={columns}
            data={sessions}
            searchable={true}
            emptyMessage="No WhatsApp sessions found"
          />
        </Card>

        {/* Connect WhatsApp Modal */}
        <Modal
          isOpen={showConnectModal}
          onClose={() => setShowConnectModal(false)}
          title="Connect WhatsApp Bot"
        >
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg">
              <MessageSquare className="h-8 w-8 text-blue-500" />
              <div>
                <h3 className="font-medium text-gray-900">WhatsApp Business API</h3>
                <p className="text-sm text-gray-600">Connect your WhatsApp account to enable bot features</p>
              </div>
            </div>
            
            <div className="text-sm text-gray-600 space-y-2">
              <p><strong>Features:</strong></p>
              <ul className="list-disc list-inside space-y-1">
                <li>Automated OSINT investigations via chat</li>
                <li>Vulnerability scanning commands</li>
                <li>File analysis through WhatsApp</li>
                <li>Real-time notifications</li>
                <li>User account management</li>
              </ul>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowConnectModal(false)}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                onClick={handleConnectWhatsApp}
                disabled={connectingBot}
                className="btn-primary flex items-center space-x-2"
              >
                {connectingBot ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Connecting...</span>
                  </>
                ) : (
                  <>
                    <MessageSquare className="h-4 w-4" />
                    <span>Connect WhatsApp</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </Modal>

        {/* QR Code Modal */}
        <Modal
          isOpen={showQrModal}
          onClose={() => setShowQrModal(false)}
          title="WhatsApp QR Code"
        >
          <div className="text-center space-y-4">
            <p className="text-gray-600">
              Scan this QR code with WhatsApp to connect the bot:
            </p>
            
            {qrCode && (
              <div className="flex justify-center">
                <img 
                  src={qrCode} 
                  alt="WhatsApp QR Code" 
                  className="max-w-xs border border-gray-200 rounded-lg"
                />
              </div>
            )}
            
            <div className="text-sm text-gray-500 space-y-1">
              <p><strong>Instructions:</strong></p>
              <p>1. Open WhatsApp on your phone</p>
              <p>2. Go to Settings → Linked Devices</p>
              <p>3. Tap "Link a Device"</p>
              <p>4. Scan this QR code</p>
            </div>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <p className="text-sm text-yellow-800">
                <strong>Note:</strong> This QR code will expire in 60 seconds. 
                If it expires, close this dialog and try connecting again.
              </p>
            </div>
          </div>
        </Modal>
      </div>
    </AdminLayout>
  )
}
