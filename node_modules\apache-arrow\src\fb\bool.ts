// automatically generated by the FlatBuffers compiler, do not modify

import * as flatbuffers from 'flatbuffers';

export class Bool {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):Bool {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsBool(bb:flatbuffers.ByteBuffer, obj?:Bool):Bool {
  return (obj || new Bool()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsBool(bb:flatbuffers.ByteBuffer, obj?:Bool):Bool {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new Bool()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static startBool(builder:flatbuffers.Builder) {
  builder.startObject(0);
}

static endBool(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

static createBool(builder:flatbuffers.Builder):flatbuffers.Offset {
  Bool.startBool(builder);
  return Bool.endBool(builder);
}
}
