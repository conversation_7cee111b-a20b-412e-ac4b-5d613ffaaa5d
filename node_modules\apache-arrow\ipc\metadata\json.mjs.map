{"version": 3, "sources": ["ipc/metadata/json.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;AAErB,gCAAgC;AAEhC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EACO,UAAU,EACpB,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAC9D,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EACxC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAe,KAAK,EAAS,QAAQ,GAChG,MAAM,eAAe,CAAC;AAEvB,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AACrF,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AAEvF,cAAc;AACd,MAAM,UAAU,cAAc,CAAC,OAAY,EAAE,eAAsC,IAAI,GAAG,EAAE;IACxF,OAAO,IAAI,MAAM,CACb,oBAAoB,CAAC,OAAO,EAAE,YAAY,CAAC,EAC3C,sBAAsB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EAC3C,YAAY,CACf,CAAC;AACN,CAAC;AAED,cAAc;AACd,MAAM,UAAU,mBAAmB,CAAC,CAAM;IACtC,OAAO,IAAI,WAAW,CAClB,CAAC,CAAC,OAAO,CAAC,EACV,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAChC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAChC,CAAC;AACN,CAAC;AAED,cAAc;AACd,MAAM,UAAU,uBAAuB,CAAC,CAAM;IAC1C,OAAO,IAAI,eAAe,CACtB,mBAAmB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAC9B,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CACxB,CAAC;AACN,CAAC;AAED,cAAc;AACd,SAAS,oBAAoB,CAAC,OAAY,EAAE,YAAoC;IAC5E,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;AACtG,CAAC;AAED,cAAc;AACd,SAAS,qBAAqB,CAAC,MAAW,EAAE,YAAoC;IAC5E,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;AACvG,CAAC;AAED,cAAc;AACd,SAAS,kBAAkB,CAAC,EAAS;IACjC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,MAAM,CAAc,CAAC,UAAU,EAAE,MAAW,EAAE,EAAE,CAAC;QAC/D,GAAG,UAAU;QACb,IAAI,SAAS,CACT,MAAM,CAAC,OAAO,CAAC,EACf,iBAAiB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CACxC;QACD,GAAG,kBAAkB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;KAC5C,EAAE,EAAiB,CAAC,CAAC;AAC1B,CAAC;AAED,cAAc;AACd,SAAS,eAAe,CAAC,EAAS,EAAE,UAA0B,EAAE;IAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;QAC/C,MAAM,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAChG,MAAM,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9F,MAAM,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAC5F,MAAM,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QACxF,OAAO,GAAG,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACD,OAAO,OAAO,CAAC;AACnB,CAAC;AAED,cAAc;AACd,SAAS,iBAAiB,CAAC,QAAkB;IACzC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACxE,CAAC;AAED,cAAc;AACd,MAAM,UAAU,aAAa,CAAC,MAAW,EAAE,YAAoC;IAE3E,IAAI,EAAU,CAAC;IACf,IAAI,IAAkB,CAAC;IACvB,IAAI,KAAmB,CAAC;IACxB,IAAI,QAAa,CAAC;IAClB,IAAI,IAAmB,CAAC;IACxB,IAAI,QAAoB,CAAC;IAEzB,4BAA4B;IAC5B,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC;QACtD,IAAI,GAAG,YAAY,CAAC,MAAM,EAAE,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC;QACzE,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,EAAE,sBAAsB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC5G,CAAC;IACD,iFAAiF;IACjF,gFAAgF;IAChF,2CAA2C;SACtC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QAC9C,kEAAkE;QAClE,IAAI,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAU,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC;QACvF,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,YAAY,CAAC,MAAM,EAAE,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;QAC/F,QAAQ,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;QACjE,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC,EAAE,sBAAsB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAChH,CAAC;IACD,gGAAgG;IAChG,yDAAyD;SACpD,CAAC;QACF,kEAAkE;QAClE,IAAI,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAU,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC;QACvF,QAAQ,GAAG,IAAI,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAE,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;QAClF,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC,EAAE,sBAAsB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAChH,CAAC;IACD,OAAO,KAAK,IAAI,IAAI,CAAC;AACzB,CAAC;AAED,cAAc;AACd,SAAS,sBAAsB,CAAC,WAA6C,EAAE;IAC3E,OAAO,IAAI,GAAG,CAAiB,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACnF,CAAC;AAED,cAAc;AACd,SAAS,iBAAiB,CAAC,KAAU;IACjC,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AACzD,CAAC;AAED,cAAc;AACd,SAAS,YAAY,CAAC,CAAM,EAAE,QAAkB;IAE5C,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC;IAEjC,QAAQ,MAAM,EAAE,CAAC;QACb,KAAK,MAAM,CAAC,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;QAC/B,KAAK,MAAM,CAAC,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;QAC/B,KAAK,QAAQ,CAAC,CAAC,OAAO,IAAI,MAAM,EAAE,CAAC;QACnC,KAAK,aAAa,CAAC,CAAC,OAAO,IAAI,WAAW,EAAE,CAAC;QAC7C,KAAK,MAAM,CAAC,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;QAC/B,KAAK,WAAW,CAAC,CAAC,OAAO,IAAI,SAAS,EAAE,CAAC;QACzC,KAAK,MAAM,CAAC,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;QAC/B,KAAK,MAAM,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,KAAK,QAAQ,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;QACjD,KAAK,SAAS,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;IACtD,CAAC;IAED,QAAQ,MAAM,EAAE,CAAC;QACb,KAAK,KAAK,CAAC,CAAC,CAAC;YACT,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;YACpB,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,UAAU,CAAgB,CAAC,CAAC;QAChE,CAAC;QACD,KAAK,eAAe,CAAC,CAAC,CAAC;YACnB,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;YACpB,OAAO,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAQ,CAAC,CAAC;QACvD,CAAC;QACD,KAAK,SAAS,CAAC,CAAC,CAAC;YACb,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;YACpB,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;QAClE,CAAC;QACD,KAAK,MAAM,CAAC,CAAC,CAAC;YACV,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;YACpB,OAAO,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAQ,CAAC,CAAC;QACjD,CAAC;QACD,KAAK,MAAM,CAAC,CAAC,CAAC;YACV,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;YACpB,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAQ,EAAE,CAAC,CAAC,UAAU,CAAiB,CAAC,CAAC;QAC/E,CAAC;QACD,KAAK,WAAW,CAAC,CAAC,CAAC;YACf,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;YACpB,OAAO,IAAI,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAQ,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;QACpE,CAAC;QACD,KAAK,UAAU,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;YACpB,OAAO,IAAI,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAQ,CAAC,CAAC;QACxD,CAAC;QACD,KAAK,UAAU,CAAC,CAAC,CAAC;YACd,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;YACpB,OAAO,IAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAQ,CAAC,CAAC;QACpD,CAAC;QACD,KAAK,OAAO,CAAC,CAAC,CAAC;YACX,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;YACpB,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YAClD,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAA2B,CAAC;YACvE,OAAO,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,CAAQ,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC;QACnF,CAAC;QACD,KAAK,iBAAiB,CAAC,CAAC,CAAC;YACrB,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;YACpB,OAAO,IAAI,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;QAC/C,CAAC;QACD,KAAK,eAAe,CAAC,CAAC,CAAC;YACnB,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;YACpB,OAAO,IAAI,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC;QACD,KAAK,KAAK,CAAC,CAAC,CAAC;YACT,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;YACpB,OAAO,IAAI,IAAI,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;QAC1D,CAAC;IACL,CAAC;IACD,MAAM,IAAI,KAAK,CAAC,uBAAuB,MAAM,GAAG,CAAC,CAAC;AACtD,CAAC", "file": "json.mjs", "sourceRoot": "../../src"}