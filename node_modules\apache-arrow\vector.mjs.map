{"version": 3, "sources": ["vector.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;AAErB,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AACjC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AACzD,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,WAAW,CAAC;AACpD,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAa,MAAM,WAAW,CAAC;AAGtD,OAAO,EACH,cAAc,EACd,mBAAmB,EACnB,oBAAoB,EACpB,sBAAsB,EACtB,WAAW,EACX,gBAAgB,EAChB,gBAAgB,EAChB,kBAAkB,GACrB,MAAM,iBAAiB,CAAC;AAEzB,OAAO,EAAE,QAAQ,IAAI,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC1D,OAAO,EAAE,QAAQ,IAAI,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC1D,OAAO,EAAE,QAAQ,IAAI,cAAc,EAAE,MAAM,sBAAsB,CAAC;AAClE,OAAO,EAAE,QAAQ,IAAI,eAAe,EAAE,MAAM,uBAAuB,CAAC;AAoBpE,MAAM,gBAAgB,GAAG,EAAgE,CAAC;AAC1F,MAAM,wBAAwB,GAAG,EAA+B,CAAC;AAEjE;;GAEG;AACH,MAAM,OAAO,MAAM;IAEf,YAAY,KAAuC;;QAC/C,MAAM,IAAI,GAAc,KAAK,CAAC,CAAC,CAAC,YAAY,MAAM;YAC9C,CAAC,CAAE,KAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YAC7C,CAAC,CAAC,KAAkB,CAAC;QACzB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC;YAC9D,MAAM,IAAI,SAAS,CAAC,wDAAwD,CAAC,CAAC;QAClF,CAAC;QACD,MAAM,IAAI,GAAG,MAAA,IAAI,CAAC,CAAC,CAAC,0CAAE,IAAI,CAAC;QAC3B,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YAClB,KAAK,CAAC;gBAAE,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;gBAAC,MAAM;YACnC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACL,qCAAqC;gBACrC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC5D,MAAM,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBAE9B,IAAI,CAAC,OAAO,GAAG,CAAC,KAAa,EAAE,EAAE,CAAC,cAAc,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;gBACvE,IAAI,CAAC,GAAG,GAAG,CAAC,KAAa,EAAE,EAAE,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;gBACxD,IAAI,CAAC,GAAG,GAAG,CAAC,KAAa,EAAE,KAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBACzE,IAAI,CAAC,OAAO,GAAG,CAAC,KAAa,EAAE,EAAE,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;gBAChE,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;gBAC1C,MAAM;YACV,CAAC;YACD;gBACI,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;gBACnE,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAC1C,MAAM;QACd,CAAC;QACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,MAAA,MAAA,IAAI,CAAC,QAAQ,0CAAE,MAAM,mCAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC;IACxC,CAAC;IA6BD;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACH,IAAW,SAAS,KAAqB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAEtE;;OAEG;IACH,IAAW,CAAC,MAAM,CAAC,WAAW,CAAC;QAC3B,OAAO,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,IAAW,UAAU,KAAK,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;IAErE;;;OAGG;IACH,aAAa;IACN,OAAO,CAAC,KAAa,IAAa,OAAO,KAAK,CAAC,CAAC,CAAC;IAExD;;;OAGG;IACH,aAAa;IACN,GAAG,CAAC,KAAa,IAAwB,OAAO,IAAI,CAAC,CAAC,CAAC;IAE9D;;;OAGG;IACI,EAAE,CAAC,KAAa;QACnB,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;;;OAIG;IACH,aAAa;IACN,GAAG,CAAC,KAAa,EAAE,KAAyB,IAAU,OAAO,CAAC,CAAC;IAEtE;;;;OAIG;IACH,aAAa;IACN,OAAO,CAAC,OAAoB,EAAE,MAAe,IAAY,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAErE,QAAQ,CAAC,OAAoB,EAAE,MAAe;QACjD,mDAAmD;QACnD,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpB,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,GAAG,MAAmB;QAChC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACtG,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,KAAc,EAAE,GAAY;QACrC,OAAO,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,CAC9E,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,CAC1C,CAAC,CAAC;IACP,CAAC;IAEM,MAAM,KAAK,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAErC;;;;;;;;;;OAUG;IACI,OAAO;QACV,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QACvD,yCAAyC;QACzC,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YAClB,KAAK,IAAI,CAAC,GAAG,CAAC;YACd,KAAK,IAAI,CAAC,KAAK,CAAC;YAChB,KAAK,IAAI,CAAC,OAAO,CAAC;YAClB,KAAK,IAAI,CAAC,IAAI,CAAC;YACf,KAAK,IAAI,CAAC,SAAS;gBACf,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;oBAClB,KAAK,CAAC,CAAC,CAAC,OAAO,IAAI,SAAS,EAAE,CAAC;oBAC/B,KAAK,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;oBAC3D,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,EAAE;wBACnE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;wBACvE,IAAI,CAAC,MAAM,IAAI,YAAY,GAAG,MAAM,CAAC;wBACrC,OAAO,IAAI,CAAC;oBAChB,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;gBACnE,CAAC;QACT,CAAC;QACD,wCAAwC;QACxC,OAAO,CAAC,GAAG,IAAI,CAAgB,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,QAAQ;QACX,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,QAAQ,CAAiC,IAAO;;QACnD,OAAO,IAAI,CAAC,UAAU,CAAC,MAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,0CAAE,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC;IAClF,CAAC;IAED;;;OAGG;IACI,UAAU,CAA2B,KAAa;QACrD,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAY,CAAC,CAAC,CAAC;QACnF,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAW,UAAU;QACjB,IAAI,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAW,CAAC,UAAU,CAAC;QAC/C,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;;;OAUG;IACI,OAAO;QACV,IAAI,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,MAAM,UAAU,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAW,CAAC,CAAC;YAChE,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACnC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC5B,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;gBAC/B,OAAO,MAAM,CAAC;YAClB,CAAC,CAAC,CAAC;YACH,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC;QACD,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACI,SAAS;QACZ,IAAI,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACtD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAW,CAAC,SAAS,EAAE,CAAC;YACxD,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACnC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC7B,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;gBAChC,OAAO,OAAO,CAAC;YACnB,CAAC,CAAC,CAAC;YACH,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;;KAIiB,MAAM,CAAC,WAAW;AAFpC,2EAA2E;AAC3E,gFAAgF;AAC/D,UAAoB,GAAG,CAAC,CAAC,KAAa,EAAE,EAAE;IACtD,KAAa,CAAC,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC;IACxC,KAAa,CAAC,IAAI,GAAG,EAAE,CAAC;IACxB,KAAa,CAAC,MAAM,GAAG,CAAC,CAAC;IACzB,KAAa,CAAC,MAAM,GAAG,CAAC,CAAC;IACzB,KAAa,CAAC,WAAW,GAAG,CAAC,CAAC;IAC9B,KAAa,CAAC,QAAQ,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9C,KAAa,CAAC,MAAM,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC;IAEjD,MAAM,OAAO,GAAW,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;SACpC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAQ,CAAC;SAC/B,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC;IAElE,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC3B,MAAM,GAAG,GAAG,UAAU,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAClD,MAAM,GAAG,GAAG,UAAU,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAClD,MAAM,OAAO,GAAG,cAAc,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAE1D,gBAAgB,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;QACjD,wBAAwB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE;YACpD,CAAC,SAAS,CAAC,EAAE,EAAE,KAAK,EAAE,gBAAgB,CAAC,cAAc,CAAC,EAAE;YACxD,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,gBAAgB,CAAC,UAAU,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,EAAE;YAC3E,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,gBAAgB,CAAC,UAAU,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,EAAE;YAC3E,CAAC,SAAS,CAAC,EAAE,EAAE,KAAK,EAAE,kBAAkB,CAAC,cAAc,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,EAAE;SACxF,CAAC,CAAC;IACP,CAAC;IAED,OAAO,QAAQ,CAAC;AACpB,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAGzB,MAAM,cAAyC,SAAQ,MAAS;IAE5D,YAAmB,MAAiB;QAChC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEnB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAEzB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAqB,IAAI,CAAC,MAAM,CAAC,CAAC;QAEzD,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE;YAC/B,KAAK,CAAC,KAAa;gBACf,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;gBACjC,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;oBAC5B,OAAO,WAAW,CAAC;gBACvB,CAAC;gBACD,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACpC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;gBACrB,OAAO,KAAK,CAAC;YACjB,CAAC;SACJ,CAAC,CAAC;QAEH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE;YAC/B,KAAK,CAAC,KAAa,EAAE,KAAyB;gBAC1C,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC7B,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;YACzB,CAAC;SACJ,CAAC,CAAC;QAEH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE;YACjC,KAAK,EAAE,CAAC,KAAc,EAAE,GAAY,EAAE,EAAE,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;SAC5F,CAAC,CAAC;QAEH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QAE3D,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE;YACrC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;SACrC,CAAC,CAAC;QAEH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE;YACnC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI;SACpB,CAAC,CAAC;IACP,CAAC;CACJ;AAED,OAAO,KAAK,MAAM,MAAM,WAAW,CAAC;AAgBpC,MAAM,UAAU,UAAU,CAAC,IAAS;IAChC,IAAI,IAAI,EAAE,CAAC;QACP,IAAI,IAAI,YAAY,IAAI,EAAE,CAAC;YAAC,OAAO,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAAC,CAAC;QACxD,IAAI,IAAI,YAAY,MAAM,EAAE,CAAC;YAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAAC,CAAC;QAC7D,IAAI,IAAI,CAAC,IAAI,YAAY,QAAQ,EAAE,CAAC;YAAC,OAAO,IAAI,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAAC,CAAC;QAC3E,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;QACD,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,IAAI,IAAI,YAAY,QAAQ,EAAE,CAAC;gBAC3B,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvC,CAAC;YACD,MAAM,KAAK,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;YAC5E,IAAI,IAAI,YAAY,SAAS,EAAE,CAAC;gBAAC,OAAO,IAAI,MAAM,CAAC,CAAC,QAAQ,iCAAM,KAAK,KAAE,IAAI,EAAE,IAAI,MAAM,CAAC,IAAI,IAAG,CAAC,CAAC,CAAC;YAAC,CAAC;YACtG,IAAI,IAAI,YAAY,UAAU,EAAE,CAAC;gBAAC,OAAO,IAAI,MAAM,CAAC,CAAC,QAAQ,iCAAM,KAAK,KAAE,IAAI,EAAE,IAAI,MAAM,CAAC,KAAK,IAAG,CAAC,CAAC,CAAC;YAAC,CAAC;YACxG,IAAI,IAAI,YAAY,UAAU,EAAE,CAAC;gBAAC,OAAO,IAAI,MAAM,CAAC,CAAC,QAAQ,iCAAM,KAAK,KAAE,IAAI,EAAE,IAAI,MAAM,CAAC,KAAK,IAAG,CAAC,CAAC,CAAC;YAAC,CAAC;YACxG,IAAI,IAAI,YAAY,aAAa,EAAE,CAAC;gBAAC,OAAO,IAAI,MAAM,CAAC,CAAC,QAAQ,iCAAM,KAAK,KAAE,IAAI,EAAE,IAAI,MAAM,CAAC,KAAK,IAAG,CAAC,CAAC,CAAC;YAAC,CAAC;YAC3G,IAAI,IAAI,YAAY,UAAU,IAAI,IAAI,YAAY,iBAAiB,EAAE,CAAC;gBAAC,OAAO,IAAI,MAAM,CAAC,CAAC,QAAQ,iCAAM,KAAK,KAAE,IAAI,EAAE,IAAI,MAAM,CAAC,KAAK,IAAG,CAAC,CAAC,CAAC;YAAC,CAAC;YAC7I,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;gBAAC,OAAO,IAAI,MAAM,CAAC,CAAC,QAAQ,iCAAM,KAAK,KAAE,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,IAAG,CAAC,CAAC,CAAC;YAAC,CAAC;YAC1G,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;gBAAC,OAAO,IAAI,MAAM,CAAC,CAAC,QAAQ,iCAAM,KAAK,KAAE,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,IAAG,CAAC,CAAC,CAAC;YAAC,CAAC;YAC1G,IAAI,IAAI,YAAY,cAAc,EAAE,CAAC;gBAAC,OAAO,IAAI,MAAM,CAAC,CAAC,QAAQ,iCAAM,KAAK,KAAE,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,IAAG,CAAC,CAAC,CAAC;YAAC,CAAC;YAC7G,IAAI,IAAI,YAAY,YAAY,EAAE,CAAC;gBAAC,OAAO,IAAI,MAAM,CAAC,CAAC,QAAQ,iCAAM,KAAK,KAAE,IAAI,EAAE,IAAI,MAAM,CAAC,OAAO,IAAG,CAAC,CAAC,CAAC;YAAC,CAAC;YAC5G,IAAI,IAAI,YAAY,YAAY,EAAE,CAAC;gBAAC,OAAO,IAAI,MAAM,CAAC,CAAC,QAAQ,iCAAM,KAAK,KAAE,IAAI,EAAE,IAAI,MAAM,CAAC,OAAO,IAAG,CAAC,CAAC,CAAC;YAAC,CAAC;YAC5G,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAC1C,CAAC;IACL,CAAC;IACD,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;AAC1C,CAAC;AAED,SAAS,YAAY,CAAC,CAAM;IACxB,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACzF,CAAC", "file": "vector.mjs", "sourceRoot": "src"}