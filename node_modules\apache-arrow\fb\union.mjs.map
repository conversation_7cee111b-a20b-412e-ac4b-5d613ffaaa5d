{"version": 3, "sources": ["fb/union.ts"], "names": [], "mappings": "AAAA,qEAAqE;AAErE,OAAO,KAAK,WAAW,MAAM,aAAa,CAAC;AAE3C,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAG5C;;;;;GAKG;AACH,MAAM,OAAO,KAAK;IAAlB;QACE,OAAE,GAAgC,IAAI,CAAC;QACvC,WAAM,GAAG,CAAC,CAAC;IA4Eb,CAAC;IA3EC,MAAM,CAAC,CAAQ,EAAE,EAAyB;QAC1C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,EAAyB,EAAE,GAAU;QACzD,OAAO,CAAC,GAAG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACtF,CAAC;IAED,MAAM,CAAC,0BAA0B,CAAC,EAAyB,EAAE,GAAU;QACrE,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACtF,CAAC;IAED,IAAI;QACF,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC;IAC9E,CAAC;IAED,OAAO,CAAC,KAAa;QACnB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9F,CAAC;IAED,aAAa;QACX,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;IAED,YAAY;QACV,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAG,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAG,CAAC,KAAK,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,EAAG,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrL,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,OAA2B;QAC3C,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,OAA2B,EAAE,IAAc;QACxD,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;IACnD,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,OAA2B,EAAE,aAAgC;QAC7E,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;IAOD,MAAM,CAAC,mBAAmB,CAAC,OAA2B,EAAE,IAAmC;QACzF,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACvC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAE,CAAC,CAAC;QAC7B,CAAC;QACD,OAAO,OAAO,CAAC,SAAS,EAAE,CAAC;IAC7B,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,OAA2B,EAAE,QAAe;QACpE,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,OAA2B;QACzC,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,OAA2B,EAAE,IAAc,EAAE,aAAgC;QAC9F,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC1B,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC7B,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QACzC,OAAO,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;CACA", "file": "union.mjs", "sourceRoot": "../src"}