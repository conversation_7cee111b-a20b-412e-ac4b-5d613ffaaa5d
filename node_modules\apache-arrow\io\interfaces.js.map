{"version": 3, "sources": ["io/interfaces.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;;AAErB,+CAA2C;AAK3C,cAAc;AACD,QAAA,aAAa,GAAQ,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAOjF,cAAc;AACd,MAAa,SAAS;IAClB,YAAoB,KAAoB;QAApB,UAAK,GAAL,KAAK,CAAe;IAAI,CAAC;IAC7C,IAAW,MAAM,KAAU,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACzD,IAAW,OAAO,KAAY,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,CAAU,CAAC,CAAC,CAAC;IAC9E,IAAW,YAAY,KAAY,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,CAAU,CAAC,CAAC,CAAC;CAC3F;AALD,8BAKC;AA8BD,cAAc;AACd,MAAsB,eAAe;IAK1B,GAAG;QACN,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,CAAC;IACtC,CAAC;IACM,IAAI,CAAkC,QAAW,EAAE,OAA2B;QACjF,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;IACM,MAAM,CAAC,QAA2B,EAAE,OAA2B,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3H,WAAW,CAAgC,MAAoD,EAAE,OAA2B;QAC/H,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAGO,aAAa;QACjB,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IACrE,CAAC;IAGO,cAAc;QAClB,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;IACxE,CAAC;CACJ;AAzBD,0CAyBC;AAKD,cAAc;AACd,MAAa,UAA0D,SAAQ,eAA0B;IASrG;QACI,KAAK,EAAE,CAAC;QAPF,YAAO,GAAgB,EAAE,CAAC;QAI1B,cAAS,GAA4C,EAAE,CAAC;QAI9D,IAAI,CAAC,cAAc,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,IAAW,MAAM,KAAoB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;IACrD,MAAM,CAAC,MAAY;sEAAI,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;KAAA;IACzD,KAAK,CAAC,KAAgB;QACzB,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACrB,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC;gBACtB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAG,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAS,CAAC,CAAC,CAAC;QAC3E,CAAC;IACL,CAAC;IACM,KAAK,CAAC,KAAW;QACpB,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC;gBACtB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;gBAClC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAG,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAClE,CAAC;IACL,CAAC;IACM,KAAK;QACR,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;YAC3B,OAAO,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1B,SAAS,CAAC,KAAK,EAAG,CAAC,OAAO,CAAC,qBAAa,CAAC,CAAC;YAC9C,CAAC;YACD,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;QAC3C,CAAC;IACL,CAAC;IAEM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,OAAO,IAAI,CAAC,CAAC,CAAC;IACzC,WAAW,CAAC,OAAkC;QACjD,OAAO,qBAAc,CAAC,WAAW,CAC7B,CAAC,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,MAAM,CAAC;YACvC,CAAC,CAAE,IAAiC;YACpC,CAAC,CAAE,IAAI,CAAC,OAAsC,EAClD,OAAO,CAAC,CAAC;IACjB,CAAC;IACM,YAAY,CAAC,OAAyB;QACzC,OAAO,qBAAc,CAAC,YAAY,CAC9B,CAAC,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,MAAM,CAAC;YACvC,CAAC,CAAE,IAAiC;YACpC,CAAC,CAAE,IAAI,CAAC,OAAsC,EAClD,OAAO,CAAC,CAAC;IACjB,CAAC;IACY,KAAK,CAAC,CAAO;sEAAI,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,qBAAa,CAAC,CAAC,CAAC;KAAA;IAC7D,MAAM,CAAC,CAAO;sEAAI,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,qBAAa,CAAC,CAAC,CAAC;KAAA;IAE7D,IAAI,CAAC,IAAoB;sEAA+B,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;KAAA;IACvG,IAAI,CAAC,IAAoB;sEAA+B,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;KAAA;IAC7G,IAAI,CAAC,GAAG,KAAY;QACvB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAG,EAAS,CAAC,CAAC;QACjF,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACrB,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;QACpE,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACrC,OAAO,OAAO,CAAC,OAAO,CAAC,qBAAa,CAAC,CAAC;QAC1C,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,OAAO,CAA4B,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC9D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAES,WAAW;QACjB,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC5C,CAAC;CACJ;AAjFD,gCAiFC", "file": "interfaces.js", "sourceRoot": "../src"}