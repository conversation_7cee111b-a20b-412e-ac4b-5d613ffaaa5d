import { CommandParser } from '../client/parser';
import { RedisArgument, SimpleStringReply } from '../RESP/types';
declare const _default: {
    readonly NOT_KEYED_COMMAND: true;
    readonly IS_READ_ONLY: true;
    /**
     * Reconfigures a node as a replica of the specified primary node
     * @param parser - The Redis command parser
     * @param nodeId - Node ID of the primary node to replicate
     */
    readonly parseCommand: (this: void, parser: CommandParser, nodeId: RedisArgument) => void;
    readonly transformReply: () => SimpleStringReply;
};
export default _default;
//# sourceMappingURL=CLUSTER_REPLICATE.d.ts.map