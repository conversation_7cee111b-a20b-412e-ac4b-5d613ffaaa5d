{"version": 3, "file": "check.d.ts", "sourceRoot": "", "sources": ["../../../src/core/helpers/check.ts"], "names": [], "mappings": "AAAA,UAAU,OAAO;IACf,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,MAAM,CAAA;IACd,OAAO,EAAE,OAAO,CAAA;IAChB,MAAM,EAAE,MAAM,CAAA;IACd,SAAS,EAAE,SAAS,CAAA;IACpB,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAE/B,QAAQ,EAAE,CAAC,GAAG,KAAK,EAAE,GAAG,EAAE,KAAK,GAAG,CAAA;CACnC;AAED;;;;;;;;;;;;;GAaG;AACH,wBAAgB,OAAO,CAAC,CAAC,SAAS,MAAM,EAAE,CAAC,SAAS,WAAW,EAC7D,GAAG,EAAE,CAAC,GAAG,SAAS,EAClB,IAAI,EAAE,CAAC,GACN,GAAG,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAE/B;AACD;;;;;;;;;;;;;;;GAeG;AACH,wBAAgB,WAAW,CACzB,CAAC,SAAS,MAAM,EAChB,CAAC,SAAS,WAAW,EACrB,CAAC,SAAS,MAAM,OAAO,EACvB,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,EACpB,GAAG,EAAE,CAAC,GAAG,SAAS,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAE/D;AAED;;;;;;;;;;GAUG;AACH,wBAAgB,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,EAAE,CAEtD"}