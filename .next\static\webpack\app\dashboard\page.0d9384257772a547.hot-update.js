"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./components/DashboardLayout.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowUp,Bug,Database,FileText,Flame,Globe,Play,Search,Shield,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DashboardPage() {\n    _s();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalScans: 0,\n        vulnerabilitiesFound: 0,\n        filesAnalyzed: 0,\n        osintQueries: 0,\n        apiCalls: 0,\n        score: 0,\n        level: 0,\n        rank: 0,\n        streak: 0,\n        pointsToday: 0,\n        pointsThisWeek: 0,\n        pointsThisMonth: 0\n    });\n    const [recentActivity, setRecentActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [achievements, setAchievements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [planUsage, setPlanUsage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [trendingVulns, setTrendingVulns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Update time every minute\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDashboardData();\n    }, []);\n    const loadDashboardData = async ()=>{\n        try {\n            setLoading(true);\n            // Fetch real dashboard data from API\n            const response = await fetch(\"/api/dashboard/stats\");\n            const data = await response.json();\n            if (data.success) {\n                setStats({\n                    totalScans: data.data.stats.totalScans,\n                    vulnerabilitiesFound: data.data.stats.vulnerabilitiesFound,\n                    filesAnalyzed: data.data.stats.fileAnalyses,\n                    osintQueries: data.data.stats.osintQueries,\n                    apiCalls: data.data.stats.cveSearches + data.data.stats.dorkingQueries,\n                    score: data.data.user.score,\n                    level: data.data.user.level,\n                    rank: data.data.user.rank,\n                    streak: data.data.user.streak,\n                    pointsToday: Math.floor(data.data.user.score * 0.1),\n                    pointsThisWeek: Math.floor(data.data.user.score * 0.3),\n                    pointsThisMonth: Math.floor(data.data.user.score * 0.6)\n                });\n                // Transform recent activities to match interface\n                const transformedActivities = data.data.recentActivities.map((activity)=>({\n                        id: activity.id.toString(),\n                        type: activity.type === \"vulnerability_scan\" ? \"scan\" : activity.type === \"osint_query\" ? \"osint\" : activity.type === \"file_analysis\" ? \"file\" : activity.type === \"cve_search\" ? \"cve\" : \"dorking\",\n                        target: activity.description.includes(\"target.\") ? \"target.example.com\" : activity.description.includes(\"domain\") ? \"example.com\" : activity.description.includes(\"file:\") ? \"malware.exe\" : activity.description.includes(\"Apache\") ? \"Apache HTTP Server\" : activity.description.includes(\"databases\") ? \"Google Search\" : \"Unknown\",\n                        time: new Date(activity.timestamp).toLocaleTimeString() + \" ago\",\n                        status: \"completed\",\n                        severity: activity.severity,\n                        result: activity.result\n                    }));\n                setRecentActivity(transformedActivities);\n                setUser(data.data.user);\n                setAchievements(data.data.achievements);\n                setPlanUsage(data.data.usage);\n                setTrendingVulns(data.data.trendingVulnerabilities);\n            } else {\n                // Fallback to mock data if API fails\n                setStats({\n                    totalScans: 142,\n                    vulnerabilitiesFound: 23,\n                    filesAnalyzed: 89,\n                    osintQueries: 456,\n                    apiCalls: 2847,\n                    score: 8950,\n                    level: 28,\n                    rank: 156,\n                    streak: 12,\n                    pointsToday: 250,\n                    pointsThisWeek: 1450,\n                    pointsThisMonth: 5890\n                });\n                setRecentActivity([\n                    {\n                        id: \"1\",\n                        type: \"scan\",\n                        target: \"example.com\",\n                        time: \"2 minutes ago\",\n                        status: \"completed\",\n                        severity: \"high\",\n                        result: \"3 vulnerabilities found\"\n                    }\n                ]);\n            }\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Error loading dashboard data:\", error);\n            setLoading(false);\n        }\n    };\n    const quickActions = [\n        {\n            title: \"OSINT Lookup\",\n            description: \"Investigate emails, phone numbers, and personal information\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"/osint\",\n            badge: \"Popular\",\n            color: \"cyber-primary\",\n            stats: \"456 queries\"\n        },\n        {\n            title: \"Vulnerability Scan\",\n            description: \"Scan websites and applications for security vulnerabilities\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"/scanner\",\n            premium: true,\n            color: \"cyber-secondary\",\n            stats: \"142 scans\"\n        },\n        {\n            title: \"File Analysis\",\n            description: \"Analyze files for malware, webshells, and suspicious content\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"/file-analyzer\",\n            premium: true,\n            color: \"cyber-accent\",\n            stats: \"89 files\"\n        },\n        {\n            title: \"CVE Database\",\n            description: \"Search and explore the latest CVE vulnerabilities\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: \"/cve\",\n            color: \"green-400\",\n            stats: \"50K+ CVEs\"\n        },\n        {\n            title: \"Google Dorking\",\n            description: \"Use advanced search queries to find sensitive information\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: \"/dorking\",\n            color: \"blue-400\",\n            stats: \"Advanced\"\n        },\n        {\n            title: \"API Playground\",\n            description: \"Test and explore our API endpoints\",\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            href: \"/playground\",\n            color: \"purple-400\",\n            stats: \"2.8K calls\"\n        }\n    ];\n    const getActivityIcon = (type)=>{\n        switch(type){\n            case \"scan\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n            case \"osint\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n            case \"file\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"cve\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"dorking\":\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            default:\n                return _barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"text-green-400\";\n            case \"failed\":\n                return \"text-red-400\";\n            case \"malicious\":\n                return \"text-red-400\";\n            case \"clean\":\n                return \"text-green-400\";\n            case \"pending\":\n                return \"text-yellow-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case \"critical\":\n                return \"text-red-400 bg-red-400/20\";\n            case \"high\":\n                return \"text-orange-400 bg-orange-400/20\";\n            case \"medium\":\n                return \"text-yellow-400 bg-yellow-400/20\";\n            case \"low\":\n                return \"text-green-400 bg-green-400/20\";\n            default:\n                return \"text-gray-400 bg-gray-400/20\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-2 border-cyber-primary border-t-transparent mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-cyber-primary font-medium\",\n                            children: \"Loading cyber dashboard...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 287,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-glow\",\n                                            children: \"Cyber\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyber-pink\",\n                                            children: \"Command\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-lg\",\n                                    children: \"Welcome back, Cyber Warrior! Your digital arsenal awaits.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 lg:mt-0 flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Current Time\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-mono text-cyber-primary\",\n                                            children: currentTime.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn-cyber-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Quick Scan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 rounded-full bg-gradient-to-r from-cyber-primary to-cyber-secondary flex items-center justify-center text-2xl font-bold text-black\",\n                                        children: \"CW\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: \"Cyber Warrior\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Level \",\n                                                            stats.level\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Rank #\",\n                                                            stats.rank\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"•\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4 text-cyber-secondary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    stats.streak,\n                                                                    \" day streak\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-cyber-primary\",\n                                        children: stats.score.toLocaleString()\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"Total Score\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-green-400\",\n                                                children: [\n                                                    \"+\",\n                                                    stats.pointsToday,\n                                                    \" today\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-3 w-3 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-8 w-8 text-cyber-primary mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.totalScans\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Total Scans\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-green-400 mt-1\",\n                                    children: \"+12 this week\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-8 w-8 text-red-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.vulnerabilitiesFound\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Vulnerabilities\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-red-400 mt-1\",\n                                    children: \"+3 critical\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-8 w-8 text-cyber-accent mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.filesAnalyzed\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Files Analyzed\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-yellow-400 mt-1\",\n                                    children: \"5 malicious\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-8 w-8 text-blue-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.osintQueries\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"OSINT Queries\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-blue-400 mt-1\",\n                                    children: \"+45 today\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-purple-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.apiCalls.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"API Calls\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-purple-400 mt-1\",\n                                    children: \"98% uptime\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-400 mx-auto mb-3 animate-cyber-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-white mb-1\",\n                                    children: stats.pointsThisWeek.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: \"Weekly Points\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-green-400 mt-1\",\n                                    children: \"+15% vs last week\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-cyber border-l-4 border-l-cyber-secondary\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-6 w-6 text-cyber-secondary mt-1\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold text-white mb-1\",\n                                        children: \"System Status Update\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-3\",\n                                        children: \"New CVE database update available. 15 critical vulnerabilities added to our intelligence feed.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-cyber-secondary text-sm\",\n                                                children: \"View Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"2 minutes ago\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 420,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Quick\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Actions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: quickActions.map((action, index)=>{\n                                const Icon = action.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card-cyber hover:scale-105 transition-all duration-300 cursor-pointer group\",\n                                    onClick: ()=>window.location.href = action.href,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg bg-\".concat(action.color, \"/20\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"h-6 w-6 text-\".concat(action.color, \" group-hover:animate-cyber-pulse\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-end space-y-1\",\n                                                    children: [\n                                                        action.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-cyber-secondary/20 text-cyber-secondary px-2 py-1 rounded-full text-xs font-bold\",\n                                                            children: action.badge\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        action.premium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-cyber-accent/20 text-cyber-accent px-2 py-1 rounded-full text-xs font-bold\",\n                                                            children: \"PRO\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold text-white mb-2 group-hover:text-cyber-primary transition-colors\",\n                                            children: action.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm mb-4\",\n                                            children: action.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium text-\".concat(action.color),\n                                                    children: action.stats\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowUp_Bug_Database_FileText_Flame_Globe_Play_Search_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-400 group-hover:text-cyber-primary transition-colors\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-glow\",\n                                    children: \"Recent\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-cyber-pink\",\n                                    children: \"Activity\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-cyber\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: recentActivity.map((activity)=>{\n                                        const Icon = getActivityIcon(activity.type);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 rounded-lg bg-cyber-secondary/5 hover:bg-cyber-primary/10 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-cyber-primary/20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"h-5 w-5 text-cyber-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-medium text-white\",\n                                                                            children: activity.target\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 514,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-bold uppercase \".concat(getStatusColor(activity.status)),\n                                                                            children: activity.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 515,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        activity.severity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs font-bold uppercase \".concat(getSeverityColor(activity.severity)),\n                                                                            children: activity.severity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 519,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-sm\",\n                                                                    children: activity.result\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: activity.time\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 capitalize\",\n                                                            children: activity.type\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, activity.id, true, {\n                                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-cyber-primary hover:text-cyber-secondary transition-colors\",\n                                        children: \"View All Activity →\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 498,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 492,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 300,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 299,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"BFRTwvn+5IkN9CfaeTLp5dlKHsI=\");\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});