"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n// Define protected routes\nconst protectedRoutes = [\n    \"/dashboard\",\n    \"/osint\",\n    \"/scanner\",\n    \"/file-analyzer\",\n    \"/cve\",\n    \"/dorking\",\n    \"/playground\",\n    \"/tools\",\n    \"/bot\",\n    \"/leaderboard\",\n    \"/profile\",\n    \"/plan\",\n    \"/admin\"\n];\n// Define admin-only routes\nconst adminRoutes = [\n    \"/admin\"\n];\n// Define public routes that should redirect to dashboard if authenticated\nconst publicRoutes = [\n    \"/login\",\n    \"/register\"\n];\n// Simple authentication check for Edge Runtime\nasync function checkAuthentication(request) {\n    try {\n        // Get token from cookies\n        const cookies = request.headers.get(\"cookie\");\n        if (!cookies) return {\n            success: false\n        };\n        const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n        const token = tokenMatch ? tokenMatch[1] : null;\n        if (!token) return {\n            success: false\n        };\n        // Simple token validation - just check if it exists and looks like a JWT\n        const parts = token.split(\".\");\n        if (parts.length !== 3) return {\n            success: false\n        };\n        try {\n            // Try to decode the payload (without verification for Edge Runtime)\n            const payload = JSON.parse(atob(parts[1]));\n            // Check if token is not expired\n            if (payload.exp && payload.exp < Date.now() / 1000) {\n                return {\n                    success: false\n                };\n            }\n            // Return user data from token payload\n            return {\n                success: true,\n                user: {\n                    id: payload.id,\n                    username: payload.username,\n                    email: payload.email,\n                    role: payload.role,\n                    plan: payload.plan\n                }\n            };\n        } catch  {\n            return {\n                success: false\n            };\n        }\n    } catch  {\n        return {\n            success: false\n        };\n    }\n}\nasync function middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Skip middleware for API routes, static files, and other assets\n    if (pathname.startsWith(\"/api/\") || pathname.startsWith(\"/_next/\") || pathname.startsWith(\"/favicon.ico\") || pathname.includes(\".\")) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Get authentication status - simplified for Edge Runtime\n    const authResult = await checkAuthentication(request);\n    const isAuthenticated = authResult.success;\n    const user = authResult.user;\n    console.log(`🔐 Middleware: ${pathname} - Auth: ${isAuthenticated} - User: ${user?.username || \"none\"}`);\n    // Handle protected routes\n    if (protectedRoutes.some((route)=>pathname.startsWith(route))) {\n        if (!isAuthenticated) {\n            console.log(`❌ Redirecting unauthenticated user from ${pathname} to /login`);\n            const loginUrl = new URL(\"/login\", request.url);\n            loginUrl.searchParams.set(\"redirect\", pathname);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(loginUrl);\n        }\n        // Check admin routes\n        if (adminRoutes.some((route)=>pathname.startsWith(route))) {\n            if (user?.role !== \"admin\" && user?.role !== \"super_admin\") {\n                console.log(`❌ Unauthorized access attempt to ${pathname} by ${user?.username}`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n            }\n        }\n        // User is authenticated and authorized, continue\n        console.log(`✅ Authorized access to ${pathname} by ${user?.username}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Handle public routes (login, register)\n    if (publicRoutes.some((route)=>pathname === route)) {\n        if (isAuthenticated) {\n            console.log(`🔄 Redirecting authenticated user from ${pathname} to /dashboard`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n        }\n        // User is not authenticated, allow access to public routes\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    // Handle root route\n    if (pathname === \"/\") {\n        if (isAuthenticated) {\n            console.log(`🔄 Redirecting authenticated user from / to /dashboard`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/dashboard\", request.url));\n        } else {\n            console.log(`🔄 Redirecting unauthenticated user from / to /login`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL(\"/login\", request.url));\n        }\n    }\n    // For all other routes, continue normally\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder files\n     */ \"/((?!api|_next/static|_next/image|favicon.ico|.*\\\\..*$).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});