#!/usr/bin/env ts-node

import { testDatabaseConnection, closeDatabaseConnections, db } from '../lib/database'

async function fixDatabase() {
  console.log('🔧 Fixing KodeXGuard Database Issues...\n')

  try {
    // Test database connection
    const connected = await testDatabaseConnection()
    if (!connected) {
      console.error('❌ Database connection failed')
      process.exit(1)
    }

    // Check current users table structure
    console.log('🔍 Checking current users table structure...')
    const userColumns = await db.query('DESCRIBE users') as any[]
    const idColumn = userColumns.find(col => col.Field === 'id')
    console.log(`Current users.id type: ${idColumn?.Type}`)

    // Create tables without foreign key constraints first
    console.log('\n🔄 Creating tables without foreign key constraints...')

    // Create subscriptions table
    await createTableSafely('subscriptions', `
      CREATE TABLE IF NOT EXISTS subscriptions (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        plan_id VARCHAR(50) NOT NULL,
        status ENUM('active', 'cancelled', 'expired', 'suspended') DEFAULT 'active',
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        auto_renew BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_plan_id (plan_id),
        INDEX idx_status (status),
        INDEX idx_end_date (end_date)
      )
    `)

    // Create payments table
    await createTableSafely('payments', `
      CREATE TABLE IF NOT EXISTS payments (
        id VARCHAR(50) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        plan_id VARCHAR(50) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'IDR',
        gateway ENUM('tripay', 'midtrans', 'xendit', 'manual') NOT NULL,
        status ENUM('pending', 'paid', 'failed', 'expired', 'cancelled') DEFAULT 'pending',
        gateway_payment_id VARCHAR(255),
        payment_url TEXT,
        expires_at TIMESTAMP NULL,
        paid_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_status (status),
        INDEX idx_gateway (gateway),
        INDEX idx_created_at (created_at)
      )
    `)

    // Create payment_logs table
    await createTableSafely('payment_logs', `
      CREATE TABLE IF NOT EXISTS payment_logs (
        id VARCHAR(36) PRIMARY KEY,
        payment_id VARCHAR(50) NOT NULL,
        user_id VARCHAR(36) NOT NULL,
        action ENUM('create', 'approve', 'reject', 'cancel', 'expire') NOT NULL,
        performed_by VARCHAR(36),
        details JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_payment_id (payment_id),
        INDEX idx_user_id (user_id),
        INDEX idx_action (action)
      )
    `)

    // Create bot_messages table
    await createTableSafely('bot_messages', `
      CREATE TABLE IF NOT EXISTS bot_messages (
        id VARCHAR(36) PRIMARY KEY,
        bot_id VARCHAR(50) NOT NULL,
        user_phone VARCHAR(20),
        user_telegram_id BIGINT,
        message_type ENUM('text', 'image', 'document', 'audio', 'video') DEFAULT 'text',
        message_content TEXT,
        command VARCHAR(50),
        response TEXT,
        status ENUM('received', 'processing', 'responded', 'failed') DEFAULT 'received',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_bot_id (bot_id),
        INDEX idx_user_phone (user_phone),
        INDEX idx_user_telegram_id (user_telegram_id),
        INDEX idx_command (command),
        INDEX idx_created_at (created_at)
      )
    `)

    // Create score_history table
    await createTableSafely('score_history', `
      CREATE TABLE IF NOT EXISTS score_history (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        action_type ENUM('osint_query', 'vulnerability_scan', 'file_analysis', 'cve_report', 'community_contribution', 'bug_report', 'achievement_unlock') NOT NULL,
        points_awarded INT NOT NULL,
        multiplier DECIMAL(3,2) DEFAULT 1.00,
        description TEXT,
        metadata JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_action_type (action_type),
        INDEX idx_created_at (created_at)
      )
    `)

    // Create user_achievements table
    await createTableSafely('user_achievements', `
      CREATE TABLE IF NOT EXISTS user_achievements (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        achievement_id VARCHAR(50) NOT NULL,
        unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_achievement (user_id, achievement_id),
        INDEX idx_user_id (user_id),
        INDEX idx_achievement_id (achievement_id)
      )
    `)

    // Create user_streaks table
    await createTableSafely('user_streaks', `
      CREATE TABLE IF NOT EXISTS user_streaks (
        id VARCHAR(36) PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        streak_type VARCHAR(50) NOT NULL,
        current_streak INT DEFAULT 0,
        longest_streak INT DEFAULT 0,
        last_activity_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_streak (user_id, streak_type),
        INDEX idx_user_id (user_id),
        INDEX idx_streak_type (streak_type)
      )
    `)

    // Create admin_logs table
    await createTableSafely('admin_logs', `
      CREATE TABLE IF NOT EXISTS admin_logs (
        id VARCHAR(36) PRIMARY KEY,
        admin_id VARCHAR(36) NOT NULL,
        action VARCHAR(100) NOT NULL,
        target_type VARCHAR(50),
        target_id VARCHAR(50),
        details JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_admin_id (admin_id),
        INDEX idx_action (action),
        INDEX idx_target_type (target_type),
        INDEX idx_created_at (created_at)
      )
    `)

    // Add missing columns to users table
    console.log('\n🔄 Adding missing columns to users table...')
    await addColumnSafely('users', 'phone', 'VARCHAR(20)')
    await addColumnSafely('users', 'telegram_id', 'BIGINT UNIQUE')
    await addColumnSafely('users', 'whatsapp_number', 'VARCHAR(20)')
    await addColumnSafely('users', 'status', "ENUM('active', 'inactive', 'suspended', 'banned') DEFAULT 'active'")
    await addColumnSafely('users', 'avatar_url', 'VARCHAR(500)')
    await addColumnSafely('users', 'timezone', "VARCHAR(50) DEFAULT 'Asia/Jakarta'")
    await addColumnSafely('users', 'language', "VARCHAR(10) DEFAULT 'id'")
    await addColumnSafely('users', 'score', 'INT DEFAULT 0')
    await addColumnSafely('users', 'level', 'INT DEFAULT 1')
    await addColumnSafely('users', 'streak_days', 'INT DEFAULT 0')

    // Insert default plans if not exist
    console.log('\n🌱 Inserting default plans...')
    await insertPlanSafely('free', 'Free', 'free', 0)
    await insertPlanSafely('student', 'Student', 'student', 50000)
    await insertPlanSafely('hobby', 'Hobby', 'hobby', 150000)
    await insertPlanSafely('bughunter', 'Bug Hunter', 'bughunter', 300000)
    await insertPlanSafely('cybersecurity', 'Cybersecurity Pro', 'cybersecurity', 500000)

    console.log('\n✅ Database fix completed successfully!')
    
  } catch (error) {
    console.error('❌ Database fix failed:', error)
    process.exit(1)
  } finally {
    await closeDatabaseConnections()
  }
}

async function createTableSafely(tableName: string, sql: string) {
  try {
    await db.query(sql)
    console.log(`✅ Created table: ${tableName}`)
  } catch (error: any) {
    if (error.code === 'ER_TABLE_EXISTS_ERROR') {
      console.log(`ℹ️ Table ${tableName} already exists`)
    } else {
      console.log(`⚠️ Error creating table ${tableName}: ${error.message}`)
    }
  }
}

async function addColumnSafely(tableName: string, columnName: string, columnDef: string) {
  try {
    await db.query(`ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnDef}`)
    console.log(`✅ Added column: ${tableName}.${columnName}`)
  } catch (error: any) {
    if (error.code === 'ER_DUP_FIELDNAME') {
      console.log(`ℹ️ Column ${tableName}.${columnName} already exists`)
    } else {
      console.log(`⚠️ Error adding column ${tableName}.${columnName}: ${error.message}`)
    }
  }
}

async function insertPlanSafely(id: string, name: string, type: string, price: number) {
  try {
    await db.query(`
      INSERT IGNORE INTO plans (id, name, type, price, currency, duration, features, is_active, description)
      VALUES (?, ?, ?, ?, 'IDR', 'monthly', '{}', TRUE, ?)
    `, [id, name, type, price, `${name} plan for KodeXGuard`])
    console.log(`✅ Inserted plan: ${name}`)
  } catch (error: any) {
    console.log(`⚠️ Error inserting plan ${name}: ${error.message}`)
  }
}

// Run fix if this file is executed directly
if (require.main === module) {
  fixDatabase()
}
