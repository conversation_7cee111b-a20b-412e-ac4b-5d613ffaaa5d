{"version": 3, "sources": ["fb/tensor.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,WAAW,MAAM,aAAa,CAAC;AAE3C,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AACrC,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAGjC,qBAAa,MAAM;IACjB,EAAE,EAAE,WAAW,CAAC,UAAU,GAAC,IAAI,CAAQ;IACvC,MAAM,SAAK;IACX,MAAM,CAAC,CAAC,EAAC,MAAM,EAAE,EAAE,EAAC,WAAW,CAAC,UAAU,GAAE,MAAM;IAMpD,MAAM,CAAC,eAAe,CAAC,EAAE,EAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,EAAC,MAAM,GAAE,MAAM;IAIrE,MAAM,CAAC,2BAA2B,CAAC,EAAE,EAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,EAAC,MAAM,GAAE,MAAM;IAKjF,QAAQ,IAAG,IAAI;IAKf;;;OAGG;IACH,IAAI,CAAC,GAAG,EAAC,GAAG,GAAE,GAAG,GAAC,IAAI;IAKtB;;OAEG;IACH,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,EAAC,SAAS,GAAE,SAAS,GAAC,IAAI;IAKnD,WAAW,IAAG,MAAM;IAKpB;;;OAGG;IACH,OAAO,CAAC,KAAK,EAAE,MAAM,GAAE,MAAM,GAAC,IAAI;IAKlC,aAAa,IAAG,MAAM;IAKtB;;OAEG;IACH,IAAI,CAAC,GAAG,CAAC,EAAC,MAAM,GAAE,MAAM,GAAC,IAAI;IAK7B,MAAM,CAAC,WAAW,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO;IAI9C,MAAM,CAAC,WAAW,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAC,IAAI;IAI7D,MAAM,CAAC,OAAO,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,UAAU,EAAC,WAAW,CAAC,MAAM;IAIzE,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,WAAW,EAAC,WAAW,CAAC,MAAM;IAI3E,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,IAAI,EAAC,WAAW,CAAC,MAAM,EAAE,GAAE,WAAW,CAAC,MAAM;IAQnG,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAC,MAAM;IAIpE,MAAM,CAAC,UAAU,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,aAAa,EAAC,WAAW,CAAC,MAAM;IAI/E,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,IAAI,EAAC,MAAM,EAAE,GAAE,WAAW,CAAC,MAAM;IAQzF,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAC,MAAM;IAItE,MAAM,CAAC,OAAO,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,UAAU,EAAC,WAAW,CAAC,MAAM;IAIzE,MAAM,CAAC,SAAS,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,GAAE,WAAW,CAAC,MAAM;IAQhE,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAC,WAAW,CAAC,MAAM;IAIhF,MAAM,CAAC,8BAA8B,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAC,WAAW,CAAC,MAAM;CAI3F", "file": "tensor.d.ts", "sourceRoot": "../src"}