-- =====================================================
-- KODEXGUARD NEW FEATURES MIGRATION
-- Migration untuk fitur-fitur baru yang telah diimplementasi
-- =====================================================

-- =====================================================
-- 1. UPDATE USERS TABLE - Add new fields
-- =====================================================

-- Add new columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS phone VARCHAR(20),
ADD COLUMN IF NOT EXISTS telegram_id BIGINT UNIQUE,
ADD COLUMN IF NOT EXISTS whatsapp_number VARCHAR(20),
ADD COLUMN IF NOT EXISTS status ENUM('active', 'inactive', 'suspended', 'banned') DEFAULT 'active',
ADD COLUMN IF NOT EXISTS avatar_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) DEFAULT 'Asia/Jakarta',
ADD COLUMN IF NOT EXISTS language VARCHAR(10) DEFAULT 'id';

-- Update role enum to include super_admin
ALTER TABLE users MODIFY COLUMN role ENUM('user', 'admin', 'super_admin', 'moderator') DEFAULT 'user';

-- Update plan enum to match new plans
ALTER TABLE users MODIFY COLUMN plan ENUM('Free', 'Student', 'Hobby', 'Bughunter', 'Cybersecurity', 'Pro', 'Expert', 'Elite') DEFAULT 'Free';

-- Add indexes for new columns
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
CREATE INDEX IF NOT EXISTS idx_users_telegram_id ON users(telegram_id);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);

-- =====================================================
-- 2. SUBSCRIPTION & PAYMENT TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS plans (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  type ENUM('free', 'student', 'hobby', 'bughunter', 'cybersecurity') NOT NULL,
  price DECIMAL(10,2) NOT NULL DEFAULT 0,
  currency VARCHAR(3) DEFAULT 'IDR',
  duration ENUM('daily', 'weekly', 'monthly', 'yearly') DEFAULT 'monthly',
  features JSON NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  is_popular BOOLEAN DEFAULT FALSE,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_type (type),
  INDEX idx_is_active (is_active)
);

CREATE TABLE IF NOT EXISTS subscriptions (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT NOT NULL,
  plan_id VARCHAR(50) NOT NULL,
  status ENUM('active', 'cancelled', 'expired', 'suspended') DEFAULT 'active',
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  auto_renew BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (plan_id) REFERENCES plans(id) ON DELETE RESTRICT,
  INDEX idx_user_id (user_id),
  INDEX idx_plan_id (plan_id),
  INDEX idx_status (status),
  INDEX idx_end_date (end_date)
);

CREATE TABLE IF NOT EXISTS payments (
  id VARCHAR(50) PRIMARY KEY,
  user_id INT NOT NULL,
  plan_id VARCHAR(50) NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'IDR',
  gateway ENUM('tripay', 'midtrans', 'xendit', 'manual') NOT NULL,
  status ENUM('pending', 'paid', 'failed', 'expired', 'cancelled') DEFAULT 'pending',
  gateway_payment_id VARCHAR(255),
  payment_url TEXT,
  expires_at TIMESTAMP NULL,
  paid_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (plan_id) REFERENCES plans(id) ON DELETE RESTRICT,
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_gateway (gateway),
  INDEX idx_created_at (created_at)
);

CREATE TABLE IF NOT EXISTS payment_logs (
  id VARCHAR(36) PRIMARY KEY,
  payment_id VARCHAR(50) NOT NULL,
  user_id INT NOT NULL,
  action ENUM('create', 'approve', 'reject', 'cancel', 'expire') NOT NULL,
  performed_by INT,
  details JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_payment_id (payment_id),
  INDEX idx_user_id (user_id),
  INDEX idx_action (action)
);

-- =====================================================
-- 3. BOT MANAGEMENT TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS bot_instances (
  id VARCHAR(50) PRIMARY KEY,
  type ENUM('whatsapp', 'telegram') NOT NULL,
  name VARCHAR(100) NOT NULL,
  status ENUM('connected', 'connecting', 'disconnected', 'error') DEFAULT 'disconnected',
  config JSON,
  last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_type (type),
  INDEX idx_status (status)
);

CREATE TABLE IF NOT EXISTS bot_messages (
  id VARCHAR(36) PRIMARY KEY,
  bot_id VARCHAR(50) NOT NULL,
  user_phone VARCHAR(20),
  user_telegram_id BIGINT,
  message_type ENUM('text', 'image', 'document', 'audio', 'video') DEFAULT 'text',
  message_content TEXT,
  command VARCHAR(50),
  response TEXT,
  status ENUM('received', 'processing', 'responded', 'failed') DEFAULT 'received',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (bot_id) REFERENCES bot_instances(id) ON DELETE CASCADE,
  INDEX idx_bot_id (bot_id),
  INDEX idx_user_phone (user_phone),
  INDEX idx_user_telegram_id (user_telegram_id),
  INDEX idx_command (command),
  INDEX idx_created_at (created_at)
);

-- =====================================================
-- 4. OSINT ENHANCEMENT TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS osint_queries (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT NOT NULL,
  type ENUM('email', 'phone', 'username', 'domain', 'ip', 'nik', 'npwp', 'imei', 'address') NOT NULL,
  value VARCHAR(500) NOT NULL,
  sources JSON,
  status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
  results JSON,
  confidence_score DECIMAL(3,2),
  risk_level ENUM('low', 'medium', 'high', 'critical'),
  error TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);

-- =====================================================
-- 5. SCORING & ACHIEVEMENT SYSTEM
-- =====================================================

CREATE TABLE IF NOT EXISTS score_history (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT NOT NULL,
  action_type ENUM('osint_query', 'vulnerability_scan', 'file_analysis', 'cve_report', 'community_contribution', 'bug_report', 'achievement_unlock') NOT NULL,
  points_awarded INT NOT NULL,
  multiplier DECIMAL(3,2) DEFAULT 1.00,
  description TEXT,
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_action_type (action_type),
  INDEX idx_created_at (created_at)
);

CREATE TABLE IF NOT EXISTS user_achievements (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT NOT NULL,
  achievement_id VARCHAR(50) NOT NULL,
  unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_achievement (user_id, achievement_id),
  INDEX idx_user_id (user_id),
  INDEX idx_achievement_id (achievement_id)
);

CREATE TABLE IF NOT EXISTS user_streaks (
  id VARCHAR(36) PRIMARY KEY,
  user_id INT NOT NULL,
  streak_type VARCHAR(50) NOT NULL,
  current_streak INT DEFAULT 0,
  longest_streak INT DEFAULT 0,
  last_activity_date DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_streak (user_id, streak_type),
  INDEX idx_user_id (user_id),
  INDEX idx_streak_type (streak_type)
);

-- =====================================================
-- 6. ADMIN & MONITORING TABLES
-- =====================================================

CREATE TABLE IF NOT EXISTS admin_logs (
  id VARCHAR(36) PRIMARY KEY,
  admin_id INT NOT NULL,
  action VARCHAR(100) NOT NULL,
  target_type VARCHAR(50),
  target_id VARCHAR(50),
  details JSON,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_admin_id (admin_id),
  INDEX idx_action (action),
  INDEX idx_target_type (target_type),
  INDEX idx_created_at (created_at)
);

-- =====================================================
-- 7. UPDATE API KEYS TABLE
-- =====================================================

-- Update api_keys table structure
ALTER TABLE api_keys 
ADD COLUMN IF NOT EXISTS status ENUM('active', 'inactive', 'revoked') DEFAULT 'active',
MODIFY COLUMN is_active BOOLEAN DEFAULT TRUE;

-- Add index for status
CREATE INDEX IF NOT EXISTS idx_api_keys_status ON api_keys(status);

-- =====================================================
-- 8. INSERT DEFAULT DATA
-- =====================================================

-- Insert default plans
INSERT IGNORE INTO plans (id, name, type, price, currency, duration, features, is_active, is_popular, description) VALUES
('free', 'Free', 'free', 0, 'IDR', 'monthly', 
 '{"maxScansPerDay": 5, "maxOsintPerDay": 10, "maxFilesPerDay": 3, "maxFileSize": 10485760, "apiAccess": false, "prioritySupport": false, "teamFeatures": false, "customIntegrations": false, "whiteLabel": false}',
 TRUE, FALSE, 'Perfect for getting started with cybersecurity'),

('student', 'Student', 'student', 50000, 'IDR', 'monthly',
 '{"maxScansPerDay": 50, "maxOsintPerDay": 100, "maxFilesPerDay": 25, "maxFileSize": 104857600, "apiAccess": true, "prioritySupport": true, "teamFeatures": false, "customIntegrations": false, "whiteLabel": false}',
 TRUE, TRUE, 'Special pricing for students and researchers'),

('hobby', 'Hobby', 'hobby', 150000, 'IDR', 'monthly',
 '{"maxScansPerDay": 100, "maxOsintPerDay": 200, "maxFilesPerDay": 50, "maxFileSize": 209715200, "apiAccess": true, "prioritySupport": true, "teamFeatures": false, "customIntegrations": true, "whiteLabel": false}',
 TRUE, FALSE, 'For hobbyists and security enthusiasts'),

('bughunter', 'Bug Hunter', 'bughunter', 300000, 'IDR', 'monthly',
 '{"maxScansPerDay": 500, "maxOsintPerDay": 1000, "maxFilesPerDay": 100, "maxFileSize": 524288000, "apiAccess": true, "prioritySupport": true, "teamFeatures": true, "customIntegrations": true, "whiteLabel": false}',
 TRUE, FALSE, 'Professional bug hunting and security research'),

('cybersecurity', 'Cybersecurity Pro', 'cybersecurity', 500000, 'IDR', 'monthly',
 '{"maxScansPerDay": -1, "maxOsintPerDay": -1, "maxFilesPerDay": -1, "maxFileSize": 1073741824, "apiAccess": true, "prioritySupport": true, "teamFeatures": true, "customIntegrations": true, "whiteLabel": true}',
 TRUE, FALSE, 'Enterprise-grade features for cybersecurity professionals');

-- Update admin user to super_admin role
UPDATE users SET role = 'super_admin' WHERE username = 'admin';

-- Insert default bot instance placeholders
INSERT IGNORE INTO bot_instances (id, type, name, status) VALUES
('whatsapp_main', 'whatsapp', 'KodeXGuard WhatsApp Bot', 'disconnected'),
('telegram_main', 'telegram', 'KodeXGuard Telegram Bot', 'disconnected');
