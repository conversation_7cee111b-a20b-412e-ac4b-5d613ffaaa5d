"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.semver = exports.patch = exports.newest = exports.minor = exports.latest = exports.greatest = exports.list = exports.defaultPrefix = void 0;
const path_1 = __importDefault(require("path"));
const spawn_please_1 = __importDefault(require("spawn-please"));
const keyValueBy_1 = __importDefault(require("../lib/keyValueBy"));
const npm = __importStar(require("./npm"));
/** Spawn bun. */
async function spawnBun(args, npmOptions = {}, spawnOptions = {}) {
    const fullArgs = [
        ...(npmOptions.prefix ? [`--prefix=${npmOptions.prefix}`] : []),
        ...(npmOptions.location === 'global' ? ['--global'] : []),
        ...(Array.isArray(args) ? args : [args]),
    ];
    return (0, spawn_please_1.default)('bun', fullArgs, spawnOptions);
}
/** Returns the global directory of bun. */
const defaultPrefix = async (options) => options.global
    ? options.prefix || process.env.BUN_INSTALL || path_1.default.dirname(await (0, spawn_please_1.default)('bun', ['pm', '-g', 'bin']))
    : undefined;
exports.defaultPrefix = defaultPrefix;
/**
 * (Bun) Fetches the list of all installed packages.
 */
const list = async (options = {}) => {
    const { default: stripAnsi } = await import('strip-ansi');
    // bun pm ls
    const stdout = await spawnBun(['pm', 'ls'], {
        ...(options.global ? { location: 'global' } : null),
        ...(options.prefix ? { prefix: options.prefix } : null),
    }, {
        env: {
            ...process.env,
            // Disable color to ensure the output is parsed correctly.
            // However, this may be ineffective in some environments (see stripAnsi below).
            // https://bun.sh/docs/runtime/configuration#environment-variables
            NO_COLOR: '1',
        },
        ...(options.cwd ? { cwd: options.cwd } : null),
        rejectOnError: false,
    });
    // Parse the output of `bun pm ls` into an object { [name]: version }.
    // When bun is spawned in the GitHub Actions environment, it outputs ANSI color. Unfortunately, it does not respect the `NO_COLOR` envirionment variable. Therefore, we have to manually strip ansi.
    const lines = stripAnsi(stdout).split('\n');
    const dependencies = (0, keyValueBy_1.default)(lines, line => {
        const match = line.match(/.* (.*?)@(.+)/);
        if (match) {
            const [, name, version] = match;
            return { [name]: version };
        }
        return null;
    });
    return dependencies;
};
exports.list = list;
exports.greatest = npm.greatest;
exports.latest = npm.latest;
exports.minor = npm.minor;
exports.newest = npm.newest;
exports.patch = npm.patch;
exports.semver = npm.semver;
exports.default = spawnBun;
//# sourceMappingURL=bun.js.map