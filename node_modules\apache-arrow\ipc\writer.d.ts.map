{"version": 3, "sources": ["ipc/writer.ts"], "names": [], "mappings": ";AAiBA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAEpC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAY,OAAO,EAAE,MAAM,YAAY,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAS,MAAM,cAAc,CAAC;AAC7C,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAEhD,OAAO,EAAE,SAAS,EAAU,MAAM,oBAAoB,CAAC;AACvD,OAAO,EAAE,aAAa,EAAmB,MAAM,YAAY,CAAC;AAE5D,OAAO,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAI/D,OAAO,EAAE,oBAAoB,EAAgB,MAAM,mBAAmB,CAAC;AACvE,OAAO,EAAE,WAAW,EAAwC,MAAM,mBAAmB,CAAC;AACtF,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,wBAAwB,EAAE,MAAM,qBAAqB,CAAC;AAG1F,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAE1E,MAAM,WAAW,8BAA8B;IAC3C;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB;;;;;OAKG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC;CAClC;AAED,qBAAa,iBAAiB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,eAAe,CAAC,UAAU,CAAE,YAAW,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAE3H,kBAAkB;WAEJ,WAAW,CAAC,OAAO,CAAC,EAAE,aAAa,GAAG;QAAE,WAAW,EAAE,OAAO,CAAA;KAAE,GAAG,MAAM;IAGrF,kBAAkB;WACJ,UAAU,CAAC,CAAC,SAAS,OAAO,EAEtC,gBAAgB,CAAC,EAAE,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG;QAAE,WAAW,EAAE,OAAO,CAAA;KAAE,EAE7E,gBAAgB,CAAC,EAAE;QAAE,aAAa,CAAC,EAAE,MAAM,CAAC;QAAC,IAAI,CAAC,EAAE,GAAG,CAAA;KAAE,GAC1D;QAAE,QAAQ,EAAE,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAAC,QAAQ,EAAE,cAAc,CAAC,UAAU,CAAC,CAAA;KAAE;gBAIpF,OAAO,CAAC,EAAE,8BAA8B;IAOpD,SAAS,CAAC,SAAS,SAAK;IACxB,SAAS,CAAC,QAAQ,UAAS;IAC3B,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC;IAChC,SAAS,CAAC,qBAAqB,EAAE,OAAO,CAAC;IAEzC,SAAS,CAAC,KAAK,6BAAwB;IACvC,SAAS,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,CAAQ;IACxC,SAAS,CAAC,iBAAiB,EAAE,SAAS,EAAE,CAAM;IAC9C,SAAS,CAAC,kBAAkB,EAAE,SAAS,EAAE,CAAM;IAC/C,SAAS,CAAC,iBAAiB,2BAA6B;IACxD,SAAS,CAAC,uBAAuB,sBAA6B;IAEvD,QAAQ,CAAC,IAAI,EAAE,IAAI,GAAG,MAAM;IAC5B,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC;IAIvC,YAAY,CAAC,IAAI,EAAE,IAAI,GAAG,UAAU;IACpC,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC;IAK/C,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;IAC1D,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAC7D,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAC1E,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAUvF,IAAW,MAAM,kBAAgC;IAC1C,CAAC,MAAM,CAAC,aAAa,CAAC;IACtB,WAAW,CAAC,OAAO,CAAC,EAAE,wBAAwB;IAC9C,YAAY,CAAC,OAAO,CAAC,EAAE,eAAe;IAEtC,KAAK;IAGL,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG;IAGlB,MAAM;IAIN,KAAK,CAAC,IAAI,GAAE,YAAY,CAAC,oBAAoB,CAAc,EAAE,MAAM,GAAE,MAAM,CAAC,CAAC,CAAC,GAAG,IAAW;IAoC5F,KAAK,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;IA+BlF,SAAS,CAAC,aAAa,CAAC,CAAC,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,SAAS,SAAI;IA0BnF,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,oBAAoB;IAW5C,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAKxC,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAOxC,SAAS,CAAC,WAAW;IAIrB,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM;IAItC,SAAS,CAAC,iBAAiB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;IAUjD,SAAS,CAAC,qBAAqB,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,UAAQ;IAU7E,SAAS,CAAC,iBAAiB,CAAC,OAAO,EAAE,eAAe,EAAE;IActD,SAAS,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;CAmBrD;AAED,cAAc;AACd,qBAAa,uBAAuB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,iBAAiB,CAAC,CAAC,CAAC;WACxE,QAAQ,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,8BAA8B,GAAG,uBAAuB,CAAC,CAAC,CAAC;WACnJ,QAAQ,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,KAAK,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,8BAA8B,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;WACtJ,QAAQ,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,KAAK,EAAE,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,8BAA8B,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;WACnK,QAAQ,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,8BAA8B,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;CAW1L;AAED,cAAc;AACd,qBAAa,qBAAqB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,iBAAiB,CAAC,CAAC,CAAC;WACtE,QAAQ,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,qBAAqB,CAAC,CAAC,CAAC;WACvG,QAAQ,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,KAAK,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;WAC1G,QAAQ,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,KAAK,EAAE,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;WACvH,QAAQ,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;;IAkB3I,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAIxC,SAAS,CAAC,qBAAqB,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,UAAQ;IAO7E,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;CAW3C;AAED,cAAc;AACd,qBAAa,qBAAqB,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,CAAE,SAAQ,iBAAiB,CAAC,CAAC,CAAC;WAEtE,QAAQ,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,IAAI,EAAE,OAAO,iBAAiB,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,qBAAqB,CAAC,CAAC,CAAC;WAEvI,QAAQ,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,IAAI,EAAE,OAAO,iBAAiB,EAAE,KAAK,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;WAC1I,QAAQ,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,IAAI,EAAE,OAAO,iBAAiB,EAAE,KAAK,EAAE,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;WACvJ,QAAQ,CAAC,CAAC,SAAS,OAAO,GAAG,GAAG,EAAE,IAAI,EAAE,OAAO,iBAAiB,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAM3K,OAAO,CAAC,cAAc,CAAgB;IACtC,OAAO,CAAC,8BAA8B,CAAgB;;IAStD,SAAS,CAAC,aAAa;IAEvB,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IACxC,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAGxC,SAAS,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;IAMlD,SAAS,CAAC,qBAAqB,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,UAAQ;IAM7E,SAAS,CAAC,iBAAiB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;IAK1C,KAAK;CA2Bf", "file": "writer.d.ts", "sourceRoot": "../src"}