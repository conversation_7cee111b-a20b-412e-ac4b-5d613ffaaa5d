import { db } from '../database'
import { readdir, readFile } from 'fs/promises'
import { join } from 'path'

interface MigrationRecord {
  id: string
  name: string
  applied_at: string
}

/**
 * Run database migrations
 * This function will run all migrations in the migrations directory
 * that have not been applied yet
 */
export async function runMigrations(): Promise<boolean> {
  try {
    console.log('🔄 Running database migrations...')

    // Create migrations table if it doesn't exist
    await createMigrationsTable()

    // Get applied migrations
    const appliedMigrations = await getAppliedMigrations()
    console.log(`ℹ️ Found ${appliedMigrations.length} previously applied migrations`)

    // Get all migration files
    const migrationsDir = join(process.cwd(), 'lib', 'database', 'migrations')
    const migrationFiles = await readdir(migrationsDir)
    
    // Filter SQL files and sort them
    const sqlFiles = migrationFiles
      .filter(file => file.endsWith('.sql'))
      .sort((a, b) => a.localeCompare(b))

    console.log(`ℹ️ Found ${sqlFiles.length} migration files`)

    // Filter out migrations that have already been applied
    const pendingMigrations = sqlFiles.filter(file => {
      const migrationId = file.replace('.sql', '')
      return !appliedMigrations.some(m => m.id === migrationId)
    })

    console.log(`ℹ️ Found ${pendingMigrations.length} pending migrations`)

    if (pendingMigrations.length === 0) {
      console.log('✅ No pending migrations to apply')
      return true
    }

    // Apply pending migrations
    for (const migrationFile of pendingMigrations) {
      const migrationId = migrationFile.replace('.sql', '')
      const migrationPath = join(migrationsDir, migrationFile)
      
      console.log(`🔄 Applying migration: ${migrationFile}`)
      
      try {
        // Read migration file
        const migrationSql = await readFile(migrationPath, 'utf-8')
        
        // Split migration into statements
        const statements = migrationSql
          .split(';')
          .map(stmt => stmt.trim())
          .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
        
        // Start transaction
        await db.query('START TRANSACTION')
        
        // Execute each statement
        for (const statement of statements) {
          try {
            await db.query(statement)
          } catch (error: any) {
            console.error(`❌ Error executing statement: ${error.message || error}`)
            throw error
          }
        }
        
        // Record migration
        await db.query(
          'INSERT INTO migrations (id, name, applied_at) VALUES (?, ?, NOW())',
          [migrationId, migrationFile]
        )
        
        // Commit transaction
        await db.query('COMMIT')
        
        console.log(`✅ Migration applied: ${migrationFile}`)
      } catch (error) {
        // Rollback transaction
        await db.query('ROLLBACK')
        console.error(`❌ Migration failed: ${migrationFile}`, error)
        throw error
      }
    }

    console.log('✅ All migrations applied successfully')
    return true
  } catch (error) {
    console.error('❌ Migration process failed:', error)
    return false
  }
}

/**
 * Create migrations table if it doesn't exist
 */
async function createMigrationsTable(): Promise<void> {
  try {
    await db.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id VARCHAR(100) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_applied_at (applied_at)
      )
    `)
  } catch (error) {
    console.error('❌ Failed to create migrations table:', error)
    throw error
  }
}

/**
 * Get all applied migrations
 */
async function getAppliedMigrations(): Promise<MigrationRecord[]> {
  try {
    const result = await db.query('SELECT * FROM migrations ORDER BY applied_at')
    return result as MigrationRecord[]
  } catch (error) {
    console.error('❌ Failed to get applied migrations:', error)
    return []
  }
}

/**
 * Run a specific migration file
 */
export async function runMigrationFile(filePath: string): Promise<boolean> {
  try {
    console.log(`🔄 Running migration file: ${filePath}`)
    
    // Read migration file
    const migrationSql = await readFile(filePath, 'utf-8')
    
    // Split migration into statements
    const statements = migrationSql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    // Execute each statement
    for (const statement of statements) {
      try {
        await db.query(statement)
      } catch (error: any) {
        // Ignore "already exists" errors
        if (!error.message?.includes('already exists')) {
          console.error(`❌ Error executing statement: ${error.message || error}`)
          throw error
        }
      }
    }
    
    console.log(`✅ Migration file applied: ${filePath}`)
    return true
  } catch (error) {
    console.error(`❌ Migration file failed: ${filePath}`, error)
    return false
  }
}

// If this file is run directly
if (require.main === module) {
  runMigrations()
    .then(success => {
      if (success) {
        console.log('✅ Migrations completed successfully')
        process.exit(0)
      } else {
        console.error('❌ Migrations failed')
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('❌ Unhandled error during migrations:', error)
      process.exit(1)
    })
}
