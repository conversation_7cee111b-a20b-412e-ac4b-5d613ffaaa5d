{"version": 3, "sources": ["../../src/lib/is-error.ts"], "names": ["isError", "getProperError", "err", "process", "env", "NODE_ENV", "Error", "isPlainObject", "JSON", "stringify"], "mappings": ";;;;;;;;;;;;;;;IAWA,OAIC;eAJuBA;;IAMRC,cAAc;eAAdA;;;+BAjBc;AAWf,SAASD,QAAQE,GAAY;IAC1C,OACE,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,UAAUA,OAAO,aAAaA;AAE7E;AAEO,SAASD,eAAeC,GAAY;IACzC,IAAIF,QAAQE,MAAM;QAChB,OAAOA;IACT;IAEA,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,wDAAwD;QACxD,2BAA2B;QAC3B,IAAI,OAAOH,QAAQ,aAAa;YAC9B,OAAO,IAAII,MACT,oCACE;QAEN;QAEA,IAAIJ,QAAQ,MAAM;YAChB,OAAO,IAAII,MACT,8BACE;QAEN;IACF;IAEA,OAAO,IAAIA,MAAMC,IAAAA,4BAAa,EAACL,OAAOM,KAAKC,SAAS,CAACP,OAAOA,MAAM;AACpE"}