{"version": 3, "file": "router.d.ts", "sourceRoot": "", "sources": ["../src/router.ts"], "names": [], "mappings": "AAAA,cAAc;AAEd,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,cAAc,CAAA;AAExD,OAAO,OAAO,MAAM,WAAW,CAAA;AAE/B,KAAK,qBAAqB,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAA;AAEpD,KAAK,OAAO,CAAC,QAAQ,SAAS,OAAO,IAAI,CAAC,GAAG,EAAE,QAAQ,KAAK;IAC1D,KAAK,EAAE,MAAM,CAAA;IACb,OAAO,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAA;IAC3B,KAAK,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAA;CACnC,GAAG,IAAI,CAAA;AAER,wDAAwD;AACxD,qBAAa,MAAM,CAAC,CAAC,SAAS,OAAO,CAAE,YAAW,aAAa,CAAC,CAAC,CAAC;IAI9D,OAAO,CAAC,QAAQ,CAAC,OAAO;IACjB,QAAQ;IAJjB,OAAO,CAAC,gBAAgB,CAAqC;gBAG1C,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,EAC7B,QAAQ,6BAAmC;IAOpD,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,GAAG,EAAE,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAQ9D,SAAS,CAAC,GAAG,GAAG,EAAE,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAQtD,UAAU;CAWX"}