'use server'

import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import os from 'os'

export async function GET(request: NextRequest) {
  try {
    // Authenticate request - only admins can access
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      )
    }

    // Get system statistics
    const cpus = os.cpus()
    const totalMem = os.totalmem()
    const freeMem = os.freemem()
    const usedMem = totalMem - freeMem
    const loadAvg = os.loadavg()
    const uptime = os.uptime()

    // Calculate CPU usage (simplified)
    const cpuUsage = Math.random() * 100 // Mock data - in real implementation, calculate actual CPU usage

    // Mock disk usage (in real implementation, use fs.statSync or similar)
    const diskTotal = 1024 * 1024 * 1024 * 100 // 100GB
    const diskUsed = diskTotal * (0.3 + Math.random() * 0.4) // 30-70% used
    const diskPercentage = (diskUsed / diskTotal) * 100

    // Mock network stats (in real implementation, read from /proc/net/dev or similar)
    const networkInbound = Math.random() * 1024 * 1024 // Random MB/s
    const networkOutbound = Math.random() * 1024 * 1024 // Random MB/s

    const systemStats = {
      cpu: {
        usage: cpuUsage,
        cores: cpus.length,
        temperature: 45 + Math.random() * 20 // Mock temperature 45-65°C
      },
      memory: {
        used: usedMem,
        total: totalMem,
        percentage: (usedMem / totalMem) * 100
      },
      disk: {
        used: diskUsed,
        total: diskTotal,
        percentage: diskPercentage
      },
      network: {
        inbound: networkInbound,
        outbound: networkOutbound
      },
      uptime: uptime,
      loadAverage: loadAvg
    }

    return NextResponse.json({
      success: true,
      data: systemStats
    })

  } catch (error) {
    console.error('Error getting system stats:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get system stats'
      },
      { status: 500 }
    )
  }
}
