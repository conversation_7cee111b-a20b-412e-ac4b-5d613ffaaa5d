'use server'

import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { db } from '@/lib/database'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate request - only admins can approve payments
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      )
    }

    const paymentId = params.id

    // Get payment details
    const [paymentRows] = await db.query(
      'SELECT * FROM payments WHERE id = ? AND status = ? AND gateway = ?',
      [paymentId, 'pending', 'manual']
    )

    if (!paymentRows || (paymentRows as any[]).length === 0) {
      return NextResponse.json(
        { success: false, message: 'Payment not found or not eligible for approval' },
        { status: 404 }
      )
    }

    const payment = (paymentRows as any[])[0]

    // Start transaction
    await db.beginTransaction()

    try {
      // Update payment status
      await db.query(
        'UPDATE payments SET status = ?, paid_at = NOW(), updated_at = NOW() WHERE id = ?',
        ['paid', paymentId]
      )

      // Get plan details
      const [planRows] = await db.query(
        'SELECT * FROM plans WHERE id = ?',
        [payment.plan_id]
      )

      if (!planRows || (planRows as any[]).length === 0) {
        throw new Error('Plan not found')
      }

      const plan = (planRows as any[])[0]

      // Calculate subscription end date based on plan duration
      let durationInDays = 30 // Default to monthly
      switch (plan.duration) {
        case 'daily':
          durationInDays = 1
          break
        case 'weekly':
          durationInDays = 7
          break
        case 'monthly':
          durationInDays = 30
          break
        case 'yearly':
          durationInDays = 365
          break
      }

      // Create or update user subscription
      const [subscriptionRows] = await db.query(
        'SELECT * FROM subscriptions WHERE user_id = ? AND plan_id = ? AND status = ?',
        [payment.user_id, payment.plan_id, 'active']
      )

      if (subscriptionRows && (subscriptionRows as any[]).length > 0) {
        // Update existing subscription
        const subscription = (subscriptionRows as any[])[0]
        await db.query(`
          UPDATE subscriptions 
          SET 
            end_date = DATE_ADD(end_date, INTERVAL ? DAY),
            updated_at = NOW()
          WHERE id = ?
        `, [durationInDays, subscription.id])
      } else {
        // Create new subscription
        await db.query(`
          INSERT INTO subscriptions (
            user_id, plan_id, status, start_date, end_date, created_at
          ) VALUES (
            ?, ?, 'active', NOW(), DATE_ADD(NOW(), INTERVAL ? DAY), NOW()
          )
        `, [payment.user_id, payment.plan_id, durationInDays])
      }

      // Update user's plan
      await db.query(
        'UPDATE users SET plan = ? WHERE id = ?',
        [plan.type, payment.user_id]
      )

      // Create payment log
      await db.query(`
        INSERT INTO payment_logs (
          payment_id, user_id, action, performed_by, details, created_at
        ) VALUES (
          ?, ?, 'approve', ?, ?, NOW()
        )
      `, [
        paymentId, 
        payment.user_id, 
        authResult.user.id,
        JSON.stringify({
          adminName: authResult.user.username,
          adminEmail: authResult.user.email
        })
      ])

      // Commit transaction
      await db.commit()

      return NextResponse.json({
        success: true,
        message: 'Payment approved successfully',
        data: {
          paymentId: paymentId
        }
      })
    } catch (error) {
      // Rollback transaction on error
      await db.rollback()
      throw error
    }
  } catch (error) {
    console.error('Error approving payment:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to approve payment'
      },
      { status: 500 }
    )
  }
}
