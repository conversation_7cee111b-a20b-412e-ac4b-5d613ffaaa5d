import { Index } from '../types/IndexType';
import { Options } from '../types/Options';
import { PackageFile } from '../types/PackageFile';
import { VersionSpec } from '../types/VersionSpec';
type Run = (options?: Options) => Promise<PackageFile | Index<VersionSpec> | void>;
/** Iteratively installs upgrades and runs tests to identify breaking upgrades. */
declare const doctor: (run: Run, options: Options) => Promise<void>;
export default doctor;
