"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const logging_1 = require("../lib/logging");
const chalk_1 = __importDefault(require("./chalk"));
/** Print an error. Exit the process if in CLI mode. */
function programError(options, message, { color = true, } = {}) {
    if (options.cli) {
        (0, logging_1.print)(options, color ? chalk_1.default.red(message) : message, null, 'error');
        process.exit(1);
    }
    else {
        throw new Error(message);
    }
}
exports.default = programError;
//# sourceMappingURL=programError.js.map