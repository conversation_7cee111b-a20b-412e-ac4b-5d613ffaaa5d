{"version": 3, "sources": ["ipc/metadata/file.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;;;AAErB,yDAAyD;AAEzD,gDAAoD;AACpD,kDAAuD;AAEvD,2CAA2C;AAE3C,IAAO,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;AACrC,IAAO,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;AAE3C,+CAAyC;AACzC,2CAAgD;AAChD,oDAAoD;AAEpD,oDAAsD;AAEtD,cAAc;AACd,MAAM,OAAO;IAET,kBAAkB;IACX,MAAM,CAAC,MAAM,CAAC,GAAyB;QAC1C,GAAG,GAAG,IAAI,UAAU,CAAC,IAAA,wBAAY,EAAC,GAAG,CAAC,CAAC,CAAC;QACxC,MAAM,MAAM,GAAG,kBAAO,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC5C,MAAM,MAAM,GAAG,kBAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAG,EAAE,IAAI,GAAG,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5E,OAAO,IAAI,aAAa,CAAC,MAAM,EAAE,MAAM,CAAY,CAAC;IACxD,CAAC;IAED,kBAAkB;IACX,MAAM,CAAC,MAAM,CAAC,MAAe;QAEhC,MAAM,CAAC,GAAY,IAAI,OAAO,EAAE,CAAC;QACjC,MAAM,YAAY,GAAG,kBAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAErD,kBAAO,CAAC,wBAAwB,CAAC,CAAC,EAAE,MAAM,CAAC,gBAAgB,CAAC,CAAC;QAC7D,KAAK,MAAM,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7D,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5B,CAAC;QACD,MAAM,mBAAmB,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC;QAE1C,kBAAO,CAAC,uBAAuB,CAAC,CAAC,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC;QAC3D,KAAK,MAAM,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC;YACjE,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5B,CAAC;QAED,MAAM,uBAAuB,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC;QAE9C,kBAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACvB,kBAAO,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;QACnC,kBAAO,CAAC,UAAU,CAAC,CAAC,EAAE,yBAAe,CAAC,EAAE,CAAC,CAAC;QAC1C,kBAAO,CAAC,gBAAgB,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;QACjD,kBAAO,CAAC,eAAe,CAAC,CAAC,EAAE,uBAAuB,CAAC,CAAC;QACpD,kBAAO,CAAC,kBAAkB,CAAC,CAAC,EAAE,kBAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpD,OAAO,CAAC,CAAC,YAAY,EAAE,CAAC;IAC5B,CAAC;IAID,IAAW,gBAAgB,KAAK,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;IACpE,IAAW,eAAe,KAAK,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;IAEvE,YAAmB,MAAc,EACtB,UAA2B,yBAAe,CAAC,EAAE,EACpD,aAA2B,EAAE,iBAA+B;QAF7C,WAAM,GAAN,MAAM,CAAQ;QACtB,YAAO,GAAP,OAAO,CAAsC;QAEpD,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC,CAAC;QACvD,iBAAiB,IAAI,CAAC,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC,CAAC;IACvE,CAAC;IAEM,CAAC,aAAa;QACjB,KAAK,IAAI,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;YAC1D,IAAI,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;gBAAC,MAAM,KAAK,CAAC;YAAC,CAAC;QACxD,CAAC;IACL,CAAC;IAEM,CAAC,iBAAiB;QACrB,KAAK,IAAI,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;YACzD,IAAI,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC;gBAAC,MAAM,KAAK,CAAC;YAAC,CAAC;QAC5D,CAAC;IACL,CAAC;IAEM,cAAc,CAAC,KAAa;QAC/B,OAAO,KAAK,IAAI,CAAC;eACV,KAAK,GAAG,IAAI,CAAC,gBAAgB;eAC7B,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;IAC9C,CAAC;IAEM,kBAAkB,CAAC,KAAa;QACnC,OAAO,KAAK,IAAI,CAAC;eACV,KAAK,GAAG,IAAI,CAAC,eAAe;eAC5B,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;IAClD,CAAC;CACJ;AAEmB,yBAAM;AAE1B,cAAc;AACd,MAAM,aAAc,SAAQ,OAAO;IAE/B,IAAW,gBAAgB,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;IAC5E,IAAW,eAAe,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;IAE1E,YAAY,MAAc,EAAY,OAAgB;QAClD,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QADC,YAAO,GAAP,OAAO,CAAS;IAEtD,CAAC;IAEM,cAAc,CAAC,KAAa;QAC/B,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACpD,IAAI,SAAS,EAAE,CAAC;gBAAC,OAAO,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAAC,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,kBAAkB,CAAC,KAAa;QACnC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YACnD,IAAI,SAAS,EAAE,CAAC;gBAAC,OAAO,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAAC,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAED,cAAc;AACd,MAAa,SAAS;IAElB,kBAAkB;IACX,MAAM,CAAC,MAAM,CAAC,KAAa;QAC9B,OAAO,IAAI,SAAS,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,KAAK,CAAC,UAAU,EAAE,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACrF,CAAC;IAED,kBAAkB;IACX,MAAM,CAAC,MAAM,CAAC,CAAU,EAAE,SAAoB;QACjD,MAAM,EAAE,cAAc,EAAE,GAAG,SAAS,CAAC;QACrC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACxC,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAChD,OAAO,gBAAM,CAAC,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;IACrE,CAAC;IAMD,YAAY,cAAsB,EAAE,UAA2B,EAAE,MAAuB;QACpF,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,MAAM,GAAG,IAAA,0BAAc,EAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,UAAU,GAAG,IAAA,0BAAc,EAAC,UAAU,CAAC,CAAC;IACjD,CAAC;CACJ;AAxBD,8BAwBC", "file": "file.js", "sourceRoot": "../../src"}