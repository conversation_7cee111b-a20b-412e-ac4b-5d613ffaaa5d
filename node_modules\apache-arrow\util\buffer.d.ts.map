{"version": 3, "sources": ["util/buffer.ts"], "names": [], "mappings": "AAkBA,OAAO,EAAE,UAAU,EAAE,qBAAqB,EAAE,sBAAsB,EAAE,MAAM,kBAAkB,CAAC;AAE7F,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AA6BzC,cAAc;AACd,wBAAgB,MAAM,CAAC,OAAO,SAAS,eAAe,EAAE,OAAO,SAAS,eAAe,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,SAAI,EAAE,gBAAgB,SAAoB,WAMpL;AAED,cAAc;AACd,wBAAgB,eAAe,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE,MAAM,CAAC,CAuB9G;AAED,cAAc;AACd,MAAM,MAAM,oBAAoB,GAAG,eAAe,GAAG,eAAe,GAAG,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,UAAU,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,GACzM,cAAc,CAAC,eAAe,GAAG,eAAe,GAAG,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,UAAU,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC,GAC1L,wBAAwB,CAAC,eAAe,GAAG,eAAe,GAAG,eAAe,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,UAAU,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC,CAAC;AAEzM,cAAc;AACd,wBAAgB,iBAAiB,CAC7B,CAAC,SAAS,qBAAqB,CAAC,GAAG,CAAC,GAAG,sBAAsB,CAAC,GAAG,CAAC,EACpE,mBAAmB,EAAE,GAAG,EAAE,KAAK,EAAE,oBAAoB,GAAG,YAAY,CAAC,CAAC,CAAC,CAmBxE;AAED,cAAc,CAAC,eAAO,MAAM,WAAW,UAAW,oBAAoB,QAAwC,CAAC;AAC/G,cAAc,CAAC,eAAO,MAAM,YAAY,UAAW,oBAAoB,QAAyC,CAAC;AACjH,cAAc,CAAC,eAAO,MAAM,YAAY,UAAW,oBAAoB,QAAyC,CAAC;AACjH,cAAc,CAAC,eAAO,MAAM,eAAe,UAAW,oBAAoB,QAA4C,CAAC;AACvH,cAAc,CAAC,eAAO,MAAM,YAAY,UAAW,oBAAoB,QAAyC,CAAC;AACjH,cAAc,CAAC,eAAO,MAAM,aAAa,UAAW,oBAAoB,QAA0C,CAAC;AACnH,cAAc,CAAC,eAAO,MAAM,aAAa,UAAW,oBAAoB,QAA0C,CAAC;AACnH,cAAc,CAAC,eAAO,MAAM,gBAAgB,UAAW,oBAAoB,QAA6C,CAAC;AACzH,cAAc,CAAC,eAAO,MAAM,cAAc,UAAW,oBAAoB,QAA2C,CAAC;AACrH,cAAc,CAAC,eAAO,MAAM,cAAc,UAAW,oBAAoB,QAA2C,CAAC;AACrH,cAAc,CAAC,eAAO,MAAM,mBAAmB,UAAW,oBAAoB,QAAgD,CAAC;AAE/H,cAAc;AACd,KAAK,4BAA4B,GAAG,QAAQ,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC;AAK1F,cAAc;AACd,wBAAiB,yBAAyB,CAAC,CAAC,SAAS,UAAU,EAAE,SAAS,EAAE,qBAAqB,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,4BAA4B,uCAgBzI;AAED,cAAc,CAAC,eAAO,MAAM,mBAAmB,UAAW,4BAA4B,wDAAgD,CAAC;AACvI,cAAc,CAAC,eAAO,MAAM,oBAAoB,UAAW,4BAA4B,0DAAiD,CAAC;AACzI,cAAc,CAAC,eAAO,MAAM,oBAAoB,UAAW,4BAA4B,0DAAiD,CAAC;AACzI,cAAc,CAAC,eAAO,MAAM,oBAAoB,UAAW,4BAA4B,0DAAiD,CAAC;AACzI,cAAc,CAAC,eAAO,MAAM,qBAAqB,UAAW,4BAA4B,4DAAkD,CAAC;AAC3I,cAAc,CAAC,eAAO,MAAM,qBAAqB,UAAW,4BAA4B,4DAAkD,CAAC;AAC3I,cAAc,CAAC,eAAO,MAAM,sBAAsB,UAAW,4BAA4B,8DAAmD,CAAC;AAC7I,cAAc,CAAC,eAAO,MAAM,sBAAsB,UAAW,4BAA4B,8DAAmD,CAAC;AAC7I,cAAc,CAAC,eAAO,MAAM,2BAA2B,UAAW,4BAA4B,wEAAwD,CAAC;AAEvJ,cAAc;AACd,KAAK,iCAAiC,GAAG,aAAa,CAAC,oBAAoB,CAAC,GAAG,QAAQ,CAAC,oBAAoB,CAAC,GAAG,WAAW,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC;AAEzK,cAAc;AACd,wBAAuB,8BAA8B,CAAC,CAAC,SAAS,UAAU,EAAE,SAAS,EAAE,qBAAqB,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,iCAAiC,GAAG,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC,CAiCpM;AAED,cAAc,CAAC,eAAO,MAAM,wBAAwB,UAAW,iCAAiC,6DAAqD,CAAC;AACtJ,cAAc,CAAC,eAAO,MAAM,yBAAyB,UAAW,iCAAiC,+DAAsD,CAAC;AACxJ,cAAc,CAAC,eAAO,MAAM,yBAAyB,UAAW,iCAAiC,+DAAsD,CAAC;AACxJ,cAAc,CAAC,eAAO,MAAM,yBAAyB,UAAW,iCAAiC,+DAAsD,CAAC;AACxJ,cAAc,CAAC,eAAO,MAAM,0BAA0B,UAAW,iCAAiC,iEAAuD,CAAC;AAC1J,cAAc,CAAC,eAAO,MAAM,0BAA0B,UAAW,iCAAiC,iEAAuD,CAAC;AAC1J,cAAc,CAAC,eAAO,MAAM,2BAA2B,UAAW,iCAAiC,mEAAwD,CAAC;AAC5J,cAAc,CAAC,eAAO,MAAM,2BAA2B,UAAW,iCAAiC,mEAAwD,CAAC;AAC5J,cAAc,CAAC,eAAO,MAAM,gCAAgC,UAAW,iCAAiC,6EAA6D,CAAC;AAEtK,cAAc;AACd,wBAAgB,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,GAAG,UAAU,CAAC;AACzG,wBAAgB,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,GAAG,aAAa,CAAC;AAa/G,cAAc;AACd,wBAAgB,gBAAgB,CAAC,CAAC,SAAS,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,WAQpE", "file": "buffer.d.ts", "sourceRoot": "../src"}