"use strict";
// automatically generated by the FlatBuffers compiler, do not modify
Object.defineProperty(exports, "__esModule", { value: true });
exports.FloatingPoint = void 0;
const flatbuffers = require("flatbuffers");
const precision_js_1 = require("./precision.js");
class FloatingPoint {
    constructor() {
        this.bb = null;
        this.bb_pos = 0;
    }
    __init(i, bb) {
        this.bb_pos = i;
        this.bb = bb;
        return this;
    }
    static getRootAsFloatingPoint(bb, obj) {
        return (obj || new FloatingPoint()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
    }
    static getSizePrefixedRootAsFloatingPoint(bb, obj) {
        bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
        return (obj || new FloatingPoint()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
    }
    precision() {
        const offset = this.bb.__offset(this.bb_pos, 4);
        return offset ? this.bb.readInt16(this.bb_pos + offset) : precision_js_1.Precision.HALF;
    }
    static startFloatingPoint(builder) {
        builder.startObject(1);
    }
    static addPrecision(builder, precision) {
        builder.addFieldInt16(0, precision, precision_js_1.Precision.HALF);
    }
    static endFloatingPoint(builder) {
        const offset = builder.endObject();
        return offset;
    }
    static createFloatingPoint(builder, precision) {
        FloatingPoint.startFloatingPoint(builder);
        FloatingPoint.addPrecision(builder, precision);
        return FloatingPoint.endFloatingPoint(builder);
    }
}
exports.FloatingPoint = FloatingPoint;

//# sourceMappingURL=floating-point.js.map
