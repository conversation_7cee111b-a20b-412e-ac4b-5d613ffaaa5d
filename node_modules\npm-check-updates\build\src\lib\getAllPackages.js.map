{"version": 3, "file": "getAllPackages.js", "sourceRoot": "", "sources": ["../../../src/lib/getAllPackages.ts"], "names": [], "mappings": ";;;;;AAAA,2DAA4B;AAC5B,oDAA2B;AAC3B,sDAA0B;AAC1B,gDAAuB;AACvB,0DAAiC;AAIjC,gEAAuC;AACvC,wFAA+D;AAC/D,kEAAyC;AAIzC,8GAA8G;AAC9G,MAAM,kBAAkB,GAAG,KAAK,EAAE,OAAe,EAAkC,EAAE;IACnF,MAAM,kBAAkB,GAAG,cAAI,CAAC,IAAI,CAAC,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,qBAAqB,CAAC,CAAA;IAClF,IAAI,iBAAyB,CAAA;IAC7B,IAAI;QACF,iBAAiB,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA;KACnE;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,IAAI,CAAA;KACZ;IACD,OAAO,iBAAI,CAAC,IAAI,CAAC,iBAAiB,CAAmB,CAAA;AACvD,CAAC,CAAA;AAED;;;;;;GAMG;AACH,KAAK,UAAU,wBAAwB,CACrC,OAAgB,EAChB,sBAA8B,EAC9B,eAAuB,EACvB,GAAW;IAEX,qEAAqE;IACrE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,MAAM,IAAA,qBAAW,EAAC,EAAE,GAAG,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAA;IAChH,MAAM,OAAO,GAAgB,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;IAExF,MAAM,gBAAgB,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC,MAAM,kBAAkB,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,CAAA;IACxF,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,QAAQ,CAAA;IAElG,IAAI,CAAC,UAAU,EAAE;QACf,IAAA,sBAAY,EACV,OAAO,EACP,6DACE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAC7B,4EAA4E,CAC7E,CAAA;KACF;IAED,mCAAmC;IACnC,qDAAqD;IACrD,oBAAoB;IACpB,MAAM,oBAAoB,GAAa,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CACxE,cAAI;SACD,IAAI,CAAC,GAAG,EAAE,SAAS,EAAE,cAAc,CAAC;QACrC,+CAA+C;SAC9C,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACvB,CAAA;IAED,sCAAsC;IACtC,MAAM,4BAA4B,GAAa;QAC7C,GAAG,gBAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACnC,MAAM,EAAE,CAAC,oBAAoB,CAAC;SAC/B,CAAC;KACH,CAAA;IAED,gDAAgD;IAChD,0DAA0D;IAC1D,uGAAuG;IACvG,MAAM,wBAAwB,GAAkB;QAC9C,GAAG,CAAC,MAAM,OAAO,CAAC,GAAG,CACnB,4BAA4B,CAAC,GAAG,CAAC,KAAK,EAAE,QAAgB,EAAwB,EAAE;YAChF,MAAM,IAAI,GAAgB,MAAM,IAAA,iCAAuB,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;YAC1E,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC7D,OAAO,IAAI,CAAA;QACb,CAAC,CAAC,CACH,CAAC;KACH,CAAA;IAED,0BAA0B;IAC1B,uGAAuG;IACvG,MAAM,wBAAwB,GAAa,wBAAwB,CAAC,GAAG,CACrE,CAAC,WAAwB,EAAU,EAAE,CAAC,WAAW,CAAC,IAAI,IAAI,EAAE,CAC7D,CAAA;IAED,MAAM,gBAAgB,GAAG,OAAO,CAAC,UAAU,KAAK,IAAI,CAAA;IACpD,IAAI,CAAC,gBAAgB,EAAE;QACrB,eAAe;QACf,OAAO,CAAC,wBAAwB,EAAE,wBAAwB,CAAC,CAAA;KAC5D;IAED,yBAAyB;IACzB,cAAc;IACd,MAAM,6BAA6B,GAAkB,wBAAwB,CAAC,MAAM,CAAC,CAAC,WAAwB,EAAE,EAAE;;QAChH,0CAA0C;QAC1C,oBAAoB;QACpB,OAAA,MAAA,OAAO,CAAC,SAAS,0CAAE,IAAI,CAAC,CAAC,SAAiB,EAAE,EAAE;QAC5C,0CAA0C;QAC1C,oBAAoB;QACpB,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI,CACd,CAAC,gBAAwB,EAAE,EAAE,CAC3B,WAAW,CAAC,IAAI,KAAK,SAAS;YAC9B,WAAW,CAAC,QAAQ;gBAClB,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,cAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,sBAAsB,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAC1G,CACF,CAAA;KAAA,CACF,CAAA;IACD,OAAO,CAAC,6BAA6B,EAAE,wBAAwB,CAAC,CAAA;AAClE,CAAC;AAED;;;;;GAKG;AACH,KAAK,UAAU,cAAc,CAAC,OAAgB;IAC5C,MAAM,sBAAsB,GAAG,OAAO,CAAC,WAAW,IAAI,cAAc,CAAA;IACpE,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAA,mBAAS,EAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IACvD,MAAM,eAAe,GAAG,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAA;IAE9G,MAAM,aAAa,GACjB,OAAO,CAAC,UAAU,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAA;IAEpG,IAAI,YAAY,GAAkB,EAAE,CAAA;IAEpC,qCAAqC;IACrC,2FAA2F;IAC3F,MAAM,kBAAkB,GAAY,CAAC,aAAa,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAA;IAC3E,IAAI,kBAAkB,EAAE;QACtB,iBAAiB;QACjB,oBAAoB;QACpB,oEAAoE;QACpE,MAAM,WAAW,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QACvD,MAAM,gBAAgB,GAAG,gBAAM,CAAC,IAAI,CAAC,WAAW,EAAE;YAChD,MAAM,EAAE,CAAC,oBAAoB,CAAC;SAC/B,CAAC,CAAA;QACF,iDAAiD;QACjD,MAAM,YAAY,GAAG;YACnB,GAAG,CAAC,MAAM,OAAO,CAAC,GAAG,CACnB,gBAAgB,CAAC,GAAG,CAClB,KAAK,EAAE,WAAmB,EAAwB,EAAE,CAAC,MAAM,IAAA,iCAAuB,EAAC,OAAO,EAAE,WAAW,CAAC,CACzG,CACF,CAAC;SACH,CAAA;QACD,YAAY,GAAG,CAAC,GAAG,YAAY,EAAE,GAAG,YAAY,CAAC,CAAA;KAClD;IAED,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAA;KAC1B;IAED,aAAa;IACb,MAAM,CAAC,qBAAqB,EAAE,cAAc,CAAC,GAA8B,MAAM,wBAAwB,CACvG,OAAO,EACP,sBAAsB,EACtB,eAAe,EACf,GAAG,CACJ,CAAA;IACD,YAAY,GAAG,CAAC,GAAG,YAAY,EAAE,GAAG,qBAAqB,CAAC,CAAA;IAC1D,OAAO,CAAC,YAAY,EAAE,cAAc,CAAC,CAAA;AACvC,CAAC;AAED,kBAAe,cAAc,CAAA"}