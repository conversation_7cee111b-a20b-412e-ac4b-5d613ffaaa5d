{"version": 3, "sources": ["../../../../src/shared/lib/lazy-dynamic/loadable.tsx"], "names": ["Suspense", "lazy", "BailoutToCSR", "PreloadCss", "convertModule", "mod", "<PERSON><PERSON><PERSON><PERSON>", "default", "defaultOptions", "loader", "Promise", "resolve", "loading", "ssr", "Loadable", "options", "opts", "Lazy", "then", "Loading", "LoadableComponent", "props", "fallbackElement", "isLoading", "past<PERSON>elay", "error", "children", "window", "moduleIds", "modules", "reason", "fallback", "displayName"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,IAAI,QAAQ,QAAO;AACtC,SAASC,YAAY,QAAQ,2BAA0B;AAEvD,SAASC,UAAU,QAAQ,gBAAe;AAE1C,yFAAyF;AACzF,qGAAqG;AACrG,qEAAqE;AACrE,SAASC,cACPC,GAA4D;IAI5D,iHAAiH;IACjH,SAAS;IACT,8BAA8B;IAC9B,iBAAiB;IACjB,+CAA+C;IAC/C,wBAAwB;IACxB,MAAMC,aAAaD,OAAO,aAAaA;IACvC,OAAO;QACLE,SAASD,aACL,AAACD,IAA2BE,OAAO,GAClCF;IACP;AACF;AAEA,MAAMG,iBAAiB;IACrBC,QAAQ,IAAMC,QAAQC,OAAO,CAACP,cAAc,IAAM;IAClDQ,SAAS;IACTC,KAAK;AACP;AASA,SAASC,SAASC,OAAwB;IACxC,MAAMC,OAAO;QAAE,GAAGR,cAAc;QAAE,GAAGO,OAAO;IAAC;IAC7C,MAAME,qBAAOhB,KAAK,IAAMe,KAAKP,MAAM,GAAGS,IAAI,CAACd;IAC3C,MAAMe,UAAUH,KAAKJ,OAAO;IAE5B,SAASQ,kBAAkBC,KAAU;QACnC,MAAMC,kBAAkBH,wBACtB,KAACA;YAAQI,WAAW;YAAMC,WAAW;YAAMC,OAAO;aAChD;QAEJ,MAAMC,WAAWV,KAAKH,GAAG,iBACvB;;gBAEG,OAAOc,WAAW,4BACjB,KAACxB;oBAAWyB,WAAWZ,KAAKa,OAAO;qBACjC;8BACJ,KAACZ;oBAAM,GAAGI,KAAK;;;2BAGjB,KAACnB;YAAa4B,QAAO;sBACnB,cAAA,KAACb;gBAAM,GAAGI,KAAK;;;QAInB,qBAAO,KAACrB;YAAS+B,UAAUT;sBAAkBI;;IAC/C;IAEAN,kBAAkBY,WAAW,GAAG;IAEhC,OAAOZ;AACT;AAEA,eAAeN,SAAQ"}