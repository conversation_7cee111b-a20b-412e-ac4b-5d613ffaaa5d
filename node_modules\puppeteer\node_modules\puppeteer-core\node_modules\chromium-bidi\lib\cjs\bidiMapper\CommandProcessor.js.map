{"version": 3, "file": "CommandProcessor.js", "sourceRoot": "", "sources": ["../../../src/bidiMapper/CommandProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAIH,yDAOiC;AACjC,8DAAsD;AACtD,4CAAuD;AAGvD,2DAAmD;AAEnD,+EAAuE;AACvE,mEAA2D;AAC3D,+FAAuF;AAEvF,yEAAiE;AACjE,+EAAuE;AACvE,2EAAmE;AACnE,2FAAmF;AACnF,sFAA8E;AAE9E,4EAAoE;AAEpE,+EAAuE;AACvE,+EAAuE;AACvE,6DAAqD;AAarD,MAAa,gBAAiB,SAAQ,8BAAuC;IAC3E,oBAAoB;IACpB,iBAAiB,CAAmB;IACpC,yBAAyB,CAA2B;IACpD,aAAa,CAAe;IAC5B,eAAe,CAAiB;IAChC,iBAAiB,CAAmB;IACpC,qBAAqB,CAAuB;IAC5C,gBAAgB,CAAkB;IAClC,iBAAiB,CAAmB;IACpC,iBAAiB,CAAmB;IACpC,kBAAkB;IAElB,OAAO,CAA6B;IACpC,OAAO,CAAY;IAEnB,YACE,aAA4B,EAC5B,gBAA2B,EAC3B,YAA0B,EAC1B,YAAoB,EACpB,oBAAyC,EACzC,sBAA8C,EAC9C,YAA0B,EAC1B,mBAA4B,EAC5B,iBAA0B,EAC1B,SAAqC,IAAI,kCAAc,EAAE,EACzD,MAAiB;QAEjB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,MAAM,cAAc,GAAG,IAAI,kCAAc,EAAE,CAAC;QAC5C,MAAM,oBAAoB,GAAG,IAAI,8CAAoB,EAAE,CAAC;QAExD,8BAA8B;QAC9B,IAAI,CAAC,iBAAiB,GAAG,IAAI,sCAAgB,CAAC,gBAAgB,CAAC,CAAC;QAChE,IAAI,CAAC,yBAAyB,GAAG,IAAI,sDAAwB,CAC3D,aAAa,EACb,gBAAgB,EAChB,YAAY,EACZ,YAAY,EACZ,sBAAsB,EACtB,YAAY,EACZ,cAAc,EACd,oBAAoB,EACpB,mBAAmB,EACnB,iBAAiB,EACjB,oBAAoB,EACpB,MAAM,CACP,CAAC;QACF,IAAI,CAAC,aAAa,GAAG,IAAI,8BAAY,CACnC,sBAAsB,EACtB,aAAa,EACb,gBAAgB,CACjB,CAAC;QACF,IAAI,CAAC,eAAe,GAAG,IAAI,kCAAc,CACvC,sBAAsB,EACtB,YAAY,CACb,CAAC;QACF,IAAI,CAAC,iBAAiB,GAAG,IAAI,sCAAgB,CAC3C,sBAAsB,EACtB,cAAc,CACf,CAAC;QACF,IAAI,CAAC,qBAAqB,GAAG,IAAI,8CAAoB,CAAC,gBAAgB,CAAC,CAAC;QACxE,IAAI,CAAC,gBAAgB,GAAG,IAAI,oCAAe,CACzC,sBAAsB,EACtB,YAAY,EACZ,oBAAoB,EACpB,MAAM,CACP,CAAC;QACF,IAAI,CAAC,iBAAiB,GAAG,IAAI,sCAAgB,CAAC,YAAY,CAAC,CAAC;QAC5D,IAAI,CAAC,iBAAiB,GAAG,IAAI,sCAAgB,CAC3C,gBAAgB,EAChB,sBAAsB,EACtB,MAAM,CACP,CAAC;QACF,kBAAkB;IACpB,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,OAA6B;QAE7B,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;YACvB,KAAK,aAAa,CAAC;YACnB,KAAK,aAAa;gBAChB,mBAAmB;gBACnB,MAAM;YAER,iBAAiB;YACjB,8BAA8B;YAC9B,KAAK,eAAe;gBAClB,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;YACxC,KAAK,2BAA2B;gBAC9B,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;YAC1D,KAAK,yBAAyB;gBAC5B,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,CAAC;YACxD,KAAK,2BAA2B;gBAC9B,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CACnD,OAAO,CAAC,MAAM,CAAC,WAAW,CAC3B,CAAC;YACJ,kBAAkB;YAElB,0BAA0B;YAC1B,8BAA8B;YAC9B,KAAK,0BAA0B;gBAC7B,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAClD,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,MAAM,CAAC,CACjD,CAAC;YACJ,KAAK,mCAAmC;gBACtC,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAC3D,IAAI,CAAC,OAAO,CAAC,4BAA4B,CAAC,OAAO,CAAC,MAAM,CAAC,CAC1D,CAAC;YACJ,KAAK,uBAAuB;gBAC1B,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAC/C,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,CAC9C,CAAC;YACJ,KAAK,wBAAwB;gBAC3B,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAChD,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAC/C,CAAC;YACJ,KAAK,yBAAyB;gBAC5B,OAAO,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAC3C,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAChD,CAAC;YACJ,KAAK,kCAAkC;gBACrC,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,CAC1D,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,CAAC,MAAM,CAAC,CACzD,CAAC;YACJ,KAAK,6BAA6B;gBAChC,MAAM,IAAI,2CAA6B,CACrC,YAAY,OAAO,CAAC,MAAM,wBAAwB,CACnD,CAAC;YACJ,KAAK,0BAA0B;gBAC7B,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAClD,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,MAAM,CAAC,CACjD,CAAC;YACJ,KAAK,uBAAuB;gBAC1B,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAC/C,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,CAC9C,CAAC;YACJ,KAAK,wBAAwB;gBAC3B,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAChD,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAC/C,CAAC;YACJ,KAAK,6BAA6B;gBAChC,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,WAAW,CACrD,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,OAAO,CAAC,MAAM,CAAC,CACpD,CAAC;YACJ,KAAK,iCAAiC;gBACpC,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,eAAe,CACzD,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,OAAO,CAAC,MAAM,CAAC,CACxD,CAAC;YACJ,kBAAkB;YAElB,aAAa;YACb,8BAA8B;YAC9B,KAAK,gBAAgB;gBACnB,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAClC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,CACnD,CAAC;YACJ,KAAK,iBAAiB;gBACpB,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CACzC,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,OAAO,CAAC,MAAM,CAAC,CACpD,CAAC;YACJ,kBAAkB;YAElB,eAAe;YACf,8BAA8B;YAC9B,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAC9C,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,MAAM,CAAC,CACvD,CAAC;YACJ,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAC9C,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,MAAM,CAAC,CACvD,CAAC;YACJ,KAAK,gBAAgB;gBACnB,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CACxC,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,MAAM,CAAC,CACjD,CAAC;YACJ,kBAAkB;YAElB,iBAAiB;YACjB,8BAA8B;YAC9B,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAC9C,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,MAAM,CAAC,CACrD,CAAC;YACJ,KAAK,yBAAyB;gBAC5B,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CACjD,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,OAAO,CAAC,MAAM,CAAC,CACxD,CAAC;YACJ,KAAK,0BAA0B;gBAC7B,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAClD,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,CAAC,MAAM,CAAC,CACzD,CAAC;YACJ,KAAK,0BAA0B;gBAC7B,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAClD,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,CAAC,MAAM,CAAC,CACzD,CAAC;YACJ,KAAK,qBAAqB;gBACxB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAC7C,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,OAAO,CAAC,MAAM,CAAC,CACpD,CAAC;YACJ,KAAK,yBAAyB;gBAC5B,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CACjD,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,OAAO,CAAC,MAAM,CAAC,CACxD,CAAC;YACJ,KAAK,yBAAyB;gBAC5B,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CACjD,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,OAAO,CAAC,MAAM,CAAC,CACxD,CAAC;YACJ,kBAAkB;YAElB,qBAAqB;YACrB,8BAA8B;YAC9B,KAAK,2BAA2B;gBAC9B,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,cAAc,CACpD,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,MAAM,CAAC,CACvD,CAAC;YACJ,kBAAkB;YAElB,gBAAgB;YAChB,8BAA8B;YAC9B,KAAK,yBAAyB;gBAC5B,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACjD,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,CAAC,MAAM,CAAC,CACzD,CAAC;YACJ,KAAK,qBAAqB;gBACxB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAC7C,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,MAAM,CAAC,CACrD,CAAC;YACJ,KAAK,eAAe;gBAClB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CACvC,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAC/C,CAAC;YACJ,KAAK,iBAAiB;gBACpB,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CACzC,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,MAAM,CAAC,CACjD,CAAC;YACJ,KAAK,kBAAkB;gBACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CACpC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,CAClD,CAAC;YACJ,KAAK,4BAA4B;gBAC/B,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CACpD,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC,OAAO,CAAC,MAAM,CAAC,CAC5D,CAAC;YACJ,kBAAkB;YAElB,iBAAiB;YACjB,8BAA8B;YAC9B,KAAK,gBAAgB;gBACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;YACzC,KAAK,mBAAmB;gBACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS,CACrC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,EACjD,OAAO,CAAC,OAAO,CAChB,CAAC;YACJ,KAAK,qBAAqB;gBACxB,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACvC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,EACjD,OAAO,CAAC,OAAO,CAChB,CAAC;YACJ,kBAAkB;YAElB,iBAAiB;YACjB,8BAA8B;YAC9B,KAAK,uBAAuB;gBAC1B,MAAM,IAAI,2CAA6B,CACrC,GAAG,OAAO,CAAC,MAAM,uBAAuB,CACzC,CAAC;YACJ,KAAK,oBAAoB;gBACvB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAC5C,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,CACnD,CAAC;YACJ,KAAK,mBAAmB;gBACtB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAC3C,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,MAAM,CAAC,CAClD,CAAC;YACJ,kBAAkB;QACpB,CAAC;QAED,iEAAiE;QACjE,0EAA0E;QAC1E,oBAAoB;QACpB,MAAM,IAAI,qCAAuB,CAAC,oBAAoB,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;IAC5E,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAA6B;QAChD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAEnD,MAAM,QAAQ,GAAG;gBACf,IAAI,EAAE,SAAS;gBACf,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,MAAM;aACgC,CAAC;YAEzC,IAAI,CAAC,IAAI,mDAAkC;gBACzC,OAAO,EAAE,oCAAe,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC;gBAClE,KAAK,EAAE,OAAO,CAAC,MAAM;aACtB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,YAAY,uBAAS,EAAE,CAAC;gBAC3B,IAAI,CAAC,IAAI,mDAAkC;oBACzC,OAAO,EAAE,oCAAe,CAAC,cAAc,CACrC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,EAC7B,OAAO,CAAC,OAAO,CAChB;oBACD,KAAK,EAAE,OAAO,CAAC,MAAM;iBACtB,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,MAAM,KAAK,GAAG,CAAU,CAAC;gBACzB,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACpC,IAAI,CAAC,IAAI,mDAAkC;oBACzC,OAAO,EAAE,oCAAe,CAAC,cAAc,CACrC,IAAI,mCAAqB,CACvB,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,KAAK,CACZ,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,EAC7B,OAAO,CAAC,OAAO,CAChB;oBACD,KAAK,EAAE,OAAO,CAAC,MAAM;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;CACF;AA1UD,4CA0UC"}