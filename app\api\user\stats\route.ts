'use server'

import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { ScoringService } from '@/lib/services/scoring'

export async function GET(request: NextRequest) {
  try {
    // Authenticate request
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const scoringService = new ScoringService()
    const stats = await scoringService.getUserStats(authResult.user.id)

    if (!stats) {
      return NextResponse.json(
        { success: false, message: 'Failed to get user stats' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        stats: stats
      }
    })

  } catch (error) {
    console.error('Error getting user stats:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get user stats'
      },
      { status: 500 }
    )
  }
}
