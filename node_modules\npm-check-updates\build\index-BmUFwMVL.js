"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const A=require("node:process"),P=require("node:os"),B=require("node:tty"),O=10,I=(r=0)=>e=>`\x1B[${e+r}m`,N=(r=0)=>e=>`\x1B[${38+r};5;${e}m`,S=(r=0)=>(e,t,o)=>`\x1B[${38+r};2;${e};${t};${o}m`,l={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},j=Object.keys(l.modifier),v=Object.keys(l.color),E=Object.keys(l.bgColor),k=[...v,...E];function _(){const r=new Map;for(const[e,t]of Object.entries(l)){for(const[o,n]of Object.entries(t))l[o]={open:`\x1B[${n[0]}m`,close:`\x1B[${n[1]}m`},t[o]=l[o],r.set(n[0],n[1]);Object.defineProperty(l,e,{value:t,enumerable:!1})}return Object.defineProperty(l,"codes",{value:r,enumerable:!1}),l.color.close="\x1B[39m",l.bgColor.close="\x1B[49m",l.color.ansi=I(),l.color.ansi256=N(),l.color.ansi16m=S(),l.bgColor.ansi=I(O),l.bgColor.ansi256=N(O),l.bgColor.ansi16m=S(O),Object.defineProperties(l,{rgbToAnsi256:{value(e,t,o){return e===t&&t===o?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(o/255*5)},enumerable:!1},hexToRgb:{value(e){const t=/[a-f\d]{6}|[a-f\d]{3}/i.exec(e.toString(16));if(!t)return[0,0,0];let[o]=t;o.length===3&&(o=[...o].map(i=>i+i).join(""));const n=Number.parseInt(o,16);return[n>>16&255,n>>8&255,n&255]},enumerable:!1},hexToAnsi256:{value:e=>l.rgbToAnsi256(...l.hexToRgb(e)),enumerable:!1},ansi256ToAnsi:{value(e){if(e<8)return 30+e;if(e<16)return 90+(e-8);let t,o,n;if(e>=232)t=((e-232)*10+8)/255,o=t,n=t;else{e-=16;const M=e%36;t=Math.floor(e/36)/5,o=Math.floor(M/6)/5,n=M%6/5}const i=Math.max(t,o,n)*2;if(i===0)return 30;let c=30+(Math.round(n)<<2|Math.round(o)<<1|Math.round(t));return i===2&&(c+=60),c},enumerable:!1},rgbToAnsi:{value:(e,t,o)=>l.ansi256ToAnsi(l.rgbToAnsi256(e,t,o)),enumerable:!1},hexToAnsi:{value:e=>l.ansi256ToAnsi(l.hexToAnsi256(e)),enumerable:!1}}),l}const L=_(),a=L;function u(r,e=globalThis.Deno?globalThis.Deno.args:A.argv){const t=r.startsWith("-")?"":r.length===1?"-":"--",o=e.indexOf(t+r),n=e.indexOf("--");return o!==-1&&(n===-1||o<n)}const{env:s}=A;let g;u("no-color")||u("no-colors")||u("color=false")||u("color=never")?g=0:(u("color")||u("colors")||u("color=true")||u("color=always"))&&(g=1);function G(){if("FORCE_COLOR"in s)return s.FORCE_COLOR==="true"?1:s.FORCE_COLOR==="false"?0:s.FORCE_COLOR.length===0?1:Math.min(Number.parseInt(s.FORCE_COLOR,10),3)}function Y(r){return r===0?!1:{level:r,hasBasic:!0,has256:r>=2,has16m:r>=3}}function $(r,{streamIsTTY:e,sniffFlags:t=!0}={}){const o=G();o!==void 0&&(g=o);const n=t?g:o;if(n===0)return 0;if(t){if(u("color=16m")||u("color=full")||u("color=truecolor"))return 3;if(u("color=256"))return 2}if("TF_BUILD"in s&&"AGENT_NAME"in s)return 1;if(r&&!e&&n===void 0)return 0;const i=n||0;if(s.TERM==="dumb")return i;if(A.platform==="win32"){const c=P.release().split(".");return Number(c[0])>=10&&Number(c[2])>=10586?Number(c[2])>=14931?3:2:1}if("CI"in s)return"GITHUB_ACTIONS"in s||"GITEA_ACTIONS"in s?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some(c=>c in s)||s.CI_NAME==="codeship"?1:i;if("TEAMCITY_VERSION"in s)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(s.TEAMCITY_VERSION)?1:0;if(s.COLORTERM==="truecolor"||s.TERM==="xterm-kitty")return 3;if("TERM_PROGRAM"in s){const c=Number.parseInt((s.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(s.TERM_PROGRAM){case"iTerm.app":return c>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(s.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(s.TERM)||"COLORTERM"in s?1:i}function x(r,e={}){const t=$(r,{streamIsTTY:r&&r.isTTY,...e});return Y(t)}const V={stdout:x({isTTY:B.isatty(1)}),stderr:x({isTTY:B.isatty(2)})},D=V;function U(r,e,t){let o=r.indexOf(e);if(o===-1)return r;const n=e.length;let i=0,c="";do c+=r.slice(i,o)+e+t,i=o+n,o=r.indexOf(e,i);while(o!==-1);return c+=r.slice(i),c}function W(r,e,t,o){let n=0,i="";do{const c=r[o-1]==="\r";i+=r.slice(n,c?o-1:o)+e+(c?`\r
`:`
`)+t,n=o+1,o=r.indexOf(`
`,n)}while(o!==-1);return i+=r.slice(n),i}const{stdout:T,stderr:p}=D,C=Symbol("GENERATOR"),f=Symbol("STYLER"),h=Symbol("IS_EMPTY"),F=["ansi","ansi","ansi256","ansi16m"],b=Object.create(null),q=(r,e={})=>{if(e.level&&!(Number.isInteger(e.level)&&e.level>=0&&e.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");const t=T?T.level:0;r.level=e.level===void 0?t:e.level};class K{constructor(e){return w(e)}}const w=r=>{const e=(...t)=>t.join(" ");return q(e,r),Object.setPrototypeOf(e,d.prototype),e};function d(r){return w(r)}Object.setPrototypeOf(d.prototype,Function.prototype);for(const[r,e]of Object.entries(a))b[r]={get(){const t=m(this,y(e.open,e.close,this[f]),this[h]);return Object.defineProperty(this,r,{value:t}),t}};b.visible={get(){const r=m(this,this[f],!0);return Object.defineProperty(this,"visible",{value:r}),r}};const R=(r,e,t,...o)=>r==="rgb"?e==="ansi16m"?a[t].ansi16m(...o):e==="ansi256"?a[t].ansi256(a.rgbToAnsi256(...o)):a[t].ansi(a.rgbToAnsi(...o)):r==="hex"?R("rgb",e,t,...a.hexToRgb(...o)):a[t][r](...o),H=["rgb","hex","ansi256"];for(const r of H){b[r]={get(){const{level:t}=this;return function(...o){const n=y(R(r,F[t],"color",...o),a.color.close,this[f]);return m(this,n,this[h])}}};const e="bg"+r[0].toUpperCase()+r.slice(1);b[e]={get(){const{level:t}=this;return function(...o){const n=y(R(r,F[t],"bgColor",...o),a.bgColor.close,this[f]);return m(this,n,this[h])}}}}const z=Object.defineProperties(()=>{},{...b,level:{enumerable:!0,get(){return this[C].level},set(r){this[C].level=r}}}),y=(r,e,t)=>{let o,n;return t===void 0?(o=r,n=e):(o=t.openAll+r,n=e+t.closeAll),{open:r,close:e,openAll:o,closeAll:n,parent:t}},m=(r,e,t)=>{const o=(...n)=>J(o,n.length===1?""+n[0]:n.join(" "));return Object.setPrototypeOf(o,z),o[C]=r,o[f]=e,o[h]=t,o},J=(r,e)=>{if(r.level<=0||!e)return r[h]?"":e;let t=r[f];if(t===void 0)return e;const{openAll:o,closeAll:n}=t;if(e.includes("\x1B"))for(;t!==void 0;)e=U(e,t.close,t.open),t=t.parent;const i=e.indexOf(`
`);return i!==-1&&(e=W(e,n,o,i)),o+e+n};Object.defineProperties(d.prototype,b);const Q=d(),X=d({level:p?p.level:0});exports.Chalk=K;exports.backgroundColorNames=E;exports.backgroundColors=E;exports.chalkStderr=X;exports.colorNames=k;exports.colors=k;exports.default=Q;exports.foregroundColorNames=v;exports.foregroundColors=v;exports.modifierNames=j;exports.modifiers=j;exports.supportsColor=T;exports.supportsColorStderr=p;
//# sourceMappingURL=index-BmUFwMVL.js.map
