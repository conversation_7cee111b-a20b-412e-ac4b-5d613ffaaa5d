#!/usr/bin/env ts-node

import { testDatabaseConnection, closeDatabaseConnections, db } from '../lib/database'

async function testDatabase() {
  console.log('🔍 Testing KodeXGuard Database Connection...\n')

  try {
    // Test basic connection
    const connected = await testDatabaseConnection()
    if (!connected) {
      console.error('❌ Database connection failed')
      process.exit(1)
    }

    // Test existing tables
    console.log('📋 Checking existing tables...')
    const tables = await db.query('SHOW TABLES') as any[]
    const tableNames = tables.map(row => Object.values(row)[0])

    console.log(`✅ Found ${tableNames.length} tables:`)
    tableNames.forEach(table => console.log(`   - ${table}`))

    // Test users table structure
    console.log('\n👤 Checking users table structure...')
    const userColumns = await db.query('DESCRIBE users') as any[]
    console.log('✅ Users table columns:')
    userColumns.forEach(col => {
      console.log(`   - ${col.Field} (${col.Type}) ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'}`)
    })

    // Test sample data
    console.log('\n📊 Checking sample data...')

    // Count users
    const userCount = await db.query('SELECT COUNT(*) as count FROM users') as any[]
    console.log(`✅ Users: ${userCount[0].count}`)

    // Check if plans table exists and has data
    if (tableNames.includes('plans')) {
      const planCount = await db.query('SELECT COUNT(*) as count FROM plans') as any[]
      console.log(`✅ Plans: ${planCount[0].count}`)

      const plans = await db.query('SELECT id, name, price FROM plans ORDER BY price') as any[]
      console.log('📋 Available plans:')
      plans.forEach(plan => {
        console.log(`   - ${plan.name} (${plan.id}): ${plan.price === 0 ? 'Free' : 'Rp ' + plan.price.toLocaleString()}`)
      })
    }

    // Check if migrations table exists
    if (tableNames.includes('migrations')) {
      const migrationCount = await db.query('SELECT COUNT(*) as count FROM migrations') as any[]
      console.log(`✅ Applied migrations: ${migrationCount[0].count}`)

      const migrations = await db.query('SELECT name, applied_at FROM migrations ORDER BY applied_at DESC LIMIT 5') as any[]
      if (migrations.length > 0) {
        console.log('📋 Recent migrations:')
        migrations.forEach(migration => {
          console.log(`   - ${migration.name} (${new Date(migration.applied_at).toLocaleString()})`)
        })
      }
    }

    // Test new features tables
    console.log('\n🔍 Checking new feature tables...')
    const newTables = [
      'plans', 'subscriptions', 'payments', 'payment_logs',
      'bot_instances', 'bot_messages', 'osint_queries',
      'score_history', 'user_achievements', 'user_streaks',
      'admin_logs'
    ]

    for (const tableName of newTables) {
      if (tableNames.includes(tableName)) {
        const count = await db.query(`SELECT COUNT(*) as count FROM ${tableName}`) as any[]
        console.log(`✅ ${tableName}: ${count[0].count} records`)
      } else {
        console.log(`❌ ${tableName}: Table not found`)
      }
    }

    console.log('\n✅ Database test completed successfully!')
    
  } catch (error) {
    console.error('❌ Database test failed:', error)
    process.exit(1)
  } finally {
    await closeDatabaseConnections()
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testDatabase()
}
