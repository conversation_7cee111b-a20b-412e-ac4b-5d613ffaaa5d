#!/usr/bin/env node
"use strict";const _e=require("node:events"),be=require("node:child_process"),Oe=require("node:path"),Ae=require("node:fs"),$e=require("node:process"),c=require("./index.js");var w={},j={},V={};let ie=class extends Error{constructor(e,t,n){super(n),Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name,this.code=t,this.exitCode=e,this.nestedError=void 0}},Ce=class extends ie{constructor(e){super(1,"commander.invalidArgument",e),Error.captureStackTrace(this,this.constructor),this.name=this.constructor.name}};V.CommanderError=ie;V.InvalidArgumentError=Ce;const{InvalidArgumentError:ye}=V;let ve=class{constructor(e,t){switch(this.description=t||"",this.variadic=!1,this.parseArg=void 0,this.defaultValue=void 0,this.defaultValueDescription=void 0,this.argChoices=void 0,e[0]){case"<":this.required=!0,this._name=e.slice(1,-1);break;case"[":this.required=!1,this._name=e.slice(1,-1);break;default:this.required=!0,this._name=e;break}this._name.length>3&&this._name.slice(-3)==="..."&&(this.variadic=!0,this._name=this._name.slice(0,-3))}name(){return this._name}_concatValue(e,t){return t===this.defaultValue||!Array.isArray(t)?[e]:t.concat(e)}default(e,t){return this.defaultValue=e,this.defaultValueDescription=t,this}argParser(e){return this.parseArg=e,this}choices(e){return this.argChoices=e.slice(),this.parseArg=(t,n)=>{if(!this.argChoices.includes(t))throw new ye(`Allowed choices are ${this.argChoices.join(", ")}.`);return this.variadic?this._concatValue(t,n):t},this}argRequired(){return this.required=!0,this}argOptional(){return this.required=!1,this}};function we(s){const e=s.name()+(s.variadic===!0?"...":"");return s.required?"<"+e+">":"["+e+"]"}j.Argument=ve;j.humanReadableArgName=we;var re={},W={};const{humanReadableArgName:Ee}=j;let xe=class{constructor(){this.helpWidth=void 0,this.sortSubcommands=!1,this.sortOptions=!1,this.showGlobalOptions=!1}visibleCommands(e){const t=e.commands.filter(i=>!i._hidden),n=e._getHelpCommand();return n&&!n._hidden&&t.push(n),this.sortSubcommands&&t.sort((i,r)=>i.name().localeCompare(r.name())),t}compareOptions(e,t){const n=i=>i.short?i.short.replace(/^-/,""):i.long.replace(/^--/,"");return n(e).localeCompare(n(t))}visibleOptions(e){const t=e.options.filter(i=>!i.hidden),n=e._getHelpOption();if(n&&!n.hidden){const i=n.short&&e._findOption(n.short),r=n.long&&e._findOption(n.long);!i&&!r?t.push(n):n.long&&!r?t.push(e.createOption(n.long,n.description)):n.short&&!i&&t.push(e.createOption(n.short,n.description))}return this.sortOptions&&t.sort(this.compareOptions),t}visibleGlobalOptions(e){if(!this.showGlobalOptions)return[];const t=[];for(let n=e.parent;n;n=n.parent){const i=n.options.filter(r=>!r.hidden);t.push(...i)}return this.sortOptions&&t.sort(this.compareOptions),t}visibleArguments(e){return e._argsDescription&&e.registeredArguments.forEach(t=>{t.description=t.description||e._argsDescription[t.name()]||""}),e.registeredArguments.find(t=>t.description)?e.registeredArguments:[]}subcommandTerm(e){const t=e.registeredArguments.map(n=>Ee(n)).join(" ");return e._name+(e._aliases[0]?"|"+e._aliases[0]:"")+(e.options.length?" [options]":"")+(t?" "+t:"")}optionTerm(e){return e.flags}argumentTerm(e){return e.name()}longestSubcommandTermLength(e,t){return t.visibleCommands(e).reduce((n,i)=>Math.max(n,t.subcommandTerm(i).length),0)}longestOptionTermLength(e,t){return t.visibleOptions(e).reduce((n,i)=>Math.max(n,t.optionTerm(i).length),0)}longestGlobalOptionTermLength(e,t){return t.visibleGlobalOptions(e).reduce((n,i)=>Math.max(n,t.optionTerm(i).length),0)}longestArgumentTermLength(e,t){return t.visibleArguments(e).reduce((n,i)=>Math.max(n,t.argumentTerm(i).length),0)}commandUsage(e){let t=e._name;e._aliases[0]&&(t=t+"|"+e._aliases[0]);let n="";for(let i=e.parent;i;i=i.parent)n=i.name()+" "+n;return n+t+" "+e.usage()}commandDescription(e){return e.description()}subcommandDescription(e){return e.summary()||e.description()}optionDescription(e){const t=[];return e.argChoices&&t.push(`choices: ${e.argChoices.map(n=>JSON.stringify(n)).join(", ")}`),e.defaultValue!==void 0&&(e.required||e.optional||e.isBoolean()&&typeof e.defaultValue=="boolean")&&t.push(`default: ${e.defaultValueDescription||JSON.stringify(e.defaultValue)}`),e.presetArg!==void 0&&e.optional&&t.push(`preset: ${JSON.stringify(e.presetArg)}`),e.envVar!==void 0&&t.push(`env: ${e.envVar}`),t.length>0?`${e.description} (${t.join(", ")})`:e.description}argumentDescription(e){const t=[];if(e.argChoices&&t.push(`choices: ${e.argChoices.map(n=>JSON.stringify(n)).join(", ")}`),e.defaultValue!==void 0&&t.push(`default: ${e.defaultValueDescription||JSON.stringify(e.defaultValue)}`),t.length>0){const n=`(${t.join(", ")})`;return e.description?`${e.description} ${n}`:n}return e.description}formatHelp(e,t){const n=t.padWidth(e,t),i=t.helpWidth||80,r=2,o=2;function l(d,O){if(O){const b=`${d.padEnd(n+o)}${O}`;return t.wrap(b,i-r,n+o)}return d}function a(d){return d.join(`
`).replace(/^/gm," ".repeat(r))}let u=[`Usage: ${t.commandUsage(e)}`,""];const h=t.commandDescription(e);h.length>0&&(u=u.concat([t.wrap(h,i,0),""]));const f=t.visibleArguments(e).map(d=>l(t.argumentTerm(d),t.argumentDescription(d)));f.length>0&&(u=u.concat(["Arguments:",a(f),""]));const _=t.visibleOptions(e).map(d=>l(t.optionTerm(d),t.optionDescription(d)));if(_.length>0&&(u=u.concat(["Options:",a(_),""])),this.showGlobalOptions){const d=t.visibleGlobalOptions(e).map(O=>l(t.optionTerm(O),t.optionDescription(O)));d.length>0&&(u=u.concat(["Global Options:",a(d),""]))}const $=t.visibleCommands(e).map(d=>l(t.subcommandTerm(d),t.subcommandDescription(d)));return $.length>0&&(u=u.concat(["Commands:",a($),""])),u.join(`
`)}padWidth(e,t){return Math.max(t.longestOptionTermLength(e,t),t.longestGlobalOptionTermLength(e,t),t.longestSubcommandTermLength(e,t),t.longestArgumentTermLength(e,t))}wrap(e,t,n,i=40){const r=" \\f\\t\\v   -   　\uFEFF",o=new RegExp(`[\\n][${r}]+`);if(e.match(o))return e;const l=t-n;if(l<i)return e;const a=e.slice(0,n),u=e.slice(n).replace(`\r
`,`
`),h=" ".repeat(n),_="\\s​",$=new RegExp(`
|.{1,${l-1}}([${_}]|$)|[^${_}]+?([${_}]|$)`,"g"),d=u.match($)||[];return a+d.map((O,b)=>O===`
`?"":(b>0?h:"")+O.trimEnd()).join(`
`)}};W.Help=xe;var P={};const{InvalidArgumentError:Se}=V;let Te=class{constructor(e,t){this.flags=e,this.description=t||"",this.required=e.includes("<"),this.optional=e.includes("["),this.variadic=/\w\.\.\.[>\]]$/.test(e),this.mandatory=!1;const n=je(e);this.short=n.shortFlag,this.long=n.longFlag,this.negate=!1,this.long&&(this.negate=this.long.startsWith("--no-")),this.defaultValue=void 0,this.defaultValueDescription=void 0,this.presetArg=void 0,this.envVar=void 0,this.parseArg=void 0,this.hidden=!1,this.argChoices=void 0,this.conflictsWith=[],this.implied=void 0}default(e,t){return this.defaultValue=e,this.defaultValueDescription=t,this}preset(e){return this.presetArg=e,this}conflicts(e){return this.conflictsWith=this.conflictsWith.concat(e),this}implies(e){let t=e;return typeof e=="string"&&(t={[e]:!0}),this.implied=Object.assign(this.implied||{},t),this}env(e){return this.envVar=e,this}argParser(e){return this.parseArg=e,this}makeOptionMandatory(e=!0){return this.mandatory=!!e,this}hideHelp(e=!0){return this.hidden=!!e,this}_concatValue(e,t){return t===this.defaultValue||!Array.isArray(t)?[e]:t.concat(e)}choices(e){return this.argChoices=e.slice(),this.parseArg=(t,n)=>{if(!this.argChoices.includes(t))throw new Se(`Allowed choices are ${this.argChoices.join(", ")}.`);return this.variadic?this._concatValue(t,n):t},this}name(){return this.long?this.long.replace(/^--/,""):this.short.replace(/^-/,"")}attributeName(){return Ve(this.name().replace(/^no-/,""))}is(e){return this.short===e||this.long===e}isBoolean(){return!this.required&&!this.optional&&!this.negate}},He=class{constructor(e){this.positiveOptions=new Map,this.negativeOptions=new Map,this.dualOptions=new Set,e.forEach(t=>{t.negate?this.negativeOptions.set(t.attributeName(),t):this.positiveOptions.set(t.attributeName(),t)}),this.negativeOptions.forEach((t,n)=>{this.positiveOptions.has(n)&&this.dualOptions.add(n)})}valueFromOption(e,t){const n=t.attributeName();if(!this.dualOptions.has(n))return!0;const i=this.negativeOptions.get(n).presetArg,r=i!==void 0?i:!1;return t.negate===(r===e)}};function Ve(s){return s.split("-").reduce((e,t)=>e+t[0].toUpperCase()+t.slice(1))}function je(s){let e,t;const n=s.split(/[ |,]+/);return n.length>1&&!/^[[<]/.test(n[1])&&(e=n.shift()),t=n.shift(),!e&&/^-[^-]$/.test(t)&&(e=t,t=void 0),{shortFlag:e,longFlag:t}}P.Option=Te;P.DualOptions=He;var se={};const oe=3;function ke(s,e){if(Math.abs(s.length-e.length)>oe)return Math.max(s.length,e.length);const t=[];for(let n=0;n<=s.length;n++)t[n]=[n];for(let n=0;n<=e.length;n++)t[0][n]=n;for(let n=1;n<=e.length;n++)for(let i=1;i<=s.length;i++){let r=1;s[i-1]===e[n-1]?r=0:r=1,t[i][n]=Math.min(t[i-1][n]+1,t[i][n-1]+1,t[i-1][n-1]+r),i>1&&n>1&&s[i-1]===e[n-2]&&s[i-2]===e[n-1]&&(t[i][n]=Math.min(t[i][n],t[i-2][n-2]+1))}return t[s.length][e.length]}function Ie(s,e){if(!e||e.length===0)return"";e=Array.from(new Set(e));const t=s.startsWith("--");t&&(s=s.slice(2),e=e.map(o=>o.slice(2)));let n=[],i=oe;const r=.4;return e.forEach(o=>{if(o.length<=1)return;const l=ke(s,o),a=Math.max(s.length,o.length);(a-l)/a>r&&(l<i?(i=l,n=[o]):l===i&&n.push(o))}),n.sort((o,l)=>o.localeCompare(l)),t&&(n=n.map(o=>`--${o}`)),n.length>1?`
(Did you mean one of ${n.join(", ")}?)`:n.length===1?`
(Did you mean ${n[0]}?)`:""}se.suggestSimilar=Ie;const Pe=_e.EventEmitter,N=be,x=Oe,M=Ae,g=$e,{Argument:Fe,humanReadableArgName:De}=j,{CommanderError:U}=V,{Help:Ne}=W,{Option:L,DualOptions:Me}=P,{suggestSimilar:R}=se;let Ue=class ae extends Pe{constructor(e){super(),this.commands=[],this.options=[],this.parent=null,this._allowUnknownOption=!1,this._allowExcessArguments=!0,this.registeredArguments=[],this._args=this.registeredArguments,this.args=[],this.rawArgs=[],this.processedArgs=[],this._scriptPath=null,this._name=e||"",this._optionValues={},this._optionValueSources={},this._storeOptionsAsProperties=!1,this._actionHandler=null,this._executableHandler=!1,this._executableFile=null,this._executableDir=null,this._defaultCommandName=null,this._exitCallback=null,this._aliases=[],this._combineFlagAndOptionalValue=!0,this._description="",this._summary="",this._argsDescription=void 0,this._enablePositionalOptions=!1,this._passThroughOptions=!1,this._lifeCycleHooks={},this._showHelpAfterError=!1,this._showSuggestionAfterError=!0,this._outputConfiguration={writeOut:t=>g.stdout.write(t),writeErr:t=>g.stderr.write(t),getOutHelpWidth:()=>g.stdout.isTTY?g.stdout.columns:void 0,getErrHelpWidth:()=>g.stderr.isTTY?g.stderr.columns:void 0,outputError:(t,n)=>n(t)},this._hidden=!1,this._helpOption=void 0,this._addImplicitHelpCommand=void 0,this._helpCommand=void 0,this._helpConfiguration={}}copyInheritedSettings(e){return this._outputConfiguration=e._outputConfiguration,this._helpOption=e._helpOption,this._helpCommand=e._helpCommand,this._helpConfiguration=e._helpConfiguration,this._exitCallback=e._exitCallback,this._storeOptionsAsProperties=e._storeOptionsAsProperties,this._combineFlagAndOptionalValue=e._combineFlagAndOptionalValue,this._allowExcessArguments=e._allowExcessArguments,this._enablePositionalOptions=e._enablePositionalOptions,this._showHelpAfterError=e._showHelpAfterError,this._showSuggestionAfterError=e._showSuggestionAfterError,this}_getCommandAndAncestors(){const e=[];for(let t=this;t;t=t.parent)e.push(t);return e}command(e,t,n){let i=t,r=n;typeof i=="object"&&i!==null&&(r=i,i=null),r=r||{};const[,o,l]=e.match(/([^ ]+) *(.*)/),a=this.createCommand(o);return i&&(a.description(i),a._executableHandler=!0),r.isDefault&&(this._defaultCommandName=a._name),a._hidden=!!(r.noHelp||r.hidden),a._executableFile=r.executableFile||null,l&&a.arguments(l),this._registerCommand(a),a.parent=this,a.copyInheritedSettings(this),i?this:a}createCommand(e){return new ae(e)}createHelp(){return Object.assign(new Ne,this.configureHelp())}configureHelp(e){return e===void 0?this._helpConfiguration:(this._helpConfiguration=e,this)}configureOutput(e){return e===void 0?this._outputConfiguration:(Object.assign(this._outputConfiguration,e),this)}showHelpAfterError(e=!0){return typeof e!="string"&&(e=!!e),this._showHelpAfterError=e,this}showSuggestionAfterError(e=!0){return this._showSuggestionAfterError=!!e,this}addCommand(e,t){if(!e._name)throw new Error(`Command passed to .addCommand() must have a name
- specify the name in Command constructor or using .name()`);return t=t||{},t.isDefault&&(this._defaultCommandName=e._name),(t.noHelp||t.hidden)&&(e._hidden=!0),this._registerCommand(e),e.parent=this,e._checkForBrokenPassThrough(),this}createArgument(e,t){return new Fe(e,t)}argument(e,t,n,i){const r=this.createArgument(e,t);return typeof n=="function"?r.default(i).argParser(n):r.default(n),this.addArgument(r),this}arguments(e){return e.trim().split(/ +/).forEach(t=>{this.argument(t)}),this}addArgument(e){const t=this.registeredArguments.slice(-1)[0];if(t&&t.variadic)throw new Error(`only the last argument can be variadic '${t.name()}'`);if(e.required&&e.defaultValue!==void 0&&e.parseArg===void 0)throw new Error(`a default value for a required argument is never used: '${e.name()}'`);return this.registeredArguments.push(e),this}helpCommand(e,t){if(typeof e=="boolean")return this._addImplicitHelpCommand=e,this;e=e??"help [command]";const[,n,i]=e.match(/([^ ]+) *(.*)/),r=t??"display help for command",o=this.createCommand(n);return o.helpOption(!1),i&&o.arguments(i),r&&o.description(r),this._addImplicitHelpCommand=!0,this._helpCommand=o,this}addHelpCommand(e,t){return typeof e!="object"?(this.helpCommand(e,t),this):(this._addImplicitHelpCommand=!0,this._helpCommand=e,this)}_getHelpCommand(){return this._addImplicitHelpCommand??(this.commands.length&&!this._actionHandler&&!this._findCommand("help"))?(this._helpCommand===void 0&&this.helpCommand(void 0,void 0),this._helpCommand):null}hook(e,t){const n=["preSubcommand","preAction","postAction"];if(!n.includes(e))throw new Error(`Unexpected value for event passed to hook : '${e}'.
Expecting one of '${n.join("', '")}'`);return this._lifeCycleHooks[e]?this._lifeCycleHooks[e].push(t):this._lifeCycleHooks[e]=[t],this}exitOverride(e){return e?this._exitCallback=e:this._exitCallback=t=>{if(t.code!=="commander.executeSubCommandAsync")throw t},this}_exit(e,t,n){this._exitCallback&&this._exitCallback(new U(e,t,n)),g.exit(e)}action(e){const t=n=>{const i=this.registeredArguments.length,r=n.slice(0,i);return this._storeOptionsAsProperties?r[i]=this:r[i]=this.opts(),r.push(this),e.apply(this,r)};return this._actionHandler=t,this}createOption(e,t){return new L(e,t)}_callParseArg(e,t,n,i){try{return e.parseArg(t,n)}catch(r){if(r.code==="commander.invalidArgument"){const o=`${i} ${r.message}`;this.error(o,{exitCode:r.exitCode,code:r.code})}throw r}}_registerOption(e){const t=e.short&&this._findOption(e.short)||e.long&&this._findOption(e.long);if(t){const n=e.long&&this._findOption(e.long)?e.long:e.short;throw new Error(`Cannot add option '${e.flags}'${this._name&&` to command '${this._name}'`} due to conflicting flag '${n}'
-  already used by option '${t.flags}'`)}this.options.push(e)}_registerCommand(e){const t=i=>[i.name()].concat(i.aliases()),n=t(e).find(i=>this._findCommand(i));if(n){const i=t(this._findCommand(n)).join("|"),r=t(e).join("|");throw new Error(`cannot add command '${r}' as already have command '${i}'`)}this.commands.push(e)}addOption(e){this._registerOption(e);const t=e.name(),n=e.attributeName();if(e.negate){const r=e.long.replace(/^--no-/,"--");this._findOption(r)||this.setOptionValueWithSource(n,e.defaultValue===void 0?!0:e.defaultValue,"default")}else e.defaultValue!==void 0&&this.setOptionValueWithSource(n,e.defaultValue,"default");const i=(r,o,l)=>{r==null&&e.presetArg!==void 0&&(r=e.presetArg);const a=this.getOptionValue(n);r!==null&&e.parseArg?r=this._callParseArg(e,r,a,o):r!==null&&e.variadic&&(r=e._concatValue(r,a)),r==null&&(e.negate?r=!1:e.isBoolean()||e.optional?r=!0:r=""),this.setOptionValueWithSource(n,r,l)};return this.on("option:"+t,r=>{const o=`error: option '${e.flags}' argument '${r}' is invalid.`;i(r,o,"cli")}),e.envVar&&this.on("optionEnv:"+t,r=>{const o=`error: option '${e.flags}' value '${r}' from env '${e.envVar}' is invalid.`;i(r,o,"env")}),this}_optionEx(e,t,n,i,r){if(typeof t=="object"&&t instanceof L)throw new Error("To add an Option object use addOption() instead of option() or requiredOption()");const o=this.createOption(t,n);if(o.makeOptionMandatory(!!e.mandatory),typeof i=="function")o.default(r).argParser(i);else if(i instanceof RegExp){const l=i;i=(a,u)=>{const h=l.exec(a);return h?h[0]:u},o.default(r).argParser(i)}else o.default(i);return this.addOption(o)}option(e,t,n,i){return this._optionEx({},e,t,n,i)}requiredOption(e,t,n,i){return this._optionEx({mandatory:!0},e,t,n,i)}combineFlagAndOptionalValue(e=!0){return this._combineFlagAndOptionalValue=!!e,this}allowUnknownOption(e=!0){return this._allowUnknownOption=!!e,this}allowExcessArguments(e=!0){return this._allowExcessArguments=!!e,this}enablePositionalOptions(e=!0){return this._enablePositionalOptions=!!e,this}passThroughOptions(e=!0){return this._passThroughOptions=!!e,this._checkForBrokenPassThrough(),this}_checkForBrokenPassThrough(){if(this.parent&&this._passThroughOptions&&!this.parent._enablePositionalOptions)throw new Error(`passThroughOptions cannot be used for '${this._name}' without turning on enablePositionalOptions for parent command(s)`)}storeOptionsAsProperties(e=!0){if(this.options.length)throw new Error("call .storeOptionsAsProperties() before adding options");if(Object.keys(this._optionValues).length)throw new Error("call .storeOptionsAsProperties() before setting option values");return this._storeOptionsAsProperties=!!e,this}getOptionValue(e){return this._storeOptionsAsProperties?this[e]:this._optionValues[e]}setOptionValue(e,t){return this.setOptionValueWithSource(e,t,void 0)}setOptionValueWithSource(e,t,n){return this._storeOptionsAsProperties?this[e]=t:this._optionValues[e]=t,this._optionValueSources[e]=n,this}getOptionValueSource(e){return this._optionValueSources[e]}getOptionValueSourceWithGlobals(e){let t;return this._getCommandAndAncestors().forEach(n=>{n.getOptionValueSource(e)!==void 0&&(t=n.getOptionValueSource(e))}),t}_prepareUserArgs(e,t){if(e!==void 0&&!Array.isArray(e))throw new Error("first parameter to parse must be array or undefined");t=t||{},e===void 0&&(e=g.argv,g.versions&&g.versions.electron&&(t.from="electron")),this.rawArgs=e.slice();let n;switch(t.from){case void 0:case"node":this._scriptPath=e[1],n=e.slice(2);break;case"electron":g.defaultApp?(this._scriptPath=e[1],n=e.slice(2)):n=e.slice(1);break;case"user":n=e.slice(0);break;default:throw new Error(`unexpected parse option { from: '${t.from}' }`)}return!this._name&&this._scriptPath&&this.nameFromFilename(this._scriptPath),this._name=this._name||"program",n}parse(e,t){const n=this._prepareUserArgs(e,t);return this._parseCommand([],n),this}async parseAsync(e,t){const n=this._prepareUserArgs(e,t);return await this._parseCommand([],n),this}_executeSubCommand(e,t){t=t.slice();let n=!1;const i=[".js",".ts",".tsx",".mjs",".cjs"];function r(h,f){const _=x.resolve(h,f);if(M.existsSync(_))return _;if(i.includes(x.extname(f)))return;const $=i.find(d=>M.existsSync(`${_}${d}`));if($)return`${_}${$}`}this._checkForMissingMandatoryOptions(),this._checkForConflictingOptions();let o=e._executableFile||`${this._name}-${e._name}`,l=this._executableDir||"";if(this._scriptPath){let h;try{h=M.realpathSync(this._scriptPath)}catch{h=this._scriptPath}l=x.resolve(x.dirname(h),l)}if(l){let h=r(l,o);if(!h&&!e._executableFile&&this._scriptPath){const f=x.basename(this._scriptPath,x.extname(this._scriptPath));f!==this._name&&(h=r(l,`${f}-${e._name}`))}o=h||o}n=i.includes(x.extname(o));let a;g.platform!=="win32"?n?(t.unshift(o),t=G(g.execArgv).concat(t),a=N.spawn(g.argv[0],t,{stdio:"inherit"})):a=N.spawn(o,t,{stdio:"inherit"}):(t.unshift(o),t=G(g.execArgv).concat(t),a=N.spawn(g.execPath,t,{stdio:"inherit"})),a.killed||["SIGUSR1","SIGUSR2","SIGTERM","SIGINT","SIGHUP"].forEach(f=>{g.on(f,()=>{a.killed===!1&&a.exitCode===null&&a.kill(f)})});const u=this._exitCallback;a.on("close",(h,f)=>{h=h??1,u?u(new U(h,"commander.executeSubCommandAsync","(close)")):g.exit(h)}),a.on("error",h=>{if(h.code==="ENOENT"){const f=l?`searched for local subcommand relative to directory '${l}'`:"no directory for search for local subcommand, use .executableDir() to supply a custom directory",_=`'${o}' does not exist
 - if '${e._name}' is not meant to be an executable command, remove description parameter from '.command()' and use '.description()' instead
 - if the default executable name is not suitable, use the executableFile option to supply a custom name or path
 - ${f}`;throw new Error(_)}else if(h.code==="EACCES")throw new Error(`'${o}' not executable`);if(!u)g.exit(1);else{const f=new U(1,"commander.executeSubCommandAsync","(error)");f.nestedError=h,u(f)}}),this.runningCommand=a}_dispatchSubcommand(e,t,n){const i=this._findCommand(e);i||this.help({error:!0});let r;return r=this._chainOrCallSubCommandHook(r,i,"preSubcommand"),r=this._chainOrCall(r,()=>{if(i._executableHandler)this._executeSubCommand(i,t.concat(n));else return i._parseCommand(t,n)}),r}_dispatchHelpCommand(e){e||this.help();const t=this._findCommand(e);return t&&!t._executableHandler&&t.help(),this._dispatchSubcommand(e,[],[this._getHelpOption()?.long??this._getHelpOption()?.short??"--help"])}_checkNumberOfArguments(){this.registeredArguments.forEach((e,t)=>{e.required&&this.args[t]==null&&this.missingArgument(e.name())}),!(this.registeredArguments.length>0&&this.registeredArguments[this.registeredArguments.length-1].variadic)&&this.args.length>this.registeredArguments.length&&this._excessArguments(this.args)}_processArguments(){const e=(n,i,r)=>{let o=i;if(i!==null&&n.parseArg){const l=`error: command-argument value '${i}' is invalid for argument '${n.name()}'.`;o=this._callParseArg(n,i,r,l)}return o};this._checkNumberOfArguments();const t=[];this.registeredArguments.forEach((n,i)=>{let r=n.defaultValue;n.variadic?i<this.args.length?(r=this.args.slice(i),n.parseArg&&(r=r.reduce((o,l)=>e(n,l,o),n.defaultValue))):r===void 0&&(r=[]):i<this.args.length&&(r=this.args[i],n.parseArg&&(r=e(n,r,n.defaultValue))),t[i]=r}),this.processedArgs=t}_chainOrCall(e,t){return e&&e.then&&typeof e.then=="function"?e.then(()=>t()):t()}_chainOrCallHooks(e,t){let n=e;const i=[];return this._getCommandAndAncestors().reverse().filter(r=>r._lifeCycleHooks[t]!==void 0).forEach(r=>{r._lifeCycleHooks[t].forEach(o=>{i.push({hookedCommand:r,callback:o})})}),t==="postAction"&&i.reverse(),i.forEach(r=>{n=this._chainOrCall(n,()=>r.callback(r.hookedCommand,this))}),n}_chainOrCallSubCommandHook(e,t,n){let i=e;return this._lifeCycleHooks[n]!==void 0&&this._lifeCycleHooks[n].forEach(r=>{i=this._chainOrCall(i,()=>r(this,t))}),i}_parseCommand(e,t){const n=this.parseOptions(t);if(this._parseOptionsEnv(),this._parseOptionsImplied(),e=e.concat(n.operands),t=n.unknown,this.args=e.concat(t),e&&this._findCommand(e[0]))return this._dispatchSubcommand(e[0],e.slice(1),t);if(this._getHelpCommand()&&e[0]===this._getHelpCommand().name())return this._dispatchHelpCommand(e[1]);if(this._defaultCommandName)return this._outputHelpIfRequested(t),this._dispatchSubcommand(this._defaultCommandName,e,t);this.commands.length&&this.args.length===0&&!this._actionHandler&&!this._defaultCommandName&&this.help({error:!0}),this._outputHelpIfRequested(n.unknown),this._checkForMissingMandatoryOptions(),this._checkForConflictingOptions();const i=()=>{n.unknown.length>0&&this.unknownOption(n.unknown[0])},r=`command:${this.name()}`;if(this._actionHandler){i(),this._processArguments();let o;return o=this._chainOrCallHooks(o,"preAction"),o=this._chainOrCall(o,()=>this._actionHandler(this.processedArgs)),this.parent&&(o=this._chainOrCall(o,()=>{this.parent.emit(r,e,t)})),o=this._chainOrCallHooks(o,"postAction"),o}if(this.parent&&this.parent.listenerCount(r))i(),this._processArguments(),this.parent.emit(r,e,t);else if(e.length){if(this._findCommand("*"))return this._dispatchSubcommand("*",e,t);this.listenerCount("command:*")?this.emit("command:*",e,t):this.commands.length?this.unknownCommand():(i(),this._processArguments())}else this.commands.length?(i(),this.help({error:!0})):(i(),this._processArguments())}_findCommand(e){if(e)return this.commands.find(t=>t._name===e||t._aliases.includes(e))}_findOption(e){return this.options.find(t=>t.is(e))}_checkForMissingMandatoryOptions(){this._getCommandAndAncestors().forEach(e=>{e.options.forEach(t=>{t.mandatory&&e.getOptionValue(t.attributeName())===void 0&&e.missingMandatoryOptionValue(t)})})}_checkForConflictingLocalOptions(){const e=this.options.filter(n=>{const i=n.attributeName();return this.getOptionValue(i)===void 0?!1:this.getOptionValueSource(i)!=="default"});e.filter(n=>n.conflictsWith.length>0).forEach(n=>{const i=e.find(r=>n.conflictsWith.includes(r.attributeName()));i&&this._conflictingOption(n,i)})}_checkForConflictingOptions(){this._getCommandAndAncestors().forEach(e=>{e._checkForConflictingLocalOptions()})}parseOptions(e){const t=[],n=[];let i=t;const r=e.slice();function o(a){return a.length>1&&a[0]==="-"}let l=null;for(;r.length;){const a=r.shift();if(a==="--"){i===n&&i.push(a),i.push(...r);break}if(l&&!o(a)){this.emit(`option:${l.name()}`,a);continue}if(l=null,o(a)){const u=this._findOption(a);if(u){if(u.required){const h=r.shift();h===void 0&&this.optionMissingArgument(u),this.emit(`option:${u.name()}`,h)}else if(u.optional){let h=null;r.length>0&&!o(r[0])&&(h=r.shift()),this.emit(`option:${u.name()}`,h)}else this.emit(`option:${u.name()}`);l=u.variadic?u:null;continue}}if(a.length>2&&a[0]==="-"&&a[1]!=="-"){const u=this._findOption(`-${a[1]}`);if(u){u.required||u.optional&&this._combineFlagAndOptionalValue?this.emit(`option:${u.name()}`,a.slice(2)):(this.emit(`option:${u.name()}`),r.unshift(`-${a.slice(2)}`));continue}}if(/^--[^=]+=/.test(a)){const u=a.indexOf("="),h=this._findOption(a.slice(0,u));if(h&&(h.required||h.optional)){this.emit(`option:${h.name()}`,a.slice(u+1));continue}}if(o(a)&&(i=n),(this._enablePositionalOptions||this._passThroughOptions)&&t.length===0&&n.length===0){if(this._findCommand(a)){t.push(a),r.length>0&&n.push(...r);break}else if(this._getHelpCommand()&&a===this._getHelpCommand().name()){t.push(a),r.length>0&&t.push(...r);break}else if(this._defaultCommandName){n.push(a),r.length>0&&n.push(...r);break}}if(this._passThroughOptions){i.push(a),r.length>0&&i.push(...r);break}i.push(a)}return{operands:t,unknown:n}}opts(){if(this._storeOptionsAsProperties){const e={},t=this.options.length;for(let n=0;n<t;n++){const i=this.options[n].attributeName();e[i]=i===this._versionOptionName?this._version:this[i]}return e}return this._optionValues}optsWithGlobals(){return this._getCommandAndAncestors().reduce((e,t)=>Object.assign(e,t.opts()),{})}error(e,t){this._outputConfiguration.outputError(`${e}
`,this._outputConfiguration.writeErr),typeof this._showHelpAfterError=="string"?this._outputConfiguration.writeErr(`${this._showHelpAfterError}
`):this._showHelpAfterError&&(this._outputConfiguration.writeErr(`
`),this.outputHelp({error:!0}));const n=t||{},i=n.exitCode||1,r=n.code||"commander.error";this._exit(i,r,e)}_parseOptionsEnv(){this.options.forEach(e=>{if(e.envVar&&e.envVar in g.env){const t=e.attributeName();(this.getOptionValue(t)===void 0||["default","config","env"].includes(this.getOptionValueSource(t)))&&(e.required||e.optional?this.emit(`optionEnv:${e.name()}`,g.env[e.envVar]):this.emit(`optionEnv:${e.name()}`))}})}_parseOptionsImplied(){const e=new Me(this.options),t=n=>this.getOptionValue(n)!==void 0&&!["default","implied"].includes(this.getOptionValueSource(n));this.options.filter(n=>n.implied!==void 0&&t(n.attributeName())&&e.valueFromOption(this.getOptionValue(n.attributeName()),n)).forEach(n=>{Object.keys(n.implied).filter(i=>!t(i)).forEach(i=>{this.setOptionValueWithSource(i,n.implied[i],"implied")})})}missingArgument(e){const t=`error: missing required argument '${e}'`;this.error(t,{code:"commander.missingArgument"})}optionMissingArgument(e){const t=`error: option '${e.flags}' argument missing`;this.error(t,{code:"commander.optionMissingArgument"})}missingMandatoryOptionValue(e){const t=`error: required option '${e.flags}' not specified`;this.error(t,{code:"commander.missingMandatoryOptionValue"})}_conflictingOption(e,t){const n=o=>{const l=o.attributeName(),a=this.getOptionValue(l),u=this.options.find(f=>f.negate&&l===f.attributeName()),h=this.options.find(f=>!f.negate&&l===f.attributeName());return u&&(u.presetArg===void 0&&a===!1||u.presetArg!==void 0&&a===u.presetArg)?u:h||o},i=o=>{const l=n(o),a=l.attributeName();return this.getOptionValueSource(a)==="env"?`environment variable '${l.envVar}'`:`option '${l.flags}'`},r=`error: ${i(e)} cannot be used with ${i(t)}`;this.error(r,{code:"commander.conflictingOption"})}unknownOption(e){if(this._allowUnknownOption)return;let t="";if(e.startsWith("--")&&this._showSuggestionAfterError){let i=[],r=this;do{const o=r.createHelp().visibleOptions(r).filter(l=>l.long).map(l=>l.long);i=i.concat(o),r=r.parent}while(r&&!r._enablePositionalOptions);t=R(e,i)}const n=`error: unknown option '${e}'${t}`;this.error(n,{code:"commander.unknownOption"})}_excessArguments(e){if(this._allowExcessArguments)return;const t=this.registeredArguments.length,n=t===1?"":"s",r=`error: too many arguments${this.parent?` for '${this.name()}'`:""}. Expected ${t} argument${n} but got ${e.length}.`;this.error(r,{code:"commander.excessArguments"})}unknownCommand(){const e=this.args[0];let t="";if(this._showSuggestionAfterError){const i=[];this.createHelp().visibleCommands(this).forEach(r=>{i.push(r.name()),r.alias()&&i.push(r.alias())}),t=R(e,i)}const n=`error: unknown command '${e}'${t}`;this.error(n,{code:"commander.unknownCommand"})}version(e,t,n){if(e===void 0)return this._version;this._version=e,t=t||"-V, --version",n=n||"output the version number";const i=this.createOption(t,n);return this._versionOptionName=i.attributeName(),this._registerOption(i),this.on("option:"+i.name(),()=>{this._outputConfiguration.writeOut(`${e}
`),this._exit(0,"commander.version",e)}),this}description(e,t){return e===void 0&&t===void 0?this._description:(this._description=e,t&&(this._argsDescription=t),this)}summary(e){return e===void 0?this._summary:(this._summary=e,this)}alias(e){if(e===void 0)return this._aliases[0];let t=this;if(this.commands.length!==0&&this.commands[this.commands.length-1]._executableHandler&&(t=this.commands[this.commands.length-1]),e===t._name)throw new Error("Command alias can't be the same as its name");const n=this.parent?._findCommand(e);if(n){const i=[n.name()].concat(n.aliases()).join("|");throw new Error(`cannot add alias '${e}' to command '${this.name()}' as already have command '${i}'`)}return t._aliases.push(e),this}aliases(e){return e===void 0?this._aliases:(e.forEach(t=>this.alias(t)),this)}usage(e){if(e===void 0){if(this._usage)return this._usage;const t=this.registeredArguments.map(n=>De(n));return[].concat(this.options.length||this._helpOption!==null?"[options]":[],this.commands.length?"[command]":[],this.registeredArguments.length?t:[]).join(" ")}return this._usage=e,this}name(e){return e===void 0?this._name:(this._name=e,this)}nameFromFilename(e){return this._name=x.basename(e,x.extname(e)),this}executableDir(e){return e===void 0?this._executableDir:(this._executableDir=e,this)}helpInformation(e){const t=this.createHelp();return t.helpWidth===void 0&&(t.helpWidth=e&&e.error?this._outputConfiguration.getErrHelpWidth():this._outputConfiguration.getOutHelpWidth()),t.formatHelp(this,t)}_getHelpContext(e){e=e||{};const t={error:!!e.error};let n;return t.error?n=i=>this._outputConfiguration.writeErr(i):n=i=>this._outputConfiguration.writeOut(i),t.write=e.write||n,t.command=this,t}outputHelp(e){let t;typeof e=="function"&&(t=e,e=void 0);const n=this._getHelpContext(e);this._getCommandAndAncestors().reverse().forEach(r=>r.emit("beforeAllHelp",n)),this.emit("beforeHelp",n);let i=this.helpInformation(n);if(t&&(i=t(i),typeof i!="string"&&!Buffer.isBuffer(i)))throw new Error("outputHelp callback must return a string or a Buffer");n.write(i),this._getHelpOption()?.long&&this.emit(this._getHelpOption().long),this.emit("afterHelp",n),this._getCommandAndAncestors().forEach(r=>r.emit("afterAllHelp",n))}helpOption(e,t){return typeof e=="boolean"?(e?this._helpOption=this._helpOption??void 0:this._helpOption=null,this):(e=e??"-h, --help",t=t??"display help for command",this._helpOption=this.createOption(e,t),this)}_getHelpOption(){return this._helpOption===void 0&&this.helpOption(void 0,void 0),this._helpOption}addHelpOption(e){return this._helpOption=e,this}help(e){this.outputHelp(e);let t=g.exitCode||0;t===0&&e&&typeof e!="function"&&e.error&&(t=1),this._exit(t,"commander.help","(outputHelp)")}addHelpText(e,t){const n=["beforeAll","before","after","afterAll"];if(!n.includes(e))throw new Error(`Unexpected value for position to addHelpText.
Expecting one of '${n.join("', '")}'`);const i=`${e}Help`;return this.on(i,r=>{let o;typeof t=="function"?o=t({error:r.error,command:r.command}):o=t,o&&r.write(`${o}
`)}),this}_outputHelpIfRequested(e){const t=this._getHelpOption();t&&e.find(i=>t.is(i))&&(this.outputHelp(),this._exit(0,"commander.helpDisplayed","(outputHelp)"))}};function G(s){return s.map(e=>{if(!e.startsWith("--inspect"))return e;let t,n="127.0.0.1",i="9229",r;return(r=e.match(/^(--inspect(-brk)?)$/))!==null?t=r[1]:(r=e.match(/^(--inspect(-brk|-port)?)=([^:]+)$/))!==null?(t=r[1],/^\d+$/.test(r[3])?i=r[3]:n=r[3]):(r=e.match(/^(--inspect(-brk|-port)?)=([^:]+):(\d+)$/))!==null&&(t=r[1],n=r[3],i=r[4]),t&&i!=="0"?`${t}=${n}:${parseInt(i)+1}`:e})}re.Command=Ue;const{Argument:le}=j,{Command:q}=re,{CommanderError:We,InvalidArgumentError:ue}=V,{Help:qe}=W,{Option:ce}=P;w.program=new q;w.createCommand=s=>new q(s);w.createOption=(s,e)=>new ce(s,e);w.createArgument=(s,e)=>new le(s,e);w.Command=q;w.Option=ce;w.Argument=le;w.Help=qe;w.CommanderError=We;w.InvalidArgumentError=ue;w.InvalidOptionArgumentError=ue;const{program:v,createCommand:Hi,createArgument:Vi,createOption:ji,CommanderError:ki,InvalidArgumentError:Ii,InvalidOptionArgumentError:Pi,Command:Fi,Argument:Di,Option:K,Help:Be}=w;var Le=c._assignValue,Re=c._baseAssignValue;function Ge(s,e,t,n){var i=!t;t||(t={});for(var r=-1,o=e.length;++r<o;){var l=e[r],a=n?n(t[l],s[l],l,t,s):void 0;a===void 0&&(a=s[l]),i?Re(t,l,a):Le(t,l,a)}return t}var F=Ge,Ke=F,Je=c.keys_1;function Ye(s,e){return s&&Ke(e,Je(e),s)}var Qe=Ye,Xe=F,Ze=c.keysIn_1;function ze(s,e){return s&&Xe(e,Ze(e),s)}var et=ze,I={exports:{}};I.exports;(function(s,e){var t=c._root,n=e&&!e.nodeType&&e,i=n&&!0&&s&&!s.nodeType&&s,r=i&&i.exports===n,o=r?t.Buffer:void 0,l=o?o.allocUnsafe:void 0;function a(u,h){if(h)return u.slice();var f=u.length,_=l?l(f):new u.constructor(f);return u.copy(_),_}s.exports=a})(I,I.exports);var tt=I.exports,nt=F,it=c._getSymbols;function rt(s,e){return nt(s,it(s),e)}var st=rt,ot=F,at=c._getSymbolsIn;function lt(s,e){return ot(s,at(s),e)}var ut=lt,ct=Object.prototype,ht=ct.hasOwnProperty;function pt(s){var e=s.length,t=new s.constructor(e);return e&&typeof s[0]=="string"&&ht.call(s,"index")&&(t.index=s.index,t.input=s.input),t}var ft=pt,J=c._Uint8Array;function dt(s){var e=new s.constructor(s.byteLength);return new J(e).set(new J(s)),e}var B=dt,mt=B;function gt(s,e){var t=e?mt(s.buffer):s.buffer;return new s.constructor(t,s.byteOffset,s.byteLength)}var _t=gt,bt=/\w*$/;function Ot(s){var e=new s.constructor(s.source,bt.exec(s));return e.lastIndex=s.lastIndex,e}var At=Ot,Y=c._Symbol,Q=Y?Y.prototype:void 0,X=Q?Q.valueOf:void 0;function $t(s){return X?Object(X.call(s)):{}}var Ct=$t,yt=B;function vt(s,e){var t=e?yt(s.buffer):s.buffer;return new s.constructor(t,s.byteOffset,s.length)}var wt=vt,Et=B,xt=_t,St=At,Tt=Ct,Ht=wt,Vt="[object Boolean]",jt="[object Date]",kt="[object Map]",It="[object Number]",Pt="[object RegExp]",Ft="[object Set]",Dt="[object String]",Nt="[object Symbol]",Mt="[object ArrayBuffer]",Ut="[object DataView]",Wt="[object Float32Array]",qt="[object Float64Array]",Bt="[object Int8Array]",Lt="[object Int16Array]",Rt="[object Int32Array]",Gt="[object Uint8Array]",Kt="[object Uint8ClampedArray]",Jt="[object Uint16Array]",Yt="[object Uint32Array]";function Qt(s,e,t){var n=s.constructor;switch(e){case Mt:return Et(s);case Vt:case jt:return new n(+s);case Ut:return xt(s,t);case Wt:case qt:case Bt:case Lt:case Rt:case Gt:case Kt:case Jt:case Yt:return Ht(s,t);case kt:return new n;case It:case Dt:return new n(s);case Pt:return St(s);case Ft:return new n;case Nt:return Tt(s)}}var Xt=Qt,Zt=c._baseCreate,zt=c._getPrototype,en=c._isPrototype;function tn(s){return typeof s.constructor=="function"&&!en(s)?Zt(zt(s)):{}}var nn=tn,rn=c._getTag,sn=c.isObjectLike_1,on="[object Map]";function an(s){return sn(s)&&rn(s)==on}var ln=an,un=ln,cn=c._baseUnary,Z=c._nodeUtilExports,z=Z&&Z.isMap,hn=z?cn(z):un,pn=hn,fn=c._getTag,dn=c.isObjectLike_1,mn="[object Set]";function gn(s){return dn(s)&&fn(s)==mn}var _n=gn,bn=_n,On=c._baseUnary,ee=c._nodeUtilExports,te=ee&&ee.isSet,An=te?On(te):bn,$n=An,Cn=c._Stack,yn=c._arrayEach,vn=c._assignValue,wn=Qe,En=et,xn=tt,Sn=c._copyArray,Tn=st,Hn=ut,Vn=c._getAllKeys,jn=c._getAllKeysIn,kn=c._getTag,In=ft,Pn=Xt,Fn=nn,Dn=c.isArray_1,Nn=c.isBufferExports,Mn=pn,Un=c.isObject_1,Wn=$n,qn=c.keys_1,Bn=c.keysIn_1,Ln=1,Rn=2,Gn=4,he="[object Arguments]",Kn="[object Array]",Jn="[object Boolean]",Yn="[object Date]",Qn="[object Error]",pe="[object Function]",Xn="[object GeneratorFunction]",Zn="[object Map]",zn="[object Number]",fe="[object Object]",ei="[object RegExp]",ti="[object Set]",ni="[object String]",ii="[object Symbol]",ri="[object WeakMap]",si="[object ArrayBuffer]",oi="[object DataView]",ai="[object Float32Array]",li="[object Float64Array]",ui="[object Int8Array]",ci="[object Int16Array]",hi="[object Int32Array]",pi="[object Uint8Array]",fi="[object Uint8ClampedArray]",di="[object Uint16Array]",mi="[object Uint32Array]",m={};m[he]=m[Kn]=m[si]=m[oi]=m[Jn]=m[Yn]=m[ai]=m[li]=m[ui]=m[ci]=m[hi]=m[Zn]=m[zn]=m[fe]=m[ei]=m[ti]=m[ni]=m[ii]=m[pi]=m[fi]=m[di]=m[mi]=!0;m[Qn]=m[pe]=m[ri]=!1;function k(s,e,t,n,i,r){var o,l=e&Ln,a=e&Rn,u=e&Gn;if(t&&(o=i?t(s,n,i,r):t(s)),o!==void 0)return o;if(!Un(s))return s;var h=Dn(s);if(h){if(o=In(s),!l)return Sn(s,o)}else{var f=kn(s),_=f==pe||f==Xn;if(Nn(s))return xn(s,l);if(f==fe||f==he||_&&!i){if(o=a||_?{}:Fn(s),!l)return a?Hn(s,En(o,s)):Tn(s,wn(o,s))}else{if(!m[f])return i?s:{};o=Pn(s,f,l)}}r||(r=new Cn);var $=r.get(s);if($)return $;r.set(s,o),Wn(s)?s.forEach(function(b){o.add(k(b,e,t,b,s,r))}):Mn(s)&&s.forEach(function(b,C){o.set(C,k(b,e,t,C,s,r))});var d=u?a?jn:Vn:a?Bn:qn,O=h?void 0:d(s);return yn(O||s,function(b,C){O&&(C=b,b=s[C]),vn(o,C,k(b,e,t,C,s,r))}),o}var gi=k,_i=gi,bi=1,Oi=4;function Ai(s){return _i(s,bi|Oi)}var $i=Ai;const Ci=c.getDefaultExportFromCjs($i),ne="Output the version number of npm-check-updates.",yi=s=>s.replace(/`/g,"");(async()=>{const{default:s}=await Promise.resolve().then(()=>require("./index-5sFb3Tvv.js")),e=s({pkg:c.pkg});if(e.update&&e.update.latest!==c.pkg.version){const{default:p}=await Promise.resolve().then(()=>require("./index-BmUFwMVL.js")),y=c.semver.parse(e.update.current)?.major,A=c.semver.parse(e.update.latest)?.major,T=(y&&A&&A>=y?new Array(A-y).fill(0).map((H,D)=>y+D+1):[]).map(H=>`${c.pkg.homepage??""}/releases/tag/v${H}.0.0`),S=`${c.pkg.homepage??""}/compare/v${e.update.current}...v${e.update.latest}`;e.notify({defer:!1,isGlobal:!0,message:`Update available ${p.dim("{currentVersion}")}${p.reset(" → ")}${e.update.type==="major"?p.red("{latestVersion}"):e.update.type==="minor"?p.yellow("{latestVersion}"):p.green("{latestVersion}")}
Run ${p.cyan("{updateCommand}")} to update
${p.dim.underline(e.update.type==="major"?T.map(H=>p.dim.underline(H)).join(`
`):S)}`})}const t=process.argv.slice(2),n=t.findIndex(p=>p==="--help"||p==="-h");if(n!==-1&&t[n+1]){const p=t[n+1].replace(/^-*/,"");p==="help"||p==="h"?console.info("Would you like some help with your help?"):(await c.chalkInit(),[...t.slice(0,n),...t.slice(n+1)].forEach(A=>{const E=A.replace(/^-*/,""),T=c.cliOptionsSorted.find(S=>E===S.long||E===S.short||E===`no-${S.long}`&&S.type==="boolean");console.info(T?c.renderExtendedHelp(T)+`
`:E==="version"||E==="v"||E==="V"?c.renderExtendedHelp({long:"version",short:"v",description:ne,type:"string"})+`
`:`Unknown option: ${A}`)})),process.exit(0)}const i=new Set(c.cliOptionsSorted.filter(p=>p.cli===!1).map(p=>`--${p.long}`));v.description("[filter] is a list or regex of package names to check (all others will be ignored).").usage("[options] [filter]").configureHelp({optionTerm:p=>p.long&&i.has(p.long)?p.long.replace("--","")+"*":p.long==="--version"?"-v, -V, --version":p.flags.replace("[bool]",""),optionDescription:p=>p.long==="--version"?ne:p.long==="--help"?`You're lookin' at it. Run "ncu --help <option>" for a specific option.`:Be.prototype.optionDescription(p)}).addOption(new K("-v, --versionAlias").hideHelp()).on("option:versionAlias",()=>{console.info(c.pkg.version),process.exit(0)}),c.cliOptionsSorted.forEach(({long:p,short:y,arg:A,description:E,default:T,help:S,parse:H,type:D})=>{const me=`${y?`-${y}, `:""}--${p}${A?` <${A}>`:""}`,ge=`${yi(E)}${S?` Run "ncu --help ${p}" for details.`:""}`;v.option(me,ge,H||T,H?T:void 0),D==="boolean"&&v.addOption(new K(`--no-${p}`).default(!1).hideHelp())}),v.version(c.pkg.version);const r=Ci(v._optionValues);v.parse(process.argv);const o=v.opts(),l=process.argv.slice(2),{color:a,configFileName:u,configFilePath:h,global:f,packageFile:_,mergeConfig:$}=o;await c.chalkInit(a);const d=!process.env.NCU_TESTS||h||$?await c.getNcuRc({configFileName:u,configFilePath:h,global:f,packageFile:_,options:{...o,cli:!0}}):null,O=(d?.args||[]).filter((p,y,A)=>(typeof p!="string"||!p.startsWith("-")||!l.includes(p))&&(typeof A[y-1]!="string"||!A[y-1].startsWith("-")||!l.includes(A[y-1]))),b=[...process.argv.slice(0,2),...O,...l];v._optionValues=r,v.parse(b);const C=v.opts(),de={...d&&Object.keys(d.config).length>0?{rcConfigPath:d.filePath}:null,...c.pickBy(v.opts(),p=>p!==void 0),args:v.args,...C.filter?{filter:C.filter}:null,...C.reject?{reject:C.reject}:null};c.run(de,{cli:!0})})();
//# sourceMappingURL=cli.js.map
