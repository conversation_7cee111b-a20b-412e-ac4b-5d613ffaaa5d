'use server'

import { NextRequest, NextResponse } from 'next/server'
import { RealAuthService } from '@/lib/auth-real'
import { db } from '@/lib/database'
import { z } from 'zod'

const statusSchema = z.object({
  status: z.enum(['active', 'inactive', 'suspended', 'banned'])
})

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate request - only admins can update user status
    const authResult = await RealAuthService.authenticateRequest(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (authResult.user.role !== 'admin' && authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      )
    }

    const userId = parseInt(params.id)
    if (isNaN(userId)) {
      return NextResponse.json(
        { success: false, message: 'Invalid user ID' },
        { status: 400 }
      )
    }

    // Get user details
    const [userRows] = await db.query(
      'SELECT * FROM users WHERE id = ?',
      [userId]
    )

    if (!userRows || (userRows as any[]).length === 0) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      )
    }

    const user = (userRows as any[])[0]

    // Super admins can only be modified by other super admins
    if (user.role === 'super_admin' && authResult.user.role !== 'super_admin') {
      return NextResponse.json(
        { success: false, message: 'You do not have permission to modify a super admin' },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const validatedData = statusSchema.parse(body)

    // Update user status
    await db.query(
      'UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?',
      [validatedData.status, userId]
    )

    // Log the status change
    await db.query(`
      INSERT INTO admin_logs (
        admin_id, action, target_type, target_id, details, created_at
      ) VALUES (
        ?, 'update_user_status', 'user', ?, ?, NOW()
      )
    `, [
      authResult.user.id,
      userId,
      JSON.stringify({
        previousStatus: user.status,
        newStatus: validatedData.status,
        adminUsername: authResult.user.username
      })
    ])

    // If user is being banned or suspended, invalidate all their API keys
    if (validatedData.status === 'banned' || validatedData.status === 'suspended') {
      await db.query(
        'UPDATE api_keys SET status = ?, updated_at = NOW() WHERE user_id = ? AND status = ?',
        ['revoked', userId, 'active']
      )
    }

    return NextResponse.json({
      success: true,
      message: `User status updated to ${validatedData.status}`,
      data: {
        userId: userId,
        status: validatedData.status
      }
    })

  } catch (error) {
    console.error('Error updating user status:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to update user status',
        error: error instanceof z.ZodError ? error.errors : undefined
      },
      { status: 500 }
    )
  }
}
