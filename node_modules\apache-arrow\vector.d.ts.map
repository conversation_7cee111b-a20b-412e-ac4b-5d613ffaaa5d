{"version": 3, "sources": ["vector.ts"], "names": [], "mappings": "AAmBA,OAAO,EAAE,QAAQ,EAAiB,MAAM,WAAW,CAAC;AACpD,OAAO,EAAE,IAAI,EAAY,SAAS,EAAE,MAAM,WAAW,CAAC;AACtD,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,kBAAkB,EAAE,MAAM,iBAAiB,CAAC;AAqB9E,MAAM,WAAW,MAAM,CAAC,CAAC,SAAS,QAAQ,GAAG,GAAG;IAK5C,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;IAC3B,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC7B,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;IAE7B;;OAEG;IACH,CAAC,MAAM,CAAC,kBAAkB,CAAC,EAAE,IAAI,CAAC;CACrC;AAKD;;GAEG;AACH,qBAAa,MAAM,CAAC,CAAC,SAAS,QAAQ,GAAG,GAAG;gBAE5B,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IAkCnD,UAAkB,QAAQ,EAAE,MAAM,EAAE,GAAG,WAAW,CAAC;IAEnD;;OAEG;IACH,SAAwB,IAAI,EAAE,CAAC,CAAC;IAEhC;;OAEG;IACH,SAAwB,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAErD;;OAEG;IACH,SAAwB,MAAM,EAAE,MAAM,CAAC;IAEvC;;OAEG;IACH,SAAwB,MAAM,EAAE,MAAM,CAAC;IAEvC;;OAEG;IACH,SAAwB,WAAW,EAAE,MAAM,CAAC;IAE5C;;OAEG;IACH,IAAW,UAAU,WAEpB;IAED;;OAEG;IACH,IAAW,QAAQ,YAElB;IAED;;OAEG;IACH,IAAW,SAAS,WAEnB;IAED;;;OAGG;IACH,IAAW,SAAS,IAAI,CAAC,CAAC,WAAW,CAAC,CAAgC;IAEtE;;OAEG;IACH,IAAW,CAAC,MAAM,CAAC,WAAW,CAAC,WAE9B;IAED;;OAEG;IACH,IAAW,UAAU,WAAgD;IAErE;;;OAGG;IAEI,OAAO,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO;IAEtC;;;OAGG;IAEI,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI;IAE7C;;;OAGG;IACI,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI;IAI5C;;;;OAIG;IAEI,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,GAAG,IAAI;IAE1D;;;;OAIG;IAEI,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,MAAM;IAEtD,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO;IAK/D;;OAEG;IACI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;IAIhE;;;OAGG;IACI,MAAM,CAAC,GAAG,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC;IAIhD;;;;OAIG;IACI,KAAK,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;IAM9C,MAAM;IAEb;;;;;;;;;;OAUG;IACI,OAAO,IAAI,CAAC,CAAC,QAAQ,CAAC;IAuB7B;;;;OAIG;IACI,QAAQ;IAIf;;;OAGG;IACI,QAAQ,CAAC,CAAC,SAAS,MAAM,CAAC,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,CAAC;IAIvD;;;OAGG;IACI,UAAU,CAAC,CAAC,SAAS,QAAQ,GAAG,GAAG,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI;IAO5E,IAAW,UAAU,IAAI,OAAO,CAK/B;IAED;;;;;;;;;;OAUG;IACI,OAAO,IAAI,cAAc,CAAC,CAAC,CAAC;IAanC;;;;;OAKG;IACI,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC;IAe7B,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,SA4BhB;CACxB;AAED,cAAM,cAAc,CAAC,CAAC,SAAS,QAAQ,GAAG,GAAG,CAAE,SAAQ,MAAM,CAAC,CAAC,CAAC;gBAEzC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;CA0CvC;AAED,OAAO,KAAK,MAAM,MAAM,WAAW,CAAC;AAEpC;;;;;;;GAOG;AACH,wBAAgB,UAAU,CAAC,CAAC,SAAS,UAAU,GAAG,WAAW,EAAE,IAAI,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;AACtH,wBAAgB,UAAU,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5F,wBAAgB,UAAU,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC9F,wBAAgB,UAAU,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAClG,wBAAgB,UAAU,CAAC,CAAC,SAAS,QAAQ,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC", "file": "vector.d.ts", "sourceRoot": "src"}