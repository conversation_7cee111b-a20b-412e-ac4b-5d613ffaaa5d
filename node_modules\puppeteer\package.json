{"name": "puppeteer", "version": "21.11.0", "description": "A high-level API to control headless Chrome over the DevTools Protocol", "keywords": ["puppeteer", "chrome", "headless", "automation"], "type": "commonjs", "bin": "./lib/esm/puppeteer/node/cli.js", "main": "./lib/cjs/puppeteer/puppeteer.js", "types": "./lib/types.d.ts", "exports": {".": {"types": "./lib/types.d.ts", "import": "./lib/esm/puppeteer/puppeteer.js", "require": "./lib/cjs/puppeteer/puppeteer.js"}, "./internal/*": {"import": "./lib/esm/puppeteer/*", "require": "./lib/cjs/puppeteer/*"}, "./*": {"import": "./*", "require": "./*"}}, "repository": {"type": "git", "url": "https://github.com/puppeteer/puppeteer/tree/main/packages/puppeteer"}, "engines": {"node": ">=16.13.2"}, "scripts": {"build:docs": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "postinstall": "node install.mjs", "prepack": "wireit"}, "wireit": {"prepack": {"command": "tsx ../../tools/cp.ts ../../README.md README.md", "files": ["../../README.md"], "output": ["README.md"]}, "build": {"dependencies": ["build:tsc", "build:types"]}, "generate:package-json": {"command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json", "files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"]}, "build:docs": {"command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "files": ["api-extractor.docs.json", "lib/esm/puppeteer/puppeteer-core.d.ts", "tsconfig.json"], "dependencies": ["build:tsc"]}, "build:tsc": {"command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/puppeteer/node/cli.js lib/esm/puppeteer/node/cli.js", "clean": "if-file-deleted", "dependencies": ["../puppeteer-core:build", "../browsers:build", "generate:package-json"], "files": ["src/**"], "output": ["lib/{cjs,esm}/**", "!lib/esm/package.json"]}, "build:types": {"command": "api-extractor run --local && eslint --cache-location .eslintcache --cache --ext=ts --no-ignore --no-eslintrc -c=../../.eslintrc.types.cjs --fix lib/types.d.ts", "files": ["../../.eslintrc.types.cjs", "api-extractor.json", "lib/esm/puppeteer/types.d.ts", "tsconfig.json"], "output": ["lib/types.d.ts"], "dependencies": ["build:tsc"]}}, "files": ["lib", "src", "install.mjs", "!*.test.ts", "!*.test.js", "!*.test.d.ts", "!*.test.js.map", "!*.test.d.ts.map", "!*.tsbuil<PERSON><PERSON>"], "author": "The Chromium Authors", "license": "Apache-2.0", "dependencies": {"cosmiconfig": "9.0.0", "puppeteer-core": "21.11.0", "@puppeteer/browsers": "1.9.1"}, "devDependencies": {"@types/node": "18.17.15"}}