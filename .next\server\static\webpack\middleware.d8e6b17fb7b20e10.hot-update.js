"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./lib/auth-simple.ts":
/*!****************************!*\
  !*** ./lib/auth-simple.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleAuthService: () => (/* binding */ SimpleAuthService)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(middleware)/./node_modules/bcryptjs/dist/bcrypt.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(middleware)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key\";\nconst JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || \"your-super-secret-refresh-key\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"1h\";\nconst JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || \"7d\";\nconst BCRYPT_ROUNDS = 12;\n// Mock users for testing\nconst mockUsers = [\n    {\n        id: 1,\n        username: \"admin\",\n        email: \"<EMAIL>\",\n        full_name: \"Administrator\",\n        role: \"admin\",\n        plan: \"Elite\",\n        level: 100,\n        score: 10000,\n        streak_days: 365,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"admin123\"\n    },\n    {\n        id: 2,\n        username: \"testuser\",\n        email: \"<EMAIL>\",\n        full_name: \"Test User\",\n        role: \"user\",\n        plan: \"Free\",\n        level: 1,\n        score: 0,\n        streak_days: 0,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"test123\"\n    }\n];\nclass SimpleAuthService {\n    // Hash password\n    static async hashPassword(password) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, BCRYPT_ROUNDS);\n    }\n    // Verify password\n    static async verifyPassword(password, hash) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hash);\n    }\n    // Generate JWT tokens\n    static generateTokens(user) {\n        const payload = {\n            id: user.id,\n            username: user.username,\n            email: user.email,\n            role: user.role,\n            plan: user.plan\n        };\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n        const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_REFRESH_SECRET, {\n            expiresIn: JWT_REFRESH_EXPIRES_IN\n        });\n        return {\n            accessToken,\n            refreshToken,\n            expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds\n        };\n    }\n    // Verify JWT token\n    static verifyToken(token, isRefreshToken = false) {\n        try {\n            const secret = isRefreshToken ? JWT_REFRESH_SECRET : JWT_SECRET;\n            return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        } catch (error) {\n            return null;\n        }\n    }\n    // Register new user\n    static async register(data) {\n        try {\n            // Check if user already exists\n            const existingUser = mockUsers.find((u)=>u.email === data.email || u.username === data.username);\n            if (existingUser) {\n                return {\n                    success: false,\n                    message: \"User already exists with this email or username\"\n                };\n            }\n            // Create new user\n            const newUser = {\n                id: mockUsers.length + 1,\n                username: data.username,\n                email: data.email,\n                full_name: data.fullName || \"\",\n                role: \"user\",\n                plan: \"Free\",\n                level: 1,\n                score: 0,\n                streak_days: 0,\n                email_verified: false,\n                status: \"active\",\n                last_active: new Date(),\n                created_at: new Date(),\n                password: data.password\n            };\n            // Add to mock users\n            mockUsers.push(newUser);\n            // Remove password from user object\n            const { password, ...userWithoutPassword } = newUser;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                message: \"Registration failed\"\n            };\n        }\n    }\n    // Login user\n    static async login(email, password) {\n        try {\n            // Find user by email\n            const user = mockUsers.find((u)=>u.email === email && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Verify password (simple comparison for testing)\n            if (password !== user.password) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Remove password from user object\n            const { password: userPassword, ...userWithoutPassword } = user;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"Login failed\"\n            };\n        }\n    }\n    // Authenticate request\n    static async authenticateRequest(request) {\n        try {\n            // Get token from Authorization header or cookies\n            let token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n            if (!token) {\n                // Try to get from cookies\n                const cookies = request.headers.get(\"cookie\");\n                console.log(\"\\uD83C\\uDF6A Cookies in middleware:\", cookies);\n                if (cookies) {\n                    const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n                    token = tokenMatch ? tokenMatch[1] : undefined;\n                    console.log(\"\\uD83D\\uDD11 Token from cookies:\", token ? \"Found\" : \"Not found\");\n                }\n            }\n            if (!token) {\n                console.log(\"❌ No token found in headers or cookies\");\n                return {\n                    success: false,\n                    message: \"No token provided\"\n                };\n            }\n            // Verify token\n            console.log(\"\\uD83D\\uDD0D Verifying token in middleware...\");\n            const decoded = this.verifyToken(token);\n            if (!decoded) {\n                console.log(\"❌ Token verification failed in middleware\");\n                return {\n                    success: false,\n                    message: \"Invalid token\"\n                };\n            }\n            console.log(\"✅ Token decoded successfully:\", decoded.id, decoded.username);\n            // Get user from mock data\n            const user = mockUsers.find((u)=>u.id === decoded.id);\n            if (!user) {\n                console.log(\"❌ User not found in mock data:\", decoded.id);\n                return {\n                    success: false,\n                    message: \"User not found\"\n                };\n            }\n            console.log(\"✅ User found in middleware:\", user.username);\n            const { password, ...userWithoutPassword } = user;\n            return {\n                success: true,\n                user: userWithoutPassword\n            };\n        } catch (error) {\n            console.error(\"Authentication error:\", error);\n            return {\n                success: false,\n                message: \"Authentication failed\"\n            };\n        }\n    }\n    // Logout user (for mock, just return success)\n    static async logout(userId, _sessionToken) {\n        // In a real implementation, this would remove the session from database\n        console.log(`User ${userId} logged out`);\n    }\n    // Refresh token\n    static async refreshToken(refreshToken) {\n        try {\n            // Verify refresh token\n            const decoded = this.verifyToken(refreshToken, true);\n            if (!decoded) {\n                return {\n                    success: false,\n                    message: \"Invalid refresh token\"\n                };\n            }\n            // Get user data\n            const user = mockUsers.find((u)=>u.id === decoded.id && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"User not found or inactive\"\n                };\n            }\n            const { password, ...userWithoutPassword } = user;\n            // Generate new tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update user last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            return {\n                success: false,\n                message: \"Token refresh failed\"\n            };\n        }\n    }\n    // Get user by ID\n    static async getUserById(userId) {\n        try {\n            const user = mockUsers.find((u)=>u.id === userId);\n            if (!user) return null;\n            const { password, ...userWithoutPassword } = user;\n            return userWithoutPassword;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            return null;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./lib/auth-simple.ts\n");

/***/ })

});