"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/verify/route";
exports.ids = ["app/api/auth/verify/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fverify%2Froute&page=%2Fapi%2Fauth%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fverify%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fverify%2Froute&page=%2Fapi%2Fauth%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fverify%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_Kode_XGuard_app_api_auth_verify_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/verify/route.ts */ \"(rsc)/./app/api/auth/verify/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/verify/route\",\n        pathname: \"/api/auth/verify\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/verify/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\api\\\\auth\\\\verify\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_Kode_XGuard_app_api_auth_verify_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/verify/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fverify%2Froute&page=%2Fapi%2Fauth%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fverify%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/verify/route.ts":
/*!**************************************!*\
  !*** ./app/api/auth/verify/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth_simple__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth-simple */ \"(rsc)/./lib/auth-simple.ts\");\n\n\nasync function GET(request) {\n    try {\n        console.log(\"\\uD83D\\uDD0D Token verification request started\");\n        // Get authentication result\n        const authResult = await _lib_auth_simple__WEBPACK_IMPORTED_MODULE_1__.SimpleAuthService.authenticateRequest(request);\n        if (!authResult.success || !authResult.user) {\n            console.log(\"❌ Token verification failed:\", authResult.message);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: authResult.message || \"Invalid or expired token\",\n                authenticated: false\n            }, {\n                status: 401\n            });\n        }\n        console.log(`✅ Token verified for user: ${authResult.user.username} (${authResult.user.id})`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            authenticated: true,\n            user: {\n                id: authResult.user.id,\n                username: authResult.user.username,\n                email: authResult.user.email,\n                fullName: authResult.user.full_name,\n                role: authResult.user.role,\n                plan: authResult.user.plan,\n                level: authResult.user.level,\n                score: authResult.user.score,\n                streak: authResult.user.streak_days,\n                emailVerified: authResult.user.email_verified,\n                lastActive: authResult.user.last_active,\n                createdAt: authResult.user.created_at\n            }\n        });\n    } catch (error) {\n        console.error(\"❌ Token verification error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Token verification failed\",\n            authenticated: false\n        }, {\n            status: 401\n        });\n    }\n}\nasync function OPTIONS(request) {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"GET, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/verify/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth-simple.ts":
/*!****************************!*\
  !*** ./lib/auth-simple.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleAuthService: () => (/* binding */ SimpleAuthService)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key\";\nconst JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || \"your-super-secret-refresh-key\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"1h\";\nconst JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || \"7d\";\nconst BCRYPT_ROUNDS = 12;\n// Mock users for testing\nconst mockUsers = [\n    {\n        id: 1,\n        username: \"admin\",\n        email: \"<EMAIL>\",\n        full_name: \"Administrator\",\n        role: \"admin\",\n        plan: \"Elite\",\n        level: 100,\n        score: 10000,\n        streak_days: 365,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"admin123\"\n    },\n    {\n        id: 2,\n        username: \"testuser\",\n        email: \"<EMAIL>\",\n        full_name: \"Test User\",\n        role: \"user\",\n        plan: \"Free\",\n        level: 1,\n        score: 0,\n        streak_days: 0,\n        email_verified: true,\n        status: \"active\",\n        last_active: new Date(),\n        created_at: new Date(),\n        password: \"test123\"\n    }\n];\nclass SimpleAuthService {\n    // Hash password\n    static async hashPassword(password) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, BCRYPT_ROUNDS);\n    }\n    // Verify password\n    static async verifyPassword(password, hash) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hash);\n    }\n    // Generate JWT tokens\n    static generateTokens(user) {\n        const payload = {\n            id: user.id,\n            username: user.username,\n            email: user.email,\n            role: user.role,\n            plan: user.plan\n        };\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN\n        });\n        const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_REFRESH_SECRET, {\n            expiresIn: JWT_REFRESH_EXPIRES_IN\n        });\n        return {\n            accessToken,\n            refreshToken,\n            expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds\n        };\n    }\n    // Verify JWT token\n    static verifyToken(token, isRefreshToken = false) {\n        try {\n            const secret = isRefreshToken ? JWT_REFRESH_SECRET : JWT_SECRET;\n            console.log(\"\\uD83D\\uDD0D Verifying token with secret length:\", secret.length);\n            console.log(\"\\uD83D\\uDD0D Token length:\", token.length);\n            const result = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n            console.log(\"✅ Token verification successful:\", result);\n            return result;\n        } catch (error) {\n            console.log(\"❌ Token verification error:\", error instanceof Error ? error.message : String(error));\n            return null;\n        }\n    }\n    // Register new user\n    static async register(data) {\n        try {\n            // Check if user already exists\n            const existingUser = mockUsers.find((u)=>u.email === data.email || u.username === data.username);\n            if (existingUser) {\n                return {\n                    success: false,\n                    message: \"User already exists with this email or username\"\n                };\n            }\n            // Create new user\n            const newUser = {\n                id: mockUsers.length + 1,\n                username: data.username,\n                email: data.email,\n                full_name: data.fullName || \"\",\n                role: \"user\",\n                plan: \"Free\",\n                level: 1,\n                score: 0,\n                streak_days: 0,\n                email_verified: false,\n                status: \"active\",\n                last_active: new Date(),\n                created_at: new Date(),\n                password: data.password\n            };\n            // Add to mock users\n            mockUsers.push(newUser);\n            // Remove password from user object\n            const { password, ...userWithoutPassword } = newUser;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                success: false,\n                message: \"Registration failed\"\n            };\n        }\n    }\n    // Login user\n    static async login(email, password) {\n        try {\n            // Find user by email\n            const user = mockUsers.find((u)=>u.email === email && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Verify password (simple comparison for testing)\n            if (password !== user.password) {\n                return {\n                    success: false,\n                    message: \"Invalid email or password\"\n                };\n            }\n            // Remove password from user object\n            const { password: userPassword, ...userWithoutPassword } = user;\n            // Generate tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"Login failed\"\n            };\n        }\n    }\n    // Authenticate request\n    static async authenticateRequest(request) {\n        try {\n            // Get token from Authorization header or cookies\n            let token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n            if (!token) {\n                // Try to get from cookies\n                const cookies = request.headers.get(\"cookie\");\n                console.log(\"\\uD83C\\uDF6A Cookies in middleware:\", cookies);\n                if (cookies) {\n                    const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n                    token = tokenMatch ? tokenMatch[1] : undefined;\n                    console.log(\"\\uD83D\\uDD11 Token from cookies:\", token ? \"Found\" : \"Not found\");\n                }\n            }\n            if (!token) {\n                console.log(\"❌ No token found in headers or cookies\");\n                return {\n                    success: false,\n                    message: \"No token provided\"\n                };\n            }\n            // Verify token\n            console.log(\"\\uD83D\\uDD0D Verifying token in middleware...\");\n            const decoded = this.verifyToken(token);\n            if (!decoded) {\n                console.log(\"❌ Token verification failed in middleware\");\n                return {\n                    success: false,\n                    message: \"Invalid token\"\n                };\n            }\n            console.log(\"✅ Token decoded successfully:\", decoded.id, decoded.username);\n            // Get user from mock data\n            const user = mockUsers.find((u)=>u.id === decoded.id);\n            if (!user) {\n                console.log(\"❌ User not found in mock data:\", decoded.id);\n                return {\n                    success: false,\n                    message: \"User not found\"\n                };\n            }\n            console.log(\"✅ User found in middleware:\", user.username);\n            const { password, ...userWithoutPassword } = user;\n            return {\n                success: true,\n                user: userWithoutPassword\n            };\n        } catch (error) {\n            console.error(\"Authentication error:\", error);\n            return {\n                success: false,\n                message: \"Authentication failed\"\n            };\n        }\n    }\n    // Logout user (for mock, just return success)\n    static async logout(userId, _sessionToken) {\n        // In a real implementation, this would remove the session from database\n        console.log(`User ${userId} logged out`);\n    }\n    // Refresh token\n    static async refreshToken(refreshToken) {\n        try {\n            // Verify refresh token\n            const decoded = this.verifyToken(refreshToken, true);\n            if (!decoded) {\n                return {\n                    success: false,\n                    message: \"Invalid refresh token\"\n                };\n            }\n            // Get user data\n            const user = mockUsers.find((u)=>u.id === decoded.id && (u.status === \"active\" || !u.status));\n            if (!user) {\n                return {\n                    success: false,\n                    message: \"User not found or inactive\"\n                };\n            }\n            const { password, ...userWithoutPassword } = user;\n            // Generate new tokens\n            const tokens = this.generateTokens(userWithoutPassword);\n            // Update user last active\n            user.last_active = new Date();\n            return {\n                success: true,\n                user: userWithoutPassword,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Token refresh error:\", error);\n            return {\n                success: false,\n                message: \"Token refresh failed\"\n            };\n        }\n    }\n    // Get user by ID\n    static async getUserById(userId) {\n        try {\n            const user = mockUsers.find((u)=>u.id === userId);\n            if (!user) return null;\n            const { password, ...userWithoutPassword } = user;\n            return userWithoutPassword;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            return null;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth-simple.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fverify%2Froute&page=%2Fapi%2Fauth%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fverify%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();