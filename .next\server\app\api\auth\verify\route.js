/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/verify/route";
exports.ids = ["app/api/auth/verify/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/mysql2/lib sync recursive ^cardinal.*$":
/*!****************************************************!*\
  !*** ./node_modules/mysql2/lib/ sync ^cardinal.*$ ***!
  \****************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/mysql2/lib sync recursive ^cardinal.*$";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "timers":
/*!*************************!*\
  !*** external "timers" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("timers");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:timers/promises":
/*!***************************************!*\
  !*** external "node:timers/promises" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:timers/promises");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fverify%2Froute&page=%2Fapi%2Fauth%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fverify%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fverify%2Froute&page=%2Fapi%2Fauth%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fverify%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Users_Downloads_Kode_XGuard_app_api_auth_verify_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/verify/route.ts */ \"(rsc)/./app/api/auth/verify/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/verify/route\",\n        pathname: \"/api/auth/verify\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/verify/route\"\n    },\n    resolvedPagePath: \"D:\\\\Users\\\\Downloads\\\\Kode-XGuard\\\\app\\\\api\\\\auth\\\\verify\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Users_Downloads_Kode_XGuard_app_api_auth_verify_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/verify/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fverify%2Froute&page=%2Fapi%2Fauth%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fverify%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/verify/route.ts":
/*!**************************************!*\
  !*** ./app/api/auth/verify/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n\n\nasync function GET(request) {\n    try {\n        console.log(\"\\uD83D\\uDD0D Token verification request started\");\n        // Get current user from token\n        const user = await _lib_auth__WEBPACK_IMPORTED_MODULE_1__.AuthService.getCurrentUser(request);\n        if (!user) {\n            console.log(\"❌ Token verification failed: No user found\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"Invalid or expired token\",\n                authenticated: false\n            }, {\n                status: 401\n            });\n        }\n        console.log(`✅ Token verified for user: ${user.username} (${user.id})`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            authenticated: true,\n            user: {\n                id: user.id,\n                username: user.username,\n                email: user.email,\n                fullName: user.full_name,\n                role: user.role,\n                plan: user.plan,\n                level: user.level,\n                score: user.score,\n                streak: user.streak_days,\n                emailVerified: user.email_verified,\n                lastActive: user.last_active,\n                createdAt: user.created_at\n            }\n        });\n    } catch (error) {\n        console.error(\"❌ Token verification error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Token verification failed\",\n            authenticated: false\n        }, {\n            status: 401\n        });\n    }\n}\nasync function OPTIONS(request) {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"GET, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/verify/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./lib/database.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n\n// Configuration\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"1h\";\nconst REFRESH_TOKEN_EXPIRES_IN = process.env.REFRESH_TOKEN_EXPIRES_IN || \"7d\";\nconst BCRYPT_ROUNDS = 12;\nclass AuthService {\n    // Hash password\n    static async hashPassword(password) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, BCRYPT_ROUNDS);\n    }\n    // Verify password\n    static async verifyPassword(password, hash) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hash);\n    }\n    // Generate JWT tokens\n    static generateTokens(user) {\n        const payload = {\n            userId: user.id,\n            email: user.email,\n            role: user.role,\n            plan: user.plan\n        };\n        const accessToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n            expiresIn: JWT_EXPIRES_IN,\n            issuer: \"kodexguard\",\n            audience: \"kodexguard-users\"\n        });\n        const refreshToken = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            type: \"refresh\"\n        }, JWT_SECRET, {\n            expiresIn: REFRESH_TOKEN_EXPIRES_IN,\n            issuer: \"kodexguard\",\n            audience: \"kodexguard-users\"\n        });\n        return {\n            accessToken,\n            refreshToken,\n            expiresIn: 7 * 24 * 60 * 60 // 7 days in seconds\n        };\n    }\n    // Register new user\n    static async register(userData) {\n        try {\n            // Check if user already exists\n            const existingUsers = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"SELECT id FROM users WHERE email = ? OR username = ?\", [\n                userData.email,\n                userData.username\n            ]);\n            if (existingUsers.length > 0) {\n                return {\n                    error: \"User already exists with this email or username\"\n                };\n            }\n            // Hash password\n            const passwordHash = await this.hashPassword(userData.password);\n            // Create user\n            const result = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(`INSERT INTO users (username, email, password_hash, full_name, role, plan, level, score, streak_days, email_verified, created_at, updated_at, last_active) \n         VALUES (?, ?, ?, ?, 'user', 'Free', 1, 0, 0, false, NOW(), NOW(), NOW())`, [\n                userData.username,\n                userData.email,\n                passwordHash,\n                userData.fullName || \"\"\n            ]);\n            const userId = result.insertId;\n            // Get created user\n            const users = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"SELECT * FROM users WHERE id = ?\", [\n                userId\n            ]);\n            const user = users[0];\n            // Generate tokens\n            const tokens = this.generateTokens(user);\n            return {\n                user,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            return {\n                error: \"Registration failed\"\n            };\n        }\n    }\n    // Login user\n    static async login(email, password) {\n        try {\n            // Find user by email\n            const users = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"SELECT * FROM users WHERE email = ?\", [\n                email\n            ]);\n            if (users.length === 0) {\n                return {\n                    error: \"Invalid email or password\"\n                };\n            }\n            const userRecord = users[0];\n            // Verify password\n            const isValidPassword = await this.verifyPassword(password, userRecord.password_hash);\n            if (!isValidPassword) {\n                return {\n                    error: \"Invalid email or password\"\n                };\n            }\n            // Update last active\n            await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"UPDATE users SET last_active = NOW() WHERE id = ?\", [\n                userRecord.id\n            ]);\n            // Remove password from user object\n            const user = {\n                id: userRecord.id,\n                username: userRecord.username,\n                email: userRecord.email,\n                full_name: userRecord.full_name,\n                role: userRecord.role,\n                plan: userRecord.plan,\n                level: userRecord.level,\n                score: userRecord.score,\n                streak_days: userRecord.streak_days,\n                email_verified: userRecord.email_verified,\n                last_active: userRecord.last_active,\n                created_at: userRecord.created_at\n            };\n            // Generate tokens\n            const tokens = this.generateTokens(user);\n            return {\n                user,\n                tokens\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                error: \"Login failed\"\n            };\n        }\n    }\n    // Authenticate request\n    static async authenticateRequest(request) {\n        try {\n            // Get token from Authorization header or cookies\n            let token = request.headers.get(\"authorization\")?.replace(\"Bearer \", \"\");\n            if (!token) {\n                // Try to get from cookies\n                const cookies = request.headers.get(\"cookie\");\n                if (cookies) {\n                    const tokenMatch = cookies.match(/accessToken=([^;]+)/);\n                    token = tokenMatch ? tokenMatch[1] : null;\n                }\n            }\n            if (!token) {\n                return {\n                    success: false,\n                    message: \"No token provided\"\n                };\n            }\n            // Verify token\n            const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n            if (!decoded) {\n                return {\n                    success: false,\n                    message: \"Invalid token\"\n                };\n            }\n            // Get user from database\n            const users = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"SELECT * FROM users WHERE id = ?\", [\n                decoded.userId\n            ]);\n            if (users.length === 0) {\n                return {\n                    success: false,\n                    message: \"User not found\"\n                };\n            }\n            const user = users[0];\n            return {\n                success: true,\n                user\n            };\n        } catch (error) {\n            console.error(\"Authentication error:\", error);\n            return {\n                success: false,\n                message: \"Authentication failed\"\n            };\n        }\n    }\n    // Get user by ID\n    static async getUserById(userId) {\n        try {\n            const users = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"SELECT * FROM users WHERE id = ?\", [\n                userId\n            ]);\n            return users.length > 0 ? users[0] : null;\n        } catch (error) {\n            console.error(\"Get user error:\", error);\n            return null;\n        }\n    }\n    // Get current user from request\n    static async getCurrentUser(request) {\n        try {\n            let token = null;\n            if (request) {\n                // Try Authorization header first\n                const authHeader = request.headers.get(\"authorization\");\n                if (authHeader?.startsWith(\"Bearer \")) {\n                    token = authHeader.substring(7);\n                } else {\n                    // Try cookies\n                    const cookieHeader = request.headers.get(\"cookie\");\n                    if (cookieHeader) {\n                        const tokenMatch = cookieHeader.match(/accessToken=([^;]+)/);\n                        token = tokenMatch ? tokenMatch[1] : null;\n                    }\n                }\n            } else {\n                // Server-side: get from cookies\n                try {\n                    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n                    token = cookieStore.get(\"accessToken\")?.value || null;\n                } catch (error) {\n                    // Cookies not available in this context\n                    return null;\n                }\n            }\n            if (!token) return null;\n            // Verify token\n            const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n            if (!decoded?.userId) return null;\n            // Get user from database\n            return await this.getUserById(decoded.userId);\n        } catch (error) {\n            console.error(\"Get current user error:\", error);\n            return null;\n        }\n    }\n    // Check if user is authenticated\n    static async isAuthenticated(request) {\n        const user = await this.getCurrentUser(request);\n        return user !== null;\n    }\n    // Check if user has specific role\n    static async hasRole(role, request) {\n        const user = await this.getCurrentUser(request);\n        return user?.role === role;\n    }\n    // Check if user is admin\n    static async isAdmin(request) {\n        return await this.hasRole(\"admin\", request);\n    }\n    // Update user profile\n    static async updateProfile(userId, profileData) {\n        try {\n            const updateFields = [];\n            const updateValues = [];\n            if (profileData.fullName !== undefined) {\n                updateFields.push(\"full_name = ?\");\n                updateValues.push(profileData.fullName);\n            }\n            if (updateFields.length === 0) {\n                return true // Nothing to update\n                ;\n            }\n            updateFields.push(\"updated_at = NOW()\");\n            updateValues.push(userId);\n            const sql = `UPDATE users SET ${updateFields.join(\", \")} WHERE id = ?`;\n            await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(sql, updateValues);\n            return true;\n        } catch (error) {\n            console.error(\"Update profile error:\", error);\n            return false;\n        }\n    }\n    // Change password\n    static async changePassword(userId, currentPassword, newPassword) {\n        try {\n            // Get current user\n            const users = await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"SELECT password_hash FROM users WHERE id = ?\", [\n                userId\n            ]);\n            if (users.length === 0) {\n                return {\n                    success: false,\n                    error: \"User not found\"\n                };\n            }\n            const userRecord = users[0];\n            // Verify current password\n            const isValid = await this.verifyPassword(currentPassword, userRecord.password_hash);\n            if (!isValid) {\n                return {\n                    success: false,\n                    error: \"Current password is incorrect\"\n                };\n            }\n            // Hash new password\n            const newPasswordHash = await this.hashPassword(newPassword);\n            // Update password\n            await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(\"UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?\", [\n                newPasswordHash,\n                userId\n            ]);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"Change password error:\", error);\n            return {\n                success: false,\n                error: \"Failed to change password\"\n            };\n        }\n    }\n    // Verify JWT token\n    static verifyToken(token) {\n        try {\n            return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n        } catch (error) {\n            return null;\n        }\n    }\n    // Generate random string\n    static generateRandomString(length) {\n        const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n        let result = \"\";\n        for(let i = 0; i < length; i++){\n            result += chars.charAt(Math.floor(Math.random() * chars.length));\n        }\n        return result;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database-mock.ts":
/*!******************************!*\
  !*** ./lib/database-mock.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MockDatabase: () => (/* binding */ MockDatabase),\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n// Mock database implementation for testing without MySQL\n// This provides realistic data for all features to work\n// Mock data\nconst mockUsers = [\n    {\n        id: 1,\n        username: \"cyberwarrior\",\n        email: \"<EMAIL>\",\n        full_name: \"Alex Chen\",\n        role: \"user\",\n        plan: \"Pro\",\n        level: 15,\n        score: 2500,\n        streak_days: 7,\n        email_verified: true,\n        last_active: new Date(),\n        created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)\n    },\n    {\n        id: 2,\n        username: \"admin\",\n        email: \"<EMAIL>\",\n        full_name: \"System Administrator\",\n        role: \"admin\",\n        plan: \"Elite\",\n        level: 100,\n        score: 50000,\n        streak_days: 365,\n        email_verified: true,\n        last_active: new Date(),\n        created_at: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)\n    },\n    {\n        id: 3,\n        username: \"securityexpert\",\n        email: \"<EMAIL>\",\n        full_name: \"Sarah Johnson\",\n        role: \"user\",\n        plan: \"Expert\",\n        level: 28,\n        score: 8950,\n        streak_days: 12,\n        email_verified: true,\n        last_active: new Date(Date.now() - 2 * 60 * 60 * 1000),\n        created_at: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)\n    }\n];\nconst mockScans = [\n    {\n        id: 1,\n        user_id: 1,\n        target_url: \"https://example.com\",\n        scan_type: \"comprehensive\",\n        status: \"completed\",\n        vulnerabilities_found: 15,\n        severity_critical: 2,\n        severity_high: 5,\n        severity_medium: 6,\n        severity_low: 2,\n        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 1 * 60 * 60 * 1000)\n    },\n    {\n        id: 2,\n        user_id: 1,\n        target_url: \"https://testsite.org\",\n        scan_type: \"advanced\",\n        status: \"completed\",\n        vulnerabilities_found: 8,\n        severity_critical: 0,\n        severity_high: 2,\n        severity_medium: 4,\n        severity_low: 2,\n        created_at: new Date(Date.now() - 24 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 23 * 60 * 60 * 1000)\n    },\n    {\n        id: 3,\n        user_id: 2,\n        target_url: \"https://vulnerable-app.com\",\n        scan_type: \"comprehensive\",\n        status: \"completed\",\n        vulnerabilities_found: 23,\n        severity_critical: 5,\n        severity_high: 8,\n        severity_medium: 7,\n        severity_low: 3,\n        created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000)\n    }\n];\nconst mockOSINTQueries = [\n    {\n        id: 1,\n        user_id: 1,\n        query_type: \"email\",\n        query_value: \"<EMAIL>\",\n        status: \"completed\",\n        results: {\n            breaches: [\n                \"Adobe\",\n                \"LinkedIn\"\n            ],\n            found: true\n        },\n        created_at: new Date(Date.now() - 30 * 60 * 1000),\n        completed_at: new Date(Date.now() - 25 * 60 * 1000)\n    },\n    {\n        id: 2,\n        user_id: 1,\n        query_type: \"domain\",\n        query_value: \"example.com\",\n        status: \"completed\",\n        results: {\n            subdomains: [\n                \"www\",\n                \"api\",\n                \"admin\"\n            ],\n            found: true\n        },\n        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 2 * 60 * 60 * 1000 + 5 * 60 * 1000)\n    }\n];\nconst mockFileAnalyses = [\n    {\n        id: 1,\n        user_id: 1,\n        filename: \"suspicious.exe\",\n        file_size: 2048576,\n        threat_detected: true,\n        threat_type: \"malware\",\n        status: \"completed\",\n        created_at: new Date(Date.now() - 45 * 60 * 1000),\n        completed_at: new Date(Date.now() - 40 * 60 * 1000)\n    },\n    {\n        id: 2,\n        user_id: 1,\n        filename: \"clean-file.pdf\",\n        file_size: 1024000,\n        threat_detected: false,\n        status: \"completed\",\n        created_at: new Date(Date.now() - 3 * 60 * 60 * 1000),\n        completed_at: new Date(Date.now() - 3 * 60 * 60 * 1000 + 2 * 60 * 1000)\n    }\n];\n// Mock database class\nclass MockDatabase {\n    static async query(sql, params = []) {\n        console.log(`Mock DB Query: ${sql}`, params);\n        // Simulate database delay\n        await new Promise((resolve)=>setTimeout(resolve, 100));\n        // Parse SQL and return appropriate mock data\n        if (sql.includes(\"SELECT * FROM users WHERE id = ?\")) {\n            const userId = params[0];\n            return mockUsers.filter((u)=>u.id === userId);\n        }\n        if (sql.includes(\"SELECT COUNT(*) as count FROM users\")) {\n            return [\n                {\n                    count: mockUsers.length\n                }\n            ];\n        }\n        if (sql.includes(\"SELECT COUNT(*) as count FROM vulnerability_scans\")) {\n            return [\n                {\n                    count: mockScans.length\n                }\n            ];\n        }\n        if (sql.includes(\"SELECT * FROM vulnerability_scans WHERE user_id = ?\")) {\n            const userId = params[0];\n            return mockScans.filter((s)=>s.user_id === userId);\n        }\n        if (sql.includes(\"SELECT * FROM osint_queries WHERE user_id = ?\")) {\n            const userId = params[0];\n            return mockOSINTQueries.filter((q)=>q.user_id === userId);\n        }\n        if (sql.includes(\"SELECT * FROM file_analyses WHERE user_id = ?\")) {\n            const userId = params[0];\n            return mockFileAnalyses.filter((f)=>f.user_id === userId);\n        }\n        if (sql.includes(\"INSERT INTO\")) {\n            // Return mock insert result\n            return {\n                insertId: Math.floor(Math.random() * 1000) + 100\n            };\n        }\n        if (sql.includes(\"UPDATE\")) {\n            // Return mock update result\n            return {\n                affectedRows: 1\n            };\n        }\n        // Default return for other queries\n        return [];\n    }\n}\n// Export mock database as default db\nconst db = MockDatabase;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database-mock.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseUtils: () => (/* binding */ DatabaseUtils),\n/* harmony export */   RedisUtils: () => (/* binding */ RedisUtils),\n/* harmony export */   closeDatabaseConnections: () => (/* binding */ closeDatabaseConnections),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   elasticsearch: () => (/* binding */ elasticsearch),\n/* harmony export */   initRedis: () => (/* binding */ initRedis),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   initializeDatabases: () => (/* binding */ initializeDatabases),\n/* harmony export */   redis: () => (/* binding */ redis),\n/* harmony export */   testDatabaseConnection: () => (/* binding */ testDatabaseConnection),\n/* harmony export */   testElasticsearchConnection: () => (/* binding */ testElasticsearchConnection)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"(rsc)/./node_modules/mysql2/promise.js\");\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! redis */ \"(rsc)/./node_modules/redis/dist/index.js\");\n/* harmony import */ var redis__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(redis__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @elastic/elasticsearch */ \"(rsc)/./node_modules/@elastic/elasticsearch/index.js\");\n/* harmony import */ var _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _database_mock__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./database-mock */ \"(rsc)/./lib/database-mock.ts\");\n\n\n\n\n\n// MySQL Database Configuration\nconst dbConfig = {\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"3306\"),\n    user: process.env.DB_USER || \"root\",\n    password: process.env.DB_PASSWORD || \"rootkan\",\n    database: process.env.DB_NAME || \"db_kodexguard\",\n    charset: \"utf8mb4\",\n    timezone: \"+00:00\",\n    // Pool configuration (valid options for mysql2)\n    connectionLimit: 10,\n    queueLimit: 0,\n    // Connection configuration\n    connectTimeout: 60000,\n    // Additional MySQL2 options\n    multipleStatements: false,\n    namedPlaceholders: false\n};\n// Create MySQL connection pool\nconst mysqlPool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0__.createPool(dbConfig);\n// For demo purposes, use mock database if MySQL is not available\n\nconst db = {\n    async query (sql, params = []) {\n        try {\n            // Use MySQL database\n            const [rows] = await mysqlPool.execute(sql, params);\n            return rows;\n        } catch (error) {\n            console.error(\"Database query error:\", error);\n            // For demo purposes, fallback to mock database\n            console.log(\"Falling back to mock database\");\n            return await _database_mock__WEBPACK_IMPORTED_MODULE_5__.MockDatabase.query(sql, params);\n        }\n    },\n    async close () {\n        await mysqlPool.end();\n    }\n};\n// Initialize database with schema\nasync function initializeDatabase() {\n    try {\n        console.log(\"\\uD83D\\uDDC4️ Initializing database schema...\");\n        // Read schema file\n        const schemaPath = (0,path__WEBPACK_IMPORTED_MODULE_4__.join)(process.cwd(), \"lib\", \"database\", \"schema.sql\");\n        const schema = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_3__.readFile)(schemaPath, \"utf-8\");\n        // Split schema into individual statements\n        const statements = schema.split(\";\").map((stmt)=>stmt.trim()).filter((stmt)=>stmt.length > 0 && !stmt.startsWith(\"--\"));\n        // Execute each statement\n        for (const statement of statements){\n            try {\n                await db.query(statement);\n            } catch (error) {\n                // Ignore \"table already exists\" errors\n                if (!error.message?.includes(\"already exists\")) {\n                    console.error(\"Schema error:\", error.message || error);\n                }\n            }\n        }\n        console.log(\"✅ Database schema initialized successfully\");\n        return true;\n    } catch (error) {\n        console.error(\"❌ Database schema initialization failed:\", error);\n        return false;\n    }\n}\n// Test database connection\nasync function testDatabaseConnection() {\n    try {\n        const connection = await mysqlPool.getConnection();\n        await connection.ping();\n        connection.release();\n        console.log(\"✅ MySQL database connected successfully\");\n        // Skip schema initialization to avoid foreign key constraint errors\n        // Database schema should be managed through migrations\n        // await initializeDatabase()\n        return true;\n    } catch (error) {\n        console.error(\"❌ MySQL database connection failed, using mock database\");\n        return true // Return true for mock database\n        ;\n    }\n}\n// Redis Configuration\nconst redisConfig = {\n    url: process.env.REDIS_URL || \"redis://localhost:6379\",\n    password: process.env.REDIS_PASSWORD || undefined,\n    socket: {\n        reconnectStrategy: (retries)=>Math.min(retries * 50, 500)\n    }\n};\n// Create Redis client\nconst redis = (0,redis__WEBPACK_IMPORTED_MODULE_1__.createClient)(redisConfig);\n// Redis connection handlers\nredis.on(\"error\", (err)=>{\n    console.error(\"❌ Redis Client Error:\", err);\n});\nredis.on(\"connect\", ()=>{\n    console.log(\"✅ Redis connected successfully\");\n});\nredis.on(\"reconnecting\", ()=>{\n    console.log(\"\\uD83D\\uDD04 Redis reconnecting...\");\n});\nredis.on(\"ready\", ()=>{\n    console.log(\"✅ Redis ready for operations\");\n});\n// Initialize Redis connection\nasync function initRedis() {\n    try {\n        if (!redis.isOpen) {\n            await redis.connect();\n        }\n        return true;\n    } catch (error) {\n        console.error(\"❌ Redis connection failed:\", error);\n        return false;\n    }\n}\n// Elasticsearch Configuration\nconst elasticsearchConfig = {\n    node: process.env.ELASTICSEARCH_URL || \"http://localhost:9200\",\n    auth: process.env.ELASTICSEARCH_USERNAME && process.env.ELASTICSEARCH_PASSWORD ? {\n        username: process.env.ELASTICSEARCH_USERNAME,\n        password: process.env.ELASTICSEARCH_PASSWORD\n    } : undefined,\n    requestTimeout: 30000,\n    pingTimeout: 3000,\n    maxRetries: 3\n};\n// Create Elasticsearch client\nconst elasticsearch = new _elastic_elasticsearch__WEBPACK_IMPORTED_MODULE_2__.Client(elasticsearchConfig);\n// Test Elasticsearch connection\nasync function testElasticsearchConnection() {\n    try {\n        const health = await elasticsearch.cluster.health();\n        console.log(\"✅ Elasticsearch connected successfully:\", health.cluster_name);\n        return true;\n    } catch (error) {\n        console.error(\"❌ Elasticsearch connection failed:\", error);\n        return false;\n    }\n}\n// Initialize all database connections\nasync function initializeDatabases() {\n    console.log(\"\\uD83D\\uDE80 Initializing database connections...\");\n    const results = {\n        mysql: await testDatabaseConnection(),\n        redis: await initRedis(),\n        elasticsearch: await testElasticsearchConnection()\n    };\n    const allConnected = Object.values(results).every(Boolean);\n    if (allConnected) {\n        console.log(\"✅ All databases connected successfully\");\n    } else {\n        console.warn(\"⚠️ Some database connections failed:\", results);\n    }\n    return results;\n}\n// Graceful shutdown\nasync function closeDatabaseConnections() {\n    console.log(\"\\uD83D\\uDD04 Closing database connections...\");\n    try {\n        await mysqlPool.end();\n        console.log(\"✅ MySQL connection closed\");\n    } catch (error) {\n        console.error(\"❌ Error closing MySQL connection:\", error);\n    }\n    try {\n        if (redis.isOpen) {\n            await redis.quit();\n        }\n        console.log(\"✅ Redis connection closed\");\n    } catch (error) {\n        console.error(\"❌ Error closing Redis connection:\", error);\n    }\n    try {\n        await elasticsearch.close();\n        console.log(\"✅ Elasticsearch connection closed\");\n    } catch (error) {\n        console.error(\"❌ Error closing Elasticsearch connection:\", error);\n    }\n}\n// Database utility functions\nclass DatabaseUtils {\n    // Execute query with error handling\n    static async query(sql, params) {\n        try {\n            return await db.query(sql, params || []);\n        } catch (error) {\n            console.error(\"Database query error:\", error);\n            throw error;\n        }\n    }\n    // Get single record\n    static async findOne(sql, params) {\n        const rows = await this.query(sql, params);\n        return Array.isArray(rows) && rows.length > 0 ? rows[0] : null;\n    }\n    // Get multiple records with pagination\n    static async findMany(sql, params, page = 1, limit = 10) {\n        const offset = (page - 1) * limit;\n        // Get total count\n        const countSql = sql.replace(/SELECT .+ FROM/, \"SELECT COUNT(*) as total FROM\");\n        const countResult = await this.findOne(countSql, params);\n        const total = countResult?.total || 0;\n        // Get paginated data\n        const dataSql = `${sql} LIMIT ${limit} OFFSET ${offset}`;\n        const data = await this.query(dataSql, params);\n        return {\n            data: Array.isArray(data) ? data : [],\n            total,\n            page,\n            limit\n        };\n    }\n    // Insert record\n    static async insert(table, data) {\n        const fields = Object.keys(data).join(\", \");\n        const placeholders = Object.keys(data).map(()=>\"?\").join(\", \");\n        const values = Object.values(data);\n        const sql = `INSERT INTO ${table} (${fields}) VALUES (${placeholders})`;\n        const result = await this.query(sql, values);\n        return result.insertId || data.id;\n    }\n    // Update record\n    static async update(table, data, where, whereParams) {\n        const setClause = Object.keys(data).map((key)=>`${key} = ?`).join(\", \");\n        const values = [\n            ...Object.values(data),\n            ...whereParams || []\n        ];\n        const sql = `UPDATE ${table} SET ${setClause} WHERE ${where}`;\n        const result = await this.query(sql, values);\n        return result.affectedRows > 0;\n    }\n    // Delete record\n    static async delete(table, where, whereParams) {\n        const sql = `DELETE FROM ${table} WHERE ${where}`;\n        const result = await this.query(sql, whereParams);\n        return result.affectedRows > 0;\n    }\n}\n// Redis utility functions\nclass RedisUtils {\n    // Set value with expiration\n    static async set(key, value, expireInSeconds) {\n        try {\n            const serializedValue = typeof value === \"string\" ? value : JSON.stringify(value);\n            if (expireInSeconds) {\n                await redis.setEx(key, expireInSeconds, serializedValue);\n            } else {\n                await redis.set(key, serializedValue);\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Redis set error:\", error);\n            return false;\n        }\n    }\n    // Get value\n    static async get(key) {\n        try {\n            const value = await redis.get(key);\n            if (!value) return null;\n            try {\n                return JSON.parse(value);\n            } catch  {\n                return value;\n            }\n        } catch (error) {\n            console.error(\"Redis get error:\", error);\n            return null;\n        }\n    }\n    // Delete key\n    static async del(key) {\n        try {\n            const result = await redis.del(key);\n            return result > 0;\n        } catch (error) {\n            console.error(\"Redis delete error:\", error);\n            return false;\n        }\n    }\n    // Check if key exists\n    static async exists(key) {\n        try {\n            const result = await redis.exists(key);\n            return result === 1;\n        } catch (error) {\n            console.error(\"Redis exists error:\", error);\n            return false;\n        }\n    }\n    // Increment counter\n    static async incr(key, expireInSeconds) {\n        try {\n            const result = await redis.incr(key);\n            if (expireInSeconds && result === 1) {\n                await redis.expire(key, expireInSeconds);\n            }\n            return result;\n        } catch (error) {\n            console.error(\"Redis increment error:\", error);\n            return 0;\n        }\n    }\n}\n// Export default database instance\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (db);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/@redis","vendor-chunks/apache-arrow","vendor-chunks/@elastic","vendor-chunks/undici","vendor-chunks/mysql2","vendor-chunks/iconv-lite","vendor-chunks/flatbuffers","vendor-chunks/debug","vendor-chunks/aws-ssl-profiles","vendor-chunks/sqlstring","vendor-chunks/seq-queue","vendor-chunks/tslib","vendor-chunks/secure-json-parse","vendor-chunks/lru-cache","vendor-chunks/long","vendor-chunks/supports-color","vendor-chunks/safer-buffer","vendor-chunks/redis","vendor-chunks/named-placeholders","vendor-chunks/lru.min","vendor-chunks/is-property","vendor-chunks/hpagent","vendor-chunks/has-flag","vendor-chunks/generate-function","vendor-chunks/denque","vendor-chunks/cluster-key-slot"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fverify%2Froute&page=%2Fapi%2Fauth%2Fverify%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fverify%2Froute.ts&appDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CUsers%5CDownloads%5CKode-XGuard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();