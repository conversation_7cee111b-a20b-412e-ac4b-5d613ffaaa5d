{"version": 3, "file": "cli.js", "sources": ["../node_modules/commander/lib/error.js", "../node_modules/commander/lib/argument.js", "../node_modules/commander/lib/help.js", "../node_modules/commander/lib/option.js", "../node_modules/commander/lib/suggestSimilar.js", "../node_modules/commander/lib/command.js", "../node_modules/commander/index.js", "../node_modules/commander/esm.mjs", "../node_modules/lodash/_copyObject.js", "../node_modules/lodash/_baseAssign.js", "../node_modules/lodash/_baseAssignIn.js", "../node_modules/lodash/_cloneBuffer.js", "../node_modules/lodash/_copySymbols.js", "../node_modules/lodash/_copySymbolsIn.js", "../node_modules/lodash/_initCloneArray.js", "../node_modules/lodash/_cloneArrayBuffer.js", "../node_modules/lodash/_cloneDataView.js", "../node_modules/lodash/_cloneRegExp.js", "../node_modules/lodash/_cloneSymbol.js", "../node_modules/lodash/_cloneTypedArray.js", "../node_modules/lodash/_initCloneByTag.js", "../node_modules/lodash/_initCloneObject.js", "../node_modules/lodash/_baseIsMap.js", "../node_modules/lodash/isMap.js", "../node_modules/lodash/_baseIsSet.js", "../node_modules/lodash/isSet.js", "../node_modules/lodash/_baseClone.js", "../node_modules/lodash/cloneDeep.js", "../src/bin/cli.ts"], "sourcesContent": ["/**\n * CommanderError class\n * @class\n */\nclass CommanderError extends Error {\n  /**\n   * Constructs the CommanderError class\n   * @param {number} exitCode suggested exit code which could be used with process.exit\n   * @param {string} code an id string representing the error\n   * @param {string} message human-readable description of the error\n   * @constructor\n   */\n  constructor(exitCode, code, message) {\n    super(message);\n    // properly capture stack trace in Node.js\n    Error.captureStackTrace(this, this.constructor);\n    this.name = this.constructor.name;\n    this.code = code;\n    this.exitCode = exitCode;\n    this.nestedError = undefined;\n  }\n}\n\n/**\n * InvalidArgumentError class\n * @class\n */\nclass InvalidArgumentError extends CommanderError {\n  /**\n   * Constructs the InvalidArgumentError class\n   * @param {string} [message] explanation of why argument is invalid\n   * @constructor\n   */\n  constructor(message) {\n    super(1, 'commander.invalidArgument', message);\n    // properly capture stack trace in Node.js\n    Error.captureStackTrace(this, this.constructor);\n    this.name = this.constructor.name;\n  }\n}\n\nexports.CommanderError = CommanderError;\nexports.InvalidArgumentError = InvalidArgumentError;\n", "const { InvalidArgumentError } = require('./error.js');\n\nclass Argument {\n  /**\n   * Initialize a new command argument with the given name and description.\n   * The default is that the argument is required, and you can explicitly\n   * indicate this with <> around the name. Put [] around the name for an optional argument.\n   *\n   * @param {string} name\n   * @param {string} [description]\n   */\n\n  constructor(name, description) {\n    this.description = description || '';\n    this.variadic = false;\n    this.parseArg = undefined;\n    this.defaultValue = undefined;\n    this.defaultValueDescription = undefined;\n    this.argChoices = undefined;\n\n    switch (name[0]) {\n      case '<': // e.g. <required>\n        this.required = true;\n        this._name = name.slice(1, -1);\n        break;\n      case '[': // e.g. [optional]\n        this.required = false;\n        this._name = name.slice(1, -1);\n        break;\n      default:\n        this.required = true;\n        this._name = name;\n        break;\n    }\n\n    if (this._name.length > 3 && this._name.slice(-3) === '...') {\n      this.variadic = true;\n      this._name = this._name.slice(0, -3);\n    }\n  }\n\n  /**\n   * Return argument name.\n   *\n   * @return {string}\n   */\n\n  name() {\n    return this._name;\n  }\n\n  /**\n   * @package internal use only\n   */\n\n  _concatValue(value, previous) {\n    if (previous === this.defaultValue || !Array.isArray(previous)) {\n      return [value];\n    }\n\n    return previous.concat(value);\n  }\n\n  /**\n   * Set the default value, and optionally supply the description to be displayed in the help.\n   *\n   * @param {*} value\n   * @param {string} [description]\n   * @return {Argument}\n   */\n\n  default(value, description) {\n    this.defaultValue = value;\n    this.defaultValueDescription = description;\n    return this;\n  }\n\n  /**\n   * Set the custom handler for processing CLI command arguments into argument values.\n   *\n   * @param {Function} [fn]\n   * @return {Argument}\n   */\n\n  argParser(fn) {\n    this.parseArg = fn;\n    return this;\n  }\n\n  /**\n   * Only allow argument value to be one of choices.\n   *\n   * @param {string[]} values\n   * @return {Argument}\n   */\n\n  choices(values) {\n    this.argChoices = values.slice();\n    this.parseArg = (arg, previous) => {\n      if (!this.argChoices.includes(arg)) {\n        throw new InvalidArgumentError(`Allowed choices are ${this.argChoices.join(', ')}.`);\n      }\n      if (this.variadic) {\n        return this._concatValue(arg, previous);\n      }\n      return arg;\n    };\n    return this;\n  }\n\n  /**\n   * Make argument required.\n   */\n  argRequired() {\n    this.required = true;\n    return this;\n  }\n\n  /**\n   * Make argument optional.\n   */\n  argOptional() {\n    this.required = false;\n    return this;\n  }\n}\n\n/**\n * Takes an argument and returns its human readable equivalent for help usage.\n *\n * @param {Argument} arg\n * @return {string}\n * @private\n */\n\nfunction humanReadableArgName(arg) {\n  const nameOutput = arg.name() + (arg.variadic === true ? '...' : '');\n\n  return arg.required\n    ? '<' + nameOutput + '>'\n    : '[' + nameOutput + ']';\n}\n\nexports.Argument = Argument;\nexports.humanReadableArgName = humanReadableArgName;\n", "const { humanReadableArgName } = require('./argument.js');\n\n/**\n * TypeScript import types for JSDoc, used by Visual Studio Code IntelliSense and `npm run typescript-checkJS`\n * https://www.typescriptlang.org/docs/handbook/jsdoc-supported-types.html#import-types\n * @typedef { import(\"./argument.js\").Argument } Argument\n * @typedef { import(\"./command.js\").Command } Command\n * @typedef { import(\"./option.js\").Option } Option\n */\n\n// Although this is a class, methods are static in style to allow override using subclass or just functions.\nclass Help {\n  constructor() {\n    this.helpWidth = undefined;\n    this.sortSubcommands = false;\n    this.sortOptions = false;\n    this.showGlobalOptions = false;\n  }\n\n  /**\n   * Get an array of the visible subcommands. Includes a placeholder for the implicit help command, if there is one.\n   *\n   * @param {Command} cmd\n   * @returns {Command[]}\n   */\n\n  visibleCommands(cmd) {\n    const visibleCommands = cmd.commands.filter(cmd => !cmd._hidden);\n    const helpCommand = cmd._getHelpCommand();\n    if (helpCommand && !helpCommand._hidden) {\n      visibleCommands.push(helpCommand);\n    }\n    if (this.sortSubcommands) {\n      visibleCommands.sort((a, b) => {\n        // @ts-ignore: overloaded return type\n        return a.name().localeCompare(b.name());\n      });\n    }\n    return visibleCommands;\n  }\n\n  /**\n   * Compare options for sort.\n   *\n   * @param {Option} a\n   * @param {Option} b\n   * @returns number\n   */\n  compareOptions(a, b) {\n    const getSortKey = (option) => {\n      // WYSIWYG for order displayed in help. Short used for comparison if present. No special handling for negated.\n      return option.short ? option.short.replace(/^-/, '') : option.long.replace(/^--/, '');\n    };\n    return getSortKey(a).localeCompare(getSortKey(b));\n  }\n\n  /**\n   * Get an array of the visible options. Includes a placeholder for the implicit help option, if there is one.\n   *\n   * @param {Command} cmd\n   * @returns {Option[]}\n   */\n\n  visibleOptions(cmd) {\n    const visibleOptions = cmd.options.filter((option) => !option.hidden);\n    // Built-in help option.\n    const helpOption = cmd._getHelpOption();\n    if (helpOption && !helpOption.hidden) {\n      // Automatically hide conflicting flags. Bit dubious but a historical behaviour that is convenient for single-command programs.\n      const removeShort = helpOption.short && cmd._findOption(helpOption.short);\n      const removeLong = helpOption.long && cmd._findOption(helpOption.long);\n      if (!removeShort && !removeLong) {\n        visibleOptions.push(helpOption); // no changes needed\n      } else if (helpOption.long && !removeLong) {\n        visibleOptions.push(cmd.createOption(helpOption.long, helpOption.description));\n      } else if (helpOption.short && !removeShort) {\n        visibleOptions.push(cmd.createOption(helpOption.short, helpOption.description));\n      }\n    }\n    if (this.sortOptions) {\n      visibleOptions.sort(this.compareOptions);\n    }\n    return visibleOptions;\n  }\n\n  /**\n   * Get an array of the visible global options. (Not including help.)\n   *\n   * @param {Command} cmd\n   * @returns {Option[]}\n   */\n\n  visibleGlobalOptions(cmd) {\n    if (!this.showGlobalOptions) return [];\n\n    const globalOptions = [];\n    for (let ancestorCmd = cmd.parent; ancestorCmd; ancestorCmd = ancestorCmd.parent) {\n      const visibleOptions = ancestorCmd.options.filter((option) => !option.hidden);\n      globalOptions.push(...visibleOptions);\n    }\n    if (this.sortOptions) {\n      globalOptions.sort(this.compareOptions);\n    }\n    return globalOptions;\n  }\n\n  /**\n   * Get an array of the arguments if any have a description.\n   *\n   * @param {Command} cmd\n   * @returns {Argument[]}\n   */\n\n  visibleArguments(cmd) {\n    // Side effect! Apply the legacy descriptions before the arguments are displayed.\n    if (cmd._argsDescription) {\n      cmd.registeredArguments.forEach(argument => {\n        argument.description = argument.description || cmd._argsDescription[argument.name()] || '';\n      });\n    }\n\n    // If there are any arguments with a description then return all the arguments.\n    if (cmd.registeredArguments.find(argument => argument.description)) {\n      return cmd.registeredArguments;\n    }\n    return [];\n  }\n\n  /**\n   * Get the command term to show in the list of subcommands.\n   *\n   * @param {Command} cmd\n   * @returns {string}\n   */\n\n  subcommandTerm(cmd) {\n    // Legacy. Ignores custom usage string, and nested commands.\n    const args = cmd.registeredArguments.map(arg => humanReadableArgName(arg)).join(' ');\n    return cmd._name +\n      (cmd._aliases[0] ? '|' + cmd._aliases[0] : '') +\n      (cmd.options.length ? ' [options]' : '') + // simplistic check for non-help option\n      (args ? ' ' + args : '');\n  }\n\n  /**\n   * Get the option term to show in the list of options.\n   *\n   * @param {Option} option\n   * @returns {string}\n   */\n\n  optionTerm(option) {\n    return option.flags;\n  }\n\n  /**\n   * Get the argument term to show in the list of arguments.\n   *\n   * @param {Argument} argument\n   * @returns {string}\n   */\n\n  argumentTerm(argument) {\n    return argument.name();\n  }\n\n  /**\n   * Get the longest command term length.\n   *\n   * @param {Command} cmd\n   * @param {Help} helper\n   * @returns {number}\n   */\n\n  longestSubcommandTermLength(cmd, helper) {\n    return helper.visibleCommands(cmd).reduce((max, command) => {\n      return Math.max(max, helper.subcommandTerm(command).length);\n    }, 0);\n  }\n\n  /**\n   * Get the longest option term length.\n   *\n   * @param {Command} cmd\n   * @param {Help} helper\n   * @returns {number}\n   */\n\n  longestOptionTermLength(cmd, helper) {\n    return helper.visibleOptions(cmd).reduce((max, option) => {\n      return Math.max(max, helper.optionTerm(option).length);\n    }, 0);\n  }\n\n  /**\n   * Get the longest global option term length.\n   *\n   * @param {Command} cmd\n   * @param {Help} helper\n   * @returns {number}\n   */\n\n  longestGlobalOptionTermLength(cmd, helper) {\n    return helper.visibleGlobalOptions(cmd).reduce((max, option) => {\n      return Math.max(max, helper.optionTerm(option).length);\n    }, 0);\n  }\n\n  /**\n   * Get the longest argument term length.\n   *\n   * @param {Command} cmd\n   * @param {Help} helper\n   * @returns {number}\n   */\n\n  longestArgumentTermLength(cmd, helper) {\n    return helper.visibleArguments(cmd).reduce((max, argument) => {\n      return Math.max(max, helper.argumentTerm(argument).length);\n    }, 0);\n  }\n\n  /**\n   * Get the command usage to be displayed at the top of the built-in help.\n   *\n   * @param {Command} cmd\n   * @returns {string}\n   */\n\n  commandUsage(cmd) {\n    // Usage\n    let cmdName = cmd._name;\n    if (cmd._aliases[0]) {\n      cmdName = cmdName + '|' + cmd._aliases[0];\n    }\n    let ancestorCmdNames = '';\n    for (let ancestorCmd = cmd.parent; ancestorCmd; ancestorCmd = ancestorCmd.parent) {\n      ancestorCmdNames = ancestorCmd.name() + ' ' + ancestorCmdNames;\n    }\n    return ancestorCmdNames + cmdName + ' ' + cmd.usage();\n  }\n\n  /**\n   * Get the description for the command.\n   *\n   * @param {Command} cmd\n   * @returns {string}\n   */\n\n  commandDescription(cmd) {\n    // @ts-ignore: overloaded return type\n    return cmd.description();\n  }\n\n  /**\n   * Get the subcommand summary to show in the list of subcommands.\n   * (Fallback to description for backwards compatibility.)\n   *\n   * @param {Command} cmd\n   * @returns {string}\n   */\n\n  subcommandDescription(cmd) {\n    // @ts-ignore: overloaded return type\n    return cmd.summary() || cmd.description();\n  }\n\n  /**\n   * Get the option description to show in the list of options.\n   *\n   * @param {Option} option\n   * @return {string}\n   */\n\n  optionDescription(option) {\n    const extraInfo = [];\n\n    if (option.argChoices) {\n      extraInfo.push(\n        // use stringify to match the display of the default value\n        `choices: ${option.argChoices.map((choice) => JSON.stringify(choice)).join(', ')}`);\n    }\n    if (option.defaultValue !== undefined) {\n      // default for boolean and negated more for programmer than end user,\n      // but show true/false for boolean option as may be for hand-rolled env or config processing.\n      const showDefault = option.required || option.optional ||\n        (option.isBoolean() && typeof option.defaultValue === 'boolean');\n      if (showDefault) {\n        extraInfo.push(`default: ${option.defaultValueDescription || JSON.stringify(option.defaultValue)}`);\n      }\n    }\n    // preset for boolean and negated are more for programmer than end user\n    if (option.presetArg !== undefined && option.optional) {\n      extraInfo.push(`preset: ${JSON.stringify(option.presetArg)}`);\n    }\n    if (option.envVar !== undefined) {\n      extraInfo.push(`env: ${option.envVar}`);\n    }\n    if (extraInfo.length > 0) {\n      return `${option.description} (${extraInfo.join(', ')})`;\n    }\n\n    return option.description;\n  }\n\n  /**\n   * Get the argument description to show in the list of arguments.\n   *\n   * @param {Argument} argument\n   * @return {string}\n   */\n\n  argumentDescription(argument) {\n    const extraInfo = [];\n    if (argument.argChoices) {\n      extraInfo.push(\n        // use stringify to match the display of the default value\n        `choices: ${argument.argChoices.map((choice) => JSON.stringify(choice)).join(', ')}`);\n    }\n    if (argument.defaultValue !== undefined) {\n      extraInfo.push(`default: ${argument.defaultValueDescription || JSON.stringify(argument.defaultValue)}`);\n    }\n    if (extraInfo.length > 0) {\n      const extraDescripton = `(${extraInfo.join(', ')})`;\n      if (argument.description) {\n        return `${argument.description} ${extraDescripton}`;\n      }\n      return extraDescripton;\n    }\n    return argument.description;\n  }\n\n  /**\n   * Generate the built-in help text.\n   *\n   * @param {Command} cmd\n   * @param {Help} helper\n   * @returns {string}\n   */\n\n  formatHelp(cmd, helper) {\n    const termWidth = helper.padWidth(cmd, helper);\n    const helpWidth = helper.helpWidth || 80;\n    const itemIndentWidth = 2;\n    const itemSeparatorWidth = 2; // between term and description\n    function formatItem(term, description) {\n      if (description) {\n        const fullText = `${term.padEnd(termWidth + itemSeparatorWidth)}${description}`;\n        return helper.wrap(fullText, helpWidth - itemIndentWidth, termWidth + itemSeparatorWidth);\n      }\n      return term;\n    }\n    function formatList(textArray) {\n      return textArray.join('\\n').replace(/^/gm, ' '.repeat(itemIndentWidth));\n    }\n\n    // Usage\n    let output = [`Usage: ${helper.commandUsage(cmd)}`, ''];\n\n    // Description\n    const commandDescription = helper.commandDescription(cmd);\n    if (commandDescription.length > 0) {\n      output = output.concat([helper.wrap(commandDescription, helpWidth, 0), '']);\n    }\n\n    // Arguments\n    const argumentList = helper.visibleArguments(cmd).map((argument) => {\n      return formatItem(helper.argumentTerm(argument), helper.argumentDescription(argument));\n    });\n    if (argumentList.length > 0) {\n      output = output.concat(['Arguments:', formatList(argumentList), '']);\n    }\n\n    // Options\n    const optionList = helper.visibleOptions(cmd).map((option) => {\n      return formatItem(helper.optionTerm(option), helper.optionDescription(option));\n    });\n    if (optionList.length > 0) {\n      output = output.concat(['Options:', formatList(optionList), '']);\n    }\n\n    if (this.showGlobalOptions) {\n      const globalOptionList = helper.visibleGlobalOptions(cmd).map((option) => {\n        return formatItem(helper.optionTerm(option), helper.optionDescription(option));\n      });\n      if (globalOptionList.length > 0) {\n        output = output.concat(['Global Options:', formatList(globalOptionList), '']);\n      }\n    }\n\n    // Commands\n    const commandList = helper.visibleCommands(cmd).map((cmd) => {\n      return formatItem(helper.subcommandTerm(cmd), helper.subcommandDescription(cmd));\n    });\n    if (commandList.length > 0) {\n      output = output.concat(['Commands:', formatList(commandList), '']);\n    }\n\n    return output.join('\\n');\n  }\n\n  /**\n   * Calculate the pad width from the maximum term length.\n   *\n   * @param {Command} cmd\n   * @param {Help} helper\n   * @returns {number}\n   */\n\n  padWidth(cmd, helper) {\n    return Math.max(\n      helper.longestOptionTermLength(cmd, helper),\n      helper.longestGlobalOptionTermLength(cmd, helper),\n      helper.longestSubcommandTermLength(cmd, helper),\n      helper.longestArgumentTermLength(cmd, helper)\n    );\n  }\n\n  /**\n   * Wrap the given string to width characters per line, with lines after the first indented.\n   * Do not wrap if insufficient room for wrapping (minColumnWidth), or string is manually formatted.\n   *\n   * @param {string} str\n   * @param {number} width\n   * @param {number} indent\n   * @param {number} [minColumnWidth=40]\n   * @return {string}\n   *\n   */\n\n  wrap(str, width, indent, minColumnWidth = 40) {\n    // Full \\s characters, minus the linefeeds.\n    const indents = ' \\\\f\\\\t\\\\v\\u00a0\\u1680\\u2000-\\u200a\\u202f\\u205f\\u3000\\ufeff';\n    // Detect manually wrapped and indented strings by searching for line break followed by spaces.\n    const manualIndent = new RegExp(`[\\\\n][${indents}]+`);\n    if (str.match(manualIndent)) return str;\n    // Do not wrap if not enough room for a wrapped column of text (as could end up with a word per line).\n    const columnWidth = width - indent;\n    if (columnWidth < minColumnWidth) return str;\n\n    const leadingStr = str.slice(0, indent);\n    const columnText = str.slice(indent).replace('\\r\\n', '\\n');\n    const indentString = ' '.repeat(indent);\n    const zeroWidthSpace = '\\u200B';\n    const breaks = `\\\\s${zeroWidthSpace}`;\n    // Match line end (so empty lines don't collapse),\n    // or as much text as will fit in column, or excess text up to first break.\n    const regex = new RegExp(`\\n|.{1,${columnWidth - 1}}([${breaks}]|$)|[^${breaks}]+?([${breaks}]|$)`, 'g');\n    const lines = columnText.match(regex) || [];\n    return leadingStr + lines.map((line, i) => {\n      if (line === '\\n') return ''; // preserve empty lines\n      return ((i > 0) ? indentString : '') + line.trimEnd();\n    }).join('\\n');\n  }\n}\n\nexports.Help = Help;\n", "const { InvalidArgumentError } = require('./error.js');\n\nclass Option {\n  /**\n   * Initialize a new `Option` with the given `flags` and `description`.\n   *\n   * @param {string} flags\n   * @param {string} [description]\n   */\n\n  constructor(flags, description) {\n    this.flags = flags;\n    this.description = description || '';\n\n    this.required = flags.includes('<'); // A value must be supplied when the option is specified.\n    this.optional = flags.includes('['); // A value is optional when the option is specified.\n    // variadic test ignores <value,...> et al which might be used to describe custom splitting of single argument\n    this.variadic = /\\w\\.\\.\\.[>\\]]$/.test(flags); // The option can take multiple values.\n    this.mandatory = false; // The option must have a value after parsing, which usually means it must be specified on command line.\n    const optionFlags = splitOptionFlags(flags);\n    this.short = optionFlags.shortFlag;\n    this.long = optionFlags.longFlag;\n    this.negate = false;\n    if (this.long) {\n      this.negate = this.long.startsWith('--no-');\n    }\n    this.defaultValue = undefined;\n    this.defaultValueDescription = undefined;\n    this.presetArg = undefined;\n    this.envVar = undefined;\n    this.parseArg = undefined;\n    this.hidden = false;\n    this.argChoices = undefined;\n    this.conflictsWith = [];\n    this.implied = undefined;\n  }\n\n  /**\n   * Set the default value, and optionally supply the description to be displayed in the help.\n   *\n   * @param {*} value\n   * @param {string} [description]\n   * @return {Option}\n   */\n\n  default(value, description) {\n    this.defaultValue = value;\n    this.defaultValueDescription = description;\n    return this;\n  }\n\n  /**\n   * Preset to use when option used without option-argument, especially optional but also boolean and negated.\n   * The custom processing (parseArg) is called.\n   *\n   * @example\n   * new Option('--color').default('GREYSCALE').preset('RGB');\n   * new Option('--donate [amount]').preset('20').argParser(parseFloat);\n   *\n   * @param {*} arg\n   * @return {Option}\n   */\n\n  preset(arg) {\n    this.presetArg = arg;\n    return this;\n  }\n\n  /**\n   * Add option name(s) that conflict with this option.\n   * An error will be displayed if conflicting options are found during parsing.\n   *\n   * @example\n   * new Option('--rgb').conflicts('cmyk');\n   * new Option('--js').conflicts(['ts', 'jsx']);\n   *\n   * @param {(string | string[])} names\n   * @return {Option}\n   */\n\n  conflicts(names) {\n    this.conflictsWith = this.conflictsWith.concat(names);\n    return this;\n  }\n\n  /**\n   * Specify implied option values for when this option is set and the implied options are not.\n   *\n   * The custom processing (parseArg) is not called on the implied values.\n   *\n   * @example\n   * program\n   *   .addOption(new Option('--log', 'write logging information to file'))\n   *   .addOption(new Option('--trace', 'log extra details').implies({ log: 'trace.txt' }));\n   *\n   * @param {Object} impliedOptionValues\n   * @return {Option}\n   */\n  implies(impliedOptionValues) {\n    let newImplied = impliedOptionValues;\n    if (typeof impliedOptionValues === 'string') {\n      // string is not documented, but easy mistake and we can do what user probably intended.\n      newImplied = { [impliedOptionValues]: true };\n    }\n    this.implied = Object.assign(this.implied || {}, newImplied);\n    return this;\n  }\n\n  /**\n   * Set environment variable to check for option value.\n   *\n   * An environment variable is only used if when processed the current option value is\n   * undefined, or the source of the current value is 'default' or 'config' or 'env'.\n   *\n   * @param {string} name\n   * @return {Option}\n   */\n\n  env(name) {\n    this.envVar = name;\n    return this;\n  }\n\n  /**\n   * Set the custom handler for processing CLI option arguments into option values.\n   *\n   * @param {Function} [fn]\n   * @return {Option}\n   */\n\n  argParser(fn) {\n    this.parseArg = fn;\n    return this;\n  }\n\n  /**\n   * Whether the option is mandatory and must have a value after parsing.\n   *\n   * @param {boolean} [mandatory=true]\n   * @return {Option}\n   */\n\n  makeOptionMandatory(mandatory = true) {\n    this.mandatory = !!mandatory;\n    return this;\n  }\n\n  /**\n   * Hide option in help.\n   *\n   * @param {boolean} [hide=true]\n   * @return {Option}\n   */\n\n  hideHelp(hide = true) {\n    this.hidden = !!hide;\n    return this;\n  }\n\n  /**\n   * @package internal use only\n   */\n\n  _concatValue(value, previous) {\n    if (previous === this.defaultValue || !Array.isArray(previous)) {\n      return [value];\n    }\n\n    return previous.concat(value);\n  }\n\n  /**\n   * Only allow option value to be one of choices.\n   *\n   * @param {string[]} values\n   * @return {Option}\n   */\n\n  choices(values) {\n    this.argChoices = values.slice();\n    this.parseArg = (arg, previous) => {\n      if (!this.argChoices.includes(arg)) {\n        throw new InvalidArgumentError(`Allowed choices are ${this.argChoices.join(', ')}.`);\n      }\n      if (this.variadic) {\n        return this._concatValue(arg, previous);\n      }\n      return arg;\n    };\n    return this;\n  }\n\n  /**\n   * Return option name.\n   *\n   * @return {string}\n   */\n\n  name() {\n    if (this.long) {\n      return this.long.replace(/^--/, '');\n    }\n    return this.short.replace(/^-/, '');\n  }\n\n  /**\n   * Return option name, in a camelcase format that can be used\n   * as a object attribute key.\n   *\n   * @return {string}\n   */\n\n  attributeName() {\n    return camelcase(this.name().replace(/^no-/, ''));\n  }\n\n  /**\n   * Check if `arg` matches the short or long flag.\n   *\n   * @param {string} arg\n   * @return {boolean}\n   * @package internal use only\n   */\n\n  is(arg) {\n    return this.short === arg || this.long === arg;\n  }\n\n  /**\n   * Return whether a boolean option.\n   *\n   * Options are one of boolean, negated, required argument, or optional argument.\n   *\n   * @return {boolean}\n   * @package internal use only\n   */\n\n  isBoolean() {\n    return !this.required && !this.optional && !this.negate;\n  }\n}\n\n/**\n * This class is to make it easier to work with dual options, without changing the existing\n * implementation. We support separate dual options for separate positive and negative options,\n * like `--build` and `--no-build`, which share a single option value. This works nicely for some\n * use cases, but is tricky for others where we want separate behaviours despite\n * the single shared option value.\n */\nclass DualOptions {\n  /**\n   * @param {Option[]} options\n   */\n  constructor(options) {\n    this.positiveOptions = new Map();\n    this.negativeOptions = new Map();\n    this.dualOptions = new Set();\n    options.forEach(option => {\n      if (option.negate) {\n        this.negativeOptions.set(option.attributeName(), option);\n      } else {\n        this.positiveOptions.set(option.attributeName(), option);\n      }\n    });\n    this.negativeOptions.forEach((value, key) => {\n      if (this.positiveOptions.has(key)) {\n        this.dualOptions.add(key);\n      }\n    });\n  }\n\n  /**\n   * Did the value come from the option, and not from possible matching dual option?\n   *\n   * @param {*} value\n   * @param {Option} option\n   * @returns {boolean}\n   */\n  valueFromOption(value, option) {\n    const optionKey = option.attributeName();\n    if (!this.dualOptions.has(optionKey)) return true;\n\n    // Use the value to deduce if (probably) came from the option.\n    const preset = this.negativeOptions.get(optionKey).presetArg;\n    const negativeValue = (preset !== undefined) ? preset : false;\n    return option.negate === (negativeValue === value);\n  }\n}\n\n/**\n * Convert string from kebab-case to camelCase.\n *\n * @param {string} str\n * @return {string}\n * @private\n */\n\nfunction camelcase(str) {\n  return str.split('-').reduce((str, word) => {\n    return str + word[0].toUpperCase() + word.slice(1);\n  });\n}\n\n/**\n * Split the short and long flag out of something like '-m,--mixed <value>'\n *\n * @private\n */\n\nfunction splitOptionFlags(flags) {\n  let shortFlag;\n  let longFlag;\n  // Use original very loose parsing to maintain backwards compatibility for now,\n  // which allowed for example unintended `-sw, --short-word` [sic].\n  const flagParts = flags.split(/[ |,]+/);\n  if (flagParts.length > 1 && !/^[[<]/.test(flagParts[1])) shortFlag = flagParts.shift();\n  longFlag = flagParts.shift();\n  // Add support for lone short flag without significantly changing parsing!\n  if (!shortFlag && /^-[^-]$/.test(longFlag)) {\n    shortFlag = longFlag;\n    longFlag = undefined;\n  }\n  return { shortFlag, longFlag };\n}\n\nexports.Option = Option;\nexports.DualOptions = DualOptions;\n", "const maxDistance = 3;\n\nfunction editDistance(a, b) {\n  // https://en.wikipedia.org/wiki/Damerau–Levenshtein_distance\n  // Calculating optimal string alignment distance, no substring is edited more than once.\n  // (Simple implementation.)\n\n  // Quick early exit, return worst case.\n  if (Math.abs(a.length - b.length) > maxDistance) return Math.max(a.length, b.length);\n\n  // distance between prefix substrings of a and b\n  const d = [];\n\n  // pure deletions turn a into empty string\n  for (let i = 0; i <= a.length; i++) {\n    d[i] = [i];\n  }\n  // pure insertions turn empty string into b\n  for (let j = 0; j <= b.length; j++) {\n    d[0][j] = j;\n  }\n\n  // fill matrix\n  for (let j = 1; j <= b.length; j++) {\n    for (let i = 1; i <= a.length; i++) {\n      let cost = 1;\n      if (a[i - 1] === b[j - 1]) {\n        cost = 0;\n      } else {\n        cost = 1;\n      }\n      d[i][j] = Math.min(\n        d[i - 1][j] + 1, // deletion\n        d[i][j - 1] + 1, // insertion\n        d[i - 1][j - 1] + cost // substitution\n      );\n      // transposition\n      if (i > 1 && j > 1 && a[i - 1] === b[j - 2] && a[i - 2] === b[j - 1]) {\n        d[i][j] = Math.min(d[i][j], d[i - 2][j - 2] + 1);\n      }\n    }\n  }\n\n  return d[a.length][b.length];\n}\n\n/**\n * Find close matches, restricted to same number of edits.\n *\n * @param {string} word\n * @param {string[]} candidates\n * @returns {string}\n */\n\nfunction suggestSimilar(word, candidates) {\n  if (!candidates || candidates.length === 0) return '';\n  // remove possible duplicates\n  candidates = Array.from(new Set(candidates));\n\n  const searchingOptions = word.startsWith('--');\n  if (searchingOptions) {\n    word = word.slice(2);\n    candidates = candidates.map(candidate => candidate.slice(2));\n  }\n\n  let similar = [];\n  let bestDistance = maxDistance;\n  const minSimilarity = 0.4;\n  candidates.forEach((candidate) => {\n    if (candidate.length <= 1) return; // no one character guesses\n\n    const distance = editDistance(word, candidate);\n    const length = Math.max(word.length, candidate.length);\n    const similarity = (length - distance) / length;\n    if (similarity > minSimilarity) {\n      if (distance < bestDistance) {\n        // better edit distance, throw away previous worse matches\n        bestDistance = distance;\n        similar = [candidate];\n      } else if (distance === bestDistance) {\n        similar.push(candidate);\n      }\n    }\n  });\n\n  similar.sort((a, b) => a.localeCompare(b));\n  if (searchingOptions) {\n    similar = similar.map(candidate => `--${candidate}`);\n  }\n\n  if (similar.length > 1) {\n    return `\\n(Did you mean one of ${similar.join(', ')}?)`;\n  }\n  if (similar.length === 1) {\n    return `\\n(Did you mean ${similar[0]}?)`;\n  }\n  return '';\n}\n\nexports.suggestSimilar = suggestSimilar;\n", "const EventEmitter = require('events').EventEmitter;\nconst childProcess = require('child_process');\nconst path = require('path');\nconst fs = require('fs');\nconst process = require('process');\n\nconst { Argument, humanReadableArgName } = require('./argument.js');\nconst { CommanderError } = require('./error.js');\nconst { Help } = require('./help.js');\nconst { Option, DualOptions } = require('./option.js');\nconst { suggestSimilar } = require('./suggestSimilar');\n\nclass Command extends EventEmitter {\n  /**\n   * Initialize a new `Command`.\n   *\n   * @param {string} [name]\n   */\n\n  constructor(name) {\n    super();\n    /** @type {Command[]} */\n    this.commands = [];\n    /** @type {Option[]} */\n    this.options = [];\n    this.parent = null;\n    this._allowUnknownOption = false;\n    this._allowExcessArguments = true;\n    /** @type {Argument[]} */\n    this.registeredArguments = [];\n    this._args = this.registeredArguments; // deprecated old name\n    /** @type {string[]} */\n    this.args = []; // cli args with options removed\n    this.rawArgs = [];\n    this.processedArgs = []; // like .args but after custom processing and collecting variadic\n    this._scriptPath = null;\n    this._name = name || '';\n    this._optionValues = {};\n    this._optionValueSources = {}; // default, env, cli etc\n    this._storeOptionsAsProperties = false;\n    this._actionHandler = null;\n    this._executableHandler = false;\n    this._executableFile = null; // custom name for executable\n    this._executableDir = null; // custom search directory for subcommands\n    this._defaultCommandName = null;\n    this._exitCallback = null;\n    this._aliases = [];\n    this._combineFlagAndOptionalValue = true;\n    this._description = '';\n    this._summary = '';\n    this._argsDescription = undefined; // legacy\n    this._enablePositionalOptions = false;\n    this._passThroughOptions = false;\n    this._lifeCycleHooks = {}; // a hash of arrays\n    /** @type {(boolean | string)} */\n    this._showHelpAfterError = false;\n    this._showSuggestionAfterError = true;\n\n    // see .configureOutput() for docs\n    this._outputConfiguration = {\n      writeOut: (str) => process.stdout.write(str),\n      writeErr: (str) => process.stderr.write(str),\n      getOutHelpWidth: () => process.stdout.isTTY ? process.stdout.columns : undefined,\n      getErrHelpWidth: () => process.stderr.isTTY ? process.stderr.columns : undefined,\n      outputError: (str, write) => write(str)\n    };\n\n    this._hidden = false;\n    /** @type {(Option | null | undefined)} */\n    this._helpOption = undefined; // Lazy created on demand. May be null if help option is disabled.\n    this._addImplicitHelpCommand = undefined; // undecided whether true or false yet, not inherited\n    /** @type {Command} */\n    this._helpCommand = undefined; // lazy initialised, inherited\n    this._helpConfiguration = {};\n  }\n\n  /**\n   * Copy settings that are useful to have in common across root command and subcommands.\n   *\n   * (Used internally when adding a command using `.command()` so subcommands inherit parent settings.)\n   *\n   * @param {Command} sourceCommand\n   * @return {Command} `this` command for chaining\n   */\n  copyInheritedSettings(sourceCommand) {\n    this._outputConfiguration = sourceCommand._outputConfiguration;\n    this._helpOption = sourceCommand._helpOption;\n    this._helpCommand = sourceCommand._helpCommand;\n    this._helpConfiguration = sourceCommand._helpConfiguration;\n    this._exitCallback = sourceCommand._exitCallback;\n    this._storeOptionsAsProperties = sourceCommand._storeOptionsAsProperties;\n    this._combineFlagAndOptionalValue = sourceCommand._combineFlagAndOptionalValue;\n    this._allowExcessArguments = sourceCommand._allowExcessArguments;\n    this._enablePositionalOptions = sourceCommand._enablePositionalOptions;\n    this._showHelpAfterError = sourceCommand._showHelpAfterError;\n    this._showSuggestionAfterError = sourceCommand._showSuggestionAfterError;\n\n    return this;\n  }\n\n  /**\n   * @returns {Command[]}\n   * @private\n   */\n\n  _getCommandAndAncestors() {\n    const result = [];\n    for (let command = this; command; command = command.parent) {\n      result.push(command);\n    }\n    return result;\n  }\n\n  /**\n   * Define a command.\n   *\n   * There are two styles of command: pay attention to where to put the description.\n   *\n   * @example\n   * // Command implemented using action handler (description is supplied separately to `.command`)\n   * program\n   *   .command('clone <source> [destination]')\n   *   .description('clone a repository into a newly created directory')\n   *   .action((source, destination) => {\n   *     console.log('clone command called');\n   *   });\n   *\n   * // Command implemented using separate executable file (description is second parameter to `.command`)\n   * program\n   *   .command('start <service>', 'start named service')\n   *   .command('stop [service]', 'stop named service, or all if no name supplied');\n   *\n   * @param {string} nameAndArgs - command name and arguments, args are `<required>` or `[optional]` and last may also be `variadic...`\n   * @param {(Object|string)} [actionOptsOrExecDesc] - configuration options (for action), or description (for executable)\n   * @param {Object} [execOpts] - configuration options (for executable)\n   * @return {Command} returns new command for action handler, or `this` for executable command\n   */\n\n  command(nameAndArgs, actionOptsOrExecDesc, execOpts) {\n    let desc = actionOptsOrExecDesc;\n    let opts = execOpts;\n    if (typeof desc === 'object' && desc !== null) {\n      opts = desc;\n      desc = null;\n    }\n    opts = opts || {};\n    const [, name, args] = nameAndArgs.match(/([^ ]+) *(.*)/);\n\n    const cmd = this.createCommand(name);\n    if (desc) {\n      cmd.description(desc);\n      cmd._executableHandler = true;\n    }\n    if (opts.isDefault) this._defaultCommandName = cmd._name;\n    cmd._hidden = !!(opts.noHelp || opts.hidden); // noHelp is deprecated old name for hidden\n    cmd._executableFile = opts.executableFile || null; // Custom name for executable file, set missing to null to match constructor\n    if (args) cmd.arguments(args);\n    this._registerCommand(cmd);\n    cmd.parent = this;\n    cmd.copyInheritedSettings(this);\n\n    if (desc) return this;\n    return cmd;\n  }\n\n  /**\n   * Factory routine to create a new unattached command.\n   *\n   * See .command() for creating an attached subcommand, which uses this routine to\n   * create the command. You can override createCommand to customise subcommands.\n   *\n   * @param {string} [name]\n   * @return {Command} new command\n   */\n\n  createCommand(name) {\n    return new Command(name);\n  }\n\n  /**\n   * You can customise the help with a subclass of Help by overriding createHelp,\n   * or by overriding Help properties using configureHelp().\n   *\n   * @return {Help}\n   */\n\n  createHelp() {\n    return Object.assign(new Help(), this.configureHelp());\n  }\n\n  /**\n   * You can customise the help by overriding Help properties using configureHelp(),\n   * or with a subclass of Help by overriding createHelp().\n   *\n   * @param {Object} [configuration] - configuration options\n   * @return {(Command|Object)} `this` command for chaining, or stored configuration\n   */\n\n  configureHelp(configuration) {\n    if (configuration === undefined) return this._helpConfiguration;\n\n    this._helpConfiguration = configuration;\n    return this;\n  }\n\n  /**\n   * The default output goes to stdout and stderr. You can customise this for special\n   * applications. You can also customise the display of errors by overriding outputError.\n   *\n   * The configuration properties are all functions:\n   *\n   *     // functions to change where being written, stdout and stderr\n   *     writeOut(str)\n   *     writeErr(str)\n   *     // matching functions to specify width for wrapping help\n   *     getOutHelpWidth()\n   *     getErrHelpWidth()\n   *     // functions based on what is being written out\n   *     outputError(str, write) // used for displaying errors, and not used for displaying help\n   *\n   * @param {Object} [configuration] - configuration options\n   * @return {(Command|Object)} `this` command for chaining, or stored configuration\n   */\n\n  configureOutput(configuration) {\n    if (configuration === undefined) return this._outputConfiguration;\n\n    Object.assign(this._outputConfiguration, configuration);\n    return this;\n  }\n\n  /**\n   * Display the help or a custom message after an error occurs.\n   *\n   * @param {(boolean|string)} [displayHelp]\n   * @return {Command} `this` command for chaining\n   */\n  showHelpAfterError(displayHelp = true) {\n    if (typeof displayHelp !== 'string') displayHelp = !!displayHelp;\n    this._showHelpAfterError = displayHelp;\n    return this;\n  }\n\n  /**\n   * Display suggestion of similar commands for unknown commands, or options for unknown options.\n   *\n   * @param {boolean} [displaySuggestion]\n   * @return {Command} `this` command for chaining\n   */\n  showSuggestionAfterError(displaySuggestion = true) {\n    this._showSuggestionAfterError = !!displaySuggestion;\n    return this;\n  }\n\n  /**\n   * Add a prepared subcommand.\n   *\n   * See .command() for creating an attached subcommand which inherits settings from its parent.\n   *\n   * @param {Command} cmd - new subcommand\n   * @param {Object} [opts] - configuration options\n   * @return {Command} `this` command for chaining\n   */\n\n  addCommand(cmd, opts) {\n    if (!cmd._name) {\n      throw new Error(`Command passed to .addCommand() must have a name\n- specify the name in Command constructor or using .name()`);\n    }\n\n    opts = opts || {};\n    if (opts.isDefault) this._defaultCommandName = cmd._name;\n    if (opts.noHelp || opts.hidden) cmd._hidden = true; // modifying passed command due to existing implementation\n\n    this._registerCommand(cmd);\n    cmd.parent = this;\n    cmd._checkForBrokenPassThrough();\n\n    return this;\n  }\n\n  /**\n   * Factory routine to create a new unattached argument.\n   *\n   * See .argument() for creating an attached argument, which uses this routine to\n   * create the argument. You can override createArgument to return a custom argument.\n   *\n   * @param {string} name\n   * @param {string} [description]\n   * @return {Argument} new argument\n   */\n\n  createArgument(name, description) {\n    return new Argument(name, description);\n  }\n\n  /**\n   * Define argument syntax for command.\n   *\n   * The default is that the argument is required, and you can explicitly\n   * indicate this with <> around the name. Put [] around the name for an optional argument.\n   *\n   * @example\n   * program.argument('<input-file>');\n   * program.argument('[output-file]');\n   *\n   * @param {string} name\n   * @param {string} [description]\n   * @param {(Function|*)} [fn] - custom argument processing function\n   * @param {*} [defaultValue]\n   * @return {Command} `this` command for chaining\n   */\n  argument(name, description, fn, defaultValue) {\n    const argument = this.createArgument(name, description);\n    if (typeof fn === 'function') {\n      argument.default(defaultValue).argParser(fn);\n    } else {\n      argument.default(fn);\n    }\n    this.addArgument(argument);\n    return this;\n  }\n\n  /**\n   * Define argument syntax for command, adding multiple at once (without descriptions).\n   *\n   * See also .argument().\n   *\n   * @example\n   * program.arguments('<cmd> [env]');\n   *\n   * @param {string} names\n   * @return {Command} `this` command for chaining\n   */\n\n  arguments(names) {\n    names.trim().split(/ +/).forEach((detail) => {\n      this.argument(detail);\n    });\n    return this;\n  }\n\n  /**\n   * Define argument syntax for command, adding a prepared argument.\n   *\n   * @param {Argument} argument\n   * @return {Command} `this` command for chaining\n   */\n  addArgument(argument) {\n    const previousArgument = this.registeredArguments.slice(-1)[0];\n    if (previousArgument && previousArgument.variadic) {\n      throw new Error(`only the last argument can be variadic '${previousArgument.name()}'`);\n    }\n    if (argument.required && argument.defaultValue !== undefined && argument.parseArg === undefined) {\n      throw new Error(`a default value for a required argument is never used: '${argument.name()}'`);\n    }\n    this.registeredArguments.push(argument);\n    return this;\n  }\n\n  /**\n   * Customise or override default help command. By default a help command is automatically added if your command has subcommands.\n   *\n   *    program.helpCommand('help [cmd]');\n   *    program.helpCommand('help [cmd]', 'show help');\n   *    program.helpCommand(false); // suppress default help command\n   *    program.helpCommand(true); // add help command even if no subcommands\n   *\n   * @param {string|boolean} enableOrNameAndArgs - enable with custom name and/or arguments, or boolean to override whether added\n   * @param {string} [description] - custom description\n   * @return {Command} `this` command for chaining\n   */\n\n  helpCommand(enableOrNameAndArgs, description) {\n    if (typeof enableOrNameAndArgs === 'boolean') {\n      this._addImplicitHelpCommand = enableOrNameAndArgs;\n      return this;\n    }\n\n    enableOrNameAndArgs = enableOrNameAndArgs ?? 'help [command]';\n    const [, helpName, helpArgs] = enableOrNameAndArgs.match(/([^ ]+) *(.*)/);\n    const helpDescription = description ?? 'display help for command';\n\n    const helpCommand = this.createCommand(helpName);\n    helpCommand.helpOption(false);\n    if (helpArgs) helpCommand.arguments(helpArgs);\n    if (helpDescription) helpCommand.description(helpDescription);\n\n    this._addImplicitHelpCommand = true;\n    this._helpCommand = helpCommand;\n\n    return this;\n  }\n\n  /**\n   * Add prepared custom help command.\n   *\n   * @param {(Command|string|boolean)} helpCommand - custom help command, or deprecated enableOrNameAndArgs as for `.helpCommand()`\n   * @param {string} [deprecatedDescription] - deprecated custom description used with custom name only\n   * @return {Command} `this` command for chaining\n   */\n  addHelpCommand(helpCommand, deprecatedDescription) {\n    // If not passed an object, call through to helpCommand for backwards compatibility,\n    // as addHelpCommand was originally used like helpCommand is now.\n    if (typeof helpCommand !== 'object') {\n      this.helpCommand(helpCommand, deprecatedDescription);\n      return this;\n    }\n\n    this._addImplicitHelpCommand = true;\n    this._helpCommand = helpCommand;\n    return this;\n  }\n\n  /**\n   * Lazy create help command.\n   *\n   * @return {(Command|null)}\n   * @package\n   */\n  _getHelpCommand() {\n    const hasImplicitHelpCommand = this._addImplicitHelpCommand ??\n      (this.commands.length && !this._actionHandler && !this._findCommand('help'));\n\n    if (hasImplicitHelpCommand) {\n      if (this._helpCommand === undefined) {\n        this.helpCommand(undefined, undefined); // use default name and description\n      }\n      return this._helpCommand;\n    }\n    return null;\n  }\n\n  /**\n   * Add hook for life cycle event.\n   *\n   * @param {string} event\n   * @param {Function} listener\n   * @return {Command} `this` command for chaining\n   */\n\n  hook(event, listener) {\n    const allowedValues = ['preSubcommand', 'preAction', 'postAction'];\n    if (!allowedValues.includes(event)) {\n      throw new Error(`Unexpected value for event passed to hook : '${event}'.\nExpecting one of '${allowedValues.join(\"', '\")}'`);\n    }\n    if (this._lifeCycleHooks[event]) {\n      this._lifeCycleHooks[event].push(listener);\n    } else {\n      this._lifeCycleHooks[event] = [listener];\n    }\n    return this;\n  }\n\n  /**\n   * Register callback to use as replacement for calling process.exit.\n   *\n   * @param {Function} [fn] optional callback which will be passed a CommanderError, defaults to throwing\n   * @return {Command} `this` command for chaining\n   */\n\n  exitOverride(fn) {\n    if (fn) {\n      this._exitCallback = fn;\n    } else {\n      this._exitCallback = (err) => {\n        if (err.code !== 'commander.executeSubCommandAsync') {\n          throw err;\n        } else {\n          // Async callback from spawn events, not useful to throw.\n        }\n      };\n    }\n    return this;\n  }\n\n  /**\n   * Call process.exit, and _exitCallback if defined.\n   *\n   * @param {number} exitCode exit code for using with process.exit\n   * @param {string} code an id string representing the error\n   * @param {string} message human-readable description of the error\n   * @return never\n   * @private\n   */\n\n  _exit(exitCode, code, message) {\n    if (this._exitCallback) {\n      this._exitCallback(new CommanderError(exitCode, code, message));\n      // Expecting this line is not reached.\n    }\n    process.exit(exitCode);\n  }\n\n  /**\n   * Register callback `fn` for the command.\n   *\n   * @example\n   * program\n   *   .command('serve')\n   *   .description('start service')\n   *   .action(function() {\n   *      // do work here\n   *   });\n   *\n   * @param {Function} fn\n   * @return {Command} `this` command for chaining\n   */\n\n  action(fn) {\n    const listener = (args) => {\n      // The .action callback takes an extra parameter which is the command or options.\n      const expectedArgsCount = this.registeredArguments.length;\n      const actionArgs = args.slice(0, expectedArgsCount);\n      if (this._storeOptionsAsProperties) {\n        actionArgs[expectedArgsCount] = this; // backwards compatible \"options\"\n      } else {\n        actionArgs[expectedArgsCount] = this.opts();\n      }\n      actionArgs.push(this);\n\n      return fn.apply(this, actionArgs);\n    };\n    this._actionHandler = listener;\n    return this;\n  }\n\n  /**\n   * Factory routine to create a new unattached option.\n   *\n   * See .option() for creating an attached option, which uses this routine to\n   * create the option. You can override createOption to return a custom option.\n   *\n   * @param {string} flags\n   * @param {string} [description]\n   * @return {Option} new option\n   */\n\n  createOption(flags, description) {\n    return new Option(flags, description);\n  }\n\n  /**\n   * Wrap parseArgs to catch 'commander.invalidArgument'.\n   *\n   * @param {(Option | Argument)} target\n   * @param {string} value\n   * @param {*} previous\n   * @param {string} invalidArgumentMessage\n   * @private\n   */\n\n  _callParseArg(target, value, previous, invalidArgumentMessage) {\n    try {\n      return target.parseArg(value, previous);\n    } catch (err) {\n      if (err.code === 'commander.invalidArgument') {\n        const message = `${invalidArgumentMessage} ${err.message}`;\n        this.error(message, { exitCode: err.exitCode, code: err.code });\n      }\n      throw err;\n    }\n  }\n\n  /**\n   * Check for option flag conflicts.\n   * Register option if no conflicts found, or throw on conflict.\n   *\n   * @param {Option} option\n   * @api private\n   */\n\n  _registerOption(option) {\n    const matchingOption = (option.short && this._findOption(option.short)) ||\n      (option.long && this._findOption(option.long));\n    if (matchingOption) {\n      const matchingFlag = (option.long && this._findOption(option.long)) ? option.long : option.short;\n      throw new Error(`Cannot add option '${option.flags}'${this._name && ` to command '${this._name}'`} due to conflicting flag '${matchingFlag}'\n-  already used by option '${matchingOption.flags}'`);\n    }\n\n    this.options.push(option);\n  }\n\n  /**\n   * Check for command name and alias conflicts with existing commands.\n   * Register command if no conflicts found, or throw on conflict.\n   *\n   * @param {Command} command\n   * @api private\n   */\n\n  _registerCommand(command) {\n    const knownBy = (cmd) => {\n      return [cmd.name()].concat(cmd.aliases());\n    };\n\n    const alreadyUsed = knownBy(command).find((name) => this._findCommand(name));\n    if (alreadyUsed) {\n      const existingCmd = knownBy(this._findCommand(alreadyUsed)).join('|');\n      const newCmd = knownBy(command).join('|');\n      throw new Error(`cannot add command '${newCmd}' as already have command '${existingCmd}'`);\n    }\n\n    this.commands.push(command);\n  }\n\n  /**\n   * Add an option.\n   *\n   * @param {Option} option\n   * @return {Command} `this` command for chaining\n   */\n  addOption(option) {\n    this._registerOption(option);\n\n    const oname = option.name();\n    const name = option.attributeName();\n\n    // store default value\n    if (option.negate) {\n      // --no-foo is special and defaults foo to true, unless a --foo option is already defined\n      const positiveLongFlag = option.long.replace(/^--no-/, '--');\n      if (!this._findOption(positiveLongFlag)) {\n        this.setOptionValueWithSource(name, option.defaultValue === undefined ? true : option.defaultValue, 'default');\n      }\n    } else if (option.defaultValue !== undefined) {\n      this.setOptionValueWithSource(name, option.defaultValue, 'default');\n    }\n\n    // handler for cli and env supplied values\n    const handleOptionValue = (val, invalidValueMessage, valueSource) => {\n      // val is null for optional option used without an optional-argument.\n      // val is undefined for boolean and negated option.\n      if (val == null && option.presetArg !== undefined) {\n        val = option.presetArg;\n      }\n\n      // custom processing\n      const oldValue = this.getOptionValue(name);\n      if (val !== null && option.parseArg) {\n        val = this._callParseArg(option, val, oldValue, invalidValueMessage);\n      } else if (val !== null && option.variadic) {\n        val = option._concatValue(val, oldValue);\n      }\n\n      // Fill-in appropriate missing values. Long winded but easy to follow.\n      if (val == null) {\n        if (option.negate) {\n          val = false;\n        } else if (option.isBoolean() || option.optional) {\n          val = true;\n        } else {\n          val = ''; // not normal, parseArg might have failed or be a mock function for testing\n        }\n      }\n      this.setOptionValueWithSource(name, val, valueSource);\n    };\n\n    this.on('option:' + oname, (val) => {\n      const invalidValueMessage = `error: option '${option.flags}' argument '${val}' is invalid.`;\n      handleOptionValue(val, invalidValueMessage, 'cli');\n    });\n\n    if (option.envVar) {\n      this.on('optionEnv:' + oname, (val) => {\n        const invalidValueMessage = `error: option '${option.flags}' value '${val}' from env '${option.envVar}' is invalid.`;\n        handleOptionValue(val, invalidValueMessage, 'env');\n      });\n    }\n\n    return this;\n  }\n\n  /**\n   * Internal implementation shared by .option() and .requiredOption()\n   *\n   * @private\n   */\n  _optionEx(config, flags, description, fn, defaultValue) {\n    if (typeof flags === 'object' && flags instanceof Option) {\n      throw new Error('To add an Option object use addOption() instead of option() or requiredOption()');\n    }\n    const option = this.createOption(flags, description);\n    option.makeOptionMandatory(!!config.mandatory);\n    if (typeof fn === 'function') {\n      option.default(defaultValue).argParser(fn);\n    } else if (fn instanceof RegExp) {\n      // deprecated\n      const regex = fn;\n      fn = (val, def) => {\n        const m = regex.exec(val);\n        return m ? m[0] : def;\n      };\n      option.default(defaultValue).argParser(fn);\n    } else {\n      option.default(fn);\n    }\n\n    return this.addOption(option);\n  }\n\n  /**\n   * Define option with `flags`, `description`, and optional argument parsing function or `defaultValue` or both.\n   *\n   * The `flags` string contains the short and/or long flags, separated by comma, a pipe or space. A required\n   * option-argument is indicated by `<>` and an optional option-argument by `[]`.\n   *\n   * See the README for more details, and see also addOption() and requiredOption().\n   *\n   * @example\n   * program\n   *     .option('-p, --pepper', 'add pepper')\n   *     .option('-p, --pizza-type <TYPE>', 'type of pizza') // required option-argument\n   *     .option('-c, --cheese [CHEESE]', 'add extra cheese', 'mozzarella') // optional option-argument with default\n   *     .option('-t, --tip <VALUE>', 'add tip to purchase cost', parseFloat) // custom parse function\n   *\n   * @param {string} flags\n   * @param {string} [description]\n   * @param {(Function|*)} [parseArg] - custom option processing function or default value\n   * @param {*} [defaultValue]\n   * @return {Command} `this` command for chaining\n   */\n\n  option(flags, description, parseArg, defaultValue) {\n    return this._optionEx({}, flags, description, parseArg, defaultValue);\n  }\n\n  /**\n  * Add a required option which must have a value after parsing. This usually means\n  * the option must be specified on the command line. (Otherwise the same as .option().)\n  *\n  * The `flags` string contains the short and/or long flags, separated by comma, a pipe or space.\n  *\n  * @param {string} flags\n  * @param {string} [description]\n  * @param {(Function|*)} [parseArg] - custom option processing function or default value\n  * @param {*} [defaultValue]\n  * @return {Command} `this` command for chaining\n  */\n\n  requiredOption(flags, description, parseArg, defaultValue) {\n    return this._optionEx({ mandatory: true }, flags, description, parseArg, defaultValue);\n  }\n\n  /**\n   * Alter parsing of short flags with optional values.\n   *\n   * @example\n   * // for `.option('-f,--flag [value]'):\n   * program.combineFlagAndOptionalValue(true);  // `-f80` is treated like `--flag=80`, this is the default behaviour\n   * program.combineFlagAndOptionalValue(false) // `-fb` is treated like `-f -b`\n   *\n   * @param {boolean} [combine=true] - if `true` or omitted, an optional value can be specified directly after the flag.\n   */\n  combineFlagAndOptionalValue(combine = true) {\n    this._combineFlagAndOptionalValue = !!combine;\n    return this;\n  }\n\n  /**\n   * Allow unknown options on the command line.\n   *\n   * @param {boolean} [allowUnknown=true] - if `true` or omitted, no error will be thrown\n   * for unknown options.\n   */\n  allowUnknownOption(allowUnknown = true) {\n    this._allowUnknownOption = !!allowUnknown;\n    return this;\n  }\n\n  /**\n   * Allow excess command-arguments on the command line. Pass false to make excess arguments an error.\n   *\n   * @param {boolean} [allowExcess=true] - if `true` or omitted, no error will be thrown\n   * for excess arguments.\n   */\n  allowExcessArguments(allowExcess = true) {\n    this._allowExcessArguments = !!allowExcess;\n    return this;\n  }\n\n  /**\n   * Enable positional options. Positional means global options are specified before subcommands which lets\n   * subcommands reuse the same option names, and also enables subcommands to turn on passThroughOptions.\n   * The default behaviour is non-positional and global options may appear anywhere on the command line.\n   *\n   * @param {boolean} [positional=true]\n   */\n  enablePositionalOptions(positional = true) {\n    this._enablePositionalOptions = !!positional;\n    return this;\n  }\n\n  /**\n   * Pass through options that come after command-arguments rather than treat them as command-options,\n   * so actual command-options come before command-arguments. Turning this on for a subcommand requires\n   * positional options to have been enabled on the program (parent commands).\n   * The default behaviour is non-positional and options may appear before or after command-arguments.\n   *\n   * @param {boolean} [passThrough=true]\n   * for unknown options.\n   */\n  passThroughOptions(passThrough = true) {\n    this._passThroughOptions = !!passThrough;\n    this._checkForBrokenPassThrough();\n    return this;\n  }\n\n  /**\n   * @private\n   */\n\n  _checkForBrokenPassThrough() {\n    if (this.parent && this._passThroughOptions && !this.parent._enablePositionalOptions) {\n      throw new Error(`passThroughOptions cannot be used for '${this._name}' without turning on enablePositionalOptions for parent command(s)`);\n    }\n  }\n\n  /**\n    * Whether to store option values as properties on command object,\n    * or store separately (specify false). In both cases the option values can be accessed using .opts().\n    *\n    * @param {boolean} [storeAsProperties=true]\n    * @return {Command} `this` command for chaining\n    */\n\n  storeOptionsAsProperties(storeAsProperties = true) {\n    if (this.options.length) {\n      throw new Error('call .storeOptionsAsProperties() before adding options');\n    }\n    if (Object.keys(this._optionValues).length) {\n      throw new Error('call .storeOptionsAsProperties() before setting option values');\n    }\n    this._storeOptionsAsProperties = !!storeAsProperties;\n    return this;\n  }\n\n  /**\n   * Retrieve option value.\n   *\n   * @param {string} key\n   * @return {Object} value\n   */\n\n  getOptionValue(key) {\n    if (this._storeOptionsAsProperties) {\n      return this[key];\n    }\n    return this._optionValues[key];\n  }\n\n  /**\n   * Store option value.\n   *\n   * @param {string} key\n   * @param {Object} value\n   * @return {Command} `this` command for chaining\n   */\n\n  setOptionValue(key, value) {\n    return this.setOptionValueWithSource(key, value, undefined);\n  }\n\n  /**\n    * Store option value and where the value came from.\n    *\n    * @param {string} key\n    * @param {Object} value\n    * @param {string} source - expected values are default/config/env/cli/implied\n    * @return {Command} `this` command for chaining\n    */\n\n  setOptionValueWithSource(key, value, source) {\n    if (this._storeOptionsAsProperties) {\n      this[key] = value;\n    } else {\n      this._optionValues[key] = value;\n    }\n    this._optionValueSources[key] = source;\n    return this;\n  }\n\n  /**\n    * Get source of option value.\n    * Expected values are default | config | env | cli | implied\n    *\n    * @param {string} key\n    * @return {string}\n    */\n\n  getOptionValueSource(key) {\n    return this._optionValueSources[key];\n  }\n\n  /**\n    * Get source of option value. See also .optsWithGlobals().\n    * Expected values are default | config | env | cli | implied\n    *\n    * @param {string} key\n    * @return {string}\n    */\n\n  getOptionValueSourceWithGlobals(key) {\n    // global overwrites local, like optsWithGlobals\n    let source;\n    this._getCommandAndAncestors().forEach((cmd) => {\n      if (cmd.getOptionValueSource(key) !== undefined) {\n        source = cmd.getOptionValueSource(key);\n      }\n    });\n    return source;\n  }\n\n  /**\n   * Get user arguments from implied or explicit arguments.\n   * Side-effects: set _scriptPath if args included script. Used for default program name, and subcommand searches.\n   *\n   * @private\n   */\n\n  _prepareUserArgs(argv, parseOptions) {\n    if (argv !== undefined && !Array.isArray(argv)) {\n      throw new Error('first parameter to parse must be array or undefined');\n    }\n    parseOptions = parseOptions || {};\n\n    // Default to using process.argv\n    if (argv === undefined) {\n      argv = process.argv;\n      // @ts-ignore: unknown property\n      if (process.versions && process.versions.electron) {\n        parseOptions.from = 'electron';\n      }\n    }\n    this.rawArgs = argv.slice();\n\n    // make it a little easier for callers by supporting various argv conventions\n    let userArgs;\n    switch (parseOptions.from) {\n      case undefined:\n      case 'node':\n        this._scriptPath = argv[1];\n        userArgs = argv.slice(2);\n        break;\n      case 'electron':\n        // @ts-ignore: unknown property\n        if (process.defaultApp) {\n          this._scriptPath = argv[1];\n          userArgs = argv.slice(2);\n        } else {\n          userArgs = argv.slice(1);\n        }\n        break;\n      case 'user':\n        userArgs = argv.slice(0);\n        break;\n      default:\n        throw new Error(`unexpected parse option { from: '${parseOptions.from}' }`);\n    }\n\n    // Find default name for program from arguments.\n    if (!this._name && this._scriptPath) this.nameFromFilename(this._scriptPath);\n    this._name = this._name || 'program';\n\n    return userArgs;\n  }\n\n  /**\n   * Parse `argv`, setting options and invoking commands when defined.\n   *\n   * The default expectation is that the arguments are from node and have the application as argv[0]\n   * and the script being run in argv[1], with user parameters after that.\n   *\n   * @example\n   * program.parse(process.argv);\n   * program.parse(); // implicitly use process.argv and auto-detect node vs electron conventions\n   * program.parse(my-args, { from: 'user' }); // just user supplied arguments, nothing special about argv[0]\n   *\n   * @param {string[]} [argv] - optional, defaults to process.argv\n   * @param {Object} [parseOptions] - optionally specify style of options with from: node/user/electron\n   * @param {string} [parseOptions.from] - where the args are from: 'node', 'user', 'electron'\n   * @return {Command} `this` command for chaining\n   */\n\n  parse(argv, parseOptions) {\n    const userArgs = this._prepareUserArgs(argv, parseOptions);\n    this._parseCommand([], userArgs);\n\n    return this;\n  }\n\n  /**\n   * Parse `argv`, setting options and invoking commands when defined.\n   *\n   * Use parseAsync instead of parse if any of your action handlers are async. Returns a Promise.\n   *\n   * The default expectation is that the arguments are from node and have the application as argv[0]\n   * and the script being run in argv[1], with user parameters after that.\n   *\n   * @example\n   * await program.parseAsync(process.argv);\n   * await program.parseAsync(); // implicitly use process.argv and auto-detect node vs electron conventions\n   * await program.parseAsync(my-args, { from: 'user' }); // just user supplied arguments, nothing special about argv[0]\n   *\n   * @param {string[]} [argv]\n   * @param {Object} [parseOptions]\n   * @param {string} parseOptions.from - where the args are from: 'node', 'user', 'electron'\n   * @return {Promise}\n   */\n\n  async parseAsync(argv, parseOptions) {\n    const userArgs = this._prepareUserArgs(argv, parseOptions);\n    await this._parseCommand([], userArgs);\n\n    return this;\n  }\n\n  /**\n   * Execute a sub-command executable.\n   *\n   * @private\n   */\n\n  _executeSubCommand(subcommand, args) {\n    args = args.slice();\n    let launchWithNode = false; // Use node for source targets so do not need to get permissions correct, and on Windows.\n    const sourceExt = ['.js', '.ts', '.tsx', '.mjs', '.cjs'];\n\n    function findFile(baseDir, baseName) {\n      // Look for specified file\n      const localBin = path.resolve(baseDir, baseName);\n      if (fs.existsSync(localBin)) return localBin;\n\n      // Stop looking if candidate already has an expected extension.\n      if (sourceExt.includes(path.extname(baseName))) return undefined;\n\n      // Try all the extensions.\n      const foundExt = sourceExt.find(ext => fs.existsSync(`${localBin}${ext}`));\n      if (foundExt) return `${localBin}${foundExt}`;\n\n      return undefined;\n    }\n\n    // Not checking for help first. Unlikely to have mandatory and executable, and can't robustly test for help flags in external command.\n    this._checkForMissingMandatoryOptions();\n    this._checkForConflictingOptions();\n\n    // executableFile and executableDir might be full path, or just a name\n    let executableFile = subcommand._executableFile || `${this._name}-${subcommand._name}`;\n    let executableDir = this._executableDir || '';\n    if (this._scriptPath) {\n      let resolvedScriptPath; // resolve possible symlink for installed npm binary\n      try {\n        resolvedScriptPath = fs.realpathSync(this._scriptPath);\n      } catch (err) {\n        resolvedScriptPath = this._scriptPath;\n      }\n      executableDir = path.resolve(path.dirname(resolvedScriptPath), executableDir);\n    }\n\n    // Look for a local file in preference to a command in PATH.\n    if (executableDir) {\n      let localFile = findFile(executableDir, executableFile);\n\n      // Legacy search using prefix of script name instead of command name\n      if (!localFile && !subcommand._executableFile && this._scriptPath) {\n        const legacyName = path.basename(this._scriptPath, path.extname(this._scriptPath));\n        if (legacyName !== this._name) {\n          localFile = findFile(executableDir, `${legacyName}-${subcommand._name}`);\n        }\n      }\n      executableFile = localFile || executableFile;\n    }\n\n    launchWithNode = sourceExt.includes(path.extname(executableFile));\n\n    let proc;\n    if (process.platform !== 'win32') {\n      if (launchWithNode) {\n        args.unshift(executableFile);\n        // add executable arguments to spawn\n        args = incrementNodeInspectorPort(process.execArgv).concat(args);\n\n        proc = childProcess.spawn(process.argv[0], args, { stdio: 'inherit' });\n      } else {\n        proc = childProcess.spawn(executableFile, args, { stdio: 'inherit' });\n      }\n    } else {\n      args.unshift(executableFile);\n      // add executable arguments to spawn\n      args = incrementNodeInspectorPort(process.execArgv).concat(args);\n      proc = childProcess.spawn(process.execPath, args, { stdio: 'inherit' });\n    }\n\n    if (!proc.killed) { // testing mainly to avoid leak warnings during unit tests with mocked spawn\n      const signals = ['SIGUSR1', 'SIGUSR2', 'SIGTERM', 'SIGINT', 'SIGHUP'];\n      signals.forEach((signal) => {\n        // @ts-ignore\n        process.on(signal, () => {\n          if (proc.killed === false && proc.exitCode === null) {\n            proc.kill(signal);\n          }\n        });\n      });\n    }\n\n    // By default terminate process when spawned process terminates.\n    const exitCallback = this._exitCallback;\n    proc.on('close', (code, _signal) => {\n      code = code ?? 1; // code is null if spawned process terminated due to a signal\n      if (!exitCallback) {\n        process.exit(code);\n      } else {\n        exitCallback(new CommanderError(code, 'commander.executeSubCommandAsync', '(close)'));\n      }\n    });\n    proc.on('error', (err) => {\n      // @ts-ignore\n      if (err.code === 'ENOENT') {\n        const executableDirMessage = executableDir\n          ? `searched for local subcommand relative to directory '${executableDir}'`\n          : 'no directory for search for local subcommand, use .executableDir() to supply a custom directory';\n        const executableMissing = `'${executableFile}' does not exist\n - if '${subcommand._name}' is not meant to be an executable command, remove description parameter from '.command()' and use '.description()' instead\n - if the default executable name is not suitable, use the executableFile option to supply a custom name or path\n - ${executableDirMessage}`;\n        throw new Error(executableMissing);\n      // @ts-ignore\n      } else if (err.code === 'EACCES') {\n        throw new Error(`'${executableFile}' not executable`);\n      }\n      if (!exitCallback) {\n        process.exit(1);\n      } else {\n        const wrappedError = new CommanderError(1, 'commander.executeSubCommandAsync', '(error)');\n        wrappedError.nestedError = err;\n        exitCallback(wrappedError);\n      }\n    });\n\n    // Store the reference to the child process\n    this.runningCommand = proc;\n  }\n\n  /**\n   * @private\n   */\n\n  _dispatchSubcommand(commandName, operands, unknown) {\n    const subCommand = this._findCommand(commandName);\n    if (!subCommand) this.help({ error: true });\n\n    let promiseChain;\n    promiseChain = this._chainOrCallSubCommandHook(promiseChain, subCommand, 'preSubcommand');\n    promiseChain = this._chainOrCall(promiseChain, () => {\n      if (subCommand._executableHandler) {\n        this._executeSubCommand(subCommand, operands.concat(unknown));\n      } else {\n        return subCommand._parseCommand(operands, unknown);\n      }\n    });\n    return promiseChain;\n  }\n\n  /**\n   * Invoke help directly if possible, or dispatch if necessary.\n   * e.g. help foo\n   *\n   * @private\n   */\n\n  _dispatchHelpCommand(subcommandName) {\n    if (!subcommandName) {\n      this.help();\n    }\n    const subCommand = this._findCommand(subcommandName);\n    if (subCommand && !subCommand._executableHandler) {\n      subCommand.help();\n    }\n\n    // Fallback to parsing the help flag to invoke the help.\n    return this._dispatchSubcommand(subcommandName, [], [\n      this._getHelpOption()?.long ?? this._getHelpOption()?.short ?? '--help'\n    ]);\n  }\n\n  /**\n   * Check this.args against expected this.registeredArguments.\n   *\n   * @private\n   */\n\n  _checkNumberOfArguments() {\n    // too few\n    this.registeredArguments.forEach((arg, i) => {\n      if (arg.required && this.args[i] == null) {\n        this.missingArgument(arg.name());\n      }\n    });\n    // too many\n    if (this.registeredArguments.length > 0 && this.registeredArguments[this.registeredArguments.length - 1].variadic) {\n      return;\n    }\n    if (this.args.length > this.registeredArguments.length) {\n      this._excessArguments(this.args);\n    }\n  }\n\n  /**\n   * Process this.args using this.registeredArguments and save as this.processedArgs!\n   *\n   * @private\n   */\n\n  _processArguments() {\n    const myParseArg = (argument, value, previous) => {\n      // Extra processing for nice error message on parsing failure.\n      let parsedValue = value;\n      if (value !== null && argument.parseArg) {\n        const invalidValueMessage = `error: command-argument value '${value}' is invalid for argument '${argument.name()}'.`;\n        parsedValue = this._callParseArg(argument, value, previous, invalidValueMessage);\n      }\n      return parsedValue;\n    };\n\n    this._checkNumberOfArguments();\n\n    const processedArgs = [];\n    this.registeredArguments.forEach((declaredArg, index) => {\n      let value = declaredArg.defaultValue;\n      if (declaredArg.variadic) {\n        // Collect together remaining arguments for passing together as an array.\n        if (index < this.args.length) {\n          value = this.args.slice(index);\n          if (declaredArg.parseArg) {\n            value = value.reduce((processed, v) => {\n              return myParseArg(declaredArg, v, processed);\n            }, declaredArg.defaultValue);\n          }\n        } else if (value === undefined) {\n          value = [];\n        }\n      } else if (index < this.args.length) {\n        value = this.args[index];\n        if (declaredArg.parseArg) {\n          value = myParseArg(declaredArg, value, declaredArg.defaultValue);\n        }\n      }\n      processedArgs[index] = value;\n    });\n    this.processedArgs = processedArgs;\n  }\n\n  /**\n   * Once we have a promise we chain, but call synchronously until then.\n   *\n   * @param {(Promise|undefined)} promise\n   * @param {Function} fn\n   * @return {(Promise|undefined)}\n   * @private\n   */\n\n  _chainOrCall(promise, fn) {\n    // thenable\n    if (promise && promise.then && typeof promise.then === 'function') {\n      // already have a promise, chain callback\n      return promise.then(() => fn());\n    }\n    // callback might return a promise\n    return fn();\n  }\n\n  /**\n   *\n   * @param {(Promise|undefined)} promise\n   * @param {string} event\n   * @return {(Promise|undefined)}\n   * @private\n   */\n\n  _chainOrCallHooks(promise, event) {\n    let result = promise;\n    const hooks = [];\n    this._getCommandAndAncestors()\n      .reverse()\n      .filter(cmd => cmd._lifeCycleHooks[event] !== undefined)\n      .forEach(hookedCommand => {\n        hookedCommand._lifeCycleHooks[event].forEach((callback) => {\n          hooks.push({ hookedCommand, callback });\n        });\n      });\n    if (event === 'postAction') {\n      hooks.reverse();\n    }\n\n    hooks.forEach((hookDetail) => {\n      result = this._chainOrCall(result, () => {\n        return hookDetail.callback(hookDetail.hookedCommand, this);\n      });\n    });\n    return result;\n  }\n\n  /**\n   *\n   * @param {(Promise|undefined)} promise\n   * @param {Command} subCommand\n   * @param {string} event\n   * @return {(Promise|undefined)}\n   * @private\n   */\n\n  _chainOrCallSubCommandHook(promise, subCommand, event) {\n    let result = promise;\n    if (this._lifeCycleHooks[event] !== undefined) {\n      this._lifeCycleHooks[event].forEach((hook) => {\n        result = this._chainOrCall(result, () => {\n          return hook(this, subCommand);\n        });\n      });\n    }\n    return result;\n  }\n\n  /**\n   * Process arguments in context of this command.\n   * Returns action result, in case it is a promise.\n   *\n   * @private\n   */\n\n  _parseCommand(operands, unknown) {\n    const parsed = this.parseOptions(unknown);\n    this._parseOptionsEnv(); // after cli, so parseArg not called on both cli and env\n    this._parseOptionsImplied();\n    operands = operands.concat(parsed.operands);\n    unknown = parsed.unknown;\n    this.args = operands.concat(unknown);\n\n    if (operands && this._findCommand(operands[0])) {\n      return this._dispatchSubcommand(operands[0], operands.slice(1), unknown);\n    }\n    if (this._getHelpCommand() && operands[0] === this._getHelpCommand().name()) {\n      return this._dispatchHelpCommand(operands[1]);\n    }\n    if (this._defaultCommandName) {\n      this._outputHelpIfRequested(unknown); // Run the help for default command from parent rather than passing to default command\n      return this._dispatchSubcommand(this._defaultCommandName, operands, unknown);\n    }\n    if (this.commands.length && this.args.length === 0 && !this._actionHandler && !this._defaultCommandName) {\n      // probably missing subcommand and no handler, user needs help (and exit)\n      this.help({ error: true });\n    }\n\n    this._outputHelpIfRequested(parsed.unknown);\n    this._checkForMissingMandatoryOptions();\n    this._checkForConflictingOptions();\n\n    // We do not always call this check to avoid masking a \"better\" error, like unknown command.\n    const checkForUnknownOptions = () => {\n      if (parsed.unknown.length > 0) {\n        this.unknownOption(parsed.unknown[0]);\n      }\n    };\n\n    const commandEvent = `command:${this.name()}`;\n    if (this._actionHandler) {\n      checkForUnknownOptions();\n      this._processArguments();\n\n      let promiseChain;\n      promiseChain = this._chainOrCallHooks(promiseChain, 'preAction');\n      promiseChain = this._chainOrCall(promiseChain, () => this._actionHandler(this.processedArgs));\n      if (this.parent) {\n        promiseChain = this._chainOrCall(promiseChain, () => {\n          this.parent.emit(commandEvent, operands, unknown); // legacy\n        });\n      }\n      promiseChain = this._chainOrCallHooks(promiseChain, 'postAction');\n      return promiseChain;\n    }\n    if (this.parent && this.parent.listenerCount(commandEvent)) {\n      checkForUnknownOptions();\n      this._processArguments();\n      this.parent.emit(commandEvent, operands, unknown); // legacy\n    } else if (operands.length) {\n      if (this._findCommand('*')) { // legacy default command\n        return this._dispatchSubcommand('*', operands, unknown);\n      }\n      if (this.listenerCount('command:*')) {\n        // skip option check, emit event for possible misspelling suggestion\n        this.emit('command:*', operands, unknown);\n      } else if (this.commands.length) {\n        this.unknownCommand();\n      } else {\n        checkForUnknownOptions();\n        this._processArguments();\n      }\n    } else if (this.commands.length) {\n      checkForUnknownOptions();\n      // This command has subcommands and nothing hooked up at this level, so display help (and exit).\n      this.help({ error: true });\n    } else {\n      checkForUnknownOptions();\n      this._processArguments();\n      // fall through for caller to handle after calling .parse()\n    }\n  }\n\n  /**\n   * Find matching command.\n   *\n   * @private\n   */\n  _findCommand(name) {\n    if (!name) return undefined;\n    return this.commands.find(cmd => cmd._name === name || cmd._aliases.includes(name));\n  }\n\n  /**\n   * Return an option matching `arg` if any.\n   *\n   * @param {string} arg\n   * @return {Option}\n   * @package internal use only\n   */\n\n  _findOption(arg) {\n    return this.options.find(option => option.is(arg));\n  }\n\n  /**\n   * Display an error message if a mandatory option does not have a value.\n   * Called after checking for help flags in leaf subcommand.\n   *\n   * @private\n   */\n\n  _checkForMissingMandatoryOptions() {\n    // Walk up hierarchy so can call in subcommand after checking for displaying help.\n    this._getCommandAndAncestors().forEach((cmd) => {\n      cmd.options.forEach((anOption) => {\n        if (anOption.mandatory && (cmd.getOptionValue(anOption.attributeName()) === undefined)) {\n          cmd.missingMandatoryOptionValue(anOption);\n        }\n      });\n    });\n  }\n\n  /**\n   * Display an error message if conflicting options are used together in this.\n   *\n   * @private\n   */\n  _checkForConflictingLocalOptions() {\n    const definedNonDefaultOptions = this.options.filter(\n      (option) => {\n        const optionKey = option.attributeName();\n        if (this.getOptionValue(optionKey) === undefined) {\n          return false;\n        }\n        return this.getOptionValueSource(optionKey) !== 'default';\n      }\n    );\n\n    const optionsWithConflicting = definedNonDefaultOptions.filter(\n      (option) => option.conflictsWith.length > 0\n    );\n\n    optionsWithConflicting.forEach((option) => {\n      const conflictingAndDefined = definedNonDefaultOptions.find((defined) =>\n        option.conflictsWith.includes(defined.attributeName())\n      );\n      if (conflictingAndDefined) {\n        this._conflictingOption(option, conflictingAndDefined);\n      }\n    });\n  }\n\n  /**\n   * Display an error message if conflicting options are used together.\n   * Called after checking for help flags in leaf subcommand.\n   *\n   * @private\n   */\n  _checkForConflictingOptions() {\n    // Walk up hierarchy so can call in subcommand after checking for displaying help.\n    this._getCommandAndAncestors().forEach((cmd) => {\n      cmd._checkForConflictingLocalOptions();\n    });\n  }\n\n  /**\n   * Parse options from `argv` removing known options,\n   * and return argv split into operands and unknown arguments.\n   *\n   * Examples:\n   *\n   *     argv => operands, unknown\n   *     --known kkk op => [op], []\n   *     op --known kkk => [op], []\n   *     sub --unknown uuu op => [sub], [--unknown uuu op]\n   *     sub -- --unknown uuu op => [sub --unknown uuu op], []\n   *\n   * @param {string[]} argv\n   * @return {{operands: string[], unknown: string[]}}\n   */\n\n  parseOptions(argv) {\n    const operands = []; // operands, not options or values\n    const unknown = []; // first unknown option and remaining unknown args\n    let dest = operands;\n    const args = argv.slice();\n\n    function maybeOption(arg) {\n      return arg.length > 1 && arg[0] === '-';\n    }\n\n    // parse options\n    let activeVariadicOption = null;\n    while (args.length) {\n      const arg = args.shift();\n\n      // literal\n      if (arg === '--') {\n        if (dest === unknown) dest.push(arg);\n        dest.push(...args);\n        break;\n      }\n\n      if (activeVariadicOption && !maybeOption(arg)) {\n        this.emit(`option:${activeVariadicOption.name()}`, arg);\n        continue;\n      }\n      activeVariadicOption = null;\n\n      if (maybeOption(arg)) {\n        const option = this._findOption(arg);\n        // recognised option, call listener to assign value with possible custom processing\n        if (option) {\n          if (option.required) {\n            const value = args.shift();\n            if (value === undefined) this.optionMissingArgument(option);\n            this.emit(`option:${option.name()}`, value);\n          } else if (option.optional) {\n            let value = null;\n            // historical behaviour is optional value is following arg unless an option\n            if (args.length > 0 && !maybeOption(args[0])) {\n              value = args.shift();\n            }\n            this.emit(`option:${option.name()}`, value);\n          } else { // boolean flag\n            this.emit(`option:${option.name()}`);\n          }\n          activeVariadicOption = option.variadic ? option : null;\n          continue;\n        }\n      }\n\n      // Look for combo options following single dash, eat first one if known.\n      if (arg.length > 2 && arg[0] === '-' && arg[1] !== '-') {\n        const option = this._findOption(`-${arg[1]}`);\n        if (option) {\n          if (option.required || (option.optional && this._combineFlagAndOptionalValue)) {\n            // option with value following in same argument\n            this.emit(`option:${option.name()}`, arg.slice(2));\n          } else {\n            // boolean option, emit and put back remainder of arg for further processing\n            this.emit(`option:${option.name()}`);\n            args.unshift(`-${arg.slice(2)}`);\n          }\n          continue;\n        }\n      }\n\n      // Look for known long flag with value, like --foo=bar\n      if (/^--[^=]+=/.test(arg)) {\n        const index = arg.indexOf('=');\n        const option = this._findOption(arg.slice(0, index));\n        if (option && (option.required || option.optional)) {\n          this.emit(`option:${option.name()}`, arg.slice(index + 1));\n          continue;\n        }\n      }\n\n      // Not a recognised option by this command.\n      // Might be a command-argument, or subcommand option, or unknown option, or help command or option.\n\n      // An unknown option means further arguments also classified as unknown so can be reprocessed by subcommands.\n      if (maybeOption(arg)) {\n        dest = unknown;\n      }\n\n      // If using positionalOptions, stop processing our options at subcommand.\n      if ((this._enablePositionalOptions || this._passThroughOptions) && operands.length === 0 && unknown.length === 0) {\n        if (this._findCommand(arg)) {\n          operands.push(arg);\n          if (args.length > 0) unknown.push(...args);\n          break;\n        } else if (this._getHelpCommand() && arg === this._getHelpCommand().name()) {\n          operands.push(arg);\n          if (args.length > 0) operands.push(...args);\n          break;\n        } else if (this._defaultCommandName) {\n          unknown.push(arg);\n          if (args.length > 0) unknown.push(...args);\n          break;\n        }\n      }\n\n      // If using passThroughOptions, stop processing options at first command-argument.\n      if (this._passThroughOptions) {\n        dest.push(arg);\n        if (args.length > 0) dest.push(...args);\n        break;\n      }\n\n      // add arg\n      dest.push(arg);\n    }\n\n    return { operands, unknown };\n  }\n\n  /**\n   * Return an object containing local option values as key-value pairs.\n   *\n   * @return {Object}\n   */\n  opts() {\n    if (this._storeOptionsAsProperties) {\n      // Preserve original behaviour so backwards compatible when still using properties\n      const result = {};\n      const len = this.options.length;\n\n      for (let i = 0; i < len; i++) {\n        const key = this.options[i].attributeName();\n        result[key] = key === this._versionOptionName ? this._version : this[key];\n      }\n      return result;\n    }\n\n    return this._optionValues;\n  }\n\n  /**\n   * Return an object containing merged local and global option values as key-value pairs.\n   *\n   * @return {Object}\n   */\n  optsWithGlobals() {\n    // globals overwrite locals\n    return this._getCommandAndAncestors().reduce(\n      (combinedOptions, cmd) => Object.assign(combinedOptions, cmd.opts()),\n      {}\n    );\n  }\n\n  /**\n   * Display error message and exit (or call exitOverride).\n   *\n   * @param {string} message\n   * @param {Object} [errorOptions]\n   * @param {string} [errorOptions.code] - an id string representing the error\n   * @param {number} [errorOptions.exitCode] - used with process.exit\n   */\n  error(message, errorOptions) {\n    // output handling\n    this._outputConfiguration.outputError(`${message}\\n`, this._outputConfiguration.writeErr);\n    if (typeof this._showHelpAfterError === 'string') {\n      this._outputConfiguration.writeErr(`${this._showHelpAfterError}\\n`);\n    } else if (this._showHelpAfterError) {\n      this._outputConfiguration.writeErr('\\n');\n      this.outputHelp({ error: true });\n    }\n\n    // exit handling\n    const config = errorOptions || {};\n    const exitCode = config.exitCode || 1;\n    const code = config.code || 'commander.error';\n    this._exit(exitCode, code, message);\n  }\n\n  /**\n   * Apply any option related environment variables, if option does\n   * not have a value from cli or client code.\n   *\n   * @private\n   */\n  _parseOptionsEnv() {\n    this.options.forEach((option) => {\n      if (option.envVar && option.envVar in process.env) {\n        const optionKey = option.attributeName();\n        // Priority check. Do not overwrite cli or options from unknown source (client-code).\n        if (this.getOptionValue(optionKey) === undefined || ['default', 'config', 'env'].includes(this.getOptionValueSource(optionKey))) {\n          if (option.required || option.optional) { // option can take a value\n            // keep very simple, optional always takes value\n            this.emit(`optionEnv:${option.name()}`, process.env[option.envVar]);\n          } else { // boolean\n            // keep very simple, only care that envVar defined and not the value\n            this.emit(`optionEnv:${option.name()}`);\n          }\n        }\n      }\n    });\n  }\n\n  /**\n   * Apply any implied option values, if option is undefined or default value.\n   *\n   * @private\n   */\n  _parseOptionsImplied() {\n    const dualHelper = new DualOptions(this.options);\n    const hasCustomOptionValue = (optionKey) => {\n      return this.getOptionValue(optionKey) !== undefined && !['default', 'implied'].includes(this.getOptionValueSource(optionKey));\n    };\n    this.options\n      .filter(option => (option.implied !== undefined) &&\n        hasCustomOptionValue(option.attributeName()) &&\n        dualHelper.valueFromOption(this.getOptionValue(option.attributeName()), option))\n      .forEach((option) => {\n        Object.keys(option.implied)\n          .filter(impliedKey => !hasCustomOptionValue(impliedKey))\n          .forEach(impliedKey => {\n            this.setOptionValueWithSource(impliedKey, option.implied[impliedKey], 'implied');\n          });\n      });\n  }\n\n  /**\n   * Argument `name` is missing.\n   *\n   * @param {string} name\n   * @private\n   */\n\n  missingArgument(name) {\n    const message = `error: missing required argument '${name}'`;\n    this.error(message, { code: 'commander.missingArgument' });\n  }\n\n  /**\n   * `Option` is missing an argument.\n   *\n   * @param {Option} option\n   * @private\n   */\n\n  optionMissingArgument(option) {\n    const message = `error: option '${option.flags}' argument missing`;\n    this.error(message, { code: 'commander.optionMissingArgument' });\n  }\n\n  /**\n   * `Option` does not have a value, and is a mandatory option.\n   *\n   * @param {Option} option\n   * @private\n   */\n\n  missingMandatoryOptionValue(option) {\n    const message = `error: required option '${option.flags}' not specified`;\n    this.error(message, { code: 'commander.missingMandatoryOptionValue' });\n  }\n\n  /**\n   * `Option` conflicts with another option.\n   *\n   * @param {Option} option\n   * @param {Option} conflictingOption\n   * @private\n   */\n  _conflictingOption(option, conflictingOption) {\n    // The calling code does not know whether a negated option is the source of the\n    // value, so do some work to take an educated guess.\n    const findBestOptionFromValue = (option) => {\n      const optionKey = option.attributeName();\n      const optionValue = this.getOptionValue(optionKey);\n      const negativeOption = this.options.find(target => target.negate && optionKey === target.attributeName());\n      const positiveOption = this.options.find(target => !target.negate && optionKey === target.attributeName());\n      if (negativeOption && (\n        (negativeOption.presetArg === undefined && optionValue === false) ||\n        (negativeOption.presetArg !== undefined && optionValue === negativeOption.presetArg)\n      )) {\n        return negativeOption;\n      }\n      return positiveOption || option;\n    };\n\n    const getErrorMessage = (option) => {\n      const bestOption = findBestOptionFromValue(option);\n      const optionKey = bestOption.attributeName();\n      const source = this.getOptionValueSource(optionKey);\n      if (source === 'env') {\n        return `environment variable '${bestOption.envVar}'`;\n      }\n      return `option '${bestOption.flags}'`;\n    };\n\n    const message = `error: ${getErrorMessage(option)} cannot be used with ${getErrorMessage(conflictingOption)}`;\n    this.error(message, { code: 'commander.conflictingOption' });\n  }\n\n  /**\n   * Unknown option `flag`.\n   *\n   * @param {string} flag\n   * @private\n   */\n\n  unknownOption(flag) {\n    if (this._allowUnknownOption) return;\n    let suggestion = '';\n\n    if (flag.startsWith('--') && this._showSuggestionAfterError) {\n      // Looping to pick up the global options too\n      let candidateFlags = [];\n      let command = this;\n      do {\n        const moreFlags = command.createHelp().visibleOptions(command)\n          .filter(option => option.long)\n          .map(option => option.long);\n        candidateFlags = candidateFlags.concat(moreFlags);\n        command = command.parent;\n      } while (command && !command._enablePositionalOptions);\n      suggestion = suggestSimilar(flag, candidateFlags);\n    }\n\n    const message = `error: unknown option '${flag}'${suggestion}`;\n    this.error(message, { code: 'commander.unknownOption' });\n  }\n\n  /**\n   * Excess arguments, more than expected.\n   *\n   * @param {string[]} receivedArgs\n   * @private\n   */\n\n  _excessArguments(receivedArgs) {\n    if (this._allowExcessArguments) return;\n\n    const expected = this.registeredArguments.length;\n    const s = (expected === 1) ? '' : 's';\n    const forSubcommand = this.parent ? ` for '${this.name()}'` : '';\n    const message = `error: too many arguments${forSubcommand}. Expected ${expected} argument${s} but got ${receivedArgs.length}.`;\n    this.error(message, { code: 'commander.excessArguments' });\n  }\n\n  /**\n   * Unknown command.\n   *\n   * @private\n   */\n\n  unknownCommand() {\n    const unknownName = this.args[0];\n    let suggestion = '';\n\n    if (this._showSuggestionAfterError) {\n      const candidateNames = [];\n      this.createHelp().visibleCommands(this).forEach((command) => {\n        candidateNames.push(command.name());\n        // just visible alias\n        if (command.alias()) candidateNames.push(command.alias());\n      });\n      suggestion = suggestSimilar(unknownName, candidateNames);\n    }\n\n    const message = `error: unknown command '${unknownName}'${suggestion}`;\n    this.error(message, { code: 'commander.unknownCommand' });\n  }\n\n  /**\n   * Get or set the program version.\n   *\n   * This method auto-registers the \"-V, --version\" option which will print the version number.\n   *\n   * You can optionally supply the flags and description to override the defaults.\n   *\n   * @param {string} [str]\n   * @param {string} [flags]\n   * @param {string} [description]\n   * @return {(this | string | undefined)} `this` command for chaining, or version string if no arguments\n   */\n\n  version(str, flags, description) {\n    if (str === undefined) return this._version;\n    this._version = str;\n    flags = flags || '-V, --version';\n    description = description || 'output the version number';\n    const versionOption = this.createOption(flags, description);\n    this._versionOptionName = versionOption.attributeName();\n    this._registerOption(versionOption);\n\n    this.on('option:' + versionOption.name(), () => {\n      this._outputConfiguration.writeOut(`${str}\\n`);\n      this._exit(0, 'commander.version', str);\n    });\n    return this;\n  }\n\n  /**\n   * Set the description.\n   *\n   * @param {string} [str]\n   * @param {Object} [argsDescription]\n   * @return {(string|Command)}\n   */\n  description(str, argsDescription) {\n    if (str === undefined && argsDescription === undefined) return this._description;\n    this._description = str;\n    if (argsDescription) {\n      this._argsDescription = argsDescription;\n    }\n    return this;\n  }\n\n  /**\n   * Set the summary. Used when listed as subcommand of parent.\n   *\n   * @param {string} [str]\n   * @return {(string|Command)}\n   */\n  summary(str) {\n    if (str === undefined) return this._summary;\n    this._summary = str;\n    return this;\n  }\n\n  /**\n   * Set an alias for the command.\n   *\n   * You may call more than once to add multiple aliases. Only the first alias is shown in the auto-generated help.\n   *\n   * @param {string} [alias]\n   * @return {(string|Command)}\n   */\n\n  alias(alias) {\n    if (alias === undefined) return this._aliases[0]; // just return first, for backwards compatibility\n\n    /** @type {Command} */\n    let command = this;\n    if (this.commands.length !== 0 && this.commands[this.commands.length - 1]._executableHandler) {\n      // assume adding alias for last added executable subcommand, rather than this\n      command = this.commands[this.commands.length - 1];\n    }\n\n    if (alias === command._name) throw new Error('Command alias can\\'t be the same as its name');\n    const matchingCommand = this.parent?._findCommand(alias);\n    if (matchingCommand) {\n      // c.f. _registerCommand\n      const existingCmd = [matchingCommand.name()].concat(matchingCommand.aliases()).join('|');\n      throw new Error(`cannot add alias '${alias}' to command '${this.name()}' as already have command '${existingCmd}'`);\n    }\n\n    command._aliases.push(alias);\n    return this;\n  }\n\n  /**\n   * Set aliases for the command.\n   *\n   * Only the first alias is shown in the auto-generated help.\n   *\n   * @param {string[]} [aliases]\n   * @return {(string[]|Command)}\n   */\n\n  aliases(aliases) {\n    // Getter for the array of aliases is the main reason for having aliases() in addition to alias().\n    if (aliases === undefined) return this._aliases;\n\n    aliases.forEach((alias) => this.alias(alias));\n    return this;\n  }\n\n  /**\n   * Set / get the command usage `str`.\n   *\n   * @param {string} [str]\n   * @return {(string|Command)}\n   */\n\n  usage(str) {\n    if (str === undefined) {\n      if (this._usage) return this._usage;\n\n      const args = this.registeredArguments.map((arg) => {\n        return humanReadableArgName(arg);\n      });\n      return [].concat(\n        (this.options.length || (this._helpOption !== null) ? '[options]' : []),\n        (this.commands.length ? '[command]' : []),\n        (this.registeredArguments.length ? args : [])\n      ).join(' ');\n    }\n\n    this._usage = str;\n    return this;\n  }\n\n  /**\n   * Get or set the name of the command.\n   *\n   * @param {string} [str]\n   * @return {(string|Command)}\n   */\n\n  name(str) {\n    if (str === undefined) return this._name;\n    this._name = str;\n    return this;\n  }\n\n  /**\n   * Set the name of the command from script filename, such as process.argv[1],\n   * or require.main.filename, or __filename.\n   *\n   * (Used internally and public although not documented in README.)\n   *\n   * @example\n   * program.nameFromFilename(require.main.filename);\n   *\n   * @param {string} filename\n   * @return {Command}\n   */\n\n  nameFromFilename(filename) {\n    this._name = path.basename(filename, path.extname(filename));\n\n    return this;\n  }\n\n  /**\n   * Get or set the directory for searching for executable subcommands of this command.\n   *\n   * @example\n   * program.executableDir(__dirname);\n   * // or\n   * program.executableDir('subcommands');\n   *\n   * @param {string} [path]\n   * @return {(string|null|Command)}\n   */\n\n  executableDir(path) {\n    if (path === undefined) return this._executableDir;\n    this._executableDir = path;\n    return this;\n  }\n\n  /**\n   * Return program help documentation.\n   *\n   * @param {{ error: boolean }} [contextOptions] - pass {error:true} to wrap for stderr instead of stdout\n   * @return {string}\n   */\n\n  helpInformation(contextOptions) {\n    const helper = this.createHelp();\n    if (helper.helpWidth === undefined) {\n      helper.helpWidth = (contextOptions && contextOptions.error) ? this._outputConfiguration.getErrHelpWidth() : this._outputConfiguration.getOutHelpWidth();\n    }\n    return helper.formatHelp(this, helper);\n  }\n\n  /**\n   * @private\n   */\n\n  _getHelpContext(contextOptions) {\n    contextOptions = contextOptions || {};\n    const context = { error: !!contextOptions.error };\n    let write;\n    if (context.error) {\n      write = (arg) => this._outputConfiguration.writeErr(arg);\n    } else {\n      write = (arg) => this._outputConfiguration.writeOut(arg);\n    }\n    context.write = contextOptions.write || write;\n    context.command = this;\n    return context;\n  }\n\n  /**\n   * Output help information for this command.\n   *\n   * Outputs built-in help, and custom text added using `.addHelpText()`.\n   *\n   * @param {{ error: boolean } | Function} [contextOptions] - pass {error:true} to write to stderr instead of stdout\n   */\n\n  outputHelp(contextOptions) {\n    let deprecatedCallback;\n    if (typeof contextOptions === 'function') {\n      deprecatedCallback = contextOptions;\n      contextOptions = undefined;\n    }\n    const context = this._getHelpContext(contextOptions);\n\n    this._getCommandAndAncestors().reverse().forEach(command => command.emit('beforeAllHelp', context));\n    this.emit('beforeHelp', context);\n\n    let helpInformation = this.helpInformation(context);\n    if (deprecatedCallback) {\n      helpInformation = deprecatedCallback(helpInformation);\n      if (typeof helpInformation !== 'string' && !Buffer.isBuffer(helpInformation)) {\n        throw new Error('outputHelp callback must return a string or a Buffer');\n      }\n    }\n    context.write(helpInformation);\n\n    if (this._getHelpOption()?.long) {\n      this.emit(this._getHelpOption().long); // deprecated\n    }\n    this.emit('afterHelp', context);\n    this._getCommandAndAncestors().forEach(command => command.emit('afterAllHelp', context));\n  }\n\n  /**\n   * You can pass in flags and a description to customise the built-in help option.\n   * Pass in false to disable the built-in help option.\n   *\n   * @example\n   * program.helpOption('-?, --help' 'show help'); // customise\n   * program.helpOption(false); // disable\n   *\n   * @param {(string | boolean)} flags\n   * @param {string} [description]\n   * @return {Command} `this` command for chaining\n   */\n\n  helpOption(flags, description) {\n    // Support disabling built-in help option.\n    if (typeof flags === 'boolean') {\n      if (flags) {\n        this._helpOption = this._helpOption ?? undefined; // preserve existing option\n      } else {\n        this._helpOption = null; // disable\n      }\n      return this;\n    }\n\n    // Customise flags and description.\n    flags = flags ?? '-h, --help';\n    description = description ?? 'display help for command';\n    this._helpOption = this.createOption(flags, description);\n\n    return this;\n  }\n\n  /**\n   * Lazy create help option.\n   * Returns null if has been disabled with .helpOption(false).\n   *\n   * @returns {(Option | null)} the help option\n   * @package internal use only\n   */\n  _getHelpOption() {\n    // Lazy create help option on demand.\n    if (this._helpOption === undefined) {\n      this.helpOption(undefined, undefined);\n    }\n    return this._helpOption;\n  }\n\n  /**\n   * Supply your own option to use for the built-in help option.\n   * This is an alternative to using helpOption() to customise the flags and description etc.\n   *\n   * @param {Option} option\n   * @return {Command} `this` command for chaining\n   */\n  addHelpOption(option) {\n    this._helpOption = option;\n    return this;\n  }\n\n  /**\n   * Output help information and exit.\n   *\n   * Outputs built-in help, and custom text added using `.addHelpText()`.\n   *\n   * @param {{ error: boolean }} [contextOptions] - pass {error:true} to write to stderr instead of stdout\n   */\n\n  help(contextOptions) {\n    this.outputHelp(contextOptions);\n    let exitCode = process.exitCode || 0;\n    if (exitCode === 0 && contextOptions && typeof contextOptions !== 'function' && contextOptions.error) {\n      exitCode = 1;\n    }\n    // message: do not have all displayed text available so only passing placeholder.\n    this._exit(exitCode, 'commander.help', '(outputHelp)');\n  }\n\n  /**\n   * Add additional text to be displayed with the built-in help.\n   *\n   * Position is 'before' or 'after' to affect just this command,\n   * and 'beforeAll' or 'afterAll' to affect this command and all its subcommands.\n   *\n   * @param {string} position - before or after built-in help\n   * @param {(string | Function)} text - string to add, or a function returning a string\n   * @return {Command} `this` command for chaining\n   */\n  addHelpText(position, text) {\n    const allowedValues = ['beforeAll', 'before', 'after', 'afterAll'];\n    if (!allowedValues.includes(position)) {\n      throw new Error(`Unexpected value for position to addHelpText.\nExpecting one of '${allowedValues.join(\"', '\")}'`);\n    }\n    const helpEvent = `${position}Help`;\n    this.on(helpEvent, (context) => {\n      let helpStr;\n      if (typeof text === 'function') {\n        helpStr = text({ error: context.error, command: context.command });\n      } else {\n        helpStr = text;\n      }\n      // Ignore falsy value when nothing to output.\n      if (helpStr) {\n        context.write(`${helpStr}\\n`);\n      }\n    });\n    return this;\n  }\n\n  /**\n   * Output help information if help flags specified\n   *\n   * @param {Array} args - array of options to search for help flags\n   * @private\n   */\n\n  _outputHelpIfRequested(args) {\n    const helpOption = this._getHelpOption();\n    const helpRequested = helpOption && args.find(arg => helpOption.is(arg));\n    if (helpRequested) {\n      this.outputHelp();\n      // (Do not have all displayed text available so only passing placeholder.)\n      this._exit(0, 'commander.helpDisplayed', '(outputHelp)');\n    }\n  }\n}\n\n/**\n * Scan arguments and increment port number for inspect calls (to avoid conflicts when spawning new command).\n *\n * @param {string[]} args - array of arguments from node.execArgv\n * @returns {string[]}\n * @private\n */\n\nfunction incrementNodeInspectorPort(args) {\n  // Testing for these options:\n  //  --inspect[=[host:]port]\n  //  --inspect-brk[=[host:]port]\n  //  --inspect-port=[host:]port\n  return args.map((arg) => {\n    if (!arg.startsWith('--inspect')) {\n      return arg;\n    }\n    let debugOption;\n    let debugHost = '127.0.0.1';\n    let debugPort = '9229';\n    let match;\n    if ((match = arg.match(/^(--inspect(-brk)?)$/)) !== null) {\n      // e.g. --inspect\n      debugOption = match[1];\n    } else if ((match = arg.match(/^(--inspect(-brk|-port)?)=([^:]+)$/)) !== null) {\n      debugOption = match[1];\n      if (/^\\d+$/.test(match[3])) {\n        // e.g. --inspect=1234\n        debugPort = match[3];\n      } else {\n        // e.g. --inspect=localhost\n        debugHost = match[3];\n      }\n    } else if ((match = arg.match(/^(--inspect(-brk|-port)?)=([^:]+):(\\d+)$/)) !== null) {\n      // e.g. --inspect=localhost:1234\n      debugOption = match[1];\n      debugHost = match[3];\n      debugPort = match[4];\n    }\n\n    if (debugOption && debugPort !== '0') {\n      return `${debugOption}=${debugHost}:${parseInt(debugPort) + 1}`;\n    }\n    return arg;\n  });\n}\n\nexports.Command = Command;\n", "const { Argument } = require('./lib/argument.js');\nconst { Command } = require('./lib/command.js');\nconst { CommanderError, InvalidArgumentError } = require('./lib/error.js');\nconst { Help } = require('./lib/help.js');\nconst { Option } = require('./lib/option.js');\n\nexports.program = new Command();\n\nexports.createCommand = (name) => new Command(name);\nexports.createOption = (flags, description) => new Option(flags, description);\nexports.createArgument = (name, description) => new Argument(name, description);\n\n/**\n * Expose classes\n */\n\nexports.Command = Command;\nexports.Option = Option;\nexports.Argument = Argument;\nexports.Help = Help;\n\nexports.CommanderError = CommanderError;\nexports.InvalidArgumentError = InvalidArgumentError;\nexports.InvalidOptionArgumentError = InvalidArgumentError; // Deprecated\n", "import commander from './index.js';\n\n// wrapper to provide named exports for ESM.\nexport const {\n  program,\n  createCommand,\n  createArgument,\n  createOption,\n  CommanderError,\n  InvalidArgumentError,\n  InvalidOptionArgumentError, // deprecated old name\n  Command,\n  Argument,\n  Option,\n  Help\n} = commander;\n", "var assignValue = require('./_assignValue'),\n    baseAssignValue = require('./_baseAssignValue');\n\n/**\n * Copies properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy properties from.\n * @param {Array} props The property identifiers to copy.\n * @param {Object} [object={}] The object to copy properties to.\n * @param {Function} [customizer] The function to customize copied values.\n * @returns {Object} Returns `object`.\n */\nfunction copyObject(source, props, object, customizer) {\n  var isNew = !object;\n  object || (object = {});\n\n  var index = -1,\n      length = props.length;\n\n  while (++index < length) {\n    var key = props[index];\n\n    var newValue = customizer\n      ? customizer(object[key], source[key], key, object, source)\n      : undefined;\n\n    if (newValue === undefined) {\n      newValue = source[key];\n    }\n    if (isNew) {\n      baseAssignValue(object, key, newValue);\n    } else {\n      assignValue(object, key, newValue);\n    }\n  }\n  return object;\n}\n\nmodule.exports = copyObject;\n", "var copyObject = require('./_copyObject'),\n    keys = require('./keys');\n\n/**\n * The base implementation of `_.assign` without support for multiple sources\n * or `customizer` functions.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @returns {Object} Returns `object`.\n */\nfunction baseAssign(object, source) {\n  return object && copyObject(source, keys(source), object);\n}\n\nmodule.exports = baseAssign;\n", "var copyObject = require('./_copyObject'),\n    keysIn = require('./keysIn');\n\n/**\n * The base implementation of `_.assignIn` without support for multiple sources\n * or `customizer` functions.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @returns {Object} Returns `object`.\n */\nfunction baseAssignIn(object, source) {\n  return object && copyObject(source, keysIn(source), object);\n}\n\nmodule.exports = baseAssignIn;\n", "var root = require('./_root');\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Built-in value references. */\nvar Buffer = moduleExports ? root.Buffer : undefined,\n    allocUnsafe = Buffer ? Buffer.allocUnsafe : undefined;\n\n/**\n * Creates a clone of  `buffer`.\n *\n * @private\n * @param {<PERSON>uffer} buffer The buffer to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Buffer} Returns the cloned buffer.\n */\nfunction cloneBuffer(buffer, isDeep) {\n  if (isDeep) {\n    return buffer.slice();\n  }\n  var length = buffer.length,\n      result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);\n\n  buffer.copy(result);\n  return result;\n}\n\nmodule.exports = cloneBuffer;\n", "var copyObject = require('./_copyObject'),\n    getSymbols = require('./_getSymbols');\n\n/**\n * Copies own symbols of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy symbols from.\n * @param {Object} [object={}] The object to copy symbols to.\n * @returns {Object} Returns `object`.\n */\nfunction copySymbols(source, object) {\n  return copyObject(source, getSymbols(source), object);\n}\n\nmodule.exports = copySymbols;\n", "var copyObject = require('./_copyObject'),\n    getSymbolsIn = require('./_getSymbolsIn');\n\n/**\n * Copies own and inherited symbols of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy symbols from.\n * @param {Object} [object={}] The object to copy symbols to.\n * @returns {Object} Returns `object`.\n */\nfunction copySymbolsIn(source, object) {\n  return copyObject(source, getSymbolsIn(source), object);\n}\n\nmodule.exports = copySymbolsIn;\n", "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Initializes an array clone.\n *\n * @private\n * @param {Array} array The array to clone.\n * @returns {Array} Returns the initialized clone.\n */\nfunction initCloneArray(array) {\n  var length = array.length,\n      result = new array.constructor(length);\n\n  // Add properties assigned by `RegExp#exec`.\n  if (length && typeof array[0] == 'string' && hasOwnProperty.call(array, 'index')) {\n    result.index = array.index;\n    result.input = array.input;\n  }\n  return result;\n}\n\nmodule.exports = initCloneArray;\n", "var Uint8Array = require('./_Uint8Array');\n\n/**\n * Creates a clone of `arrayBuffer`.\n *\n * @private\n * @param {ArrayBuffer} arrayBuffer The array buffer to clone.\n * @returns {ArrayBuffer} Returns the cloned array buffer.\n */\nfunction cloneArrayBuffer(arrayBuffer) {\n  var result = new arrayBuffer.constructor(arrayBuffer.byteLength);\n  new Uint8Array(result).set(new Uint8Array(arrayBuffer));\n  return result;\n}\n\nmodule.exports = cloneArrayBuffer;\n", "var cloneArrayBuffer = require('./_cloneArrayBuffer');\n\n/**\n * Creates a clone of `dataView`.\n *\n * @private\n * @param {Object} dataView The data view to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned data view.\n */\nfunction cloneDataView(dataView, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(dataView.buffer) : dataView.buffer;\n  return new dataView.constructor(buffer, dataView.byteOffset, dataView.byteLength);\n}\n\nmodule.exports = cloneDataView;\n", "/** Used to match `RegExp` flags from their coerced string values. */\nvar reFlags = /\\w*$/;\n\n/**\n * Creates a clone of `regexp`.\n *\n * @private\n * @param {Object} regexp The regexp to clone.\n * @returns {Object} Returns the cloned regexp.\n */\nfunction cloneRegExp(regexp) {\n  var result = new regexp.constructor(regexp.source, reFlags.exec(regexp));\n  result.lastIndex = regexp.lastIndex;\n  return result;\n}\n\nmodule.exports = cloneRegExp;\n", "var Symbol = require('./_Symbol');\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n    symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n\n/**\n * Creates a clone of the `symbol` object.\n *\n * @private\n * @param {Object} symbol The symbol object to clone.\n * @returns {Object} Returns the cloned symbol object.\n */\nfunction cloneSymbol(symbol) {\n  return symbolValueOf ? Object(symbolValueOf.call(symbol)) : {};\n}\n\nmodule.exports = cloneSymbol;\n", "var cloneArrayBuffer = require('./_cloneArrayBuffer');\n\n/**\n * Creates a clone of `typedArray`.\n *\n * @private\n * @param {Object} typedArray The typed array to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned typed array.\n */\nfunction cloneTypedArray(typedArray, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;\n  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);\n}\n\nmodule.exports = cloneTypedArray;\n", "var cloneArrayBuffer = require('./_cloneArrayBuffer'),\n    cloneDataView = require('./_cloneDataView'),\n    cloneRegExp = require('./_cloneRegExp'),\n    cloneSymbol = require('./_cloneSymbol'),\n    cloneTypedArray = require('./_cloneTypedArray');\n\n/** `Object#toString` result references. */\nvar boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    symbolTag = '[object Symbol]';\n\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]',\n    float32Tag = '[object Float32Array]',\n    float64Tag = '[object Float64Array]',\n    int8Tag = '[object Int8Array]',\n    int16Tag = '[object Int16Array]',\n    int32Tag = '[object Int32Array]',\n    uint8Tag = '[object Uint8Array]',\n    uint8ClampedTag = '[object Uint8ClampedArray]',\n    uint16Tag = '[object Uint16Array]',\n    uint32Tag = '[object Uint32Array]';\n\n/**\n * Initializes an object clone based on its `toStringTag`.\n *\n * **Note:** This function only supports cloning values with tags of\n * `Boolean`, `Date`, `Error`, `Map`, `Number`, `RegExp`, `Set`, or `String`.\n *\n * @private\n * @param {Object} object The object to clone.\n * @param {string} tag The `toStringTag` of the object to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneByTag(object, tag, isDeep) {\n  var Ctor = object.constructor;\n  switch (tag) {\n    case arrayBufferTag:\n      return cloneArrayBuffer(object);\n\n    case boolTag:\n    case dateTag:\n      return new Ctor(+object);\n\n    case dataViewTag:\n      return cloneDataView(object, isDeep);\n\n    case float32Tag: case float64Tag:\n    case int8Tag: case int16Tag: case int32Tag:\n    case uint8Tag: case uint8ClampedTag: case uint16Tag: case uint32Tag:\n      return cloneTypedArray(object, isDeep);\n\n    case mapTag:\n      return new Ctor;\n\n    case numberTag:\n    case stringTag:\n      return new Ctor(object);\n\n    case regexpTag:\n      return cloneRegExp(object);\n\n    case setTag:\n      return new Ctor;\n\n    case symbolTag:\n      return cloneSymbol(object);\n  }\n}\n\nmodule.exports = initCloneByTag;\n", "var baseCreate = require('./_baseCreate'),\n    getPrototype = require('./_getPrototype'),\n    isPrototype = require('./_isPrototype');\n\n/**\n * Initializes an object clone.\n *\n * @private\n * @param {Object} object The object to clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneObject(object) {\n  return (typeof object.constructor == 'function' && !isPrototype(object))\n    ? baseCreate(getPrototype(object))\n    : {};\n}\n\nmodule.exports = initCloneObject;\n", "var getTag = require('./_getTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar mapTag = '[object Map]';\n\n/**\n * The base implementation of `_.isMap` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a map, else `false`.\n */\nfunction baseIsMap(value) {\n  return isObjectLike(value) && getTag(value) == mapTag;\n}\n\nmodule.exports = baseIsMap;\n", "var baseIsMap = require('./_baseIsMap'),\n    baseUnary = require('./_baseUnary'),\n    nodeUtil = require('./_nodeUtil');\n\n/* Node.js helper references. */\nvar nodeIsMap = nodeUtil && nodeUtil.isMap;\n\n/**\n * Checks if `value` is classified as a `Map` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a map, else `false`.\n * @example\n *\n * _.isMap(new Map);\n * // => true\n *\n * _.isMap(new WeakMap);\n * // => false\n */\nvar isMap = nodeIsMap ? baseUnary(nodeIsMap) : baseIsMap;\n\nmodule.exports = isMap;\n", "var getTag = require('./_getTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar setTag = '[object Set]';\n\n/**\n * The base implementation of `_.isSet` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a set, else `false`.\n */\nfunction baseIsSet(value) {\n  return isObjectLike(value) && getTag(value) == setTag;\n}\n\nmodule.exports = baseIsSet;\n", "var baseIsSet = require('./_baseIsSet'),\n    baseUnary = require('./_baseUnary'),\n    nodeUtil = require('./_nodeUtil');\n\n/* Node.js helper references. */\nvar nodeIsSet = nodeUtil && nodeUtil.isSet;\n\n/**\n * Checks if `value` is classified as a `Set` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a set, else `false`.\n * @example\n *\n * _.isSet(new Set);\n * // => true\n *\n * _.isSet(new WeakSet);\n * // => false\n */\nvar isSet = nodeIsSet ? baseUnary(nodeIsSet) : baseIsSet;\n\nmodule.exports = isSet;\n", "var Stack = require('./_Stack'),\n    arrayEach = require('./_arrayEach'),\n    assignValue = require('./_assignValue'),\n    baseAssign = require('./_baseAssign'),\n    baseAssignIn = require('./_baseAssignIn'),\n    cloneBuffer = require('./_cloneBuffer'),\n    copyArray = require('./_copyArray'),\n    copySymbols = require('./_copySymbols'),\n    copySymbolsIn = require('./_copySymbolsIn'),\n    getAllKeys = require('./_getAllKeys'),\n    getAllKeysIn = require('./_getAllKeysIn'),\n    getTag = require('./_getTag'),\n    initCloneArray = require('./_initCloneArray'),\n    initCloneByTag = require('./_initCloneByTag'),\n    initCloneObject = require('./_initCloneObject'),\n    isArray = require('./isArray'),\n    isBuffer = require('./isBuffer'),\n    isMap = require('./isMap'),\n    isObject = require('./isObject'),\n    isSet = require('./isSet'),\n    keys = require('./keys'),\n    keysIn = require('./keysIn');\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1,\n    CLONE_FLAT_FLAG = 2,\n    CLONE_SYMBOLS_FLAG = 4;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    arrayTag = '[object Array]',\n    boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    errorTag = '[object Error]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    objectTag = '[object Object]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    symbolTag = '[object Symbol]',\n    weakMapTag = '[object WeakMap]';\n\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]',\n    float32Tag = '[object Float32Array]',\n    float64Tag = '[object Float64Array]',\n    int8Tag = '[object Int8Array]',\n    int16Tag = '[object Int16Array]',\n    int32Tag = '[object Int32Array]',\n    uint8Tag = '[object Uint8Array]',\n    uint8ClampedTag = '[object Uint8ClampedArray]',\n    uint16Tag = '[object Uint16Array]',\n    uint32Tag = '[object Uint32Array]';\n\n/** Used to identify `toStringTag` values supported by `_.clone`. */\nvar cloneableTags = {};\ncloneableTags[argsTag] = cloneableTags[arrayTag] =\ncloneableTags[arrayBufferTag] = cloneableTags[dataViewTag] =\ncloneableTags[boolTag] = cloneableTags[dateTag] =\ncloneableTags[float32Tag] = cloneableTags[float64Tag] =\ncloneableTags[int8Tag] = cloneableTags[int16Tag] =\ncloneableTags[int32Tag] = cloneableTags[mapTag] =\ncloneableTags[numberTag] = cloneableTags[objectTag] =\ncloneableTags[regexpTag] = cloneableTags[setTag] =\ncloneableTags[stringTag] = cloneableTags[symbolTag] =\ncloneableTags[uint8Tag] = cloneableTags[uint8ClampedTag] =\ncloneableTags[uint16Tag] = cloneableTags[uint32Tag] = true;\ncloneableTags[errorTag] = cloneableTags[funcTag] =\ncloneableTags[weakMapTag] = false;\n\n/**\n * The base implementation of `_.clone` and `_.cloneDeep` which tracks\n * traversed objects.\n *\n * @private\n * @param {*} value The value to clone.\n * @param {boolean} bitmask The bitmask flags.\n *  1 - Deep clone\n *  2 - Flatten inherited properties\n *  4 - Clone symbols\n * @param {Function} [customizer] The function to customize cloning.\n * @param {string} [key] The key of `value`.\n * @param {Object} [object] The parent object of `value`.\n * @param {Object} [stack] Tracks traversed objects and their clone counterparts.\n * @returns {*} Returns the cloned value.\n */\nfunction baseClone(value, bitmask, customizer, key, object, stack) {\n  var result,\n      isDeep = bitmask & CLONE_DEEP_FLAG,\n      isFlat = bitmask & CLONE_FLAT_FLAG,\n      isFull = bitmask & CLONE_SYMBOLS_FLAG;\n\n  if (customizer) {\n    result = object ? customizer(value, key, object, stack) : customizer(value);\n  }\n  if (result !== undefined) {\n    return result;\n  }\n  if (!isObject(value)) {\n    return value;\n  }\n  var isArr = isArray(value);\n  if (isArr) {\n    result = initCloneArray(value);\n    if (!isDeep) {\n      return copyArray(value, result);\n    }\n  } else {\n    var tag = getTag(value),\n        isFunc = tag == funcTag || tag == genTag;\n\n    if (isBuffer(value)) {\n      return cloneBuffer(value, isDeep);\n    }\n    if (tag == objectTag || tag == argsTag || (isFunc && !object)) {\n      result = (isFlat || isFunc) ? {} : initCloneObject(value);\n      if (!isDeep) {\n        return isFlat\n          ? copySymbolsIn(value, baseAssignIn(result, value))\n          : copySymbols(value, baseAssign(result, value));\n      }\n    } else {\n      if (!cloneableTags[tag]) {\n        return object ? value : {};\n      }\n      result = initCloneByTag(value, tag, isDeep);\n    }\n  }\n  // Check for circular references and return its corresponding clone.\n  stack || (stack = new Stack);\n  var stacked = stack.get(value);\n  if (stacked) {\n    return stacked;\n  }\n  stack.set(value, result);\n\n  if (isSet(value)) {\n    value.forEach(function(subValue) {\n      result.add(baseClone(subValue, bitmask, customizer, subValue, value, stack));\n    });\n  } else if (isMap(value)) {\n    value.forEach(function(subValue, key) {\n      result.set(key, baseClone(subValue, bitmask, customizer, key, value, stack));\n    });\n  }\n\n  var keysFunc = isFull\n    ? (isFlat ? getAllKeysIn : getAllKeys)\n    : (isFlat ? keysIn : keys);\n\n  var props = isArr ? undefined : keysFunc(value);\n  arrayEach(props || value, function(subValue, key) {\n    if (props) {\n      key = subValue;\n      subValue = value[key];\n    }\n    // Recursively populate clone (susceptible to call stack limits).\n    assignValue(result, key, baseClone(subValue, bitmask, customizer, key, value, stack));\n  });\n  return result;\n}\n\nmodule.exports = baseClone;\n", "var baseClone = require('./_baseClone');\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1,\n    CLONE_SYMBOLS_FLAG = 4;\n\n/**\n * This method is like `_.clone` except that it recursively clones `value`.\n *\n * @static\n * @memberOf _\n * @since 1.0.0\n * @category Lang\n * @param {*} value The value to recursively clone.\n * @returns {*} Returns the deep cloned value.\n * @see _.clone\n * @example\n *\n * var objects = [{ 'a': 1 }, { 'b': 2 }];\n *\n * var deep = _.cloneDeep(objects);\n * console.log(deep[0] === objects[0]);\n * // => false\n */\nfunction cloneDeep(value) {\n  return baseClone(value, CLONE_DEEP_FLAG | CLONE_SYMBOLS_FLAG);\n}\n\nmodule.exports = cloneDeep;\n", "#!/usr/bin/env node\nimport { Help, Option, program } from 'commander'\nimport cloneDeep from 'lodash/cloneDeep'\nimport pickBy from 'lodash/pickBy'\nimport semver from 'semver'\nimport pkg from '../../package.json'\nimport cliOptions, { renderExtendedHelp } from '../cli-options'\nimport ncu from '../index'\nimport { chalkInit } from '../lib/chalk'\n// async global contexts are only available in esm modules -> function\nimport getNcuRc from '../lib/getNcuRc'\n\nconst optionVersionDescription = 'Output the version number of npm-check-updates.'\n\n/** Removes inline code ticks. */\nconst uncode = (s: string) => s.replace(/`/g, '')\n\n;(async () => {\n  // importing update-notifier dynamically as esm modules are only allowed to be dynamically imported inside of cjs modules\n  const { default: updateNotifier } = await import('update-notifier')\n\n  // check if a new version of ncu is available and print an update notification\n  //\n  // For testing from specific versions, use:\n  //\n  // updateNotifier({\n  //   pkg: {\n  //     name: 'npm-check-updates',\n  //     version: x.y.z\n  //   },\n  //   updateCheckInterval: 0\n  // })\n\n  const notifier = updateNotifier({ pkg })\n  if (notifier.update && notifier.update.latest !== pkg.version) {\n    const { default: chalk } = await import('chalk')\n\n    // generate release urls for all the major versions from the current version up to the latest\n    const currentMajor = semver.parse(notifier.update.current)?.major\n    const latestMajor = semver.parse(notifier.update.latest)?.major\n    const majorVersions =\n      // Greater than or equal to (>=) will always return false if either operant is NaN or undefined.\n      // Without this condition, it can result in a RangeError: Invalid array length.\n      // See: https://github.com/raineorshine/npm-check-updates/issues/1200\n      currentMajor && latestMajor && latestMajor >= currentMajor\n        ? new Array(latestMajor - currentMajor).fill(0).map((x, i) => currentMajor + i + 1)\n        : []\n    const releaseUrls = majorVersions.map(majorVersion => `${pkg.homepage ?? ''}/releases/tag/v${majorVersion}.0.0`)\n\n    // for non-major updates, generate a URL to view all commits since the current version\n    const compareUrl = `${pkg.homepage ?? ''}/compare/v${notifier.update.current}...v${notifier.update.latest}`\n\n    notifier.notify({\n      defer: false,\n      isGlobal: true,\n      message: `Update available ${chalk.dim('{currentVersion}')}${chalk.reset(' → ')}${\n        notifier.update.type === 'major'\n          ? chalk.red('{latestVersion}')\n          : notifier.update.type === 'minor'\n            ? chalk.yellow('{latestVersion}')\n            : chalk.green('{latestVersion}')\n      }\nRun ${chalk.cyan('{updateCommand}')} to update\n${chalk.dim.underline(\n  notifier.update.type === 'major' ? releaseUrls.map(url => chalk.dim.underline(url)).join('\\n') : compareUrl,\n)}`,\n    })\n  }\n\n  // manually detect option-specific help\n  // https://github.com/raineorshine/npm-check-updates/issues/787\n  const rawArgs = process.argv.slice(2)\n  const indexHelp = rawArgs.findIndex(arg => arg === '--help' || arg === '-h')\n  if (indexHelp !== -1 && rawArgs[indexHelp + 1]) {\n    const helpOption = rawArgs[indexHelp + 1].replace(/^-*/, '')\n    if (helpOption === 'help' || helpOption === 'h') {\n      console.info('Would you like some help with your help?')\n    } else {\n      await chalkInit()\n      const nonHelpArgs = [...rawArgs.slice(0, indexHelp), ...rawArgs.slice(indexHelp + 1)]\n      nonHelpArgs.forEach(arg => {\n        // match option by long or short\n        const query = arg.replace(/^-*/, '')\n        const option = cliOptions.find(\n          option =>\n            query === option.long ||\n            query === option.short ||\n            (query === `no-${option.long}` && option.type === 'boolean'),\n        )\n        if (option) {\n          console.info(renderExtendedHelp(option) + '\\n')\n        } else if (query === 'version' || query === 'v' || query === 'V') {\n          console.info(\n            renderExtendedHelp({\n              long: 'version',\n              short: 'v',\n              description: optionVersionDescription,\n              // do not pass boolean or it will print --no-version\n              type: 'string',\n            }) + '\\n',\n          )\n        } else {\n          console.info(`Unknown option: ${arg}`)\n        }\n      })\n    }\n    process.exit(0)\n  }\n\n  // a set of options that only work in an rc config file, not on the command line\n  const noCli = new Set(cliOptions.filter(option => option.cli === false).map(option => `--${option.long}`))\n\n  // start commander program\n  program\n    .description('[filter] is a list or regex of package names to check (all others will be ignored).')\n    .usage('[options] [filter]')\n    // See: boolean optional arg below\n    .configureHelp({\n      optionTerm: option =>\n        option.long && noCli.has(option.long)\n          ? option.long.replace('--', '') + '*'\n          : option.long === '--version'\n            ? // add -v to version help to cover the alias added below\n              '-v, -V, --version'\n            : option.flags.replace('[bool]', ''),\n      optionDescription: option =>\n        option.long === '--version'\n          ? optionVersionDescription\n          : option.long === '--help'\n            ? `You're lookin' at it. Run \"ncu --help <option>\" for a specific option.`\n            : Help.prototype.optionDescription(option),\n    })\n    // add hidden -v alias for --V/--version\n    .addOption(new Option('-v, --versionAlias').hideHelp())\n    .on('option:versionAlias', () => {\n      console.info(pkg.version)\n      process.exit(0)\n    })\n\n  // add cli options\n  cliOptions.forEach(({ long, short, arg, description, default: defaultValue, help, parse, type }) => {\n    const flags = `${short ? `-${short}, ` : ''}--${long}${arg ? ` <${arg}>` : ''}`\n    // format description for cli by removing inline code ticks\n    // point to help in description if extended help text is available\n    const descriptionFormatted = `${uncode(description)}${help ? ` Run \"ncu --help ${long}\" for details.` : ''}`\n\n    // handle 3rd/4th argument polymorphism\n    program.option(flags, descriptionFormatted, parse || defaultValue, parse ? defaultValue : undefined)\n\n    // add --no- prefixed boolean options\n    // necessary for overriding booleans set to true in the ncurc\n    if (type === 'boolean') {\n      program.addOption(new Option(`--no-${long}`).default(false).hideHelp())\n    }\n  })\n\n  // set version option at the end\n  program.version(pkg.version)\n\n  // commander mutates its optionValues with program.parse\n  // In order to call program.parse again and parse the rc file options, we need to clear commander's internal optionValues\n  // Otherwise array options will be duplicated\n  const defaultOptionValues = cloneDeep((program as any)._optionValues)\n  program.parse(process.argv)\n\n  const programOpts = program.opts()\n  const programArgs = process.argv.slice(2)\n\n  const { color, configFileName, configFilePath, global, packageFile, mergeConfig } = programOpts\n\n  // Force color on all chalk instances.\n  // See: /src/lib/chalk.ts\n  await chalkInit(color)\n\n  // load .ncurc\n  // Do not load when tests are running (can be overridden if configFilePath is set explicitly, or --mergeConfig option specified)\n  const rcResult =\n    !process.env.NCU_TESTS || configFilePath || mergeConfig\n      ? await getNcuRc({\n          configFileName,\n          configFilePath,\n          global,\n          packageFile,\n          options: { ...programOpts, cli: true },\n        })\n      : null\n\n  // override rc args with program args\n  const rcArgs = (rcResult?.args || []).filter(\n    (arg, i, args) =>\n      (typeof arg !== 'string' || !arg.startsWith('-') || !programArgs.includes(arg)) &&\n      (typeof args[i - 1] !== 'string' || !args[i - 1].startsWith('-') || !programArgs.includes(args[i - 1])),\n  )\n\n  // insert config arguments into command line arguments so they can all be parsed by commander\n  const combinedArguments = [...process.argv.slice(0, 2), ...rcArgs, ...programArgs]\n\n  // See defaultOptionValues comment above\n  ;(program as any)._optionValues = defaultOptionValues\n  program.parse(combinedArguments)\n  const combinedProgramOpts = program.opts()\n\n  // filter out undefined program options and combine cli options with config file options\n  const options = {\n    ...(rcResult && Object.keys(rcResult.config).length > 0 ? { rcConfigPath: rcResult.filePath } : null),\n    ...pickBy(program.opts(), value => value !== undefined),\n    args: program.args,\n    ...(combinedProgramOpts.filter ? { filter: combinedProgramOpts.filter } : null),\n    ...(combinedProgramOpts.reject ? { reject: combinedProgramOpts.reject } : null),\n  }\n\n  // NOTE: Options handling and defaults go in initOptions in index.js\n\n  ncu(options, { cli: true })\n})()\n"], "names": ["exitCode", "code", "message", "Commander<PERSON><PERSON><PERSON>", "error", "InvalidArgumentError", "require$$0", "Argument$3", "name", "description", "value", "previous", "fn", "values", "arg", "humanReadableArgName", "nameOutput", "argument", "Argument", "Help$3", "cmd", "visibleCommands", "helpCommand", "a", "b", "getSortKey", "option", "visibleOptions", "helpOption", "removeShort", "removeLong", "globalOptions", "ancestorCmd", "args", "helper", "max", "command", "cmdName", "ancestorCmdNames", "extraInfo", "choice", "extraDescripton", "termWidth", "helpWidth", "itemIndentWidth", "itemSeparatorWidth", "formatItem", "term", "fullText", "formatList", "textArray", "output", "commandDescription", "argumentList", "optionList", "globalOptionList", "commandList", "str", "width", "indent", "minColumn<PERSON>idth", "indents", "manualIndent", "columnWidth", "leadingStr", "columnText", "indentString", "breaks", "regex", "lines", "line", "i", "help", "Help", "Option$3", "flags", "optionFlags", "splitOptionFlags", "names", "impliedOptionValues", "newImplied", "mandatory", "hide", "camelcase", "DualOptions$1", "options", "key", "optionKey", "preset", "negativeValue", "word", "shortFlag", "longFlag", "flagParts", "Option", "DualOptions", "maxDistance", "editDistance", "d", "j", "cost", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "candidates", "searchingOptions", "candidate", "similar", "bestDistance", "minSimilarity", "distance", "length", "suggestSimilar_1", "EventEmitter", "childProcess", "require$$1", "path", "require$$2", "fs", "require$$3", "process", "require$$4", "require$$5", "require$$6", "require$$7", "require$$8", "require$$9", "Command", "write", "sourceCommand", "result", "nameAndArgs", "actionOptsOrExecDesc", "execOpts", "desc", "opts", "configuration", "displayHelp", "displaySuggestion", "defaultValue", "detail", "previousArgument", "enableOrNameAndArgs", "helpName", "helpArgs", "helpDescription", "deprecatedDescription", "event", "listener", "<PERSON><PERSON><PERSON><PERSON>", "err", "expectedArgsCount", "actionArgs", "target", "invalidArgumentMessage", "matchingOption", "matchingFlag", "knownBy", "alreadyUsed", "existingCmd", "newCmd", "oname", "positiveLongFlag", "handleOptionValue", "val", "invalidValueMessage", "valueSource", "oldValue", "config", "def", "m", "parseArg", "combine", "allowUnknown", "allowExcess", "positional", "passThrough", "storeAsProperties", "source", "argv", "parseOptions", "userArgs", "subcommand", "launchWithNode", "sourceExt", "findFile", "baseDir", "baseName", "localBin", "foundExt", "ext", "executableFile", "executableDir", "resolvedScriptPath", "localFile", "legacyName", "proc", "incrementNodeInspectorPort", "signal", "exitCallback", "_signal", "executableDirMessage", "executableMissing", "wrappedError", "commandName", "operands", "unknown", "subCommand", "<PERSON><PERSON><PERSON><PERSON>", "subcommandName", "myParseArg", "parsedValue", "processedArgs", "declaredArg", "index", "processed", "v", "promise", "hooks", "hookedCommand", "callback", "hookDetail", "hook", "parsed", "checkForUnknownOptions", "commandEvent", "anOption", "definedNonDefaultOptions", "conflictingAndDefined", "defined", "dest", "maybeOption", "activeVariadicOption", "len", "combinedOptions", "errorOptions", "dualHelper", "hasCustomOptionValue", "<PERSON><PERSON><PERSON>", "conflictingOption", "findBestOptionFromValue", "optionValue", "negativeOption", "positiveOption", "getErrorMessage", "bestOption", "flag", "suggestion", "<PERSON><PERSON><PERSON><PERSON>", "moreFlags", "<PERSON><PERSON><PERSON><PERSON>", "expected", "s", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "versionOption", "argsDescription", "alias", "matchingCommand", "aliases", "filename", "contextOptions", "context", "deprecatedCallback", "helpInformation", "position", "text", "helpEvent", "helpStr", "debugOption", "debugHost", "debugPort", "match", "commander", "program", "createCommand", "createArgument", "createOption", "InvalidOptionArgumentError", "assignValue", "baseAssignValue", "copyObject", "props", "object", "customizer", "isNew", "newValue", "_copyObject", "keys", "baseAssign", "_baseAssign", "keysIn", "baseAssignIn", "_baseAssignIn", "root", "freeExports", "exports", "freeModule", "module", "moduleExports", "<PERSON><PERSON><PERSON>", "allocUnsafe", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "isDeep", "getSymbols", "copySymbols", "_copySymbols", "getSymbolsIn", "copySymbolsIn", "_copySymbolsIn", "objectProto", "hasOwnProperty", "initCloneArray", "array", "_initCloneArray", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arrayBuffer", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cloneDataView", "dataView", "_cloneDataView", "reFlags", "cloneRegExp", "regexp", "_cloneRegExp", "Symbol", "symbol<PERSON>roto", "symbolValueOf", "cloneSymbol", "symbol", "_cloneSymbol", "cloneTypedArray", "typedArray", "_cloneTypedArray", "boolTag", "dateTag", "mapTag", "numberTag", "regexpTag", "setTag", "stringTag", "symbolTag", "arrayBufferTag", "dataViewTag", "float32Tag", "float64Tag", "int8Tag", "int16Tag", "int32Tag", "uint8Tag", "uint8ClampedTag", "uint16Tag", "uint32Tag", "initCloneByTag", "tag", "Ctor", "_initCloneByTag", "baseCreate", "getPrototype", "isPrototype", "initCloneObject", "_initCloneObject", "getTag", "isObjectLike", "baseIsMap", "_baseIsMap", "baseUnary", "nodeUtil", "nodeIsMap", "isMap", "isMap_1", "baseIsSet", "_baseIsSet", "nodeIsSet", "isSet", "isSet_1", "<PERSON><PERSON>", "arrayEach", "copyArray", "getAllKeys", "getAllKeysIn", "require$$10", "require$$11", "require$$12", "require$$13", "require$$14", "isArray", "require$$15", "<PERSON><PERSON><PERSON><PERSON>", "require$$16", "require$$17", "isObject", "require$$18", "require$$19", "require$$20", "require$$21", "CLONE_DEEP_FLAG", "CLONE_FLAT_FLAG", "CLONE_SYMBOLS_FLAG", "argsTag", "arrayTag", "errorTag", "funcTag", "genTag", "objectTag", "weakMapTag", "cloneableTags", "baseClone", "bitmask", "stack", "is<PERSON><PERSON>", "isFull", "isArr", "isFunc", "stacked", "subValue", "keysFunc", "_baseClone", "cloneDeep", "cloneDeep_1", "optionVersionDescription", "uncode", "updateNotifier", "notifier", "pkg", "chalk", "<PERSON><PERSON><PERSON><PERSON>", "semver", "latestMajor", "releaseUrls", "x", "majorVersion", "compareUrl", "url", "rawArgs", "indexHelp", "chalkInit", "query", "cliOptions", "renderExtendedHelp", "noCli", "long", "short", "parse", "type", "descriptionFormatted", "defaultOptionValues", "programOpts", "programArgs", "color", "configFileName", "config<PERSON><PERSON><PERSON><PERSON>", "global", "packageFile", "mergeConfig", "rcResult", "getNcuRc", "rcArgs", "combinedArguments", "combinedProgramOpts", "pickBy", "ncu"], "mappings": ";yMAIA,cAA6B,KAAM,CAQjC,YAAYA,EAAUC,EAAMC,EAAS,CACnC,MAAMA,CAAO,EAEb,MAAM,kBAAkB,KAAM,KAAK,WAAW,EAC9C,KAAK,KAAO,KAAK,YAAY,KAC7B,KAAK,KAAOD,EACZ,KAAK,SAAWD,EAChB,KAAK,YAAc,MACpB,CACH,KAMA,cAAmCG,EAAe,CAMhD,YAAYD,EAAS,CACnB,MAAM,EAAG,4BAA6BA,CAAO,EAE7C,MAAM,kBAAkB,KAAM,KAAK,WAAW,EAC9C,KAAK,KAAO,KAAK,YAAY,IAC9B,CACH,EAEsBE,EAAA,eAAGD,GACzBC,EAAA,qBAA+BC,GC1C/B,KAAM,CAAEA,qBAAAA,EAAsB,EAAGC,EAEjC,IAAAC,GAAA,KAAe,CAUb,YAAYC,EAAMC,EAAa,CAQ7B,OAPA,KAAK,YAAcA,GAAe,GAClC,KAAK,SAAW,GAChB,KAAK,SAAW,OAChB,KAAK,aAAe,OACpB,KAAK,wBAA0B,OAC/B,KAAK,WAAa,OAEVD,EAAK,CAAC,EAAC,CACb,IAAK,IACH,KAAK,SAAW,GAChB,KAAK,MAAQA,EAAK,MAAM,EAAG,EAAE,EAC7B,MACF,IAAK,IACH,KAAK,SAAW,GAChB,KAAK,MAAQA,EAAK,MAAM,EAAG,EAAE,EAC7B,MACF,QACE,KAAK,SAAW,GAChB,KAAK,MAAQA,EACb,KACH,CAEG,KAAK,MAAM,OAAS,GAAK,KAAK,MAAM,MAAM,EAAE,IAAM,QACpD,KAAK,SAAW,GAChB,KAAK,MAAQ,KAAK,MAAM,MAAM,EAAG,EAAE,EAEtC,CAQD,MAAO,CACL,OAAO,KAAK,KACb,CAMD,aAAaE,EAAOC,EAAU,CAC5B,OAAIA,IAAa,KAAK,cAAgB,CAAC,MAAM,QAAQA,CAAQ,EACpD,CAACD,CAAK,EAGRC,EAAS,OAAOD,CAAK,CAC7B,CAUD,QAAQA,EAAOD,EAAa,CAC1B,YAAK,aAAeC,EACpB,KAAK,wBAA0BD,EACxB,IACR,CASD,UAAUG,EAAI,CACZ,YAAK,SAAWA,EACT,IACR,CASD,QAAQC,EAAQ,CACd,YAAK,WAAaA,EAAO,QACzB,KAAK,SAAW,CAACC,EAAKH,IAAa,CACjC,GAAI,CAAC,KAAK,WAAW,SAASG,CAAG,EAC/B,MAAM,IAAIT,GAAqB,uBAAuB,KAAK,WAAW,KAAK,IAAI,CAAC,GAAG,EAErF,OAAI,KAAK,SACA,KAAK,aAAaS,EAAKH,CAAQ,EAEjCG,CACb,EACW,IACR,CAKD,aAAc,CACZ,YAAK,SAAW,GACT,IACR,CAKD,aAAc,CACZ,YAAK,SAAW,GACT,IACR,CACH,EAUA,SAASC,GAAqBD,EAAK,CACjC,MAAME,EAAaF,EAAI,QAAUA,EAAI,WAAa,GAAO,MAAQ,IAEjE,OAAOA,EAAI,SACP,IAAME,EAAa,IACnB,IAAMA,EAAa,GACzB,CAEgBC,EAAA,SAAGC,GACnBD,EAAA,qBAA+BF,kBChJ/B,KAAM,CAAEA,qBAAAA,EAAsB,EAAGT,EAWjC,IAAAa,GAAA,KAAW,CACT,aAAc,CACZ,KAAK,UAAY,OACjB,KAAK,gBAAkB,GACvB,KAAK,YAAc,GACnB,KAAK,kBAAoB,EAC1B,CASD,gBAAgBC,EAAK,CACnB,MAAMC,EAAkBD,EAAI,SAAS,OAAOA,GAAO,CAACA,EAAI,OAAO,EACzDE,EAAcF,EAAI,kBACxB,OAAIE,GAAe,CAACA,EAAY,SAC9BD,EAAgB,KAAKC,CAAW,EAE9B,KAAK,iBACPD,EAAgB,KAAK,CAACE,EAAGC,IAEhBD,EAAE,KAAM,EAAC,cAAcC,EAAE,KAAI,CAAE,CACvC,EAEIH,CACR,CASD,eAAeE,EAAGC,EAAG,CACnB,MAAMC,EAAcC,GAEXA,EAAO,MAAQA,EAAO,MAAM,QAAQ,KAAM,EAAE,EAAIA,EAAO,KAAK,QAAQ,MAAO,EAAE,EAEtF,OAAOD,EAAWF,CAAC,EAAE,cAAcE,EAAWD,CAAC,CAAC,CACjD,CASD,eAAeJ,EAAK,CAClB,MAAMO,EAAiBP,EAAI,QAAQ,OAAQM,GAAW,CAACA,EAAO,MAAM,EAE9DE,EAAaR,EAAI,iBACvB,GAAIQ,GAAc,CAACA,EAAW,OAAQ,CAEpC,MAAMC,EAAcD,EAAW,OAASR,EAAI,YAAYQ,EAAW,KAAK,EAClEE,EAAaF,EAAW,MAAQR,EAAI,YAAYQ,EAAW,IAAI,EACjE,CAACC,GAAe,CAACC,EACnBH,EAAe,KAAKC,CAAU,EACrBA,EAAW,MAAQ,CAACE,EAC7BH,EAAe,KAAKP,EAAI,aAAaQ,EAAW,KAAMA,EAAW,WAAW,CAAC,EACpEA,EAAW,OAAS,CAACC,GAC9BF,EAAe,KAAKP,EAAI,aAAaQ,EAAW,MAAOA,EAAW,WAAW,CAAC,CAEjF,CACD,OAAI,KAAK,aACPD,EAAe,KAAK,KAAK,cAAc,EAElCA,CACR,CASD,qBAAqBP,EAAK,CACxB,GAAI,CAAC,KAAK,kBAAmB,MAAO,GAEpC,MAAMW,EAAgB,CAAA,EACtB,QAASC,EAAcZ,EAAI,OAAQY,EAAaA,EAAcA,EAAY,OAAQ,CAChF,MAAML,EAAiBK,EAAY,QAAQ,OAAQN,GAAW,CAACA,EAAO,MAAM,EAC5EK,EAAc,KAAK,GAAGJ,CAAc,CACrC,CACD,OAAI,KAAK,aACPI,EAAc,KAAK,KAAK,cAAc,EAEjCA,CACR,CASD,iBAAiBX,EAAK,CASpB,OAPIA,EAAI,kBACNA,EAAI,oBAAoB,QAAQH,GAAY,CAC1CA,EAAS,YAAcA,EAAS,aAAeG,EAAI,iBAAiBH,EAAS,MAAM,GAAK,EAChG,CAAO,EAICG,EAAI,oBAAoB,KAAKH,GAAYA,EAAS,WAAW,EACxDG,EAAI,oBAEN,EACR,CASD,eAAeA,EAAK,CAElB,MAAMa,EAAOb,EAAI,oBAAoB,IAAIN,GAAOC,GAAqBD,CAAG,CAAC,EAAE,KAAK,GAAG,EACnF,OAAOM,EAAI,OACRA,EAAI,SAAS,CAAC,EAAI,IAAMA,EAAI,SAAS,CAAC,EAAI,KAC1CA,EAAI,QAAQ,OAAS,aAAe,KACpCa,EAAO,IAAMA,EAAO,GACxB,CASD,WAAWP,EAAQ,CACjB,OAAOA,EAAO,KACf,CASD,aAAaT,EAAU,CACrB,OAAOA,EAAS,MACjB,CAUD,4BAA4BG,EAAKc,EAAQ,CACvC,OAAOA,EAAO,gBAAgBd,CAAG,EAAE,OAAO,CAACe,EAAKC,IACvC,KAAK,IAAID,EAAKD,EAAO,eAAeE,CAAO,EAAE,MAAM,EACzD,CAAC,CACL,CAUD,wBAAwBhB,EAAKc,EAAQ,CACnC,OAAOA,EAAO,eAAed,CAAG,EAAE,OAAO,CAACe,EAAKT,IACtC,KAAK,IAAIS,EAAKD,EAAO,WAAWR,CAAM,EAAE,MAAM,EACpD,CAAC,CACL,CAUD,8BAA8BN,EAAKc,EAAQ,CACzC,OAAOA,EAAO,qBAAqBd,CAAG,EAAE,OAAO,CAACe,EAAKT,IAC5C,KAAK,IAAIS,EAAKD,EAAO,WAAWR,CAAM,EAAE,MAAM,EACpD,CAAC,CACL,CAUD,0BAA0BN,EAAKc,EAAQ,CACrC,OAAOA,EAAO,iBAAiBd,CAAG,EAAE,OAAO,CAACe,EAAKlB,IACxC,KAAK,IAAIkB,EAAKD,EAAO,aAAajB,CAAQ,EAAE,MAAM,EACxD,CAAC,CACL,CASD,aAAaG,EAAK,CAEhB,IAAIiB,EAAUjB,EAAI,MACdA,EAAI,SAAS,CAAC,IAChBiB,EAAUA,EAAU,IAAMjB,EAAI,SAAS,CAAC,GAE1C,IAAIkB,EAAmB,GACvB,QAASN,EAAcZ,EAAI,OAAQY,EAAaA,EAAcA,EAAY,OACxEM,EAAmBN,EAAY,OAAS,IAAMM,EAEhD,OAAOA,EAAmBD,EAAU,IAAMjB,EAAI,MAAK,CACpD,CASD,mBAAmBA,EAAK,CAEtB,OAAOA,EAAI,aACZ,CAUD,sBAAsBA,EAAK,CAEzB,OAAOA,EAAI,QAAO,GAAMA,EAAI,YAAW,CACxC,CASD,kBAAkBM,EAAQ,CACxB,MAAMa,EAAY,CAAA,EAuBlB,OArBIb,EAAO,YACTa,EAAU,KAER,YAAYb,EAAO,WAAW,IAAKc,GAAW,KAAK,UAAUA,CAAM,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAElFd,EAAO,eAAiB,SAGNA,EAAO,UAAYA,EAAO,UAC3CA,EAAO,UAAS,GAAM,OAAOA,EAAO,cAAiB,YAEtDa,EAAU,KAAK,YAAYb,EAAO,yBAA2B,KAAK,UAAUA,EAAO,YAAY,CAAC,EAAE,EAIlGA,EAAO,YAAc,QAAaA,EAAO,UAC3Ca,EAAU,KAAK,WAAW,KAAK,UAAUb,EAAO,SAAS,CAAC,EAAE,EAE1DA,EAAO,SAAW,QACpBa,EAAU,KAAK,QAAQb,EAAO,MAAM,EAAE,EAEpCa,EAAU,OAAS,EACd,GAAGb,EAAO,WAAW,KAAKa,EAAU,KAAK,IAAI,CAAC,IAGhDb,EAAO,WACf,CASD,oBAAoBT,EAAU,CAC5B,MAAMsB,EAAY,CAAA,EASlB,GARItB,EAAS,YACXsB,EAAU,KAER,YAAYtB,EAAS,WAAW,IAAKuB,GAAW,KAAK,UAAUA,CAAM,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAEpFvB,EAAS,eAAiB,QAC5BsB,EAAU,KAAK,YAAYtB,EAAS,yBAA2B,KAAK,UAAUA,EAAS,YAAY,CAAC,EAAE,EAEpGsB,EAAU,OAAS,EAAG,CACxB,MAAME,EAAkB,IAAIF,EAAU,KAAK,IAAI,CAAC,IAChD,OAAItB,EAAS,YACJ,GAAGA,EAAS,WAAW,IAAIwB,CAAe,GAE5CA,CACR,CACD,OAAOxB,EAAS,WACjB,CAUD,WAAWG,EAAKc,EAAQ,CACtB,MAAMQ,EAAYR,EAAO,SAASd,EAAKc,CAAM,EACvCS,EAAYT,EAAO,WAAa,GAChCU,EAAkB,EAClBC,EAAqB,EAC3B,SAASC,EAAWC,EAAMtC,EAAa,CACrC,GAAIA,EAAa,CACf,MAAMuC,EAAW,GAAGD,EAAK,OAAOL,EAAYG,CAAkB,CAAC,GAAGpC,CAAW,GAC7E,OAAOyB,EAAO,KAAKc,EAAUL,EAAYC,EAAiBF,EAAYG,CAAkB,CACzF,CACD,OAAOE,CACR,CACD,SAASE,EAAWC,EAAW,CAC7B,OAAOA,EAAU,KAAK;AAAA,CAAI,EAAE,QAAQ,MAAO,IAAI,OAAON,CAAe,CAAC,CACvE,CAGD,IAAIO,EAAS,CAAC,UAAUjB,EAAO,aAAad,CAAG,CAAC,GAAI,EAAE,EAGtD,MAAMgC,EAAqBlB,EAAO,mBAAmBd,CAAG,EACpDgC,EAAmB,OAAS,IAC9BD,EAASA,EAAO,OAAO,CAACjB,EAAO,KAAKkB,EAAoBT,EAAW,CAAC,EAAG,EAAE,CAAC,GAI5E,MAAMU,EAAenB,EAAO,iBAAiBd,CAAG,EAAE,IAAKH,GAC9C6B,EAAWZ,EAAO,aAAajB,CAAQ,EAAGiB,EAAO,oBAAoBjB,CAAQ,CAAC,CACtF,EACGoC,EAAa,OAAS,IACxBF,EAASA,EAAO,OAAO,CAAC,aAAcF,EAAWI,CAAY,EAAG,EAAE,CAAC,GAIrE,MAAMC,EAAapB,EAAO,eAAed,CAAG,EAAE,IAAKM,GAC1CoB,EAAWZ,EAAO,WAAWR,CAAM,EAAGQ,EAAO,kBAAkBR,CAAM,CAAC,CAC9E,EAKD,GAJI4B,EAAW,OAAS,IACtBH,EAASA,EAAO,OAAO,CAAC,WAAYF,EAAWK,CAAU,EAAG,EAAE,CAAC,GAG7D,KAAK,kBAAmB,CAC1B,MAAMC,EAAmBrB,EAAO,qBAAqBd,CAAG,EAAE,IAAKM,GACtDoB,EAAWZ,EAAO,WAAWR,CAAM,EAAGQ,EAAO,kBAAkBR,CAAM,CAAC,CAC9E,EACG6B,EAAiB,OAAS,IAC5BJ,EAASA,EAAO,OAAO,CAAC,kBAAmBF,EAAWM,CAAgB,EAAG,EAAE,CAAC,EAE/E,CAGD,MAAMC,EAActB,EAAO,gBAAgBd,CAAG,EAAE,IAAKA,GAC5C0B,EAAWZ,EAAO,eAAed,CAAG,EAAGc,EAAO,sBAAsBd,CAAG,CAAC,CAChF,EACD,OAAIoC,EAAY,OAAS,IACvBL,EAASA,EAAO,OAAO,CAAC,YAAaF,EAAWO,CAAW,EAAG,EAAE,CAAC,GAG5DL,EAAO,KAAK;AAAA,CAAI,CACxB,CAUD,SAAS/B,EAAKc,EAAQ,CACpB,OAAO,KAAK,IACVA,EAAO,wBAAwBd,EAAKc,CAAM,EAC1CA,EAAO,8BAA8Bd,EAAKc,CAAM,EAChDA,EAAO,4BAA4Bd,EAAKc,CAAM,EAC9CA,EAAO,0BAA0Bd,EAAKc,CAAM,CAClD,CACG,CAcD,KAAKuB,EAAKC,EAAOC,EAAQC,EAAiB,GAAI,CAE5C,MAAMC,EAAU,2BAEVC,EAAe,IAAI,OAAO,SAASD,CAAO,IAAI,EACpD,GAAIJ,EAAI,MAAMK,CAAY,EAAG,OAAOL,EAEpC,MAAMM,EAAcL,EAAQC,EAC5B,GAAII,EAAcH,EAAgB,OAAOH,EAEzC,MAAMO,EAAaP,EAAI,MAAM,EAAGE,CAAM,EAChCM,EAAaR,EAAI,MAAME,CAAM,EAAE,QAAQ;AAAA,EAAQ;AAAA,CAAI,EACnDO,EAAe,IAAI,OAAOP,CAAM,EAEhCQ,EAAS,OAGTC,EAAQ,IAAI,OAAO;AAAA,OAAUL,EAAc,CAAC,MAAMI,CAAM,UAAUA,CAAM,QAAQA,CAAM,OAAQ,GAAG,EACjGE,EAAQJ,EAAW,MAAMG,CAAK,GAAK,CAAA,EACzC,OAAOJ,EAAaK,EAAM,IAAI,CAACC,EAAMC,IAC/BD,IAAS;AAAA,EAAa,IACjBC,EAAI,EAAKL,EAAe,IAAMI,EAAK,SAC7C,EAAE,KAAK;AAAA,CAAI,CACb,CACH,EAEAE,EAAA,KAAeC,YCxcf,KAAM,CAAEpE,qBAAAA,EAAsB,EAAGC,EAEjC,IAAAoE,GAAA,KAAa,CAQX,YAAYC,EAAOlE,EAAa,CAC9B,KAAK,MAAQkE,EACb,KAAK,YAAclE,GAAe,GAElC,KAAK,SAAWkE,EAAM,SAAS,GAAG,EAClC,KAAK,SAAWA,EAAM,SAAS,GAAG,EAElC,KAAK,SAAW,iBAAiB,KAAKA,CAAK,EAC3C,KAAK,UAAY,GACjB,MAAMC,EAAcC,GAAiBF,CAAK,EAC1C,KAAK,MAAQC,EAAY,UACzB,KAAK,KAAOA,EAAY,SACxB,KAAK,OAAS,GACV,KAAK,OACP,KAAK,OAAS,KAAK,KAAK,WAAW,OAAO,GAE5C,KAAK,aAAe,OACpB,KAAK,wBAA0B,OAC/B,KAAK,UAAY,OACjB,KAAK,OAAS,OACd,KAAK,SAAW,OAChB,KAAK,OAAS,GACd,KAAK,WAAa,OAClB,KAAK,cAAgB,GACrB,KAAK,QAAU,MAChB,CAUD,QAAQlE,EAAOD,EAAa,CAC1B,YAAK,aAAeC,EACpB,KAAK,wBAA0BD,EACxB,IACR,CAcD,OAAOK,EAAK,CACV,YAAK,UAAYA,EACV,IACR,CAcD,UAAUgE,EAAO,CACf,YAAK,cAAgB,KAAK,cAAc,OAAOA,CAAK,EAC7C,IACR,CAeD,QAAQC,EAAqB,CAC3B,IAAIC,EAAaD,EACjB,OAAI,OAAOA,GAAwB,WAEjCC,EAAa,CAAE,CAACD,CAAmB,EAAG,KAExC,KAAK,QAAU,OAAO,OAAO,KAAK,SAAW,CAAA,EAAIC,CAAU,EACpD,IACR,CAYD,IAAIxE,EAAM,CACR,YAAK,OAASA,EACP,IACR,CASD,UAAUI,EAAI,CACZ,YAAK,SAAWA,EACT,IACR,CASD,oBAAoBqE,EAAY,GAAM,CACpC,YAAK,UAAY,CAAC,CAACA,EACZ,IACR,CASD,SAASC,EAAO,GAAM,CACpB,YAAK,OAAS,CAAC,CAACA,EACT,IACR,CAMD,aAAaxE,EAAOC,EAAU,CAC5B,OAAIA,IAAa,KAAK,cAAgB,CAAC,MAAM,QAAQA,CAAQ,EACpD,CAACD,CAAK,EAGRC,EAAS,OAAOD,CAAK,CAC7B,CASD,QAAQG,EAAQ,CACd,YAAK,WAAaA,EAAO,QACzB,KAAK,SAAW,CAACC,EAAKH,IAAa,CACjC,GAAI,CAAC,KAAK,WAAW,SAASG,CAAG,EAC/B,MAAM,IAAIT,GAAqB,uBAAuB,KAAK,WAAW,KAAK,IAAI,CAAC,GAAG,EAErF,OAAI,KAAK,SACA,KAAK,aAAaS,EAAKH,CAAQ,EAEjCG,CACb,EACW,IACR,CAQD,MAAO,CACL,OAAI,KAAK,KACA,KAAK,KAAK,QAAQ,MAAO,EAAE,EAE7B,KAAK,MAAM,QAAQ,KAAM,EAAE,CACnC,CASD,eAAgB,CACd,OAAOqE,GAAU,KAAK,KAAI,EAAG,QAAQ,OAAQ,EAAE,CAAC,CACjD,CAUD,GAAGrE,EAAK,CACN,OAAO,KAAK,QAAUA,GAAO,KAAK,OAASA,CAC5C,CAWD,WAAY,CACV,MAAO,CAAC,KAAK,UAAY,CAAC,KAAK,UAAY,CAAC,KAAK,MAClD,CACH,EASAsE,GAAA,KAAkB,CAIhB,YAAYC,EAAS,CACnB,KAAK,gBAAkB,IAAI,IAC3B,KAAK,gBAAkB,IAAI,IAC3B,KAAK,YAAc,IAAI,IACvBA,EAAQ,QAAQ3D,GAAU,CACpBA,EAAO,OACT,KAAK,gBAAgB,IAAIA,EAAO,cAAa,EAAIA,CAAM,EAEvD,KAAK,gBAAgB,IAAIA,EAAO,cAAa,EAAIA,CAAM,CAE/D,CAAK,EACD,KAAK,gBAAgB,QAAQ,CAAChB,EAAO4E,IAAQ,CACvC,KAAK,gBAAgB,IAAIA,CAAG,GAC9B,KAAK,YAAY,IAAIA,CAAG,CAEhC,CAAK,CACF,CASD,gBAAgB5E,EAAOgB,EAAQ,CAC7B,MAAM6D,EAAY7D,EAAO,gBACzB,GAAI,CAAC,KAAK,YAAY,IAAI6D,CAAS,EAAG,MAAO,GAG7C,MAAMC,EAAS,KAAK,gBAAgB,IAAID,CAAS,EAAE,UAC7CE,EAAiBD,IAAW,OAAaA,EAAS,GACxD,OAAO9D,EAAO,UAAY+D,IAAkB/E,EAC7C,CACH,EAUA,SAASyE,GAAU1B,EAAK,CACtB,OAAOA,EAAI,MAAM,GAAG,EAAE,OAAO,CAACA,EAAKiC,IAC1BjC,EAAMiC,EAAK,CAAC,EAAE,YAAW,EAAKA,EAAK,MAAM,CAAC,CAClD,CACH,CAQA,SAASb,GAAiBF,EAAO,CAC/B,IAAIgB,EACAC,EAGJ,MAAMC,EAAYlB,EAAM,MAAM,QAAQ,EACtC,OAAIkB,EAAU,OAAS,GAAK,CAAC,QAAQ,KAAKA,EAAU,CAAC,CAAC,IAAGF,EAAYE,EAAU,MAAK,GACpFD,EAAWC,EAAU,QAEjB,CAACF,GAAa,UAAU,KAAKC,CAAQ,IACvCD,EAAYC,EACZA,EAAW,QAEN,CAAE,UAAAD,EAAW,SAAAC,EACtB,CAEclE,EAAA,OAAGoE,GACjBpE,EAAA,YAAsBqE,aCtUtB,MAAMC,GAAc,EAEpB,SAASC,GAAa1E,EAAGC,EAAG,CAM1B,GAAI,KAAK,IAAID,EAAE,OAASC,EAAE,MAAM,EAAIwE,GAAa,OAAO,KAAK,IAAIzE,EAAE,OAAQC,EAAE,MAAM,EAGnF,MAAM0E,EAAI,CAAA,EAGV,QAAS3B,EAAI,EAAGA,GAAKhD,EAAE,OAAQgD,IAC7B2B,EAAE3B,CAAC,EAAI,CAACA,CAAC,EAGX,QAAS4B,EAAI,EAAGA,GAAK3E,EAAE,OAAQ2E,IAC7BD,EAAE,CAAC,EAAEC,CAAC,EAAIA,EAIZ,QAASA,EAAI,EAAGA,GAAK3E,EAAE,OAAQ2E,IAC7B,QAAS,EAAI,EAAG,GAAK5E,EAAE,OAAQ,IAAK,CAClC,IAAI6E,EAAO,EACP7E,EAAE,EAAI,CAAC,IAAMC,EAAE2E,EAAI,CAAC,EACtBC,EAAO,EAEPA,EAAO,EAETF,EAAE,CAAC,EAAEC,CAAC,EAAI,KAAK,IACbD,EAAE,EAAI,CAAC,EAAEC,CAAC,EAAI,EACdD,EAAE,CAAC,EAAEC,EAAI,CAAC,EAAI,EACdD,EAAE,EAAI,CAAC,EAAEC,EAAI,CAAC,EAAIC,CAC1B,EAEU,EAAI,GAAKD,EAAI,GAAK5E,EAAE,EAAI,CAAC,IAAMC,EAAE2E,EAAI,CAAC,GAAK5E,EAAE,EAAI,CAAC,IAAMC,EAAE2E,EAAI,CAAC,IACjED,EAAE,CAAC,EAAEC,CAAC,EAAI,KAAK,IAAID,EAAE,CAAC,EAAEC,CAAC,EAAGD,EAAE,EAAI,CAAC,EAAEC,EAAI,CAAC,EAAI,CAAC,EAElD,CAGH,OAAOD,EAAE3E,EAAE,MAAM,EAAEC,EAAE,MAAM,CAC7B,CAUA,SAAS6E,GAAeX,EAAMY,EAAY,CACxC,GAAI,CAACA,GAAcA,EAAW,SAAW,EAAG,MAAO,GAEnDA,EAAa,MAAM,KAAK,IAAI,IAAIA,CAAU,CAAC,EAE3C,MAAMC,EAAmBb,EAAK,WAAW,IAAI,EACzCa,IACFb,EAAOA,EAAK,MAAM,CAAC,EACnBY,EAAaA,EAAW,IAAIE,GAAaA,EAAU,MAAM,CAAC,CAAC,GAG7D,IAAIC,EAAU,CAAA,EACVC,EAAeV,GACnB,MAAMW,EAAgB,GAuBtB,OAtBAL,EAAW,QAASE,GAAc,CAChC,GAAIA,EAAU,QAAU,EAAG,OAE3B,MAAMI,EAAWX,GAAaP,EAAMc,CAAS,EACvCK,EAAS,KAAK,IAAInB,EAAK,OAAQc,EAAU,MAAM,GACjCK,EAASD,GAAYC,EACxBF,IACXC,EAAWF,GAEbA,EAAeE,EACfH,EAAU,CAACD,CAAS,GACXI,IAAaF,GACtBD,EAAQ,KAAKD,CAAS,EAG9B,CAAG,EAEDC,EAAQ,KAAK,CAAClF,EAAGC,IAAMD,EAAE,cAAcC,CAAC,CAAC,EACrC+E,IACFE,EAAUA,EAAQ,IAAID,GAAa,KAAKA,CAAS,EAAE,GAGjDC,EAAQ,OAAS,EACZ;AAAA,uBAA0BA,EAAQ,KAAK,IAAI,CAAC,KAEjDA,EAAQ,SAAW,EACd;AAAA,gBAAmBA,EAAQ,CAAC,CAAC,KAE/B,EACT,CAEAK,GAAA,eAAyBT,GCnGzB,MAAMU,GAAezG,GAAkB,aACjC0G,EAAeC,GACfC,EAAOC,GACPC,EAAKC,GACLC,EAAUC,GAEV,UAAErG,GAAU,qBAAAH,EAAsB,EAAGyG,EACrC,CAAErH,eAAAA,CAAgB,EAAGsH,EACrB,CAAEhD,KAAAA,EAAM,EAAGiD,EACX,QAAE5B,EAAQ,YAAAC,EAAa,EAAG4B,EAC1B,CAAE,eAAAtB,CAAgB,EAAGuB,UAE3B,MAAMC,WAAgBd,EAAa,CAOjC,YAAYvG,EAAM,CAChB,QAEA,KAAK,SAAW,GAEhB,KAAK,QAAU,GACf,KAAK,OAAS,KACd,KAAK,oBAAsB,GAC3B,KAAK,sBAAwB,GAE7B,KAAK,oBAAsB,GAC3B,KAAK,MAAQ,KAAK,oBAElB,KAAK,KAAO,GACZ,KAAK,QAAU,GACf,KAAK,cAAgB,GACrB,KAAK,YAAc,KACnB,KAAK,MAAQA,GAAQ,GACrB,KAAK,cAAgB,GACrB,KAAK,oBAAsB,GAC3B,KAAK,0BAA4B,GACjC,KAAK,eAAiB,KACtB,KAAK,mBAAqB,GAC1B,KAAK,gBAAkB,KACvB,KAAK,eAAiB,KACtB,KAAK,oBAAsB,KAC3B,KAAK,cAAgB,KACrB,KAAK,SAAW,GAChB,KAAK,6BAA+B,GACpC,KAAK,aAAe,GACpB,KAAK,SAAW,GAChB,KAAK,iBAAmB,OACxB,KAAK,yBAA2B,GAChC,KAAK,oBAAsB,GAC3B,KAAK,gBAAkB,GAEvB,KAAK,oBAAsB,GAC3B,KAAK,0BAA4B,GAGjC,KAAK,qBAAuB,CAC1B,SAAWiD,GAAQ6D,EAAQ,OAAO,MAAM7D,CAAG,EAC3C,SAAWA,GAAQ6D,EAAQ,OAAO,MAAM7D,CAAG,EAC3C,gBAAiB,IAAM6D,EAAQ,OAAO,MAAQA,EAAQ,OAAO,QAAU,OACvE,gBAAiB,IAAMA,EAAQ,OAAO,MAAQA,EAAQ,OAAO,QAAU,OACvE,YAAa,CAAC7D,EAAKqE,IAAUA,EAAMrE,CAAG,CAC5C,EAEI,KAAK,QAAU,GAEf,KAAK,YAAc,OACnB,KAAK,wBAA0B,OAE/B,KAAK,aAAe,OACpB,KAAK,mBAAqB,EAC3B,CAUD,sBAAsBsE,EAAe,CACnC,YAAK,qBAAuBA,EAAc,qBAC1C,KAAK,YAAcA,EAAc,YACjC,KAAK,aAAeA,EAAc,aAClC,KAAK,mBAAqBA,EAAc,mBACxC,KAAK,cAAgBA,EAAc,cACnC,KAAK,0BAA4BA,EAAc,0BAC/C,KAAK,6BAA+BA,EAAc,6BAClD,KAAK,sBAAwBA,EAAc,sBAC3C,KAAK,yBAA2BA,EAAc,yBAC9C,KAAK,oBAAsBA,EAAc,oBACzC,KAAK,0BAA4BA,EAAc,0BAExC,IACR,CAOD,yBAA0B,CACxB,MAAMC,EAAS,CAAA,EACf,QAAS5F,EAAU,KAAMA,EAASA,EAAUA,EAAQ,OAClD4F,EAAO,KAAK5F,CAAO,EAErB,OAAO4F,CACR,CA2BD,QAAQC,EAAaC,EAAsBC,EAAU,CACnD,IAAIC,EAAOF,EACPG,EAAOF,EACP,OAAOC,GAAS,UAAYA,IAAS,OACvCC,EAAOD,EACPA,EAAO,MAETC,EAAOA,GAAQ,GACf,KAAM,CAAA,CAAG7H,EAAMyB,CAAI,EAAIgG,EAAY,MAAM,eAAe,EAElD7G,EAAM,KAAK,cAAcZ,CAAI,EAanC,OAZI4H,IACFhH,EAAI,YAAYgH,CAAI,EACpBhH,EAAI,mBAAqB,IAEvBiH,EAAK,YAAW,KAAK,oBAAsBjH,EAAI,OACnDA,EAAI,QAAU,CAAC,EAAEiH,EAAK,QAAUA,EAAK,QACrCjH,EAAI,gBAAkBiH,EAAK,gBAAkB,KACzCpG,GAAMb,EAAI,UAAUa,CAAI,EAC5B,KAAK,iBAAiBb,CAAG,EACzBA,EAAI,OAAS,KACbA,EAAI,sBAAsB,IAAI,EAE1BgH,EAAa,KACVhH,CACR,CAYD,cAAcZ,EAAM,CAClB,OAAO,IAAIqH,GAAQrH,CAAI,CACxB,CASD,YAAa,CACX,OAAO,OAAO,OAAO,IAAIiE,GAAQ,KAAK,cAAa,CAAE,CACtD,CAUD,cAAc6D,EAAe,CAC3B,OAAIA,IAAkB,OAAkB,KAAK,oBAE7C,KAAK,mBAAqBA,EACnB,KACR,CAqBD,gBAAgBA,EAAe,CAC7B,OAAIA,IAAkB,OAAkB,KAAK,sBAE7C,OAAO,OAAO,KAAK,qBAAsBA,CAAa,EAC/C,KACR,CAQD,mBAAmBC,EAAc,GAAM,CACrC,OAAI,OAAOA,GAAgB,WAAUA,EAAc,CAAC,CAACA,GACrD,KAAK,oBAAsBA,EACpB,IACR,CAQD,yBAAyBC,EAAoB,GAAM,CACjD,YAAK,0BAA4B,CAAC,CAACA,EAC5B,IACR,CAYD,WAAWpH,EAAKiH,EAAM,CACpB,GAAI,CAACjH,EAAI,MACP,MAAM,IAAI,MAAM;AAAA,2DACqC,EAGvD,OAAAiH,EAAOA,GAAQ,GACXA,EAAK,YAAW,KAAK,oBAAsBjH,EAAI,QAC/CiH,EAAK,QAAUA,EAAK,UAAQjH,EAAI,QAAU,IAE9C,KAAK,iBAAiBA,CAAG,EACzBA,EAAI,OAAS,KACbA,EAAI,2BAA0B,EAEvB,IACR,CAaD,eAAeZ,EAAMC,EAAa,CAChC,OAAO,IAAIS,GAASV,EAAMC,CAAW,CACtC,CAkBD,SAASD,EAAMC,EAAaG,EAAI6H,EAAc,CAC5C,MAAMxH,EAAW,KAAK,eAAeT,EAAMC,CAAW,EACtD,OAAI,OAAOG,GAAO,WAChBK,EAAS,QAAQwH,CAAY,EAAE,UAAU7H,CAAE,EAE3CK,EAAS,QAAQL,CAAE,EAErB,KAAK,YAAYK,CAAQ,EAClB,IACR,CAcD,UAAU6D,EAAO,CACf,OAAAA,EAAM,KAAI,EAAG,MAAM,IAAI,EAAE,QAAS4D,GAAW,CAC3C,KAAK,SAASA,CAAM,CAC1B,CAAK,EACM,IACR,CAQD,YAAYzH,EAAU,CACpB,MAAM0H,EAAmB,KAAK,oBAAoB,MAAM,EAAE,EAAE,CAAC,EAC7D,GAAIA,GAAoBA,EAAiB,SACvC,MAAM,IAAI,MAAM,2CAA2CA,EAAiB,KAAM,CAAA,GAAG,EAEvF,GAAI1H,EAAS,UAAYA,EAAS,eAAiB,QAAaA,EAAS,WAAa,OACpF,MAAM,IAAI,MAAM,2DAA2DA,EAAS,KAAM,CAAA,GAAG,EAE/F,YAAK,oBAAoB,KAAKA,CAAQ,EAC/B,IACR,CAeD,YAAY2H,EAAqBnI,EAAa,CAC5C,GAAI,OAAOmI,GAAwB,UACjC,YAAK,wBAA0BA,EACxB,KAGTA,EAAsBA,GAAuB,iBAC7C,KAAM,CAAA,CAAGC,EAAUC,CAAQ,EAAIF,EAAoB,MAAM,eAAe,EAClEG,EAAkBtI,GAAe,2BAEjCa,EAAc,KAAK,cAAcuH,CAAQ,EAC/C,OAAAvH,EAAY,WAAW,EAAK,EACxBwH,GAAUxH,EAAY,UAAUwH,CAAQ,EACxCC,GAAiBzH,EAAY,YAAYyH,CAAe,EAE5D,KAAK,wBAA0B,GAC/B,KAAK,aAAezH,EAEb,IACR,CASD,eAAeA,EAAa0H,EAAuB,CAGjD,OAAI,OAAO1H,GAAgB,UACzB,KAAK,YAAYA,EAAa0H,CAAqB,EAC5C,OAGT,KAAK,wBAA0B,GAC/B,KAAK,aAAe1H,EACb,KACR,CAQD,iBAAkB,CAIhB,OAH+B,KAAK,0BACjC,KAAK,SAAS,QAAU,CAAC,KAAK,gBAAkB,CAAC,KAAK,aAAa,MAAM,IAGtE,KAAK,eAAiB,QACxB,KAAK,YAAY,OAAW,MAAS,EAEhC,KAAK,cAEP,IACR,CAUD,KAAK2H,EAAOC,EAAU,CACpB,MAAMC,EAAgB,CAAC,gBAAiB,YAAa,YAAY,EACjE,GAAI,CAACA,EAAc,SAASF,CAAK,EAC/B,MAAM,IAAI,MAAM,gDAAgDA,CAAK;AAAA,oBACvDE,EAAc,KAAK,MAAM,CAAC,GAAG,EAE7C,OAAI,KAAK,gBAAgBF,CAAK,EAC5B,KAAK,gBAAgBA,CAAK,EAAE,KAAKC,CAAQ,EAEzC,KAAK,gBAAgBD,CAAK,EAAI,CAACC,CAAQ,EAElC,IACR,CASD,aAAatI,EAAI,CACf,OAAIA,EACF,KAAK,cAAgBA,EAErB,KAAK,cAAiBwI,GAAQ,CAC5B,GAAIA,EAAI,OAAS,mCACf,MAAMA,CAIhB,EAEW,IACR,CAYD,MAAMpJ,EAAUC,EAAMC,EAAS,CACzB,KAAK,eACP,KAAK,cAAc,IAAIC,EAAeH,EAAUC,EAAMC,CAAO,CAAC,EAGhEoH,EAAQ,KAAKtH,CAAQ,CACtB,CAiBD,OAAOY,EAAI,CACT,MAAMsI,EAAYjH,GAAS,CAEzB,MAAMoH,EAAoB,KAAK,oBAAoB,OAC7CC,EAAarH,EAAK,MAAM,EAAGoH,CAAiB,EAClD,OAAI,KAAK,0BACPC,EAAWD,CAAiB,EAAI,KAEhCC,EAAWD,CAAiB,EAAI,KAAK,KAAI,EAE3CC,EAAW,KAAK,IAAI,EAEb1I,EAAG,MAAM,KAAM0I,CAAU,CACtC,EACI,YAAK,eAAiBJ,EACf,IACR,CAaD,aAAavE,EAAOlE,EAAa,CAC/B,OAAO,IAAIqF,EAAOnB,EAAOlE,CAAW,CACrC,CAYD,cAAc8I,EAAQ7I,EAAOC,EAAU6I,EAAwB,CAC7D,GAAI,CACF,OAAOD,EAAO,SAAS7I,EAAOC,CAAQ,CACvC,OAAQyI,EAAK,CACZ,GAAIA,EAAI,OAAS,4BAA6B,CAC5C,MAAMlJ,EAAU,GAAGsJ,CAAsB,IAAIJ,EAAI,OAAO,GACxD,KAAK,MAAMlJ,EAAS,CAAE,SAAUkJ,EAAI,SAAU,KAAMA,EAAI,IAAI,CAAE,CAC/D,CACD,MAAMA,CACP,CACF,CAUD,gBAAgB1H,EAAQ,CACtB,MAAM+H,EAAkB/H,EAAO,OAAS,KAAK,YAAYA,EAAO,KAAK,GAClEA,EAAO,MAAQ,KAAK,YAAYA,EAAO,IAAI,EAC9C,GAAI+H,EAAgB,CAClB,MAAMC,EAAgBhI,EAAO,MAAQ,KAAK,YAAYA,EAAO,IAAI,EAAKA,EAAO,KAAOA,EAAO,MAC3F,MAAM,IAAI,MAAM,sBAAsBA,EAAO,KAAK,IAAI,KAAK,OAAS,gBAAgB,KAAK,KAAK,GAAG,6BAA6BgI,CAAY;AAAA,6BACnHD,EAAe,KAAK,GAAG,CAC/C,CAED,KAAK,QAAQ,KAAK/H,CAAM,CACzB,CAUD,iBAAiBU,EAAS,CACxB,MAAMuH,EAAWvI,GACR,CAACA,EAAI,KAAM,CAAA,EAAE,OAAOA,EAAI,QAAO,CAAE,EAGpCwI,EAAcD,EAAQvH,CAAO,EAAE,KAAM5B,GAAS,KAAK,aAAaA,CAAI,CAAC,EAC3E,GAAIoJ,EAAa,CACf,MAAMC,EAAcF,EAAQ,KAAK,aAAaC,CAAW,CAAC,EAAE,KAAK,GAAG,EAC9DE,EAASH,EAAQvH,CAAO,EAAE,KAAK,GAAG,EACxC,MAAM,IAAI,MAAM,uBAAuB0H,CAAM,8BAA8BD,CAAW,GAAG,CAC1F,CAED,KAAK,SAAS,KAAKzH,CAAO,CAC3B,CAQD,UAAUV,EAAQ,CAChB,KAAK,gBAAgBA,CAAM,EAE3B,MAAMqI,EAAQrI,EAAO,OACflB,EAAOkB,EAAO,gBAGpB,GAAIA,EAAO,OAAQ,CAEjB,MAAMsI,EAAmBtI,EAAO,KAAK,QAAQ,SAAU,IAAI,EACtD,KAAK,YAAYsI,CAAgB,GACpC,KAAK,yBAAyBxJ,EAAMkB,EAAO,eAAiB,OAAY,GAAOA,EAAO,aAAc,SAAS,CAErH,MAAeA,EAAO,eAAiB,QACjC,KAAK,yBAAyBlB,EAAMkB,EAAO,aAAc,SAAS,EAIpE,MAAMuI,EAAoB,CAACC,EAAKC,EAAqBC,IAAgB,CAG/DF,GAAO,MAAQxI,EAAO,YAAc,SACtCwI,EAAMxI,EAAO,WAIf,MAAM2I,EAAW,KAAK,eAAe7J,CAAI,EACrC0J,IAAQ,MAAQxI,EAAO,SACzBwI,EAAM,KAAK,cAAcxI,EAAQwI,EAAKG,EAAUF,CAAmB,EAC1DD,IAAQ,MAAQxI,EAAO,WAChCwI,EAAMxI,EAAO,aAAawI,EAAKG,CAAQ,GAIrCH,GAAO,OACLxI,EAAO,OACTwI,EAAM,GACGxI,EAAO,aAAeA,EAAO,SACtCwI,EAAM,GAENA,EAAM,IAGV,KAAK,yBAAyB1J,EAAM0J,EAAKE,CAAW,CAC1D,EAEI,YAAK,GAAG,UAAYL,EAAQG,GAAQ,CAClC,MAAMC,EAAsB,kBAAkBzI,EAAO,KAAK,eAAewI,CAAG,gBAC5ED,EAAkBC,EAAKC,EAAqB,KAAK,CACvD,CAAK,EAEGzI,EAAO,QACT,KAAK,GAAG,aAAeqI,EAAQG,GAAQ,CACrC,MAAMC,EAAsB,kBAAkBzI,EAAO,KAAK,YAAYwI,CAAG,eAAexI,EAAO,MAAM,gBACrGuI,EAAkBC,EAAKC,EAAqB,KAAK,CACzD,CAAO,EAGI,IACR,CAOD,UAAUG,EAAQ3F,EAAOlE,EAAaG,EAAI6H,EAAc,CACtD,GAAI,OAAO9D,GAAU,UAAYA,aAAiBmB,EAChD,MAAM,IAAI,MAAM,iFAAiF,EAEnG,MAAMpE,EAAS,KAAK,aAAaiD,EAAOlE,CAAW,EAEnD,GADAiB,EAAO,oBAAoB,CAAC,CAAC4I,EAAO,SAAS,EACzC,OAAO1J,GAAO,WAChBc,EAAO,QAAQ+G,CAAY,EAAE,UAAU7H,CAAE,UAChCA,aAAc,OAAQ,CAE/B,MAAMwD,EAAQxD,EACdA,EAAK,CAACsJ,EAAKK,IAAQ,CACjB,MAAMC,EAAIpG,EAAM,KAAK8F,CAAG,EACxB,OAAOM,EAAIA,EAAE,CAAC,EAAID,CAC1B,EACM7I,EAAO,QAAQ+G,CAAY,EAAE,UAAU7H,CAAE,CAC/C,MACMc,EAAO,QAAQd,CAAE,EAGnB,OAAO,KAAK,UAAUc,CAAM,CAC7B,CAwBD,OAAOiD,EAAOlE,EAAagK,EAAUhC,EAAc,CACjD,OAAO,KAAK,UAAU,CAAE,EAAE9D,EAAOlE,EAAagK,EAAUhC,CAAY,CACrE,CAeD,eAAe9D,EAAOlE,EAAagK,EAAUhC,EAAc,CACzD,OAAO,KAAK,UAAU,CAAE,UAAW,IAAQ9D,EAAOlE,EAAagK,EAAUhC,CAAY,CACtF,CAYD,4BAA4BiC,EAAU,GAAM,CAC1C,YAAK,6BAA+B,CAAC,CAACA,EAC/B,IACR,CAQD,mBAAmBC,EAAe,GAAM,CACtC,YAAK,oBAAsB,CAAC,CAACA,EACtB,IACR,CAQD,qBAAqBC,EAAc,GAAM,CACvC,YAAK,sBAAwB,CAAC,CAACA,EACxB,IACR,CASD,wBAAwBC,EAAa,GAAM,CACzC,YAAK,yBAA2B,CAAC,CAACA,EAC3B,IACR,CAWD,mBAAmBC,EAAc,GAAM,CACrC,YAAK,oBAAsB,CAAC,CAACA,EAC7B,KAAK,2BAA0B,EACxB,IACR,CAMD,4BAA6B,CAC3B,GAAI,KAAK,QAAU,KAAK,qBAAuB,CAAC,KAAK,OAAO,yBAC1D,MAAM,IAAI,MAAM,0CAA0C,KAAK,KAAK,oEAAoE,CAE3I,CAUD,yBAAyBC,EAAoB,GAAM,CACjD,GAAI,KAAK,QAAQ,OACf,MAAM,IAAI,MAAM,wDAAwD,EAE1E,GAAI,OAAO,KAAK,KAAK,aAAa,EAAE,OAClC,MAAM,IAAI,MAAM,+DAA+D,EAEjF,YAAK,0BAA4B,CAAC,CAACA,EAC5B,IACR,CASD,eAAezF,EAAK,CAClB,OAAI,KAAK,0BACA,KAAKA,CAAG,EAEV,KAAK,cAAcA,CAAG,CAC9B,CAUD,eAAeA,EAAK5E,EAAO,CACzB,OAAO,KAAK,yBAAyB4E,EAAK5E,EAAO,MAAS,CAC3D,CAWD,yBAAyB4E,EAAK5E,EAAOsK,EAAQ,CAC3C,OAAI,KAAK,0BACP,KAAK1F,CAAG,EAAI5E,EAEZ,KAAK,cAAc4E,CAAG,EAAI5E,EAE5B,KAAK,oBAAoB4E,CAAG,EAAI0F,EACzB,IACR,CAUD,qBAAqB1F,EAAK,CACxB,OAAO,KAAK,oBAAoBA,CAAG,CACpC,CAUD,gCAAgCA,EAAK,CAEnC,IAAI0F,EACJ,YAAK,wBAAuB,EAAG,QAAS5J,GAAQ,CAC1CA,EAAI,qBAAqBkE,CAAG,IAAM,SACpC0F,EAAS5J,EAAI,qBAAqBkE,CAAG,EAE7C,CAAK,EACM0F,CACR,CASD,iBAAiBC,EAAMC,EAAc,CACnC,GAAID,IAAS,QAAa,CAAC,MAAM,QAAQA,CAAI,EAC3C,MAAM,IAAI,MAAM,qDAAqD,EAEvEC,EAAeA,GAAgB,GAG3BD,IAAS,SACXA,EAAO3D,EAAQ,KAEXA,EAAQ,UAAYA,EAAQ,SAAS,WACvC4D,EAAa,KAAO,aAGxB,KAAK,QAAUD,EAAK,QAGpB,IAAIE,EACJ,OAAQD,EAAa,KAAI,CACvB,KAAK,OACL,IAAK,OACH,KAAK,YAAcD,EAAK,CAAC,EACzBE,EAAWF,EAAK,MAAM,CAAC,EACvB,MACF,IAAK,WAEC3D,EAAQ,YACV,KAAK,YAAc2D,EAAK,CAAC,EACzBE,EAAWF,EAAK,MAAM,CAAC,GAEvBE,EAAWF,EAAK,MAAM,CAAC,EAEzB,MACF,IAAK,OACHE,EAAWF,EAAK,MAAM,CAAC,EACvB,MACF,QACE,MAAM,IAAI,MAAM,oCAAoCC,EAAa,IAAI,KAAK,CAC7E,CAGD,MAAI,CAAC,KAAK,OAAS,KAAK,aAAa,KAAK,iBAAiB,KAAK,WAAW,EAC3E,KAAK,MAAQ,KAAK,OAAS,UAEpBC,CACR,CAmBD,MAAMF,EAAMC,EAAc,CACxB,MAAMC,EAAW,KAAK,iBAAiBF,EAAMC,CAAY,EACzD,YAAK,cAAc,GAAIC,CAAQ,EAExB,IACR,CAqBD,MAAM,WAAWF,EAAMC,EAAc,CACnC,MAAMC,EAAW,KAAK,iBAAiBF,EAAMC,CAAY,EACzD,aAAM,KAAK,cAAc,CAAE,EAAEC,CAAQ,EAE9B,IACR,CAQD,mBAAmBC,EAAYnJ,EAAM,CACnCA,EAAOA,EAAK,QACZ,IAAIoJ,EAAiB,GACrB,MAAMC,EAAY,CAAC,MAAO,MAAO,OAAQ,OAAQ,MAAM,EAEvD,SAASC,EAASC,EAASC,EAAU,CAEnC,MAAMC,EAAWxE,EAAK,QAAQsE,EAASC,CAAQ,EAC/C,GAAIrE,EAAG,WAAWsE,CAAQ,EAAG,OAAOA,EAGpC,GAAIJ,EAAU,SAASpE,EAAK,QAAQuE,CAAQ,CAAC,EAAG,OAGhD,MAAME,EAAWL,EAAU,KAAKM,GAAOxE,EAAG,WAAW,GAAGsE,CAAQ,GAAGE,CAAG,EAAE,CAAC,EACzE,GAAID,EAAU,MAAO,GAAGD,CAAQ,GAAGC,CAAQ,EAG5C,CAGD,KAAK,iCAAgC,EACrC,KAAK,4BAA2B,EAGhC,IAAIE,EAAiBT,EAAW,iBAAmB,GAAG,KAAK,KAAK,IAAIA,EAAW,KAAK,GAChFU,EAAgB,KAAK,gBAAkB,GAC3C,GAAI,KAAK,YAAa,CACpB,IAAIC,EACJ,GAAI,CACFA,EAAqB3E,EAAG,aAAa,KAAK,WAAW,CACtD,MAAa,CACZ2E,EAAqB,KAAK,WAC3B,CACDD,EAAgB5E,EAAK,QAAQA,EAAK,QAAQ6E,CAAkB,EAAGD,CAAa,CAC7E,CAGD,GAAIA,EAAe,CACjB,IAAIE,EAAYT,EAASO,EAAeD,CAAc,EAGtD,GAAI,CAACG,GAAa,CAACZ,EAAW,iBAAmB,KAAK,YAAa,CACjE,MAAMa,EAAa/E,EAAK,SAAS,KAAK,YAAaA,EAAK,QAAQ,KAAK,WAAW,CAAC,EAC7E+E,IAAe,KAAK,QACtBD,EAAYT,EAASO,EAAe,GAAGG,CAAU,IAAIb,EAAW,KAAK,EAAE,EAE1E,CACDS,EAAiBG,GAAaH,CAC/B,CAEDR,EAAiBC,EAAU,SAASpE,EAAK,QAAQ2E,CAAc,CAAC,EAEhE,IAAIK,EACA5E,EAAQ,WAAa,QACnB+D,GACFpJ,EAAK,QAAQ4J,CAAc,EAE3B5J,EAAOkK,EAA2B7E,EAAQ,QAAQ,EAAE,OAAOrF,CAAI,EAE/DiK,EAAOlF,EAAa,MAAMM,EAAQ,KAAK,CAAC,EAAGrF,EAAM,CAAE,MAAO,SAAW,CAAA,GAErEiK,EAAOlF,EAAa,MAAM6E,EAAgB5J,EAAM,CAAE,MAAO,SAAS,CAAE,GAGtEA,EAAK,QAAQ4J,CAAc,EAE3B5J,EAAOkK,EAA2B7E,EAAQ,QAAQ,EAAE,OAAOrF,CAAI,EAC/DiK,EAAOlF,EAAa,MAAMM,EAAQ,SAAUrF,EAAM,CAAE,MAAO,SAAS,CAAE,GAGnEiK,EAAK,QACQ,CAAC,UAAW,UAAW,UAAW,SAAU,QAAQ,EAC5D,QAASE,GAAW,CAE1B9E,EAAQ,GAAG8E,EAAQ,IAAM,CACnBF,EAAK,SAAW,IAASA,EAAK,WAAa,MAC7CA,EAAK,KAAKE,CAAM,CAE5B,CAAS,CACT,CAAO,EAIH,MAAMC,EAAe,KAAK,cAC1BH,EAAK,GAAG,QAAS,CAACjM,EAAMqM,IAAY,CAClCrM,EAAOA,GAAQ,EACVoM,EAGHA,EAAa,IAAIlM,EAAeF,EAAM,mCAAoC,SAAS,CAAC,EAFpFqH,EAAQ,KAAKrH,CAAI,CAIzB,CAAK,EACDiM,EAAK,GAAG,QAAU9C,GAAQ,CAExB,GAAIA,EAAI,OAAS,SAAU,CACzB,MAAMmD,EAAuBT,EACzB,wDAAwDA,CAAa,IACrE,kGACEU,EAAoB,IAAIX,CAAc;AAAA,SAC3CT,EAAW,KAAK;AAAA;AAAA,KAEpBmB,CAAoB,GACjB,MAAM,IAAI,MAAMC,CAAiB,CAEzC,SAAiBpD,EAAI,OAAS,SACtB,MAAM,IAAI,MAAM,IAAIyC,CAAc,kBAAkB,EAEtD,GAAI,CAACQ,EACH/E,EAAQ,KAAK,CAAC,MACT,CACL,MAAMmF,EAAe,IAAItM,EAAe,EAAG,mCAAoC,SAAS,EACxFsM,EAAa,YAAcrD,EAC3BiD,EAAaI,CAAY,CAC1B,CACP,CAAK,EAGD,KAAK,eAAiBP,CACvB,CAMD,oBAAoBQ,EAAaC,EAAUC,EAAS,CAClD,MAAMC,EAAa,KAAK,aAAaH,CAAW,EAC3CG,GAAY,KAAK,KAAK,CAAE,MAAO,EAAI,CAAE,EAE1C,IAAIC,EACJ,OAAAA,EAAe,KAAK,2BAA2BA,EAAcD,EAAY,eAAe,EACxFC,EAAe,KAAK,aAAaA,EAAc,IAAM,CACnD,GAAID,EAAW,mBACb,KAAK,mBAAmBA,EAAYF,EAAS,OAAOC,CAAO,CAAC,MAE5D,QAAOC,EAAW,cAAcF,EAAUC,CAAO,CAEzD,CAAK,EACME,CACR,CASD,qBAAqBC,EAAgB,CAC9BA,GACH,KAAK,KAAI,EAEX,MAAMF,EAAa,KAAK,aAAaE,CAAc,EACnD,OAAIF,GAAc,CAACA,EAAW,oBAC5BA,EAAW,KAAI,EAIV,KAAK,oBAAoBE,EAAgB,GAAI,CAClD,KAAK,eAAc,GAAI,MAAQ,KAAK,eAAc,GAAI,OAAS,QACrE,CAAK,CACF,CAQD,yBAA0B,CAExB,KAAK,oBAAoB,QAAQ,CAACjM,EAAKyD,IAAM,CACvCzD,EAAI,UAAY,KAAK,KAAKyD,CAAC,GAAK,MAClC,KAAK,gBAAgBzD,EAAI,KAAM,CAAA,CAEvC,CAAK,EAEG,OAAK,oBAAoB,OAAS,GAAK,KAAK,oBAAoB,KAAK,oBAAoB,OAAS,CAAC,EAAE,WAGrG,KAAK,KAAK,OAAS,KAAK,oBAAoB,QAC9C,KAAK,iBAAiB,KAAK,IAAI,CAElC,CAQD,mBAAoB,CAClB,MAAMkM,EAAa,CAAC/L,EAAUP,EAAOC,IAAa,CAEhD,IAAIsM,EAAcvM,EAClB,GAAIA,IAAU,MAAQO,EAAS,SAAU,CACvC,MAAMkJ,EAAsB,kCAAkCzJ,CAAK,8BAA8BO,EAAS,MAAM,KAChHgM,EAAc,KAAK,cAAchM,EAAUP,EAAOC,EAAUwJ,CAAmB,CAChF,CACD,OAAO8C,CACb,EAEI,KAAK,wBAAuB,EAE5B,MAAMC,EAAgB,CAAA,EACtB,KAAK,oBAAoB,QAAQ,CAACC,EAAaC,IAAU,CACvD,IAAI1M,EAAQyM,EAAY,aACpBA,EAAY,SAEVC,EAAQ,KAAK,KAAK,QACpB1M,EAAQ,KAAK,KAAK,MAAM0M,CAAK,EACzBD,EAAY,WACdzM,EAAQA,EAAM,OAAO,CAAC2M,EAAWC,IACxBN,EAAWG,EAAaG,EAAGD,CAAS,EAC1CF,EAAY,YAAY,IAEpBzM,IAAU,SACnBA,EAAQ,CAAA,GAED0M,EAAQ,KAAK,KAAK,SAC3B1M,EAAQ,KAAK,KAAK0M,CAAK,EACnBD,EAAY,WACdzM,EAAQsM,EAAWG,EAAazM,EAAOyM,EAAY,YAAY,IAGnED,EAAcE,CAAK,EAAI1M,CAC7B,CAAK,EACD,KAAK,cAAgBwM,CACtB,CAWD,aAAaK,EAAS3M,EAAI,CAExB,OAAI2M,GAAWA,EAAQ,MAAQ,OAAOA,EAAQ,MAAS,WAE9CA,EAAQ,KAAK,IAAM3M,EAAI,CAAA,EAGzBA,EAAE,CACV,CAUD,kBAAkB2M,EAAStE,EAAO,CAChC,IAAIjB,EAASuF,EACb,MAAMC,EAAQ,CAAA,EACd,YAAK,wBAAyB,EAC3B,QAAS,EACT,OAAOpM,GAAOA,EAAI,gBAAgB6H,CAAK,IAAM,MAAS,EACtD,QAAQwE,GAAiB,CACxBA,EAAc,gBAAgBxE,CAAK,EAAE,QAASyE,GAAa,CACzDF,EAAM,KAAK,CAAE,cAAAC,EAAe,SAAAC,CAAU,CAAA,CAChD,CAAS,CACT,CAAO,EACCzE,IAAU,cACZuE,EAAM,QAAO,EAGfA,EAAM,QAASG,GAAe,CAC5B3F,EAAS,KAAK,aAAaA,EAAQ,IAC1B2F,EAAW,SAASA,EAAW,cAAe,IAAI,CAC1D,CACP,CAAK,EACM3F,CACR,CAWD,2BAA2BuF,EAASV,EAAY5D,EAAO,CACrD,IAAIjB,EAASuF,EACb,OAAI,KAAK,gBAAgBtE,CAAK,IAAM,QAClC,KAAK,gBAAgBA,CAAK,EAAE,QAAS2E,GAAS,CAC5C5F,EAAS,KAAK,aAAaA,EAAQ,IAC1B4F,EAAK,KAAMf,CAAU,CAC7B,CACT,CAAO,EAEI7E,CACR,CASD,cAAc2E,EAAUC,EAAS,CAC/B,MAAMiB,EAAS,KAAK,aAAajB,CAAO,EAOxC,GANA,KAAK,iBAAgB,EACrB,KAAK,qBAAoB,EACzBD,EAAWA,EAAS,OAAOkB,EAAO,QAAQ,EAC1CjB,EAAUiB,EAAO,QACjB,KAAK,KAAOlB,EAAS,OAAOC,CAAO,EAE/BD,GAAY,KAAK,aAAaA,EAAS,CAAC,CAAC,EAC3C,OAAO,KAAK,oBAAoBA,EAAS,CAAC,EAAGA,EAAS,MAAM,CAAC,EAAGC,CAAO,EAEzE,GAAI,KAAK,gBAAiB,GAAID,EAAS,CAAC,IAAM,KAAK,kBAAkB,OACnE,OAAO,KAAK,qBAAqBA,EAAS,CAAC,CAAC,EAE9C,GAAI,KAAK,oBACP,YAAK,uBAAuBC,CAAO,EAC5B,KAAK,oBAAoB,KAAK,oBAAqBD,EAAUC,CAAO,EAEzE,KAAK,SAAS,QAAU,KAAK,KAAK,SAAW,GAAK,CAAC,KAAK,gBAAkB,CAAC,KAAK,qBAElF,KAAK,KAAK,CAAE,MAAO,EAAM,CAAA,EAG3B,KAAK,uBAAuBiB,EAAO,OAAO,EAC1C,KAAK,iCAAgC,EACrC,KAAK,4BAA2B,EAGhC,MAAMC,EAAyB,IAAM,CAC/BD,EAAO,QAAQ,OAAS,GAC1B,KAAK,cAAcA,EAAO,QAAQ,CAAC,CAAC,CAE5C,EAEUE,EAAe,WAAW,KAAK,KAAI,CAAE,GAC3C,GAAI,KAAK,eAAgB,CACvBD,IACA,KAAK,kBAAiB,EAEtB,IAAIhB,EACJ,OAAAA,EAAe,KAAK,kBAAkBA,EAAc,WAAW,EAC/DA,EAAe,KAAK,aAAaA,EAAc,IAAM,KAAK,eAAe,KAAK,aAAa,CAAC,EACxF,KAAK,SACPA,EAAe,KAAK,aAAaA,EAAc,IAAM,CACnD,KAAK,OAAO,KAAKiB,EAAcpB,EAAUC,CAAO,CAC1D,CAAS,GAEHE,EAAe,KAAK,kBAAkBA,EAAc,YAAY,EACzDA,CACR,CACD,GAAI,KAAK,QAAU,KAAK,OAAO,cAAciB,CAAY,EACvDD,IACA,KAAK,kBAAiB,EACtB,KAAK,OAAO,KAAKC,EAAcpB,EAAUC,CAAO,UACvCD,EAAS,OAAQ,CAC1B,GAAI,KAAK,aAAa,GAAG,EACvB,OAAO,KAAK,oBAAoB,IAAKA,EAAUC,CAAO,EAEpD,KAAK,cAAc,WAAW,EAEhC,KAAK,KAAK,YAAaD,EAAUC,CAAO,EAC/B,KAAK,SAAS,OACvB,KAAK,eAAc,GAEnBkB,IACA,KAAK,kBAAiB,EAE9B,MAAe,KAAK,SAAS,QACvBA,IAEA,KAAK,KAAK,CAAE,MAAO,EAAM,CAAA,IAEzBA,IACA,KAAK,kBAAiB,EAGzB,CAOD,aAAatN,EAAM,CACjB,GAAKA,EACL,OAAO,KAAK,SAAS,KAAKY,GAAOA,EAAI,QAAUZ,GAAQY,EAAI,SAAS,SAASZ,CAAI,CAAC,CACnF,CAUD,YAAYM,EAAK,CACf,OAAO,KAAK,QAAQ,KAAKY,GAAUA,EAAO,GAAGZ,CAAG,CAAC,CAClD,CASD,kCAAmC,CAEjC,KAAK,wBAAuB,EAAG,QAASM,GAAQ,CAC9CA,EAAI,QAAQ,QAAS4M,GAAa,CAC5BA,EAAS,WAAc5M,EAAI,eAAe4M,EAAS,cAAa,CAAE,IAAM,QAC1E5M,EAAI,4BAA4B4M,CAAQ,CAElD,CAAO,CACP,CAAK,CACF,CAOD,kCAAmC,CACjC,MAAMC,EAA2B,KAAK,QAAQ,OAC3CvM,GAAW,CACV,MAAM6D,EAAY7D,EAAO,gBACzB,OAAI,KAAK,eAAe6D,CAAS,IAAM,OAC9B,GAEF,KAAK,qBAAqBA,CAAS,IAAM,SACjD,CACP,EAEmC0I,EAAyB,OACrDvM,GAAWA,EAAO,cAAc,OAAS,CAChD,EAE2B,QAASA,GAAW,CACzC,MAAMwM,EAAwBD,EAAyB,KAAME,GAC3DzM,EAAO,cAAc,SAASyM,EAAQ,cAAa,CAAE,CAC7D,EACUD,GACF,KAAK,mBAAmBxM,EAAQwM,CAAqB,CAE7D,CAAK,CACF,CAQD,6BAA8B,CAE5B,KAAK,wBAAuB,EAAG,QAAS9M,GAAQ,CAC9CA,EAAI,iCAAgC,CAC1C,CAAK,CACF,CAkBD,aAAa6J,EAAM,CACjB,MAAM0B,EAAW,CAAA,EACXC,EAAU,CAAA,EAChB,IAAIwB,EAAOzB,EACX,MAAM1K,EAAOgJ,EAAK,QAElB,SAASoD,EAAYvN,EAAK,CACxB,OAAOA,EAAI,OAAS,GAAKA,EAAI,CAAC,IAAM,GACrC,CAGD,IAAIwN,EAAuB,KAC3B,KAAOrM,EAAK,QAAQ,CAClB,MAAMnB,EAAMmB,EAAK,QAGjB,GAAInB,IAAQ,KAAM,CACZsN,IAASxB,GAASwB,EAAK,KAAKtN,CAAG,EACnCsN,EAAK,KAAK,GAAGnM,CAAI,EACjB,KACD,CAED,GAAIqM,GAAwB,CAACD,EAAYvN,CAAG,EAAG,CAC7C,KAAK,KAAK,UAAUwN,EAAqB,MAAM,GAAIxN,CAAG,EACtD,QACD,CAGD,GAFAwN,EAAuB,KAEnBD,EAAYvN,CAAG,EAAG,CACpB,MAAMY,EAAS,KAAK,YAAYZ,CAAG,EAEnC,GAAIY,EAAQ,CACV,GAAIA,EAAO,SAAU,CACnB,MAAMhB,EAAQuB,EAAK,QACfvB,IAAU,QAAW,KAAK,sBAAsBgB,CAAM,EAC1D,KAAK,KAAK,UAAUA,EAAO,MAAM,GAAIhB,CAAK,CACtD,SAAqBgB,EAAO,SAAU,CAC1B,IAAIhB,EAAQ,KAERuB,EAAK,OAAS,GAAK,CAACoM,EAAYpM,EAAK,CAAC,CAAC,IACzCvB,EAAQuB,EAAK,SAEf,KAAK,KAAK,UAAUP,EAAO,MAAM,GAAIhB,CAAK,CACtD,MACY,KAAK,KAAK,UAAUgB,EAAO,KAAI,CAAE,EAAE,EAErC4M,EAAuB5M,EAAO,SAAWA,EAAS,KAClD,QACD,CACF,CAGD,GAAIZ,EAAI,OAAS,GAAKA,EAAI,CAAC,IAAM,KAAOA,EAAI,CAAC,IAAM,IAAK,CACtD,MAAMY,EAAS,KAAK,YAAY,IAAIZ,EAAI,CAAC,CAAC,EAAE,EAC5C,GAAIY,EAAQ,CACNA,EAAO,UAAaA,EAAO,UAAY,KAAK,6BAE9C,KAAK,KAAK,UAAUA,EAAO,MAAM,GAAIZ,EAAI,MAAM,CAAC,CAAC,GAGjD,KAAK,KAAK,UAAUY,EAAO,KAAI,CAAE,EAAE,EACnCO,EAAK,QAAQ,IAAInB,EAAI,MAAM,CAAC,CAAC,EAAE,GAEjC,QACD,CACF,CAGD,GAAI,YAAY,KAAKA,CAAG,EAAG,CACzB,MAAMsM,EAAQtM,EAAI,QAAQ,GAAG,EACvBY,EAAS,KAAK,YAAYZ,EAAI,MAAM,EAAGsM,CAAK,CAAC,EACnD,GAAI1L,IAAWA,EAAO,UAAYA,EAAO,UAAW,CAClD,KAAK,KAAK,UAAUA,EAAO,KAAI,CAAE,GAAIZ,EAAI,MAAMsM,EAAQ,CAAC,CAAC,EACzD,QACD,CACF,CAWD,GALIiB,EAAYvN,CAAG,IACjBsN,EAAOxB,IAIJ,KAAK,0BAA4B,KAAK,sBAAwBD,EAAS,SAAW,GAAKC,EAAQ,SAAW,GAC7G,GAAI,KAAK,aAAa9L,CAAG,EAAG,CAC1B6L,EAAS,KAAK7L,CAAG,EACbmB,EAAK,OAAS,GAAG2K,EAAQ,KAAK,GAAG3K,CAAI,EACzC,KACV,SAAmB,KAAK,mBAAqBnB,IAAQ,KAAK,gBAAe,EAAG,OAAQ,CAC1E6L,EAAS,KAAK7L,CAAG,EACbmB,EAAK,OAAS,GAAG0K,EAAS,KAAK,GAAG1K,CAAI,EAC1C,KACV,SAAmB,KAAK,oBAAqB,CACnC2K,EAAQ,KAAK9L,CAAG,EACZmB,EAAK,OAAS,GAAG2K,EAAQ,KAAK,GAAG3K,CAAI,EACzC,KACD,EAIH,GAAI,KAAK,oBAAqB,CAC5BmM,EAAK,KAAKtN,CAAG,EACTmB,EAAK,OAAS,GAAGmM,EAAK,KAAK,GAAGnM,CAAI,EACtC,KACD,CAGDmM,EAAK,KAAKtN,CAAG,CACd,CAED,MAAO,CAAE,SAAA6L,EAAU,QAAAC,EACpB,CAOD,MAAO,CACL,GAAI,KAAK,0BAA2B,CAElC,MAAM5E,EAAS,CAAA,EACTuG,EAAM,KAAK,QAAQ,OAEzB,QAAShK,EAAI,EAAGA,EAAIgK,EAAKhK,IAAK,CAC5B,MAAMe,EAAM,KAAK,QAAQf,CAAC,EAAE,cAAa,EACzCyD,EAAO1C,CAAG,EAAIA,IAAQ,KAAK,mBAAqB,KAAK,SAAW,KAAKA,CAAG,CACzE,CACD,OAAO0C,CACR,CAED,OAAO,KAAK,aACb,CAOD,iBAAkB,CAEhB,OAAO,KAAK,wBAAuB,EAAG,OACpC,CAACwG,EAAiBpN,IAAQ,OAAO,OAAOoN,EAAiBpN,EAAI,MAAM,EACnE,CAAE,CACR,CACG,CAUD,MAAMlB,EAASuO,EAAc,CAE3B,KAAK,qBAAqB,YAAY,GAAGvO,CAAO;AAAA,EAAM,KAAK,qBAAqB,QAAQ,EACpF,OAAO,KAAK,qBAAwB,SACtC,KAAK,qBAAqB,SAAS,GAAG,KAAK,mBAAmB;AAAA,CAAI,EACzD,KAAK,sBACd,KAAK,qBAAqB,SAAS;AAAA,CAAI,EACvC,KAAK,WAAW,CAAE,MAAO,EAAM,CAAA,GAIjC,MAAMoK,EAASmE,GAAgB,GACzBzO,EAAWsK,EAAO,UAAY,EAC9BrK,EAAOqK,EAAO,MAAQ,kBAC5B,KAAK,MAAMtK,EAAUC,EAAMC,CAAO,CACnC,CAQD,kBAAmB,CACjB,KAAK,QAAQ,QAASwB,GAAW,CAC/B,GAAIA,EAAO,QAAUA,EAAO,UAAU4F,EAAQ,IAAK,CACjD,MAAM/B,EAAY7D,EAAO,iBAErB,KAAK,eAAe6D,CAAS,IAAM,QAAa,CAAC,UAAW,SAAU,KAAK,EAAE,SAAS,KAAK,qBAAqBA,CAAS,CAAC,KACxH7D,EAAO,UAAYA,EAAO,SAE5B,KAAK,KAAK,aAAaA,EAAO,KAAI,CAAE,GAAI4F,EAAQ,IAAI5F,EAAO,MAAM,CAAC,EAGlE,KAAK,KAAK,aAAaA,EAAO,KAAI,CAAE,EAAE,EAG3C,CACP,CAAK,CACF,CAOD,sBAAuB,CACrB,MAAMgN,EAAa,IAAI3I,GAAY,KAAK,OAAO,EACzC4I,EAAwBpJ,GACrB,KAAK,eAAeA,CAAS,IAAM,QAAa,CAAC,CAAC,UAAW,SAAS,EAAE,SAAS,KAAK,qBAAqBA,CAAS,CAAC,EAE9H,KAAK,QACF,OAAO7D,GAAWA,EAAO,UAAY,QACpCiN,EAAqBjN,EAAO,eAAe,GAC3CgN,EAAW,gBAAgB,KAAK,eAAehN,EAAO,cAAe,CAAA,EAAGA,CAAM,CAAC,EAChF,QAASA,GAAW,CACnB,OAAO,KAAKA,EAAO,OAAO,EACvB,OAAOkN,GAAc,CAACD,EAAqBC,CAAU,CAAC,EACtD,QAAQA,GAAc,CACrB,KAAK,yBAAyBA,EAAYlN,EAAO,QAAQkN,CAAU,EAAG,SAAS,CAC3F,CAAW,CACX,CAAO,CACJ,CASD,gBAAgBpO,EAAM,CACpB,MAAMN,EAAU,qCAAqCM,CAAI,IACzD,KAAK,MAAMN,EAAS,CAAE,KAAM,2BAA6B,CAAA,CAC1D,CASD,sBAAsBwB,EAAQ,CAC5B,MAAMxB,EAAU,kBAAkBwB,EAAO,KAAK,qBAC9C,KAAK,MAAMxB,EAAS,CAAE,KAAM,iCAAmC,CAAA,CAChE,CASD,4BAA4BwB,EAAQ,CAClC,MAAMxB,EAAU,2BAA2BwB,EAAO,KAAK,kBACvD,KAAK,MAAMxB,EAAS,CAAE,KAAM,uCAAyC,CAAA,CACtE,CASD,mBAAmBwB,EAAQmN,EAAmB,CAG5C,MAAMC,EAA2BpN,GAAW,CAC1C,MAAM6D,EAAY7D,EAAO,gBACnBqN,EAAc,KAAK,eAAexJ,CAAS,EAC3CyJ,EAAiB,KAAK,QAAQ,KAAKzF,GAAUA,EAAO,QAAUhE,IAAcgE,EAAO,cAAe,CAAA,EAClG0F,EAAiB,KAAK,QAAQ,KAAK1F,GAAU,CAACA,EAAO,QAAUhE,IAAcgE,EAAO,cAAe,CAAA,EACzG,OAAIyF,IACDA,EAAe,YAAc,QAAaD,IAAgB,IAC1DC,EAAe,YAAc,QAAaD,IAAgBC,EAAe,WAEnEA,EAEFC,GAAkBvN,CAC/B,EAEUwN,EAAmBxN,GAAW,CAClC,MAAMyN,EAAaL,EAAwBpN,CAAM,EAC3C6D,EAAY4J,EAAW,gBAE7B,OADe,KAAK,qBAAqB5J,CAAS,IACnC,MACN,yBAAyB4J,EAAW,MAAM,IAE5C,WAAWA,EAAW,KAAK,GACxC,EAEUjP,EAAU,UAAUgP,EAAgBxN,CAAM,CAAC,wBAAwBwN,EAAgBL,CAAiB,CAAC,GAC3G,KAAK,MAAM3O,EAAS,CAAE,KAAM,6BAA+B,CAAA,CAC5D,CASD,cAAckP,EAAM,CAClB,GAAI,KAAK,oBAAqB,OAC9B,IAAIC,EAAa,GAEjB,GAAID,EAAK,WAAW,IAAI,GAAK,KAAK,0BAA2B,CAE3D,IAAIE,EAAiB,CAAA,EACjBlN,EAAU,KACd,EAAG,CACD,MAAMmN,EAAYnN,EAAQ,WAAU,EAAG,eAAeA,CAAO,EAC1D,OAAOV,GAAUA,EAAO,IAAI,EAC5B,IAAIA,GAAUA,EAAO,IAAI,EAC5B4N,EAAiBA,EAAe,OAAOC,CAAS,EAChDnN,EAAUA,EAAQ,MAC1B,OAAeA,GAAW,CAACA,EAAQ,0BAC7BiN,EAAahJ,EAAe+I,EAAME,CAAc,CACjD,CAED,MAAMpP,EAAU,0BAA0BkP,CAAI,IAAIC,CAAU,GAC5D,KAAK,MAAMnP,EAAS,CAAE,KAAM,yBAA2B,CAAA,CACxD,CASD,iBAAiBsP,EAAc,CAC7B,GAAI,KAAK,sBAAuB,OAEhC,MAAMC,EAAW,KAAK,oBAAoB,OACpCC,EAAKD,IAAa,EAAK,GAAK,IAE5BvP,EAAU,4BADM,KAAK,OAAS,SAAS,KAAK,KAAM,CAAA,IAAM,EACL,cAAcuP,CAAQ,YAAYC,CAAC,YAAYF,EAAa,MAAM,IAC3H,KAAK,MAAMtP,EAAS,CAAE,KAAM,2BAA6B,CAAA,CAC1D,CAQD,gBAAiB,CACf,MAAMyP,EAAc,KAAK,KAAK,CAAC,EAC/B,IAAIN,EAAa,GAEjB,GAAI,KAAK,0BAA2B,CAClC,MAAMO,EAAiB,CAAA,EACvB,KAAK,WAAU,EAAG,gBAAgB,IAAI,EAAE,QAASxN,GAAY,CAC3DwN,EAAe,KAAKxN,EAAQ,KAAM,CAAA,EAE9BA,EAAQ,SAASwN,EAAe,KAAKxN,EAAQ,MAAK,CAAE,CAChE,CAAO,EACDiN,EAAahJ,EAAesJ,EAAaC,CAAc,CACxD,CAED,MAAM1P,EAAU,2BAA2ByP,CAAW,IAAIN,CAAU,GACpE,KAAK,MAAMnP,EAAS,CAAE,KAAM,0BAA4B,CAAA,CACzD,CAeD,QAAQuD,EAAKkB,EAAOlE,EAAa,CAC/B,GAAIgD,IAAQ,OAAW,OAAO,KAAK,SACnC,KAAK,SAAWA,EAChBkB,EAAQA,GAAS,gBACjBlE,EAAcA,GAAe,4BAC7B,MAAMoP,EAAgB,KAAK,aAAalL,EAAOlE,CAAW,EAC1D,YAAK,mBAAqBoP,EAAc,gBACxC,KAAK,gBAAgBA,CAAa,EAElC,KAAK,GAAG,UAAYA,EAAc,KAAM,EAAE,IAAM,CAC9C,KAAK,qBAAqB,SAAS,GAAGpM,CAAG;AAAA,CAAI,EAC7C,KAAK,MAAM,EAAG,oBAAqBA,CAAG,CAC5C,CAAK,EACM,IACR,CASD,YAAYA,EAAKqM,EAAiB,CAChC,OAAIrM,IAAQ,QAAaqM,IAAoB,OAAkB,KAAK,cACpE,KAAK,aAAerM,EAChBqM,IACF,KAAK,iBAAmBA,GAEnB,KACR,CAQD,QAAQrM,EAAK,CACX,OAAIA,IAAQ,OAAkB,KAAK,UACnC,KAAK,SAAWA,EACT,KACR,CAWD,MAAMsM,EAAO,CACX,GAAIA,IAAU,OAAW,OAAO,KAAK,SAAS,CAAC,EAG/C,IAAI3N,EAAU,KAMd,GALI,KAAK,SAAS,SAAW,GAAK,KAAK,SAAS,KAAK,SAAS,OAAS,CAAC,EAAE,qBAExEA,EAAU,KAAK,SAAS,KAAK,SAAS,OAAS,CAAC,GAG9C2N,IAAU3N,EAAQ,MAAO,MAAM,IAAI,MAAM,6CAA8C,EAC3F,MAAM4N,EAAkB,KAAK,QAAQ,aAAaD,CAAK,EACvD,GAAIC,EAAiB,CAEnB,MAAMnG,EAAc,CAACmG,EAAgB,KAAM,CAAA,EAAE,OAAOA,EAAgB,QAAS,CAAA,EAAE,KAAK,GAAG,EACvF,MAAM,IAAI,MAAM,qBAAqBD,CAAK,iBAAiB,KAAK,KAAM,CAAA,8BAA8BlG,CAAW,GAAG,CACnH,CAED,OAAAzH,EAAQ,SAAS,KAAK2N,CAAK,EACpB,IACR,CAWD,QAAQE,EAAS,CAEf,OAAIA,IAAY,OAAkB,KAAK,UAEvCA,EAAQ,QAASF,GAAU,KAAK,MAAMA,CAAK,CAAC,EACrC,KACR,CASD,MAAMtM,EAAK,CACT,GAAIA,IAAQ,OAAW,CACrB,GAAI,KAAK,OAAQ,OAAO,KAAK,OAE7B,MAAMxB,EAAO,KAAK,oBAAoB,IAAKnB,GAClCC,GAAqBD,CAAG,CAChC,EACD,MAAO,CAAE,EAAC,OACP,KAAK,QAAQ,QAAW,KAAK,cAAgB,KAAQ,YAAc,CAAE,EACrE,KAAK,SAAS,OAAS,YAAc,CAAE,EACvC,KAAK,oBAAoB,OAASmB,EAAO,CAAE,CACpD,EAAQ,KAAK,GAAG,CACX,CAED,YAAK,OAASwB,EACP,IACR,CASD,KAAKA,EAAK,CACR,OAAIA,IAAQ,OAAkB,KAAK,OACnC,KAAK,MAAQA,EACN,KACR,CAeD,iBAAiByM,EAAU,CACzB,YAAK,MAAQhJ,EAAK,SAASgJ,EAAUhJ,EAAK,QAAQgJ,CAAQ,CAAC,EAEpD,IACR,CAcD,cAAchJ,EAAM,CAClB,OAAIA,IAAS,OAAkB,KAAK,gBACpC,KAAK,eAAiBA,EACf,KACR,CASD,gBAAgBiJ,EAAgB,CAC9B,MAAMjO,EAAS,KAAK,aACpB,OAAIA,EAAO,YAAc,SACvBA,EAAO,UAAaiO,GAAkBA,EAAe,MAAS,KAAK,qBAAqB,gBAAe,EAAK,KAAK,qBAAqB,gBAAe,GAEhJjO,EAAO,WAAW,KAAMA,CAAM,CACtC,CAMD,gBAAgBiO,EAAgB,CAC9BA,EAAiBA,GAAkB,GACnC,MAAMC,EAAU,CAAE,MAAO,CAAC,CAACD,EAAe,KAAK,EAC/C,IAAIrI,EACJ,OAAIsI,EAAQ,MACVtI,EAAShH,GAAQ,KAAK,qBAAqB,SAASA,CAAG,EAEvDgH,EAAShH,GAAQ,KAAK,qBAAqB,SAASA,CAAG,EAEzDsP,EAAQ,MAAQD,EAAe,OAASrI,EACxCsI,EAAQ,QAAU,KACXA,CACR,CAUD,WAAWD,EAAgB,CACzB,IAAIE,EACA,OAAOF,GAAmB,aAC5BE,EAAqBF,EACrBA,EAAiB,QAEnB,MAAMC,EAAU,KAAK,gBAAgBD,CAAc,EAEnD,KAAK,wBAAuB,EAAG,QAAO,EAAG,QAAQ/N,GAAWA,EAAQ,KAAK,gBAAiBgO,CAAO,CAAC,EAClG,KAAK,KAAK,aAAcA,CAAO,EAE/B,IAAIE,EAAkB,KAAK,gBAAgBF,CAAO,EAClD,GAAIC,IACFC,EAAkBD,EAAmBC,CAAe,EAChD,OAAOA,GAAoB,UAAY,CAAC,OAAO,SAASA,CAAe,GACzE,MAAM,IAAI,MAAM,sDAAsD,EAG1EF,EAAQ,MAAME,CAAe,EAEzB,KAAK,eAAgB,GAAE,MACzB,KAAK,KAAK,KAAK,eAAgB,EAAC,IAAI,EAEtC,KAAK,KAAK,YAAaF,CAAO,EAC9B,KAAK,0BAA0B,QAAQhO,GAAWA,EAAQ,KAAK,eAAgBgO,CAAO,CAAC,CACxF,CAeD,WAAWzL,EAAOlE,EAAa,CAE7B,OAAI,OAAOkE,GAAU,WACfA,EACF,KAAK,YAAc,KAAK,aAAe,OAEvC,KAAK,YAAc,KAEd,OAITA,EAAQA,GAAS,aACjBlE,EAAcA,GAAe,2BAC7B,KAAK,YAAc,KAAK,aAAakE,EAAOlE,CAAW,EAEhD,KACR,CASD,gBAAiB,CAEf,OAAI,KAAK,cAAgB,QACvB,KAAK,WAAW,OAAW,MAAS,EAE/B,KAAK,WACb,CASD,cAAciB,EAAQ,CACpB,YAAK,YAAcA,EACZ,IACR,CAUD,KAAKyO,EAAgB,CACnB,KAAK,WAAWA,CAAc,EAC9B,IAAInQ,EAAWsH,EAAQ,UAAY,EAC/BtH,IAAa,GAAKmQ,GAAkB,OAAOA,GAAmB,YAAcA,EAAe,QAC7FnQ,EAAW,GAGb,KAAK,MAAMA,EAAU,iBAAkB,cAAc,CACtD,CAYD,YAAYuQ,EAAUC,EAAM,CAC1B,MAAMrH,EAAgB,CAAC,YAAa,SAAU,QAAS,UAAU,EACjE,GAAI,CAACA,EAAc,SAASoH,CAAQ,EAClC,MAAM,IAAI,MAAM;AAAA,oBACFpH,EAAc,KAAK,MAAM,CAAC,GAAG,EAE7C,MAAMsH,EAAY,GAAGF,CAAQ,OAC7B,YAAK,GAAGE,EAAYL,GAAY,CAC9B,IAAIM,EACA,OAAOF,GAAS,WAClBE,EAAUF,EAAK,CAAE,MAAOJ,EAAQ,MAAO,QAASA,EAAQ,OAAO,CAAE,EAEjEM,EAAUF,EAGRE,GACFN,EAAQ,MAAM,GAAGM,CAAO;AAAA,CAAI,CAEpC,CAAK,EACM,IACR,CASD,uBAAuBzO,EAAM,CAC3B,MAAML,EAAa,KAAK,iBACFA,GAAcK,EAAK,KAAKnB,GAAOc,EAAW,GAAGd,CAAG,CAAC,IAErE,KAAK,WAAU,EAEf,KAAK,MAAM,EAAG,0BAA2B,cAAc,EAE1D,CACH,EAUA,SAASqL,EAA2BlK,EAAM,CAKxC,OAAOA,EAAK,IAAKnB,GAAQ,CACvB,GAAI,CAACA,EAAI,WAAW,WAAW,EAC7B,OAAOA,EAET,IAAI6P,EACAC,EAAY,YACZC,EAAY,OACZC,EAoBJ,OAnBKA,EAAQhQ,EAAI,MAAM,sBAAsB,KAAO,KAElD6P,EAAcG,EAAM,CAAC,GACXA,EAAQhQ,EAAI,MAAM,oCAAoC,KAAO,MACvE6P,EAAcG,EAAM,CAAC,EACjB,QAAQ,KAAKA,EAAM,CAAC,CAAC,EAEvBD,EAAYC,EAAM,CAAC,EAGnBF,EAAYE,EAAM,CAAC,IAEXA,EAAQhQ,EAAI,MAAM,0CAA0C,KAAO,OAE7E6P,EAAcG,EAAM,CAAC,EACrBF,EAAYE,EAAM,CAAC,EACnBD,EAAYC,EAAM,CAAC,GAGjBH,GAAeE,IAAc,IACxB,GAAGF,CAAW,IAAIC,CAAS,IAAI,SAASC,CAAS,EAAI,CAAC,GAExD/P,CACX,CAAG,CACH,CAEAsB,GAAA,QAAkByF,GC1vElB,KAAM,CAAE3G,SAAAA,EAAU,EAAGZ,EACf,CAAEuH,QAAAA,CAAS,EAAGZ,GACd,gBAAE9G,GAAc,qBAAEE,EAAsB,EAAG8G,EAC3C,CAAE1C,KAAAA,EAAM,EAAG4C,EACX,CAAEvB,OAAAA,EAAQ,EAAGyB,EAEnBwJ,EAAA,QAAkB,IAAIlJ,EAEDkJ,EAAA,cAAIvQ,GAAS,IAAIqH,EAAQrH,CAAI,EAClDuQ,EAAA,aAAuB,CAACpM,EAAOlE,IAAgB,IAAIqF,GAAOnB,EAAOlE,CAAW,EAC5EsQ,EAAA,eAAyB,CAACvQ,EAAMC,IAAgB,IAAIS,GAASV,EAAMC,CAAW,EAM/DsQ,EAAA,QAAGlJ,EACJkJ,EAAA,OAAGjL,GACDiL,EAAA,SAAG7P,GACP6P,EAAA,KAAGtM,GAEOsM,EAAA,eAAG5Q,GACG4Q,EAAA,qBAAG1Q,GACG0Q,EAAA,2BAAG1Q,GCpB9B,KAAM,CACX,QAAA2Q,EACA,cAAAC,GACA,eAAAC,GACA,aAAAC,GACA,eAAAhR,GACA,qBAAAE,GACA,2BAAA+Q,GACA,QAAAvJ,GACA,SAAA3G,GACA,OAAA4E,EACA,KAAArB,EACF,EAAIsM,ECfJ,IAAIM,GAAc/Q,EAAyB,aACvCgR,GAAkBrK,EAAAA,iBAYtB,SAASsK,GAAWvG,EAAQwG,EAAOC,EAAQC,EAAY,CACrD,IAAIC,EAAQ,CAACF,EACbA,IAAWA,EAAS,CAAA,GAKpB,QAHIrE,EAAQ,GACRvG,EAAS2K,EAAM,OAEZ,EAAEpE,EAAQvG,GAAQ,CACvB,IAAIvB,EAAMkM,EAAMpE,CAAK,EAEjBwE,EAAWF,EACXA,EAAWD,EAAOnM,CAAG,EAAG0F,EAAO1F,CAAG,EAAGA,EAAKmM,EAAQzG,CAAM,EACxD,OAEA4G,IAAa,SACfA,EAAW5G,EAAO1F,CAAG,GAEnBqM,EACFL,GAAgBG,EAAQnM,EAAKsM,CAAQ,EAErCP,GAAYI,EAAQnM,EAAKsM,CAAQ,CAEpC,CACD,OAAOH,CACT,CAEA,IAAAI,EAAiBN,GCvCbA,GAAajR,EACbwR,GAAO7K,EAAAA,OAWX,SAAS8K,GAAWN,EAAQzG,EAAQ,CAClC,OAAOyG,GAAUF,GAAWvG,EAAQ8G,GAAK9G,CAAM,EAAGyG,CAAM,CAC1D,CAEA,IAAAO,GAAiBD,GChBbR,GAAajR,EACb2R,GAAShL,EAAAA,SAWb,SAASiL,GAAaT,EAAQzG,EAAQ,CACpC,OAAOyG,GAAUF,GAAWvG,EAAQiH,GAAOjH,CAAM,EAAGyG,CAAM,CAC5D,CAEA,IAAAU,GAAiBD,2CChBjB,IAAIE,EAAO9R,EAAAA,MAGP+R,EAA4CC,GAAW,CAACA,EAAQ,UAAYA,EAG5EC,EAAaF,GAAe,IAA6BG,GAAU,CAACA,EAAO,UAAYA,EAGvFC,EAAgBF,GAAcA,EAAW,UAAYF,EAGrDK,EAASD,EAAgBL,EAAK,OAAS,OACvCO,EAAcD,EAASA,EAAO,YAAc,OAUhD,SAASE,EAAYC,EAAQC,EAAQ,CACnC,GAAIA,EACF,OAAOD,EAAO,QAEhB,IAAIhM,EAASgM,EAAO,OAChB7K,EAAS2K,EAAcA,EAAY9L,CAAM,EAAI,IAAIgM,EAAO,YAAYhM,CAAM,EAE9E,OAAAgM,EAAO,KAAK7K,CAAM,EACXA,CACR,CAEDwK,EAAA,QAAiBI,kCClCbrB,GAAajR,EACbyS,GAAa9L,EAAAA,YAUjB,SAAS+L,GAAYhI,EAAQyG,EAAQ,CACnC,OAAOF,GAAWvG,EAAQ+H,GAAW/H,CAAM,EAAGyG,CAAM,CACtD,CAEA,IAAAwB,GAAiBD,GCfbzB,GAAajR,EACb4S,GAAejM,EAAAA,cAUnB,SAASkM,GAAcnI,EAAQyG,EAAQ,CACrC,OAAOF,GAAWvG,EAAQkI,GAAalI,CAAM,EAAGyG,CAAM,CACxD,CAEA,IAAA2B,GAAiBD,GCdbE,GAAc,OAAO,UAGrBC,GAAiBD,GAAY,eASjC,SAASE,GAAeC,EAAO,CAC7B,IAAI3M,EAAS2M,EAAM,OACfxL,EAAS,IAAIwL,EAAM,YAAY3M,CAAM,EAGzC,OAAIA,GAAU,OAAO2M,EAAM,CAAC,GAAK,UAAYF,GAAe,KAAKE,EAAO,OAAO,IAC7ExL,EAAO,MAAQwL,EAAM,MACrBxL,EAAO,MAAQwL,EAAM,OAEhBxL,CACT,CAEA,IAAAyL,GAAiBF,GCzBbG,EAAapT,EAAAA,YASjB,SAASqT,GAAiBC,EAAa,CACrC,IAAI5L,EAAS,IAAI4L,EAAY,YAAYA,EAAY,UAAU,EAC/D,WAAIF,EAAW1L,CAAM,EAAE,IAAI,IAAI0L,EAAWE,CAAW,CAAC,EAC/C5L,CACT,CAEA,IAAA6L,EAAiBF,GCfbA,GAAmBrT,EAUvB,SAASwT,GAAcC,EAAUjB,EAAQ,CACvC,IAAID,EAASC,EAASa,GAAiBI,EAAS,MAAM,EAAIA,EAAS,OACnE,OAAO,IAAIA,EAAS,YAAYlB,EAAQkB,EAAS,WAAYA,EAAS,UAAU,CAClF,CAEA,IAAAC,GAAiBF,GCdbG,GAAU,OASd,SAASC,GAAYC,EAAQ,CAC3B,IAAInM,EAAS,IAAImM,EAAO,YAAYA,EAAO,OAAQF,GAAQ,KAAKE,CAAM,CAAC,EACvE,OAAAnM,EAAO,UAAYmM,EAAO,UACnBnM,CACT,CAEA,IAAAoM,GAAiBF,GChBbG,EAAS/T,EAAAA,QAGTgU,EAAcD,EAASA,EAAO,UAAY,OAC1CE,EAAgBD,EAAcA,EAAY,QAAU,OASxD,SAASE,GAAYC,EAAQ,CAC3B,OAAOF,EAAgB,OAAOA,EAAc,KAAKE,CAAM,CAAC,EAAI,EAC9D,CAEA,IAAAC,GAAiBF,GCjBbb,GAAmBrT,EAUvB,SAASqU,GAAgBC,EAAY9B,EAAQ,CAC3C,IAAID,EAASC,EAASa,GAAiBiB,EAAW,MAAM,EAAIA,EAAW,OACvE,OAAO,IAAIA,EAAW,YAAY/B,EAAQ+B,EAAW,WAAYA,EAAW,MAAM,CACpF,CAEA,IAAAC,GAAiBF,GCfbhB,GAAmBrT,EACnBwT,GAAgB7M,GAChBiN,GAAc/M,GACdqN,GAAcnN,GACdsN,GAAkBpN,GAGlBuN,GAAU,mBACVC,GAAU,gBACVC,GAAS,eACTC,GAAY,kBACZC,GAAY,kBACZC,GAAS,eACTC,GAAY,kBACZC,GAAY,kBAEZC,GAAiB,uBACjBC,GAAc,oBACdC,GAAa,wBACbC,GAAa,wBACbC,GAAU,qBACVC,GAAW,sBACXC,GAAW,sBACXC,GAAW,sBACXC,GAAkB,6BAClBC,GAAY,uBACZC,GAAY,uBAchB,SAASC,GAAexE,EAAQyE,EAAKpD,EAAQ,CAC3C,IAAIqD,EAAO1E,EAAO,YAClB,OAAQyE,EAAG,CACT,KAAKZ,GACH,OAAO3B,GAAiBlC,CAAM,EAEhC,KAAKqD,GACL,KAAKC,GACH,OAAO,IAAIoB,EAAK,CAAC1E,CAAM,EAEzB,KAAK8D,GACH,OAAOzB,GAAcrC,EAAQqB,CAAM,EAErC,KAAK0C,GAAY,KAAKC,GACtB,KAAKC,GAAS,KAAKC,GAAU,KAAKC,GAClC,KAAKC,GAAU,KAAKC,GAAiB,KAAKC,GAAW,KAAKC,GACxD,OAAOrB,GAAgBlD,EAAQqB,CAAM,EAEvC,KAAKkC,GACH,OAAO,IAAImB,EAEb,KAAKlB,GACL,KAAKG,GACH,OAAO,IAAIe,EAAK1E,CAAM,EAExB,KAAKyD,GACH,OAAOhB,GAAYzC,CAAM,EAE3B,KAAK0D,GACH,OAAO,IAAIgB,EAEb,KAAKd,GACH,OAAOb,GAAY/C,CAAM,CAC5B,CACH,CAEA,IAAA2E,GAAiBH,GC5EbI,GAAa/V,EAAwB,YACrCgW,GAAerP,EAA0B,cACzCsP,GAAcpP,EAAAA,aASlB,SAASqP,GAAgB/E,EAAQ,CAC/B,OAAQ,OAAOA,EAAO,aAAe,YAAc,CAAC8E,GAAY9E,CAAM,EAClE4E,GAAWC,GAAa7E,CAAM,CAAC,EAC/B,EACN,CAEA,IAAAgF,GAAiBD,GCjBbE,GAASpW,EAAoB,QAC7BqW,GAAe1P,EAAAA,eAGf+N,GAAS,eASb,SAAS4B,GAAUlW,EAAO,CACxB,OAAOiW,GAAajW,CAAK,GAAKgW,GAAOhW,CAAK,GAAKsU,EACjD,CAEA,IAAA6B,GAAiBD,GCjBbA,GAAYtW,GACZwW,GAAY7P,EAAuB,WACnC8P,EAAW5P,EAAAA,iBAGX6P,EAAYD,GAAYA,EAAS,MAmBjCE,GAAQD,EAAYF,GAAUE,CAAS,EAAIJ,GAE/CM,GAAiBD,GC1BbP,GAASpW,EAAoB,QAC7BqW,GAAe1P,EAAAA,eAGfkO,GAAS,eASb,SAASgC,GAAUzW,EAAO,CACxB,OAAOiW,GAAajW,CAAK,GAAKgW,GAAOhW,CAAK,GAAKyU,EACjD,CAEA,IAAAiC,GAAiBD,GCjBbA,GAAY7W,GACZwW,GAAY7P,EAAuB,WACnC8P,GAAW5P,EAAAA,iBAGXkQ,GAAYN,IAAYA,GAAS,MAmBjCO,GAAQD,GAAYP,GAAUO,EAAS,EAAIF,GAE/CI,GAAiBD,GC1BbE,GAAQlX,EAAmB,OAC3BmX,GAAYxQ,EAAuB,WACnCoK,GAAclK,EAAyB,aACvC4K,GAAa1K,GACb6K,GAAe3K,GACfqL,GAAcpL,GACdkQ,GAAYjQ,EAAuB,WACnCuL,GAActL,GACdyL,GAAgBxL,GAChBgQ,GAAa/P,EAAwB,YACrCgQ,GAAeC,EAA0B,cACzCnB,GAASoB,EAAoB,QAC7BvE,GAAiBwE,GACjB9B,GAAiB+B,GACjBxB,GAAkByB,GAClBC,GAAUC,EAAoB,UAC9BC,GAAWC,EAAqB,gBAChCpB,GAAQqB,GACRC,GAAWC,EAAqB,WAChClB,GAAQmB,GACR3G,GAAO4G,EAAiB,OACxBzG,GAAS0G,EAAAA,SAGTC,GAAkB,EAClBC,GAAkB,EAClBC,GAAqB,EAGrBC,GAAU,qBACVC,GAAW,iBACXlE,GAAU,mBACVC,GAAU,gBACVkE,GAAW,iBACXC,GAAU,oBACVC,GAAS,6BACTnE,GAAS,eACTC,GAAY,kBACZmE,GAAY,kBACZlE,GAAY,kBACZC,GAAS,eACTC,GAAY,kBACZC,GAAY,kBACZgE,GAAa,mBAEb/D,GAAiB,uBACjBC,GAAc,oBACdC,GAAa,wBACbC,GAAa,wBACbC,GAAU,qBACVC,GAAW,sBACXC,GAAW,sBACXC,GAAW,sBACXC,GAAkB,6BAClBC,GAAY,uBACZC,GAAY,uBAGZsD,EAAgB,CAAA,EACpBA,EAAcP,EAAO,EAAIO,EAAcN,EAAQ,EAC/CM,EAAchE,EAAc,EAAIgE,EAAc/D,EAAW,EACzD+D,EAAcxE,EAAO,EAAIwE,EAAcvE,EAAO,EAC9CuE,EAAc9D,EAAU,EAAI8D,EAAc7D,EAAU,EACpD6D,EAAc5D,EAAO,EAAI4D,EAAc3D,EAAQ,EAC/C2D,EAAc1D,EAAQ,EAAI0D,EAActE,EAAM,EAC9CsE,EAAcrE,EAAS,EAAIqE,EAAcF,EAAS,EAClDE,EAAcpE,EAAS,EAAIoE,EAAcnE,EAAM,EAC/CmE,EAAclE,EAAS,EAAIkE,EAAcjE,EAAS,EAClDiE,EAAczD,EAAQ,EAAIyD,EAAcxD,EAAe,EACvDwD,EAAcvD,EAAS,EAAIuD,EAActD,EAAS,EAAI,GACtDsD,EAAcL,EAAQ,EAAIK,EAAcJ,EAAO,EAC/CI,EAAcD,EAAU,EAAI,GAkB5B,SAASE,EAAU7Y,EAAO8Y,EAAS9H,EAAYpM,EAAKmM,EAAQgI,EAAO,CACjE,IAAIzR,EACA8K,EAAS0G,EAAUZ,GACnBc,EAASF,EAAUX,GACnBc,EAASH,EAAUV,GAKvB,GAHIpH,IACF1J,EAASyJ,EAASC,EAAWhR,EAAO4E,EAAKmM,EAAQgI,CAAK,EAAI/H,EAAWhR,CAAK,GAExEsH,IAAW,OACb,OAAOA,EAET,GAAI,CAACuQ,GAAS7X,CAAK,EACjB,OAAOA,EAET,IAAIkZ,EAAQ1B,GAAQxX,CAAK,EACzB,GAAIkZ,GAEF,GADA5R,EAASuL,GAAe7S,CAAK,EACzB,CAACoS,EACH,OAAO4E,GAAUhX,EAAOsH,CAAM,MAE3B,CACL,IAAIkO,EAAMQ,GAAOhW,CAAK,EAClBmZ,EAAS3D,GAAOgD,IAAWhD,GAAOiD,GAEtC,GAAIf,GAAS1X,CAAK,EAChB,OAAOkS,GAAYlS,EAAOoS,CAAM,EAElC,GAAIoD,GAAOkD,IAAalD,GAAO6C,IAAYc,GAAU,CAACpI,GAEpD,GADAzJ,EAAU0R,GAAUG,EAAU,CAAA,EAAKrD,GAAgB9V,CAAK,EACpD,CAACoS,EACH,OAAO4G,EACHvG,GAAczS,EAAOwR,GAAalK,EAAQtH,CAAK,CAAC,EAChDsS,GAAYtS,EAAOqR,GAAW/J,EAAQtH,CAAK,CAAC,MAE7C,CACL,GAAI,CAAC4Y,EAAcpD,CAAG,EACpB,OAAOzE,EAAS/Q,EAAQ,GAE1BsH,EAASiO,GAAevV,EAAOwV,EAAKpD,CAAM,CAC3C,CACF,CAED2G,IAAUA,EAAQ,IAAIjC,IACtB,IAAIsC,EAAUL,EAAM,IAAI/Y,CAAK,EAC7B,GAAIoZ,EACF,OAAOA,EAETL,EAAM,IAAI/Y,EAAOsH,CAAM,EAEnBsP,GAAM5W,CAAK,EACbA,EAAM,QAAQ,SAASqZ,EAAU,CAC/B/R,EAAO,IAAIuR,EAAUQ,EAAUP,EAAS9H,EAAYqI,EAAUrZ,EAAO+Y,CAAK,CAAC,CACjF,CAAK,EACQxC,GAAMvW,CAAK,GACpBA,EAAM,QAAQ,SAASqZ,EAAUzU,EAAK,CACpC0C,EAAO,IAAI1C,EAAKiU,EAAUQ,EAAUP,EAAS9H,EAAYpM,EAAK5E,EAAO+Y,CAAK,CAAC,CACjF,CAAK,EAGH,IAAIO,EAAWL,EACVD,EAAS9B,GAAeD,GACxB+B,EAASzH,GAASH,GAEnBN,EAAQoI,EAAQ,OAAYI,EAAStZ,CAAK,EAC9C,OAAA+W,GAAUjG,GAAS9Q,EAAO,SAASqZ,EAAUzU,EAAK,CAC5CkM,IACFlM,EAAMyU,EACNA,EAAWrZ,EAAM4E,CAAG,GAGtB+L,GAAYrJ,EAAQ1C,EAAKiU,EAAUQ,EAAUP,EAAS9H,EAAYpM,EAAK5E,EAAO+Y,CAAK,CAAC,CACxF,CAAG,EACMzR,CACT,CAEA,IAAAiS,GAAiBV,ECrKbA,GAAYjZ,GAGZsY,GAAkB,EAClBE,GAAqB,EAoBzB,SAASoB,GAAUxZ,EAAO,CACxB,OAAO6Y,GAAU7Y,EAAOkY,GAAkBE,EAAkB,CAC9D,CAEA,IAAAqB,GAAiBD,0CChBXE,GAA2B,kDAG3BC,GAAU,GAAc,EAAE,QAAQ,KAAM,EAAE,GAE9C,SAAY,CAEZ,KAAM,CAAE,QAASC,CAAmB,EAAA,MAAM,QAAA,QAAA,EAAA,KAAA,IAAA,QAAO,qBAAiB,CAAA,EAc5DC,EAAWD,EAAe,CAAEE,IAAAA,EAAAA,GAAK,CAAA,EACvC,GAAID,EAAS,QAAUA,EAAS,OAAO,SAAWC,MAAI,QAAS,CAC7D,KAAM,CAAE,QAASC,CAAU,EAAA,MAAM,QAAA,QAAA,EAAA,KAAA,IAAA,QAAO,qBAAO,CAAA,EAGzCC,EAAeC,EAAAA,OAAO,MAAMJ,EAAS,OAAO,OAAO,GAAG,MACtDK,EAAcD,EAAAA,OAAO,MAAMJ,EAAS,OAAO,MAAM,GAAG,MAQpDM,GAHJH,GAAgBE,GAAeA,GAAeF,EAC1C,IAAI,MAAME,EAAcF,CAAY,EAAE,KAAK,CAAC,EAAE,IAAI,CAACI,EAAGvW,IAAMmW,EAAenW,EAAI,CAAC,EAChF,CAAC,GAC2B,IAAoBwW,GAAA,GAAGP,MAAI,UAAY,EAAE,kBAAkBO,CAAY,MAAM,EAGzGC,EAAa,GAAGR,EAAAA,IAAI,UAAY,EAAE,aAAaD,EAAS,OAAO,OAAO,OAAOA,EAAS,OAAO,MAAM,GAEzGA,EAAS,OAAO,CACd,MAAO,GACP,SAAU,GACV,QAAS,oBAAoBE,EAAM,IAAI,kBAAkB,CAAC,GAAGA,EAAM,MAAM,KAAK,CAAC,GAC7EF,EAAS,OAAO,OAAS,QACrBE,EAAM,IAAI,iBAAiB,EAC3BF,EAAS,OAAO,OAAS,QACvBE,EAAM,OAAO,iBAAiB,EAC9BA,EAAM,MAAM,iBAAiB,CACrC;AAAA,MACAA,EAAM,KAAK,iBAAiB,CAAC;AAAA,EACjCA,EAAM,IAAI,UACVF,EAAS,OAAO,OAAS,QAAUM,EAAY,IAAII,GAAOR,EAAM,IAAI,UAAUQ,CAAG,CAAC,EAAE,KAAK;AAAA,CAAI,EAAID,CAAA,CAClG,EAAA,CACI,CACH,CAIA,MAAME,EAAU,QAAQ,KAAK,MAAM,CAAC,EAC9BC,EAAYD,EAAQ,aAAiBpa,IAAQ,UAAYA,IAAQ,IAAI,EAC3E,GAAIqa,IAAc,IAAMD,EAAQC,EAAY,CAAC,EAAG,CAC9C,MAAMvZ,EAAasZ,EAAQC,EAAY,CAAC,EAAE,QAAQ,MAAO,EAAE,EACvDvZ,IAAe,QAAUA,IAAe,IAC1C,QAAQ,KAAK,0CAA0C,GAEvD,MAAMwZ,EAAU,UAAA,EACI,CAAC,GAAGF,EAAQ,MAAM,EAAGC,CAAS,EAAG,GAAGD,EAAQ,MAAMC,EAAY,CAAC,CAAC,EACxE,QAAera,GAAA,CAEzB,MAAMua,EAAQva,EAAI,QAAQ,MAAO,EAAE,EAC7BY,EAAS4Z,EAAAA,iBAAW,KACxB5Z,GACE2Z,IAAU3Z,EAAO,MACjB2Z,IAAU3Z,EAAO,OAChB2Z,IAAU,MAAM3Z,EAAO,IAAI,IAAMA,EAAO,OAAS,SAAA,EAGpD,QAAQ,KADNA,EACW6Z,EAAAA,mBAAmB7Z,CAAM,EAAI;AAAA,EACjC2Z,IAAU,WAAaA,IAAU,KAAOA,IAAU,IAEzDE,qBAAmB,CACjB,KAAM,UACN,MAAO,IACP,YAAanB,GAEb,KAAM,QACP,CAAA,EAAI;AAAA,EAGM,mBAAmBtZ,CAAG,EAZW,CAahD,CACD,GAEH,QAAQ,KAAK,CAAC,CAChB,CAGA,MAAM0a,EAAQ,IAAI,IAAIF,EAAAA,iBAAW,UAAiB5Z,EAAO,MAAQ,EAAK,EAAE,IAAcA,GAAA,KAAKA,EAAO,IAAI,EAAE,CAAC,EAGzGsP,EACG,YAAY,qFAAqF,EACjG,MAAM,oBAAoB,EAE1B,cAAc,CACb,WACEtP,GAAAA,EAAO,MAAQ8Z,EAAM,IAAI9Z,EAAO,IAAI,EAChCA,EAAO,KAAK,QAAQ,KAAM,EAAE,EAAI,IAChCA,EAAO,OAAS,YAEd,oBACAA,EAAO,MAAM,QAAQ,SAAU,EAAE,EACzC,kBAAmBA,GACjBA,EAAO,OAAS,YACZ0Y,GACA1Y,EAAO,OAAS,SACd,yEACA+C,GAAK,UAAU,kBAAkB/C,CAAM,CAAA,CAChD,EAEA,UAAU,IAAIoE,EAAO,oBAAoB,EAAE,UAAU,EACrD,GAAG,sBAAuB,IAAM,CACvB,QAAA,KAAK0U,MAAI,OAAO,EACxB,QAAQ,KAAK,CAAC,CAAA,CACf,EAGHc,EAAAA,iBAAW,QAAQ,CAAC,CAAE,KAAAG,EAAM,MAAAC,EAAO,IAAA5a,EAAK,YAAAL,EAAa,QAASgI,EAAc,KAAAjE,EAAM,MAAAmX,EAAO,KAAAC,CAAA,IAAW,CAClG,MAAMjX,GAAQ,GAAG+W,EAAQ,IAAIA,CAAK,KAAO,EAAE,KAAKD,CAAI,GAAG3a,EAAM,KAAKA,CAAG,IAAM,EAAE,GAGvE+a,GAAuB,GAAGxB,GAAO5Z,CAAW,CAAC,GAAG+D,EAAO,oBAAoBiX,CAAI,iBAAmB,EAAE,GAG1GzK,EAAQ,OAAOrM,GAAOkX,GAAsBF,GAASlT,EAAckT,EAAQlT,EAAe,MAAS,EAI/FmT,IAAS,WACH5K,EAAA,UAAU,IAAIlL,EAAO,QAAQ2V,CAAI,EAAE,EAAE,QAAQ,EAAK,EAAE,SAAU,CAAA,CACxE,CACD,EAGOzK,EAAA,QAAQwJ,MAAI,OAAO,EAKrB,MAAAsB,EAAsB5B,GAAWlJ,EAAgB,aAAa,EAC5DA,EAAA,MAAM,QAAQ,IAAI,EAEpB,MAAA+K,EAAc/K,EAAQ,OACtBgL,EAAc,QAAQ,KAAK,MAAM,CAAC,EAElC,CAAE,MAAAC,EAAO,eAAAC,EAAgB,eAAAC,EAAgB,OAAAC,EAAQ,YAAAC,EAAa,YAAAC,CAAgB,EAAAP,EAIpF,MAAMX,EAAAA,UAAUa,CAAK,EAIf,MAAAM,EACJ,CAAC,QAAQ,IAAI,WAAaJ,GAAkBG,EACxC,MAAME,WAAS,CACb,eAAAN,EACA,eAAAC,EACA,OAAAC,EACA,YAAAC,EACA,QAAS,CAAE,GAAGN,EAAa,IAAK,EAAK,CACtC,CAAA,EACD,KAGAU,GAAUF,GAAU,MAAQ,CAAI,GAAA,OACpC,CAACzb,EAAKyD,EAAGtC,KACN,OAAOnB,GAAQ,UAAY,CAACA,EAAI,WAAW,GAAG,GAAK,CAACkb,EAAY,SAASlb,CAAG,KAC5E,OAAOmB,EAAKsC,EAAI,CAAC,GAAM,UAAY,CAACtC,EAAKsC,EAAI,CAAC,EAAE,WAAW,GAAG,GAAK,CAACyX,EAAY,SAAS/Z,EAAKsC,EAAI,CAAC,CAAC,EAAA,EAInGmY,EAAoB,CAAC,GAAG,QAAQ,KAAK,MAAM,EAAG,CAAC,EAAG,GAAGD,EAAQ,GAAGT,CAAW,EAG/EhL,EAAgB,cAAgB8K,EAClC9K,EAAQ,MAAM0L,CAAiB,EACzB,MAAAC,EAAsB3L,EAAQ,OAG9B3L,GAAU,CACd,GAAIkX,GAAY,OAAO,KAAKA,EAAS,MAAM,EAAE,OAAS,EAAI,CAAE,aAAcA,EAAS,QAAa,EAAA,KAChG,GAAGK,EAAAA,OAAO5L,EAAQ,OAAQtQ,GAASA,IAAU,MAAS,EACtD,KAAMsQ,EAAQ,KACd,GAAI2L,EAAoB,OAAS,CAAE,OAAQA,EAAoB,QAAW,KAC1E,GAAIA,EAAoB,OAAS,CAAE,OAAQA,EAAoB,QAAW,IAAA,EAK5EE,EAAAA,IAAIxX,GAAS,CAAE,IAAK,EAAM,CAAA,CAC5B,GAAG", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]}