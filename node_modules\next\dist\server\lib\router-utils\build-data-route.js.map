{"version": 3, "sources": ["../../../../src/server/lib/router-utils/build-data-route.ts"], "names": ["buildDataRoute", "page", "buildId", "pagePath", "normalizePagePath", "dataRoute", "path", "posix", "join", "dataRouteRegex", "namedDataRouteRegex", "routeKeys", "isDynamicRoute", "routeRegex", "getNamedRouteRegex", "replace", "normalizeRouteRegex", "re", "source", "namedRegex", "RegExp", "escapeStringRegexp"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;6DAPC;mCACiB;2BACH;4BACI;kCACC;8BACD;;;;;;AAE5B,SAASA,eAAeC,IAAY,EAAEC,OAAe;IAC1D,MAAMC,WAAWC,IAAAA,oCAAiB,EAACH;IACnC,MAAMI,YAAYC,aAAI,CAACC,KAAK,CAACC,IAAI,CAAC,eAAeN,SAAS,CAAC,EAAEC,SAAS,KAAK,CAAC;IAE5E,IAAIM;IACJ,IAAIC;IACJ,IAAIC;IAEJ,IAAIC,IAAAA,yBAAc,EAACX,OAAO;QACxB,MAAMY,aAAaC,IAAAA,8BAAkB,EACnCT,UAAUU,OAAO,CAAC,WAAW,KAC7B;QAGFN,iBAAiBO,IAAAA,qCAAmB,EAClCH,WAAWI,EAAE,CAACC,MAAM,CAACH,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC;QAE7DL,sBAAsBG,WAAWM,UAAU,CAAEJ,OAAO,CAClD,kBACA,CAAC,QAAQ,CAAC;QAEZJ,YAAYE,WAAWF,SAAS;IAClC,OAAO;QACLF,iBAAiBO,IAAAA,qCAAmB,EAClC,IAAII,OACF,CAAC,CAAC,EAAEd,aAAI,CAACC,KAAK,CAACC,IAAI,CACjB,eACAa,IAAAA,gCAAkB,EAACnB,UACnB,CAAC,EAAEC,SAAS,KAAK,CAAC,EAClB,CAAC,CAAC,EACJe,MAAM;IAEZ;IAEA,OAAO;QACLjB;QACAU;QACAF;QACAC;IACF;AACF"}