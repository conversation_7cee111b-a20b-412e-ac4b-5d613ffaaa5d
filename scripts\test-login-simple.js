// Simple login test using Node.js built-in fetch
console.log('🧪 Testing Login API with Node.js fetch...\n')

const testLogin = async () => {
  try {
    const loginData = {
      email: '<EMAIL>',
      password: 'admin123'
    }

    console.log('📤 Sending login request...')
    console.log('Email:', loginData.email)
    console.log('Password: [HIDDEN]')

    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData)
    })

    console.log('\n📥 Response received:')
    console.log('Status:', response.status)
    console.log('Status Text:', response.statusText)
    console.log('OK:', response.ok)

    const result = await response.json()
    console.log('\n📋 Response body:')
    console.log(JSON.stringify(result, null, 2))

    if (response.ok && result.success) {
      console.log('\n✅ LOGIN TEST PASSED!')
      console.log('🎉 User successfully authenticated')
      console.log('👤 User ID:', result.user?.id)
      console.log('📧 Email:', result.user?.email)
      console.log('🔑 Role:', result.user?.role)
      console.log('📦 Plan:', result.user?.plan)
    } else {
      console.log('\n❌ LOGIN TEST FAILED!')
      console.log('Error:', result.message || result.error || 'Unknown error')
    }

  } catch (error) {
    console.error('\n💥 LOGIN TEST ERROR!')
    console.error('Error type:', error.constructor.name)
    console.error('Error message:', error.message)
    console.error('Full error:', error)
  }
}

testLogin()
